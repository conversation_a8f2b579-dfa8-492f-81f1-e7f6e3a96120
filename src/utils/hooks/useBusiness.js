import { useAppStore } from '@/stores/useAppStore';
const api = {
  delBusiness: '/csccs/businessControl/delBusiness',
  startBusiness: '/bpmn/businessManage/startBusiness',
  executeBusiness: '/bpmn/businessManage/executeBusiness'
};

/**
 * @description 开启工作流
 * @param {{businessConfigCode:string,businessJson:object,applicationReason:string,businessName:string}}  params
 * @param {string} businessConfigCode  业务编码
 * @param {object} businessJson 数据提交
 * @param {string} applicationReason  原因备注
 * @param {string} businessName  工作流名称
 * @return {Promise}
 * **/
export function openBusiness({ businessConfigCode, businessJson, applicationReason, businessName } = {}) {
  const appStore = useAppStore();
  const { id: sysId } = appStore.system;
  return $http.post(api.startBusiness, {
    businessConfigCode,
    sysId,
    businessJson,
    applicationReason,
    businessName
  });
}

/**
 * @description 提交工作流
 * @param {{businessConfigCode:string,businessId:string,businessExecutionType:"SUBMIT",businessName:string}}  params
 * @param {string} params.businessConfigCode 业务编码
 * @param {string} params.businessId 工作流ID
 * @param {string} params.businessExecutionType  执行类型默认 SUBMIT
 * @param {string} params.businessName  工作流名称
 * @return {Promise}
 * **/
export function submitBusiness({
  businessConfigCode,
  businessId,
  businessExecutionType = 'SUBMIT',
  businessName
} = {}) {
  const appStore = useAppStore();
  const { id: sysId } = appStore.system;
  return $http.post(api.executeBusiness, {
    businessConfigCode,
    sysId,
    businessId,
    businessExecutionType,
    businessName
  });
}

/**
 * @description 删除工作流
 * @param {string} businessId  工作流ID
 * @return {Promise}
 * **/

export function deleteBusiness(businessId) {
  const appStore = useAppStore();
  const { id: sysId } = appStore.system;
  return $http.post(api.delBusiness, {
    sysId,
    businessId
  });
}
