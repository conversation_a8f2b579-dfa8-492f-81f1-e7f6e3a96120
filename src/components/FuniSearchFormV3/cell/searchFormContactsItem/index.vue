<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-09-21 09:48:17
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-10-17 18:03:12
 * @FilePath: /funi-cloud-web-gsbms/src/apps/component/selectPerson/index.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->

<template>
  <div ref="selectPerson" style="width: 100%">
    <div
      class="choosePerson"
      :class="{
        hasValue: modelValue && modelValue?.length > 0
      }"
      @click="selectSwitch()"
    >
      <div
        class="input__box"
        :class="{
          ipt_flex: true,
          [class_key]: true
        }"
      >
        <div v-if="!modelValue || modelValue.length == 0" class="input__placeholder">
          {{ placeholder }}
        </div>
        <div v-for="(item, index) in modelValue" class="valueItem" :key="item.id">
          <span>
            {{ item.nickName || item.name }}
          </span>
          <el-icon @click.stop="delItem(item)">
            <Close />
          </el-icon>
        </div>
      </div>
      <span class="el-input__suffix">
        <span class="el-input__suffix-inner"
          ><i class="el-icon el-select__caret el-select__icon icon_default"> <SelectSvg /> </i>
          <i class="el-icon el-select__caret el-select__icon icon_clear" @click.stop="clearFunc"> <ClearSvg /> </i>
        </span>
      </span>
    </div>
    <SelectModal :value="modelValue" @change="change" ref="select_modal"></SelectModal>
  </div>
</template>

<script setup lang="tsx">
import { ref, onMounted, nextTick, watch, provide, unref, computed } from 'vue';
import SelectModal from './selectModal.vue';
import SelectSvg from './svg/select.svg.vue';
import ClearSvg from './svg/clear.svg.vue';

import { selectPersonApiKey } from './hooks/injectionSymbols';
import { setLocal } from './hooks/api.js';

import type { SelectPersonApi } from './hooks/injectionSymbols';
const selectPerson = ref();
const select_modal = ref();
const class_key = ref();
const emit = defineEmits(['change', 'update:modelValue']);

const props = defineProps({
  modelValue: Array<{ nickName: string; id: string; name?: string }>,
  selfOrgUrl: {
    type: String,
    default: ''
  },
  requestTreeUrl: {
    type: String,
    default: '/csccs/orgList/orgTree'
  },
  findAccountByOrgIds: {
    type: String,
    default: '/csccs/govAccountOrg/findAllAccountList'
  },
  placeholder: {
    type: String,
    default: '请选择'
  }
});
let { selfOrgUrl, requestTreeUrl, findAccountByOrgIds } = props as SelectPersonApi;
provide(selectPersonApiKey, { selfOrgUrl, requestTreeUrl, findAccountByOrgIds, mode: 'multiple' });
onMounted(() => {
  class_key.value = 'input__box_' + $utils.guid();
  // TODO 设置初始化宽度
  setDefaultWidth();
});

const setDefaultWidth = () => {
  let w = selectPerson.value.offsetWidth;
};

const selectSwitch = async () => {
  setDefaultWidth();
  select_modal.value.show();
};
const change = v => {
  let data = $utils.clone(v, true);
  emit('update:modelValue', data);
  emit('change', data);
};

const clearFunc = () => {
  emit('update:modelValue', []);
  emit('change', []);
};
const delItem = item => {
  let index: number = props.modelValue?.findIndex(el => el.id === item.id) ?? -1;
  let data = $utils.clone(props.modelValue, true);
  if (index > -1) {
    data.splice(index, 1);
  }
  console.log(data);
  emit('update:modelValue', data);
  emit('change', data);
};

watch(
  () => props.modelValue,
  newVal => {
    setLocal(newVal);
  },
  {
    deep: true
  }
);
</script>

<style scoped>
@import url(./index.css);
</style>
