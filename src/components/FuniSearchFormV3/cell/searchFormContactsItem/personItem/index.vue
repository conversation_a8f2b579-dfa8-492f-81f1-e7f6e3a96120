<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-09-23 16:13:28
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-09-28 17:06:21
 * @FilePath: /funi-cloud-web-gsbms/src/apps/component/selectPerson/personItem/index.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div
    class="personItem"
    :class="{
      canChoose: canChoose
    }"
    @click="clickFn"
  >
    <span> {{ name }}</span>
    <el-icon v-if="!canChoose" @click.stop="delItem">
      <Close />
    </el-icon>
  </div>
</template>
<script setup>
const emit = defineEmits(['delItem', 'clickItem']);
const props = defineProps({
  name: String,
  id: String,
  canChoose: {
    type: Boolean,
    default: false
  }
});

const clickFn = () => {
  emit('clickItem', {
    id: props.id,
    name: props.name,
    nickName: props.name
  });
};

const delItem = () => {
  emit('delItem', props.id, props.name);
};

defineExpose({
  // id: props.id
});
</script>
<style scoped>
.personItem {
  height: 24px;
  line-height: 24px;
  max-width: 100%;
  display: inline-flex;
  align-items: center;
  padding: 2px 10px;
  background-color: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
  cursor: pointer;
  font-size: 13px;
  gap: 5px;
  transition: all 0.3s;
  box-sizing: border-box;
  overflow: hidden;
}

.personItem > span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
