/*
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-09-23 14:42:23
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-09-26 10:24:27
 * @FilePath: /funi-cloud-web-gsbms/src/apps/component/selectPerson/hooks/injectionSymbols.ts
 * @Description: 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */
import type { InjectionKey, } from 'vue'
export type SelectPersonApi = {
    selfOrgUrl: string,
    requestTreeUrl: string,
    findAccountByOrgIds: string,
    mode?: 'multiple'
}

export const selectPersonApiKey: InjectionKey<SelectPersonApi> =
    Symbol('selectPersonApiKey')