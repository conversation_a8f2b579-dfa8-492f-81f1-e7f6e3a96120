export const getSelfOrg = (url, params) => {
  if (url) {
    return $http.post(url, params);
  }
};
export const getOrgTree = async (url, params) => {
  let { list } = await $http.fetch(url, params);

  getTopTree(list);

  return list;
};

export const getPersonList = (url, params, config) => {
  return $http.post(url, params, config);
};

const getTopTree = list => {
  const _run = (arr, index) => {
    arr.forEach(item => {
      if (index < 3 && item.children && item.children.length) {
        _run(item.children, index + 1);
      } else {
        item.children = [];
      }
    });
  };
  _run(list, 1);
};

export const setLocal = list => {
  if (list) {
    list = JSON.parse(JSON.stringify(list));
    let data = localStorage.getItem('user_selected_time');
    data = data ? JSON.parse(data) : [];
    for (let i = 0; i < list.length; i++) {
      let index = data.findIndex(item => item.id === list[i].id);
      if (index > -1) {
        let time = (data[index].time || 0) + 1;
        list[i].time = time++;
        data.splice(index, 1);
      }
    }

    list.push(...data);
    localStorage.setItem('user_selected_time', JSON.stringify(list));
  }
};

export const getLocal = async (selectPersonApi, myOrg, value) => {
  value = JSON.parse(JSON.stringify(value));
  let data = localStorage.getItem('user_selected_time');
  data = (data ? JSON.parse(data) : []).filter(item => {
    let bool = !value.find(el => el.id === item.id);
    return bool;
  });
  if (!data.length || data.length < 10) {
    let { list } = await getPersonList(selectPersonApi.findAccountByOrgIds, {
      orgIds: myOrg,
      pageSize: 40
    });
    list = list
      .filter((item, index) => {
        let bool = ![...value, ...data].find(el => el.id === item.accountId);
        return bool;
      })
      .map(item => {
        return {
          name: item.nickname,
          id: item.accountId,
          nickName: item.nickname
        };
      });
    for (let i = 0; i < list.length; i++) {
      data.push(list[i]);
      if (data.length === 10) break;
    }
    setLocal(data);
  }

  return data.slice(0, 10);
};
