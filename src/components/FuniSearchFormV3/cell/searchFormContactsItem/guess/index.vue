<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-09-28 09:47:16
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-09-28 10:45:53
 * @FilePath: /funi-cloud-web-gsbms/src/apps/component/selectPerson/guess/index.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div class="guess">
    <div>
      <span class="guess_title">猜你想找</span>
      <span class="guess_title__tips">{{ selectPersonApi?.mode === 'multiple' ? '可多选' : '单选' }}</span>
    </div>
    <div class="guess_list">
      <TransitionGroup name="list">
        <PersonItem
          ref="person_items"
          v-for="item in user_selected"
          :id="item.id"
          :key="item.id"
          :name="item.nickName || item.name"
          @clickItem="clickItem"
          canChoose
        ></PersonItem>
      </TransitionGroup>
    </div>
  </div>
</template>
<script setup>
import { ref, inject, onMounted } from 'vue';
import { selectPersonApiKey } from './../hooks/injectionSymbols.ts';
import { getSelfOrg, getLocal } from './../hooks/api.js';
import PersonItem from './../personItem/index.vue';
import { useAppStore } from '@/stores/useAppStore';
const selectPersonApi = inject(selectPersonApiKey);
const user_selected = ref([]);
const emit = defineEmits(['cancelFn']);
const person_items = ref();
const useStore = useAppStore();
let myOrg = void 0;
const props = defineProps({
  value: Array
});
onMounted(() => {
  getMyOrg();
});
const getMyOrg = async () => {
  let url = selectPersonApi?.selfOrgUrl;
  let list = [];
  if (url) {
    let data = await getSelfOrg(url, void 0);
    list = data?.list ?? [];
  } else {
    list = useStore?.user?.unit?.id ? [useStore?.user?.unit?.id] : [];
  }

  myOrg = [list.pop()];
};
const _init = async () => {
  if (!myOrg) await getMyOrg();
  let data = await getLocal(selectPersonApi, myOrg, props.value);
  user_selected.value = data;
};

const clickItem = async item => {
  let data = $utils.clone(props.value, true);
  if (selectPersonApi.mode === 'radio') {
    data = [item];
  } else {
    data.push(item);
  }
  emit('addPerson', data);
};
const _reset = () => {
  user_selected.value = [];
};
defineExpose({
  _init,
  _reset
});
</script>
<style scoped>
@import url('./../hooks/transition.css');
.guess {
  padding: 10px;
}
.guess_title {
  color: black;
  font-size: 14px;
}
.guess_title__tips {
  font-size: 12px;
  display: inline-block;
  margin-left: 5px;
  color: rgba(144, 147, 153, 1);
}

.btn_group {
  display: flex;
  justify-content: end;
  align-items: center;
}
.guess_list {
  padding: 10px 0;
  display: flex;
  justify-content: start;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 5px;
}

.person_list {
  width: 100%;
  min-height: var(--min-height);
  max-height: 200px;
  overflow-y: auto;
}
.no_data {
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
