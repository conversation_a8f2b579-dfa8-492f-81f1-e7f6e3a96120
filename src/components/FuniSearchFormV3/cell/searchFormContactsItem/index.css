.iptbox {
    width: 100%;
    height: auto;
    position: relative;
}

.multipleBox {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}


.choosePerson {
    width: 100%;
    border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 11px;
    cursor: pointer;
    box-sizing: border-box;
    box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
}


:deep(.el-form-item.is-error .choosePerson) {
    box-shadow: 0 0 0 1px var(--el-color-danger) inset;
}

.choosePerson:hover {
    border-color: var(--el-border-color-hover);
}

.choosePersonDdtelis {
    border: none;
    padding: 0;
}

.hasValue:hover .icon_default {
    display: none;
}

.hasValue:hover .icon_clear {
    display: block;
}

.input__placeholder {
    font-size: 14px;
    color: var(--el-text-color-placeholder);
    white-space: nowrap;
}

.input__box {
    width: 100%;
    min-height: 32px;
    max-height: 100px;
    overflow-y: auto;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 4px;
    box-sizing: border-box;
}

.input__box:has(.valueItem) {
    padding: 4px 0;
}


.chooseBanner {
    width: 100%;
    height: 70vh;
    display: grid;
    grid-template-columns: var(--gridColumns);
    grid-auto-rows: 70vh;
}


.valueItem {
    display: inline-flex;
    background-color: var(--el-fill-color);
    color: var(--el-color-info);
    font-size: 12px;
    line-height: 24px;
    box-sizing: border-box;
    padding: 0 5px;
    border-radius: 3px;
    justify-content: space-between;
    align-items: center;
}

.valueItem> :deep(i) {
    margin-left: 4px;
    width: 16px;
    height: 16px;
}

.valueItem> :deep(i):hover {
    /* background-color: #fff; */
    color: #fff;
    border-radius: 50%;
    background-color: var(--el-color-info-light-3);
}

.valueItem__single {
    display: inline-block;
    width: auto;
    color: var(--el-text-color-regular);
    line-height: 30px;
}

.icon_clear {
    display: none;
    margin-left: 8px;
}

.search_input {
    display: inline-block;
    width: 20px;
    max-width: 100%;
    flex-grow: 1;
    line-height: 28px;
    height: 28px;
    overflow: hidden;
}

:deep(.search_input .el-input__wrapper:hover),
:deep(.search_input .el-input__wrapper) {
    box-shadow: none;
    padding: 0;
    background-color: unset
}

:deep(.search_input input) {
    border: none;
    outline: none;
    padding: 0;
    color: var(--el-select-multiple-input-color);
    font-size: var(--el-select-font-size);
    appearance: none;
    height: 28px;
    background-color: transparent;
}

.el-form-item.is-error .choosePerson {
    box-shadow: 0 0 0 1px var(--el-color-danger) inset;
}