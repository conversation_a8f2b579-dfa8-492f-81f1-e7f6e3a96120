<!--
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-09-23 16:41:33
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-09-28 15:11:53
 * @FilePath: /funi-cloud-web-gsbms/src/apps/component/selectPerson/selectSearch/index.vue
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
-->
<template>
  <div>
    <el-popover
      ref="popover"
      placement="bottom"
      :show-arrow="false"
      :popper-style="{ padding: '0' }"
      :visible="visible"
      :width="200"
      @hide="hide"
    >
      <template #reference>
        <div class="select_search" :class="class_key" @click.stop="focus">
          <div class="select_search__ipt">
            <el-icon color="rgba(0, 127, 255, 1)" @click="iconClick">
              <CirclePlus />
            </el-icon>
            <el-form style="width: 100%">
              <el-input
                ref="kw_input"
                size="small"
                class="search_person"
                v-model="keyword"
                @input="keywordChange"
                placeholder="搜索并添加人员"
                :validateEvent="false"
              />
            </el-form>
          </div>
        </div>
      </template>
      <div v-click-outside="onClickOutside">
        <Guess ref="guess" v-if="!keyword" :value="value" @cancelFn="cancelFn" @addPerson="addPerson" />
        <div v-else class="el-select-dropdown">
          <div
            v-loading="loading"
            class="el-select-dropdown__wrap person_list"
            :class="{
              'is-multiple': selectPersonApi.mode === 'multiple'
            }"
            :style="{
              '--min-height': loading ? '200px' : 'none'
            }"
            v-infinite-scroll="loadMore"
            :infinite-scroll-distance="20"
            :infinite-scroll-immediate="false"
          >
            <ul v-if="presonList.length" class="el-scrollbar__view el-select-dropdown__list">
              <li
                class="el-select-dropdown__item"
                :class="{
                  selected: !!value?.find(el => el.id === item.accountId)
                }"
                :key="item.accountId"
                @click="clickFun(item)"
                v-for="item in presonList"
              >
                {{ item.nickname }}
              </li>
            </ul>
            <div v-if="!presonList.length && !loading" class="no_data">暂无数据</div>
          </div>
        </div>
      </div>
    </el-popover>
  </div>
</template>
<script setup>
import { onMounted, ref, unref, watch, computed, nextTick, inject } from 'vue';
import { selectPersonApiKey } from './../hooks/injectionSymbols.ts';
import PersonItem from './../personItem/index.vue';
import { getSelfOrg, getPersonList, getLocal } from './../hooks/api.js';
import { ClickOutside as vClickOutside } from 'element-plus';
import Guess from './../guess/index.vue';
const selectPersonApi = inject(selectPersonApiKey);
const visible = ref(false);
const keyword = ref(void 0);
const kw_input = ref();
const guess = ref();
const presonList = ref([]);
const loading = ref(false);
const person_items = ref([]);
const popover = ref();
const props = defineProps({
  value: Array
});
let timer = null;
let pageNo = 1;
let total = 0;
const class_key = ref();
const emits = defineEmits(['change']);
onMounted(() => {
  class_key.value = 'search_person_' + $utils.guid();
});
const keywordChange = async () => {
  await nextTick();
  if (!keyword.value && visible.value) unref(guess)?._init();
  else loading.value = true;

  clearTimeout(timer);
  timer = setTimeout(() => {
    clearTimeout(timer);
    pageNo = 1;
    getUserList();
  }, 600);
};

const loadMore = () => {
  if (Math.ceil(total / 15) <= pageNo) {
    return false;
  }
  pageNo++;
  getUserList();
};

const getUserList = async () => {
  pageNo == 1 && (loading.value = true);
  let data = await getPersonList(selectPersonApi.findAccountByOrgIds, {
    accountName: keyword.value,
    pageNo,
    pageSize: 15
  }).finally(() => {
    loading.value = false;
  });
  if (pageNo === 1) total = data.total;
  if (pageNo === 1) presonList.value = [];
  presonList.value.push(...data.list);
};

const clickFun = async item => {
  let data = JSON.parse(JSON.stringify(props.value));
  if (selectPersonApi.mode === 'multiple') {
    let index = props.value.findIndex(el => el.id === item.accountId);
    if (index < 0) {
      data.push({
        id: item.accountId,
        name: item.nickname,
        nickName: item.nickname
      });
    } else {
      data.splice(index, 1);
    }
  } else {
    data = [
      {
        id: item.accountId,
        name: item.nickname,
        nickName: item.nickname
      }
    ];
  }

  emits('change', data);
  await nextTick();
  popover.value.popperRef.popperInstanceRef.update();
};

const cancelFn = async () => {
  visible.value = false;
  await nextTick();
  setTimeout(() => {
    keyword.value = void 0;
    unref(kw_input)?.blur();
  }, 300);
};

const addPerson = async data => {
  emits('change', data);
  await nextTick();
  unref(guess)?._init();
};
const focus = () => {
  if (!visible.value) {
    unref(guess)?._init();
  }
  kw_input.value.focus();
  visible.value = true;
};

const iconClick = () => {
  focus();
};

const onClickOutside = e => {
  let dom = document.querySelector('.' + unref(class_key));
  if (!e.composedPath().includes(dom)) {
    cancelFn();
  }
};

const hide = () => {
  guess.value && guess.value._reset();
};
</script>
<style scoped>
* {
  box-sizing: border-box;
}

/* :deep(.el-input__wrapper:hover), */
:deep(.select_search__ipt .el-input__wrapper) {
  box-shadow: none !important;
}

:deep(.select_search__ipt .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset !important;
}

.select_search {
  display: inline-flex;
  font-size: 13px;
}

.select_search__ipt {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .el-icon {
    color: var(--el-color-primary);
  }
}

.guess {
  padding: 10px;
}

.guess_title {
  color: black;
  font-size: 14px;
}

.guess_title__tips {
  font-size: 12px;
  display: inline-block;
  margin-left: 5px;
  color: rgba(144, 147, 153, 1);
}

.btn_group {
  display: flex;
  justify-content: end;
  align-items: center;
}

.guess_list {
  padding: 10px 0;
  display: flex;
  justify-content: start;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 5px;
}

.person_list {
  width: 100%;
  min-height: var(--min-height);
  max-height: 200px;
  overflow-y: auto;
}

.no_data {
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
