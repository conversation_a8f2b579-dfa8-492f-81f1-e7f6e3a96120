<template>
  <el-dialog
    style="transition: all 0.5s"
    v-model="dialogVisible"
    title="选择人员"
    :width="(showTotal + 1) * 240 + 40"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
  >
    <div
      :style="{
        '--bar-item-width': barWidth + 'px',
        '--box-padding': boxPadding + 'px',
        '--border-color': 'rgba(228, 231, 237, 1)',
        '--org-group-width': showTotal * 240 + 'px',
        '--el-border-radius-small': selectPersonApi.mode === 'multiple' ? '2px' : '50%'
      }"
      class="multiple"
      v-loading="loading01"
    >
      <div>
        <span class="multiple_title">已选</span>
        <span class="select_total">{{ selectTotal }}人</span>
      </div>
      <div class="choosed_banner" v-if="showPersonItem && dialogVisible">
        <TransitionGroup name="list">
          <PersonItem
            v-for="(item, index) in personData"
            :id="item.id"
            :key="item.id"
            :name="item.nickName || item.name"
            @delItem="delItem"
          />
          <SelectSearch :value="personData" @change="selectSearchChange" />
        </TransitionGroup>
      </div>
      <div class="level_banner">
        <div class="level_banner__item__group">
          <div class="title_box">
            <span class="multiple_title">选择组织</span>
            <span class="crumbs" v-if="crumbs.length > 1">
              <template v-for="(item, index) in crumbs">
                <span
                  :class="{
                    crumbs_item: index < crumbs.length - 1
                  }"
                  @click="index < crumbs.length - 1 && changeTree(item, index)"
                >
                  {{ item }}
                </span>
                <el-icon v-if="index < crumbs.length - 1">
                  <ArrowRight />
                </el-icon>
              </template>
            </span>
          </div>
          <div class="org_banner">
            <div
              class="org_group__item"
              :class="{ last: showOrgList.length - 1 === index }"
              v-for="(item, index) in showOrgList"
              :key="index"
            >
              <el-radio-group
                v-model="orgValue[index]"
                @change="
                  e => {
                    orgChange(e, index);
                  }
                "
              >
                <el-radio v-for="(items, indexs) in item" :id="items.id" :label="items.id" :validate-event="false">
                  <div>
                    <span>{{ items.orgShortName || items.orgName }}</span>
                  </div>
                </el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
        <div class="level_banner__item last">
          <div class="title_box" style="gap: 10px">
            <span class="multiple_title"> 选择人员 </span>
            <el-checkbox v-model="allChoose" id="all" label="all" :validate-event="false" @change="allCheckbox">
              <span>全选</span>
            </el-checkbox>
          </div>
          <div class="person_item">
            <div class="person_search">
              <el-form style="width: 100%">
                <el-input
                  v-model="keyword"
                  placeholder="搜索人员姓名"
                  @input="keywordChange"
                  :suffix-icon="Search"
                  clearable
                  :validateEvent="false"
                />
              </el-form>
            </div>
            <div
              class="person_list"
              v-infinite-scroll="loadMore"
              :infinite-scroll-distance="5"
              :infinite-scroll-immediate="false"
              v-loading="loading"
            >
              <el-checkbox-group v-model="personSelectId" @change="checkedChange" :validate-event="false">
                <TransitionGroup :css="false" @before-enter="onBeforeEnter" @enter="onEnter">
                  <template v-for="(item, index) in presonList" :key="item.id">
                    <el-checkbox
                      :id="item.accountId"
                      :label="item.accountId"
                      :validate-event="false"
                      :data-index="item.animationIndex"
                    >
                      <div class="user-item">
                        <span>{{ item.name || item.account || item.nickname }}</span>
                      </div>
                    </el-checkbox>
                  </template>
                </TransitionGroup>
              </el-checkbox-group>

              <div v-if="!presonList.length && !loading" class="no_data">暂无数据</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :disabled="buttonDisabled" @click="confirm"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { onMounted, ref, watch, computed, nextTick, inject, unref } from 'vue';
import { getOrgTree, getPersonList } from './hooks/api.js';
import { selectPersonApiKey } from './hooks/injectionSymbols.ts';
import { Search } from '@element-plus/icons-vue';
import PersonItem from './personItem/index.vue';
import SelectSearch from './selectSearch/index.vue';
import { onBeforeEnter, onEnter } from './hooks/transition';
const emit = defineEmits(['setWidth', 'change']);
const barWidth = ref(240);
const boxPadding = ref(10);
const dialogVisible = ref(false);
const orgList = ref([]);
const showTotal = computed(() => showOrgList.value.length);
const orgValue = ref([]);
const showOrgList = ref([]);
const presonList = ref([]);
const selectPersonApi = inject(selectPersonApiKey);
const personSelectId = ref([]);
const keyword = ref(void 0);
const timer = ref(null);
const loading = ref(false);
const loading01 = ref(false);
const allChoose = ref(false);
let controller = void 0;

const personData = ref([]);
const props = defineProps({
  value: Array,

  showPersonItem: {
    type: Boolean,
    default: true
  },
  buttonDis: {
    type: Boolean,
    default: false
  }
});

const selectTotal = computed(() => personData?.value?.length || 0);
const orgIds = ref(void 0);
let total = 0;
let pageData = {
  pageNo: 1,
  pageSize: 15
};
onMounted(() => {
  emit('setWidth', barWidth.value * 2 + boxPadding.value);
});
const buttonDisabled = computed(() => {
  if (props.buttonDis) {
    return !selectTotal.value;
  }
  return false;
});

const _init = async () => {
  personSelectId.value = props.value ? props.value.map(item => item.id) : [];
  personData.value = props.value ? $utils.clone(props.value, true) : [];
  showOrgList.value = [[]];
  orgValue.value = [];
  orgIds.value = [];
  presonList.value = [];
  keyword.value = void 0;
  loading01.value = true;
  let list = await getOrgTree(selectPersonApi.requestTreeUrl, void 0).finally(() => (loading01.value = false));
  orgList.value = list;
  showOrgList.value[0] = orgList.value;
  await getAllUser();
};

const show = () => {
  _init();
  dialogVisible.value = true;
};
const orgChange = async (e, index) => {
  showOrgList.value = showOrgList.value.slice(0, index + 1);
  orgValue.value = orgValue.value.slice(0, index + 1);
  allChoose.value = false;
  await nextTick();
  let object = showOrgList.value[index]?.find(item => item.id === e);
  let list = object ? object['children'] : [];

  if (list.length) {
    showOrgList.value[index + 1] = list;
  }
  orgIds.value = [showOrgList.value[index]?.find(item => item.id === orgValue.value[index])?.id];
  keyword.value = void 0;
  pageData.pageNo = 1;
  getAllUser();
};

const getAllUser = async ({ type } = {}) => {
  if (pageData.pageNo === 1) {
    presonList.value = [];
    loading.value = true;
    controller && controller.abort();
  }
  controller = new AbortController();
  let data = await getPersonList(
    selectPersonApi.findAccountByOrgIds,
    {
      orgIds: orgIds.value,
      accountName: keyword.value,
      isDimission: '0',
      security: $utils.SM.sm3(Date.now()),
      flag: type && type == 'all' ? false : true,
      ...pageData
    },
    {
      signal: controller.signal
    }
  ).finally(() => {
    controller = void 0;
    timer.value = setTimeout(() => {
      clearTimeout(timer.value);
      loading.value = false;
    }, 100);
  });
  if (pageData.pageNo === 1) total = data.total || data.list.length;
  if (pageData.pageNo === 1) presonList.value = [];
  presonList.value.push(...(data.list || []).map((item, index) => ({ ...item, animationIndex: index })));

  if (type && type == 'all') {
    presonList.value.map(item => {
      if (item?.accountId && !personSelectId.value.includes(item?.accountId)) {
        personSelectId.value.push(item?.accountId);
      }
    });
    checkedChange();
  }
};

const loadMore = () => {
  if (Math.ceil(total / pageData.pageSize) <= pageData.pageNo || presonList.value.length === total) {
    return false;
  }
  pageData.pageNo++;
  getAllUser();
};

const keywordChange = async () => {
  await nextTick();
  timer.value = setTimeout(() => {
    clearTimeout(timer.value);
    pageData.pageNo = 1;
    getAllUser();
  }, 600);
};
const delItem = id => {
  let index = personData.value.findIndex(item => item.id === id);
  if (index > -1) personData.value.splice(index, 1);
  personSelectId.value = personData.value ? personData.value.map(item => item.id) : [];
};

const checkedChange = async () => {
  await nextTick();

  presonList.value.forEach(item => {
    let index = personData.value.findIndex(el => el.id === item.accountId);
    if (index > -1) personData.value.splice(index, 1);
  });
  if (selectPersonApi.mode === 'radio') {
    personSelectId.value = personSelectId.value.slice(-1);
    await nextTick();
    personSelectId.value.forEach(item => {
      let object = presonList.value.find(el => el.accountId === item);
      if (object)
        personData.value = [
          {
            id: object.accountId,
            name: object.nickname,
            nickName: object.nickname
          }
        ];
    });
  } else {
    personSelectId.value.forEach(item => {
      let object = presonList.value.find(el => el.accountId === item);
      if (object)
        personData.value.push({
          id: object.accountId,
          name: object.nickname,
          nickName: object.nickname
        });
    });
  }
};

const confirm = () => {
  emit('change', unref(personData));
  dialogVisible.value = false;
};
const changeTree = (e, index) => {
  orgChange(orgValue.value[index], index);
};
const crumbs = computed(() => {
  showOrgList.value;
  let list = [];
  orgValue.value.forEach((item, index) => {
    let object = showOrgList.value[index]?.find(el => el.id === item);
    object && list.push(object.orgShortName || object.orgName);
  });
  return list;
});

const selectSearchChange = v => {
  personData.value = $utils.clone(v, true);
  personSelectId.value = personData.value ? personData.value.map(item => item.id) : [];
};

const allCheckbox = e => {
  if (e) {
    pageData.pageNo = 1;
    getAllUser({ type: 'all' });
  } else {
    presonList.value.map(item => {
      if (item?.accountId && personSelectId.value.includes(item?.accountId)) {
        let index = personSelectId.value.findIndex(el => el === item?.accountId);
        personSelectId.value.splice(index, 1);
      }
    });
    checkedChange();
  }
};
defineExpose({
  show
});
</script>

<style scoped>
@import url('./hooks/transition.css');
* {
  box-sizing: border-box;
}

.multiple {
  min-width: calc(var(--bar-item-width) * 2);
}

.multiple_title {
  color: black;
  font-size: 15px;
  font-weight: 500;
}

.select_total {
  color: rgba(144, 147, 153, 1);
  font-size: 12px;
  display: inline-block;
  margin-left: 10px;
}

.level_banner {
  display: flex;
  justify-content: start;
  align-items: center;
  border: 1px solid var(--border-color);
  height: 43vh;
  width: max-content;
}

.title_box {
  width: 100%;
  height: 40px;
  border-bottom: 1px solid var(--border-color);

  padding: 10px;
  display: flex;
  justify-content: start;
  align-items: center;
}

.level_banner__item {
  width: 240px;
  height: 100%;
}

.level_banner__item:not(.last) {
  border-right: 1px solid var(--border-color);
}

.level_banner__item__group {
  height: 100%;
  width: auto;
  border-right: 1px solid var(--border-color);
}

.org_banner {
  width: var(--org-group-width);
  height: calc(100% - 40px);
  display: flex;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
  transition: all 0.5s;
}

.org_group__item {
  width: 240px;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
  padding: 0 10px;
}

.org_group__item:not(.last) {
  border-right: 1px solid var(--border-color);
}

:deep(.el-radio-group) {
  display: inline-flex;
  flex-direction: column;
  align-items: flex-start;
  flex-wrap: wrap;
  font-size: 0;
}

.person_item {
  width: 100%;
  height: calc(100% - 40px);
  padding: 10px 6px 0;
}

.person_search {
  height: 35px;
  display: flex;
  align-items: center;
}

.person_list {
  width: 100%;
  height: calc(100% - 35px);
  overflow-y: auto;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-direction: column;
}

.choosed_banner {
  display: flex;
  justify-content: start;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 10px;
  padding: 10px 0 10px;
}

.dialog-footer {
  display: flex;
  justify-content: end;
  align-items: center;
}

.no_data {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-input-border-color) !important;
}

.crumbs {
  color: rgba(144, 147, 153, 1);
  display: inline-flex;
  align-items: center;
  margin-left: 20px;
  font-size: 14px;
}

.crumbs_item {
  cursor: pointer;
  color: var(--el-color-primary);
}
</style>
