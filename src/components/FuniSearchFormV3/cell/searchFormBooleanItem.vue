<template>
  <el-select clearable v-model="selectValue" style="width: 100%" placeholder="请选择" :loading="loading">
    <el-option v-for="item in options" :key="item.value" :label="item.key" :value="item.value" />
  </el-select>
</template>

<script setup>
import { ref, watchEffect, inject, computed, watch } from 'vue';

defineOptions({
  name: 'searchFormBooleanItem'
});

const props = defineProps({
  modelValue: [String, Array, Number, Object, Boolean],
  field: String,
  prompt: String
});

const emit = defineEmits(['update:modelValue']);

const selectValue = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
});

const options = computed(() => {
  try {
    const promptObj = JSON.parse(props.prompt);
    return [
      { key: promptObj.trueValue, value: '1' },
      { key: promptObj.falseValue, value: '0' }
    ];
  } catch (error) {
    return [
      { key: '是', value: '1' },
      { key: '否', value: '0' }
    ];
  }
});
</script>
