<template>
  <div>
    <el-dialog v-model="visible" align-center width="450px" center :show-close="false">
      <div class="successBanner">
        <funi-icon style="font-size: 50px; color: #72c140" icon="ep:success-filled" />
        <span>操作成功</span>
      </div>
      <template #footer>
        <el-button type="primary" @click="success">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useMultiTab } from '@/utils/hooks/useMultiTab';

const visible = ref(false);
const multiTab = useMultiTab();

const show = () => (visible.value = true);
const success = () => multiTab.closeCurrentPage();

defineExpose({ show });
</script>

<style scoped>
.successBanner {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 15px;
}

.successBanner > span {
  display: inline-flex;
  font-size: 18px;
}

:deep(.el-dialog__header) {
  display: none;
}

:deep(.el-dialog__body) {
  padding: 20px 0 10px;
}
</style>
