import Vrouter from '@/router';
import AppApis from '@/apis/app';
import { useAppStore } from '@/stores/useAppStore';
import { usePermissionStore } from '@/stores/usePermissionStore.js';

/**
 * @description 工作流
 * @returns
 */
export const useBusiness = () => {
  const appStore = useAppStore();
  const router = Vrouter;

  const open = (businessConfigCode, businessJson, applicationReason, businessName) => {
    return AppApis.startBusiness({
      businessConfigCode,
      sysId: appStore.system?.id,
      businessJson,
      applicationReason,
      businessName
    });
  };

  const submit = (businessConfigCode, businessId, businessExecutionType, businessName) => {
    return AppApis.executeBusiness({
      businessConfigCode,
      sysId: appStore.system?.id,
      businessId,
      businessExecutionType,
      businessJson: {},
      businessName
    });
  };

  const del = businessId => {
    return AppApis.delBusiness({ sysId: appStore.system?.id, businessId });
  };

  const auditEnd = func => {
    const permissionStore = usePermissionStore();
    const menuId = router.currentRoute.value.meta.menus[0];
    const menu = permissionStore.menus.filter(item => item.id === menuId);
    func && func();
    if (menu && menu.length) {
      const name = menu[0]?.defaultPage?.route?.name;
      name && router.push({ name });
    }
  };

  return {
    /**
     * @description 开启工作流
     * @param {*} businessConfigCode
     * @param {*} businessJson
     * @param {*} applicationReason
     * @param {*} businessName
     * @returns Promise<any>
     */
    open,

    /**
     * @description 提交工作流
     * @param {*} businessConfigCode
     * @param {*} businessId
     * @param {*} businessExecutionType
     * @param {*} businessName
     * @returns Promise<any>
     */
    submit,

    /**
     * @description 删除工作流
     * @param {*} businessId
     * @returns Promise<any>
     */
    del,

    /**
     * @description 审核结束跳转
     * @param {*} func
     */
    auditEnd
  };
};
