<!-- 采购计划管理 - 新建页面 -->
<!--
  AI Agent 页面创建模板 - 新建页面
  
  模板说明：
  - 基于 FuniDetail 组件的标准新建页面
  - 专门用于新建采购计划
  - 集成 FuniForm 表单组件和标准操作流程
-->
<template>
  <funi-detail :steps="stepsConfig" :detailHeadOption="detailHeadOption" @onStepChange="handleStepChange">
    <template #step1>
      <funi-form ref="formRef" :schema="formSchema" :model="formData" :rules="formRules" label-width="120px" />
    </template>
  </funi-detail>
</template>

<script setup lang="jsx">
import { useRouter } from 'vue-router';
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useProcurementPlanStore } from '../../store.js';

// 路由和Store
const router = useRouter();
const procurementPlanStore = useProcurementPlanStore();

// 表单引用
const formRef = ref(null);

// 表单数据
const formData = reactive({
  planName: '',
  purchaseType: '',
  bidCategory: '公开招标',
  purchaseMethod: '公开招标',
  bidAmount: 0,
  fundSource: '',
  bidTime: '',
  planStartTime: '',
  planEndTime: '',
  organizationMethod: '自行组织',
  agency: '',
  annualPlan: '',
  applicant: '',
  projectName: '',
  projectType: '',
  projectOwner: '',
  subCompany: '',
  projectDescription: '',
  projectRemark: ''
});

// 步骤配置
const stepsConfig = reactive([
  {
    title: '新建采购计划',
    name: 'step1'
  }
]);

// 详情页头部配置
const detailHeadOption = reactive({
  title: '新建采购计划',
  btns: [
    {
      label: '保存',
      type: 'primary',
      on: {
        click: () => handleSave()
      }
    },
    {
      label: '保存并提交',
      type: 'success',
      on: {
        click: () => handleSaveAndSubmit()
      }
    },
    {
      label: '返回',
      on: {
        click: () => handleBack()
      }
    }
  ]
});

// 表单配置
const formSchema = reactive([
  {
    prop: 'planName',
    label: '计划名称',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入计划名称'
    },
    span: 12
  },
  {
    prop: 'purchaseType',
    label: '采购类型',
    component: 'el-select',
    componentProps: {
      placeholder: '请选择采购类型',
      options: [
        { label: '货物', value: '货物' },
        { label: '工程', value: '工程' },
        { label: '服务', value: '服务' }
      ]
    },
    span: 12
  },
  {
    prop: 'bidCategory',
    label: '招标类别',
    component: 'el-select',
    componentProps: {
      placeholder: '请选择招标类别',
      options: [
        { label: '公开招标', value: '公开招标' },
        { label: '邀请招标', value: '邀请招标' }
      ]
    },
    span: 12
  },
  {
    prop: 'purchaseMethod',
    label: '采购方式',
    component: 'el-select',
    componentProps: {
      placeholder: '请选择采购方式',
      options: [
        { label: '公开招标', value: '公开招标' },
        { label: '邀请招标', value: '邀请招标' },
        { label: '竞争性谈判', value: '竞争性谈判' },
        { label: '单一来源', value: '单一来源' },
        { label: '询价', value: '询价' }
      ]
    },
    span: 12
  },
  {
    prop: 'bidAmount',
    label: '预算金额(万元)',
    component: 'el-input-number',
    componentProps: {
      placeholder: '请输入预算金额',
      min: 0,
      precision: 2
    },
    span: 12
  },
  {
    prop: 'fundSource',
    label: '资金来源',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入资金来源'
    },
    span: 12
  },
  {
    prop: 'bidTime',
    label: '计划招标时间',
    component: 'el-date-picker',
    componentProps: {
      type: 'date',
      placeholder: '请选择计划招标时间',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    span: 12
  },
  {
    prop: 'planStartTime',
    label: '计划开始时间',
    component: 'el-date-picker',
    componentProps: {
      type: 'date',
      placeholder: '请选择计划开始时间',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    span: 12
  },
  {
    prop: 'planEndTime',
    label: '计划结束时间',
    component: 'el-date-picker',
    componentProps: {
      type: 'date',
      placeholder: '请选择计划结束时间',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    span: 12
  },
  {
    prop: 'organizationMethod',
    label: '组织方式',
    component: 'el-select',
    componentProps: {
      placeholder: '请选择组织方式',
      options: [
        { label: '自行组织', value: '自行组织' },
        { label: '委托代理', value: '委托代理' }
      ]
    },
    span: 12
  },
  {
    prop: 'agency',
    label: '代理机构',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入代理机构（委托代理时填写）'
    },
    span: 12
  },
  {
    prop: 'annualPlan',
    label: '年度计划',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入年度计划'
    },
    span: 12
  },
  {
    prop: 'applicant',
    label: '申请人',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入申请人'
    },
    span: 12
  },
  {
    prop: 'projectName',
    label: '项目名称',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入项目名称'
    },
    span: 12
  },
  {
    prop: 'projectType',
    label: '项目类型',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入项目类型'
    },
    span: 12
  },
  {
    prop: 'projectOwner',
    label: '项目业主',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入项目业主'
    },
    span: 12
  },
  {
    prop: 'subCompany',
    label: '子公司',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入子公司'
    },
    span: 12
  },
  {
    prop: 'projectDescription',
    label: '项目描述',
    component: 'el-input',
    componentProps: {
      type: 'textarea',
      rows: 4,
      placeholder: '请详细描述项目内容、需求和要求'
    },
    span: 24
  },
  {
    prop: 'projectRemark',
    label: '项目备注',
    component: 'el-input',
    componentProps: {
      type: 'textarea',
      rows: 3,
      placeholder: '请输入项目备注信息'
    },
    span: 24
  }
]);

// 表单验证规则
const formRules = reactive({
  planName: [
    { required: true, message: '请输入计划名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  purchaseType: [{ required: true, message: '请选择采购类型', trigger: 'change' }],
  bidCategory: [{ required: true, message: '请选择招标类别', trigger: 'change' }],
  purchaseMethod: [{ required: true, message: '请选择采购方式', trigger: 'change' }],
  bidAmount: [
    { required: true, message: '请输入预算金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '预算金额必须大于等于0', trigger: 'blur' }
  ],
  projectName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
  projectOwner: [{ required: true, message: '请输入项目业主', trigger: 'blur' }],
  applicant: [{ required: true, message: '请输入申请人', trigger: 'blur' }]
});

// 事件处理函数
const handleStepChange = step => {
  console.log('步骤切换:', step);
};

const handleSave = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) return;

    // 保存数据
    await procurementPlanStore.createProcurementPlan(formData);
    ElMessage.success('保存成功');

    // 返回列表页
    handleBack();
  } catch (error) {
    ElMessage.error('保存失败');
  }
};

const handleSaveAndSubmit = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) return;

    // 保存数据
    const result = await procurementPlanStore.createProcurementPlan(formData);

    // 提交审批
    if (result && result.id) {
      await procurementPlanStore.submitProcurementPlan(result.id);
      ElMessage.success('保存并提交成功');
    } else {
      ElMessage.success('保存成功');
    }

    // 返回列表页
    handleBack();
  } catch (error) {
    ElMessage.error('保存失败');
  }
};

const handleBack = () => {
  router.push('/procurement-plan');
};

// 组件挂载
onMounted(() => {
  console.log('采购计划管理新建页面已挂载');
});
</script>

<style scoped>
/* 页面特定样式 */
</style>
