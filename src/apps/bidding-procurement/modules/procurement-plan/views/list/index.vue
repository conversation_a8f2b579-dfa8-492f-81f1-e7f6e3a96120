<!-- 采购计划管理 - 列表页面 -->
<!--
  AI Agent 页面创建模板 - 标准列表页
  
  模板说明：
  - 基于 FuniListPageV2 组件的标准列表页面
  - 支持多页签配置和标准CRUD操作
  - 集成标准交互模式
  - 支持工作流待办/已办页签切换
-->
<template>
  <funi-list-page-v2 :cardTab="cardTabConfig" @headBtnClick="handleHeadBtnClick" />
</template>

<script setup lang="jsx">
import { useRouter } from 'vue-router';
import { reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useProcurementPlanStore } from '../../store.js';

// 路由和Store
const router = useRouter();
const procurementPlanStore = useProcurementPlanStore();

// 页签配置
const cardTabConfig = reactive([
  {
    key: 'pending',
    label: '待办',
    api: '/api/procurement-plans/pending',
    curdOption: {
      // 表格列配置
      columns: [
        {
          prop: 'planCode',
          label: '计划编号',
          fixed: 'left',
          width: 150
        },
        {
          prop: 'planName',
          label: '计划名称',
          width: 200
        },
        {
          prop: 'purchaseType',
          label: '采购类型',
          width: 120
        },
        {
          prop: 'bidAmount',
          label: '预算金额(万元)',
          width: 150,
          render: ({ row }) => <span>{row.bidAmount ? row.bidAmount.toFixed(2) : '0.00'}</span>
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          render: ({ row }) => <el-tag type={getStatusTagType(row.status)}>{getStatusText(row.status)}</el-tag>
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180
        }
      ],
      // 操作按钮配置
      btns: [
        {
          label: '新建采购计划',
          type: 'primary',
          auth: '采购计划管理:create'
        },
        {
          label: '导入',
          type: 'default',
          auth: '采购计划管理:import'
        },
        {
          label: '导出',
          type: 'default',
          auth: '采购计划管理:export'
        }
      ],
      // 行操作按钮
      rowBtns: [
        {
          label: '查看',
          type: 'text'
        },
        {
          label: '编辑',
          type: 'text',
          auth: '采购计划管理:edit',
          show: row => row.status === 'draft'
        },
        {
          label: '提交',
          type: 'text',
          auth: '采购计划管理:submit',
          show: row => row.status === 'draft'
        },
        {
          label: '撤回',
          type: 'text',
          auth: '采购计划管理:withdraw',
          show: row => row.status === 'submitted'
        },
        {
          label: '删除',
          type: 'text',
          auth: '采购计划管理:delete',
          show: row => row.status === 'draft'
        }
      ]
    }
  },
  {
    key: 'completed',
    label: '已办',
    api: '/api/procurement-plans/completed',
    curdOption: {
      // 表格列配置
      columns: [
        {
          prop: 'planCode',
          label: '计划编号',
          fixed: 'left',
          width: 150
        },
        {
          prop: 'planName',
          label: '计划名称',
          width: 200
        },
        {
          prop: 'purchaseType',
          label: '采购类型',
          width: 120
        },
        {
          prop: 'bidAmount',
          label: '预算金额(万元)',
          width: 150,
          render: ({ row }) => <span>{row.bidAmount ? row.bidAmount.toFixed(2) : '0.00'}</span>
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          render: ({ row }) => <el-tag type={getStatusTagType(row.status)}>{getStatusText(row.status)}</el-tag>
        },
        {
          prop: 'updateTime',
          label: '处理时间',
          width: 180
        }
      ],
      // 操作按钮配置
      btns: [
        {
          label: '导出',
          type: 'default',
          auth: '采购计划管理:export'
        }
      ],
      // 行操作按钮
      rowBtns: [
        {
          label: '查看',
          type: 'text'
        }
      ]
    }
  },
  {
    key: 'all',
    label: '全部采购计划',
    api: '/api/procurement-plans/list',
    curdOption: {
      // 表格列配置
      columns: [
        {
          prop: 'planCode',
          label: '计划编号',
          fixed: 'left',
          width: 150
        },
        {
          prop: 'planName',
          label: '计划名称',
          width: 200
        },
        {
          prop: 'purchaseType',
          label: '采购类型',
          width: 120
        },
        {
          prop: 'bidAmount',
          label: '预算金额(万元)',
          width: 150,
          render: ({ row }) => <span>{row.bidAmount ? row.bidAmount.toFixed(2) : '0.00'}</span>
        },
        {
          prop: 'status',
          label: '状态',
          width: 100,
          render: ({ row }) => <el-tag type={getStatusTagType(row.status)}>{getStatusText(row.status)}</el-tag>
        },
        {
          prop: 'createTime',
          label: '创建时间',
          width: 180
        },
        {
          prop: 'updateTime',
          label: '更新时间',
          width: 180
        }
      ],
      // 操作按钮配置
      btns: [
        {
          label: '新建采购计划',
          type: 'primary',
          auth: '采购计划管理:create'
        },
        {
          label: '导入',
          type: 'default',
          auth: '采购计划管理:import'
        },
        {
          label: '导出',
          type: 'default',
          auth: '采购计划管理:export'
        }
      ],
      // 行操作按钮
      rowBtns: [
        {
          label: '查看',
          type: 'text'
        },
        {
          label: '编辑',
          type: 'text',
          auth: '采购计划管理:edit',
          show: row => row.status === 'draft'
        },
        {
          label: '删除',
          type: 'text',
          auth: '采购计划管理:delete',
          show: row => row.status === 'draft'
        }
      ]
    }
  }
]);

// 状态文本映射
const getStatusText = status => {
  const statusMap = {
    draft: '草稿',
    submitted: '已提交',
    approved: '已审批',
    rejected: '已驳回',
    withdrawn: '已撤回'
  };
  return statusMap[status] || '未知';
};

// 状态标签类型映射
const getStatusTagType = status => {
  const typeMap = {
    draft: 'info',
    submitted: 'warning',
    approved: 'success',
    rejected: 'danger',
    withdrawn: 'info'
  };
  return typeMap[status] || 'info';
};

// 事件处理函数
const handleHeadBtnClick = btn => {
  console.log('头部按钮点击:', btn);
};

const handleCreate = () => {
  router.push('/procurement-plan/create');
};

const handleDetail = row => {
  router.push(`/procurement-plan/detail/${row.id}`);
};

const handleEdit = row => {
  router.push(`/procurement-plan/detail/${row.id}?mode=edit`);
};

const handleSubmit = async row => {
  try {
    await ElMessageBox.confirm('确定要提交此采购计划吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    await procurementPlanStore.submitProcurementPlan(row.id);
    ElMessage.success('提交成功');
    // 刷新列表
    window.location.reload();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('提交失败');
    }
  }
};

const handleWithdraw = async row => {
  try {
    await ElMessageBox.confirm('确定要撤回此采购计划吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    await procurementPlanStore.withdrawProcurementPlan(row.id);
    ElMessage.success('撤回成功');
    // 刷新列表
    window.location.reload();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('撤回失败');
    }
  }
};

const handleDelete = async row => {
  try {
    await ElMessageBox.confirm('确定要删除此采购计划吗？删除后无法恢复。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    await procurementPlanStore.deleteProcurementPlan(row.id);
    ElMessage.success('删除成功');
    // 刷新列表
    window.location.reload();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};

const handleImport = () => {
  ElMessage.info('导入功能开发中...');
};

const handleExport = () => {
  ElMessage.info('导出功能开发中...');
};

// 组件挂载
onMounted(() => {
  console.log('采购计划管理列表页面已挂载');
});
</script>

<style scoped>
/* 页面特定样式 */
</style>
