<!-- 采购计划管理 - 详情页面 -->
<!--
  AI Agent 页面创建模板 - 标准详情页
  
  模板说明：
  - 基于 FuniDetail 组件的标准详情页面
  - 支持查看、编辑、新建三种模式
  - 集成 FuniForm 表单组件和标准操作流程
  - 支持采购计划特有字段和工作流功能
-->
<template>
  <funi-detail :steps="stepsConfig" :detailHeadOption="detailHeadOption" @onStepChange="handleStepChange">
    <template #step1>
      <funi-form ref="formRef" :schema="formSchema" :model="formData" :rules="formRules" label-width="120px" />
    </template>

    <template #step2 v-if="!isCreateMode">
      <funi-file-table :businessId="route.params.id" :readonly="!isEditMode" title="相关附件" />
    </template>

    <template #step3 v-if="!isCreateMode && !isEditMode">
      <funi-bus-audit-drawer :businessId="route.params.id" :readonly="true" title="审批记录" />
    </template>
  </funi-detail>
</template>

<script setup lang="jsx">
import { useRouter, useRoute } from 'vue-router';
import { reactive, ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { useProcurementPlanStore } from '../../store.js';

// 路由和Store
const router = useRouter();
const route = useRoute();
const procurementPlanStore = useProcurementPlanStore();

// 表单引用
const formRef = ref(null);

// 页面模式
const pageMode = computed(() => {
  if (route.path.includes('/create')) return 'create';
  if (route.query.mode === 'edit') return 'edit';
  return 'view';
});

// 是否为编辑模式
const isEditMode = computed(() => pageMode.value !== 'view');
const isCreateMode = computed(() => pageMode.value === 'create');

// 表单数据
const formData = reactive({
  planName: '',
  purchaseType: '',
  bidCategory: '',
  purchaseMethod: '',
  bidAmount: 0,
  fundSource: '',
  bidTime: '',
  planStartTime: '',
  planEndTime: '',
  organizationMethod: '',
  agency: '',
  annualPlan: '',
  applicant: '',
  projectName: '',
  projectType: '',
  projectOwner: '',
  subCompany: '',
  projectDescription: '',
  projectRemark: ''
});

// 步骤配置
const stepsConfig = computed(() => {
  const steps = [
    {
      title: pageMode.value === 'create' ? '新建采购计划' : '采购计划信息',
      name: 'step1'
    }
  ];

  if (!isCreateMode.value) {
    steps.push({
      title: '相关附件',
      name: 'step2'
    });

    if (!isEditMode.value) {
      steps.push({
        title: '审批记录',
        name: 'step3'
      });
    }
  }

  return steps;
});

// 详情页头部配置
const detailHeadOption = reactive({
  title: computed(() => {
    if (pageMode.value === 'create') return '新建采购计划';
    if (pageMode.value === 'edit') return '编辑采购计划';
    return '采购计划详情';
  }),
  btns: computed(() => {
    const btns = [];

    if (pageMode.value === 'view') {
      btns.push({
        label: '编辑',
        type: 'primary',
        auth: '采购计划管理:edit',
        on: {
          click: () => handleEdit()
        }
      });
    }

    if (isEditMode.value) {
      btns.push({
        label: '保存',
        type: 'primary',
        on: {
          click: () => handleSave()
        }
      });
    }

    btns.push({
      label: '返回',
      on: {
        click: () => handleBack()
      }
    });

    return btns;
  })
});

// 表单配置
const formSchema = reactive([
  {
    prop: 'planName',
    label: '计划名称',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入计划名称',
      disabled: !isEditMode.value
    },
    span: 12
  },
  {
    prop: 'purchaseType',
    label: '采购类型',
    component: 'el-select',
    componentProps: {
      placeholder: '请选择采购类型',
      disabled: !isEditMode.value,
      options: [
        { label: '货物', value: '货物' },
        { label: '工程', value: '工程' },
        { label: '服务', value: '服务' }
      ]
    },
    span: 12
  },
  {
    prop: 'bidCategory',
    label: '招标类别',
    component: 'el-select',
    componentProps: {
      placeholder: '请选择招标类别',
      disabled: !isEditMode.value,
      options: [
        { label: '公开招标', value: '公开招标' },
        { label: '邀请招标', value: '邀请招标' }
      ]
    },
    span: 12
  },
  {
    prop: 'purchaseMethod',
    label: '采购方式',
    component: 'el-select',
    componentProps: {
      placeholder: '请选择采购方式',
      disabled: !isEditMode.value,
      options: [
        { label: '公开招标', value: '公开招标' },
        { label: '邀请招标', value: '邀请招标' },
        { label: '竞争性谈判', value: '竞争性谈判' },
        { label: '单一来源', value: '单一来源' },
        { label: '询价', value: '询价' }
      ]
    },
    span: 12
  },
  {
    prop: 'bidAmount',
    label: '预算金额(万元)',
    component: 'el-input-number',
    componentProps: {
      placeholder: '请输入预算金额',
      disabled: !isEditMode.value,
      min: 0,
      precision: 2
    },
    span: 12
  },
  {
    prop: 'fundSource',
    label: '资金来源',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入资金来源',
      disabled: !isEditMode.value
    },
    span: 12
  },
  {
    prop: 'bidTime',
    label: '计划招标时间',
    component: 'el-date-picker',
    componentProps: {
      type: 'date',
      placeholder: '请选择计划招标时间',
      disabled: !isEditMode.value,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    span: 12
  },
  {
    prop: 'planStartTime',
    label: '计划开始时间',
    component: 'el-date-picker',
    componentProps: {
      type: 'date',
      placeholder: '请选择计划开始时间',
      disabled: !isEditMode.value,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    span: 12
  },
  {
    prop: 'planEndTime',
    label: '计划结束时间',
    component: 'el-date-picker',
    componentProps: {
      type: 'date',
      placeholder: '请选择计划结束时间',
      disabled: !isEditMode.value,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    span: 12
  },
  {
    prop: 'organizationMethod',
    label: '组织方式',
    component: 'el-select',
    componentProps: {
      placeholder: '请选择组织方式',
      disabled: !isEditMode.value,
      options: [
        { label: '自行组织', value: '自行组织' },
        { label: '委托代理', value: '委托代理' }
      ]
    },
    span: 12
  },
  {
    prop: 'agency',
    label: '代理机构',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入代理机构',
      disabled: !isEditMode.value
    },
    span: 12
  },
  {
    prop: 'annualPlan',
    label: '年度计划',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入年度计划',
      disabled: !isEditMode.value
    },
    span: 12
  },
  {
    prop: 'applicant',
    label: '申请人',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入申请人',
      disabled: !isEditMode.value
    },
    span: 12
  },
  {
    prop: 'projectName',
    label: '项目名称',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入项目名称',
      disabled: !isEditMode.value
    },
    span: 12
  },
  {
    prop: 'projectType',
    label: '项目类型',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入项目类型',
      disabled: !isEditMode.value
    },
    span: 12
  },
  {
    prop: 'projectOwner',
    label: '项目业主',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入项目业主',
      disabled: !isEditMode.value
    },
    span: 12
  },
  {
    prop: 'subCompany',
    label: '子公司',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入子公司',
      disabled: !isEditMode.value
    },
    span: 12
  },
  {
    prop: 'projectDescription',
    label: '项目描述',
    component: 'el-input',
    componentProps: {
      type: 'textarea',
      rows: 4,
      placeholder: '请输入项目描述',
      disabled: !isEditMode.value
    },
    span: 24
  },
  {
    prop: 'projectRemark',
    label: '项目备注',
    component: 'el-input',
    componentProps: {
      type: 'textarea',
      rows: 3,
      placeholder: '请输入项目备注',
      disabled: !isEditMode.value
    },
    span: 24
  }
]);

// 表单验证规则
const formRules = reactive({
  planName: [
    { required: true, message: '请输入计划名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  purchaseType: [{ required: true, message: '请选择采购类型', trigger: 'change' }],
  bidAmount: [
    { required: true, message: '请输入预算金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '预算金额必须大于等于0', trigger: 'blur' }
  ],
  projectName: [{ required: true, message: '请输入项目名称', trigger: 'blur' }]
});

// 事件处理函数
const handleStepChange = step => {
  console.log('步骤切换:', step);
};

const handleEdit = () => {
  router.push({ query: { ...route.query, mode: 'edit' } });
};

const handleSave = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) return;

    // 保存数据
    if (pageMode.value === 'create') {
      await procurementPlanStore.createProcurementPlan(formData);
      ElMessage.success('创建成功');
    } else {
      await procurementPlanStore.updateProcurementPlan(route.params.id, formData);
      ElMessage.success('更新成功');
    }

    // 返回列表页
    handleBack();
  } catch (error) {
    ElMessage.error(pageMode.value === 'create' ? '创建失败' : '更新失败');
  }
};

const handleBack = () => {
  router.push('/procurement-plan');
};

// 加载数据
const loadData = async () => {
  if (pageMode.value !== 'create' && route.params.id) {
    try {
      const data = await procurementPlanStore.getProcurementPlan(route.params.id);
      Object.assign(formData, data);
    } catch (error) {
      ElMessage.error('加载数据失败');
    }
  }
};

// 组件挂载
onMounted(() => {
  loadData();
  console.log('采购计划管理详情页面已挂载，模式:', pageMode.value);
});
</script>

<style scoped>
/* 页面特定样式 */
</style>
