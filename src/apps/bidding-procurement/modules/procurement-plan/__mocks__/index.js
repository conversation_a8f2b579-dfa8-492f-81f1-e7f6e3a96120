/**
 * 采购计划管理 - Mock数据
 * 
 * 用于开发和测试的模拟数据
 */

// 采购计划状态枚举
export const PlanStatus = {
  DRAFT: 'draft',
  SUBMITTED: 'submitted',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  WITHDRAWN: 'withdrawn'
};

// 采购类型枚举
export const PurchaseType = {
  GOODS: '货物',
  ENGINEERING: '工程',
  SERVICE: '服务'
};

// 采购方式枚举
export const PurchaseMethod = {
  PUBLIC_BIDDING: '公开招标',
  INVITED_BIDDING: '邀请招标',
  COMPETITIVE_NEGOTIATION: '竞争性谈判',
  SINGLE_SOURCE: '单一来源',
  INQUIRY: '询价'
};

// 模拟采购计划列表数据
export const mockProcurementPlanList = [
  {
    id: '1',
    planCode: 'CG-2024-001',
    planName: '办公用品采购计划',
    purchaseType: PurchaseType.GOODS,
    bidCategory: '公开招标',
    purchaseMethod: PurchaseMethod.PUBLIC_BIDDING,
    bidAmount: 50.00,
    fundSource: '自有资金',
    bidTime: '2024-03-15',
    planStartTime: '2024-04-01',
    planEndTime: '2024-06-30',
    organizationMethod: '自行组织',
    agency: '',
    annualPlan: '2024年度采购计划',
    applicant: '张三',
    projectName: '办公用品采购项目',
    projectType: '货物类',
    projectOwner: 'XX集团有限公司',
    subCompany: 'XX分公司',
    projectDescription: '采购办公桌椅、文具用品、电脑设备等日常办公用品，满足公司日常办公需求。',
    projectRemark: '按季度分批采购，确保质量符合标准。',
    status: PlanStatus.APPROVED,
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-01-20 14:20:00',
    createUser: '张三',
    updateUser: '李四',
    attachments: [
      {
        id: '1',
        name: '采购需求说明书.pdf',
        url: '/files/procurement-plan/1/需求说明书.pdf',
        size: '2.5MB',
        uploadTime: '2024-01-15 10:35:00'
      }
    ]
  },
  {
    id: '2',
    planCode: 'CG-2024-002',
    planName: '网络设备升级采购计划',
    purchaseType: PurchaseType.GOODS,
    bidCategory: '公开招标',
    purchaseMethod: PurchaseMethod.PUBLIC_BIDDING,
    bidAmount: 120.50,
    fundSource: '专项资金',
    bidTime: '2024-04-10',
    planStartTime: '2024-05-01',
    planEndTime: '2024-07-31',
    organizationMethod: '委托代理',
    agency: 'XX招标代理有限公司',
    annualPlan: '2024年度采购计划',
    applicant: '王五',
    projectName: '网络设备升级改造项目',
    projectType: '货物类',
    projectOwner: 'XX集团有限公司',
    subCompany: 'XX技术分公司',
    projectDescription: '升级现有网络设备，包括交换机、路由器、防火墙等，提升网络性能和安全性。',
    projectRemark: '需要与现有系统兼容，确保平滑过渡。',
    status: PlanStatus.SUBMITTED,
    createTime: '2024-02-01 09:15:00',
    updateTime: '2024-02-05 16:45:00',
    createUser: '王五',
    updateUser: '王五',
    attachments: []
  },
  {
    id: '3',
    planCode: 'CG-2024-003',
    planName: '物业管理服务采购计划',
    purchaseType: PurchaseType.SERVICE,
    bidCategory: '公开招标',
    purchaseMethod: PurchaseMethod.PUBLIC_BIDDING,
    bidAmount: 200.00,
    fundSource: '运营资金',
    bidTime: '2024-05-20',
    planStartTime: '2024-06-01',
    planEndTime: '2025-05-31',
    organizationMethod: '自行组织',
    agency: '',
    annualPlan: '2024年度采购计划',
    applicant: '赵六',
    projectName: '总部大楼物业管理服务项目',
    projectType: '服务类',
    projectOwner: 'XX集团有限公司',
    subCompany: 'XX集团有限公司',
    projectDescription: '为总部大楼提供全方位物业管理服务，包括保洁、保安、绿化、维修等。',
    projectRemark: '服务期限一年，可根据服务质量决定是否续约。',
    status: PlanStatus.DRAFT,
    createTime: '2024-02-10 14:20:00',
    updateTime: '2024-02-10 14:20:00',
    createUser: '赵六',
    updateUser: '赵六',
    attachments: []
  },
  {
    id: '4',
    planCode: 'CG-2024-004',
    planName: '车辆维修保养服务采购计划',
    purchaseType: PurchaseType.SERVICE,
    bidCategory: '邀请招标',
    purchaseMethod: PurchaseMethod.INVITED_BIDDING,
    bidAmount: 80.00,
    fundSource: '自有资金',
    bidTime: '2024-06-15',
    planStartTime: '2024-07-01',
    planEndTime: '2024-12-31',
    organizationMethod: '委托代理',
    agency: 'XX招标代理有限公司',
    annualPlan: '2024年度采购计划',
    applicant: '孙七',
    projectName: '公司车辆维修保养服务项目',
    projectType: '服务类',
    projectOwner: 'XX集团有限公司',
    subCompany: 'XX运输分公司',
    projectDescription: '为公司车队提供定期维修保养服务，确保车辆安全运行。',
    projectRemark: '要求服务商具有相应资质和经验。',
    status: PlanStatus.REJECTED,
    createTime: '2024-02-15 11:10:00',
    updateTime: '2024-02-20 09:30:00',
    createUser: '孙七',
    updateUser: '审批员',
    attachments: []
  },
  {
    id: '5',
    planCode: 'CG-2024-005',
    planName: '食堂设备采购计划',
    purchaseType: PurchaseType.GOODS,
    bidCategory: '竞争性谈判',
    purchaseMethod: PurchaseMethod.COMPETITIVE_NEGOTIATION,
    bidAmount: 35.80,
    fundSource: '自有资金',
    bidTime: '2024-07-10',
    planStartTime: '2024-08-01',
    planEndTime: '2024-09-30',
    organizationMethod: '自行组织',
    agency: '',
    annualPlan: '2024年度采购计划',
    applicant: '周八',
    projectName: '员工食堂设备更新项目',
    projectType: '货物类',
    projectOwner: 'XX集团有限公司',
    subCompany: 'XX集团有限公司',
    projectDescription: '更新员工食堂厨房设备，包括炉灶、冰箱、消毒柜等，改善就餐环境。',
    projectRemark: '设备需符合食品安全标准。',
    status: PlanStatus.WITHDRAWN,
    createTime: '2024-02-20 15:45:00',
    updateTime: '2024-02-25 10:15:00',
    createUser: '周八',
    updateUser: '周八',
    attachments: []
  }
];

// 模拟单个采购计划详情数据
export const mockProcurementPlanDetail = {
  id: '1',
  planCode: 'CG-2024-001',
  planName: '办公用品采购计划',
  purchaseType: PurchaseType.GOODS,
  bidCategory: '公开招标',
  purchaseMethod: PurchaseMethod.PUBLIC_BIDDING,
  bidAmount: 50.00,
  fundSource: '自有资金',
  bidTime: '2024-03-15',
  planStartTime: '2024-04-01',
  planEndTime: '2024-06-30',
  organizationMethod: '自行组织',
  agency: '',
  annualPlan: '2024年度采购计划',
  applicant: '张三',
  projectName: '办公用品采购项目',
  projectType: '货物类',
  projectOwner: 'XX集团有限公司',
  subCompany: 'XX分公司',
  projectDescription: '采购办公桌椅、文具用品、电脑设备等日常办公用品，满足公司日常办公需求。包括但不限于：\n1. 办公桌椅：100套\n2. 文具用品：各类笔、纸张、文件夹等\n3. 电脑设备：台式机20台，笔记本10台\n4. 打印设备：多功能一体机5台',
  projectRemark: '按季度分批采购，确保质量符合标准。所有设备需提供一年质保服务。',
  status: PlanStatus.APPROVED,
  createTime: '2024-01-15 10:30:00',
  updateTime: '2024-01-20 14:20:00',
  createUser: '张三',
  updateUser: '李四',
  attachments: [
    {
      id: '1',
      name: '采购需求说明书.pdf',
      url: '/files/procurement-plan/1/需求说明书.pdf',
      size: '2.5MB',
      uploadTime: '2024-01-15 10:35:00'
    },
    {
      id: '2',
      name: '预算明细表.xlsx',
      url: '/files/procurement-plan/1/预算明细表.xlsx',
      size: '1.2MB',
      uploadTime: '2024-01-15 11:00:00'
    }
  ],
  auditHistory: [
    {
      id: '1',
      auditor: '李四',
      auditTime: '2024-01-20 14:20:00',
      result: 'approved',
      opinion: '采购需求合理，预算充足，同意执行。'
    },
    {
      id: '2',
      auditor: '王五',
      auditTime: '2024-01-18 16:30:00',
      result: 'approved',
      opinion: '技术方案可行，建议按计划执行。'
    }
  ]
};

// 模拟分页数据
export const mockPaginationData = {
  page: 1,
  size: 10,
  total: 5
};

// 模拟API响应数据
export const mockApiResponse = {
  data: mockProcurementPlanList,
  total: mockPaginationData.total,
  page: mockPaginationData.page,
  size: mockPaginationData.size
};

// 导出所有Mock数据
export default {
  PlanStatus,
  PurchaseType,
  PurchaseMethod,
  mockProcurementPlanList,
  mockProcurementPlanDetail,
  mockPaginationData,
  mockApiResponse
};
