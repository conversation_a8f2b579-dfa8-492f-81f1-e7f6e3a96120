/**
 * 采购计划管理 - API层
 * 
 * 模板说明：
 * - 标准的API接口封装模板
 * - 基于 window.$http 的统一请求方法
 * - 包含完整的CRUD操作接口
 */

/**
 * 采购计划管理相关API接口
 */
export const procurementPlanApi = {
  /**
   * 获取采购计划列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页数量
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.status - 状态筛选
   * @returns {Promise} 返回采购计划列表数据
   */
  getList(params = {}) {
    return window.$http.post('/api/procurement-plans/list', {
      page: params.page || 1,
      size: params.size || 10,
      keyword: params.keyword || '',
      status: params.status
    });
  },

  /**
   * 获取待办采购计划列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回待办采购计划列表数据
   */
  getPendingList(params = {}) {
    return window.$http.post('/api/procurement-plans/pending', {
      page: params.page || 1,
      size: params.size || 10,
      keyword: params.keyword || '',
      status: params.status
    });
  },

  /**
   * 获取已办采购计划列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回已办采购计划列表数据
   */
  getCompletedList(params = {}) {
    return window.$http.post('/api/procurement-plans/completed', {
      page: params.page || 1,
      size: params.size || 10,
      keyword: params.keyword || '',
      status: params.status
    });
  },

  /**
   * 根据ID获取采购计划详情
   * @param {string|number} id - 采购计划ID
   * @returns {Promise} 返回采购计划详情数据
   */
  getById(id) {
    return window.$http.fetch(`/api/procurement-plans/${id}`);
  },

  /**
   * 创建新采购计划
   * @param {Object} data - 采购计划数据
   * @returns {Promise} 返回创建结果
   */
  create(data) {
    return window.$http.post('/api/procurement-plans/create', data);
  },

  /**
   * 更新采购计划信息
   * @param {string|number} id - 采购计划ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 返回更新结果
   */
  update(id, data) {
    return window.$http.post(`/api/procurement-plans/update/${id}`, data);
  },

  /**
   * 删除采购计划
   * @param {string|number} id - 采购计划ID
   * @returns {Promise} 返回删除结果
   */
  delete(id) {
    return window.$http.post(`/api/procurement-plans/delete/${id}`);
  },

  /**
   * 批量删除采购计划
   * @param {Array} ids - 采购计划ID数组
   * @returns {Promise} 返回批量删除结果
   */
  batchDelete(ids) {
    return window.$http.post('/api/procurement-plans/batch-delete', {
      ids: ids
    });
  },

  /**
   * 提交审批
   * @param {string|number} id - 采购计划ID
   * @returns {Promise} 返回提交结果
   */
  submit(id) {
    return window.$http.post(`/api/procurement-plans/${id}/submit`);
  },

  /**
   * 审批通过
   * @param {string|number} id - 采购计划ID
   * @param {Object} data - 审批数据
   * @returns {Promise} 返回审批结果
   */
  approve(id, data) {
    return window.$http.post(`/api/procurement-plans/${id}/approve`, data);
  },

  /**
   * 审批驳回
   * @param {string|number} id - 采购计划ID
   * @param {Object} data - 驳回数据
   * @returns {Promise} 返回驳回结果
   */
  reject(id, data) {
    return window.$http.post(`/api/procurement-plans/${id}/reject`, data);
  },

  /**
   * 撤回申请
   * @param {string|number} id - 采购计划ID
   * @returns {Promise} 返回撤回结果
   */
  withdraw(id) {
    return window.$http.post(`/api/procurement-plans/${id}/withdraw`);
  },

  /**
   * 上传附件
   * @param {string|number} id - 采购计划ID
   * @param {FormData} formData - 文件数据
   * @returns {Promise} 返回上传结果
   */
  uploadAttachment(id, formData) {
    return window.$http.post(`/api/procurement-plans/${id}/attachments`, formData);
  },

  /**
   * 删除附件
   * @param {string|number} id - 采购计划ID
   * @param {string|number} fileId - 文件ID
   * @returns {Promise} 返回删除结果
   */
  deleteAttachment(id, fileId) {
    return window.$http.post(`/api/procurement-plans/${id}/attachments/${fileId}/delete`);
  },

  /**
   * 导入采购计划数据
   * @param {FormData} formData - 导入文件数据
   * @returns {Promise} 返回导入结果
   */
  import(formData) {
    return window.$http.post('/api/procurement-plans/import', formData);
  },

  /**
   * 导出采购计划数据
   * @param {Object} params - 导出参数
   * @returns {Promise} 返回导出文件信息
   */
  export(params = {}) {
    return window.$http.post('/api/procurement-plans/export', {
      keyword: params.keyword || '',
      status: params.status
    });
  }
};

// 默认导出
export default procurementPlanApi;
