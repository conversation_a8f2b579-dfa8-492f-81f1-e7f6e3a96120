/**
 * 采购计划管理 - 路由配置
 *
 * 基于 router-standards.md 规范配置
 * - 路径使用 kebab-case 格式
 * - 组件路径使用 @/apps/ 前缀的绝对路径
 * - 所有组件使用动态导入（懒加载）
 * - 元数据包含 title 和 isMenu 配置
 */

export default {
  path: 'procurement-plan',
  name: 'ProcurementPlan',
  redirect: 'procurement-plan/list',
  meta: {
    title: '采购计划管理',
    isMenu: true
  },
  children: [
    {
      path: 'list',
      name: 'ProcurementPlanList',
      component: () => import('./views/list/index.vue'),
      meta: {
        title: '采购计划列表',
        isMenu: false
      }
    },
    {
      path: 'detail/:id',
      name: 'ProcurementPlanDetail',
      component: () => import('./views/detail/index.vue'),
      meta: {
        title: '采购计划详情',
        isMenu: false
      }
    },
    {
      path: 'create',
      name: 'ProcurementPlanCreate',
      component: () => import('./views/create/index.vue'),
      meta: {
        title: '新建采购计划',
        isMenu: false
      }
    }
  ]
};
