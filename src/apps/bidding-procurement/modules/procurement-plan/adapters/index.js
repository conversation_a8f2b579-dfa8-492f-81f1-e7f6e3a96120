/**
 * 采购计划管理 - Adapters层
 * 
 * 模板说明：
 * - 纯函数数据转换模板
 * - 负责API数据与前端数据结构的转换
 * - 不调用其他层，保持纯函数特性
 */

/**
 * 采购计划管理 数据转换适配器
 */
export const procurementPlanAdapters = {
  /**
   * 适配列表数据
   * 将API返回的列表数据转换为前端需要的格式
   * @param {Object} apiResponse - API响应数据
   * @returns {Object} 转换后的列表数据
   */
  adaptListData(apiResponse) {
    if (!apiResponse || !apiResponse.data) {
      return {
        list: [],
        total: 0,
        page: 1,
        size: 10
      };
    }

    const { data, total, page, size } = apiResponse;
    
    return {
      list: data.map(item => this.adaptItemData(item)),
      total: total || 0,
      page: page || 1,
      size: size || 10
    };
  },

  /**
   * 适配单个采购计划数据
   * 将API返回的单个采购计划数据转换为前端格式
   * @param {Object} apiItem - API返回的单个采购计划数据
   * @returns {Object} 转换后的采购计划数据
   */
  adaptItemData(apiItem) {
    if (!apiItem) return null;

    return {
      id: apiItem.id || '',
      planCode: apiItem.planCode || '',
      planName: apiItem.planName || '',
      purchaseType: apiItem.purchaseType || '',
      bidCategory: apiItem.bidCategory || '',
      purchaseMethod: apiItem.purchaseMethod || '',
      bidAmount: apiItem.bidAmount || 0,
      fundSource: apiItem.fundSource || '',
      bidTime: this.formatDate(apiItem.bidTime),
      planStartTime: this.formatDate(apiItem.planStartTime),
      planEndTime: this.formatDate(apiItem.planEndTime),
      organizationMethod: apiItem.organizationMethod || '',
      agency: apiItem.agency || '',
      annualPlan: apiItem.annualPlan || '',
      applicant: apiItem.applicant || '',
      projectName: apiItem.projectName || '',
      projectType: apiItem.projectType || '',
      projectOwner: apiItem.projectOwner || '',
      subCompany: apiItem.subCompany || '',
      projectDescription: apiItem.projectDescription || '',
      projectRemark: apiItem.projectRemark || '',
      status: apiItem.status || 'draft',
      statusText: this.getStatusText(apiItem.status),
      createTime: this.formatDateTime(apiItem.createTime),
      updateTime: this.formatDateTime(apiItem.updateTime),
      createUser: apiItem.createUser || '',
      updateUser: apiItem.updateUser || '',
      attachments: apiItem.attachments || []
    };
  },

  /**
   * 适配详情数据
   * 将API返回的详情数据转换为前端格式
   * @param {Object} apiResponse - API响应数据
   * @returns {Object} 转换后的详情数据
   */
  adaptDetailData(apiResponse) {
    if (!apiResponse) return null;
    
    return this.adaptItemData(apiResponse);
  },

  /**
   * 适配创建数据
   * 将前端表单数据转换为API需要的格式
   * @param {Object} formData - 前端表单数据
   * @returns {Object} 转换后的API数据
   */
  adaptCreateData(formData) {
    if (!formData) return {};

    return {
      planName: formData.planName || '',
      purchaseType: formData.purchaseType || '',
      bidCategory: formData.bidCategory || '',
      purchaseMethod: formData.purchaseMethod || '',
      bidAmount: formData.bidAmount || 0,
      fundSource: formData.fundSource || '',
      bidTime: formData.bidTime || '',
      planStartTime: formData.planStartTime || '',
      planEndTime: formData.planEndTime || '',
      organizationMethod: formData.organizationMethod || '',
      agency: formData.agency || '',
      annualPlan: formData.annualPlan || '',
      applicant: formData.applicant || '',
      projectName: formData.projectName || '',
      projectType: formData.projectType || '',
      projectOwner: formData.projectOwner || '',
      subCompany: formData.subCompany || '',
      projectDescription: formData.projectDescription || '',
      projectRemark: formData.projectRemark || ''
    };
  },

  /**
   * 适配更新数据
   * 将前端表单数据转换为API需要的格式
   * @param {Object} formData - 前端表单数据
   * @returns {Object} 转换后的API数据
   */
  adaptUpdateData(formData) {
    if (!formData) return {};

    return this.adaptCreateData(formData);
  },

  /**
   * 获取状态文本
   * @param {string} status - 状态值
   * @returns {string} 状态文本
   */
  getStatusText(status) {
    const statusMap = {
      draft: '草稿',
      submitted: '已提交',
      approved: '已审批',
      rejected: '已驳回',
      withdrawn: '已撤回'
    };
    return statusMap[status] || '未知';
  },

  /**
   * 获取状态标签类型
   * @param {string} status - 状态值
   * @returns {string} 标签类型
   */
  getStatusTagType(status) {
    const typeMap = {
      draft: 'info',
      submitted: 'warning',
      approved: 'success',
      rejected: 'danger',
      withdrawn: 'info'
    };
    return typeMap[status] || 'info';
  },

  /**
   * 格式化日期时间
   * @param {string|Date} dateTime - 日期时间
   * @returns {string} 格式化后的日期时间字符串
   */
  formatDateTime(dateTime) {
    if (!dateTime) return '';
    
    try {
      const date = new Date(dateTime);
      if (isNaN(date.getTime())) return '';
      
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (error) {
      console.warn('日期格式化失败:', error);
      return '';
    }
  },

  /**
   * 格式化日期
   * @param {string|Date} date - 日期
   * @returns {string} 格式化后的日期字符串
   */
  formatDate(date) {
    if (!date) return '';
    
    try {
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) return '';
      
      return dateObj.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      console.warn('日期格式化失败:', error);
      return '';
    }
  },

  /**
   * 格式化金额
   * @param {number} amount - 金额
   * @returns {string} 格式化后的金额字符串
   */
  formatAmount(amount) {
    if (!amount && amount !== 0) return '0.00';
    
    return Number(amount).toFixed(2);
  },

  /**
   * 适配搜索参数
   * 将前端搜索条件转换为API参数
   * @param {Object} searchParams - 前端搜索参数
   * @returns {Object} 转换后的API参数
   */
  adaptSearchParams(searchParams) {
    if (!searchParams) return {};

    return {
      keyword: searchParams.keyword || '',
      status: searchParams.status || null,
      purchaseType: searchParams.purchaseType || '',
      startDate: searchParams.startDate || '',
      endDate: searchParams.endDate || '',
      minAmount: searchParams.minAmount || null,
      maxAmount: searchParams.maxAmount || null
    };
  },

  /**
   * 适配审批数据
   * 将前端审批表单数据转换为API格式
   * @param {Object} auditData - 审批数据
   * @returns {Object} 转换后的API数据
   */
  adaptAuditData(auditData) {
    if (!auditData) return {};

    return {
      opinion: auditData.opinion || '',
      result: auditData.result || '',
      remark: auditData.remark || ''
    };
  }
};

// 默认导出
export default procurementPlanAdapters;
