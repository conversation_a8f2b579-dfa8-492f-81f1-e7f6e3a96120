/**
 * 采购计划管理 - Store层
 * 
 * 模板说明：
 * - 基于 Pinia 的状态管理模板
 * - 集成API层和Adapters层调用
 * - 包含完整的业务逻辑封装
 */

import { defineStore } from 'pinia';
import { ref, reactive } from 'vue';
import { procurementPlanApi } from './api/index.js';
import { procurementPlanAdapters } from './adapters/index.js';

/**
 * 采购计划管理 Store
 */
export const useProcurementPlanStore = defineStore('procurementPlan', () => {
  // 状态定义
  const procurementPlanList = ref([]);
  const currentProcurementPlan = ref(null);
  const loading = ref(false);
  const pagination = reactive({
    page: 1,
    size: 10,
    total: 0
  });

  // 搜索条件
  const searchParams = reactive({
    keyword: '',
    status: null,
    purchaseType: '',
    startDate: '',
    endDate: ''
  });

  /**
   * 获取采购计划列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回处理后的列表数据
   */
  const getProcurementPlanList = async (params = {}) => {
    try {
      loading.value = true;
      
      // 合并查询参数
      const queryParams = {
        ...searchParams,
        ...params,
        page: pagination.page,
        size: pagination.size
      };

      // 调用API
      const response = await procurementPlanApi.getList(queryParams);
      
      // 数据转换
      const adaptedData = procurementPlanAdapters.adaptListData(response);
      
      // 更新状态
      procurementPlanList.value = adaptedData.list;
      pagination.total = adaptedData.total;
      
      return adaptedData;
    } catch (error) {
      console.error('获取采购计划列表失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 获取待办采购计划列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回处理后的列表数据
   */
  const getPendingProcurementPlanList = async (params = {}) => {
    try {
      loading.value = true;
      
      const queryParams = {
        ...searchParams,
        ...params,
        page: pagination.page,
        size: pagination.size
      };

      const response = await procurementPlanApi.getPendingList(queryParams);
      const adaptedData = procurementPlanAdapters.adaptListData(response);
      
      procurementPlanList.value = adaptedData.list;
      pagination.total = adaptedData.total;
      
      return adaptedData;
    } catch (error) {
      console.error('获取待办采购计划列表失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 获取已办采购计划列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回处理后的列表数据
   */
  const getCompletedProcurementPlanList = async (params = {}) => {
    try {
      loading.value = true;
      
      const queryParams = {
        ...searchParams,
        ...params,
        page: pagination.page,
        size: pagination.size
      };

      const response = await procurementPlanApi.getCompletedList(queryParams);
      const adaptedData = procurementPlanAdapters.adaptListData(response);
      
      procurementPlanList.value = adaptedData.list;
      pagination.total = adaptedData.total;
      
      return adaptedData;
    } catch (error) {
      console.error('获取已办采购计划列表失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 根据ID获取采购计划详情
   * @param {string|number} id - 采购计划ID
   * @returns {Promise} 返回处理后的详情数据
   */
  const getProcurementPlan = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await procurementPlanApi.getById(id);
      
      // 数据转换
      const adaptedData = procurementPlanAdapters.adaptDetailData(response);
      
      // 更新状态
      currentProcurementPlan.value = adaptedData;
      
      return adaptedData;
    } catch (error) {
      console.error('获取采购计划详情失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 创建新采购计划
   * @param {Object} data - 采购计划数据
   * @returns {Promise} 返回创建结果
   */
  const createProcurementPlan = async (data) => {
    try {
      loading.value = true;
      
      // 数据转换
      const apiData = procurementPlanAdapters.adaptCreateData(data);
      
      // 调用API
      const response = await procurementPlanApi.create(apiData);
      
      // 数据转换
      const adaptedData = procurementPlanAdapters.adaptDetailData(response);
      
      return adaptedData;
    } catch (error) {
      console.error('创建采购计划失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新采购计划信息
   * @param {string|number} id - 采购计划ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 返回更新结果
   */
  const updateProcurementPlan = async (id, data) => {
    try {
      loading.value = true;
      
      // 数据转换
      const apiData = procurementPlanAdapters.adaptUpdateData(data);
      
      // 调用API
      const response = await procurementPlanApi.update(id, apiData);
      
      // 数据转换
      const adaptedData = procurementPlanAdapters.adaptDetailData(response);
      
      // 更新当前采购计划状态
      if (currentProcurementPlan.value && currentProcurementPlan.value.id === id) {
        currentProcurementPlan.value = adaptedData;
      }
      
      return adaptedData;
    } catch (error) {
      console.error('更新采购计划失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 删除采购计划
   * @param {string|number} id - 采购计划ID
   * @returns {Promise} 返回删除结果
   */
  const deleteProcurementPlan = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await procurementPlanApi.delete(id);
      
      // 从列表中移除
      const index = procurementPlanList.value.findIndex(item => item.id === id);
      if (index > -1) {
        procurementPlanList.value.splice(index, 1);
        pagination.total--;
      }
      
      return response;
    } catch (error) {
      console.error('删除采购计划失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 提交审批
   * @param {string|number} id - 采购计划ID
   * @returns {Promise} 返回提交结果
   */
  const submitProcurementPlan = async (id) => {
    try {
      loading.value = true;
      const response = await procurementPlanApi.submit(id);
      return response;
    } catch (error) {
      console.error('提交采购计划失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 审批通过
   * @param {string|number} id - 采购计划ID
   * @param {Object} data - 审批数据
   * @returns {Promise} 返回审批结果
   */
  const approveProcurementPlan = async (id, data) => {
    try {
      loading.value = true;
      const apiData = procurementPlanAdapters.adaptAuditData(data);
      const response = await procurementPlanApi.approve(id, apiData);
      return response;
    } catch (error) {
      console.error('审批采购计划失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 审批驳回
   * @param {string|number} id - 采购计划ID
   * @param {Object} data - 驳回数据
   * @returns {Promise} 返回驳回结果
   */
  const rejectProcurementPlan = async (id, data) => {
    try {
      loading.value = true;
      const apiData = procurementPlanAdapters.adaptAuditData(data);
      const response = await procurementPlanApi.reject(id, apiData);
      return response;
    } catch (error) {
      console.error('驳回采购计划失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 撤回申请
   * @param {string|number} id - 采购计划ID
   * @returns {Promise} 返回撤回结果
   */
  const withdrawProcurementPlan = async (id) => {
    try {
      loading.value = true;
      const response = await procurementPlanApi.withdraw(id);
      return response;
    } catch (error) {
      console.error('撤回采购计划失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新搜索条件
   * @param {Object} params - 搜索参数
   */
  const updateSearchParams = (params) => {
    Object.assign(searchParams, params);
    pagination.page = 1; // 重置页码
  };

  /**
   * 更新分页信息
   * @param {Object} paginationData - 分页数据
   */
  const updatePagination = (paginationData) => {
    Object.assign(pagination, paginationData);
  };

  /**
   * 重置状态
   */
  const resetState = () => {
    procurementPlanList.value = [];
    currentProcurementPlan.value = null;
    loading.value = false;
    pagination.page = 1;
    pagination.total = 0;
    searchParams.keyword = '';
    searchParams.status = null;
    searchParams.purchaseType = '';
    searchParams.startDate = '';
    searchParams.endDate = '';
  };

  // 返回状态和方法
  return {
    // 状态
    procurementPlanList,
    currentProcurementPlan,
    loading,
    pagination,
    searchParams,
    
    // 方法
    getProcurementPlanList,
    getPendingProcurementPlanList,
    getCompletedProcurementPlanList,
    getProcurementPlan,
    createProcurementPlan,
    updateProcurementPlan,
    deleteProcurementPlan,
    submitProcurementPlan,
    approveProcurementPlan,
    rejectProcurementPlan,
    withdrawProcurementPlan,
    updateSearchParams,
    updatePagination,
    resetState
  };
});
