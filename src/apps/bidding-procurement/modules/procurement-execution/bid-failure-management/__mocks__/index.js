/**
 * 流标或中止管理 - Mock数据
 *
 * 用于开发和测试阶段的模拟数据
 */

export const mockBidFailureAnnouncementList = [
  {
    id: '1',
    title: '某某基础设施建设项目流标公告',
    projectId: 'project_001',
    projectName: '某某基础设施建设项目',
    projectCode: 'PROJ-2024-001',
    sectionId: 'section_001',
    sectionName: '第一标段',
    procurementMethod: '公开招标',
    budgetAmount: 15000000,
    projectOwner: '某某市政府',
    type: 'bid_failure',
    typeText: '流标',
    isPublic: true,
    isPublicText: '是',
    content: '<p>经评标委员会评审，本次招标因有效投标人不足三家，根据《招标投标法》相关规定，宣布本次招标流标。</p><p>现将流标情况公告如下：</p><p>1. 招标项目：某某基础设施建设项目第一标段</p><p>2. 流标原因：有效投标人不足三家</p><p>3. 后续安排：将重新组织招标</p>',
    status: 4,
    statusText: '已发布',
    auditStatus: 'approved',
    auditStatusText: '审核通过',
    publishStatus: 'published',
    publishStatusText: '已发布',
    attachments: [
      {
        id: 'file_001',
        name: '流标说明文件.pdf',
        url: '/files/bid_failure_001.pdf',
        size: 1024000,
        sizeText: '1.00 MB',
        type: 'pdf',
        uploadTime: '2024-01-25 10:00:00'
      }
    ],
    auditBasis: [
      {
        id: 'basis_001',
        name: '评标报告.pdf',
        url: '/files/evaluation_report_001.pdf',
        size: 2048000,
        sizeText: '2.00 MB',
        type: 'pdf',
        uploadTime: '2024-01-24 16:30:00'
      }
    ],
    auditComment: '流标公告内容完整，符合相关规定，同意发布。',
    publishTime: '2024-01-25 10:00:00',
    createTime: '2024-01-24 15:30:00',
    updateTime: '2024-01-25 10:00:00',
    createUser: '张三',
    updateUser: '李四'
  },
  {
    id: '2',
    title: '办公设备采购项目中止公告',
    projectId: 'project_002',
    projectName: '办公设备采购项目',
    projectCode: 'PROJ-2024-002',
    sectionId: 'section_002',
    sectionName: '第一包',
    procurementMethod: '竞争性谈判',
    budgetAmount: 1000000,
    projectOwner: '某某事业单位',
    type: 'termination',
    typeText: '中止',
    isPublic: false,
    isPublicText: '否',
    content: '<p>因采购需求发生重大变化，经采购人申请，现决定中止本次采购活动。</p><p>中止情况说明：</p><p>1. 采购项目：办公设备采购项目第一包</p><p>2. 中止原因：采购需求发生重大变化</p><p>3. 后续安排：待需求明确后重新组织采购</p>',
    status: 2,
    statusText: '待审核',
    auditStatus: 'pending',
    auditStatusText: '待审核',
    publishStatus: 'unpublished',
    publishStatusText: '未发布',
    attachments: [
      {
        id: 'file_002',
        name: '中止申请书.pdf',
        url: '/files/termination_002.pdf',
        size: 512000,
        sizeText: '500.00 KB',
        type: 'pdf',
        uploadTime: '2024-01-20 14:00:00'
      }
    ],
    auditBasis: [],
    auditComment: '',
    publishTime: '',
    createTime: '2024-01-20 13:30:00',
    updateTime: '2024-01-20 14:00:00',
    createUser: '赵六',
    updateUser: '赵六'
  },
  {
    id: '3',
    title: '道路维修工程项目流标公告',
    projectId: 'project_003',
    projectName: '道路维修工程项目',
    projectCode: 'PROJ-2024-003',
    sectionId: 'section_003',
    sectionName: '第一标段',
    procurementMethod: '公开招标',
    budgetAmount: 5000000,
    projectOwner: '某某区政府',
    type: 'bid_failure',
    typeText: '流标',
    isPublic: true,
    isPublicText: '是',
    content: '<p>经评标委员会评审，本次招标因所有投标文件均不符合招标文件要求，根据相关规定，宣布本次招标流标。</p><p>流标详情：</p><p>1. 招标项目：道路维修工程项目第一标段</p><p>2. 流标原因：所有投标文件均不符合招标文件要求</p><p>3. 后续安排：修改招标文件后重新招标</p>',
    status: 5,
    statusText: '已撤销',
    auditStatus: 'approved',
    auditStatusText: '审核通过',
    publishStatus: 'revoked',
    publishStatusText: '已撤销',
    attachments: [
      {
        id: 'file_003',
        name: '流标说明.pdf',
        url: '/files/bid_failure_003.pdf',
        size: 768000,
        sizeText: '750.00 KB',
        type: 'pdf',
        uploadTime: '2024-01-18 09:00:00'
      }
    ],
    auditBasis: [
      {
        id: 'basis_002',
        name: '评标委员会意见.pdf',
        url: '/files/committee_opinion_003.pdf',
        size: 1536000,
        sizeText: '1.50 MB',
        type: 'pdf',
        uploadTime: '2024-01-17 17:00:00'
      }
    ],
    auditComment: '流标公告内容完整，符合相关规定，同意发布。',
    publishTime: '2024-01-18 09:00:00',
    createTime: '2024-01-17 16:30:00',
    updateTime: '2024-01-19 11:20:00',
    createUser: '孙七',
    updateUser: '孙七'
  }
];

export const mockBidFailureAnnouncementDetail = {
  id: '1',
  title: '某某基础设施建设项目流标公告',
  projectId: 'project_001',
  projectName: '某某基础设施建设项目',
  projectCode: 'PROJ-2024-001',
  sectionId: 'section_001',
  sectionName: '第一标段',
  procurementMethod: '公开招标',
  budgetAmount: 15000000,
  projectOwner: '某某市政府',
  type: 'bid_failure',
  typeText: '流标',
  isPublic: true,
  isPublicText: '是',
  content: '<p>经评标委员会评审，本次招标因有效投标人不足三家，根据《招标投标法》相关规定，宣布本次招标流标。</p><p>现将流标情况公告如下：</p><p>1. 招标项目：某某基础设施建设项目第一标段</p><p>2. 招标编号：PROJ-2024-001-01</p><p>3. 招标人：某某市政府</p><p>4. 招标代理机构：某某招标代理有限公司</p><p>5. 流标原因：有效投标人不足三家</p><p>6. 投标截止时间：2024年1月20日10:00</p><p>7. 开标时间：2024年1月20日10:30</p><p>8. 评标时间：2024年1月20日-2024年1月22日</p><p>9. 后续安排：将重新组织招标，具体时间另行通知</p><p>特此公告。</p>',
  status: 4,
  statusText: '已发布',
  auditStatus: 'approved',
  auditStatusText: '审核通过',
  publishStatus: 'published',
  publishStatusText: '已发布',
  attachments: [
    {
      id: 'file_001',
      name: '流标说明文件.pdf',
      url: '/files/bid_failure_001.pdf',
      size: 1024000,
      sizeText: '1.00 MB',
      type: 'pdf',
      uploadTime: '2024-01-25 10:00:00'
    }
  ],
  auditBasis: [
    {
      id: 'basis_001',
      name: '评标报告.pdf',
      url: '/files/evaluation_report_001.pdf',
      size: 2048000,
      sizeText: '2.00 MB',
      type: 'pdf',
      uploadTime: '2024-01-24 16:30:00'
    }
  ],
  auditComment: '流标公告内容完整，符合相关规定，同意发布。',
  publishTime: '2024-01-25 10:00:00',
  createTime: '2024-01-24 15:30:00',
  updateTime: '2024-01-25 10:00:00',
  createUser: '张三',
  updateUser: '李四'
};

// 项目标段选项数据
export const mockProjectSectionOptions = [
  {
    id: 'section_001',
    projectName: '某某基础设施建设项目',
    projectCode: 'PROJ-2024-001',
    sectionName: '第一标段',
    procurementMethod: '公开招标',
    budgetAmount: 15000000,
    projectOwner: '某某市政府'
  },
  {
    id: 'section_002',
    projectName: '办公设备采购项目',
    projectCode: 'PROJ-2024-002',
    sectionName: '第一包',
    procurementMethod: '竞争性谈判',
    budgetAmount: 1000000,
    projectOwner: '某某事业单位'
  },
  {
    id: 'section_003',
    projectName: '道路维修工程项目',
    projectCode: 'PROJ-2024-003',
    sectionName: '第一标段',
    procurementMethod: '公开招标',
    budgetAmount: 5000000,
    projectOwner: '某某区政府'
  }
];

// 流标/中止类型选项
export const mockBidFailureTypeOptions = [
  { value: 'bid_failure', label: '流标' },
  { value: 'termination', label: '中止' }
];

export const enableMock = true;

// 默认导出
export default {
  enableMock,
  mockBidFailureAnnouncementList,
  mockBidFailureAnnouncementDetail,
  mockProjectSectionOptions,
  mockBidFailureTypeOptions
};
