<!-- 流标或中止管理 - 列表页面 -->
<!--
  流标或中止管理列表页面
  
  功能说明：
  - 基于 FuniListPageV2 组件的标准列表页面
  - 支持多页签配置和标准CRUD操作
  - 集成权限控制、搜索配置、数据脱敏等企业级功能
  - 支持动态按钮生成和完善的错误处理
  - 路由跳转参数与 add-page.vue 和 detail-page.vue 保持一致

  路由跳转规则：
  - 新增页面：type="add", bizName="新增"
  - 编辑页面：type="edit", bizName="编辑"
  - 详情页面：type="info", bizName="详情"
  - 审核页面：type="audit", bizName="审核"
-->
<template>
  <funi-list-page-v2 ref="listPageRef" :cardTab="cardTabConfig" @headBtnClick="handleHeadBtnClick" />
</template>

<script setup lang="jsx">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElNotification, ElMessageBox } from 'element-plus';
import { useBidFailureAnnouncementStore } from '../../store.js';

// ==================== 基础变量配置 ====================
const listPageRef = ref();
const router = useRouter();
const bidFailureStore = useBidFailureAnnouncementStore();

// ==================== 权限配置 ====================
/**
 * 权限配置对象
 * 根据实际业务需求配置对应的权限码
 */
const auths = {
  export: 'bid_failure_management_export', // 导出权限
  add: 'bid_failure_management_add', // 新增权限
  delete: 'bid_failure_management_delete', // 删除权限
  audit: 'bid_failure_management_audit', // 审核权限
  edit: 'bid_failure_management_edit', // 编辑权限
  submit: 'bid_failure_management_submit', // 提交权限
  publish: 'bid_failure_management_publish', // 发布权限
  revoke: 'bid_failure_management_revoke', // 撤销权限
  detail: 'bid_failure_management_detail' // 详情权限
};

// ==================== 数据状态管理 ====================
// 列表查询参数存储
const listPageParams = ref({});

// ==================== 核心业务函数 ====================

/**
 * 跳转到详情页面
 * @param {Object} row - 当前行数据
 */
const goDetail = row => {
  router.push({
    path: `/bidding-procurement/procurement-execution/bid-failure-management/detail/${row.id}`,
    query: {
      type: 'info', // 页面类型：详情
      bizName: '详情', // 业务名称
      title: row.title || '流标或中止公告详情', // 页面标题
      tab: ['流标或中止公告', row.title || '', '详情'].join('-') // 标签页标题
    }
  });
};

/**
 * 删除记录
 * @param {string|number} id - 记录ID
 */
const deleteItem = async id => {
  try {
    await bidFailureStore.deleteBidFailureAnnouncement(id);
    ElNotification({
      title: '提示',
      message: '删除成功',
      type: 'success',
      duration: 2000
    });
    listPageRef.value.reload({ resetPage: false });
  } catch (error) {
    console.error('删除失败:', error);
    ElNotification({
      title: '错误',
      message: '删除失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

/**
 * 跳转到编辑页面
 * @param {Object} row - 当前行数据
 */
const editItem = row => {
  router.push({
    path: `/bidding-procurement/procurement-execution/bid-failure-management/edit/${row.id}`,
    query: {
      type: 'edit', // 页面类型：编辑
      bizName: '编辑', // 业务名称
      title: row.title || '编辑流标或中止公告', // 页面标题
      tab: ['流标或中止公告', row.title || '', '编辑'].join('-') // 标签页标题
    }
  });
};

/**
 * 跳转到审核页面
 * @param {Object} row - 当前行数据
 */
const auditItem = row => {
  router.push({
    path: `/bidding-procurement/procurement-execution/bid-failure-management/audit/${row.id}`,
    query: {
      type: 'audit', // 页面类型：审核
      bizName: '审核', // 业务名称
      title: row.title || '审核流标或中止公告', // 页面标题
      tab: ['流标或中止公告', row.title || '', '审核'].join('-') // 标签页标题
    }
  });
};

/**
 * 提交审核
 * @param {Object} row - 当前行数据
 */
const submitItem = async row => {
  try {
    await bidFailureStore.submitBidFailureAnnouncement(row.id);
    ElNotification({
      title: '提示',
      message: '提交成功',
      type: 'success',
      duration: 2000
    });
    listPageRef.value.reload({ resetPage: false });
  } catch (error) {
    console.error('提交失败:', error);
    ElNotification({
      title: '错误',
      message: '提交失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

/**
 * 发布公告
 * @param {Object} row - 当前行数据
 */
const publishItem = async row => {
  try {
    await bidFailureStore.publishBidFailureAnnouncement(row.id);
    ElNotification({
      title: '提示',
      message: '发布成功',
      type: 'success',
      duration: 2000
    });
    listPageRef.value.reload({ resetPage: false });
  } catch (error) {
    console.error('发布失败:', error);
    ElNotification({
      title: '错误',
      message: '发布失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

/**
 * 撤销公告
 * @param {Object} row - 当前行数据
 */
const revokeItem = async row => {
  try {
    await ElMessageBox.confirm('确认撤销该公告吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    await bidFailureStore.revokeBidFailureAnnouncement(row.id);
    ElNotification({
      title: '提示',
      message: '撤销成功',
      type: 'success',
      duration: 2000
    });
    listPageRef.value.reload({ resetPage: false });
  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤销失败:', error);
      ElNotification({
        title: '错误',
        message: '撤销失败，请重试',
        type: 'error',
        duration: 2000
      });
    }
  }
};

// ==================== 动态按钮生成器 ====================

/**
 * 动态按钮生成器
 * 根据数据状态动态生成操作按钮
 */
const generateActionButtons = row => {
  const buttons = [];

  // 根据状态显示不同的操作按钮
  switch (row.status) {
    case 1: // 草稿
      buttons.push(
        <el-link type="primary" v-auth={auths.edit} onClick={() => editItem(row)} style={{ marginRight: '10px' }}>
          编辑
        </el-link>,
        <el-link type="primary" v-auth={auths.submit} onClick={() => submitItem(row)} style={{ marginRight: '10px' }}>
          提交
        </el-link>,
        <el-popconfirm title="是否删除?" onConfirm={() => deleteItem(row.id)}>
          {{
            reference: () => (
              <el-link type="danger" v-auth={auths.delete}>
                删除
              </el-link>
            )
          }}
        </el-popconfirm>
      );
      break;
    case 2: // 待审核
      buttons.push(
        <el-link type="primary" v-auth={auths.audit} onClick={() => auditItem(row)} style={{ marginRight: '10px' }}>
          审核
        </el-link>
      );
      break;
    case 3: // 审核通过
      buttons.push(
        <el-link type="primary" v-auth={auths.publish} onClick={() => publishItem(row)} style={{ marginRight: '10px' }}>
          发布
        </el-link>
      );
      break;
    case 4: // 已发布
      buttons.push(
        <el-link type="warning" v-auth={auths.revoke} onClick={() => revokeItem(row)} style={{ marginRight: '10px' }}>
          撤销
        </el-link>
      );
      break;
  }

  // 详情按钮始终显示
  buttons.push(
    <el-link type="primary" v-auth={auths.detail} onClick={() => goDetail(row)}>
      详情
    </el-link>
  );

  return buttons.length > 0 ? buttons : <span>--</span>;
};

/**
 * 表格列配置
 */
const columns = reactive([
  {
    label: '公告标题',
    prop: 'title',
    fixed: 'left',
    render: ({ row }) => {
      return (
        <el-link type="primary" onClick={() => goDetail(row)}>
          {row.title || '--'}
        </el-link>
      );
    }
  },
  {
    label: '项目编号',
    prop: 'projectCode'
  },
  {
    label: '采购方式',
    prop: 'procurementMethod'
  },
  {
    label: '流标/中止类型',
    prop: 'typeText',
    render: ({ row }) => <el-tag type={row.type === 'bid_failure' ? 'warning' : 'info'}>{row.typeText || '--'}</el-tag>
  },
  {
    label: '审核状态',
    prop: 'statusText',
    render: ({ row }) => {
      const statusColorMap = {
        1: 'info', // 草稿
        2: 'warning', // 待审核
        3: 'success', // 审核通过
        4: 'success', // 已发布
        5: 'danger' // 已撤销
      };
      return <el-tag type={statusColorMap[row.status] || 'info'}>{row.statusText || '--'}</el-tag>;
    }
  },
  {
    label: '发布状态',
    prop: 'publishStatusText'
  },
  {
    label: '创建人',
    prop: 'createUser'
  },
  {
    label: '创建时间',
    prop: 'createTime'
  },
  {
    label: '操作',
    prop: 'actions',
    width: 200,
    fixed: 'right',
    render: ({ row }) => {
      return <div>{generateActionButtons(row)}</div>;
    }
  }
]);

// 数据加载函数
const loadData = async (pageParams, searchParams) => {
  listPageParams.value = searchParams;

  // 更新store中的搜索参数和分页信息
  bidFailureStore.updateSearchParams(searchParams);
  bidFailureStore.updatePagination(pageParams);

  // 调用store方法获取数据
  const result = await bidFailureStore.getBidFailureAnnouncementList();

  return {
    list: result.list,
    total: result.total
  };
};

// 页签配置
const cardTabConfig = computed(() => {
  return [
    {
      label: '全部',
      key: 'all',
      curdOption: {
        columns,
        lodaData: loadData,
        btns: [{ key: 'add', label: '新增', auth: auths.add, type: 'primary' }],
        fixedButtons: true,
        reloadOnActive: true
      }
    },
    {
      label: '待审核',
      key: 'pending',
      curdOption: {
        columns,
        lodaData: (pageParams, searchParams) => loadData(pageParams, { ...searchParams, status: 2 }),
        btns: [{ key: 'add', label: '新增', auth: auths.add, type: 'primary' }],
        fixedButtons: true,
        reloadOnActive: true
      }
    },
    {
      label: '审核中',
      key: 'auditing',
      curdOption: {
        columns,
        lodaData: (pageParams, searchParams) => loadData(pageParams, { ...searchParams, status: 3 }),
        btns: [{ key: 'add', label: '新增', auth: auths.add, type: 'primary' }],
        fixedButtons: true,
        reloadOnActive: true
      }
    },
    {
      label: '已发布',
      key: 'published',
      curdOption: {
        columns,
        lodaData: (pageParams, searchParams) => loadData(pageParams, { ...searchParams, status: 4 }),
        btns: [{ key: 'add', label: '新增', auth: auths.add, type: 'primary' }],
        fixedButtons: true,
        reloadOnActive: true
      }
    }
  ];
});

// 新增功能
const addItem = () => {
  router.push({
    path: '/bidding-procurement/procurement-execution/bid-failure-management/create',
    query: {
      type: 'add', // 页面类型：新增
      bizName: '新增', // 业务名称
      title: '新增流标或中止公告', // 页面标题
      tab: '流标或中止公告-新增' // 标签页标题
    }
  });
};

// 头部按钮点击事件处理
const handleHeadBtnClick = key => {
  switch (key) {
    case 'add':
      addItem();
      break;
    default:
      break;
  }
};

// ==================== 生命周期钩子 ====================

/**
 * 组件挂载完成
 */
onMounted(() => {
  console.log('流标或中止管理列表页面已挂载');
});
</script>
