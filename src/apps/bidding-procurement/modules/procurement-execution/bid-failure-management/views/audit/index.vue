<!-- 流标或中止管理 - 审核页面 -->
<!--
  流标或中止管理审核页面
  
  功能说明：
  - 基于 FuniDetail 组件的标准审核页面
  - 支持审核操作和审核意见填写
  - 集成审核按钮和工作流功能
-->
<template>
  <div>
    <!-- 业务详情组件 - 核心组件，集成了步骤流程、审核按钮、工作流等功能 -->
    <funi-detail
      :steps="steps"
      :bizName="bizName"
      :showWorkflow="false"
      :auditButtons="auditButtons"
      :detailHeadOption="detailHeadOption || {}"
      @auditEvent="auditEvent"
    />
    
    <!-- 审核抽屉 -->
    <funi-bus-audit-drawer
      ref="auditDrawerRef"
      @confirm="handleAuditConfirm"
    />
  </div>
</template>

<script setup lang="jsx">
// Vue 3 Composition API 相关导入
import { ref, computed, onMounted } from 'vue';
// 路由相关导入
import { useRoute, useRouter } from 'vue-router';
// Element Plus 组件导入
import { ElNotification, ElMessageBox } from 'element-plus';
// 多标签页管理 Hook 导入
import { useMultiTab } from '@/utils/hooks/useMultiTab';
// Store导入
import { useBidFailureAnnouncementStore } from '../../store.js';

// 自定义tab内容组件导入
import BidFailureAnnouncementInfo from '../detail/components/BidFailureAnnouncementInfo.vue';
import ProjectInfo from '../detail/components/ProjectInfo.vue';
import OperationRecord from '../detail/components/OperationRecord.vue';

// 获取当前路由信息
const route = useRoute();
const router = useRouter();
// 多标签页管理实例
const multiTab = useMultiTab();
// Store实例
const bidFailureStore = useBidFailureAnnouncementStore();

// 路由查询参数的响应式引用
const routeQuery = ref(route.query);

// 流标或中止公告记录ID
const id = ref(route.params.id);
// 流标或中止公告业务名称（从路由参数获取）
const bizName = computed(() => routeQuery.value.bizName);
// 流标或中止公告业务类型（从路由参数获取）
const type = computed(() => routeQuery.value.type);

// 流标或中止公告业务信息数据
const infoData = ref();

// 审核抽屉引用
const auditDrawerRef = ref();

/**
 * 流标或中止公告详情页头部配置选项
 * 配置页面标题、状态、按钮等信息
 */
const detailHeadOption = computed(() => {
  return {
    statusName: '状态',                           // 状态名称
    title: routeQuery.value.title,               // 页面标题
    no: infoData.value?.id,              // 流标或中止公告业务编号
    status: infoData.value?.statusText,        // 当前流标或中止公告业务节点状态
    btns: []
  };
});

/**
 * 审核按钮配置
 */
const auditButtons = computed(() => {
  if (infoData.value?.status === 2) { // 待审核状态
    return [
      {
        name: '审核通过',
        type: 'primary',
        action: () => handleAudit('approved')
      },
      {
        name: '审核驳回',
        type: 'danger',
        action: () => handleAudit('rejected')
      }
    ];
  }
  return [];
});

/**
 * 流标或中止公告步骤配置
 * 定义流标或中止公告详情页面的各个步骤和对应的组件
 */
const steps = computed(() => {
  return [
    // 第一步：流标或中止公告基本信息
    {
      title: '公告信息',                           // 步骤标题
      preservable: false,                          // 是否可保存草稿
      type: BidFailureAnnouncementInfo,                             // 使用的流标或中止公告组件类型
      props: {
        id: id.value,                             // 流标或中止公告记录ID
        type: type.value,                      // 流标或中止公告业务类型
        data: infoData.value,             // 流标或中止公告数据
        isEdit: false  // 审核页不可编辑
      }
    },
    // 第二步：项目信息
    {
      title: '项目信息',                        // 步骤标题
      preservable: false,                          // 是否可保存草稿
      type: ProjectInfo,                           // 项目信息组件
      props: { 
        data: infoData.value     // 传递流标或中止公告数据
      }
    },
    // 第三步：操作记录
    {
      title: '操作记录',                            // 步骤标题
      preservable: false,                          // 是否可保存草稿
      type: OperationRecord,                          // 操作记录组件
      props: { 
        id: id.value     // 传递流标或中止公告ID
      }
    }
  ];
});

/**
 * 处理审核操作
 */
const handleAudit = (auditResult) => {
  // 打开审核抽屉
  auditDrawerRef.value.open({
    title: auditResult === 'approved' ? '审核通过' : '审核驳回',
    auditResult: auditResult
  });
};

/**
 * 处理审核确认
 */
const handleAuditConfirm = async (auditData) => {
  try {
    await bidFailureStore.auditBidFailureAnnouncement(id.value, {
      auditResult: auditData.auditResult,
      auditComment: auditData.auditComment
    });
    
    ElNotification({
      title: '提示',
      message: '审核完成',
      type: 'success',
      duration: 2000
    });
    
    // 关闭当前页面
    multiTab.closeCurrentPage();
    
  } catch (error) {
    console.error('审核失败:', error);
    ElNotification({
      title: '错误',
      message: '审核失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

/**
 * 流标或中止公告审核事件处理函数
 * 处理审核完成后的操作，关闭当前页面
 */
const auditEvent = () => {
  multiTab.closeCurrentPage();
};

/**
 * 加载流标或中止公告详情数据
 */
const loadDetailData = async () => {
  try {
    const data = await bidFailureStore.getBidFailureAnnouncement(id.value);
    infoData.value = data;
  } catch (error) {
    console.error('加载详情失败:', error);
    ElNotification({
      title: '错误',
      message: '加载数据失败',
      type: 'error',
      duration: 2000
    });
  }
};

// ==================== 生命周期钩子 ====================

/**
 * 组件挂载完成
 */
onMounted(() => {
  console.log('流标或中止管理审核页面已挂载');
  loadDetailData();
});
</script>
