<!-- 操作记录组件 -->
<template>
  <div class="operation-record">
    <el-timeline>
      <el-timeline-item
        v-for="(record, index) in operationRecords"
        :key="index"
        :timestamp="record.operationTime"
        placement="top"
        :type="getTimelineType(record.operationType)"
      >
        <el-card>
          <h4>{{ record.operationName }}</h4>
          <p>操作人：{{ record.operationUser }}</p>
          <p v-if="record.operationComment">操作说明：{{ record.operationComment }}</p>
        </el-card>
      </el-timeline-item>
    </el-timeline>
    
    <div v-if="!operationRecords.length" class="no-data">
      <el-empty description="暂无操作记录" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps } from 'vue';

// 定义props
const props = defineProps({
  id: {
    type: [String, Number],
    required: true
  }
});

// 操作记录数据
const operationRecords = ref([]);

/**
 * 获取时间线类型
 */
const getTimelineType = (operationType) => {
  const typeMap = {
    'create': 'primary',
    'submit': 'warning',
    'audit': 'success',
    'publish': 'success',
    'revoke': 'danger',
    'edit': 'info'
  };
  return typeMap[operationType] || 'primary';
};

/**
 * 加载操作记录
 */
const loadOperationRecords = async () => {
  try {
    // 模拟操作记录数据
    operationRecords.value = [
      {
        operationType: 'create',
        operationName: '创建公告',
        operationUser: '张三',
        operationTime: '2024-01-24 15:30:00',
        operationComment: '创建流标或中止公告'
      },
      {
        operationType: 'submit',
        operationName: '提交审核',
        operationUser: '张三',
        operationTime: '2024-01-24 16:00:00',
        operationComment: '提交公告审核'
      },
      {
        operationType: 'audit',
        operationName: '审核通过',
        operationUser: '李四',
        operationTime: '2024-01-24 18:00:00',
        operationComment: '公告内容完整，符合相关规定，同意发布'
      },
      {
        operationType: 'publish',
        operationName: '发布公告',
        operationUser: '王五',
        operationTime: '2024-01-25 10:00:00',
        operationComment: '公告已发布'
      }
    ];
  } catch (error) {
    console.error('加载操作记录失败:', error);
  }
};

onMounted(() => {
  loadOperationRecords();
});
</script>

<style scoped>
.operation-record {
  padding: 20px;
}

.no-data {
  text-align: center;
  padding: 40px 0;
}

.el-timeline-item :deep(.el-card) {
  margin-bottom: 0;
}

.el-timeline-item h4 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.el-timeline-item p {
  margin: 5px 0;
  color: #606266;
  font-size: 14px;
}
</style>
