<!-- 流标或中止管理 - 详情页面 -->
<!--
  流标或中止管理详情页面
  
  功能说明：
  - 基于 FuniDetail 组件的标准详情页面
  - 支持查看、审核两种模式
  - 集成多步骤流程、审核按钮、工作流等功能

  适用页面类型：
  - type="info": 详情页面
  - type="audit": 审核页面
-->
<template>
  <div>
    <!-- 业务详情组件 - 核心组件，集成了步骤流程、审核按钮、工作流等功能 -->
    <funi-detail
      :steps="steps"
      :bizName="bizName"
      :showWorkflow="false"
      :auditButtons="buttons"
      :beforeAuditFn="beforeAuditFn"
      :detailHeadOption="detailHeadOption || {}"
      :businessId="['info', 'audit'].includes(type) && businessId ? businessId : void 0"
      @auditEvent="auditEvent"
    />
  </div>
</template>

<script setup lang="jsx">
// Vue 3 Composition API 相关导入
import { ref, computed, onMounted } from 'vue';
// 路由相关导入
import { useRoute } from 'vue-router';
// 多标签页管理 Hook 导入
import { useMultiTab } from '@/utils/hooks/useMultiTab';
// 业务逻辑 Hook 导入
import { useBusiness } from '@/hooks/useBusiness.js';
// Store导入
import { useBidFailureAnnouncementStore } from '../../store.js';

// 自定义tab内容组件导入
import BidFailureAnnouncementInfo from './components/BidFailureAnnouncementInfo.vue'; // 流标或中止公告基本信息组件
import ProjectInfo from './components/ProjectInfo.vue'; // 项目信息组件
import OperationRecord from './components/OperationRecord.vue'; // 操作记录组件

// 获取当前路由信息
const route = useRoute();
// 多标签页管理实例
const multiTab = useMultiTab();
// 获取业务逻辑处理实例
const business = useBusiness();
// Store实例
const bidFailureStore = useBidFailureAnnouncementStore();

// 路由查询参数的响应式引用
const routeQuery = ref(route.query);

// 流标或中止公告记录ID
const id = ref(route.params.id);
// 流标或中止公告业务名称（从路由参数获取）
const bizName = computed(() => routeQuery.value.bizName);
// 流标或中止公告业务类型（从路由参数获取）
const type = computed(() => routeQuery.value.type);

// 流标或中止公告业务信息数据
const infoData = ref();
// 业务ID
const businessId = ref();
// 审核按钮配置数组
const buttons = ref([]);

// tab组件引用
const baseInfoRef = ref(); // 流标或中止公告基本信息组件引用

/**
 * 流标或中止公告详情页头部配置选项
 * 配置页面标题、状态、按钮等信息
 */
const detailHeadOption = computed(() => {
  return {
    statusName: '状态', // 状态名称
    title: routeQuery.value.title, // 页面标题
    no: infoData.value?.id, // 流标或中止公告业务编号
    status: infoData.value?.statusText, // 当前流标或中止公告业务节点状态
    btns: []
  };
});

/**
 * 审核前置函数
 * 在执行审核操作前进行必要的验证和处理
 * @param {Object} param - 审核参数
 * @param {string} param.businessExecutionType - 业务执行类型
 * @returns {Promise} 返回验证结果的Promise
 */
const beforeAuditFn = ({ businessExecutionType }) => {
  // 判断是否启用审核功能
  const enableAudit =
    ['AGREE', 'SUBMIT'].includes(businessExecutionType) && // 同意或提交操作
    ['BUSINESS_CONFIG_CODE'].includes(infoData.value.dicBusinessTypeCode); // 特定业务类型

  try {
    // 如果启用审核且基本信息组件有验证函数，则执行验证
    return enableAudit && baseInfoRef.value.authFun ? baseInfoRef.value.authFun() : Promise.resolve({});
  } catch {
    // 验证失败时返回拒绝的Promise
    return Promise.reject();
  }
};

/**
 * 流标或中止公告步骤配置
 * 定义流标或中止公告详情页面的各个步骤和对应的组件
 */
const steps = computed(() => {
  return [
    // 第一步：流标或中止公告基本信息
    {
      title: '公告信息', // 步骤标题
      preservable: true, // 是否可保存草稿
      type: BidFailureAnnouncementInfo, // 使用的流标或中止公告组件类型
      props: {
        id: id.value, // 流标或中止公告记录ID
        type: type.value, // 流标或中止公告业务类型
        data: infoData.value, // 流标或中止公告数据
        ref: e => (baseInfoRef.value = e), // 流标或中止公告组件引用设置
        isEdit: false // 详情页不可编辑
      },
      on: {
        // 更新ID事件处理器
        updateID: data => {
          id.value = data?.id;
          businessId.value = data?.businessId;
        },
        // 流标或中止公告信息更新事件处理器
        infoUpdate: data => {
          infoData.value = data;
          id.value = data?.id;
          businessId.value = data?.businessId;
        }
      }
    },
    // 第二步：项目信息
    {
      title: '项目信息', // 步骤标题
      preservable: false, // 是否可保存草稿
      type: ProjectInfo, // 项目信息组件
      props: {
        data: infoData.value // 传递流标或中止公告数据
      }
    },
    // 第三步：操作记录
    {
      title: '操作记录', // 步骤标题
      preservable: false, // 是否可保存草稿
      type: OperationRecord, // 操作记录组件
      props: {
        id: id.value // 传递流标或中止公告ID
      }
    }
  ];
});

/**
 * 流标或中止公告审核事件处理函数
 * 处理审核完成后的操作，关闭当前页面
 */
const auditEvent = () => {
  multiTab.closeCurrentPage();
};

/**
 * 加载流标或中止公告详情数据
 */
const loadDetailData = async () => {
  try {
    const data = await bidFailureStore.getBidFailureAnnouncement(id.value);
    infoData.value = data;

    // 根据类型和状态设置审核按钮
    if (type.value === 'audit' && data.status === 2) {
      buttons.value = [
        {
          name: '审核通过',
          type: 'primary',
          action: 'approve'
        },
        {
          name: '审核驳回',
          type: 'danger',
          action: 'reject'
        }
      ];
    }
  } catch (error) {
    console.error('加载详情失败:', error);
  }
};

// ==================== 生命周期钩子 ====================

/**
 * 组件挂载完成
 */
onMounted(() => {
  console.log('流标或中止管理详情页面已挂载');
  loadDetailData();
});
</script>
