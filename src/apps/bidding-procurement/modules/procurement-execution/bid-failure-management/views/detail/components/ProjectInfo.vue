<!-- 项目信息组件 -->
<template>
  <div class="project-info">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="项目名称">
        {{ data?.projectName || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="项目编号">
        {{ data?.projectCode || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="标段名称">
        {{ data?.sectionName || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="采购方式">
        {{ data?.procurementMethod || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="预算金额">
        <span v-if="data?.budgetAmount">
          ¥{{ formatAmount(data.budgetAmount) }}
        </span>
        <span v-else>--</span>
      </el-descriptions-item>
      <el-descriptions-item label="项目业主">
        {{ data?.projectOwner || '--' }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';

// 定义props
const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
});

/**
 * 格式化金额
 */
const formatAmount = (amount) => {
  if (!amount) return '0.00';
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};
</script>

<style scoped>
.project-info {
  padding: 20px;
}
</style>
