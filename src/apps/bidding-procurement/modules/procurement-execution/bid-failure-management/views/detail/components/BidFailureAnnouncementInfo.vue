<!-- 流标或中止公告信息组件 -->
<template>
  <div class="bid-failure-announcement-info">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="公告标题">
        {{ data?.title || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="流标/中止类型">
        <el-tag :type="data?.type === 'bid_failure' ? 'warning' : 'info'">
          {{ data?.typeText || '--' }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="是否公示">
        <el-tag :type="data?.isPublic ? 'success' : 'info'">
          {{ data?.isPublicText || '--' }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="审核状态">
        <el-tag :type="getStatusTagType(data?.status)">
          {{ data?.statusText || '--' }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="发布状态">
        {{ data?.publishStatusText || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="发布时间">
        {{ data?.publishTime || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="创建人">
        {{ data?.createUser || '--' }}
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ data?.createTime || '--' }}
      </el-descriptions-item>
    </el-descriptions>

    <!-- 流标或中止详情 -->
    <div class="content-section" style="margin-top: 20px;">
      <h3>流标或中止详情</h3>
      <div class="content-wrapper" v-html="data?.content || '暂无内容'"></div>
    </div>

    <!-- 其他附件 -->
    <div class="attachments-section" style="margin-top: 20px;" v-if="data?.attachments?.length">
      <h3>其他附件</h3>
      <funi-file-table 
        :fileList="data.attachments" 
        :showUpload="false"
        :showDelete="false"
      />
    </div>

    <!-- 审核依据 -->
    <div class="audit-basis-section" style="margin-top: 20px;" v-if="data?.auditBasis?.length">
      <h3>审核依据</h3>
      <funi-file-table 
        :fileList="data.auditBasis" 
        :showUpload="false"
        :showDelete="false"
      />
    </div>

    <!-- 审核意见 -->
    <div class="audit-comment-section" style="margin-top: 20px;" v-if="data?.auditComment">
      <h3>审核意见</h3>
      <el-input
        v-model="data.auditComment"
        type="textarea"
        :rows="3"
        readonly
        placeholder="暂无审核意见"
      />
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';

// 定义props
const props = defineProps({
  id: {
    type: [String, Number],
    default: ''
  },
  type: {
    type: String,
    default: 'info'
  },
  data: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

// 定义emits
const emit = defineEmits(['infoUpdate']);

/**
 * 获取状态标签类型
 */
const getStatusTagType = (status) => {
  const statusColorMap = {
    1: 'info',     // 草稿
    2: 'warning',  // 待审核
    3: 'success',  // 审核通过
    4: 'success',  // 已发布
    5: 'danger'    // 已撤销
  };
  return statusColorMap[status] || 'info';
};
</script>

<style scoped>
.bid-failure-announcement-info {
  padding: 20px;
}

.content-section h3,
.attachments-section h3,
.audit-basis-section h3,
.audit-comment-section h3 {
  margin-bottom: 10px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.content-wrapper {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  min-height: 100px;
  background-color: #fafafa;
}

.content-wrapper :deep(p) {
  margin: 0 0 10px 0;
  line-height: 1.6;
}

.content-wrapper :deep(p:last-child) {
  margin-bottom: 0;
}
</style>
