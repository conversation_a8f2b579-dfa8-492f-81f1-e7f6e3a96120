<!-- 流标或中止公告表单组件 -->
<template>
  <div class="bid-failure-announcement-form">
    <funi-form ref="formRef" v-model="formData" :schema="formSchema" label-width="120px" :disabled="!isEdit">
      <!-- 是否公示字段的额外提示信息 -->
      <template #isPublic-extra>
        <span v-if="autoIsPublic" style="margin-left: 10px; color: #909399; font-size: 12px">
          (根据采购金额自动判断)
        </span>
      </template>

      <!-- 富文本编辑器的边框样式 -->
      <template #content>
        <div style="border: 1px solid #dcdfe6; border-radius: 4px">
          <funi-rich-text-editor v-model="formData.content" :disabled="!isEdit" height="300px" />
        </div>
      </template>
    </funi-form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, defineProps, defineEmits, defineExpose } from 'vue';
import { useRouter } from 'vue-router';

// 定义props
const props = defineProps({
  id: {
    type: [String, Number],
    default: ''
  },
  type: {
    type: String,
    default: 'add'
  },
  data: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: true
  },
  bizName: {
    type: String,
    default: ''
  }
});

// 定义emits
const emit = defineEmits(['updateID', 'infoUpdate', 'save', 'submit']);

const router = useRouter();

// 表单引用
const formRef = ref();

// 表单数据
const formData = reactive({
  projectSectionId: '',
  title: '',
  type: '',
  isPublic: false,
  content: '',
  attachments: [],
  auditBasis: []
});

// 项目标段选项
const projectSectionOptions = ref([
  {
    value: 'section_001',
    label: '某某基础设施建设项目 - 第一标段',
    projectName: '某某基础设施建设项目',
    projectCode: 'PROJ-2024-001',
    sectionName: '第一标段',
    procurementMethod: '公开招标',
    budgetAmount: 15000000,
    projectOwner: '某某市政府'
  },
  {
    value: 'section_002',
    label: '办公设备采购项目 - 第一包',
    projectName: '办公设备采购项目',
    projectCode: 'PROJ-2024-002',
    sectionName: '第一包',
    procurementMethod: '竞争性谈判',
    budgetAmount: 1000000,
    projectOwner: '某某事业单位'
  }
]);

// 流标/中止类型选项
const typeOptions = ref([
  { value: 'bid_failure', label: '流标' },
  { value: 'termination', label: '中止' }
]);

// 是否自动判断公示
const autoIsPublic = computed(() => {
  const selectedSection = projectSectionOptions.value.find(option => option.value === formData.projectSectionId);
  return selectedSection && selectedSection.budgetAmount >= 2000000; // 200万以上自动公示
});

// 表单schema配置
const formSchema = computed(() => [
  {
    prop: 'projectSectionId',
    label: '项目标段',
    component: 'el-select',
    props: {
      placeholder: '请选择项目标段',
      style: { width: '100%' },
      disabled: !props.isEdit,
      options: projectSectionOptions.value
    },
    rules: [{ required: true, message: '请选择项目标段', trigger: 'change' }],
    span: 24,
    on: {
      change: handleProjectSectionChange
    }
  },
  {
    prop: 'title',
    label: '公告标题',
    component: 'el-input',
    props: {
      placeholder: '请输入公告标题',
      disabled: !props.isEdit
    },
    rules: [
      { required: true, message: '请输入公告标题', trigger: 'blur' },
      { min: 5, max: 100, message: '标题长度在 5 到 100 个字符', trigger: 'blur' }
    ],
    span: 12
  },
  {
    prop: 'type',
    label: '流标/中止类型',
    component: 'el-select',
    props: {
      placeholder: '请选择类型',
      style: { width: '100%' },
      disabled: !props.isEdit,
      options: typeOptions.value
    },
    rules: [{ required: true, message: '请选择流标/中止类型', trigger: 'change' }],
    span: 12
  },
  {
    prop: 'isPublic',
    label: '是否公示',
    component: 'el-switch',
    props: {
      disabled: !props.isEdit || autoIsPublic.value
    },
    span: 12,
    slots: {
      extra: 'isPublic-extra'
    }
  },
  {
    prop: 'content',
    label: '流标或中止详情',
    component: 'funi-rich-text-editor',
    props: {
      disabled: !props.isEdit,
      height: '300px'
    },
    rules: [{ required: true, message: '请输入流标或中止详情', trigger: 'blur' }],
    span: 24,
    slots: {
      default: 'content'
    }
  },
  {
    prop: 'attachments',
    label: '其他附件',
    component: 'funi-file-table',
    props: {
      showUpload: props.isEdit,
      showDelete: props.isEdit,
      accept: '.pdf,.doc,.docx,.xls,.xlsx',
      limit: 10
    },
    span: 24
  },
  {
    prop: 'auditBasis',
    label: '审核依据',
    component: 'funi-file-table',
    props: {
      showUpload: props.isEdit,
      showDelete: props.isEdit,
      accept: '.pdf,.doc,.docx,.xls,.xlsx',
      limit: 10
    },
    span: 24
  }
]);

/**
 * 处理项目标段变化
 */
const handleProjectSectionChange = value => {
  const selectedSection = projectSectionOptions.value.find(option => option.value === value);
  if (selectedSection) {
    // 根据采购金额自动判断是否公示
    if (selectedSection.budgetAmount >= 2000000) {
      formData.isPublic = true;
    }

    // 触发信息更新事件
    emit('infoUpdate', {
      ...formData,
      projectInfo: selectedSection
    });
  }
};

/**
 * 处理保存
 */
const handleSave = async () => {
  try {
    await formRef.value.validate();
    emit('save', formData);
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

/**
 * 处理提交
 */
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    emit('submit', formData);
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

/**
 * 处理取消
 */
const handleCancel = () => {
  router.go(-1);
};

/**
 * 初始化表单数据
 */
const initFormData = () => {
  if (props.data && Object.keys(props.data).length > 0) {
    Object.assign(formData, props.data);
  }
};

// 暴露验证方法
defineExpose({
  validate: () => formRef.value.validate(),
  resetFields: () => formRef.value.resetFields()
});

onMounted(() => {
  initFormData();
});
</script>

<style scoped>
.bid-failure-announcement-form {
  padding: 20px;
}

.el-row {
  margin-bottom: 20px;
}

.el-row:last-child {
  margin-bottom: 0;
}
</style>
