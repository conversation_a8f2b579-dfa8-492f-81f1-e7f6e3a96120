<!-- 流标或中止管理 - 新增/编辑页面 -->
<!--
  流标或中止管理新增/编辑页面
  
  功能说明：
  - 基于 FuniDetail 组件的标准新增/编辑页面
  - 支持多步骤流程和业务流程处理
  - 集成附件上传和提交成功提示功能

  适用页面类型：
  - type="add": 新增页面
  - type="edit": 编辑页面
-->
<template>
  <div>
    <!-- 流标或中止公告详情组件，包含步骤流程和头部信息 -->
    <funi-detail :bizName="bizName" :steps="steps" :detailHeadOption="detailHeadOption || {}" />
    <!-- 提交成功提示组件 -->
    <funi-submit-success ref="submitSuccessRef" />
  </div>
</template>

<script setup lang="jsx">
// Vue 3 Composition API 相关导入
import { ref, computed, onMounted } from 'vue';
// 路由相关导入
import { useRoute } from 'vue-router';
// 业务逻辑 Hook 导入
import { useBusiness } from '@/hooks/useBusiness.js';
// Element Plus 组件导入
import { ElNotification } from 'element-plus';
// Store导入
import { useBidFailureAnnouncementStore } from '../../store.js';

// 自定义tab内容组件 - 流标或中止公告基本信息组件
import BidFailureAnnouncementForm from './components/BidFailureAnnouncementForm.vue';

// 获取当前路由信息
const route = useRoute();
// Store实例
const bidFailureStore = useBidFailureAnnouncementStore();
// 获取业务逻辑处理实例
const business = useBusiness();
// 路由查询参数的响应式引用
const routeQuery = ref(route.query);

// 流标或中止公告记录ID
const id = ref(route.params.id || routeQuery.value.id);
// 流标或中止公告业务名称（从路由参数获取）
const bizName = computed(() => routeQuery.value.bizName);
// 流标或中止公告业务类型（从路由参数获取）
const type = computed(() => routeQuery.value.type);

// 流标或中止公告业务信息数据
const infoData = ref({});
// 业务ID
const businessId = ref();

// 详情页头部配置选项
const detailHeadOption = computed(() => {
  return {
    statusName: '状态', // 状态名称
    no: infoData.value?.id, // 业务编号
    title: routeQuery.value.title, // 页面标题
    status: infoData.value?.statusText // 当前业务节点状态
  };
});

// 提交成功组件的引用
const submitSuccessRef = ref();

// 表单组件引用
const formRef = ref();

// 步骤配置，定义流标或中止公告业务流程的各个步骤
const steps = computed(() => {
  // 判断是否为编辑模式（非审核和查看模式）
  const isEdit = !['audit', 'info'].includes(type.value);

  return [
    // 第一步：流标或中止公告基本信息填写
    {
      title: '基本信息', // 步骤标题
      preservable: true, // 是否可保存草稿
      type: BidFailureAnnouncementForm, // 自定义tab内容组件
      // 传递给组件的属性
      props: {
        id: id.value, // 流标或中止公告记录ID
        isEdit, // 是否编辑模式
        type: type.value, // 流标或中止公告业务类型
        bizName: bizName.value, // 流标或中止公告业务名称
        data: infoData.value, // 流标或中止公告数据
        ref: e => (formRef.value = e) // 表单组件引用
      },
      // 组件事件监听器
      on: {
        // 更新ID事件处理器
        updateID: data => {
          id.value = data?.id;
          businessId.value = data?.businessId;
        },
        // 流标或中止公告信息更新事件处理器
        infoUpdate: data => {
          infoData.value = data;
          id.value = data?.id;
          businessId.value = data?.businessId;
        }
      }
    },
    {
      title: '要件信息', // 步骤标题
      preservable: false, // 不可保存草稿
      type: 'FuniFileTable', // 使用系统文件表格组件
      props: {
        params: { businessId: businessId.value }, // 传递业务ID参数
        callbackFun: submitBusiness // 提交回调函数
      }
    }
  ];
});

/**
 * 提交业务流程
 * 根据业务类型确定业务代码，然后调用业务提交接口
 */
const submitBusiness = async () => {
  // 根据业务名称确定业务代码
  const businessCode =
    bizName.value === '新增'
      ? 'BUSINESS_CONFIG_CODE_ADD' // 新增业务代码
      : routeQuery.value.dicBusinessTypeCode; // 其他业务类型代码

  // 调用业务提交接口
  await business.submit(businessCode, businessId.value, 'SUBMIT', businessName.value);

  // 显示提交成功提示
  submitSuccessRef.value.show();
};

/**
 * 加载编辑数据
 */
const loadEditData = async () => {
  if (type.value === 'edit' && id.value) {
    try {
      const data = await bidFailureStore.getBidFailureAnnouncement(id.value);
      infoData.value = data;
    } catch (error) {
      console.error('加载编辑数据失败:', error);
      ElNotification({
        title: '错误',
        message: '加载数据失败',
        type: 'error',
        duration: 2000
      });
    }
  }
};

// ==================== 生命周期钩子 ====================

/**
 * 组件挂载完成
 */
onMounted(() => {
  console.log('流标或中止管理新增/编辑页面已挂载');
  loadEditData();
});
</script>
