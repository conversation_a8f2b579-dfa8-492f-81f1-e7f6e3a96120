/**
 * 流标或中止管理模块路由配置
 */

export default {
  path: 'bid-failure-management',
  name: 'BidFailureManagement',
  meta: { 
    title: '流标或中止管理', 
    isMenu: true 
  },
  children: [
    {
      path: 'list',
      name: 'BidFailureManagementList',
      component: () => import('./views/list/index.vue'),
      meta: { 
        title: '流标或中止公告列表' 
      }
    },
    {
      path: 'detail/:id',
      name: 'BidFailureManagementDetail',
      component: () => import('./views/detail/index.vue'),
      meta: { 
        title: '流标或中止公告详情' 
      }
    },
    {
      path: 'audit/:id',
      name: 'BidFailureManagementAudit',
      component: () => import('./views/audit/index.vue'),
      meta: { 
        title: '审核流标或中止公告' 
      }
    },
    {
      path: 'create',
      name: 'BidFailureManagementCreate',
      component: () => import('./views/create/index.vue'),
      meta: { 
        title: '新建流标或中止公告' 
      }
    },
    {
      path: 'edit/:id',
      name: 'BidFailureManagementEdit',
      component: () => import('./views/create/index.vue'),
      meta: { 
        title: '编辑流标或中止公告' 
      }
    }
  ]
};
