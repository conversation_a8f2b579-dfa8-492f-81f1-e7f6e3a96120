/**
 * 流标或中止管理 - Adapters层
 * 
 * 模板说明：
 * - 纯函数数据转换模板
 * - 负责API数据与前端数据结构的转换
 * - 不调用其他层，保持纯函数特性
 * 
 * 占位符说明：
 * - 流标或中止管理: 模块中文名称
 * - 流标或中止公告: 实体中文名称
 * - bidFailureAnnouncement: 实体英文名称
 */

/**
 * 流标或中止管理 数据转换适配器
 */
export const bidFailureAnnouncementAdapters = {
  /**
   * 适配列表数据
   * 将API返回的列表数据转换为前端需要的格式
   * @param {Object} apiResponse - API响应数据
   * @returns {Object} 转换后的列表数据
   */
  adaptBidFailureAnnouncementList(apiResponse) {
    if (!apiResponse) {
      return { list: [], total: 0 };
    }

    const { list, total } = apiResponse;
    
    return {
      list: list.map(item => this.adaptBidFailureAnnouncementDetail(item)),
      total: total || 0,
    };
  },

  /**
   * 适配单个流标或中止公告数据
   * 将API返回的单个流标或中止公告数据转换为前端格式
   * @param {Object} apiItem - API返回的单个流标或中止公告数据
   * @returns {Object} 转换后的流标或中止公告数据
   */
  adaptBidFailureAnnouncementDetail(apiItem) {
    if (!apiItem) return null;

    return {
      id: apiItem.id || '',
      title: apiItem.title || '',
      content: apiItem.content || '',
      type: apiItem.type || '',
      typeText: this.getTypeText(apiItem.type),
      isPublic: apiItem.isPublic !== undefined ? apiItem.isPublic : false,
      isPublicText: apiItem.isPublic ? '是' : '否',
      status: apiItem.status !== undefined ? apiItem.status : 1,
      statusText: this.getStatusText(apiItem.status),
      auditStatus: apiItem.auditStatus || '',
      auditStatusText: this.getAuditStatusText(apiItem.auditStatus),
      publishStatus: apiItem.publishStatus || '',
      publishStatusText: this.getPublishStatusText(apiItem.publishStatus),
      attachments: apiItem.attachments || [],
      auditBasis: apiItem.auditBasis || [],
      auditComment: apiItem.auditComment || '',
      publishTime: this.formatDateTime(apiItem.publishTime),
      createTime: this.formatDateTime(apiItem.createTime),
      updateTime: this.formatDateTime(apiItem.updateTime),
      createUser: apiItem.createUser || '',
      updateUser: apiItem.updateUser || '',
      // 项目标段信息
      projectName: apiItem.projectName || '',
      projectCode: apiItem.projectCode || '',
      sectionName: apiItem.sectionName || '',
      procurementMethod: apiItem.procurementMethod || '',
      budgetAmount: apiItem.budgetAmount || 0,
      projectOwner: apiItem.projectOwner || ''
    };
  },

  /**
   * 适配表单数据
   * 将前端表单数据转换为API需要的格式
   * @param {Object} formData - 前端表单数据
   * @returns {Object} 转换后的API数据
   */
  adaptBidFailureAnnouncementForm(formData) {
    if (!formData) return {};

    return {
      title: formData.title || '',
      content: formData.content || '',
      type: formData.type || '',
      isPublic: formData.isPublic !== undefined ? formData.isPublic : false,
      attachments: formData.attachments || [],
      auditBasis: formData.auditBasis || [],
      projectSectionId: formData.projectSectionId || ''
    };
  },

  /**
   * 适配项目标段选项数据
   * 将API返回的项目标段数据转换为选择器选项格式
   * @param {Array} apiData - API返回的项目标段数据
   * @returns {Array} 转换后的选项数据
   */
  adaptProjectSectionOptions(apiData) {
    if (!Array.isArray(apiData)) return [];

    return apiData.map(item => ({
      value: item.id || '',
      label: `${item.projectName || ''} - ${item.sectionName || ''}`,
      projectName: item.projectName || '',
      projectCode: item.projectCode || '',
      sectionName: item.sectionName || '',
      procurementMethod: item.procurementMethod || '',
      budgetAmount: item.budgetAmount || 0,
      projectOwner: item.projectOwner || '',
      // 其他15个字段
      ...item
    }));
  },

  /**
   * 获取流标/中止类型文本
   * @param {string} type - 类型值
   * @returns {string} 类型文本
   */
  getTypeText(type) {
    const typeMap = {
      'bid_failure': '流标',
      'termination': '中止'
    };
    return typeMap[type] || '未知';
  },

  /**
   * 获取状态文本
   * @param {number} status - 状态值
   * @returns {string} 状态文本
   */
  getStatusText(status) {
    const statusMap = {
      1: '草稿',
      2: '待审核',
      3: '审核通过',
      4: '已发布',
      5: '已撤销'
    };
    return statusMap[status] || '未知';
  },

  /**
   * 获取审核状态文本
   * @param {string} auditStatus - 审核状态值
   * @returns {string} 审核状态文本
   */
  getAuditStatusText(auditStatus) {
    const auditStatusMap = {
      'pending': '待审核',
      'in_progress': '审核中',
      'approved': '审核通过',
      'rejected': '审核驳回'
    };
    return auditStatusMap[auditStatus] || '未知';
  },

  /**
   * 获取发布状态文本
   * @param {string} publishStatus - 发布状态值
   * @returns {string} 发布状态文本
   */
  getPublishStatusText(publishStatus) {
    const publishStatusMap = {
      'unpublished': '未发布',
      'published': '已发布',
      'revoked': '已撤销'
    };
    return publishStatusMap[publishStatus] || '未知';
  },

  /**
   * 格式化日期时间
   * @param {string|Date} dateTime - 日期时间
   * @returns {string} 格式化后的日期时间字符串
   */
  formatDateTime(dateTime) {
    if (!dateTime) return '';
    
    try {
      const date = new Date(dateTime);
      if (isNaN(date.getTime())) return '';
      
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (error) {
      console.warn('日期格式化失败:', error);
      return '';
    }
  },

  /**
   * 格式化日期
   * @param {string|Date} date - 日期
   * @returns {string} 格式化后的日期字符串
   */
  formatDate(date) {
    if (!date) return '';
    
    try {
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) return '';
      
      return dateObj.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      console.warn('日期格式化失败:', error);
      return '';
    }
  },

  /**
   * 适配搜索参数
   * 将前端搜索条件转换为API参数
   * @param {Object} searchParams - 前端搜索参数
   * @returns {Object} 转换后的API参数
   */
  adaptSearchParams(searchParams) {
    if (!searchParams) return {};

    return {
      keyword: searchParams.keyword || '',
      status: searchParams.status !== undefined ? searchParams.status : null,
      auditStatus: searchParams.auditStatus || '',
      publishStatus: searchParams.publishStatus || '',
      projectName: searchParams.projectName || '',
      type: searchParams.type || '',
      startDate: searchParams.startDate || '',
      endDate: searchParams.endDate || ''
    };
  }
};

// 默认导出
export default bidFailureAnnouncementAdapters;
