/**
 * 流标或中止管理 - API层
 *
 * 模板说明：
 * - 标准的API接口封装模板
 * - 基于 window.$http 的统一请求方法
 * - 包含完整的CRUD操作接口和工作流接口
 *
 * 占位符说明：
 * - 流标或中止管理: 模块中文名称
 * - 流标或中止公告: 实体中文名称
 * - bidFailureAnnouncement: 实体英文名称
 * - /api/bid-failure-announcements: API基础路径
 */
import { enableMock, mockBidFailureAnnouncementList } from '../__mocks__/index.js';

/**
 * 流标或中止管理相关API接口
 */
export const bidFailureAnnouncementApi = {
  /**
   * 获取流标或中止公告列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页数量
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.status - 状态筛选
   * @param {string} params.type - 流标/中止类型筛选
   * @returns {Promise} 返回流标或中止公告列表数据
   */
  getBidFailureAnnouncementList(params = {}) {
    if (enableMock) {
      return { total: mockBidFailureAnnouncementList.length, list: mockBidFailureAnnouncementList };
    }
    return window.$http.post('/api/bid-failure-announcements/list', {
      page: params.page || 1,
      size: params.size || 10,
      keyword: params.keyword || '',
      status: params.status,
      type: params.type
    });
  },

  /**
   * 根据ID获取流标或中止公告详情
   * @param {string|number} id - 流标或中止公告ID
   * @returns {Promise} 返回流标或中止公告详情数据
   */
  getBidFailureAnnouncementDetail(id) {
    if (enableMock) {
      return mockBidFailureAnnouncementList.find(item => item.id === id);
    }
    return window.$http.fetch(`/api/bid-failure-announcements/${id}`);
  },

  /**
   * 创建新流标或中止公告
   * @param {Object} data - 流标或中止公告数据
   * @param {string} data.title - 公告标题
   * @param {string} data.content - 流标或中止详情
   * @param {string} data.type - 流标/中止类型
   * @param {boolean} data.isPublic - 是否公示
   * @param {Array} data.attachments - 其他附件
   * @param {Array} data.auditBasis - 审核依据
   * @returns {Promise} 返回创建结果
   */
  createBidFailureAnnouncement(data) {
    if (enableMock) {
      return { id: Date.now(), ...data, status: 1 };
    }
    return window.$http.post('/api/bid-failure-announcements/create', {
      title: data.title,
      content: data.content,
      type: data.type,
      isPublic: data.isPublic,
      attachments: data.attachments || [],
      auditBasis: data.auditBasis || [],
      projectSectionId: data.projectSectionId
    });
  },

  /**
   * 更新流标或中止公告信息
   * @param {string|number} id - 流标或中止公告ID
   * @param {Object} data - 更新数据
   * @param {string} data.title - 公告标题
   * @param {string} data.content - 流标或中止详情
   * @param {string} data.type - 流标/中止类型
   * @param {boolean} data.isPublic - 是否公示
   * @param {Array} data.attachments - 其他附件
   * @param {Array} data.auditBasis - 审核依据
   * @returns {Promise} 返回更新结果
   */
  updateBidFailureAnnouncement(id, data) {
    if (enableMock) {
      const item = mockBidFailureAnnouncementList.find(item => item.id === id);
      if (item) {
        Object.assign(item, data);
        return item;
      }
      return null;
    }
    return window.$http.post(`/api/bid-failure-announcements/update/${id}`, {
      title: data.title,
      content: data.content,
      type: data.type,
      isPublic: data.isPublic,
      attachments: data.attachments,
      auditBasis: data.auditBasis
    });
  },

  /**
   * 删除流标或中止公告
   * @param {string|number} id - 流标或中止公告ID
   * @returns {Promise} 返回删除结果
   */
  deleteBidFailureAnnouncement(id) {
    if (enableMock) {
      const index = mockBidFailureAnnouncementList.findIndex(item => item.id === id);
      if (index > -1) {
        mockBidFailureAnnouncementList.splice(index, 1);
      }
      return true;
    }
    return window.$http.post(`/api/bid-failure-announcements/delete/${id}`);
  },

  /**
   * 提交流标或中止公告审核
   * @param {string|number} id - 流标或中止公告ID
   * @returns {Promise} 返回提交结果
   */
  submitBidFailureAnnouncement(id) {
    if (enableMock) {
      const item = mockBidFailureAnnouncementList.find(item => item.id === id);
      if (item) {
        item.status = 2; // 待审核
        return item;
      }
      return null;
    }
    return window.$http.post(`/api/bid-failure-announcements/submit/${id}`);
  },

  /**
   * 审核流标或中止公告
   * @param {string|number} id - 流标或中止公告ID
   * @param {Object} data - 审核数据
   * @param {number} data.auditResult - 审核结果 (1:通过, 0:驳回)
   * @param {string} data.auditComment - 审核意见
   * @returns {Promise} 返回审核结果
   */
  auditBidFailureAnnouncement(id, data) {
    if (enableMock) {
      const item = mockBidFailureAnnouncementList.find(item => item.id === id);
      if (item) {
        item.status = data.auditResult === 1 ? 3 : 1; // 通过:审核通过, 驳回:草稿
        item.auditComment = data.auditComment;
        return item;
      }
      return null;
    }
    return window.$http.post(`/api/bid-failure-announcements/audit/${id}`, {
      auditResult: data.auditResult,
      auditComment: data.auditComment
    });
  },

  /**
   * 发布流标或中止公告
   * @param {string|number} id - 流标或中止公告ID
   * @returns {Promise} 返回发布结果
   */
  publishBidFailureAnnouncement(id) {
    if (enableMock) {
      const item = mockBidFailureAnnouncementList.find(item => item.id === id);
      if (item) {
        item.status = 4; // 已发布
        item.publishTime = new Date().toISOString();
        return item;
      }
      return null;
    }
    return window.$http.post(`/api/bid-failure-announcements/publish/${id}`);
  },

  /**
   * 撤销流标或中止公告
   * @param {string|number} id - 流标或中止公告ID
   * @returns {Promise} 返回撤销结果
   */
  revokeBidFailureAnnouncement(id) {
    if (enableMock) {
      const item = mockBidFailureAnnouncementList.find(item => item.id === id);
      if (item) {
        item.status = 1; // 草稿
        return item;
      }
      return null;
    }
    return window.$http.post(`/api/bid-failure-announcements/revoke/${id}`);
  },

  /**
   * 批量删除流标或中止公告
   * @param {Array} ids - 流标或中止公告ID数组
   * @returns {Promise} 返回批量删除结果
   */
  batchDelete(ids) {
    return window.$http.post('/api/bid-failure-announcements/batch-delete', {
      ids: ids
    });
  },

  /**
   * 导出流标或中止公告数据
   * @param {Object} params - 导出参数
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.status - 状态筛选
   * @param {string} params.type - 类型筛选
   * @returns {Promise} 返回导出文件信息
   */
  export(params = {}) {
    return window.$http.post('/api/bid-failure-announcements/export', {
      keyword: params.keyword || '',
      status: params.status,
      type: params.type
    });
  }
};

// 默认导出
export default bidFailureAnnouncementApi;
