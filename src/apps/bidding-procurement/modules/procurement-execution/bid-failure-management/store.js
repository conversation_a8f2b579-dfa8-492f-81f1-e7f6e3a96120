/**
 * 流标或中止管理 - Store层
 * 
 * 模板说明：
 * - 基于 Pinia 的状态管理模板
 * - 集成API层和Adapters层调用
 * - 包含完整的业务逻辑封装
 * 
 * 占位符说明：
 * - 流标或中止管理: 模块中文名称
 * - 流标或中止公告: 实体中文名称
 * - bidFailureAnnouncement: 实体英文名称
 * - useBidFailureAnnouncementStore: Store名称
 */

import { defineStore } from 'pinia';
import { ref, reactive } from 'vue';
import { bidFailureAnnouncementApi } from './api/index.js';
import { bidFailureAnnouncementAdapters } from './adapters/index.js';

/**
 * 流标或中止管理 Store
 */
export const useBidFailureAnnouncementStore = defineStore('bidFailureAnnouncement', () => {
  // 状态定义
  const bidFailureAnnouncementList = ref([]);
  const currentBidFailureAnnouncement = ref(null);
  const loading = ref(false);
  const pagination = reactive({
    page: 1,
    size: 10,
    total: 0
  });

  // 搜索条件
  const searchParams = reactive({
    keyword: '',
    status: null,
    auditStatus: '',
    publishStatus: '',
    projectName: '',
    type: ''
  });

  /**
   * 获取流标或中止公告列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回处理后的列表数据
   */
  const getBidFailureAnnouncementList = async (params = {}) => {
    try {
      loading.value = true;
      
      // 合并查询参数
      const queryParams = {
        ...searchParams,
        ...params,
        page: pagination.page,
        size: pagination.size
      };

      // 调用API
      const response = await bidFailureAnnouncementApi.getBidFailureAnnouncementList(queryParams);
      
      // 数据转换
      const adaptedData = bidFailureAnnouncementAdapters.adaptBidFailureAnnouncementList(response);
      
      // 更新状态
      bidFailureAnnouncementList.value = adaptedData.list;
      pagination.total = adaptedData.total;
      
      return adaptedData;
    } catch (error) {
      console.error('获取流标或中止公告列表失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 根据ID获取流标或中止公告详情
   * @param {string|number} id - 流标或中止公告ID
   * @returns {Promise} 返回处理后的详情数据
   */
  const getBidFailureAnnouncement = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await bidFailureAnnouncementApi.getBidFailureAnnouncementDetail(id);
      
      // 数据转换
      const adaptedData = bidFailureAnnouncementAdapters.adaptBidFailureAnnouncementDetail(response);
      
      // 更新状态
      currentBidFailureAnnouncement.value = adaptedData;
      
      return adaptedData;
    } catch (error) {
      console.error('获取流标或中止公告详情失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 创建新流标或中止公告
   * @param {Object} data - 流标或中止公告数据
   * @returns {Promise} 返回创建结果
   */
  const createBidFailureAnnouncement = async (data) => {
    try {
      loading.value = true;
      
      // 数据转换
      const apiData = bidFailureAnnouncementAdapters.adaptBidFailureAnnouncementForm(data);
      
      // 调用API
      const response = await bidFailureAnnouncementApi.createBidFailureAnnouncement(apiData);
      
      // 数据转换
      const adaptedData = bidFailureAnnouncementAdapters.adaptBidFailureAnnouncementDetail(response);
      
      return adaptedData;
    } catch (error) {
      console.error('创建流标或中止公告失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新流标或中止公告信息
   * @param {string|number} id - 流标或中止公告ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 返回更新结果
   */
  const updateBidFailureAnnouncement = async (id, data) => {
    try {
      loading.value = true;
      
      // 数据转换
      const apiData = bidFailureAnnouncementAdapters.adaptBidFailureAnnouncementForm(data);
      
      // 调用API
      const response = await bidFailureAnnouncementApi.updateBidFailureAnnouncement(id, apiData);
      
      // 数据转换
      const adaptedData = bidFailureAnnouncementAdapters.adaptBidFailureAnnouncementDetail(response);
      
      // 更新当前流标或中止公告状态
      if (currentBidFailureAnnouncement.value && currentBidFailureAnnouncement.value.id === id) {
        currentBidFailureAnnouncement.value = adaptedData;
      }
      
      return adaptedData;
    } catch (error) {
      console.error('更新流标或中止公告失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 删除流标或中止公告
   * @param {string|number} id - 流标或中止公告ID
   * @returns {Promise} 返回删除结果
   */
  const deleteBidFailureAnnouncement = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await bidFailureAnnouncementApi.deleteBidFailureAnnouncement(id);
      
      // 从列表中移除
      const index = bidFailureAnnouncementList.value.findIndex(item => item.id === id);
      if (index > -1) {
        bidFailureAnnouncementList.value.splice(index, 1);
        pagination.total--;
      }
      
      return response;
    } catch (error) {
      console.error('删除流标或中止公告失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 提交流标或中止公告审核
   * @param {string|number} id - 流标或中止公告ID
   * @returns {Promise} 返回提交结果
   */
  const submitBidFailureAnnouncement = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await bidFailureAnnouncementApi.submitBidFailureAnnouncement(id);
      
      // 数据转换
      const adaptedData = bidFailureAnnouncementAdapters.adaptBidFailureAnnouncementDetail(response);
      
      // 更新当前流标或中止公告状态
      if (currentBidFailureAnnouncement.value && currentBidFailureAnnouncement.value.id === id) {
        currentBidFailureAnnouncement.value = adaptedData;
      }
      
      return adaptedData;
    } catch (error) {
      console.error('提交流标或中止公告失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 审核流标或中止公告
   * @param {string|number} id - 流标或中止公告ID
   * @param {Object} data - 审核数据
   * @returns {Promise} 返回审核结果
   */
  const auditBidFailureAnnouncement = async (id, data) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await bidFailureAnnouncementApi.auditBidFailureAnnouncement(id, data);
      
      // 数据转换
      const adaptedData = bidFailureAnnouncementAdapters.adaptBidFailureAnnouncementDetail(response);
      
      // 更新当前流标或中止公告状态
      if (currentBidFailureAnnouncement.value && currentBidFailureAnnouncement.value.id === id) {
        currentBidFailureAnnouncement.value = adaptedData;
      }
      
      return adaptedData;
    } catch (error) {
      console.error('审核流标或中止公告失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 发布流标或中止公告
   * @param {string|number} id - 流标或中止公告ID
   * @returns {Promise} 返回发布结果
   */
  const publishBidFailureAnnouncement = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await bidFailureAnnouncementApi.publishBidFailureAnnouncement(id);
      
      // 数据转换
      const adaptedData = bidFailureAnnouncementAdapters.adaptBidFailureAnnouncementDetail(response);
      
      // 更新当前流标或中止公告状态
      if (currentBidFailureAnnouncement.value && currentBidFailureAnnouncement.value.id === id) {
        currentBidFailureAnnouncement.value = adaptedData;
      }
      
      return adaptedData;
    } catch (error) {
      console.error('发布流标或中止公告失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 撤销流标或中止公告
   * @param {string|number} id - 流标或中止公告ID
   * @returns {Promise} 返回撤销结果
   */
  const revokeBidFailureAnnouncement = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await bidFailureAnnouncementApi.revokeBidFailureAnnouncement(id);
      
      // 数据转换
      const adaptedData = bidFailureAnnouncementAdapters.adaptBidFailureAnnouncementDetail(response);
      
      // 更新当前流标或中止公告状态
      if (currentBidFailureAnnouncement.value && currentBidFailureAnnouncement.value.id === id) {
        currentBidFailureAnnouncement.value = adaptedData;
      }
      
      return adaptedData;
    } catch (error) {
      console.error('撤销流标或中止公告失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新搜索条件
   * @param {Object} params - 搜索参数
   */
  const updateSearchParams = (params) => {
    Object.assign(searchParams, params);
    pagination.page = 1; // 重置页码
  };

  /**
   * 更新分页信息
   * @param {Object} paginationData - 分页数据
   */
  const updatePagination = (paginationData) => {
    Object.assign(pagination, paginationData);
  };

  /**
   * 重置状态
   */
  const resetState = () => {
    bidFailureAnnouncementList.value = [];
    currentBidFailureAnnouncement.value = null;
    loading.value = false;
    pagination.page = 1;
    pagination.total = 0;
    searchParams.keyword = '';
    searchParams.status = null;
    searchParams.auditStatus = '';
    searchParams.publishStatus = '';
    searchParams.projectName = '';
    searchParams.type = '';
  };

  // 返回状态和方法
  return {
    // 状态
    bidFailureAnnouncementList,
    currentBidFailureAnnouncement,
    loading,
    pagination,
    searchParams,
    
    // 方法
    getBidFailureAnnouncementList,
    getBidFailureAnnouncement,
    createBidFailureAnnouncement,
    updateBidFailureAnnouncement,
    deleteBidFailureAnnouncement,
    submitBidFailureAnnouncement,
    auditBidFailureAnnouncement,
    publishBidFailureAnnouncement,
    revokeBidFailureAnnouncement,
    updateSearchParams,
    updatePagination,
    resetState
  };
});
