/**
 * 中标结果公示管理 - Store层
 * 
 * 模板说明：
 * - 基于 Pinia 的状态管理模板
 * - 集成API层和Adapters层调用
 * - 包含完整的业务逻辑封装
 * 
 * 占位符说明：
 * - 中标结果公示管理: 模块中文名称
 * - 中标结果公示: 实体中文名称
 * - awardResultAnnouncement: 实体英文名称
 * - useAwardResultAnnouncementStore: Store名称
 */

import { defineStore } from 'pinia';
import { ref, reactive } from 'vue';
import { awardResultAnnouncementApi } from './api/index.js';
import { awardResultAnnouncementAdapters } from './adapters/index.js';

/**
 * 中标结果公示管理 Store
 */
export const useAwardResultAnnouncementStore = defineStore('awardResultAnnouncement', () => {
  // 状态定义
  const awardResultAnnouncementList = ref([]);
  const currentAwardResultAnnouncement = ref(null);
  const loading = ref(false);
  const pagination = reactive({
    page: 1,
    size: 10,
    total: 0
  });

  // 搜索条件
  const searchParams = reactive({
    keyword: '',
    projectName: '',
    auditStatus: null,
    publishStatus: null
  });

  /**
   * 获取中标结果公示列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回处理后的列表数据
   */
  const getAwardResultAnnouncementList = async (params = {}) => {
    try {
      loading.value = true;
      
      // 合并查询参数
      const queryParams = {
        ...searchParams,
        ...params,
        page: pagination.page,
        size: pagination.size
      };

      // 调用API
      const response = await awardResultAnnouncementApi.getAwardResultAnnouncementList(queryParams);
      
      // 数据转换
      const adaptedData = awardResultAnnouncementAdapters.adaptAwardResultAnnouncementList(response);
      
      // 更新状态
      awardResultAnnouncementList.value = adaptedData.list;
      pagination.total = adaptedData.total;
      
      return adaptedData;
    } catch (error) {
      console.error('获取中标结果公示列表失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 根据ID获取中标结果公示详情
   * @param {string|number} id - 中标结果公示ID
   * @returns {Promise} 返回处理后的详情数据
   */
  const getAwardResultAnnouncementDetail = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await awardResultAnnouncementApi.getAwardResultAnnouncementDetail(id);
      
      // 数据转换
      const adaptedData = awardResultAnnouncementAdapters.adaptAwardResultAnnouncementDetail(response);
      
      // 更新状态
      currentAwardResultAnnouncement.value = adaptedData;
      
      return adaptedData;
    } catch (error) {
      console.error('获取中标结果公示详情失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 创建新中标结果公示
   * @param {Object} data - 中标结果公示数据
   * @returns {Promise} 返回创建结果
   */
  const createAwardResultAnnouncement = async (data) => {
    try {
      loading.value = true;
      
      // 数据转换
      const apiData = awardResultAnnouncementAdapters.adaptAwardResultAnnouncementForm(data);
      
      // 调用API
      const response = await awardResultAnnouncementApi.createAwardResultAnnouncement(apiData);
      
      // 数据转换
      const adaptedData = awardResultAnnouncementAdapters.adaptAwardResultAnnouncementDetail(response);
      
      return adaptedData;
    } catch (error) {
      console.error('创建中标结果公示失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新中标结果公示信息
   * @param {string|number} id - 中标结果公示ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 返回更新结果
   */
  const updateAwardResultAnnouncement = async (id, data) => {
    try {
      loading.value = true;
      
      // 数据转换
      const apiData = awardResultAnnouncementAdapters.adaptAwardResultAnnouncementForm(data);
      
      // 调用API
      const response = await awardResultAnnouncementApi.updateAwardResultAnnouncement(id, apiData);
      
      // 数据转换
      const adaptedData = awardResultAnnouncementAdapters.adaptAwardResultAnnouncementDetail(response);
      
      // 更新当前中标结果公示状态
      if (currentAwardResultAnnouncement.value && currentAwardResultAnnouncement.value.id === id) {
        currentAwardResultAnnouncement.value = adaptedData;
      }
      
      return adaptedData;
    } catch (error) {
      console.error('更新中标结果公示失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 删除中标结果公示
   * @param {string|number} id - 中标结果公示ID
   * @returns {Promise} 返回删除结果
   */
  const deleteAwardResultAnnouncement = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await awardResultAnnouncementApi.deleteAwardResultAnnouncement(id);
      
      // 从列表中移除
      const index = awardResultAnnouncementList.value.findIndex(item => item.id === id);
      if (index > -1) {
        awardResultAnnouncementList.value.splice(index, 1);
        pagination.total--;
      }
      
      return response;
    } catch (error) {
      console.error('删除中标结果公示失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 提交审核
   * @param {string|number} id - 中标结果公示ID
   * @returns {Promise} 返回提交结果
   */
  const submitAwardResultAnnouncement = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await awardResultAnnouncementApi.submitAwardResultAnnouncement(id);
      
      // 数据转换
      const adaptedData = awardResultAnnouncementAdapters.adaptAwardResultAnnouncementDetail(response);
      
      // 更新当前状态
      if (currentAwardResultAnnouncement.value && currentAwardResultAnnouncement.value.id === id) {
        currentAwardResultAnnouncement.value = adaptedData;
      }
      
      return adaptedData;
    } catch (error) {
      console.error('提交审核失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 审核中标结果公示
   * @param {string|number} id - 中标结果公示ID
   * @param {Object} auditData - 审核数据
   * @returns {Promise} 返回审核结果
   */
  const auditAwardResultAnnouncement = async (id, auditData) => {
    try {
      loading.value = true;
      
      // 数据转换
      const apiData = awardResultAnnouncementAdapters.adaptAuditData(auditData);
      
      // 调用API
      const response = await awardResultAnnouncementApi.auditAwardResultAnnouncement(id, apiData);
      
      // 数据转换
      const adaptedData = awardResultAnnouncementAdapters.adaptAwardResultAnnouncementDetail(response);
      
      // 更新当前状态
      if (currentAwardResultAnnouncement.value && currentAwardResultAnnouncement.value.id === id) {
        currentAwardResultAnnouncement.value = adaptedData;
      }
      
      return adaptedData;
    } catch (error) {
      console.error('审核中标结果公示失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 发布中标结果公示
   * @param {string|number} id - 中标结果公示ID
   * @returns {Promise} 返回发布结果
   */
  const publishAwardResultAnnouncement = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await awardResultAnnouncementApi.publishAwardResultAnnouncement(id);
      
      // 数据转换
      const adaptedData = awardResultAnnouncementAdapters.adaptAwardResultAnnouncementDetail(response);
      
      // 更新当前状态
      if (currentAwardResultAnnouncement.value && currentAwardResultAnnouncement.value.id === id) {
        currentAwardResultAnnouncement.value = adaptedData;
      }
      
      return adaptedData;
    } catch (error) {
      console.error('发布中标结果公示失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 撤销中标结果公示
   * @param {string|number} id - 中标结果公示ID
   * @returns {Promise} 返回撤销结果
   */
  const revokeAwardResultAnnouncement = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await awardResultAnnouncementApi.revokeAwardResultAnnouncement(id);
      
      // 数据转换
      const adaptedData = awardResultAnnouncementAdapters.adaptAwardResultAnnouncementDetail(response);
      
      // 更新当前状态
      if (currentAwardResultAnnouncement.value && currentAwardResultAnnouncement.value.id === id) {
        currentAwardResultAnnouncement.value = adaptedData;
      }
      
      return adaptedData;
    } catch (error) {
      console.error('撤销中标结果公示失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新搜索条件
   * @param {Object} params - 搜索参数
   */
  const updateSearchParams = (params) => {
    Object.assign(searchParams, params);
    pagination.page = 1; // 重置页码
  };

  /**
   * 更新分页信息
   * @param {Object} paginationData - 分页数据
   */
  const updatePagination = (paginationData) => {
    Object.assign(pagination, paginationData);
  };

  /**
   * 重置状态
   */
  const resetState = () => {
    awardResultAnnouncementList.value = [];
    currentAwardResultAnnouncement.value = null;
    loading.value = false;
    pagination.page = 1;
    pagination.total = 0;
    searchParams.keyword = '';
    searchParams.projectName = '';
    searchParams.auditStatus = null;
    searchParams.publishStatus = null;
  };

  // 返回状态和方法
  return {
    // 状态
    awardResultAnnouncementList,
    currentAwardResultAnnouncement,
    loading,
    pagination,
    searchParams,
    
    // 方法
    getAwardResultAnnouncementList,
    getAwardResultAnnouncementDetail,
    createAwardResultAnnouncement,
    updateAwardResultAnnouncement,
    deleteAwardResultAnnouncement,
    submitAwardResultAnnouncement,
    auditAwardResultAnnouncement,
    publishAwardResultAnnouncement,
    revokeAwardResultAnnouncement,
    updateSearchParams,
    updatePagination,
    resetState
  };
});
