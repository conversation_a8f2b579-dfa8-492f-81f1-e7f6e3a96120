/**
 * 中标结果公示管理 - Adapters层
 * 
 * 模板说明：
 * - 纯函数数据转换模板
 * - 负责API数据与前端数据结构的转换
 * - 不调用其他层，保持纯函数特性
 * 
 * 占位符说明：
 * - 中标结果公示管理: 模块中文名称
 * - 中标结果公示: 实体中文名称
 * - awardResultAnnouncement: 实体英文名称
 */

/**
 * 中标结果公示管理 数据转换适配器
 */
export const awardResultAnnouncementAdapters = {
  /**
   * 适配中标结果公示列表数据
   * 将API返回的列表数据转换为前端需要的格式
   * @param {Object} apiResponse - API响应数据
   * @returns {Object} 转换后的列表数据
   */
  adaptAwardResultAnnouncementList(apiResponse) {
    if (!apiResponse) {
      return { list: [], total: 0 };
    }

    const { list, total } = apiResponse;
    
    return {
      list: list.map(item => this.adaptAwardResultAnnouncementDetail(item)),
      total: total || 0,
    };
  },

  /**
   * 适配中标结果公示详情数据
   * 将API返回的详情数据转换为前端格式
   * @param {Object} apiItem - API返回的中标结果公示数据
   * @returns {Object} 转换后的中标结果公示数据
   */
  adaptAwardResultAnnouncementDetail(apiItem) {
    if (!apiItem) return null;

    return {
      id: apiItem.id || '',
      title: apiItem.title || '',
      projectId: apiItem.projectId || '',
      projectName: apiItem.projectName || '',
      sectionId: apiItem.sectionId || '',
      sectionName: apiItem.sectionName || '',
      winnerInfo: this.adaptWinnerInfo(apiItem.winnerInfo),
      winningAmount: apiItem.winningAmount || 0,
      ownerRepresentative: apiItem.ownerRepresentative || '',
      winningDescription: apiItem.winningDescription || '',
      exceptionDescription: apiItem.exceptionDescription || '',
      attachments: apiItem.attachments || [],
      publicityStartDate: this.formatDate(apiItem.publicityStartDate),
      publicityEndDate: this.formatDate(apiItem.publicityEndDate),
      auditStatus: apiItem.auditStatus !== undefined ? apiItem.auditStatus : 0,
      auditStatusText: this.getAuditStatusText(apiItem.auditStatus),
      publishStatus: apiItem.publishStatus !== undefined ? apiItem.publishStatus : 0,
      publishStatusText: this.getPublishStatusText(apiItem.publishStatus),
      publishTime: this.formatDateTime(apiItem.publishTime),
      createTime: this.formatDateTime(apiItem.createTime),
      updateTime: this.formatDateTime(apiItem.updateTime),
      createUser: apiItem.createUser || '',
      updateUser: apiItem.updateUser || ''
    };
  },

  /**
   * 适配中标人信息
   * @param {Array} winnerInfo - 中标人信息数组
   * @returns {Array} 转换后的中标人信息
   */
  adaptWinnerInfo(winnerInfo) {
    if (!Array.isArray(winnerInfo)) return [];
    
    return winnerInfo.map(winner => ({
      id: winner.id || '',
      companyName: winner.companyName || '',
      contactPerson: winner.contactPerson || '',
      contactPhone: winner.contactPhone || '',
      winningAmount: winner.winningAmount || 0,
      winningRate: winner.winningRate || 0,
      rank: winner.rank || 1
    }));
  },

  /**
   * 适配表单数据
   * 将前端表单数据转换为API需要的格式
   * @param {Object} formData - 前端表单数据
   * @returns {Object} 转换后的API数据
   */
  adaptAwardResultAnnouncementForm(formData) {
    if (!formData) return {};

    return {
      title: formData.title || '',
      projectId: formData.projectId || '',
      sectionId: formData.sectionId || '',
      winnerInfo: formData.winnerInfo || [],
      winningAmount: formData.winningAmount || 0,
      ownerRepresentative: formData.ownerRepresentative || '',
      winningDescription: formData.winningDescription || '',
      exceptionDescription: formData.exceptionDescription || '',
      attachments: formData.attachments || [],
      publicityStartDate: formData.publicityStartDate || '',
      publicityEndDate: formData.publicityEndDate || ''
    };
  },

  /**
   * 适配审核数据
   * 将前端审核表单数据转换为API需要的格式
   * @param {Object} auditData - 前端审核数据
   * @returns {Object} 转换后的API数据
   */
  adaptAuditData(auditData) {
    if (!auditData) return {};

    return {
      auditResult: auditData.auditResult || 1,
      auditComment: auditData.auditComment || ''
    };
  },

  /**
   * 获取审核状态文本
   * @param {number} auditStatus - 审核状态值
   * @returns {string} 审核状态文本
   */
  getAuditStatusText(auditStatus) {
    const statusMap = {
      0: '待提交',
      1: '审核中',
      2: '审核通过',
      3: '审核驳回'
    };
    return statusMap[auditStatus] || '未知';
  },

  /**
   * 获取发布状态文本
   * @param {number} publishStatus - 发布状态值
   * @returns {string} 发布状态文本
   */
  getPublishStatusText(publishStatus) {
    const statusMap = {
      0: '未发布',
      1: '已发布'
    };
    return statusMap[publishStatus] || '未知';
  },

  /**
   * 格式化日期时间
   * @param {string|Date} dateTime - 日期时间
   * @returns {string} 格式化后的日期时间字符串
   */
  formatDateTime(dateTime) {
    if (!dateTime) return '';
    
    try {
      const date = new Date(dateTime);
      if (isNaN(date.getTime())) return '';
      
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (error) {
      console.warn('日期格式化失败:', error);
      return '';
    }
  },

  /**
   * 格式化日期
   * @param {string|Date} date - 日期
   * @returns {string} 格式化后的日期字符串
   */
  formatDate(date) {
    if (!date) return '';
    
    try {
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) return '';
      
      return dateObj.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      console.warn('日期格式化失败:', error);
      return '';
    }
  },

  /**
   * 适配搜索参数
   * 将前端搜索条件转换为API参数
   * @param {Object} searchParams - 前端搜索参数
   * @returns {Object} 转换后的API参数
   */
  adaptSearchParams(searchParams) {
    if (!searchParams) return {};

    return {
      keyword: searchParams.keyword || '',
      projectName: searchParams.projectName || '',
      auditStatus: searchParams.auditStatus !== undefined ? searchParams.auditStatus : null,
      publishStatus: searchParams.publishStatus !== undefined ? searchParams.publishStatus : null,
      startDate: searchParams.startDate || '',
      endDate: searchParams.endDate || ''
    };
  }
};

// 默认导出
export default awardResultAnnouncementAdapters;
