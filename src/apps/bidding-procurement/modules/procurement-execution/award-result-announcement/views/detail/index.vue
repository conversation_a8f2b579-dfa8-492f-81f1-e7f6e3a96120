<!-- 中标结果公示管理 - 详情页面 -->
<template>
  <funi-detail 
    :steps="stepsConfig"
    :detailHeadOption="detailHeadOption"
    @onStepChange="handleStepChange"
  >
    <template #step1>
      <!-- 基本信息 -->
      <el-card class="detail-card" header="基本信息">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="公告标题">
            {{ detailData.title || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="项目名称">
            {{ detailData.projectName || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="标段名称">
            {{ detailData.sectionName || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="中标金额">
            {{ detailData.winningAmount ? (detailData.winningAmount / 10000).toFixed(2) + '万元' : '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="业主代表">
            {{ detailData.ownerRepresentative || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="公示时间">
            {{ detailData.publicityStartDate && detailData.publicityEndDate 
               ? `${detailData.publicityStartDate} 至 ${detailData.publicityEndDate}` 
               : '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="审核状态">
            <el-tag :type="getAuditStatusType(detailData.auditStatus)">
              {{ detailData.auditStatusText || '--' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发布状态">
            <el-tag :type="detailData.publishStatus === 1 ? 'success' : 'info'">
              {{ detailData.publishStatusText || '--' }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </template>

    <template #step2>
      <!-- 中标人信息 -->
      <el-card class="detail-card" header="中标人信息">
        <el-table :data="detailData.winnerInfo || []" border>
          <el-table-column prop="rank" label="排名" width="80" />
          <el-table-column prop="companyName" label="公司名称" />
          <el-table-column prop="contactPerson" label="联系人" />
          <el-table-column prop="contactPhone" label="联系电话" />
          <el-table-column prop="winningAmount" label="中标金额(万元)">
            <template #default="{ row }">
              {{ row.winningAmount ? (row.winningAmount / 10000).toFixed(2) : '--' }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </template>

    <template #step3>
      <!-- 详细说明 -->
      <el-card class="detail-card" header="中标情况说明">
        <div class="rich-text-content" v-html="detailData.winningDescription || '暂无说明'"></div>
      </el-card>

      <el-card class="detail-card" header="异常情况说明" style="margin-top: 20px;">
        <div class="rich-text-content" v-html="detailData.exceptionDescription || '无异常情况'"></div>
      </el-card>

      <!-- 附件信息 -->
      <el-card class="detail-card" header="相关附件" style="margin-top: 20px;">
        <funi-file-table 
          v-if="detailData.attachments && detailData.attachments.length > 0"
          :fileList="detailData.attachments"
          :readonly="true"
        />
        <div v-else class="no-data">暂无附件</div>
      </el-card>
    </template>

    <template #step4>
      <!-- 操作记录 -->
      <el-card class="detail-card" header="操作记录">
        <el-timeline>
          <el-timeline-item 
            v-if="detailData.createTime"
            :timestamp="detailData.createTime" 
            placement="top"
          >
            <el-card>
              <h4>创建公示</h4>
              <p>创建人：{{ detailData.createUser || '--' }}</p>
            </el-card>
          </el-timeline-item>
          
          <el-timeline-item 
            v-if="detailData.auditStatus >= 1"
            :timestamp="detailData.updateTime" 
            placement="top"
          >
            <el-card>
              <h4>提交审核</h4>
              <p>操作人：{{ detailData.updateUser || '--' }}</p>
            </el-card>
          </el-timeline-item>

          <el-timeline-item 
            v-if="detailData.auditStatus >= 2"
            :timestamp="detailData.updateTime" 
            placement="top"
          >
            <el-card>
              <h4>审核{{ detailData.auditStatus === 2 ? '通过' : '驳回' }}</h4>
              <p>审核人：{{ detailData.updateUser || '--' }}</p>
            </el-card>
          </el-timeline-item>

          <el-timeline-item 
            v-if="detailData.publishTime"
            :timestamp="detailData.publishTime" 
            placement="top"
          >
            <el-card>
              <h4>发布公示</h4>
              <p>发布人：{{ detailData.updateUser || '--' }}</p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </template>
  </funi-detail>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';
import { reactive, ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { useAwardResultAnnouncementStore } from '../../store.js';

// 路由和Store
const router = useRouter();
const route = useRoute();
const store = useAwardResultAnnouncementStore();

// 详情数据
const detailData = ref({});

// 步骤配置
const stepsConfig = reactive([
  {
    title: '基本信息',
    name: 'step1'
  },
  {
    title: '中标人信息',
    name: 'step2'
  },
  {
    title: '详细说明',
    name: 'step3'
  },
  {
    title: '操作记录',
    name: 'step4'
  }
]);

// 详情页头部配置
const detailHeadOption = reactive({
  title: '中标结果公示详情',
  btns: computed(() => {
    const btns = [];
    
    // 根据状态显示不同操作按钮
    if (detailData.value.auditStatus === 0) {
      btns.push({
        label: '编辑',
        type: 'primary',
        on: {
          click: () => handleEdit()
        }
      });
      btns.push({
        label: '提交审核',
        type: 'success',
        on: {
          click: () => handleSubmit()
        }
      });
    } else if (detailData.value.auditStatus === 2 && detailData.value.publishStatus === 0) {
      btns.push({
        label: '发布',
        type: 'success',
        on: {
          click: () => handlePublish()
        }
      });
    } else if (detailData.value.publishStatus === 1) {
      btns.push({
        label: '撤销',
        type: 'warning',
        on: {
          click: () => handleRevoke()
        }
      });
    }
    
    btns.push({
      label: '返回',
      on: {
        click: () => handleBack()
      }
    });
    
    return btns;
  })
});

// 获取审核状态类型
const getAuditStatusType = (status) => {
  const statusMap = {
    0: 'info',
    1: 'warning',
    2: 'success',
    3: 'danger'
  };
  return statusMap[status] || 'info';
};

// 事件处理函数
const handleStepChange = (step) => {
  console.log('步骤切换:', step);
};

const handleEdit = () => {
  router.push({
    name: 'AwardResultAnnouncementEdit',
    params: { id: route.params.id }
  });
};

const handleSubmit = async () => {
  try {
    await store.submitAwardResultAnnouncement(route.params.id);
    ElMessage.success('提交审核成功');
    loadData(); // 重新加载数据
  } catch (error) {
    ElMessage.error('提交审核失败');
  }
};

const handlePublish = async () => {
  try {
    await store.publishAwardResultAnnouncement(route.params.id);
    ElMessage.success('发布成功');
    loadData(); // 重新加载数据
  } catch (error) {
    ElMessage.error('发布失败');
  }
};

const handleRevoke = async () => {
  try {
    await store.revokeAwardResultAnnouncement(route.params.id);
    ElMessage.success('撤销成功');
    loadData(); // 重新加载数据
  } catch (error) {
    ElMessage.error('撤销失败');
  }
};

const handleBack = () => {
  router.push({ name: 'AwardResultAnnouncementList' });
};

// 加载数据
const loadData = async () => {
  if (route.params.id) {
    try {
      const data = await store.getAwardResultAnnouncementDetail(route.params.id);
      detailData.value = data || {};
    } catch (error) {
      ElMessage.error('加载数据失败');
    }
  }
};

// 组件挂载
onMounted(() => {
  loadData();
  console.log('中标结果公示详情页面已挂载');
});
</script>

<style scoped>
.detail-card {
  margin-bottom: 20px;
}

.rich-text-content {
  min-height: 100px;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 20px;
}
</style>
