<!-- 中标结果公示管理 - 列表页面 -->
<template>
  <funi-list-page-v2 ref="listPageRef" :cardTab="cardTabConfig" @headBtnClick="handleHeadBtnClick" />
</template>

<script setup lang="jsx">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElNotification, ElMessageBox } from 'element-plus';
import { useAwardResultAnnouncementStore } from '../../store.js';

// ==================== 基础变量配置 ====================
const listPageRef = ref();
const router = useRouter();
const store = useAwardResultAnnouncementStore();

// ==================== 权限配置 ====================
const auths = {
  export: 'award_result_announcement_export',
  add: 'award_result_announcement_add',
  delete: 'award_result_announcement_delete',
  audit: 'award_result_announcement_audit',
  edit: 'award_result_announcement_edit',
  publish: 'award_result_announcement_publish',
  revoke: 'award_result_announcement_revoke',
  detail: 'award_result_announcement_detail'
};

// ==================== 核心业务函数 ====================

/**
 * 跳转到详情页面
 */
const goDetail = row => {
  router.push({
    name: 'AwardResultAnnouncementDetail',
    params: { id: row.id }
  });
};

/**
 * 删除记录
 */
const deleteItem = async id => {
  try {
    await store.deleteAwardResultAnnouncement(id);
    ElNotification({
      title: '提示',
      message: '删除成功',
      type: 'success',
      duration: 2000
    });
    listPageRef.value.reload({ resetPage: false });
  } catch (error) {
    console.error('删除失败:', error);
    ElNotification({
      title: '错误',
      message: '删除失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

/**
 * 跳转到编辑页面
 */
const editItem = row => {
  router.push({
    name: 'AwardResultAnnouncementEdit',
    params: { id: row.id }
  });
};

/**
 * 跳转到审核页面
 */
const auditItem = row => {
  router.push({
    name: 'AwardResultAnnouncementAudit',
    params: { id: row.id }
  });
};

/**
 * 提交审核
 */
const submitItem = async row => {
  try {
    await store.submitAwardResultAnnouncement(row.id);
    ElNotification({
      title: '提示',
      message: '提交审核成功',
      type: 'success',
      duration: 2000
    });
    listPageRef.value.reload({ resetPage: false });
  } catch (error) {
    console.error('提交审核失败:', error);
    ElNotification({
      title: '错误',
      message: '提交审核失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

/**
 * 发布公示
 */
const publishItem = async row => {
  try {
    await store.publishAwardResultAnnouncement(row.id);
    ElNotification({
      title: '提示',
      message: '发布成功',
      type: 'success',
      duration: 2000
    });
    listPageRef.value.reload({ resetPage: false });
  } catch (error) {
    console.error('发布失败:', error);
    ElNotification({
      title: '错误',
      message: '发布失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

/**
 * 撤销公示
 */
const revokeItem = async row => {
  ElMessageBox.confirm('确认撤销该公示吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await store.revokeAwardResultAnnouncement(row.id);
      ElNotification({
        title: '提示',
        message: '撤销成功',
        type: 'success',
        duration: 2000
      });
      listPageRef.value.reload({ resetPage: false });
    } catch (error) {
      console.error('撤销失败:', error);
      ElNotification({
        title: '错误',
        message: '撤销失败，请重试',
        type: 'error',
        duration: 2000
      });
    }
  }).catch(() => {
    // 用户取消操作
  });
};

// ==================== 动态按钮生成器 ====================
const generateActionButtons = (row) => {
  const buttons = [];
  
  // 根据状态显示不同按钮
  if (row.auditStatus === 0) { // 待提交
    buttons.push(
      <el-link type="primary" onClick={() => editItem(row)} style={{ marginRight: '10px' }}>
        编辑
      </el-link>,
      <el-link type="primary" onClick={() => submitItem(row)} style={{ marginRight: '10px' }}>
        提交
      </el-link>,
      <el-popconfirm title="是否删除?" onConfirm={() => deleteItem(row.id)}>
        {{
          reference: () => (
            <el-link type="danger">删除</el-link>
          )
        }}
      </el-popconfirm>
    );
  } else if (row.auditStatus === 1) { // 审核中
    buttons.push(
      <el-link type="primary" onClick={() => auditItem(row)} style={{ marginRight: '10px' }}>
        审核
      </el-link>
    );
  } else if (row.auditStatus === 2 && row.publishStatus === 0) { // 审核通过，未发布
    buttons.push(
      <el-link type="primary" onClick={() => publishItem(row)} style={{ marginRight: '10px' }}>
        发布
      </el-link>
    );
  } else if (row.publishStatus === 1) { // 已发布
    buttons.push(
      <el-link type="warning" onClick={() => revokeItem(row)}>
        撤销
      </el-link>
    );
  }
  
  // 详情按钮始终显示
  buttons.unshift(
    <el-link type="primary" onClick={() => goDetail(row)} style={{ marginRight: '10px' }}>
      详情
    </el-link>
  );
  
  return buttons;
};

/**
 * 表格列配置
 */
const columns = reactive([
  {
    label: '公告标题',
    prop: 'title',
    fixed: 'left',
    render: ({ row }) => {
      return (
        <el-link type="primary" onClick={() => goDetail(row)}>
          {row.title || '--'}
        </el-link>
      );
    }
  },
  {
    label: '项目名称',
    prop: 'projectName'
  },
  {
    label: '标段名称',
    prop: 'sectionName'
  },
  {
    label: '中标金额(万元)',
    prop: 'winningAmount',
    render: ({ row }) => {
      return row.winningAmount ? (row.winningAmount / 10000).toFixed(2) : '--';
    }
  },
  {
    label: '审核状态',
    prop: 'auditStatusText',
    render: ({ row }) => {
      const statusMap = {
        0: 'info',
        1: 'warning', 
        2: 'success',
        3: 'danger'
      };
      return (
        <el-tag type={statusMap[row.auditStatus] || 'info'}>
          {row.auditStatusText || '--'}
        </el-tag>
      );
    }
  },
  {
    label: '发布状态',
    prop: 'publishStatusText',
    render: ({ row }) => {
      return (
        <el-tag type={row.publishStatus === 1 ? 'success' : 'info'}>
          {row.publishStatusText || '--'}
        </el-tag>
      );
    }
  },
  {
    label: '公示时间',
    prop: 'publicityStartDate'
  },
  {
    label: '创建时间',
    prop: 'createTime'
  },
  {
    label: '操作',
    prop: 'actions',
    width: 200,
    fixed: 'right',
    render: ({ row }) => {
      return <div>{generateActionButtons(row)}</div>;
    }
  }
]);

// 数据加载函数
const loadData = async (pageParams, searchParams) => {
  try {
    store.updatePagination(pageParams);
    store.updateSearchParams(searchParams);
    const result = await store.getAwardResultAnnouncementList();
    return {
      list: result.list,
      total: result.total
    };
  } catch (error) {
    console.error('加载数据失败:', error);
    return { list: [], total: 0 };
  }
};

// 页签配置
const cardTabConfig = computed(() => {
  return [
    {
      label: '全部',
      key: 'all',
      curdOption: {
        columns,
        lodaData: loadData,
        btns: [
          { key: 'add', label: '新建', auth: auths.add, type: 'primary' }
        ],
        fixedButtons: true,
        reloadOnActive: true
      }
    },
    {
      label: '待审核',
      key: 'pending',
      curdOption: {
        columns,
        lodaData: (pageParams, searchParams) => loadData(pageParams, { ...searchParams, auditStatus: 1 }),
        btns: [],
        fixedButtons: true,
        reloadOnActive: true
      }
    },
    {
      label: '已发布',
      key: 'published',
      curdOption: {
        columns,
        lodaData: (pageParams, searchParams) => loadData(pageParams, { ...searchParams, publishStatus: 1 }),
        btns: [],
        fixedButtons: true,
        reloadOnActive: true
      }
    }
  ];
});

// 新增功能
const addItem = () => {
  router.push({
    name: 'AwardResultAnnouncementCreate'
  });
};

// 头部按钮点击事件处理
const handleHeadBtnClick = key => {
  switch (key) {
    case 'add':
      addItem();
      break;
    default:
      break;
  }
};

// ==================== 生命周期钩子 ====================
onMounted(() => {
  console.log('中标结果公示管理列表页面已挂载');
});
</script>
