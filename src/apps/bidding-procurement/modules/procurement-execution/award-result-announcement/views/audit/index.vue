<!-- 中标结果公示管理 - 审核页面 -->
<template>
  <funi-detail 
    :steps="stepsConfig"
    :detailHeadOption="detailHeadOption"
    @onStepChange="handleStepChange"
  >
    <template #step1>
      <!-- 基本信息（只读） -->
      <el-card class="detail-card" header="基本信息">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="公告标题">
            {{ detailData.title || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="项目名称">
            {{ detailData.projectName || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="标段名称">
            {{ detailData.sectionName || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="中标金额">
            {{ detailData.winningAmount ? (detailData.winningAmount / 10000).toFixed(2) + '万元' : '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="业主代表">
            {{ detailData.ownerRepresentative || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="公示时间">
            {{ detailData.publicityStartDate && detailData.publicityEndDate 
               ? `${detailData.publicityStartDate} 至 ${detailData.publicityEndDate}` 
               : '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="当前状态">
            <el-tag :type="getAuditStatusType(detailData.auditStatus)">
              {{ detailData.auditStatusText || '--' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ detailData.createTime || '--' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </template>

    <template #step2>
      <!-- 中标人信息（只读） -->
      <el-card class="detail-card" header="中标人信息">
        <el-table :data="detailData.winnerInfo || []" border>
          <el-table-column prop="rank" label="排名" width="80" />
          <el-table-column prop="companyName" label="公司名称" />
          <el-table-column prop="contactPerson" label="联系人" />
          <el-table-column prop="contactPhone" label="联系电话" />
          <el-table-column prop="winningAmount" label="中标金额(万元)">
            <template #default="{ row }">
              {{ row.winningAmount ? (row.winningAmount / 10000).toFixed(2) : '--' }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </template>

    <template #step3>
      <!-- 详细说明（只读） -->
      <el-card class="detail-card" header="中标情况说明">
        <div class="rich-text-content" v-html="detailData.winningDescription || '暂无说明'"></div>
      </el-card>

      <el-card class="detail-card" header="异常情况说明" style="margin-top: 20px;">
        <div class="rich-text-content" v-html="detailData.exceptionDescription || '无异常情况'"></div>
      </el-card>

      <!-- 附件信息（只读） -->
      <el-card class="detail-card" header="相关附件" style="margin-top: 20px;">
        <funi-file-table 
          v-if="detailData.attachments && detailData.attachments.length > 0"
          :fileList="detailData.attachments"
          :readonly="true"
        />
        <div v-else class="no-data">暂无附件</div>
      </el-card>
    </template>

    <template #step4>
      <!-- 审核操作 -->
      <el-card class="detail-card" header="审核操作">
        <funi-form
          ref="auditFormRef"
          :schema="auditFormSchema"
          :model="auditData"
          :rules="auditFormRules"
          label-width="120px"
        />
        
        <div class="audit-buttons" style="margin-top: 20px;">
          <el-button type="success" @click="handleAudit(1)">
            审核通过
          </el-button>
          <el-button type="danger" @click="handleAudit(2)">
            审核驳回
          </el-button>
        </div>
      </el-card>

      <!-- 审核历史 -->
      <el-card class="detail-card" header="审核历史" style="margin-top: 20px;">
        <el-timeline>
          <el-timeline-item 
            v-if="detailData.createTime"
            :timestamp="detailData.createTime" 
            placement="top"
          >
            <el-card>
              <h4>创建公示</h4>
              <p>创建人：{{ detailData.createUser || '--' }}</p>
            </el-card>
          </el-timeline-item>
          
          <el-timeline-item 
            v-if="detailData.auditStatus >= 1"
            :timestamp="detailData.updateTime" 
            placement="top"
          >
            <el-card>
              <h4>提交审核</h4>
              <p>操作人：{{ detailData.updateUser || '--' }}</p>
            </el-card>
          </el-timeline-item>

          <el-timeline-item 
            v-if="detailData.auditStatus >= 2"
            :timestamp="detailData.updateTime" 
            placement="top"
          >
            <el-card>
              <h4>审核{{ detailData.auditStatus === 2 ? '通过' : '驳回' }}</h4>
              <p>审核人：{{ detailData.updateUser || '--' }}</p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </template>
  </funi-detail>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';
import { reactive, ref, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useAwardResultAnnouncementStore } from '../../store.js';

// 路由和Store
const router = useRouter();
const route = useRoute();
const store = useAwardResultAnnouncementStore();

// 表单引用
const auditFormRef = ref(null);

// 详情数据
const detailData = ref({});

// 审核数据
const auditData = reactive({
  auditResult: 1, // 1:通过, 2:驳回
  auditComment: ''
});

// 步骤配置
const stepsConfig = reactive([
  {
    title: '基本信息',
    name: 'step1'
  },
  {
    title: '中标人信息',
    name: 'step2'
  },
  {
    title: '详细说明',
    name: 'step3'
  },
  {
    title: '审核操作',
    name: 'step4'
  }
]);

// 详情页头部配置
const detailHeadOption = reactive({
  title: '审核中标结果公示',
  btns: [
    {
      label: '返回',
      on: {
        click: () => handleBack()
      }
    }
  ]
});

// 审核表单配置
const auditFormSchema = reactive([
  {
    prop: 'auditComment',
    label: '审核意见',
    component: 'el-input',
    componentProps: {
      type: 'textarea',
      rows: 4,
      placeholder: '请输入审核意见'
    },
    span: 24
  }
]);

// 审核表单验证规则
const auditFormRules = reactive({
  auditComment: [
    { required: true, message: '请输入审核意见', trigger: 'blur' }
  ]
});

// 获取审核状态类型
const getAuditStatusType = (status) => {
  const statusMap = {
    0: 'info',
    1: 'warning',
    2: 'success',
    3: 'danger'
  };
  return statusMap[status] || 'info';
};

// 事件处理函数
const handleStepChange = (step) => {
  console.log('步骤切换:', step);
};

const handleAudit = async (auditResult) => {
  try {
    // 表单验证
    const valid = await auditFormRef.value.validate();
    if (!valid) return;

    const resultText = auditResult === 1 ? '通过' : '驳回';
    
    await ElMessageBox.confirm(
      `确认审核${resultText}该中标结果公示吗？`,
      '确认审核',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 设置审核结果
    auditData.auditResult = auditResult;
    
    // 调用审核接口
    await store.auditAwardResultAnnouncement(route.params.id, auditData);
    
    ElMessage.success(`审核${resultText}成功`);
    
    // 返回列表页
    handleBack();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('审核操作失败');
    }
  }
};

const handleBack = () => {
  router.push({ name: 'AwardResultAnnouncementList' });
};

// 加载数据
const loadData = async () => {
  if (route.params.id) {
    try {
      const data = await store.getAwardResultAnnouncementDetail(route.params.id);
      detailData.value = data || {};
      
      // 检查是否可以审核
      if (data.auditStatus !== 1) {
        ElMessage.warning('该公示不在审核状态，无法进行审核操作');
        handleBack();
      }
    } catch (error) {
      ElMessage.error('加载数据失败');
    }
  }
};

// 组件挂载
onMounted(() => {
  loadData();
  console.log('中标结果公示审核页面已挂载');
});
</script>

<style scoped>
.detail-card {
  margin-bottom: 20px;
}

.rich-text-content {
  min-height: 100px;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 20px;
}

.audit-buttons {
  text-align: center;
}

.audit-buttons .el-button {
  margin: 0 10px;
}
</style>
