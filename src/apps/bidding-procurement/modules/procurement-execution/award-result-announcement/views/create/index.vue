<!-- 中标结果公示管理 - 新建/编辑页面 -->
<template>
  <funi-detail 
    :steps="stepsConfig"
    :detailHeadOption="detailHeadOption"
    @onStepChange="handleStepChange"
  >
    <template #step1>
      <!-- 基本信息 -->
      <el-card class="form-card" header="基本信息">
        <funi-form
          ref="basicFormRef"
          :schema="basicFormSchema"
          :model="formData"
          :rules="basicFormRules"
          label-width="120px"
        />
      </el-card>
    </template>

    <template #step2>
      <!-- 中标人信息 -->
      <el-card class="form-card" header="中标人信息">
        <el-button type="primary" @click="addWinner" style="margin-bottom: 20px;">
          添加中标人
        </el-button>
        
        <el-table :data="formData.winnerInfo" border>
          <el-table-column prop="rank" label="排名" width="80">
            <template #default="{ row, $index }">
              {{ $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="公司名称" width="200">
            <template #default="{ row }">
              <el-input v-model="row.companyName" placeholder="请输入公司名称" />
            </template>
          </el-table-column>
          <el-table-column label="联系人" width="120">
            <template #default="{ row }">
              <el-input v-model="row.contactPerson" placeholder="请输入联系人" />
            </template>
          </el-table-column>
          <el-table-column label="联系电话" width="150">
            <template #default="{ row }">
              <el-input v-model="row.contactPhone" placeholder="请输入联系电话" />
            </template>
          </el-table-column>
          <el-table-column label="中标金额(万元)" width="150">
            <template #default="{ row }">
              <el-input-number 
                v-model="row.winningAmount" 
                :precision="2"
                :min="0"
                placeholder="请输入中标金额"
                style="width: 100%;"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="{ $index }">
              <el-button type="danger" size="small" @click="removeWinner($index)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </template>

    <template #step3>
      <!-- 详细说明 -->
      <el-card class="form-card" header="详细说明">
        <funi-form
          ref="descriptionFormRef"
          :schema="descriptionFormSchema"
          :model="formData"
          label-width="120px"
        />
      </el-card>

      <!-- 附件上传 -->
      <el-card class="form-card" header="相关附件" style="margin-top: 20px;">
        <funi-file-table 
          v-model="formData.attachments"
          :businessId="formData.id"
          :readonly="false"
        />
      </el-card>
    </template>
  </funi-detail>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';
import { reactive, ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { useAwardResultAnnouncementStore } from '../../store.js';

// 路由和Store
const router = useRouter();
const route = useRoute();
const store = useAwardResultAnnouncementStore();

// 表单引用
const basicFormRef = ref(null);
const descriptionFormRef = ref(null);

// 页面模式
const pageMode = computed(() => {
  return route.name === 'AwardResultAnnouncementCreate' ? 'create' : 'edit';
});

// 表单数据
const formData = reactive({
  title: '',
  projectId: '',
  projectName: '',
  sectionId: '',
  sectionName: '',
  winnerInfo: [],
  winningAmount: 0,
  ownerRepresentative: '',
  winningDescription: '',
  exceptionDescription: '',
  attachments: [],
  publicityStartDate: '',
  publicityEndDate: ''
});

// 步骤配置
const stepsConfig = reactive([
  {
    title: '基本信息',
    name: 'step1'
  },
  {
    title: '中标人信息',
    name: 'step2'
  },
  {
    title: '详细说明',
    name: 'step3'
  }
]);

// 详情页头部配置
const detailHeadOption = reactive({
  title: computed(() => {
    return pageMode.value === 'create' ? '新建中标结果公示' : '编辑中标结果公示';
  }),
  btns: [
    {
      label: '保存',
      type: 'primary',
      on: {
        click: () => handleSave()
      }
    },
    {
      label: '返回',
      on: {
        click: () => handleBack()
      }
    }
  ]
});

// 基本信息表单配置
const basicFormSchema = reactive([
  {
    prop: 'title',
    label: '公告标题',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入公告标题'
    },
    span: 24
  },
  {
    prop: 'projectName',
    label: '项目名称',
    component: 'el-input',
    componentProps: {
      placeholder: '请选择项目',
      readonly: true
    },
    span: 12
  },
  {
    prop: 'sectionName',
    label: '标段名称',
    component: 'el-input',
    componentProps: {
      placeholder: '请选择标段',
      readonly: true
    },
    span: 12
  },
  {
    prop: 'ownerRepresentative',
    label: '业主代表',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入业主代表姓名'
    },
    span: 12
  },
  {
    prop: 'winningAmount',
    label: '中标金额(万元)',
    component: 'el-input-number',
    componentProps: {
      placeholder: '请输入中标金额',
      precision: 2,
      min: 0,
      style: { width: '100%' }
    },
    span: 12
  },
  {
    prop: 'publicityStartDate',
    label: '公示开始时间',
    component: 'el-date-picker',
    componentProps: {
      type: 'date',
      placeholder: '请选择公示开始时间',
      style: { width: '100%' }
    },
    span: 12
  },
  {
    prop: 'publicityEndDate',
    label: '公示结束时间',
    component: 'el-date-picker',
    componentProps: {
      type: 'date',
      placeholder: '请选择公示结束时间',
      style: { width: '100%' }
    },
    span: 12
  }
]);

// 详细说明表单配置
const descriptionFormSchema = reactive([
  {
    prop: 'winningDescription',
    label: '中标情况说明',
    component: 'funi-rich-text-editor',
    componentProps: {
      placeholder: '请输入中标情况说明',
      height: 200
    },
    span: 24
  },
  {
    prop: 'exceptionDescription',
    label: '异常情况说明',
    component: 'funi-rich-text-editor',
    componentProps: {
      placeholder: '请输入异常情况说明（如无异常可不填）',
      height: 200
    },
    span: 24
  }
]);

// 基本信息表单验证规则
const basicFormRules = reactive({
  title: [
    { required: true, message: '请输入公告标题', trigger: 'blur' }
  ],
  projectName: [
    { required: true, message: '请选择项目', trigger: 'blur' }
  ],
  sectionName: [
    { required: true, message: '请选择标段', trigger: 'blur' }
  ],
  ownerRepresentative: [
    { required: true, message: '请输入业主代表', trigger: 'blur' }
  ],
  winningAmount: [
    { required: true, message: '请输入中标金额', trigger: 'blur' }
  ],
  publicityStartDate: [
    { required: true, message: '请选择公示开始时间', trigger: 'change' }
  ],
  publicityEndDate: [
    { required: true, message: '请选择公示结束时间', trigger: 'change' }
  ]
});

// 添加中标人
const addWinner = () => {
  formData.winnerInfo.push({
    id: '',
    companyName: '',
    contactPerson: '',
    contactPhone: '',
    winningAmount: 0,
    winningRate: 0,
    rank: formData.winnerInfo.length + 1
  });
};

// 删除中标人
const removeWinner = (index) => {
  formData.winnerInfo.splice(index, 1);
  // 重新设置排名
  formData.winnerInfo.forEach((item, idx) => {
    item.rank = idx + 1;
  });
};

// 事件处理函数
const handleStepChange = (step) => {
  console.log('步骤切换:', step);
};

const handleSave = async () => {
  try {
    // 验证基本信息表单
    const basicValid = await basicFormRef.value.validate();
    if (!basicValid) {
      ElMessage.error('请完善基本信息');
      return;
    }

    // 验证中标人信息
    if (!formData.winnerInfo || formData.winnerInfo.length === 0) {
      ElMessage.error('请至少添加一个中标人');
      return;
    }

    // 保存数据
    if (pageMode.value === 'create') {
      await store.createAwardResultAnnouncement(formData);
      ElMessage.success('创建成功');
    } else {
      await store.updateAwardResultAnnouncement(route.params.id, formData);
      ElMessage.success('更新成功');
    }
    
    // 返回列表页
    handleBack();
  } catch (error) {
    ElMessage.error(pageMode.value === 'create' ? '创建失败' : '更新失败');
  }
};

const handleBack = () => {
  router.push({ name: 'AwardResultAnnouncementList' });
};

// 加载数据
const loadData = async () => {
  if (pageMode.value === 'edit' && route.params.id) {
    try {
      const data = await store.getAwardResultAnnouncementDetail(route.params.id);
      Object.assign(formData, data);
    } catch (error) {
      ElMessage.error('加载数据失败');
    }
  } else {
    // 新建模式，初始化一个中标人
    addWinner();
  }
};

// 组件挂载
onMounted(() => {
  loadData();
  console.log('中标结果公示新建/编辑页面已挂载，模式:', pageMode.value);
});
</script>

<style scoped>
.form-card {
  margin-bottom: 20px;
}
</style>
