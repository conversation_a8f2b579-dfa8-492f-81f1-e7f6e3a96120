/**
 * 中标结果公示管理 - 路由配置
 */

export default {
  path: 'award-result-announcement',
  name: 'AwardResultAnnouncement',
  meta: { 
    title: '中标结果公示管理', 
    isMenu: true 
  },
  children: [
    {
      path: 'list',
      name: 'AwardResultAnnouncementList',
      component: () => import('./views/list/index.vue'),
      meta: { 
        title: '中标结果公示列表' 
      }
    },
    {
      path: 'detail/:id',
      name: 'AwardResultAnnouncementDetail',
      component: () => import('./views/detail/index.vue'),
      meta: { 
        title: '中标结果公示详情' 
      }
    },
    {
      path: 'create',
      name: 'AwardResultAnnouncementCreate',
      component: () => import('./views/create/index.vue'),
      meta: { 
        title: '新建中标结果公示' 
      }
    },
    {
      path: 'edit/:id',
      name: 'AwardResultAnnouncementEdit',
      component: () => import('./views/create/index.vue'),
      meta: { 
        title: '编辑中标结果公示' 
      }
    },
    {
      path: 'audit/:id',
      name: 'AwardResultAnnouncementAudit',
      component: () => import('./views/audit/index.vue'),
      meta: { 
        title: '审核中标结果公示' 
      }
    }
  ]
};
