/**
 * 中标结果公示管理 - API层
 *
 * 模板说明：
 * - 标准的API接口封装模板
 * - 基于 window.$http 的统一请求方法
 * - 包含完整的CRUD操作接口
 *
 * 占位符说明：
 * - 中标结果公示管理: 模块中文名称
 * - 中标结果公示: 实体中文名称
 * - awardResultAnnouncement: 实体英文名称
 * - /api/award-result-announcements: API基础路径
 */
import { enableMock, mockAwardResultAnnouncementList } from '../__mocks__/index.js';

/**
 * 中标结果公示管理相关API接口
 */
export const awardResultAnnouncementApi = {
  /**
   * 获取中标结果公示列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页数量
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.status - 状态筛选
   * @param {string} params.projectName - 项目名称筛选
   * @param {number} params.auditStatus - 审核状态筛选
   * @param {number} params.publishStatus - 发布状态筛选
   * @returns {Promise} 返回中标结果公示列表数据
   */
  getAwardResultAnnouncementList(params = {}) {
    if (enableMock) {
      return { total: mockAwardResultAnnouncementList.length, list: mockAwardResultAnnouncementList };
    }
    return window.$http.post('/api/award-result-announcements/list', {
      page: params.page || 1,
      size: params.size || 10,
      keyword: params.keyword || '',
      status: params.status,
      projectName: params.projectName || '',
      auditStatus: params.auditStatus,
      publishStatus: params.publishStatus
    });
  },

  /**
   * 根据ID获取中标结果公示详情
   * @param {string|number} id - 中标结果公示ID
   * @returns {Promise} 返回中标结果公示详情数据
   */
  getAwardResultAnnouncementDetail(id) {
    if (enableMock) {
      return mockAwardResultAnnouncementList.find(item => item.id === id);
    }
    return window.$http.fetch(`/api/award-result-announcements/${id}`);
  },

  /**
   * 创建新中标结果公示
   * @param {Object} data - 中标结果公示数据
   * @param {string} data.title - 公告标题
   * @param {string} data.projectId - 项目ID
   * @param {string} data.sectionId - 标段ID
   * @param {Array} data.winnerInfo - 中标人信息
   * @param {number} data.winningAmount - 中标金额
   * @param {string} data.ownerRepresentative - 业主代表
   * @param {string} data.winningDescription - 中标情况说明
   * @param {string} data.exceptionDescription - 异常情况说明
   * @param {Array} data.attachments - 附件列表
   * @param {string} data.publicityStartDate - 公示开始时间
   * @param {string} data.publicityEndDate - 公示结束时间
   * @returns {Promise} 返回创建结果
   */
  createAwardResultAnnouncement(data) {
    if (enableMock) {
      return { id: Date.now(), ...data };
    }
    return window.$http.post('/api/award-result-announcements/create', {
      title: data.title,
      projectId: data.projectId,
      sectionId: data.sectionId,
      winnerInfo: data.winnerInfo || [],
      winningAmount: data.winningAmount,
      ownerRepresentative: data.ownerRepresentative,
      winningDescription: data.winningDescription || '',
      exceptionDescription: data.exceptionDescription || '',
      attachments: data.attachments || [],
      publicityStartDate: data.publicityStartDate,
      publicityEndDate: data.publicityEndDate
    });
  },

  /**
   * 更新中标结果公示信息
   * @param {string|number} id - 中标结果公示ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 返回更新结果
   */
  updateAwardResultAnnouncement(id, data) {
    if (enableMock) {
      const item = mockAwardResultAnnouncementList.find(item => item.id === id);
      if (item) {
        Object.assign(item, data);
        return item;
      }
      return null;
    }
    return window.$http.post(`/api/award-result-announcements/update/${id}`, {
      title: data.title,
      projectId: data.projectId,
      sectionId: data.sectionId,
      winnerInfo: data.winnerInfo,
      winningAmount: data.winningAmount,
      ownerRepresentative: data.ownerRepresentative,
      winningDescription: data.winningDescription,
      exceptionDescription: data.exceptionDescription,
      attachments: data.attachments,
      publicityStartDate: data.publicityStartDate,
      publicityEndDate: data.publicityEndDate
    });
  },

  /**
   * 删除中标结果公示
   * @param {string|number} id - 中标结果公示ID
   * @returns {Promise} 返回删除结果
   */
  deleteAwardResultAnnouncement(id) {
    if (enableMock) {
      const index = mockAwardResultAnnouncementList.findIndex(item => item.id === id);
      if (index > -1) {
        mockAwardResultAnnouncementList.splice(index, 1);
      }
      return true;
    }
    return window.$http.post(`/api/award-result-announcements/delete/${id}`);
  },

  /**
   * 提交审核
   * @param {string|number} id - 中标结果公示ID
   * @returns {Promise} 返回提交结果
   */
  submitAwardResultAnnouncement(id) {
    if (enableMock) {
      const item = mockAwardResultAnnouncementList.find(item => item.id === id);
      if (item) {
        item.auditStatus = 1; // 审核中
        return item;
      }
      return null;
    }
    return window.$http.post(`/api/award-result-announcements/submit/${id}`);
  },

  /**
   * 审核中标结果公示
   * @param {string|number} id - 中标结果公示ID
   * @param {Object} data - 审核数据
   * @param {number} data.auditResult - 审核结果 (1:通过, 2:驳回)
   * @param {string} data.auditComment - 审核意见
   * @returns {Promise} 返回审核结果
   */
  auditAwardResultAnnouncement(id, data) {
    if (enableMock) {
      const item = mockAwardResultAnnouncementList.find(item => item.id === id);
      if (item) {
        item.auditStatus = data.auditResult === 1 ? 2 : 3; // 2:审核通过, 3:审核驳回
        item.auditComment = data.auditComment;
        return item;
      }
      return null;
    }
    return window.$http.post(`/api/award-result-announcements/audit/${id}`, {
      auditResult: data.auditResult,
      auditComment: data.auditComment || ''
    });
  },

  /**
   * 发布中标结果公示
   * @param {string|number} id - 中标结果公示ID
   * @returns {Promise} 返回发布结果
   */
  publishAwardResultAnnouncement(id) {
    if (enableMock) {
      const item = mockAwardResultAnnouncementList.find(item => item.id === id);
      if (item) {
        item.publishStatus = 1; // 已发布
        item.publishTime = new Date().toISOString();
        return item;
      }
      return null;
    }
    return window.$http.post(`/api/award-result-announcements/publish/${id}`);
  },

  /**
   * 撤销中标结果公示
   * @param {string|number} id - 中标结果公示ID
   * @returns {Promise} 返回撤销结果
   */
  revokeAwardResultAnnouncement(id) {
    if (enableMock) {
      const item = mockAwardResultAnnouncementList.find(item => item.id === id);
      if (item) {
        item.publishStatus = 0; // 未发布
        item.publishTime = null;
        return item;
      }
      return null;
    }
    return window.$http.post(`/api/award-result-announcements/revoke/${id}`);
  }
};

// 默认导出
export default awardResultAnnouncementApi;
