/**
 * 中标结果公示管理 - Mock数据
 *
 * 用于开发和测试阶段的模拟数据
 */

export const mockAwardResultAnnouncementList = [
  {
    id: '1',
    title: '某某基础设施建设项目中标结果公示',
    projectId: 'project_001',
    projectName: '某某基础设施建设项目',
    sectionId: 'section_001',
    sectionName: '第一标段',
    winnerInfo: [
      {
        id: 'winner_001',
        companyName: '某某建设集团有限公司',
        contactPerson: '张经理',
        contactPhone: '13800138001',
        winningAmount: 12500000,
        winningRate: 0,
        rank: 1
      }
    ],
    winningAmount: 12500000,
    ownerRepresentative: '李主任',
    winningDescription: '<p>经评标委员会评审，某某建设集团有限公司为第一中标候选人，中标金额为1250万元。</p>',
    exceptionDescription: '<p>评标过程中无异常情况。</p>',
    attachments: [
      {
        id: 'file_001',
        name: '中标通知书.pdf',
        url: '/files/award_notice_001.pdf',
        size: 2048576,
        sizeText: '2.00 MB',
        type: 'pdf',
        uploadTime: '2024-01-25 10:00:00'
      }
    ],
    publicityStartDate: '2024-01-25',
    publicityEndDate: '2024-01-30',
    auditStatus: 2,
    auditStatusText: '审核通过',
    publishStatus: 1,
    publishStatusText: '已发布',
    publishTime: '2024-01-25 10:00:00',
    createTime: '2024-01-24 15:30:00',
    updateTime: '2024-01-25 10:00:00',
    createUser: '张三',
    updateUser: '李四'
  },
  {
    id: '2',
    title: '办公设备采购项目中标结果公示',
    projectId: 'project_002',
    projectName: '办公设备采购项目',
    sectionId: 'section_002',
    sectionName: '第一包',
    winnerInfo: [
      {
        id: 'winner_002',
        companyName: '某某科技有限公司',
        contactPerson: '王经理',
        contactPhone: '13800138002',
        winningAmount: 850000,
        winningRate: 0,
        rank: 1
      }
    ],
    winningAmount: 850000,
    ownerRepresentative: '赵主任',
    winningDescription: '<p>经评标委员会评审，某某科技有限公司为第一中标候选人，中标金额为85万元。</p>',
    exceptionDescription: '<p>评标过程中无异常情况。</p>',
    attachments: [
      {
        id: 'file_002',
        name: '中标通知书.pdf',
        url: '/files/award_notice_002.pdf',
        size: 1536000,
        sizeText: '1.50 MB',
        type: 'pdf',
        uploadTime: '2024-01-20 14:00:00'
      }
    ],
    publicityStartDate: '2024-01-20',
    publicityEndDate: '2024-01-25',
    auditStatus: 1,
    auditStatusText: '审核中',
    publishStatus: 0,
    publishStatusText: '未发布',
    publishTime: null,
    createTime: '2024-01-19 16:30:00',
    updateTime: '2024-01-20 09:15:00',
    createUser: '赵六',
    updateUser: '赵六'
  },
  {
    id: '3',
    title: '道路维修工程中标结果公示',
    projectId: 'project_003',
    projectName: '道路维修工程项目',
    sectionId: 'section_003',
    sectionName: '第一标段',
    winnerInfo: [
      {
        id: 'winner_003',
        companyName: '某某路桥工程公司',
        contactPerson: '孙经理',
        contactPhone: '13800138003',
        winningAmount: 3200000,
        winningRate: 0,
        rank: 1
      }
    ],
    winningAmount: 3200000,
    ownerRepresentative: '钱主任',
    winningDescription: '<p>经评标委员会评审，某某路桥工程公司为第一中标候选人，中标金额为320万元。</p>',
    exceptionDescription: '<p>评标过程中发现部分投标文件存在轻微格式问题，但不影响评标结果。</p>',
    attachments: [
      {
        id: 'file_003',
        name: '中标通知书.pdf',
        url: '/files/award_notice_003.pdf',
        size: 3072000,
        sizeText: '3.00 MB',
        type: 'pdf',
        uploadTime: '2024-01-18 09:00:00'
      },
      {
        id: 'file_004',
        name: '技术评审表.xlsx',
        url: '/files/technical_review_003.xlsx',
        size: 256000,
        sizeText: '250.00 KB',
        type: 'xlsx',
        uploadTime: '2024-01-17 15:35:00'
      }
    ],
    publicityStartDate: '2024-01-18',
    publicityEndDate: '2024-01-23',
    auditStatus: 2,
    auditStatusText: '审核通过',
    publishStatus: 0,
    publishStatusText: '未发布',
    publishTime: null,
    createTime: '2024-01-17 14:30:00',
    updateTime: '2024-01-19 11:20:00',
    createUser: '孙七',
    updateUser: '孙七'
  },
  {
    id: '4',
    title: '绿化工程项目中标结果公示',
    projectId: 'project_004',
    projectName: '绿化工程项目',
    sectionId: 'section_004',
    sectionName: '第一标段',
    winnerInfo: [
      {
        id: 'winner_004',
        companyName: '某某园林绿化公司',
        contactPerson: '周经理',
        contactPhone: '13800138004',
        winningAmount: 1800000,
        winningRate: 0,
        rank: 1
      }
    ],
    winningAmount: 1800000,
    ownerRepresentative: '吴主任',
    winningDescription: '<p>经评标委员会评审，某某园林绿化公司为第一中标候选人，中标金额为180万元。</p>',
    exceptionDescription: '<p>评标过程中无异常情况。</p>',
    attachments: [
      {
        id: 'file_005',
        name: '中标通知书.pdf',
        url: '/files/award_notice_004.pdf',
        size: 1024000,
        sizeText: '1.00 MB',
        type: 'pdf',
        uploadTime: '2024-01-15 16:00:00'
      }
    ],
    publicityStartDate: '2024-01-15',
    publicityEndDate: '2024-01-20',
    auditStatus: 0,
    auditStatusText: '待提交',
    publishStatus: 0,
    publishStatusText: '未发布',
    publishTime: null,
    createTime: '2024-01-15 14:30:00',
    updateTime: '2024-01-15 16:00:00',
    createUser: '郑八',
    updateUser: '郑八'
  }
];

export const mockAwardResultAnnouncementDetail = {
  id: '1',
  title: '某某基础设施建设项目中标结果公示',
  projectId: 'project_001',
  projectName: '某某基础设施建设项目',
  sectionId: 'section_001',
  sectionName: '第一标段',
  winnerInfo: [
    {
      id: 'winner_001',
      companyName: '某某建设集团有限公司',
      contactPerson: '张经理',
      contactPhone: '13800138001',
      winningAmount: 12500000,
      winningRate: 0,
      rank: 1
    }
  ],
  winningAmount: 12500000,
  ownerRepresentative: '李主任',
  winningDescription: '<p>经评标委员会评审，某某建设集团有限公司为第一中标候选人，中标金额为1250万元。该公司在技术方案、施工组织设计、企业资质等方面均表现优秀，完全满足招标文件要求。</p>',
  exceptionDescription: '<p>评标过程中无异常情况，所有程序均按照相关法规执行。</p>',
  attachments: [
    {
      id: 'file_001',
      name: '中标通知书.pdf',
      url: '/files/award_notice_001.pdf',
      size: 2048576,
      sizeText: '2.00 MB',
      type: 'pdf',
      uploadTime: '2024-01-25 10:00:00'
    },
    {
      id: 'file_002',
      name: '评标报告.pdf',
      url: '/files/evaluation_report_001.pdf',
      size: 3145728,
      sizeText: '3.00 MB',
      type: 'pdf',
      uploadTime: '2024-01-24 16:30:00'
    }
  ],
  publicityStartDate: '2024-01-25',
  publicityEndDate: '2024-01-30',
  auditStatus: 2,
  auditStatusText: '审核通过',
  publishStatus: 1,
  publishStatusText: '已发布',
  publishTime: '2024-01-25 10:00:00',
  createTime: '2024-01-24 15:30:00',
  updateTime: '2024-01-25 10:00:00',
  createUser: '张三',
  updateUser: '李四'
};

export const enableMock = true;

// 默认导出
export default {
  enableMock,
  mockAwardResultAnnouncementList,
  mockAwardResultAnnouncementDetail
};
