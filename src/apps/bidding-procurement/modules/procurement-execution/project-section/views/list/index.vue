<!-- 项目标段管理 - 列表页面 -->
<!--
  项目标段管理列表页面
  
  功能说明：
  - 基于 FuniListPageV2 组件的标准列表页面
  - 支持多页签配置和标准CRUD操作
  - 集成权限控制、搜索配置、数据脱敏等企业级功能
  - 支持动态按钮生成和完善的错误处理
-->
<template>
  <funi-list-page-v2 ref="listPageRef" :cardTab="cardTabConfig" @headBtnClick="handleHeadBtnClick" />
</template>

<script setup lang="jsx">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElNotification, ElMessageBox } from 'element-plus';
import { useProjectSectionStore } from '../../store.js';

// ==================== 基础变量配置 ====================
const listPageRef = ref();
const router = useRouter();
const projectSectionStore = useProjectSectionStore();

// ==================== 权限配置 ====================
/**
 * 权限配置对象
 * 根据实际业务需求配置对应的权限码
 */
const auths = reactive({
  export: 'project_section_export', // 导出权限
  add: 'project_section_add', // 新增权限
  delete: 'project_section_delete', // 删除权限
  audit: 'project_section_audit', // 审核权限
  edit: 'project_section_edit', // 编辑权限
  revocation: 'project_section_revocation', // 撤回权限
  detail: 'project_section_detail' // 详情权限
});

// ==================== 数据状态管理 ====================
// 列表查询参数存储
const listPageParams = ref({});

// ==================== 核心业务函数 ====================

/**
 * 跳转到详情页面
 * @param {Object} row - 当前行数据
 */
const goDetail = row => {
  router.push({
    path: '/bidding-procurement/procurement-execution/project-section/detail/' + row.id,
    query: {
      id: row.id,
      sectionName: row.sectionName
    }
  });
};

/**
 * 删除记录
 * @param {string|number} id - 记录ID
 */
const deleteItem = async id => {
  try {
    await projectSectionStore.deleteProjectSection(id);
    ElNotification({
      title: '提示',
      message: '删除成功',
      type: 'success',
      duration: 2000
    });
    listPageRef.value.reload({ resetPage: false });
  } catch (error) {
    console.error('删除失败:', error);
    ElNotification({
      title: '错误',
      message: '删除失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

/**
 * 跳转到编辑页面
 * @param {Object} row - 当前行数据
 */
const editItem = row => {
  router.push({
    path: '/bidding-procurement/procurement-execution/project-section/edit/' + row.id,
    query: {
      id: row.id,
      sectionName: row.sectionName,
      bizName: '编辑'
    }
  });
};

/**
 * 跳转到审核页面
 * @param {Object} row - 当前行数据
 */
const audit = row => {
  router.push({
    path: '/bidding-procurement/procurement-execution/project-section/audit/' + row.id,
    query: {
      id: row.id,
      sectionName: row.sectionName,
      bizName: '审核'
    }
  });
};

// 撤销功能（可选）
const revocation = row => {
  ElMessageBox.confirm('确认撤销该记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // 这里调用撤销API
      ElNotification({
        title: '提示',
        message: '撤销成功',
        type: 'success',
        duration: 2000
      });
      listPageRef.value.reload({ resetPage: false });
    })
    .catch(() => {
      // 用户取消操作
    });
};

// ==================== 动态按钮生成器 ====================

/**
 * 动态按钮生成器 - 集成版本
 */
const generateActionButtons = (row, btnFunctions = {}, authConfig = {}) => {
  // 按钮配置映射表 - 定义所有支持的按钮类型
  const buttonTypeConfig = {
    // 审核按钮 - 跳转到审核页面
    AUDIT_BUTTON: {
      component: (row, fn, auths) => (
        <el-link type="primary" v-auth={auths.audit} onClick={() => fn(row)} style={{ marginRight: '20px' }}>
          审核
        </el-link>
      )
    },

    // 撤回按钮 - 撤回当前记录
    REVOCATION: {
      component: (row, fn, auths) => (
        <el-link type="primary" v-auth={auths.revocation} onClick={() => fn(row)} style={{ marginRight: '20px' }}>
          撤回
        </el-link>
      )
    },

    // 编辑按钮 - 跳转到编辑页面
    EDIT_BUTTON: {
      component: (row, fn, auths) => (
        <el-link type="primary" v-auth={auths.edit} onClick={() => fn(row)} style={{ marginRight: '20px' }}>
          编辑
        </el-link>
      )
    },

    // 删除按钮 - 带确认弹窗的删除操作
    DELETE_BUTTON: {
      component: (row, fn, auths) => (
        <el-popconfirm title="是否删除?" onConfirm={() => fn(row.id)}>
          {{
            reference: () => (
              <el-link type="primary" v-auth={auths.delete}>
                删除
              </el-link>
            )
          }}
        </el-popconfirm>
      )
    },

    // 详情按钮 - 跳转到详情页面
    DETAIL_BUTTON: {
      component: (row, fn, auths) => (
        <el-link type="primary" v-auth={auths.detail} onClick={() => fn(row)} style={{ marginRight: '20px' }}>
          详情
        </el-link>
      )
    }
  };

  // 数据验证：检查 row.buttonList 是否存在且为数组
  if (!row.buttonList || !Array.isArray(row.buttonList)) {
    return <span>--</span>;
  }

  // 动态渲染按钮：根据 buttonList 配置生成对应按钮
  return (
    <>
      {row.buttonList.map((buttonKey, index) => {
        const buttonConfig = buttonTypeConfig[buttonKey];
        const buttonFunction = btnFunctions[buttonKey];

        // 安全检查：如果按钮配置或处理函数不存在，跳过该按钮
        if (!buttonConfig || !buttonFunction) {
          console.warn(`按钮配置缺失: ${buttonKey}`);
          return null;
        }

        return <span key={`${buttonKey}-${index}`}>{buttonConfig.component(row, buttonFunction, authConfig)}</span>;
      })}
    </>
  );
};

// 表格列配置
const columns = reactive([
  {
    label: '标段名称',
    prop: 'sectionName',
    fixed: 'left',
    render: ({ row }) => {
      return (
        <el-link type="primary" onClick={() => goDetail(row)}>
          {row.sectionName || '--'}
        </el-link>
      );
    }
  },
  {
    label: '标段金额',
    prop: 'sectionAmount',
    render: ({ row }) => <span>{row.sectionAmount ? `¥${row.sectionAmount.toLocaleString()}` : '--'}</span>
  },
  {
    label: '项目计划',
    prop: 'projectPlanId'
  },
  {
    label: '审核状态',
    prop: 'auditStatus',
    render: ({ row }) => (
      <el-tag type={row.auditStatus === 'approved' ? 'success' : row.auditStatus === 'rejected' ? 'danger' : 'warning'}>
        {row.auditStatus === 'approved' ? '已通过' : row.auditStatus === 'rejected' ? '已驳回' : '待审核'}
      </el-tag>
    )
  },
  {
    label: '创建时间',
    prop: 'createTime'
  },
  {
    label: '操作',
    prop: 'actions',
    width: 200,
    fixed: 'right',
    render: ({ row }) => {
      return (
        <div>
          {generateActionButtons(
            row,
            {
              AUDIT_BUTTON: audit,
              REVOCATION: revocation,
              EDIT_BUTTON: editItem,
              DELETE_BUTTON: deleteItem,
              DETAIL_BUTTON: goDetail
            },
            auths
          )}
        </div>
      );
    }
  }
]);

// 数据加载函数
const loadData = async (pageParams, searchParams) => {
  listPageParams.value = searchParams;
  return await projectSectionStore.fetchProjectSectionList({
    ...pageParams,
    ...searchParams
  });
};

const loadDataWithHandle = async (pageParams, searchParams, handle) => {
  listPageParams.value = searchParams;
  return await projectSectionStore.fetchProjectSectionList({
    ...pageParams,
    ...searchParams,
    handle
  });
};

// 页签配置
const cardTabConfig = computed(() => {
  return [
    {
      label: '待办件',
      key: 'pending',
      curdOption: {
        columns,
        lodaData: (pageParams, searchParams) => loadDataWithHandle(pageParams, searchParams, 0),
        btns: [
          { key: 'add', label: '新增', type: 'primary' },
          { key: 'export', label: '导出' }
        ],
        fixedButtons: true,
        reloadOnActive: true,
        requestParams: { handle: 0 }
      },
      searchConfig: { pageCode: 'project_section_list' }
    },
    {
      label: '已办件',
      key: 'completed',
      curdOption: {
        columns: columns.slice(0, -1), // 移除操作列
        lodaData: (pageParams, searchParams) => loadDataWithHandle(pageParams, searchParams, 1),
        btns: [{ key: 'exportCompleted', label: '导出', auth: auths.export }],
        fixedButtons: true,
        reloadOnActive: true,
        requestParams: { handle: 1 }
      },
      searchConfig: { pageCode: 'project_section_list' }
    }
  ];
});

// 新增功能
const addItem = () => {
  router.push({
    path: '/bidding-procurement/procurement-execution/project-section/create',
    query: {
      bizName: '新增'
    }
  });
};

// 头部按钮点击事件处理
const handleHeadBtnClick = key => {
  switch (key) {
    case 'add':
      addItem();
      break;
    case 'export':
      // exportPending();
      break;
    case 'exportCompleted':
      // exportCompleted();
      break;
    default:
      break;
  }
};

// ==================== 生命周期钩子 ====================

/**
 * 组件挂载完成
 * 可在此处执行初始化逻辑
 */
onMounted(() => {
  console.log('项目标段管理列表页面已挂载');
  // 可选：执行初始化数据加载
  // listPageRef.value?.reload();
});
</script>
