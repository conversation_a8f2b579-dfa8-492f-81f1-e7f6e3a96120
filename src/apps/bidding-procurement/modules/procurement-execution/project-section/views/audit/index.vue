<!-- 项目标段管理 - 审核页面 -->
<!--
  项目标段审核页面
  
  功能说明：
  - 基于 FuniDetail 组件的工作流页面
  - 支持多步骤流程和工作流组件集成
  - 包含附件上传、审核按钮等工作流特性
-->
<template>
  <funi-detail 
    :steps="stepsConfig"
    :detailHeadOption="detailHeadOption"
    @onStepChange="handleStepChange"
  >
    <!-- 第一步：基础信息 -->
    <template #step1>
      <funi-form
        ref="formRef"
        :schema="formSchema"
        :model="formData"
        :rules="formRules"
        :disabled="true"
        label-width="120px"
      />
    </template>

    <!-- 第二步：附件查看 -->
    <template #step2>
      <funi-file-table
        v-if="businessId"
        :businessId="businessId"
        :readonly="true"
        fileType="workflow"
      />
      <div v-else class="no-business-tip">
        <el-alert
          title="暂无相关附件"
          type="info"
          :closable="false"
        />
      </div>
    </template>

    <!-- 第三步：审核信息 -->
    <template #step3>
      <div class="audit-section">
        <el-card>
          <template #header>
            <span>项目标段信息确认</span>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="标段名称">
              {{ formData.sectionName }}
            </el-descriptions-item>
            <el-descriptions-item label="标段金额">
              ¥{{ formData.sectionAmount?.toLocaleString() }}
            </el-descriptions-item>
            <el-descriptions-item label="项目计划">
              {{ formData.projectPlanId }}
            </el-descriptions-item>
            <el-descriptions-item label="当前状态">
              <el-tag :type="getAuditStatusType(formData.auditStatus)">
                {{ getAuditStatusText(formData.auditStatus) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="标段描述" :span="2">
              {{ formData.sectionDescription || '无' }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <el-card style="margin-top: 20px;">
          <template #header>
            <span>审核操作</span>
          </template>
          <funi-form
            ref="auditFormRef"
            :schema="auditFormSchema"
            :model="auditFormData"
            :rules="auditFormRules"
            label-width="120px"
          />
        </el-card>

        <el-card style="margin-top: 20px;">
          <template #header>
            <span>审核历史</span>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in auditHistory"
              :key="index"
              :timestamp="item.timestamp"
              placement="top"
              :type="getTimelineType(item.result)"
            >
              <el-card>
                <h4>{{ item.operation }}</h4>
                <p>审核人：{{ item.auditor }}</p>
                <p>审核结果：
                  <el-tag :type="getAuditStatusType(item.result)">
                    {{ getAuditStatusText(item.result) }}
                  </el-tag>
                </p>
                <p>审核意见：{{ item.comment || '无' }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </div>
    </template>
  </funi-detail>

  <!-- 工作流审核按钮 -->
  <funi-audit-buttom-btn
    v-if="businessId && isWorkflowMode"
    :businessId="businessId"
    :processKey="processKey"
    @onAudit="handleAudit"
  />

  <!-- 审核抽屉 -->
  <funi-bus-audit-drawer
    v-model="auditDrawerVisible"
    :businessId="businessId"
    @onSubmit="handleAuditSubmit"
  />
</template>

<script setup lang="jsx">
import { useRouter, useRoute } from 'vue-router';
import { reactive, ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { useProjectSectionStore } from '../../store.js';

// 路由和Store
const router = useRouter();
const route = useRoute();
const projectSectionStore = useProjectSectionStore();

// 表单引用
const formRef = ref(null);
const auditFormRef = ref(null);

// 业务ID
const businessId = computed(() => route.params.id);

// 工作流流程标识
const processKey = 'project_section_audit';

// 是否为工作流模式
const isWorkflowMode = computed(() => true);

// 审核抽屉显示状态
const auditDrawerVisible = ref(false);

// 表单数据
const formData = reactive({
  sectionName: '',
  sectionAmount: 0,
  sectionDescription: '',
  projectPlanId: '',
  auditStatus: '',
  auditTime: '',
  auditUser: '',
  createTime: '',
  updateTime: ''
});

// 审核表单数据
const auditFormData = reactive({
  auditResult: '',
  auditComment: '',
  auditAttachment: []
});

// 审核历史
const auditHistory = ref([
  {
    timestamp: '2024-01-15 10:30:00',
    operation: '提交审核',
    auditor: '张三',
    result: 'pending',
    comment: '提交项目标段审核申请'
  },
  {
    timestamp: '2024-01-16 14:20:00',
    operation: '初审',
    auditor: '李四',
    result: 'approved',
    comment: '初审通过，材料齐全'
  }
]);

// 步骤配置
const stepsConfig = reactive([
  {
    title: '标段信息',
    name: 'step1'
  },
  {
    title: '相关附件',
    name: 'step2'
  },
  {
    title: '审核操作',
    name: 'step3'
  }
]);

// 详情页头部配置
const detailHeadOption = reactive({
  title: '审核项目标段',
  btns: [
    {
      label: '通过',
      type: 'success',
      on: {
        click: () => handleQuickAudit('approved')
      }
    },
    {
      label: '驳回',
      type: 'danger',
      on: {
        click: () => handleQuickAudit('rejected')
      }
    },
    {
      label: '返回',
      on: {
        click: () => handleBack()
      }
    }
  ]
});

// 表单配置
const formSchema = reactive([
  {
    prop: 'sectionName',
    label: '标段名称',
    component: 'el-input',
    componentProps: {
      disabled: true
    },
    span: 12
  },
  {
    prop: 'sectionAmount',
    label: '标段金额',
    component: 'el-input-number',
    componentProps: {
      disabled: true,
      precision: 2,
      controls: false,
      style: { width: '100%' }
    },
    span: 12
  },
  {
    prop: 'projectPlanId',
    label: '项目计划',
    component: 'el-input',
    componentProps: {
      disabled: true
    },
    span: 12
  },
  {
    prop: 'auditStatus',
    label: '当前状态',
    component: 'el-input',
    componentProps: {
      disabled: true
    },
    span: 12
  },
  {
    prop: 'sectionDescription',
    label: '标段描述',
    component: 'el-input',
    componentProps: {
      type: 'textarea',
      rows: 4,
      disabled: true
    },
    span: 24
  }
]);

// 审核表单配置
const auditFormSchema = reactive([
  {
    prop: 'auditResult',
    label: '审核结果',
    component: 'el-select',
    componentProps: {
      placeholder: '请选择审核结果',
      options: [
        { label: '通过', value: 'approved' },
        { label: '驳回', value: 'rejected' }
      ]
    },
    span: 12
  },
  {
    prop: 'auditComment',
    label: '审核意见',
    component: 'el-input',
    componentProps: {
      type: 'textarea',
      rows: 4,
      placeholder: '请输入审核意见'
    },
    span: 24
  }
]);

// 审核表单验证规则
const auditFormRules = reactive({
  auditResult: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  auditComment: [
    { required: true, message: '请输入审核意见', trigger: 'blur' },
    { min: 5, max: 500, message: '审核意见长度在 5 到 500 个字符', trigger: 'blur' }
  ]
});

// 表单验证规则
const formRules = reactive({});

// 获取审核状态类型
const getAuditStatusType = (status) => {
  const typeMap = {
    'approved': 'success',
    'rejected': 'danger',
    'pending': 'warning'
  };
  return typeMap[status] || 'info';
};

// 获取审核状态文本
const getAuditStatusText = (status) => {
  const textMap = {
    'approved': '已通过',
    'rejected': '已驳回',
    'pending': '待审核'
  };
  return textMap[status] || '未知';
};

// 获取时间线类型
const getTimelineType = (result) => {
  const typeMap = {
    'approved': 'success',
    'rejected': 'danger',
    'pending': 'primary'
  };
  return typeMap[result] || 'primary';
};

// 事件处理函数
const handleStepChange = (step) => {
  console.log('步骤切换:', step);
};

const handleQuickAudit = async (result) => {
  try {
    // 设置审核结果
    auditFormData.auditResult = result;
    
    // 如果没有填写审核意见，提供默认意见
    if (!auditFormData.auditComment) {
      auditFormData.auditComment = result === 'approved' ? '审核通过' : '审核不通过，请修改后重新提交';
    }
    
    // 提交审核
    await handleAuditSubmit();
  } catch (error) {
    console.error('快速审核失败:', error);
  }
};

const handleAudit = () => {
  auditDrawerVisible.value = true;
};

const handleAuditSubmit = async () => {
  try {
    // 验证审核表单
    const valid = await auditFormRef.value.validate();
    if (!valid) return;

    // 提交审核
    await projectSectionStore.auditProjectSection(route.params.id, auditFormData);
    
    ElMessage.success('审核提交成功');
    
    // 关闭抽屉
    auditDrawerVisible.value = false;
    
    // 返回列表页
    handleBack();
  } catch (error) {
    console.error('审核提交失败:', error);
    ElMessage.error('审核提交失败，请重试');
  }
};

const handleBack = () => {
  router.push('/bidding-procurement/procurement-execution/project-section/list');
};

// 加载数据
const loadData = async () => {
  if (route.params.id) {
    try {
      const data = await projectSectionStore.fetchProjectSectionDetail(route.params.id);
      Object.assign(formData, data);
    } catch (error) {
      ElMessage.error('加载数据失败');
    }
  }
};

// 组件挂载
onMounted(() => {
  loadData();
  console.log('项目标段管理审核页面已挂载');
});
</script>

<style scoped>
.audit-section {
  margin-top: 20px;
}

.no-business-tip {
  padding: 20px;
  text-align: center;
}

:deep(.el-card__header) {
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-timeline-item__content) {
  padding-bottom: 20px;
}
</style>
