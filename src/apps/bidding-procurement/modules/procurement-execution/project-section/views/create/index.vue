<!-- 项目标段管理 - 新建页面 -->
<!--
  项目标段新建页面
  
  功能说明：
  - 基于 FuniDetail 组件的新建页面
  - 支持表单验证和数据提交
  - 集成文件上传功能
-->
<template>
  <funi-detail 
    :steps="stepsConfig"
    :detailHeadOption="detailHeadOption"
    @onStepChange="handleStepChange"
  >
    <template #step1>
      <funi-form
        ref="formRef"
        :schema="formSchema"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      />
    </template>
    
    <template #step2>
      <div class="file-upload-section">
        <el-card>
          <template #header>
            <span>相关附件</span>
          </template>
          <funi-file-table
            v-if="businessId"
            :businessId="businessId"
            :readonly="false"
            fileType="normal"
          />
          <div v-else class="no-business-tip">
            <el-alert
              title="请先保存基础信息后再上传附件"
              type="warning"
              :closable="false"
            />
          </div>
        </el-card>
      </div>
    </template>
  </funi-detail>
</template>

<script setup lang="jsx">
import { useRouter, useRoute } from 'vue-router';
import { reactive, ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { useProjectSectionStore } from '../../store.js';

// 路由和Store
const router = useRouter();
const route = useRoute();
const projectSectionStore = useProjectSectionStore();

// 表单引用
const formRef = ref(null);

// 业务ID（用于文件上传）
const businessId = ref('');

// 表单数据
const formData = reactive({
  sectionName: '',
  sectionAmount: 0,
  sectionDescription: '',
  projectPlanId: ''
});

// 步骤配置
const stepsConfig = reactive([
  {
    title: '基本信息',
    name: 'step1'
  },
  {
    title: '附件上传',
    name: 'step2'
  }
]);

// 详情页头部配置
const detailHeadOption = reactive({
  title: '新建项目标段',
  btns: [
    {
      label: '保存',
      type: 'primary',
      on: {
        click: () => handleSave()
      }
    },
    {
      label: '保存并继续',
      type: 'success',
      on: {
        click: () => handleSaveAndContinue()
      }
    },
    {
      label: '返回',
      on: {
        click: () => handleBack()
      }
    }
  ]
});

// 表单配置
const formSchema = reactive([
  {
    prop: 'sectionName',
    label: '标段名称',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入标段名称',
      clearable: true
    },
    span: 12
  },
  {
    prop: 'sectionAmount',
    label: '标段金额',
    component: 'el-input-number',
    componentProps: {
      placeholder: '请输入标段金额',
      min: 0,
      precision: 2,
      controls: false,
      style: { width: '100%' }
    },
    span: 12
  },
  {
    prop: 'projectPlanId',
    label: '项目计划',
    component: 'el-select',
    componentProps: {
      placeholder: '请选择项目计划',
      filterable: true,
      clearable: true,
      options: [
        // 这里应该从API获取项目计划列表
        { label: '示例项目计划1', value: 'plan1' },
        { label: '示例项目计划2', value: 'plan2' }
      ]
    },
    span: 12
  },
  {
    prop: 'sectionDescription',
    label: '标段描述',
    component: 'el-input',
    componentProps: {
      type: 'textarea',
      rows: 4,
      placeholder: '请输入标段描述',
      clearable: true
    },
    span: 24
  }
]);

// 表单验证规则
const formRules = reactive({
  sectionName: [
    { required: true, message: '请输入标段名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  sectionAmount: [
    { required: true, message: '请输入标段金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '标段金额必须大于等于0', trigger: 'blur' }
  ],
  projectPlanId: [
    { required: true, message: '请选择项目计划', trigger: 'change' }
  ]
});

// 事件处理函数
const handleStepChange = (step) => {
  console.log('步骤切换:', step);
};

const handleSave = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) return;

    // 保存数据
    const result = await projectSectionStore.createProjectSection(formData);
    
    // 设置业务ID用于文件上传
    if (result && result.id) {
      businessId.value = result.id;
    }
    
    ElMessage.success('创建成功');
    
    // 返回列表页
    handleBack();
  } catch (error) {
    console.error('创建失败:', error);
    ElMessage.error('创建失败，请重试');
  }
};

const handleSaveAndContinue = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) return;

    // 保存数据
    const result = await projectSectionStore.createProjectSection(formData);
    
    // 设置业务ID用于文件上传
    if (result && result.id) {
      businessId.value = result.id;
    }
    
    ElMessage.success('保存成功，可以继续上传附件');
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败，请重试');
  }
};

const handleBack = () => {
  router.push('/bidding-procurement/procurement-execution/project-section/list');
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    sectionName: '',
    sectionAmount: 0,
    sectionDescription: '',
    projectPlanId: ''
  });
  businessId.value = '';
  formRef.value?.resetFields();
};

// 组件挂载
onMounted(() => {
  console.log('项目标段管理新建页面已挂载');
  // 可以在这里加载项目计划选项等初始化数据
});
</script>

<style scoped>
.file-upload-section {
  margin-top: 20px;
}

.no-business-tip {
  padding: 20px;
  text-align: center;
}

:deep(.el-card__header) {
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}
</style>
