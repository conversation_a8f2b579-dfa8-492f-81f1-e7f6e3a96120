<!-- 项目标段管理 - 编辑页面 -->
<!--
  项目标段编辑页面
  
  功能说明：
  - 基于 FuniDetail 组件的编辑页面
  - 支持数据回填和更新
  - 包含编辑限制和历史记录
-->
<template>
  <funi-detail 
    :steps="stepsConfig"
    :detailHeadOption="detailHeadOption"
    @onStepChange="handleStepChange"
  >
    <template #step1>
      <funi-form
        ref="formRef"
        :schema="formSchema"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      />
    </template>
    
    <template #step2>
      <div class="file-upload-section">
        <el-card>
          <template #header>
            <span>相关附件</span>
          </template>
          <funi-file-table
            :businessId="businessId"
            :readonly="isReadonly"
            fileType="normal"
          />
        </el-card>
      </div>
    </template>
    
    <template #step3>
      <div class="history-section">
        <el-card>
          <template #header>
            <span>编辑历史</span>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in editHistory"
              :key="index"
              :timestamp="item.timestamp"
              placement="top"
            >
              <el-card>
                <h4>{{ item.operation }}</h4>
                <p>操作人：{{ item.operator }}</p>
                <p>变更内容：{{ item.changes }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </div>
    </template>
  </funi-detail>
</template>

<script setup lang="jsx">
import { useRouter, useRoute } from 'vue-router';
import { reactive, ref, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useProjectSectionStore } from '../../store.js';

// 路由和Store
const router = useRouter();
const route = useRoute();
const projectSectionStore = useProjectSectionStore();

// 表单引用
const formRef = ref(null);

// 业务ID
const businessId = computed(() => route.params.id);

// 原始数据（用于对比变更）
const originalData = ref({});

// 编辑历史
const editHistory = ref([
  {
    timestamp: '2024-01-15 10:30:00',
    operation: '创建记录',
    operator: '张三',
    changes: '创建了新的项目标段记录'
  },
  {
    timestamp: '2024-01-16 14:20:00',
    operation: '修改标段金额',
    operator: '李四',
    changes: '标段金额从 1000000 修改为 1200000'
  }
]);

// 是否只读（已审核通过的数据部分字段不可编辑）
const isReadonly = computed(() => {
  return formData.auditStatus === 'approved';
});

// 表单数据
const formData = reactive({
  sectionName: '',
  sectionAmount: 0,
  sectionDescription: '',
  projectPlanId: '',
  auditStatus: '',
  auditTime: '',
  auditUser: '',
  createTime: '',
  updateTime: ''
});

// 步骤配置
const stepsConfig = reactive([
  {
    title: '基本信息',
    name: 'step1'
  },
  {
    title: '附件管理',
    name: 'step2'
  },
  {
    title: '编辑历史',
    name: 'step3'
  }
]);

// 详情页头部配置
const detailHeadOption = reactive({
  title: '编辑项目标段',
  btns: computed(() => {
    const btns = [];
    
    if (!isReadonly.value) {
      btns.push({
        label: '保存',
        type: 'primary',
        on: {
          click: () => handleSave()
        }
      });
    }
    
    btns.push({
      label: '返回',
      on: {
        click: () => handleBack()
      }
    });
    
    return btns;
  })
});

// 表单配置
const formSchema = reactive([
  {
    prop: 'sectionName',
    label: '标段名称',
    component: 'el-input',
    componentProps: computed(() => ({
      placeholder: '请输入标段名称',
      clearable: true,
      disabled: isReadonly.value
    })),
    span: 12
  },
  {
    prop: 'sectionAmount',
    label: '标段金额',
    component: 'el-input-number',
    componentProps: computed(() => ({
      placeholder: '请输入标段金额',
      min: 0,
      precision: 2,
      controls: false,
      style: { width: '100%' },
      disabled: isReadonly.value
    })),
    span: 12
  },
  {
    prop: 'projectPlanId',
    label: '项目计划',
    component: 'el-select',
    componentProps: computed(() => ({
      placeholder: '请选择项目计划',
      filterable: true,
      clearable: true,
      disabled: true, // 项目计划一般不允许修改
      options: [
        { label: '示例项目计划1', value: 'plan1' },
        { label: '示例项目计划2', value: 'plan2' }
      ]
    })),
    span: 12
  },
  {
    prop: 'auditStatus',
    label: '审核状态',
    component: 'el-select',
    componentProps: {
      placeholder: '审核状态',
      disabled: true,
      options: [
        { label: '待审核', value: 'pending' },
        { label: '已通过', value: 'approved' },
        { label: '已驳回', value: 'rejected' }
      ]
    },
    span: 12
  },
  {
    prop: 'sectionDescription',
    label: '标段描述',
    component: 'el-input',
    componentProps: computed(() => ({
      type: 'textarea',
      rows: 4,
      placeholder: '请输入标段描述',
      clearable: true,
      disabled: isReadonly.value
    })),
    span: 24
  }
]);

// 表单验证规则
const formRules = reactive({
  sectionName: [
    { required: true, message: '请输入标段名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  sectionAmount: [
    { required: true, message: '请输入标段金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '标段金额必须大于等于0', trigger: 'blur' }
  ],
  projectPlanId: [
    { required: true, message: '请选择项目计划', trigger: 'change' }
  ]
});

// 事件处理函数
const handleStepChange = (step) => {
  console.log('步骤切换:', step);
};

const handleSave = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) return;

    // 检查数据变更
    const changes = getDataChanges();
    if (changes.length === 0) {
      ElMessage.info('数据未发生变更');
      return;
    }

    // 确认更新
    const confirmResult = await ElMessageBox.confirm(
      `检测到以下变更：\n${changes.join('\n')}\n\n确认保存吗？`,
      '确认更新',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    if (confirmResult !== 'confirm') return;

    // 保存数据
    await projectSectionStore.updateProjectSection(route.params.id, formData);
    ElMessage.success('更新成功');
    
    // 返回列表页
    handleBack();
  } catch (error) {
    console.error('更新失败:', error);
    ElMessage.error('更新失败，请重试');
  }
};

const handleBack = () => {
  router.push('/bidding-procurement/procurement-execution/project-section/list');
};

// 获取数据变更
const getDataChanges = () => {
  const changes = [];
  const compareFields = [
    { key: 'sectionName', label: '标段名称' },
    { key: 'sectionAmount', label: '标段金额' },
    { key: 'sectionDescription', label: '标段描述' }
  ];

  compareFields.forEach(field => {
    const originalValue = originalData.value[field.key];
    const currentValue = formData[field.key];
    
    if (originalValue !== currentValue) {
      changes.push(`${field.label}: ${originalValue} → ${currentValue}`);
    }
  });

  return changes;
};

// 加载数据
const loadData = async () => {
  if (route.params.id) {
    try {
      const data = await projectSectionStore.fetchProjectSectionDetail(route.params.id);
      Object.assign(formData, data);
      
      // 保存原始数据用于对比
      originalData.value = { ...data };
    } catch (error) {
      ElMessage.error('加载数据失败');
    }
  }
};

// 组件挂载
onMounted(() => {
  loadData();
  console.log('项目标段管理编辑页面已挂载');
});
</script>

<style scoped>
.file-upload-section,
.history-section {
  margin-top: 20px;
}

:deep(.el-card__header) {
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-timeline-item__content) {
  padding-bottom: 20px;
}

.readonly-tip {
  margin-bottom: 20px;
}
</style>
