<!-- 项目标段管理 - 详情页面 -->
<!--
  项目标段详情页面
  
  功能说明：
  - 基于 FuniDetail 组件的标准详情页面
  - 支持查看、编辑、新建三种模式
  - 集成 FuniForm 表单组件和标准操作流程
-->
<template>
  <funi-detail 
    :steps="stepsConfig"
    :detailHeadOption="detailHeadOption"
    @onStepChange="handleStepChange"
  >
    <template #step1>
      <funi-form
        ref="formRef"
        :schema="formSchema"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      />
    </template>
    
    <template #step2>
      <div class="section-tabs">
        <el-tabs v-model="activeTab" type="card">
          <el-tab-pane label="标段信息" name="section">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="标段名称">
                {{ formData.sectionName }}
              </el-descriptions-item>
              <el-descriptions-item label="标段金额">
                ¥{{ formData.sectionAmount?.toLocaleString() }}
              </el-descriptions-item>
              <el-descriptions-item label="项目计划ID">
                {{ formData.projectPlanId }}
              </el-descriptions-item>
              <el-descriptions-item label="审核状态">
                <el-tag :type="getAuditStatusType(formData.auditStatus)">
                  {{ getAuditStatusText(formData.auditStatus) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="标段描述" :span="2">
                {{ formData.sectionDescription || '无' }}
              </el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
          
          <el-tab-pane label="交易公告" name="announcement">
            <div class="tab-content">
              <el-empty description="暂无交易公告信息" />
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="补遗澄清答疑" name="clarification">
            <div class="tab-content">
              <el-empty description="暂无补遗澄清答疑信息" />
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="评标结果公示" name="bid-result">
            <div class="tab-content">
              <el-empty description="暂无评标结果公示信息" />
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="中标结果公示" name="award-result">
            <div class="tab-content">
              <el-empty description="暂无中标结果公示信息" />
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="签约履行" name="contract">
            <div class="tab-content">
              <el-empty description="暂无签约履行信息" />
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="流标或中止" name="failure">
            <div class="tab-content">
              <el-empty description="暂无流标或中止信息" />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </template>
  </funi-detail>
</template>

<script setup lang="jsx">
import { useRouter, useRoute } from 'vue-router';
import { reactive, ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { useProjectSectionStore } from '../../store.js';

// 路由和Store
const router = useRouter();
const route = useRoute();
const projectSectionStore = useProjectSectionStore();

// 表单引用
const formRef = ref(null);

// 当前活跃的标签页
const activeTab = ref('section');

// 页面模式
const pageMode = computed(() => {
  if (route.path.includes('/create')) return 'create';
  if (route.query.mode === 'edit') return 'edit';
  return 'view';
});

// 是否为编辑模式
const isEditMode = computed(() => pageMode.value !== 'view');

// 表单数据
const formData = reactive({
  sectionName: '',
  sectionAmount: 0,
  sectionDescription: '',
  projectPlanId: '',
  auditStatus: '',
  auditTime: '',
  auditUser: '',
  createTime: '',
  updateTime: ''
});

// 步骤配置
const stepsConfig = reactive([
  {
    title: pageMode.value === 'create' ? '新建项目标段' : '项目标段信息',
    name: 'step1'
  },
  {
    title: '详细信息',
    name: 'step2'
  }
]);

// 详情页头部配置
const detailHeadOption = reactive({
  title: computed(() => {
    if (pageMode.value === 'create') return '新建项目标段';
    if (pageMode.value === 'edit') return '编辑项目标段';
    return '项目标段详情';
  }),
  btns: computed(() => {
    const btns = [];
    
    if (pageMode.value === 'view') {
      btns.push({
        label: '编辑',
        type: 'primary',
        auth: 'project_section:edit',
        on: {
          click: () => handleEdit()
        }
      });
    }
    
    if (isEditMode.value) {
      btns.push({
        label: '保存',
        type: 'primary',
        on: {
          click: () => handleSave()
        }
      });
    }
    
    btns.push({
      label: '返回',
      on: {
        click: () => handleBack()
      }
    });
    
    return btns;
  })
});

// 表单配置
const formSchema = reactive([
  {
    prop: 'sectionName',
    label: '标段名称',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入标段名称',
      disabled: !isEditMode.value
    },
    span: 12
  },
  {
    prop: 'sectionAmount',
    label: '标段金额',
    component: 'el-input-number',
    componentProps: {
      placeholder: '请输入标段金额',
      disabled: !isEditMode.value,
      min: 0,
      precision: 2,
      controls: false,
      style: { width: '100%' }
    },
    span: 12
  },
  {
    prop: 'projectPlanId',
    label: '项目计划',
    component: 'el-select',
    componentProps: {
      placeholder: '请选择项目计划',
      disabled: !isEditMode.value,
      filterable: true,
      options: []
    },
    span: 12
  },
  {
    prop: 'auditStatus',
    label: '审核状态',
    component: 'el-select',
    componentProps: {
      placeholder: '请选择审核状态',
      disabled: true,
      options: [
        { label: '待审核', value: 'pending' },
        { label: '已通过', value: 'approved' },
        { label: '已驳回', value: 'rejected' }
      ]
    },
    span: 12
  },
  {
    prop: 'sectionDescription',
    label: '标段描述',
    component: 'el-input',
    componentProps: {
      type: 'textarea',
      rows: 4,
      placeholder: '请输入标段描述',
      disabled: !isEditMode.value
    },
    span: 24
  }
]);

// 表单验证规则
const formRules = reactive({
  sectionName: [
    { required: true, message: '请输入标段名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  sectionAmount: [
    { required: true, message: '请输入标段金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '标段金额必须大于等于0', trigger: 'blur' }
  ],
  projectPlanId: [
    { required: true, message: '请选择项目计划', trigger: 'change' }
  ]
});

// 获取审核状态类型
const getAuditStatusType = (status) => {
  const typeMap = {
    'approved': 'success',
    'rejected': 'danger',
    'pending': 'warning'
  };
  return typeMap[status] || 'info';
};

// 获取审核状态文本
const getAuditStatusText = (status) => {
  const textMap = {
    'approved': '已通过',
    'rejected': '已驳回',
    'pending': '待审核'
  };
  return textMap[status] || '未知';
};

// 事件处理函数
const handleStepChange = (step) => {
  console.log('步骤切换:', step);
};

const handleEdit = () => {
  router.push({ query: { ...route.query, mode: 'edit' } });
};

const handleSave = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) return;

    // 保存数据
    if (pageMode.value === 'create') {
      await projectSectionStore.createProjectSection(formData);
      ElMessage.success('创建成功');
    } else {
      await projectSectionStore.updateProjectSection(route.params.id, formData);
      ElMessage.success('更新成功');
    }
    
    // 返回列表页
    handleBack();
  } catch (error) {
    ElMessage.error(pageMode.value === 'create' ? '创建失败' : '更新失败');
  }
};

const handleBack = () => {
  router.push('/bidding-procurement/procurement-execution/project-section/list');
};

// 加载数据
const loadData = async () => {
  if (pageMode.value !== 'create' && route.params.id) {
    try {
      const data = await projectSectionStore.fetchProjectSectionDetail(route.params.id);
      Object.assign(formData, data);
    } catch (error) {
      ElMessage.error('加载数据失败');
    }
  }
};

// 组件挂载
onMounted(() => {
  loadData();
  console.log('项目标段管理详情页面已挂载，模式:', pageMode.value);
});
</script>

<style scoped>
.section-tabs {
  margin-top: 20px;
}

.tab-content {
  padding: 20px;
  min-height: 200px;
}
</style>
