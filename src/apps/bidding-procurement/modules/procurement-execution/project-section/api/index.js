/**
 * 项目标段管理 - API层
 *
 * 模板说明：
 * - 标准的API接口封装模板
 * - 基于 window.$http 的统一请求方法
 * - 包含完整的CRUD操作接口
 *
 * 占位符说明：
 * - 项目标段管理: 模块中文名称，如"用户管理"
 * - 项目标段: 实体中文名称，如"用户"
 * - projectSection: 实体英文名称，如"user"
 * - /api/project-sections: API基础路径，如"/api/users"
 */

/**
 * 项目标段管理相关API接口
 */
export const projectSectionApi = {
  /**
   * 获取项目标段列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页数量
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.status - 状态筛选
   * @returns {Promise} 返回项目标段列表数据
   */
  getList(params = {}) {
    return window.$http.post('/api/project-sections/list', {
      page: params.page || 1,
      size: params.size || 10,
      keyword: params.keyword || '',
      status: params.status
    });
  },

  /**
   * 根据ID获取项目标段详情
   * @param {string|number} id - 项目标段ID
   * @returns {Promise} 返回项目标段详情数据
   */
  getById(id) {
    return window.$http.fetch(`/api/project-sections/${id}`);
  },

  /**
   * 创建新项目标段
   * @param {Object} data - 项目标段数据
   * @param {string} data.sectionName - 标段名称
   * @param {number} data.sectionAmount - 标段金额
   * @param {string} data.sectionDescription - 标段描述
   * @param {string} data.projectPlanId - 项目计划ID
   * @returns {Promise} 返回创建结果
   */
  create(data) {
    return window.$http.post('/api/project-sections/create', {
      sectionName: data.sectionName,
      sectionAmount: data.sectionAmount,
      sectionDescription: data.sectionDescription || '',
      projectPlanId: data.projectPlanId
    });
  },

  /**
   * 更新项目标段信息
   * @param {string|number} id - 项目标段ID
   * @param {Object} data - 更新数据
   * @param {string} data.sectionName - 标段名称
   * @param {number} data.sectionAmount - 标段金额
   * @param {string} data.sectionDescription - 标段描述
   * @returns {Promise} 返回更新结果
   */
  update(id, data) {
    return window.$http.post(`/api/project-sections/update/${id}`, {
      sectionName: data.sectionName,
      sectionAmount: data.sectionAmount,
      sectionDescription: data.sectionDescription
    });
  },

  /**
   * 删除项目标段
   * @param {string|number} id - 项目标段ID
   * @returns {Promise} 返回删除结果
   */
  delete(id) {
    return window.$http.post(`/api/project-sections/delete/${id}`);
  },

  /**
   * 批量删除项目标段
   * @param {Array} ids - 项目标段ID数组
   * @returns {Promise} 返回批量删除结果
   */
  batchDelete(ids) {
    return window.$http.post('/api/project-sections/batch-delete', {
      ids: ids
    });
  },

  /**
   * 更新项目标段状态
   * @param {string|number} id - 项目标段ID
   * @param {number} status - 新状态 (1:启用, 0:禁用)
   * @returns {Promise} 返回状态更新结果
   */
  updateStatus(id, status) {
    return window.$http.post(`/api/project-sections/update-status/${id}`, {
      status: status
    });
  },

  /**
   * 根据项目计划查询标段
   * @param {string|number} planId - 项目计划ID
   * @returns {Promise} 返回项目标段列表
   */
  getProjectSectionsByPlan(planId) {
    return window.$http.post('/api/project-sections/by-plan', {
      projectPlanId: planId
    });
  },

  /**
   * 标段审核接口
   * @param {string|number} id - 项目标段ID
   * @param {Object} auditData - 审核数据
   * @param {string} auditData.auditStatus - 审核状态
   * @param {string} auditData.auditUser - 审核人
   * @param {string} auditData.auditRemark - 审核备注
   * @returns {Promise} 返回审核结果
   */
  auditProjectSection(id, auditData) {
    return window.$http.post(`/api/project-sections/audit/${id}`, {
      auditStatus: auditData.auditStatus,
      auditUser: auditData.auditUser,
      auditRemark: auditData.auditRemark || ''
    });
  },

  /**
   * 标段状态更新
   * @param {string|number} id - 项目标段ID
   * @param {string} status - 新状态
   * @returns {Promise} 返回状态更新结果
   */
  updateSectionStatus(id, status) {
    return window.$http.post(`/api/project-sections/section-status/${id}`, {
      sectionStatus: status
    });
  },

  /**
   * 导出项目标段数据
   * @param {Object} params - 导出参数
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.status - 状态筛选
   * @returns {Promise} 返回导出文件信息
   */
  export(params = {}) {
    return window.$http.post('/api/project-sections/export', {
      keyword: params.keyword || '',
      status: params.status
    });
  }
};

// 默认导出
export default projectSectionApi;
