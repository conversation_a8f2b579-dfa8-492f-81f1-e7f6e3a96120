/**
 * 项目标段管理 - Store层
 * 
 * 模板说明：
 * - 基于 Pinia 的状态管理模板
 * - 集成API层和Adapters层调用
 * - 包含完整的业务逻辑封装
 * 
 * 占位符说明：
 * - 项目标段管理: 模块中文名称，如"用户管理"
 * - 项目标段: 实体中文名称，如"用户"
 * - projectSection: 实体英文名称，如"user"
 * - useProjectSectionStore: Store名称，如"useUserStore"
 */

import { defineStore } from 'pinia';
import { ref, reactive } from 'vue';
import { projectSectionApi } from './api/index.js';
import { projectSectionAdapters } from './adapters/index.js';

/**
 * 项目标段管理 Store
 */
export const useProjectSectionStore = defineStore('projectSection', () => {
  // 状态定义
  const projectSectionList = ref([]);
  const currentProjectSection = ref(null);
  const loading = ref(false);
  const pagination = reactive({
    page: 1,
    size: 10,
    total: 0
  });

  // 搜索条件
  const searchParams = reactive({
    keyword: '',
    status: null,
    projectPlanId: ''
  });

  /**
   * 获取项目标段列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回处理后的列表数据
   */
  const fetchProjectSectionList = async (params = {}) => {
    try {
      loading.value = true;
      
      // 合并查询参数
      const queryParams = {
        ...searchParams,
        ...params,
        page: pagination.page,
        size: pagination.size
      };

      // 调用API
      const response = await projectSectionApi.getList(queryParams);
      
      // 数据转换
      const adaptedData = projectSectionAdapters.adaptListData(response);
      
      // 更新状态
      projectSectionList.value = adaptedData.list;
      pagination.total = adaptedData.total;
      
      return adaptedData;
    } catch (error) {
      console.error('获取项目标段列表失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 根据ID获取项目标段详情
   * @param {string|number} id - 项目标段ID
   * @returns {Promise} 返回处理后的详情数据
   */
  const fetchProjectSectionDetail = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await projectSectionApi.getById(id);
      
      // 数据转换
      const adaptedData = projectSectionAdapters.adaptDetailData(response);
      
      // 更新状态
      currentProjectSection.value = adaptedData;
      
      return adaptedData;
    } catch (error) {
      console.error('获取项目标段详情失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 创建新项目标段
   * @param {Object} data - 项目标段数据
   * @returns {Promise} 返回创建结果
   */
  const createProjectSection = async (data) => {
    try {
      loading.value = true;
      
      // 数据转换
      const apiData = projectSectionAdapters.adaptCreateData(data);
      
      // 调用API
      const response = await projectSectionApi.create(apiData);
      
      // 数据转换
      const adaptedData = projectSectionAdapters.adaptDetailData(response);
      
      return adaptedData;
    } catch (error) {
      console.error('创建项目标段失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新项目标段信息
   * @param {string|number} id - 项目标段ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 返回更新结果
   */
  const updateProjectSection = async (id, data) => {
    try {
      loading.value = true;
      
      // 数据转换
      const apiData = projectSectionAdapters.adaptUpdateData(data);
      
      // 调用API
      const response = await projectSectionApi.update(id, apiData);
      
      // 数据转换
      const adaptedData = projectSectionAdapters.adaptDetailData(response);
      
      // 更新当前项目标段状态
      if (currentProjectSection.value && currentProjectSection.value.id === id) {
        currentProjectSection.value = adaptedData;
      }
      
      return adaptedData;
    } catch (error) {
      console.error('更新项目标段失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 删除项目标段
   * @param {string|number} id - 项目标段ID
   * @returns {Promise} 返回删除结果
   */
  const deleteProjectSection = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await projectSectionApi.delete(id);
      
      // 从列表中移除
      const index = projectSectionList.value.findIndex(item => item.id === id);
      if (index > -1) {
        projectSectionList.value.splice(index, 1);
        pagination.total--;
      }
      
      return response;
    } catch (error) {
      console.error('删除项目标段失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 审核项目标段
   * @param {string|number} id - 项目标段ID
   * @param {Object} auditData - 审核数据
   * @returns {Promise} 返回审核结果
   */
  const auditProjectSection = async (id, auditData) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await projectSectionApi.auditProjectSection(id, auditData);
      
      // 数据转换
      const adaptedData = projectSectionAdapters.adaptDetailData(response);
      
      // 更新当前项目标段状态
      if (currentProjectSection.value && currentProjectSection.value.id === id) {
        currentProjectSection.value = adaptedData;
      }
      
      return adaptedData;
    } catch (error) {
      console.error('审核项目标段失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新搜索条件
   * @param {Object} params - 搜索参数
   */
  const updateSearchParams = (params) => {
    Object.assign(searchParams, params);
    pagination.page = 1; // 重置页码
  };

  /**
   * 更新分页信息
   * @param {Object} paginationData - 分页数据
   */
  const updatePagination = (paginationData) => {
    Object.assign(pagination, paginationData);
  };

  /**
   * 重置状态
   */
  const resetState = () => {
    projectSectionList.value = [];
    currentProjectSection.value = null;
    loading.value = false;
    pagination.page = 1;
    pagination.total = 0;
    searchParams.keyword = '';
    searchParams.status = null;
    searchParams.projectPlanId = '';
  };

  // 返回状态和方法
  return {
    // 状态
    projectSectionList,
    currentProjectSection,
    loading,
    pagination,
    searchParams,
    
    // 方法
    fetchProjectSectionList,
    fetchProjectSectionDetail,
    createProjectSection,
    updateProjectSection,
    deleteProjectSection,
    auditProjectSection,
    updateSearchParams,
    updatePagination,
    resetState
  };
});
