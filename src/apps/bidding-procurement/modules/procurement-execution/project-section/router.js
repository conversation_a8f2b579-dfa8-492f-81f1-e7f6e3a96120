/**
 * 项目标段管理 - 路由配置
 * 
 * 路由结构：
 * /project-section
 * ├── /list (列表页)
 * ├── /detail/:id (详情页)
 * ├── /create (新建页)
 * ├── /edit/:id (编辑页)
 * └── /audit/:id (审核页)
 */

export default {
  path: 'project-section',
  name: 'ProjectSection',
  redirect: 'project-section/list',
  meta: {
    title: '项目标段管理',
    isMenu: true
  },
  children: [
    {
      path: 'list',
      name: 'ProjectSectionList',
      component: () => import('@/apps/bidding-procurement/modules/procurement-execution/project-section/views/list/index.vue'),
      meta: {
        title: '项目标段列表',
        isMenu: false
      }
    },
    {
      path: 'detail/:id',
      name: 'ProjectSectionDetail',
      component: () => import('@/apps/bidding-procurement/modules/procurement-execution/project-section/views/detail/index.vue'),
      meta: {
        title: '项目标段详情',
        isMenu: false
      }
    },
    {
      path: 'create',
      name: 'ProjectSectionCreate',
      component: () => import('@/apps/bidding-procurement/modules/procurement-execution/project-section/views/create/index.vue'),
      meta: {
        title: '新建项目标段',
        isMenu: false
      }
    },
    {
      path: 'edit/:id',
      name: 'ProjectSectionEdit',
      component: () => import('@/apps/bidding-procurement/modules/procurement-execution/project-section/views/edit/index.vue'),
      meta: {
        title: '编辑项目标段',
        isMenu: false
      }
    },
    {
      path: 'audit/:id',
      name: 'ProjectSectionAudit',
      component: () => import('@/apps/bidding-procurement/modules/procurement-execution/project-section/views/audit/index.vue'),
      meta: {
        title: '审核项目标段',
        isMenu: false
      }
    }
  ]
};
