<!-- 评标结果公示管理 - 详情页面 -->
<template>
  <funi-detail 
    :steps="stepsConfig"
    :detailHeadOption="detailHeadOption"
    @onStepChange="handleStepChange"
  >
    <template #step1>
      <div class="bid-result-detail">
        <!-- 基本信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="公告标题">
              {{ bidResultData.title || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="项目名称">
              {{ bidResultData.projectName || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="标段名称">
              {{ bidResultData.sectionName || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="公示时间">
              {{ bidResultData.announcementTime || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="审核状态">
              <el-tag :type="getAuditStatusType(bidResultData.auditStatus)">
                {{ bidResultData.auditStatusText || '--' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="发布状态">
              <el-tag :type="getPublishStatusType(bidResultData.publishStatus)">
                {{ bidResultData.publishStatusText || '--' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="候选人数量">
              {{ bidResultData.candidateCount || 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ bidResultData.createTime || '--' }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 投标情况说明 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>投标情况说明</span>
            </div>
          </template>
          <div class="content-container" v-html="bidResultData.biddingDescription || '暂无内容'"></div>
        </el-card>

        <!-- 评标说明 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>评标说明</span>
            </div>
          </template>
          <div class="content-container" v-html="bidResultData.evaluationDescription || '暂无内容'"></div>
        </el-card>

        <!-- 候选人信息 -->
        <el-card class="detail-card" shadow="never" v-if="bidResultData.candidateInfo && bidResultData.candidateInfo.length > 0">
          <template #header>
            <div class="card-header">
              <span>候选人信息</span>
            </div>
          </template>
          <el-table :data="bidResultData.candidateInfo" border>
            <el-table-column prop="rank" label="排名" width="80" align="center" />
            <el-table-column prop="companyName" label="投标人名称" min-width="200" />
            <el-table-column prop="bidAmountText" label="投标报价" width="150" align="right" />
            <el-table-column prop="evaluationScore" label="评标得分" width="120" align="center" />
            <el-table-column prop="winnerTypeText" label="中标类型" width="150" align="center">
              <template #default="{ row }">
                <el-tag v-if="row.winnerTypeText" :type="getWinnerTypeColor(row.winnerType)">
                  {{ row.winnerTypeText }}
                </el-tag>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column prop="qualificationStatusText" label="资格状态" width="120" align="center">
              <template #default="{ row }">
                <el-tag :type="row.qualificationStatus === 'qualified' ? 'success' : 'danger'">
                  {{ row.qualificationStatusText }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 附件信息 -->
        <el-card class="detail-card" shadow="never" v-if="bidResultData.attachments && bidResultData.attachments.length > 0">
          <template #header>
            <div class="card-header">
              <span>附件信息</span>
            </div>
          </template>
          <funi-file-table 
            :fileList="bidResultData.attachments"
            :readonly="true"
          />
        </el-card>

        <!-- 审核历史 -->
        <el-card class="detail-card" shadow="never" v-if="bidResultData.auditHistory && bidResultData.auditHistory.length > 0">
          <template #header>
            <div class="card-header">
              <span>审核历史</span>
            </div>
          </template>
          <el-table :data="bidResultData.auditHistory" border>
            <el-table-column prop="auditResultText" label="审核结果" width="120" align="center">
              <template #default="{ row }">
                <el-tag :type="row.auditResult === 'approved' ? 'success' : 'danger'">
                  {{ row.auditResultText }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="auditComment" label="审核意见" min-width="200" />
            <el-table-column prop="auditUser" label="审核人" width="120" />
            <el-table-column prop="auditTime" label="审核时间" width="180" />
          </el-table>
        </el-card>

        <!-- 撤销原因 -->
        <el-card class="detail-card" shadow="never" v-if="bidResultData.revokeReason">
          <template #header>
            <div class="card-header">
              <span>撤销原因</span>
            </div>
          </template>
          <div class="content-container">
            {{ bidResultData.revokeReason }}
          </div>
        </el-card>
      </div>
    </template>

    <template #step2>
      <div class="project-info">
        <!-- 项目信息页签内容 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>项目详细信息</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="项目编号">
              {{ bidResultData.projectId || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="项目名称">
              {{ bidResultData.projectName || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="标段编号">
              {{ bidResultData.sectionId || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="标段名称">
              {{ bidResultData.sectionName || '--' }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>
    </template>

    <template #step3>
      <div class="operation-records">
        <!-- 操作记录页签内容 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>操作记录</span>
            </div>
          </template>
          <el-table :data="operationRecords" border>
            <el-table-column prop="operation" label="操作类型" width="120" />
            <el-table-column prop="operator" label="操作人" width="120" />
            <el-table-column prop="operationTime" label="操作时间" width="180" />
            <el-table-column prop="remark" label="备注" min-width="200" />
          </el-table>
        </el-card>
      </div>
    </template>
  </funi-detail>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';
import { reactive, ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { useBidResultAnnouncementStore } from '../../store.js';

// 路由和Store
const router = useRouter();
const route = useRoute();
const bidResultAnnouncementStore = useBidResultAnnouncementStore();

// 评标结果公示数据
const bidResultData = ref({});

// 操作记录数据
const operationRecords = ref([]);

// 步骤配置
const stepsConfig = reactive([
  {
    title: '公示详情',
    name: 'step1'
  },
  {
    title: '项目信息',
    name: 'step2'
  },
  {
    title: '操作记录',
    name: 'step3'
  }
]);

// 详情页头部配置
const detailHeadOption = reactive({
  title: computed(() => bidResultData.value.title || '评标结果公示详情'),
  btns: computed(() => {
    const btns = [];
    
    // 根据状态显示不同的操作按钮
    if (bidResultData.value.auditStatus === 'draft') {
      btns.push({
        label: '编辑',
        type: 'primary',
        auth: 'bid_result_announcement_edit',
        on: {
          click: () => handleEdit()
        }
      });
      btns.push({
        label: '提交审核',
        type: 'primary',
        auth: 'bid_result_announcement_submit',
        on: {
          click: () => handleSubmit()
        }
      });
    } else if (bidResultData.value.auditStatus === 'approved' && bidResultData.value.publishStatus === 'unpublished') {
      btns.push({
        label: '发布',
        type: 'primary',
        auth: 'bid_result_announcement_publish',
        on: {
          click: () => handlePublish()
        }
      });
    } else if (bidResultData.value.publishStatus === 'published') {
      btns.push({
        label: '撤销',
        type: 'danger',
        auth: 'bid_result_announcement_revoke',
        on: {
          click: () => handleRevoke()
        }
      });
    }
    
    btns.push({
      label: '返回',
      on: {
        click: () => handleBack()
      }
    });
    
    return btns;
  })
});

// 获取审核状态类型
const getAuditStatusType = (status) => {
  const typeMap = {
    'draft': 'info',
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger'
  };
  return typeMap[status] || 'info';
};

// 获取发布状态类型
const getPublishStatusType = (status) => {
  const typeMap = {
    'unpublished': 'info',
    'published': 'success',
    'revoked': 'danger'
  };
  return typeMap[status] || 'info';
};

// 获取中标类型颜色
const getWinnerTypeColor = (type) => {
  const colorMap = {
    'first': 'success',
    'second': 'warning',
    'third': 'info'
  };
  return colorMap[type] || 'info';
};

// 处理步骤切换
const handleStepChange = (step) => {
  console.log('步骤切换:', step);
};

// 编辑
const handleEdit = () => {
  router.push({
    path: `/bidding-procurement/procurement-execution/bid-result-announcement/edit/${route.params.id}`,
    query: {
      title: bidResultData.value.title
    }
  });
};

// 提交审核
const handleSubmit = async () => {
  try {
    await bidResultAnnouncementStore.submitBidResultAnnouncement(route.params.id);
    ElMessage.success('提交审核成功');
    await loadData();
  } catch (error) {
    console.error('提交审核失败:', error);
    ElMessage.error('提交审核失败，请重试');
  }
};

// 发布
const handlePublish = async () => {
  try {
    await bidResultAnnouncementStore.publishBidResultAnnouncement(route.params.id);
    ElMessage.success('发布成功');
    await loadData();
  } catch (error) {
    console.error('发布失败:', error);
    ElMessage.error('发布失败，请重试');
  }
};

// 撤销
const handleRevoke = async () => {
  // 这里应该弹出撤销原因输入框，简化处理
  try {
    await bidResultAnnouncementStore.revokeBidResultAnnouncement(route.params.id, {
      revokeReason: '管理员撤销'
    });
    ElMessage.success('撤销成功');
    await loadData();
  } catch (error) {
    console.error('撤销失败:', error);
    ElMessage.error('撤销失败，请重试');
  }
};

// 返回
const handleBack = () => {
  router.push('/bidding-procurement/procurement-execution/bid-result-announcement/list');
};

// 加载数据
const loadData = async () => {
  try {
    const data = await bidResultAnnouncementStore.getBidResultAnnouncement(route.params.id);
    bidResultData.value = data;
    
    // 模拟操作记录数据
    operationRecords.value = [
      {
        operation: '创建',
        operator: data.createUser,
        operationTime: data.createTime,
        remark: '创建评标结果公示'
      },
      {
        operation: '更新',
        operator: data.updateUser,
        operationTime: data.updateTime,
        remark: '更新评标结果公示信息'
      }
    ];
  } catch (error) {
    console.error('加载数据失败:', error);
    ElMessage.error('加载数据失败，请重试');
  }
};

// 组件挂载
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.bid-result-detail {
  padding: 20px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  font-weight: bold;
  font-size: 16px;
}

.content-container {
  padding: 10px;
  min-height: 100px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.project-info,
.operation-records {
  padding: 20px;
}
</style>
