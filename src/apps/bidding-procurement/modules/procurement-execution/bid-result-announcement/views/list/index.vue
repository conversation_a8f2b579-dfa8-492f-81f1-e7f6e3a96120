<!-- 评标结果公示管理 - 列表页面 -->
<template>
  <funi-list-page-v2 ref="listPageRef" :cardTab="cardTabConfig" @headBtnClick="handleHeadBtnClick" />
</template>

<script setup lang="jsx">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElNotification, ElMessageBox } from 'element-plus';
import { useBidResultAnnouncementStore } from '../../store.js';

// ==================== 基础变量配置 ====================
const listPageRef = ref();
const router = useRouter();
const bidResultAnnouncementStore = useBidResultAnnouncementStore();

// ==================== 权限配置 ====================
const auths = reactive({
  export: 'bid_result_announcement_export', // 导出权限
  add: 'bid_result_announcement_add', // 新增权限
  delete: 'bid_result_announcement_delete', // 删除权限
  audit: 'bid_result_announcement_audit', // 审核权限
  edit: 'bid_result_announcement_edit', // 编辑权限
  submit: 'bid_result_announcement_submit', // 提交权限
  publish: 'bid_result_announcement_publish', // 发布权限
  revoke: 'bid_result_announcement_revoke', // 撤销权限
  detail: 'bid_result_announcement_detail' // 详情权限
});

// ==================== 数据状态管理 ====================
const listPageParams = ref({});

// ==================== 核心业务函数 ====================

/**
 * 跳转到详情页面
 */
const goDetail = row => {
  router.push({
    path: '/bidding-procurement/procurement-execution/bid-result-announcement/detail/' + row.id,
    query: {
      title: row.title
    }
  });
};

/**
 * 删除评标结果公示
 */
const deleteItem = async id => {
  try {
    await bidResultAnnouncementStore.deleteBidResultAnnouncement(id);
    ElNotification({
      title: '提示',
      message: '删除成功',
      type: 'success',
      duration: 2000
    });
    listPageRef.value.reload({ resetPage: false });
  } catch (error) {
    console.error('删除失败:', error);
    ElNotification({
      title: '错误',
      message: '删除失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

/**
 * 跳转到编辑页面
 */
const editItem = row => {
  router.push({
    path: '/bidding-procurement/procurement-execution/bid-result-announcement/edit/' + row.id,
    query: {
      title: row.title
    }
  });
};

/**
 * 提交审核
 */
const submitItem = async row => {
  try {
    await bidResultAnnouncementStore.submitBidResultAnnouncement(row.id);
    ElNotification({
      title: '提示',
      message: '提交审核成功',
      type: 'success',
      duration: 2000
    });
    listPageRef.value.reload({ resetPage: false });
  } catch (error) {
    console.error('提交审核失败:', error);
    ElNotification({
      title: '错误',
      message: '提交审核失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

/**
 * 跳转到审核页面
 */
const auditItem = row => {
  router.push({
    path: '/bidding-procurement/procurement-execution/bid-result-announcement/audit/' + row.id,
    query: {
      title: row.title
    }
  });
};

/**
 * 发布评标结果公示
 */
const publishItem = async row => {
  try {
    await bidResultAnnouncementStore.publishBidResultAnnouncement(row.id);
    ElNotification({
      title: '提示',
      message: '发布成功',
      type: 'success',
      duration: 2000
    });
    listPageRef.value.reload({ resetPage: false });
  } catch (error) {
    console.error('发布失败:', error);
    ElNotification({
      title: '错误',
      message: '发布失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

/**
 * 撤销评标结果公示
 */
const revokeItem = async row => {
  ElMessageBox.prompt('请输入撤销原因', '撤销评标结果公示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /.+/,
    inputErrorMessage: '撤销原因不能为空'
  })
    .then(async ({ value }) => {
      try {
        await bidResultAnnouncementStore.revokeBidResultAnnouncement(row.id, {
          revokeReason: value
        });
        ElNotification({
          title: '提示',
          message: '撤销成功',
          type: 'success',
          duration: 2000
        });
        listPageRef.value.reload({ resetPage: false });
      } catch (error) {
        console.error('撤销失败:', error);
        ElNotification({
          title: '错误',
          message: '撤销失败，请重试',
          type: 'error',
          duration: 2000
        });
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// ==================== 动态按钮生成器 ====================

/**
 * 动态按钮生成器
 */
const generateActionButtons = row => {
  const buttons = [];

  // 根据状态显示不同的操作按钮
  if (row.auditStatus === 'draft') {
    // 草稿状态：可编辑、删除、提交
    buttons.push(
      <el-link type="primary" onClick={() => editItem(row)} style={{ marginRight: '10px' }}>
        编辑
      </el-link>
    );
    buttons.push(
      <el-popconfirm title="是否删除?" onConfirm={() => deleteItem(row.id)}>
        {{
          reference: () => (
            <el-link type="primary" style={{ marginRight: '10px' }}>
              删除
            </el-link>
          )
        }}
      </el-popconfirm>
    );
    buttons.push(
      <el-link type="primary" onClick={() => submitItem(row)} style={{ marginRight: '10px' }}>
        提交
      </el-link>
    );
  } else if (row.auditStatus === 'pending') {
    // 待审核状态：可审核
    buttons.push(
      <el-link type="primary" onClick={() => auditItem(row)} style={{ marginRight: '10px' }}>
        审核
      </el-link>
    );
  } else if (row.auditStatus === 'approved' && row.publishStatus === 'unpublished') {
    // 审核通过但未发布：可发布
    buttons.push(
      <el-link type="primary" onClick={() => publishItem(row)} style={{ marginRight: '10px' }}>
        发布
      </el-link>
    );
  } else if (row.publishStatus === 'published') {
    // 已发布状态：可撤销
    buttons.push(
      <el-link type="primary" onClick={() => revokeItem(row)} style={{ marginRight: '10px' }}>
        撤销
      </el-link>
    );
  }

  // 详情按钮始终显示
  buttons.push(
    <el-link type="primary" onClick={() => goDetail(row)}>
      详情
    </el-link>
  );

  return buttons;
};

// 表格列配置
const columns = reactive([
  {
    label: '公告标题',
    prop: 'title',
    fixed: 'left',
    render: ({ row }) => {
      return (
        <el-link type="primary" onClick={() => goDetail(row)}>
          {row.title || '--'}
        </el-link>
      );
    }
  },
  {
    label: '项目名称',
    prop: 'projectName',
    render: ({ row }) => row.projectName || '--'
  },
  {
    label: '标段名称',
    prop: 'sectionName',
    render: ({ row }) => row.sectionName || '--'
  },
  {
    label: '候选人数量',
    prop: 'candidateCount',
    render: ({ row }) => row.candidateCount || 0
  },
  {
    label: '审核状态',
    prop: 'auditStatusText',
    render: ({ row }) => {
      const statusColorMap = {
        draft: 'info',
        pending: 'warning',
        approved: 'success',
        rejected: 'danger'
      };
      return <el-tag type={statusColorMap[row.auditStatus] || 'info'}>{row.auditStatusText || '--'}</el-tag>;
    }
  },
  {
    label: '发布状态',
    prop: 'publishStatusText',
    render: ({ row }) => {
      const statusColorMap = {
        unpublished: 'info',
        published: 'success',
        revoked: 'danger'
      };
      return <el-tag type={statusColorMap[row.publishStatus] || 'info'}>{row.publishStatusText || '--'}</el-tag>;
    }
  },
  {
    label: '公示时间',
    prop: 'announcementTime',

    render: ({ row }) => row.announcementTime || '--'
  },
  {
    label: '创建时间',
    prop: 'createTime',

    render: ({ row }) => row.createTime || '--'
  },
  {
    label: '操作',
    prop: 'actions',
    width: 200,
    fixed: 'right',
    render: ({ row }) => {
      return <div>{generateActionButtons(row)}</div>;
    }
  }
]);

// 数据加载函数
const loadData = async (pageParams, searchParams) => {
  listPageParams.value = searchParams;
  try {
    return bidResultAnnouncementStore.getBidResultAnnouncementList({
      ...pageParams,
      ...searchParams
    });
  } catch (error) {
    console.error('加载数据失败:', error);
    return {
      list: [],
      total: 0
    };
  }
};

// 根据状态加载数据
const loadDataByStatus = async (pageParams, searchParams, auditStatus) => {
  listPageParams.value = { ...searchParams, auditStatus };
  try {
    return await bidResultAnnouncementStore.getBidResultAnnouncementList({
      ...pageParams,
      ...searchParams,
      auditStatus
    });
  } catch (error) {
    console.error('加载数据失败:', error);
    return {
      list: [],
      total: 0
    };
  }
};

// 页签配置
const cardTabConfig = computed(() => {
  return [
    {
      label: '全部',
      key: 'all',
      curdOption: {
        columns,
        lodaData: loadData,
        btns: [
          { key: 'add', label: '新建', type: 'primary' },
          { key: 'export', label: '导出', auth: auths.export }
        ],
        fixedButtons: true,
        reloadOnActive: true
      }
    },
    {
      label: '待审核',
      key: 'pending',
      curdOption: {
        columns,
        lodaData: (pageParams, searchParams) => loadDataByStatus(pageParams, searchParams, 'pending'),
        btns: [{ key: 'export', label: '导出', auth: auths.export }],
        fixedButtons: true,
        reloadOnActive: true
      }
    },
    {
      label: '审核中',
      key: 'auditing',
      curdOption: {
        columns,
        lodaData: (pageParams, searchParams) => loadDataByStatus(pageParams, searchParams, 'auditing'),
        btns: [{ key: 'export', label: '导出', auth: auths.export }],
        fixedButtons: true,
        reloadOnActive: true
      }
    },
    {
      label: '已发布',
      key: 'published',
      curdOption: {
        columns,
        lodaData: (pageParams, searchParams) => loadDataByStatus(pageParams, searchParams, 'approved'),
        btns: [{ key: 'export', label: '导出', auth: auths.export }],
        fixedButtons: true,
        reloadOnActive: true
      }
    }
  ];
});

// 导出功能
const exportData = async () => {
  try {
    await bidResultAnnouncementStore.exportBidResultAnnouncement(listPageParams.value);
    ElNotification({
      title: '提示',
      message: '导出成功',
      type: 'success',
      duration: 2000
    });
  } catch (error) {
    console.error('导出失败:', error);
    ElNotification({
      title: '错误',
      message: '导出失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

// 新增功能
const addItem = () => {
  router.push({
    path: '/bidding-procurement/procurement-execution/bid-result-announcement/create',
    query: {
      bizName: '新建'
    }
  });
};

// 头部按钮点击事件处理
const handleHeadBtnClick = key => {
  switch (key) {
    case 'add':
      addItem();
      break;
    case 'export':
      exportData();
      break;
    default:
      break;
  }
};

// ==================== 生命周期钩子 ====================

/**
 * 组件挂载完成
 */
onMounted(() => {
  console.log('评标结果公示管理列表页面已挂载');
});
</script>
