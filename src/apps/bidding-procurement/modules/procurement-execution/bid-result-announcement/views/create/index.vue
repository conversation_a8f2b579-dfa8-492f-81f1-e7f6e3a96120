<!-- 评标结果公示管理 - 新建/编辑页面 -->
<template>
  <funi-detail 
    :steps="stepsConfig"
    :detailHeadOption="detailHeadOption"
    @onStepChange="handleStepChange"
  >
    <template #step1>
      <funi-form
        ref="formRef"
        :schema="formSchema"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      />
    </template>
  </funi-detail>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';
import { reactive, ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { useBidResultAnnouncementStore } from '../../store.js';

// 路由和Store
const router = useRouter();
const route = useRoute();
const bidResultAnnouncementStore = useBidResultAnnouncementStore();

// 表单引用
const formRef = ref(null);

// 页面模式
const pageMode = computed(() => {
  if (route.path.includes('/create')) return 'create';
  if (route.path.includes('/edit')) return 'edit';
  return 'view';
});

// 表单数据
const formData = reactive({
  title: '',
  projectId: '',
  projectName: '',
  sectionId: '',
  sectionName: '',
  announcementTime: '',
  biddingDescription: '',
  evaluationDescription: '',
  candidateInfo: [],
  attachments: []
});

// 步骤配置
const stepsConfig = reactive([
  {
    title: pageMode.value === 'create' ? '新建评标结果公示' : '编辑评标结果公示',
    name: 'step1'
  }
]);

// 详情页头部配置
const detailHeadOption = reactive({
  title: computed(() => {
    return pageMode.value === 'create' ? '新建评标结果公示' : '编辑评标结果公示';
  }),
  btns: computed(() => {
    return [
      {
        label: '保存草稿',
        type: 'default',
        on: {
          click: () => handleSave(false)
        }
      },
      {
        label: '保存并提交',
        type: 'primary',
        on: {
          click: () => handleSave(true)
        }
      },
      {
        label: '返回',
        on: {
          click: () => handleBack()
        }
      }
    ];
  })
});

// 搜索项目
const searchProjects = async (query) => {
  // 这里应该调用项目搜索API
  return [
    { value: 'project_001', label: '某某基础设施建设项目' },
    { value: 'project_002', label: '办公设备采购项目' },
    { value: 'project_003', label: '道路维修工程项目' }
  ].filter(item => item.label.includes(query));
};

// 搜索标段
const searchSections = async (query) => {
  // 这里应该调用标段搜索API
  return [
    { value: 'section_001', label: '第一标段' },
    { value: 'section_002', label: '第一包' },
    { value: 'section_003', label: '第二标段' }
  ].filter(item => item.label.includes(query));
};

// 表单配置
const formSchema = computed(() => [
  {
    field: 'title',
    label: '公告标题',
    component: 'el-input',
    props: {
      placeholder: '请输入公告标题'
    },
    required: true
  },
  {
    field: 'projectId',
    label: '项目名称',
    component: 'el-select',
    props: {
      placeholder: '请选择项目',
      filterable: true,
      remote: true,
      remoteMethod: searchProjects,
      clearable: true,
      onChange: (value) => {
        const project = formData.projectOptions?.find(p => p.value === value);
        if (project) {
          formData.projectName = project.label;
        }
      }
    },
    required: true
  },
  {
    field: 'sectionId',
    label: '标段名称',
    component: 'el-select',
    props: {
      placeholder: '请选择标段',
      filterable: true,
      remote: true,
      remoteMethod: searchSections,
      clearable: true,
      onChange: (value) => {
        const section = formData.sectionOptions?.find(s => s.value === value);
        if (section) {
          formData.sectionName = section.label;
        }
      }
    },
    required: true
  },
  {
    field: 'announcementTime',
    label: '公示时间',
    component: 'el-date-picker',
    props: {
      type: 'datetime',
      placeholder: '请选择公示时间',
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss'
    },
    required: true
  },
  {
    field: 'biddingDescription',
    label: '投标情况说明',
    component: 'el-input',
    props: {
      type: 'textarea',
      rows: 6,
      placeholder: '请输入投标情况说明'
    },
    required: true
  },
  {
    field: 'evaluationDescription',
    label: '评标说明',
    component: 'el-input',
    props: {
      type: 'textarea',
      rows: 6,
      placeholder: '请输入评标说明'
    },
    required: true
  },
  {
    field: 'candidateInfo',
    label: '候选人信息',
    component: 'candidate-table',
    props: {
      data: formData.candidateInfo
    }
  },
  {
    field: 'attachments',
    label: '附件',
    component: 'funi-file-table',
    props: {
      fileList: formData.attachments,
      businessType: 'bid-result-announcement',
      multiple: true
    }
  }
]);

// 表单验证规则
const formRules = reactive({
  title: [
    { required: true, message: '请输入公告标题', trigger: 'blur' }
  ],
  projectId: [
    { required: true, message: '请选择项目', trigger: 'change' }
  ],
  sectionId: [
    { required: true, message: '请选择标段', trigger: 'change' }
  ],
  announcementTime: [
    { required: true, message: '请选择公示时间', trigger: 'change' }
  ],
  biddingDescription: [
    { required: true, message: '请输入投标情况说明', trigger: 'blur' }
  ],
  evaluationDescription: [
    { required: true, message: '请输入评标说明', trigger: 'blur' }
  ]
});

// 处理步骤切换
const handleStepChange = (step) => {
  console.log('步骤切换:', step);
};

// 保存数据
const handleSave = async (submit = false) => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) {
      ElMessage.error('请完善表单信息');
      return;
    }

    let result;
    if (pageMode.value === 'create') {
      // 新建
      result = await bidResultAnnouncementStore.createBidResultAnnouncement(formData);
    } else {
      // 编辑
      result = await bidResultAnnouncementStore.updateBidResultAnnouncement(route.params.id, formData);
    }

    ElMessage.success(pageMode.value === 'create' ? '创建成功' : '更新成功');

    // 如果需要提交审核
    if (submit && result.id) {
      await bidResultAnnouncementStore.submitBidResultAnnouncement(result.id);
      ElMessage.success('提交审核成功');
    }

    // 返回列表页
    handleBack();
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败，请重试');
  }
};

// 返回
const handleBack = () => {
  router.push('/bidding-procurement/procurement-execution/bid-result-announcement/list');
};

// 加载数据
const loadData = async () => {
  if (pageMode.value === 'edit' && route.params.id) {
    try {
      const data = await bidResultAnnouncementStore.getBidResultAnnouncement(route.params.id);
      Object.assign(formData, data);
    } catch (error) {
      console.error('加载数据失败:', error);
      ElMessage.error('加载数据失败，请重试');
    }
  }
};

// 组件挂载
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.candidate-table {
  width: 100%;
}
</style>
