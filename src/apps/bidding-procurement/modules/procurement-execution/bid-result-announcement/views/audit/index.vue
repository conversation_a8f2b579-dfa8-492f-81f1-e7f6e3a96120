<!-- 评标结果公示管理 - 审核页面 -->
<template>
  <funi-detail 
    :steps="stepsConfig"
    :detailHeadOption="detailHeadOption"
    @onStepChange="handleStepChange"
  >
    <template #step1>
      <div class="audit-page">
        <!-- 评标结果公示信息（只读） -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>评标结果公示信息</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="公告标题">
              {{ bidResultData.title || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="项目名称">
              {{ bidResultData.projectName || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="标段名称">
              {{ bidResultData.sectionName || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="公示时间">
              {{ bidResultData.announcementTime || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="候选人数量">
              {{ bidResultData.candidateCount || 0 }}
            </el-descriptions-item>
            <el-descriptions-item label="当前状态">
              <el-tag :type="getAuditStatusType(bidResultData.auditStatus)">
                {{ bidResultData.auditStatusText || '--' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 投标情况说明 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>投标情况说明</span>
            </div>
          </template>
          <div class="content-container" v-html="bidResultData.biddingDescription || '暂无内容'"></div>
        </el-card>

        <!-- 评标说明 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>评标说明</span>
            </div>
          </template>
          <div class="content-container" v-html="bidResultData.evaluationDescription || '暂无内容'"></div>
        </el-card>

        <!-- 候选人信息 -->
        <el-card class="detail-card" shadow="never" v-if="bidResultData.candidateInfo && bidResultData.candidateInfo.length > 0">
          <template #header>
            <div class="card-header">
              <span>候选人信息</span>
            </div>
          </template>
          <el-table :data="bidResultData.candidateInfo" border>
            <el-table-column prop="rank" label="排名" width="80" align="center" />
            <el-table-column prop="companyName" label="投标人名称" min-width="200" />
            <el-table-column prop="bidAmountText" label="投标报价" width="150" align="right" />
            <el-table-column prop="evaluationScore" label="评标得分" width="120" align="center" />
            <el-table-column prop="winnerTypeText" label="中标类型" width="150" align="center">
              <template #default="{ row }">
                <el-tag v-if="row.winnerTypeText" :type="getWinnerTypeColor(row.winnerType)">
                  {{ row.winnerTypeText }}
                </el-tag>
                <span v-else>--</span>
              </template>
            </el-table-column>
            <el-table-column prop="qualificationStatusText" label="资格状态" width="120" align="center">
              <template #default="{ row }">
                <el-tag :type="row.qualificationStatus === 'qualified' ? 'success' : 'danger'">
                  {{ row.qualificationStatusText }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 附件信息 -->
        <el-card class="detail-card" shadow="never" v-if="bidResultData.attachments && bidResultData.attachments.length > 0">
          <template #header>
            <div class="card-header">
              <span>附件信息</span>
            </div>
          </template>
          <funi-file-table 
            :fileList="bidResultData.attachments"
            :readonly="true"
          />
        </el-card>

        <!-- 审核表单 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>审核意见</span>
            </div>
          </template>
          <funi-form
            ref="auditFormRef"
            :schema="auditFormSchema"
            :model="auditFormData"
            :rules="auditFormRules"
            label-width="120px"
          />
        </el-card>

        <!-- 审核历史 -->
        <el-card class="detail-card" shadow="never" v-if="bidResultData.auditHistory && bidResultData.auditHistory.length > 0">
          <template #header>
            <div class="card-header">
              <span>审核历史</span>
            </div>
          </template>
          <el-table :data="bidResultData.auditHistory" border>
            <el-table-column prop="auditResultText" label="审核结果" width="120" align="center">
              <template #default="{ row }">
                <el-tag :type="row.auditResult === 'approved' ? 'success' : 'danger'">
                  {{ row.auditResultText }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="auditComment" label="审核意见" min-width="200" />
            <el-table-column prop="auditUser" label="审核人" width="120" />
            <el-table-column prop="auditTime" label="审核时间" width="180" />
          </el-table>
        </el-card>
      </div>
    </template>
  </funi-detail>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';
import { reactive, ref, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useBidResultAnnouncementStore } from '../../store.js';

// 路由和Store
const router = useRouter();
const route = useRoute();
const bidResultAnnouncementStore = useBidResultAnnouncementStore();

// 表单引用
const auditFormRef = ref(null);

// 评标结果公示数据
const bidResultData = ref({});

// 审核表单数据
const auditFormData = reactive({
  auditResult: '',
  auditComment: ''
});

// 步骤配置
const stepsConfig = reactive([
  {
    title: '审核评标结果公示',
    name: 'step1'
  }
]);

// 详情页头部配置
const detailHeadOption = reactive({
  title: computed(() => `审核评标结果公示 - ${bidResultData.value.title || ''}`),
  btns: computed(() => {
    return [
      {
        label: '审核通过',
        type: 'success',
        auth: 'bid_result_announcement_audit',
        on: {
          click: () => handleAudit('approved')
        }
      },
      {
        label: '审核驳回',
        type: 'danger',
        auth: 'bid_result_announcement_audit',
        on: {
          click: () => handleAudit('rejected')
        }
      },
      {
        label: '返回',
        on: {
          click: () => handleBack()
        }
      }
    ];
  })
});

// 审核表单配置
const auditFormSchema = computed(() => [
  {
    field: 'auditResult',
    label: '审核结果',
    component: 'el-radio-group',
    props: {
      options: [
        { label: '通过', value: 'approved' },
        { label: '驳回', value: 'rejected' }
      ]
    },
    required: true
  },
  {
    field: 'auditComment',
    label: '审核意见',
    component: 'el-input',
    props: {
      type: 'textarea',
      rows: 4,
      placeholder: '请输入审核意见'
    },
    required: true
  }
]);

// 审核表单验证规则
const auditFormRules = reactive({
  auditResult: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  auditComment: [
    { required: true, message: '请输入审核意见', trigger: 'blur' }
  ]
});

// 获取审核状态类型
const getAuditStatusType = (status) => {
  const typeMap = {
    'draft': 'info',
    'pending': 'warning',
    'approved': 'success',
    'rejected': 'danger'
  };
  return typeMap[status] || 'info';
};

// 获取中标类型颜色
const getWinnerTypeColor = (type) => {
  const colorMap = {
    'first': 'success',
    'second': 'warning',
    'third': 'info'
  };
  return colorMap[type] || 'info';
};

// 处理步骤切换
const handleStepChange = (step) => {
  console.log('步骤切换:', step);
};

// 处理审核
const handleAudit = async (result) => {
  try {
    // 设置审核结果
    auditFormData.auditResult = result;
    
    // 表单验证
    const valid = await auditFormRef.value.validate();
    if (!valid) {
      ElMessage.error('请完善审核信息');
      return;
    }

    // 确认审核操作
    const action = result === 'approved' ? '通过' : '驳回';
    await ElMessageBox.confirm(
      `确认${action}该评标结果公示吗？`,
      '确认审核',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 提交审核
    await bidResultAnnouncementStore.auditBidResultAnnouncement(route.params.id, auditFormData);
    
    ElMessage.success(`审核${action}成功`);
    
    // 返回列表页
    handleBack();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审核失败:', error);
      ElMessage.error('审核失败，请重试');
    }
  }
};

// 返回
const handleBack = () => {
  router.push('/bidding-procurement/procurement-execution/bid-result-announcement/list');
};

// 加载数据
const loadData = async () => {
  try {
    const data = await bidResultAnnouncementStore.getBidResultAnnouncement(route.params.id);
    bidResultData.value = data;
  } catch (error) {
    console.error('加载数据失败:', error);
    ElMessage.error('加载数据失败，请重试');
  }
};

// 组件挂载
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.audit-page {
  padding: 20px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  font-weight: bold;
  font-size: 16px;
}

.content-container {
  padding: 10px;
  min-height: 100px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}
</style>
