/**
 * 评标结果公示管理 - Store层
 *
 * 模板说明：
 * - 基于 Pinia 的状态管理模板
 * - 集成API层和Adapters层调用
 * - 包含完整的业务逻辑封装
 *
 * 占位符说明：
 * - 评标结果公示管理: 模块中文名称
 * - 评标结果公示: 实体中文名称
 * - bidResultAnnouncement: 实体英文名称
 * - useBidResultAnnouncementStore: Store名称
 */

import { defineStore } from 'pinia';
import { ref, reactive } from 'vue';
import { bidResultAnnouncementApi } from './api/index.js';
import { bidResultAnnouncementAdapters } from './adapters/index.js';

/**
 * 评标结果公示管理 Store
 */
export const useBidResultAnnouncementStore = defineStore('bidResultAnnouncement', () => {
  // 状态定义
  const bidResultAnnouncementList = ref([]);
  const currentBidResultAnnouncement = ref(null);
  const loading = ref(false);
  const submitting = ref(false);
  const pagination = reactive({
    page: 1,
    size: 10,
    total: 0
  });

  // 搜索条件
  const searchParams = reactive({
    keyword: '',
    status: null,
    auditStatus: '',
    publishStatus: '',
    projectName: ''
  });

  // 表单数据状态
  const formData = ref({
    title: '',
    projectId: '',
    sectionId: '',
    announcementTime: '',
    biddingDescription: '',
    evaluationDescription: '',
    candidateInfo: [],
    attachments: []
  });

  // 候选人信息状态
  const candidateList = ref([]);

  // 审核流程状态
  const auditHistory = ref([]);

  /**
   * 获取评标结果公示列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回处理后的列表数据
   */
  const getBidResultAnnouncementList = async (params = {}) => {
    try {
      loading.value = true;

      // 合并查询参数
      const queryParams = {
        ...searchParams,
        ...params,
        page: pagination.page,
        size: pagination.size
      };

      // 调用API
      const response = await bidResultAnnouncementApi.getList(queryParams);

      // 数据转换
      const adaptedData = bidResultAnnouncementAdapters.adaptListData(response);

      // 更新状态
      bidResultAnnouncementList.value = adaptedData.list;
      pagination.total = adaptedData.total;

      return adaptedData;
    } catch (error) {
      console.error('获取评标结果公示列表失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 根据ID获取评标结果公示详情
   * @param {string|number} id - 评标结果公示ID
   * @returns {Promise} 返回处理后的详情数据
   */
  const getBidResultAnnouncement = async id => {
    try {
      loading.value = true;

      // 调用API
      const response = await bidResultAnnouncementApi.getById(id);

      // 数据转换
      const adaptedData = bidResultAnnouncementAdapters.adaptDetailData(response);

      // 更新状态
      currentBidResultAnnouncement.value = adaptedData;
      candidateList.value = adaptedData.candidateInfo || [];
      auditHistory.value = adaptedData.auditHistory || [];

      return adaptedData;
    } catch (error) {
      console.error('获取评标结果公示详情失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 创建新评标结果公示
   * @param {Object} data - 评标结果公示数据
   * @returns {Promise} 返回创建结果
   */
  const createBidResultAnnouncement = async data => {
    try {
      submitting.value = true;

      // 数据转换
      const apiData = bidResultAnnouncementAdapters.adaptFormData(data);

      // 调用API
      const response = await bidResultAnnouncementApi.create(apiData);

      // 数据转换
      const adaptedData = bidResultAnnouncementAdapters.adaptDetailData(response);

      return adaptedData;
    } catch (error) {
      console.error('创建评标结果公示失败:', error);
      throw error;
    } finally {
      submitting.value = false;
    }
  };

  /**
   * 更新评标结果公示信息
   * @param {string|number} id - 评标结果公示ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 返回更新结果
   */
  const updateBidResultAnnouncement = async (id, data) => {
    try {
      submitting.value = true;

      // 数据转换
      const apiData = bidResultAnnouncementAdapters.adaptFormData(data);

      // 调用API
      const response = await bidResultAnnouncementApi.update(id, apiData);

      // 数据转换
      const adaptedData = bidResultAnnouncementAdapters.adaptDetailData(response);

      // 更新当前评标结果公示状态
      if (currentBidResultAnnouncement.value && currentBidResultAnnouncement.value.id === id) {
        currentBidResultAnnouncement.value = adaptedData;
      }

      return adaptedData;
    } catch (error) {
      console.error('更新评标结果公示失败:', error);
      throw error;
    } finally {
      submitting.value = false;
    }
  };

  /**
   * 删除评标结果公示
   * @param {string|number} id - 评标结果公示ID
   * @returns {Promise} 返回删除结果
   */
  const deleteBidResultAnnouncement = async id => {
    try {
      loading.value = true;

      // 调用API
      const response = await bidResultAnnouncementApi.delete(id);

      // 从列表中移除
      const index = bidResultAnnouncementList.value.findIndex(item => item.id === id);
      if (index > -1) {
        bidResultAnnouncementList.value.splice(index, 1);
        pagination.total--;
      }

      return response;
    } catch (error) {
      console.error('删除评标结果公示失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 批量删除评标结果公示
   * @param {Array} ids - 评标结果公示ID数组
   * @returns {Promise} 返回批量删除结果
   */
  const batchDeleteBidResultAnnouncement = async ids => {
    try {
      loading.value = true;

      // 调用API
      const response = await bidResultAnnouncementApi.batchDelete(ids);

      // 从列表中移除
      bidResultAnnouncementList.value = bidResultAnnouncementList.value.filter(item => !ids.includes(item.id));
      pagination.total -= ids.length;

      return response;
    } catch (error) {
      console.error('批量删除评标结果公示失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 提交评标结果公示审核
   * @param {string|number} id - 评标结果公示ID
   * @returns {Promise} 返回提交结果
   */
  const submitBidResultAnnouncement = async id => {
    try {
      submitting.value = true;

      // 调用API
      const response = await bidResultAnnouncementApi.submit(id);

      // 更新当前评标结果公示状态
      if (currentBidResultAnnouncement.value && currentBidResultAnnouncement.value.id === id) {
        currentBidResultAnnouncement.value.auditStatus = 'pending';
        currentBidResultAnnouncement.value.auditStatusText = '待审核';
      }

      return response;
    } catch (error) {
      console.error('提交评标结果公示审核失败:', error);
      throw error;
    } finally {
      submitting.value = false;
    }
  };

  /**
   * 审核评标结果公示
   * @param {string|number} id - 评标结果公示ID
   * @param {Object} data - 审核数据
   * @returns {Promise} 返回审核结果
   */
  const auditBidResultAnnouncement = async (id, data) => {
    try {
      submitting.value = true;

      // 数据转换
      const apiData = bidResultAnnouncementAdapters.adaptAuditData(data);

      // 调用API
      const response = await bidResultAnnouncementApi.audit(id, apiData);

      // 更新当前评标结果公示状态
      if (currentBidResultAnnouncement.value && currentBidResultAnnouncement.value.id === id) {
        currentBidResultAnnouncement.value.auditStatus = data.auditResult === 'approved' ? 'approved' : 'rejected';
        currentBidResultAnnouncement.value.auditStatusText = data.auditResult === 'approved' ? '审核通过' : '审核驳回';
      }

      return response;
    } catch (error) {
      console.error('审核评标结果公示失败:', error);
      throw error;
    } finally {
      submitting.value = false;
    }
  };

  /**
   * 发布评标结果公示
   * @param {string|number} id - 评标结果公示ID
   * @returns {Promise} 返回发布结果
   */
  const publishBidResultAnnouncement = async id => {
    try {
      submitting.value = true;

      // 调用API
      const response = await bidResultAnnouncementApi.publish(id);

      // 更新当前评标结果公示状态
      if (currentBidResultAnnouncement.value && currentBidResultAnnouncement.value.id === id) {
        currentBidResultAnnouncement.value.publishStatus = 'published';
        currentBidResultAnnouncement.value.publishStatusText = '已发布';
      }

      return response;
    } catch (error) {
      console.error('发布评标结果公示失败:', error);
      throw error;
    } finally {
      submitting.value = false;
    }
  };

  /**
   * 撤销评标结果公示
   * @param {string|number} id - 评标结果公示ID
   * @param {Object} data - 撤销数据
   * @returns {Promise} 返回撤销结果
   */
  const revokeBidResultAnnouncement = async (id, data) => {
    try {
      submitting.value = true;

      // 调用API
      const response = await bidResultAnnouncementApi.revoke(id, data);

      // 更新当前评标结果公示状态
      if (currentBidResultAnnouncement.value && currentBidResultAnnouncement.value.id === id) {
        currentBidResultAnnouncement.value.publishStatus = 'revoked';
        currentBidResultAnnouncement.value.publishStatusText = '已撤销';
        currentBidResultAnnouncement.value.revokeReason = data.revokeReason;
      }

      return response;
    } catch (error) {
      console.error('撤销评标结果公示失败:', error);
      throw error;
    } finally {
      submitting.value = false;
    }
  };

  /**
   * 导出评标结果公示数据
   * @param {Object} params - 导出参数
   * @returns {Promise} 返回导出结果
   */
  const exportBidResultAnnouncement = async (params = {}) => {
    try {
      loading.value = true;

      // 合并搜索参数
      const exportParams = {
        ...searchParams,
        ...params
      };

      // 调用API
      const response = await bidResultAnnouncementApi.export(exportParams);

      return response;
    } catch (error) {
      console.error('导出评标结果公示数据失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新搜索条件
   * @param {Object} params - 搜索参数
   */
  const updateSearchParams = params => {
    Object.assign(searchParams, params);
    pagination.page = 1; // 重置页码
  };

  /**
   * 更新分页信息
   * @param {Object} paginationData - 分页数据
   */
  const updatePagination = paginationData => {
    Object.assign(pagination, paginationData);
  };

  /**
   * 更新表单数据
   * @param {Object} data - 表单数据
   */
  const updateFormData = data => {
    Object.assign(formData.value, data);
  };

  /**
   * 重置表单数据
   */
  const resetFormData = () => {
    formData.value = {
      title: '',
      projectId: '',
      sectionId: '',
      announcementTime: '',
      biddingDescription: '',
      evaluationDescription: '',
      candidateInfo: [],
      attachments: []
    };
  };

  /**
   * 更新候选人信息
   * @param {Array} candidates - 候选人信息列表
   */
  const updateCandidateList = candidates => {
    candidateList.value = candidates;
  };

  /**
   * 添加候选人
   * @param {Object} candidate - 候选人信息
   */
  const addCandidate = candidate => {
    candidateList.value.push(candidate);
  };

  /**
   * 移除候选人
   * @param {number} index - 候选人索引
   */
  const removeCandidate = index => {
    candidateList.value.splice(index, 1);
  };

  /**
   * 重置状态
   */
  const resetState = () => {
    bidResultAnnouncementList.value = [];
    currentBidResultAnnouncement.value = null;
    loading.value = false;
    submitting.value = false;
    pagination.page = 1;
    pagination.total = 0;
    searchParams.keyword = '';
    searchParams.status = null;
    searchParams.auditStatus = '';
    searchParams.publishStatus = '';
    searchParams.projectName = '';
    candidateList.value = [];
    auditHistory.value = [];
    resetFormData();
  };

  // 返回状态和方法
  return {
    // 状态
    bidResultAnnouncementList,
    currentBidResultAnnouncement,
    loading,
    submitting,
    pagination,
    searchParams,
    formData,
    candidateList,
    auditHistory,

    // 方法
    getBidResultAnnouncementList,
    getBidResultAnnouncement,
    createBidResultAnnouncement,
    updateBidResultAnnouncement,
    deleteBidResultAnnouncement,
    batchDeleteBidResultAnnouncement,
    submitBidResultAnnouncement,
    auditBidResultAnnouncement,
    publishBidResultAnnouncement,
    revokeBidResultAnnouncement,
    exportBidResultAnnouncement,
    updateSearchParams,
    updatePagination,
    updateFormData,
    resetFormData,
    updateCandidateList,
    addCandidate,
    removeCandidate,
    resetState
  };
});
