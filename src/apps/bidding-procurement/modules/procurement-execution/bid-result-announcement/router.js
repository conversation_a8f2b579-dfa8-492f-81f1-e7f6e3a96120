/**
 * 评标结果公示管理模块路由配置
 *
 * 路由结构：
 * - bid-result-announcement (评标结果公示管理)
 *   - list (评标结果公示列表)
 *   - detail/:id (评标结果公示详情)
 *   - create (新建评标结果公示)
 *   - edit/:id (编辑评标结果公示)
 *   - audit/:id (审核评标结果公示)
 */

export default {
  path: 'bid-result-announcement',
  name: 'BidResultAnnouncement',
  meta: {
    title: '评标结果公示管理',
    isMenu: true
  },
  children: [
    {
      path: 'list',
      name: 'BidResultAnnouncementList',
      component: () => import('./views/list/index.vue'),
      meta: {
        title: '评标结果公示列表'
      }
    },
    {
      path: 'detail/:id',
      name: 'BidResultAnnouncementDetail',
      component: () => import('./views/detail/index.vue'),
      meta: {
        title: '评标结果公示详情'
      }
    },
    {
      path: 'create',
      name: 'BidResultAnnouncementCreate',
      component: () => import('./views/create/index.vue'),
      meta: {
        title: '新建评标结果公示'
      }
    },
    {
      path: 'edit/:id',
      name: 'BidResultAnnouncementEdit',
      component: () => import('./views/create/index.vue'),
      meta: {
        title: '编辑评标结果公示'
      }
    },
    {
      path: 'audit/:id',
      name: 'BidResultAnnouncementAudit',
      component: () => import('./views/audit/index.vue'),
      meta: {
        title: '审核评标结果公示'
      }
    }
  ]
};
