/**
 * 评标结果公示管理 - Adapters层
 *
 * 模板说明：
 * - 纯函数数据转换模板
 * - 负责API数据与前端数据结构的转换
 * - 不调用其他层，保持纯函数特性
 *
 * 占位符说明：
 * - 评标结果公示管理: 模块中文名称
 * - 评标结果公示: 实体中文名称
 * - bidResultAnnouncement: 实体英文名称
 */

/**
 * 评标结果公示管理 数据转换适配器
 */
export const bidResultAnnouncementAdapters = {
  /**
   * 适配列表数据
   * 将API返回的列表数据转换为前端需要的格式
   * @param {Object} apiResponse - API响应数据
   * @returns {Object} 转换后的列表数据
   */
  adaptListData(apiResponse) {
    if (!apiResponse) {
      return {
        list: [],
        total: 0,
        page: 1,
        size: 10
      };
    }
    console.debug('apiResponse', apiResponse);
    const { list, total } = apiResponse;

    return {
      list: list.map(item => this.adaptItemData(item)),
      total: total || 0
    };
  },

  /**
   * 适配单个评标结果公示数据
   * 将API返回的单个评标结果公示数据转换为前端格式
   * @param {Object} apiItem - API返回的单个评标结果公示数据
   * @returns {Object} 转换后的评标结果公示数据
   */
  adaptItemData(apiItem) {
    if (!apiItem) return null;

    return {
      id: apiItem.id || '',
      title: apiItem.title || '',
      projectId: apiItem.projectId || '',
      projectName: apiItem.projectName || '',
      sectionId: apiItem.sectionId || '',
      sectionName: apiItem.sectionName || '',
      announcementTime: this.formatDateTime(apiItem.announcementTime),
      status: apiItem.status !== undefined ? apiItem.status : 1,
      statusText: this.getStatusText(apiItem.status),
      auditStatus: apiItem.auditStatus || 'draft',
      auditStatusText: this.getAuditStatusText(apiItem.auditStatus),
      publishStatus: apiItem.publishStatus || 'unpublished',
      publishStatusText: this.getPublishStatusText(apiItem.publishStatus),
      candidateCount: apiItem.candidateCount || 0,
      createTime: this.formatDateTime(apiItem.createTime),
      updateTime: this.formatDateTime(apiItem.updateTime),
      createUser: apiItem.createUser || '',
      updateUser: apiItem.updateUser || ''
    };
  },

  /**
   * 适配详情数据
   * 将API返回的详情数据转换为前端格式
   * @param {Object} apiResponse - API响应数据
   * @returns {Object} 转换后的详情数据
   */
  adaptDetailData(apiResponse) {
    if (!apiResponse) return null;

    const baseData = this.adaptItemData(apiResponse);

    return {
      ...baseData,
      biddingDescription: apiResponse.biddingDescription || '',
      evaluationDescription: apiResponse.evaluationDescription || '',
      candidateInfo: this.adaptCandidateList(apiResponse.candidateInfo),
      attachments: this.adaptAttachmentList(apiResponse.attachments),
      auditHistory: this.adaptAuditHistory(apiResponse.auditHistory),
      revokeReason: apiResponse.revokeReason || ''
    };
  },

  /**
   * 适配表单数据
   * 将前端表单数据转换为API需要的格式
   * @param {Object} formData - 前端表单数据
   * @returns {Object} 转换后的API数据
   */
  adaptFormData(formData) {
    if (!formData) return {};

    return {
      title: formData.title || '',
      projectId: formData.projectId || '',
      sectionId: formData.sectionId || '',
      announcementTime: formData.announcementTime || '',
      biddingDescription: formData.biddingDescription || '',
      evaluationDescription: formData.evaluationDescription || '',
      candidateInfo: this.adaptCandidateFormData(formData.candidateInfo),
      attachments: this.adaptAttachmentFormData(formData.attachments)
    };
  },

  /**
   * 适配候选人信息列表
   * @param {Array} candidateList - 候选人信息列表
   * @returns {Array} 转换后的候选人信息列表
   */
  adaptCandidateList(candidateList) {
    if (!Array.isArray(candidateList)) return [];

    return candidateList.map(candidate => this.adaptCandidateInfo(candidate));
  },

  /**
   * 适配单个候选人信息
   * @param {Object} candidate - 候选人信息
   * @returns {Object} 转换后的候选人信息
   */
  adaptCandidateInfo(candidate) {
    if (!candidate) return null;

    return {
      id: candidate.id || '',
      rank: candidate.rank || 0,
      companyName: candidate.companyName || '',
      bidAmount: candidate.bidAmount || 0,
      bidAmountText: this.formatCurrency(candidate.bidAmount),
      evaluationScore: candidate.evaluationScore || 0,
      isWinner: candidate.isWinner || false,
      winnerType: candidate.winnerType || '', // 'first': 第一中标候选人, 'second': 第二中标候选人
      winnerTypeText: this.getWinnerTypeText(candidate.winnerType),
      qualificationStatus: candidate.qualificationStatus || 'qualified',
      qualificationStatusText: this.getQualificationStatusText(candidate.qualificationStatus)
    };
  },

  /**
   * 适配候选人表单数据
   * @param {Array} candidateFormData - 候选人表单数据
   * @returns {Array} 转换后的候选人API数据
   */
  adaptCandidateFormData(candidateFormData) {
    if (!Array.isArray(candidateFormData)) return [];

    return candidateFormData.map(candidate => ({
      rank: candidate.rank || 0,
      companyName: candidate.companyName || '',
      bidAmount: candidate.bidAmount || 0,
      evaluationScore: candidate.evaluationScore || 0,
      isWinner: candidate.isWinner || false,
      winnerType: candidate.winnerType || '',
      qualificationStatus: candidate.qualificationStatus || 'qualified'
    }));
  },

  /**
   * 适配附件列表
   * @param {Array} attachmentList - 附件列表
   * @returns {Array} 转换后的附件列表
   */
  adaptAttachmentList(attachmentList) {
    if (!Array.isArray(attachmentList)) return [];

    return attachmentList.map(attachment => ({
      id: attachment.id || '',
      name: attachment.name || '',
      url: attachment.url || '',
      size: attachment.size || 0,
      sizeText: this.formatFileSize(attachment.size),
      type: attachment.type || '',
      uploadTime: this.formatDateTime(attachment.uploadTime)
    }));
  },

  /**
   * 适配附件表单数据
   * @param {Array} attachmentFormData - 附件表单数据
   * @returns {Array} 转换后的附件API数据
   */
  adaptAttachmentFormData(attachmentFormData) {
    if (!Array.isArray(attachmentFormData)) return [];

    return attachmentFormData.map(attachment => ({
      name: attachment.name || '',
      url: attachment.url || '',
      size: attachment.size || 0,
      type: attachment.type || ''
    }));
  },

  /**
   * 适配审核数据
   * @param {Object} auditData - 审核数据
   * @returns {Object} 转换后的审核数据
   */
  adaptAuditData(auditData) {
    if (!auditData) return {};

    return {
      auditResult: auditData.auditResult || '',
      auditComment: auditData.auditComment || ''
    };
  },

  /**
   * 适配审核历史
   * @param {Array} auditHistory - 审核历史
   * @returns {Array} 转换后的审核历史
   */
  adaptAuditHistory(auditHistory) {
    if (!Array.isArray(auditHistory)) return [];

    return auditHistory.map(audit => ({
      id: audit.id || '',
      auditResult: audit.auditResult || '',
      auditResultText: this.getAuditResultText(audit.auditResult),
      auditComment: audit.auditComment || '',
      auditUser: audit.auditUser || '',
      auditTime: this.formatDateTime(audit.auditTime)
    }));
  },

  /**
   * 获取状态文本
   * @param {number} status - 状态值
   * @returns {string} 状态文本
   */
  getStatusText(status) {
    const statusMap = {
      1: '正常',
      0: '禁用'
    };
    return statusMap[status] || '未知';
  },

  /**
   * 获取审核状态文本
   * @param {string} auditStatus - 审核状态
   * @returns {string} 审核状态文本
   */
  getAuditStatusText(auditStatus) {
    const statusMap = {
      draft: '草稿',
      pending: '待审核',
      auditing: '审核中',
      approved: '审核通过',
      rejected: '审核驳回'
    };
    return statusMap[auditStatus] || '未知';
  },

  /**
   * 获取发布状态文本
   * @param {string} publishStatus - 发布状态
   * @returns {string} 发布状态文本
   */
  getPublishStatusText(publishStatus) {
    const statusMap = {
      unpublished: '未发布',
      published: '已发布',
      revoked: '已撤销'
    };
    return statusMap[publishStatus] || '未知';
  },

  /**
   * 获取中标类型文本
   * @param {string} winnerType - 中标类型
   * @returns {string} 中标类型文本
   */
  getWinnerTypeText(winnerType) {
    const typeMap = {
      first: '第一中标候选人',
      second: '第二中标候选人',
      third: '第三中标候选人'
    };
    return typeMap[winnerType] || '';
  },

  /**
   * 获取资格状态文本
   * @param {string} qualificationStatus - 资格状态
   * @returns {string} 资格状态文本
   */
  getQualificationStatusText(qualificationStatus) {
    const statusMap = {
      qualified: '合格',
      unqualified: '不合格'
    };
    return statusMap[qualificationStatus] || '未知';
  },

  /**
   * 获取审核结果文本
   * @param {string} auditResult - 审核结果
   * @returns {string} 审核结果文本
   */
  getAuditResultText(auditResult) {
    const resultMap = {
      approved: '通过',
      rejected: '驳回'
    };
    return resultMap[auditResult] || '未知';
  },

  /**
   * 格式化货币
   * @param {number} amount - 金额
   * @returns {string} 格式化后的货币字符串
   */
  formatCurrency(amount) {
    if (!amount && amount !== 0) return '';

    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY'
    }).format(amount);
  },

  /**
   * 格式化文件大小
   * @param {number} size - 文件大小（字节）
   * @returns {string} 格式化后的文件大小
   */
  formatFileSize(size) {
    if (!size && size !== 0) return '';

    const units = ['B', 'KB', 'MB', 'GB'];
    let index = 0;
    let fileSize = size;

    while (fileSize >= 1024 && index < units.length - 1) {
      fileSize /= 1024;
      index++;
    }

    return `${fileSize.toFixed(2)} ${units[index]}`;
  },

  /**
   * 格式化日期时间
   * @param {string|Date} dateTime - 日期时间
   * @returns {string} 格式化后的日期时间字符串
   */
  formatDateTime(dateTime) {
    if (!dateTime) return '';

    try {
      const date = new Date(dateTime);
      if (isNaN(date.getTime())) return '';

      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (error) {
      console.warn('日期格式化失败:', error);
      return '';
    }
  },

  /**
   * 格式化日期
   * @param {string|Date} date - 日期
   * @returns {string} 格式化后的日期字符串
   */
  formatDate(date) {
    if (!date) return '';

    try {
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) return '';

      return dateObj.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      console.warn('日期格式化失败:', error);
      return '';
    }
  },

  /**
   * 适配搜索参数
   * 将前端搜索条件转换为API参数
   * @param {Object} searchParams - 前端搜索参数
   * @returns {Object} 转换后的API参数
   */
  adaptSearchParams(searchParams) {
    if (!searchParams) return {};

    return {
      keyword: searchParams.keyword || '',
      status: searchParams.status !== undefined ? searchParams.status : null,
      auditStatus: searchParams.auditStatus || '',
      publishStatus: searchParams.publishStatus || '',
      projectName: searchParams.projectName || '',
      startDate: searchParams.startDate || '',
      endDate: searchParams.endDate || ''
    };
  }
};

// 默认导出
export default bidResultAnnouncementAdapters;
