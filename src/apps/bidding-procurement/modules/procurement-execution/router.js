/**
 * 采购执行管理 - 路由组织配置
 *
 * 基于 router-standards.md 规范配置
 * - 中间层模块路由组织
 * - 整合所有子模块路由配置
 * - 建立完整的路由层级结构
 */

// 导入子模块路由配置
import projectSectionRouter from './project-section/router.js';
import announcementRouter from './announcement-management/router.js';
import clarificationRouter from './clarification-management/router.js';
import bidResultAnnouncementRouter from './bid-result-announcement/router.js';
import awardResultAnnouncementRouter from './award-result-announcement/router.js';
// import awardResultRouter from './award-result/router.js';
// import contractPerformanceRouter from './contract-performance/router.js';
import bidFailureRouter from './bid-failure-management/router.js';

export default {
  path: 'procurement-execution',
  name: 'ProcurementExecution',
  meta: {
    title: '采购执行管理',
    isMenu: true
  },
  children: [
    projectSectionRouter,
    announcementRouter,
    clarificationRouter,
    bidResultAnnouncementRouter,
    awardResultAnnouncementRouter,
    bidFailureRouter
    // awardResultRouter,
    // contractPerformanceRouter
  ]
};
