/**
 * 公告管理 - Store层
 * 
 * 模板说明：
 * - 基于 Pinia 的状态管理模板
 * - 集成API层和Adapters层调用
 * - 包含完整的业务逻辑封装
 * 
 * 占位符说明：
 * - 公告管理: 模块中文名称
 * - 公告: 实体中文名称
 * - announcement: 实体英文名称
 * - useAnnouncementStore: Store名称
 */

import { defineStore } from 'pinia';
import { ref, reactive } from 'vue';
import { announcementApi } from './api/index.js';
import { announcementAdapters } from './adapters/index.js';

/**
 * 公告管理 Store
 */
export const useAnnouncementStore = defineStore('announcement', () => {
  // 状态定义
  const announcementList = ref([]);
  const currentAnnouncement = ref(null);
  const loading = ref(false);
  const pagination = reactive({
    page: 1,
    size: 10,
    total: 0
  });

  // 搜索条件
  const searchParams = reactive({
    keyword: '',
    auditStatus: null,
    publishStatus: null,
    procurementType: null
  });

  /**
   * 获取公告列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回处理后的列表数据
   */
  const getAnnouncementList = async (params = {}) => {
    try {
      loading.value = true;
      
      // 合并查询参数
      const queryParams = {
        ...searchParams,
        ...params,
        page: pagination.page,
        size: pagination.size
      };

      // 调用API
      const response = await announcementApi.getAnnouncementList(queryParams);
      
      // 数据转换
      const adaptedData = announcementAdapters.formatAnnouncementList(response);
      
      // 更新状态
      announcementList.value = adaptedData.list;
      pagination.total = adaptedData.total;
      
      return adaptedData;
    } catch (error) {
      console.error('获取公告列表失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 根据ID获取公告详情
   * @param {string|number} id - 公告ID
   * @returns {Promise} 返回处理后的详情数据
   */
  const getAnnouncement = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await announcementApi.getAnnouncementDetail(id);
      
      // 数据转换
      const adaptedData = announcementAdapters.formatAnnouncementDetail(response);
      
      // 更新状态
      currentAnnouncement.value = adaptedData;
      
      return adaptedData;
    } catch (error) {
      console.error('获取公告详情失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 创建新公告
   * @param {Object} data - 公告数据
   * @returns {Promise} 返回创建结果
   */
  const createAnnouncement = async (data) => {
    try {
      loading.value = true;
      
      // 数据转换
      const apiData = announcementAdapters.formatAnnouncementForm(data);
      
      // 调用API
      const response = await announcementApi.createAnnouncement(apiData);
      
      // 数据转换
      const adaptedData = announcementAdapters.formatAnnouncementDetail(response);
      
      return adaptedData;
    } catch (error) {
      console.error('创建公告失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新公告信息
   * @param {string|number} id - 公告ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 返回更新结果
   */
  const updateAnnouncement = async (id, data) => {
    try {
      loading.value = true;
      
      // 数据转换
      const apiData = announcementAdapters.formatAnnouncementForm(data);
      
      // 调用API
      const response = await announcementApi.updateAnnouncement(id, apiData);
      
      // 数据转换
      const adaptedData = announcementAdapters.formatAnnouncementDetail(response);
      
      // 更新当前公告状态
      if (currentAnnouncement.value && currentAnnouncement.value.id === id) {
        currentAnnouncement.value = adaptedData;
      }
      
      return adaptedData;
    } catch (error) {
      console.error('更新公告失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 删除公告
   * @param {string|number} id - 公告ID
   * @returns {Promise} 返回删除结果
   */
  const deleteAnnouncement = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await announcementApi.deleteAnnouncement(id);
      
      // 从列表中移除
      const index = announcementList.value.findIndex(item => item.id === id);
      if (index > -1) {
        announcementList.value.splice(index, 1);
        pagination.total--;
      }
      
      return response;
    } catch (error) {
      console.error('删除公告失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 提交公告审核
   * @param {string|number} id - 公告ID
   * @returns {Promise} 返回提交结果
   */
  const submitAnnouncement = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await announcementApi.submitAnnouncement(id);
      
      // 更新列表中的状态
      const item = announcementList.value.find(item => item.id === id);
      if (item) {
        item.auditStatus = 'auditing';
        item.auditStatusText = '审核中';
      }
      
      return response;
    } catch (error) {
      console.error('提交公告审核失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 审核公告
   * @param {string|number} id - 公告ID
   * @param {Object} data - 审核数据
   * @returns {Promise} 返回审核结果
   */
  const auditAnnouncement = async (id, data) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await announcementApi.auditAnnouncement(id, data);
      
      // 更新列表中的状态
      const item = announcementList.value.find(item => item.id === id);
      if (item) {
        item.auditStatus = data.auditResult === 'approved' ? 'approved' : 'rejected';
        item.auditStatusText = data.auditResult === 'approved' ? '审核通过' : '审核驳回';
      }
      
      return response;
    } catch (error) {
      console.error('审核公告失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 发布公告
   * @param {string|number} id - 公告ID
   * @returns {Promise} 返回发布结果
   */
  const publishAnnouncement = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await announcementApi.publishAnnouncement(id);
      
      // 更新列表中的状态
      const item = announcementList.value.find(item => item.id === id);
      if (item) {
        item.publishStatus = 'published';
        item.publishStatusText = '已发布';
      }
      
      return response;
    } catch (error) {
      console.error('发布公告失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 撤销公告
   * @param {string|number} id - 公告ID
   * @param {string} reason - 撤销原因
   * @returns {Promise} 返回撤销结果
   */
  const revokeAnnouncement = async (id, reason = '') => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await announcementApi.revokeAnnouncement(id, reason);
      
      // 更新列表中的状态
      const item = announcementList.value.find(item => item.id === id);
      if (item) {
        item.publishStatus = 'revoked';
        item.publishStatusText = '已撤销';
      }
      
      return response;
    } catch (error) {
      console.error('撤销公告失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新搜索条件
   * @param {Object} params - 搜索参数
   */
  const updateSearchParams = (params) => {
    Object.assign(searchParams, params);
    pagination.page = 1; // 重置页码
  };

  /**
   * 更新分页信息
   * @param {Object} paginationData - 分页数据
   */
  const updatePagination = (paginationData) => {
    Object.assign(pagination, paginationData);
  };

  /**
   * 重置状态
   */
  const resetState = () => {
    announcementList.value = [];
    currentAnnouncement.value = null;
    loading.value = false;
    pagination.page = 1;
    pagination.total = 0;
    searchParams.keyword = '';
    searchParams.auditStatus = null;
    searchParams.publishStatus = null;
    searchParams.procurementType = null;
  };

  // 返回状态和方法
  return {
    // 状态
    announcementList,
    currentAnnouncement,
    loading,
    pagination,
    searchParams,
    
    // 方法
    getAnnouncementList,
    getAnnouncement,
    createAnnouncement,
    updateAnnouncement,
    deleteAnnouncement,
    submitAnnouncement,
    auditAnnouncement,
    publishAnnouncement,
    revokeAnnouncement,
    updateSearchParams,
    updatePagination,
    resetState
  };
});
