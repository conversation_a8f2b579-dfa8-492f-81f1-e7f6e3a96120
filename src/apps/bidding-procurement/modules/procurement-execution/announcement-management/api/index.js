/**
 * 公告管理 - API层
 *
 * 模板说明：
 * - 标准的API接口封装模板
 * - 基于 window.$http 的统一请求方法
 * - 包含完整的CRUD操作接口
 *
 * 占位符说明：
 * - 公告管理: 模块中文名称
 * - 公告: 实体中文名称
 * - announcement: 实体英文名称
 * - /api/announcements: API基础路径
 */

/**
 * 公告管理相关API接口
 */
export const announcementApi = {
  /**
   * 获取公告列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页数量
   * @param {string} params.keyword - 搜索关键词
   * @param {string} params.auditStatus - 审核状态筛选
   * @param {string} params.publishStatus - 发布状态筛选
   * @param {string} params.procurementType - 采购类型筛选
   * @returns {Promise} 返回公告列表数据
   */
  getAnnouncementList(params = {}) {
    return window.$http.post('/api/announcements/list', {
      page: params.page || 1,
      size: params.size || 10,
      keyword: params.keyword || '',
      auditStatus: params.auditStatus,
      publishStatus: params.publishStatus,
      procurementType: params.procurementType
    });
  },

  /**
   * 根据ID获取公告详情
   * @param {string|number} id - 公告ID
   * @returns {Promise} 返回公告详情数据
   */
  getAnnouncementDetail(id) {
    return window.$http.fetch(`/api/announcements/${id}`);
  },

  /**
   * 创建新公告
   * @param {Object} data - 公告数据
   * @param {string} data.title - 公告标题
   * @param {string} data.sectionId - 关联标段ID
   * @param {boolean} data.isPublic - 是否公示
   * @param {string} data.content - 公告内容
   * @param {Date} data.startTime - 公告开始时间
   * @param {Date} data.clarificationDeadline - 澄清截止时间
   * @param {Date} data.openingTime - 开标时间
   * @param {Array} data.attachments - 附件列表
   * @param {Array} data.auditDocuments - 审核依据
   * @param {string} data.remark - 备注
   * @returns {Promise} 返回创建结果
   */
  createAnnouncement(data) {
    return window.$http.post('/api/announcements/create', {
      title: data.title,
      sectionId: data.sectionId,
      isPublic: data.isPublic || false,
      content: data.content,
      startTime: data.startTime,
      clarificationDeadline: data.clarificationDeadline,
      openingTime: data.openingTime,
      attachments: data.attachments || [],
      auditDocuments: data.auditDocuments || [],
      remark: data.remark || ''
    });
  },

  /**
   * 更新公告信息
   * @param {string|number} id - 公告ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 返回更新结果
   */
  updateAnnouncement(id, data) {
    return window.$http.post(`/api/announcements/update/${id}`, {
      title: data.title,
      sectionId: data.sectionId,
      isPublic: data.isPublic,
      content: data.content,
      startTime: data.startTime,
      clarificationDeadline: data.clarificationDeadline,
      openingTime: data.openingTime,
      attachments: data.attachments,
      auditDocuments: data.auditDocuments,
      remark: data.remark
    });
  },

  /**
   * 删除公告
   * @param {string|number} id - 公告ID
   * @returns {Promise} 返回删除结果
   */
  deleteAnnouncement(id) {
    return window.$http.post(`/api/announcements/delete/${id}`);
  },

  /**
   * 提交公告审核
   * @param {string|number} id - 公告ID
   * @returns {Promise} 返回提交结果
   */
  submitAnnouncement(id) {
    return window.$http.post(`/api/announcements/submit/${id}`);
  },

  /**
   * 审核公告
   * @param {string|number} id - 公告ID
   * @param {Object} data - 审核数据
   * @param {string} data.auditResult - 审核结果 (approved/rejected)
   * @param {string} data.auditRemark - 审核意见
   * @returns {Promise} 返回审核结果
   */
  auditAnnouncement(id, data) {
    return window.$http.post(`/api/announcements/audit/${id}`, {
      auditResult: data.auditResult,
      auditRemark: data.auditRemark || ''
    });
  },

  /**
   * 发布公告
   * @param {string|number} id - 公告ID
   * @returns {Promise} 返回发布结果
   */
  publishAnnouncement(id) {
    return window.$http.post(`/api/announcements/publish/${id}`);
  },

  /**
   * 撤销公告
   * @param {string|number} id - 公告ID
   * @param {string} reason - 撤销原因
   * @returns {Promise} 返回撤销结果
   */
  revokeAnnouncement(id, reason = '') {
    return window.$http.post(`/api/announcements/revoke/${id}`, {
      reason: reason
    });
  },

  /**
   * 批量删除公告
   * @param {Array} ids - 公告ID数组
   * @returns {Promise} 返回批量删除结果
   */
  batchDelete(ids) {
    return window.$http.post('/api/announcements/batch-delete', {
      ids: ids
    });
  },

  /**
   * 导出公告数据
   * @param {Object} params - 导出参数
   * @returns {Promise} 返回导出文件信息
   */
  export(params = {}) {
    return window.$http.post('/api/announcements/export', {
      keyword: params.keyword || '',
      auditStatus: params.auditStatus,
      publishStatus: params.publishStatus,
      procurementType: params.procurementType
    });
  }
};

// 默认导出
export default announcementApi;
