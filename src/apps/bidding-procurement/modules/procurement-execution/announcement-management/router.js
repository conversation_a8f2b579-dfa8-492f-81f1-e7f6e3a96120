/**
 * 公告管理模块路由配置
 * 
 * 路由结构：
 * - announcement-management (公告管理)
 *   - list (公告列表)
 *   - detail/:id (公告详情)
 *   - create (新建公告)
 *   - edit/:id (编辑公告)
 */

export default {
  path: 'announcement-management',
  name: 'AnnouncementManagement',
  meta: { 
    title: '公告管理', 
    isMenu: true 
  },
  children: [
    {
      path: 'list',
      name: 'AnnouncementList',
      component: () => import('./views/list/index.vue'),
      meta: { 
        title: '公告列表' 
      }
    },
    {
      path: 'detail/:id',
      name: 'AnnouncementDetail',
      component: () => import('./views/detail/index.vue'),
      meta: { 
        title: '公告详情' 
      }
    },
    {
      path: 'create',
      name: 'AnnouncementCreate',
      component: () => import('./views/create/index.vue'),
      meta: { 
        title: '新建公告' 
      }
    },
    {
      path: 'edit/:id',
      name: 'AnnouncementEdit',
      component: () => import('./views/create/index.vue'),
      meta: { 
        title: '编辑公告' 
      }
    }
  ]
};
