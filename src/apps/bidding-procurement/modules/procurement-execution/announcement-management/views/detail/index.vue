<!-- 公告管理 - 详情页面 -->
<template>
  <funi-detail 
    :steps="stepsConfig"
    :detailHeadOption="detailHeadOption"
    @onStepChange="handleStepChange"
  >
    <template #step1>
      <div class="announcement-detail">
        <!-- 基本信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="公告标题">
              {{ announcementData.title || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="项目名称">
              {{ announcementData.projectName || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="标段名称">
              {{ announcementData.sectionName || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="采购类型">
              {{ announcementData.procurementType || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="采购方式">
              {{ announcementData.procurementMethod || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="是否公示">
              <el-tag :type="announcementData.isPublic ? 'success' : 'info'">
                {{ announcementData.isPublic ? '是' : '否' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="审核状态">
              <el-tag :type="getAuditStatusType(announcementData.auditStatus)">
                {{ announcementData.auditStatusText || '--' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="发布状态">
              <el-tag :type="getPublishStatusType(announcementData.publishStatus)">
                {{ announcementData.publishStatusText || '--' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 时间信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>时间信息</span>
            </div>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="公告开始时间">
              {{ announcementData.startTime || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="澄清截止时间">
              {{ announcementData.clarificationDeadline || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="开标时间">
              {{ announcementData.openingTime || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ announcementData.createdAt || '--' }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 公告内容 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>公告内容</span>
            </div>
          </template>
          <div class="content-container" v-html="announcementData.content || '暂无内容'"></div>
        </el-card>

        <!-- 附件信息 -->
        <el-card class="detail-card" shadow="never" v-if="announcementData.attachments && announcementData.attachments.length > 0">
          <template #header>
            <div class="card-header">
              <span>附件信息</span>
            </div>
          </template>
          <funi-file-table 
            :fileList="announcementData.attachments"
            :readonly="true"
          />
        </el-card>

        <!-- 审核依据 -->
        <el-card class="detail-card" shadow="never" v-if="announcementData.auditDocuments && announcementData.auditDocuments.length > 0">
          <template #header>
            <div class="card-header">
              <span>审核依据</span>
            </div>
          </template>
          <funi-file-table 
            :fileList="announcementData.auditDocuments"
            :readonly="true"
          />
        </el-card>

        <!-- 备注信息 -->
        <el-card class="detail-card" shadow="never" v-if="announcementData.remark">
          <template #header>
            <div class="card-header">
              <span>备注信息</span>
            </div>
          </template>
          <div class="remark-content">
            {{ announcementData.remark }}
          </div>
        </el-card>

        <!-- 审核操作区域 -->
        <el-card class="detail-card" shadow="never" v-if="isAuditMode && announcementData.auditStatus === 'auditing'">
          <template #header>
            <div class="card-header">
              <span>审核操作</span>
            </div>
          </template>
          <funi-bus-audit-drawer
            ref="auditDrawerRef"
            :businessId="announcementData.id"
            businessType="ANNOUNCEMENT"
            @onAuditSuccess="handleAuditSuccess"
          />
        </el-card>
      </div>
    </template>
  </funi-detail>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';
import { reactive, ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { useAnnouncementStore } from '../../store.js';

// 路由和Store
const router = useRouter();
const route = useRoute();
const announcementStore = useAnnouncementStore();

// 审核抽屉引用
const auditDrawerRef = ref(null);

// 是否为审核模式
const isAuditMode = computed(() => route.query.mode === 'audit');

// 公告数据
const announcementData = reactive({
  id: '',
  title: '',
  projectName: '',
  sectionName: '',
  procurementType: '',
  procurementMethod: '',
  isPublic: false,
  auditStatus: '',
  auditStatusText: '',
  publishStatus: '',
  publishStatusText: '',
  startTime: '',
  clarificationDeadline: '',
  openingTime: '',
  content: '',
  attachments: [],
  auditDocuments: [],
  remark: '',
  createdAt: '',
  createdBy: ''
});

// 步骤配置
const stepsConfig = reactive([
  {
    title: '公告信息',
    name: 'step1'
  }
]);

// 详情页头部配置
const detailHeadOption = reactive({
  title: computed(() => {
    if (isAuditMode.value) return '审核公告';
    return '公告详情';
  }),
  btns: computed(() => {
    const btns = [];
    
    // 根据状态和模式显示不同按钮
    if (isAuditMode.value && announcementData.auditStatus === 'auditing') {
      btns.push({
        label: '通过',
        type: 'success',
        on: {
          click: () => handleAudit('approved')
        }
      });
      btns.push({
        label: '驳回',
        type: 'danger',
        on: {
          click: () => handleAudit('rejected')
        }
      });
    } else if (announcementData.auditStatus === 'draft') {
      btns.push({
        label: '编辑',
        type: 'primary',
        on: {
          click: () => handleEdit()
        }
      });
    }
    
    btns.push({
      label: '返回',
      on: {
        click: () => handleBack()
      }
    });
    
    return btns;
  })
});

// 获取审核状态类型
const getAuditStatusType = (status) => {
  const typeMap = {
    'draft': 'info',
    'auditing': 'warning',
    'approved': 'success',
    'rejected': 'danger'
  };
  return typeMap[status] || 'info';
};

// 获取发布状态类型
const getPublishStatusType = (status) => {
  const typeMap = {
    'unpublished': 'info',
    'published': 'success',
    'revoked': 'danger'
  };
  return typeMap[status] || 'info';
};

// 事件处理函数
const handleStepChange = (step) => {
  console.log('步骤切换:', step);
};

const handleEdit = () => {
  router.push({
    path: `/bidding-procurement/procurement-execution/announcement-management/edit/${announcementData.id}`
  });
};

const handleAudit = async (result) => {
  try {
    const auditData = {
      auditResult: result,
      auditRemark: result === 'approved' ? '审核通过' : '审核驳回'
    };
    
    await announcementStore.auditAnnouncement(announcementData.id, auditData);
    ElMessage.success('审核操作成功');
    
    // 重新加载数据
    await loadData();
  } catch (error) {
    ElMessage.error('审核操作失败');
  }
};

const handleAuditSuccess = () => {
  ElMessage.success('审核成功');
  loadData();
};

const handleBack = () => {
  router.push('/bidding-procurement/procurement-execution/announcement-management/list');
};

// 加载数据
const loadData = async () => {
  if (route.params.id) {
    try {
      const data = await announcementStore.getAnnouncement(route.params.id);
      Object.assign(announcementData, data);
    } catch (error) {
      ElMessage.error('加载数据失败');
    }
  }
};

// 组件挂载
onMounted(() => {
  loadData();
  console.log('公告管理详情页面已挂载，模式:', isAuditMode.value ? '审核' : '查看');
});
</script>

<style scoped>
.announcement-detail {
  padding: 20px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  font-weight: bold;
  font-size: 16px;
}

.content-container {
  min-height: 200px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.remark-content {
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  line-height: 1.6;
}
</style>
