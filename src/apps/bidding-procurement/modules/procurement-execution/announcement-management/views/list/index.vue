<!-- 公告管理 - 列表页面 -->
<template>
  <funi-list-page-v2 ref="listPageRef" :cardTab="cardTabConfig" @headBtnClick="handleHeadBtnClick" />
</template>

<script setup lang="jsx">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElNotification, ElMessageBox } from 'element-plus';
import { useAnnouncementStore } from '../../store.js';

// ==================== 基础变量配置 ====================
const listPageRef = ref();
const router = useRouter();
const announcementStore = useAnnouncementStore();

// ==================== 权限配置 ====================
const auths = reactive({
  export: 'announcement_export', // 导出权限
  add: 'announcement_add', // 新增权限
  delete: 'announcement_delete', // 删除权限
  audit: 'announcement_audit', // 审核权限
  edit: 'announcement_edit', // 编辑权限
  submit: 'announcement_submit', // 提交权限
  publish: 'announcement_publish', // 发布权限
  revoke: 'announcement_revoke', // 撤销权限
  detail: 'announcement_detail' // 详情权限
});

// ==================== 数据状态管理 ====================
const listPageParams = ref({});

// ==================== 核心业务函数 ====================

/**
 * 跳转到详情页面
 */
const goDetail = row => {
  router.push({
    path: '/bidding-procurement/procurement-execution/announcement-management/detail/' + row.id,
    query: {
      title: row.title
    }
  });
};

/**
 * 删除公告
 */
const deleteItem = async id => {
  try {
    await announcementStore.deleteAnnouncement(id);
    ElNotification({
      title: '提示',
      message: '删除成功',
      type: 'success',
      duration: 2000
    });
    listPageRef.value.reload({ resetPage: false });
  } catch (error) {
    console.error('删除失败:', error);
    ElNotification({
      title: '错误',
      message: '删除失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

/**
 * 跳转到编辑页面
 */
const editItem = row => {
  router.push({
    path: '/bidding-procurement/procurement-execution/announcement-management/edit/' + row.id,
    query: {
      title: row.title
    }
  });
};

/**
 * 提交审核
 */
const submitItem = async row => {
  try {
    await announcementStore.submitAnnouncement(row.id);
    ElNotification({
      title: '提示',
      message: '提交审核成功',
      type: 'success',
      duration: 2000
    });
    listPageRef.value.reload({ resetPage: false });
  } catch (error) {
    console.error('提交审核失败:', error);
    ElNotification({
      title: '错误',
      message: '提交审核失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

/**
 * 审核公告
 */
const auditItem = row => {
  router.push({
    path: '/bidding-procurement/procurement-execution/announcement-management/detail/' + row.id,
    query: {
      title: row.title,
      mode: 'audit'
    }
  });
};

/**
 * 发布公告
 */
const publishItem = async row => {
  try {
    await announcementStore.publishAnnouncement(row.id);
    ElNotification({
      title: '提示',
      message: '发布成功',
      type: 'success',
      duration: 2000
    });
    listPageRef.value.reload({ resetPage: false });
  } catch (error) {
    console.error('发布失败:', error);
    ElNotification({
      title: '错误',
      message: '发布失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

/**
 * 撤销公告
 */
const revokeItem = row => {
  ElMessageBox.prompt('请输入撤销原因', '撤销公告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'textarea',
    inputPlaceholder: '请输入撤销原因'
  })
    .then(async ({ value }) => {
      try {
        await announcementStore.revokeAnnouncement(row.id, value);
        ElNotification({
          title: '提示',
          message: '撤销成功',
          type: 'success',
          duration: 2000
        });
        listPageRef.value.reload({ resetPage: false });
      } catch (error) {
        console.error('撤销失败:', error);
        ElNotification({
          title: '错误',
          message: '撤销失败，请重试',
          type: 'error',
          duration: 2000
        });
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// ==================== 动态按钮生成器 ====================
const generateActionButtons = row => {
  const buttons = [];

  // 查看详情按钮（始终显示）
  buttons.push(
    <el-link type="primary" v-auth={auths.detail} onClick={() => goDetail(row)} style={{ marginRight: '10px' }}>
      查看
    </el-link>
  );

  // 根据状态显示不同操作按钮
  switch (row.auditStatus) {
    case 'draft':
      buttons.push(
        <el-link type="primary" v-auth={auths.edit} onClick={() => editItem(row)} style={{ marginRight: '10px' }}>
          编辑
        </el-link>,
        <el-popconfirm title="确认删除该公告吗?" onConfirm={() => deleteItem(row.id)}>
          {{
            reference: () => (
              <el-link type="danger" v-auth={auths.delete} style={{ marginRight: '10px' }}>
                删除
              </el-link>
            )
          }}
        </el-popconfirm>,
        <el-link type="success" v-auth={auths.submit} onClick={() => submitItem(row)} style={{ marginRight: '10px' }}>
          提交
        </el-link>
      );
      break;
    case 'auditing':
      buttons.push(
        <el-link type="warning" v-auth={auths.audit} onClick={() => auditItem(row)} style={{ marginRight: '10px' }}>
          审核
        </el-link>,
        <el-link type="danger" v-auth={auths.revoke} onClick={() => revokeItem(row)} style={{ marginRight: '10px' }}>
          撤销
        </el-link>
      );
      break;
    case 'approved':
      if (row.publishStatus === 'unpublished') {
        buttons.push(
          <el-link
            type="success"
            v-auth={auths.publish}
            onClick={() => publishItem(row)}
            style={{ marginRight: '10px' }}
          >
            发布
          </el-link>,
          <el-link type="danger" v-auth={auths.revoke} onClick={() => revokeItem(row)} style={{ marginRight: '10px' }}>
            撤销
          </el-link>
        );
      }
      break;
  }

  return <div>{buttons}</div>;
};

// 表格列配置
const columns = reactive([
  {
    label: '公告标题',
    prop: 'title',
    fixed: 'left',
    width: 200,
    render: ({ row }) => {
      return (
        <el-link type="primary" onClick={() => goDetail(row)}>
          {row.title || '--'}
        </el-link>
      );
    }
  },
  {
    label: '项目名称',
    prop: 'projectName',
    width: 180
  },
  {
    label: '标段名称',
    prop: 'sectionName',
    width: 150
  },
  {
    label: '采购类型',
    prop: 'procurementType',
    width: 100
  },
  {
    label: '审核状态',
    prop: 'auditStatusText',
    width: 100,
    render: ({ row }) => {
      const typeMap = {
        draft: 'info',
        auditing: 'warning',
        approved: 'success',
        rejected: 'danger'
      };
      return <el-tag type={typeMap[row.auditStatus] || 'info'}>{row.auditStatusText || '--'}</el-tag>;
    }
  },
  {
    label: '发布状态',
    prop: 'publishStatusText',
    width: 100,
    render: ({ row }) => {
      const typeMap = {
        unpublished: 'info',
        published: 'success',
        revoked: 'danger'
      };
      return <el-tag type={typeMap[row.publishStatus] || 'info'}>{row.publishStatusText || '--'}</el-tag>;
    }
  },
  {
    label: '开标时间',
    prop: 'openingTime',
    width: 150
  },
  {
    label: '创建人',
    prop: 'createdBy',
    width: 100
  },
  {
    label: '创建时间',
    prop: 'createdAt',
    width: 150
  },
  {
    label: '操作',
    prop: 'actions',
    width: 200,
    fixed: 'right',
    render: ({ row }) => generateActionButtons(row)
  }
]);

// 数据加载函数
const loadData = async (pageParams, searchParams) => {
  listPageParams.value = searchParams;

  // 更新store中的搜索参数和分页信息
  announcementStore.updateSearchParams(searchParams);
  announcementStore.updatePagination(pageParams);

  // 获取数据
  const result = await announcementStore.getAnnouncementList();

  return {
    data: result.list,
    total: result.total
  };
};

// 页签配置
const cardTabConfig = computed(() => {
  return [
    {
      label: '全部公告',
      key: 'all',
      curdOption: {
        columns,
        lodaData: loadData,
        btns: [
          { key: 'add', label: '新建公告', type: 'primary' },
          { key: 'export', label: '导出', auth: auths.export }
        ],
        fixedButtons: true,
        reloadOnActive: true
      },
      searchConfig: { pageCode: 'announcement_management_list' }
    }
  ];
});

// 新增功能
const addItem = () => {
  router.push({
    path: '/bidding-procurement/procurement-execution/announcement-management/create'
  });
};

// 导出功能
const exportData = async () => {
  try {
    await announcementStore.export(listPageParams.value);
    ElNotification({
      title: '提示',
      message: '导出成功',
      type: 'success',
      duration: 2000
    });
  } catch (error) {
    console.error('导出失败:', error);
    ElNotification({
      title: '错误',
      message: '导出失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

// 头部按钮点击事件处理
const handleHeadBtnClick = key => {
  switch (key) {
    case 'add':
      addItem();
      break;
    case 'export':
      exportData();
      break;
    default:
      break;
  }
};

// ==================== 生命周期钩子 ====================
onMounted(() => {
  console.log('公告管理列表页面已挂载');
});
</script>
