<!-- 公告管理 - 新建/编辑页面 -->
<template>
  <funi-detail 
    :steps="stepsConfig"
    :detailHeadOption="detailHeadOption"
    @onStepChange="handleStepChange"
  >
    <template #step1>
      <funi-form
        ref="formRef"
        :schema="formSchema"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      />
    </template>
  </funi-detail>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';
import { reactive, ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { useAnnouncementStore } from '../../store.js';

// 路由和Store
const router = useRouter();
const route = useRoute();
const announcementStore = useAnnouncementStore();

// 表单引用
const formRef = ref(null);

// 页面模式
const pageMode = computed(() => {
  if (route.path.includes('/create')) return 'create';
  if (route.path.includes('/edit')) return 'edit';
  return 'view';
});

// 表单数据
const formData = reactive({
  title: '',
  sectionId: '',
  sectionName: '',
  projectName: '',
  procurementType: '',
  procurementMethod: '',
  isPublic: false,
  content: '',
  startTime: '',
  clarificationDeadline: '',
  openingTime: '',
  attachments: [],
  auditDocuments: [],
  remark: ''
});

// 步骤配置
const stepsConfig = reactive([
  {
    title: pageMode.value === 'create' ? '新建公告' : '编辑公告',
    name: 'step1'
  }
]);

// 详情页头部配置
const detailHeadOption = reactive({
  title: computed(() => {
    return pageMode.value === 'create' ? '新建公告' : '编辑公告';
  }),
  btns: computed(() => {
    return [
      {
        label: '保存草稿',
        type: 'default',
        on: {
          click: () => handleSave(false)
        }
      },
      {
        label: '保存并提交',
        type: 'primary',
        on: {
          click: () => handleSave(true)
        }
      },
      {
        label: '返回',
        on: {
          click: () => handleBack()
        }
      }
    ];
  })
});

// 搜索标段
const searchSections = async (query) => {
  // 这里应该调用标段搜索API
  console.log('搜索标段:', query);
};

// 表单配置
const formSchema = reactive([
  {
    prop: 'title',
    label: '公告标题',
    component: 'el-input',
    componentProps: {
      placeholder: '请输入公告标题',
      maxlength: 255,
      showWordLimit: true
    },
    span: 24
  },
  {
    prop: 'sectionId',
    label: '选择标段',
    component: 'el-select',
    componentProps: {
      placeholder: '请选择标段',
      filterable: true,
      options: []
    },
    span: 12
  },
  {
    prop: 'procurementType',
    label: '采购类型',
    component: 'el-select',
    componentProps: {
      placeholder: '请选择采购类型',
      options: [
        { label: '工程', value: '工程' },
        { label: '货物', value: '货物' },
        { label: '服务', value: '服务' }
      ]
    },
    span: 12
  },
  {
    prop: 'procurementMethod',
    label: '采购方式',
    component: 'el-select',
    componentProps: {
      placeholder: '请选择采购方式',
      options: [
        { label: '公开招标', value: '公开招标' },
        { label: '邀请招标', value: '邀请招标' },
        { label: '竞争性谈判', value: '竞争性谈判' },
        { label: '单一来源', value: '单一来源' }
      ]
    },
    span: 12
  },
  {
    prop: 'isPublic',
    label: '是否公示',
    component: 'el-switch',
    componentProps: {
      activeText: '是',
      inactiveText: '否'
    },
    span: 12
  },
  {
    prop: 'startTime',
    label: '公告开始时间',
    component: 'el-date-picker',
    componentProps: {
      type: 'datetime',
      placeholder: '请选择公告开始时间',
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss'
    },
    span: 12
  },
  {
    prop: 'clarificationDeadline',
    label: '澄清截止时间',
    component: 'el-date-picker',
    componentProps: {
      type: 'datetime',
      placeholder: '请选择澄清截止时间',
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss'
    },
    span: 12
  },
  {
    prop: 'openingTime',
    label: '开标时间',
    component: 'el-date-picker',
    componentProps: {
      type: 'datetime',
      placeholder: '请选择开标时间',
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss'
    },
    span: 12
  },
  {
    prop: 'content',
    label: '公告内容',
    component: 'el-input',
    componentProps: {
      type: 'textarea',
      rows: 8,
      placeholder: '请输入公告内容'
    },
    span: 24
  },
  {
    prop: 'remark',
    label: '备注',
    component: 'el-input',
    componentProps: {
      type: 'textarea',
      rows: 4,
      placeholder: '请输入备注信息',
      maxlength: 500,
      showWordLimit: true
    },
    span: 24
  }
]);

// 表单验证规则
const formRules = reactive({
  title: [
    { required: true, message: '请输入公告标题', trigger: 'blur' },
    { min: 2, max: 255, message: '长度在 2 到 255 个字符', trigger: 'blur' }
  ],
  sectionId: [
    { required: true, message: '请选择标段', trigger: 'change' }
  ],
  procurementType: [
    { required: true, message: '请选择采购类型', trigger: 'change' }
  ],
  procurementMethod: [
    { required: true, message: '请选择采购方式', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入公告内容', trigger: 'blur' }
  ],
  startTime: [
    { required: true, message: '请选择公告开始时间', trigger: 'change' }
  ],
  clarificationDeadline: [
    { required: true, message: '请选择澄清截止时间', trigger: 'change' }
  ],
  openingTime: [
    { required: true, message: '请选择开标时间', trigger: 'change' }
  ]
});

// 事件处理函数
const handleStepChange = (step) => {
  console.log('步骤切换:', step);
};

const handleSave = async (submitAfterSave = false) => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) return;

    // 时间验证
    if (new Date(formData.clarificationDeadline) >= new Date(formData.openingTime)) {
      ElMessage.error('澄清截止时间不能晚于开标时间');
      return;
    }

    // 保存数据
    let result;
    if (pageMode.value === 'create') {
      result = await announcementStore.createAnnouncement(formData);
      ElMessage.success('创建成功');
    } else {
      result = await announcementStore.updateAnnouncement(route.params.id, formData);
      ElMessage.success('更新成功');
    }
    
    // 如果需要提交审核
    if (submitAfterSave && result) {
      try {
        const announcementId = result.id || route.params.id;
        await announcementStore.submitAnnouncement(announcementId);
        ElMessage.success('提交审核成功');
      } catch (error) {
        ElMessage.warning('保存成功，但提交审核失败');
      }
    }
    
    // 返回列表页
    handleBack();
  } catch (error) {
    ElMessage.error(pageMode.value === 'create' ? '创建失败' : '更新失败');
  }
};

const handleBack = () => {
  router.push('/bidding-procurement/procurement-execution/announcement-management/list');
};

// 加载数据
const loadData = async () => {
  if (pageMode.value === 'edit' && route.params.id) {
    try {
      const data = await announcementStore.getAnnouncement(route.params.id);
      Object.assign(formData, data);
    } catch (error) {
      ElMessage.error('加载数据失败');
    }
  }
};

// 组件挂载
onMounted(() => {
  loadData();
  console.log('公告管理新建/编辑页面已挂载，模式:', pageMode.value);
});
</script>

<style scoped>
/* 页面特定样式 */
</style>
