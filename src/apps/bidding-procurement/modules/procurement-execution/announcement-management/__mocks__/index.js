/**
 * 公告管理 - Mock数据
 * 
 * 用于开发和测试阶段的模拟数据
 */

export const mockAnnouncementList = [
  {
    id: '1',
    title: '某某项目招标公告',
    sectionId: 'section_001',
    sectionName: '第一标段',
    projectName: '某某基础设施建设项目',
    procurementType: '工程',
    procurementMethod: '公开招标',
    isPublic: true,
    content: '<p>根据《中华人民共和国招标投标法》等有关法律法规，现对某某基础设施建设项目进行公开招标...</p>',
    startTime: '2024-01-15 09:00:00',
    clarificationDeadline: '2024-01-20 17:00:00',
    openingTime: '2024-01-25 09:30:00',
    auditStatus: 'approved',
    auditStatusText: '审核通过',
    publishStatus: 'published',
    publishStatusText: '已发布',
    attachments: [
      {
        id: 'file_001',
        name: '招标文件.pdf',
        url: '/files/tender_doc_001.pdf',
        size: '2.5MB'
      }
    ],
    auditDocuments: [
      {
        id: 'audit_001',
        name: '审核依据文件.pdf',
        url: '/files/audit_doc_001.pdf',
        size: '1.2MB'
      }
    ],
    remark: '重要项目，请及时关注',
    createdBy: '张三',
    createdAt: '2024-01-10 14:30:00',
    updatedBy: '李四',
    updatedAt: '2024-01-12 16:45:00'
  },
  {
    id: '2',
    title: '办公设备采购招标公告',
    sectionId: 'section_002',
    sectionName: '第二标段',
    projectName: '办公设备采购项目',
    procurementType: '货物',
    procurementMethod: '竞争性谈判',
    isPublic: false,
    content: '<p>为满足办公需求，现对办公设备进行采购招标...</p>',
    startTime: '2024-01-18 09:00:00',
    clarificationDeadline: '2024-01-22 17:00:00',
    openingTime: '2024-01-26 14:00:00',
    auditStatus: 'auditing',
    auditStatusText: '审核中',
    publishStatus: 'unpublished',
    publishStatusText: '未发布',
    attachments: [],
    auditDocuments: [],
    remark: '',
    createdBy: '王五',
    createdAt: '2024-01-16 10:20:00',
    updatedBy: '王五',
    updatedAt: '2024-01-16 10:20:00'
  },
  {
    id: '3',
    title: '物业服务招标公告',
    sectionId: 'section_003',
    sectionName: '第三标段',
    projectName: '物业服务采购项目',
    procurementType: '服务',
    procurementMethod: '邀请招标',
    isPublic: true,
    content: '<p>为提升物业服务质量，现对物业服务进行招标...</p>',
    startTime: '2024-01-20 09:00:00',
    clarificationDeadline: '2024-01-25 17:00:00',
    openingTime: '2024-01-30 10:00:00',
    auditStatus: 'draft',
    auditStatusText: '待审核',
    publishStatus: 'unpublished',
    publishStatusText: '未发布',
    attachments: [
      {
        id: 'file_002',
        name: '服务要求说明.docx',
        url: '/files/service_req_002.docx',
        size: '800KB'
      }
    ],
    auditDocuments: [],
    remark: '需要重点关注服务质量要求',
    createdBy: '赵六',
    createdAt: '2024-01-18 11:15:00',
    updatedBy: '赵六',
    updatedAt: '2024-01-18 15:30:00'
  }
];

export const mockAnnouncementDetail = {
  id: '1',
  title: '某某项目招标公告',
  sectionId: 'section_001',
  sectionName: '第一标段',
  projectName: '某某基础设施建设项目',
  procurementType: '工程',
  procurementMethod: '公开招标',
  isPublic: true,
  content: `
    <h3>招标公告</h3>
    <p>根据《中华人民共和国招标投标法》等有关法律法规，现对某某基础设施建设项目进行公开招标，欢迎符合条件的投标人参与投标。</p>
    
    <h4>一、项目概况</h4>
    <p>项目名称：某某基础设施建设项目</p>
    <p>项目编号：PROJECT_2024_001</p>
    <p>建设地点：某某市某某区</p>
    <p>建设规模：总建筑面积约10000平方米</p>
    
    <h4>二、投标人资格要求</h4>
    <p>1. 具有独立法人资格</p>
    <p>2. 具有建筑工程施工总承包二级及以上资质</p>
    <p>3. 近三年内无重大质量安全事故</p>
    
    <h4>三、投标文件递交</h4>
    <p>递交时间：2024年1月25日上午9:30</p>
    <p>递交地点：某某市公共资源交易中心</p>
  `,
  startTime: '2024-01-15 09:00:00',
  clarificationDeadline: '2024-01-20 17:00:00',
  openingTime: '2024-01-25 09:30:00',
  auditStatus: 'approved',
  auditStatusText: '审核通过',
  publishStatus: 'published',
  publishStatusText: '已发布',
  attachments: [
    {
      id: 'file_001',
      name: '招标文件.pdf',
      url: '/files/tender_doc_001.pdf',
      size: '2.5MB',
      uploadTime: '2024-01-10 14:30:00'
    },
    {
      id: 'file_002',
      name: '工程量清单.xlsx',
      url: '/files/quantity_list_001.xlsx',
      size: '1.8MB',
      uploadTime: '2024-01-10 14:35:00'
    }
  ],
  auditDocuments: [
    {
      id: 'audit_001',
      name: '审核依据文件.pdf',
      url: '/files/audit_doc_001.pdf',
      size: '1.2MB',
      uploadTime: '2024-01-12 10:00:00'
    }
  ],
  remark: '重要项目，请及时关注',
  createdBy: '张三',
  createdAt: '2024-01-10 14:30:00',
  updatedBy: '李四',
  updatedAt: '2024-01-12 16:45:00',
  auditHistory: [
    {
      id: 'audit_history_001',
      auditor: '审核员A',
      auditTime: '2024-01-12 16:45:00',
      auditResult: 'approved',
      auditRemark: '内容完整，符合要求，同意发布'
    }
  ]
};

export const mockSearchOptions = {
  auditStatusOptions: [
    { label: '全部', value: '' },
    { label: '待审核', value: 'draft' },
    { label: '审核中', value: 'auditing' },
    { label: '审核通过', value: 'approved' },
    { label: '审核驳回', value: 'rejected' }
  ],
  publishStatusOptions: [
    { label: '全部', value: '' },
    { label: '未发布', value: 'unpublished' },
    { label: '已发布', value: 'published' },
    { label: '已撤销', value: 'revoked' }
  ],
  procurementTypeOptions: [
    { label: '全部', value: '' },
    { label: '工程', value: '工程' },
    { label: '货物', value: '货物' },
    { label: '服务', value: '服务' }
  ]
};

export default {
  mockAnnouncementList,
  mockAnnouncementDetail,
  mockSearchOptions
};
