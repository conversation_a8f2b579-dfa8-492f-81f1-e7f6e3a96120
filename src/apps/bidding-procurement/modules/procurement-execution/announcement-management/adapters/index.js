/**
 * 公告管理 - Adapters层
 * 
 * 模板说明：
 * - 纯函数数据转换模板
 * - 负责API数据与前端数据结构的转换
 * - 不调用其他层，保持纯函数特性
 * 
 * 占位符说明：
 * - 公告管理: 模块中文名称
 * - 公告: 实体中文名称
 * - announcement: 实体英文名称
 */

/**
 * 公告管理 数据转换适配器
 */
export const announcementAdapters = {
  /**
   * 适配列表数据
   * 将API返回的列表数据转换为前端需要的格式
   * @param {Object} apiResponse - API响应数据
   * @returns {Object} 转换后的列表数据
   */
  formatAnnouncementList(apiResponse) {
    if (!apiResponse || !apiResponse.data) {
      return {
        list: [],
        total: 0,
        page: 1,
        size: 10
      };
    }

    const { data, total, page, size } = apiResponse;
    
    return {
      list: data.map(item => this.adaptItemData(item)),
      total: total || 0,
      page: page || 1,
      size: size || 10
    };
  },

  /**
   * 适配单个公告数据
   * 将API返回的单个公告数据转换为前端格式
   * @param {Object} apiItem - API返回的单个公告数据
   * @returns {Object} 转换后的公告数据
   */
  adaptItemData(apiItem) {
    if (!apiItem) return null;

    return {
      id: apiItem.id || '',
      title: apiItem.title || '',
      sectionId: apiItem.sectionId || '',
      sectionName: apiItem.sectionName || '',
      projectName: apiItem.projectName || '',
      procurementType: apiItem.procurementType || '',
      procurementMethod: apiItem.procurementMethod || '',
      isPublic: apiItem.isPublic || false,
      content: apiItem.content || '',
      startTime: this.formatDateTime(apiItem.startTime),
      clarificationDeadline: this.formatDateTime(apiItem.clarificationDeadline),
      openingTime: this.formatDateTime(apiItem.openingTime),
      auditStatus: apiItem.auditStatus || 'draft',
      auditStatusText: this.getAuditStatusText(apiItem.auditStatus),
      publishStatus: apiItem.publishStatus || 'unpublished',
      publishStatusText: this.getPublishStatusText(apiItem.publishStatus),
      attachments: apiItem.attachments || [],
      auditDocuments: apiItem.auditDocuments || [],
      remark: apiItem.remark || '',
      createdBy: apiItem.createdBy || '',
      createdAt: this.formatDateTime(apiItem.createdAt),
      updatedBy: apiItem.updatedBy || '',
      updatedAt: this.formatDateTime(apiItem.updatedAt)
    };
  },

  /**
   * 适配详情数据
   * 将API返回的详情数据转换为前端格式
   * @param {Object} apiResponse - API响应数据
   * @returns {Object} 转换后的详情数据
   */
  formatAnnouncementDetail(apiResponse) {
    if (!apiResponse) return null;
    
    return this.adaptItemData(apiResponse);
  },

  /**
   * 适配表单数据
   * 将前端表单数据转换为API需要的格式
   * @param {Object} formData - 前端表单数据
   * @returns {Object} 转换后的API数据
   */
  formatAnnouncementForm(formData) {
    if (!formData) return {};

    return {
      title: formData.title || '',
      sectionId: formData.sectionId || '',
      isPublic: formData.isPublic || false,
      content: formData.content || '',
      startTime: formData.startTime || null,
      clarificationDeadline: formData.clarificationDeadline || null,
      openingTime: formData.openingTime || null,
      attachments: formData.attachments || [],
      auditDocuments: formData.auditDocuments || [],
      remark: formData.remark || ''
    };
  },

  /**
   * 获取审核状态文本
   * @param {string} auditStatus - 审核状态值
   * @returns {string} 审核状态文本
   */
  getAuditStatusText(auditStatus) {
    const statusMap = {
      'draft': '待审核',
      'auditing': '审核中',
      'approved': '审核通过',
      'rejected': '审核驳回'
    };
    return statusMap[auditStatus] || '未知';
  },

  /**
   * 获取发布状态文本
   * @param {string} publishStatus - 发布状态值
   * @returns {string} 发布状态文本
   */
  getPublishStatusText(publishStatus) {
    const statusMap = {
      'unpublished': '未发布',
      'published': '已发布',
      'revoked': '已撤销'
    };
    return statusMap[publishStatus] || '未知';
  },

  /**
   * 格式化日期时间
   * @param {string|Date} dateTime - 日期时间
   * @returns {string} 格式化后的日期时间字符串
   */
  formatDateTime(dateTime) {
    if (!dateTime) return '';
    
    try {
      const date = new Date(dateTime);
      if (isNaN(date.getTime())) return '';
      
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (error) {
      console.warn('日期格式化失败:', error);
      return '';
    }
  },

  /**
   * 格式化日期
   * @param {string|Date} date - 日期
   * @returns {string} 格式化后的日期字符串
   */
  formatDate(date) {
    if (!date) return '';
    
    try {
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) return '';
      
      return dateObj.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      console.warn('日期格式化失败:', error);
      return '';
    }
  },

  /**
   * 适配搜索参数
   * 将前端搜索条件转换为API参数
   * @param {Object} searchParams - 前端搜索参数
   * @returns {Object} 转换后的API参数
   */
  adaptSearchParams(searchParams) {
    if (!searchParams) return {};

    return {
      keyword: searchParams.keyword || '',
      auditStatus: searchParams.auditStatus || null,
      publishStatus: searchParams.publishStatus || null,
      procurementType: searchParams.procurementType || null,
      startDate: searchParams.startDate || '',
      endDate: searchParams.endDate || ''
    };
  },

  /**
   * 获取操作按钮配置
   * 根据公告状态返回可用的操作按钮
   * @param {Object} item - 公告数据
   * @returns {Array} 操作按钮配置数组
   */
  getActionButtons(item) {
    const buttons = [];
    
    if (!item) return buttons;

    // 查看详情按钮（始终显示）
    buttons.push({
      text: '查看',
      type: 'primary',
      action: 'view',
      icon: 'View'
    });

    // 根据状态显示不同操作按钮
    switch (item.auditStatus) {
      case 'draft':
        buttons.push(
          { text: '编辑', type: 'primary', action: 'edit', icon: 'Edit' },
          { text: '删除', type: 'danger', action: 'delete', icon: 'Delete' },
          { text: '提交', type: 'success', action: 'submit', icon: 'Upload' }
        );
        break;
      case 'auditing':
        buttons.push(
          { text: '审核', type: 'warning', action: 'audit', icon: 'Check' },
          { text: '撤销', type: 'danger', action: 'revoke', icon: 'Close' }
        );
        break;
      case 'approved':
        if (item.publishStatus === 'unpublished') {
          buttons.push(
            { text: '发布', type: 'success', action: 'publish', icon: 'Promotion' },
            { text: '撤销', type: 'danger', action: 'revoke', icon: 'Close' }
          );
        }
        break;
    }

    return buttons;
  }
};

// 默认导出
export default announcementAdapters;
