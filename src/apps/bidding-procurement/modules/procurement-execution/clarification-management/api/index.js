/**
 * 补遗澄清答疑管理 - API层
 *
 * 模板说明：
 * - 标准的API接口封装模板
 * - 基于 window.$http 的统一请求方法
 * - 包含完整的CRUD操作接口
 *
 * 占位符说明：
 * - 补遗澄清答疑管理: 模块中文名称
 * - 补遗澄清答疑: 实体中文名称
 * - clarification: 实体英文名称
 * - /api/clarifications: API基础路径
 */

/**
 * 补遗澄清答疑管理相关API接口
 */
export const clarificationApi = {
  /**
   * 获取补遗澄清答疑列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页数量
   * @param {string} params.keyword - 搜索关键词
   * @param {string} params.projectName - 项目名称
   * @param {string} params.sectionName - 标段名称
   * @param {string} params.type - 类型筛选（补遗/澄清/答疑）
   * @param {number} params.status - 状态筛选
   * @returns {Promise} 返回补遗澄清答疑列表数据
   */
  getList(params = {}) {
    return window.$http.post('/api/clarifications/list', {
      page: params.page || 1,
      size: params.size || 10,
      keyword: params.keyword || '',
      projectName: params.projectName || '',
      sectionName: params.sectionName || '',
      type: params.type || '',
      status: params.status
    });
  },

  /**
   * 根据ID获取补遗澄清答疑详情
   * @param {string|number} id - 补遗澄清答疑ID
   * @returns {Promise} 返回补遗澄清答疑详情数据
   */
  getDetail(id) {
    return window.$http.fetch(`/api/clarifications/${id}`);
  },

  /**
   * 创建新补遗澄清答疑
   * @param {Object} data - 补遗澄清答疑数据
   * @param {string} data.projectSectionId - 项目标段ID
   * @param {string} data.title - 公告标题
   * @param {string} data.type - 类型（补遗/澄清/答疑）
   * @param {boolean} data.isPublic - 是否公示
   * @param {string} data.content - 补遗澄清答疑说明
   * @param {Array} data.attachments - 审核依据文件
   * @returns {Promise} 返回创建结果
   */
  create(data) {
    return window.$http.post('/api/clarifications/create', {
      projectSectionId: data.projectSectionId,
      title: data.title,
      type: data.type,
      isPublic: data.isPublic || false,
      content: data.content,
      attachments: data.attachments || []
    });
  },

  /**
   * 更新补遗澄清答疑信息
   * @param {string|number} id - 补遗澄清答疑ID
   * @param {Object} data - 更新数据
   * @param {string} data.title - 公告标题
   * @param {string} data.type - 类型
   * @param {boolean} data.isPublic - 是否公示
   * @param {string} data.content - 补遗澄清答疑说明
   * @param {Array} data.attachments - 审核依据文件
   * @returns {Promise} 返回更新结果
   */
  update(id, data) {
    return window.$http.post(`/api/clarifications/update/${id}`, {
      title: data.title,
      type: data.type,
      isPublic: data.isPublic,
      content: data.content,
      attachments: data.attachments
    });
  },

  /**
   * 删除补遗澄清答疑
   * @param {string|number} id - 补遗澄清答疑ID
   * @returns {Promise} 返回删除结果
   */
  delete(id) {
    return window.$http.post(`/api/clarifications/delete/${id}`);
  },

  /**
   * 提交审核
   * @param {string|number} id - 补遗澄清答疑ID
   * @returns {Promise} 返回提交结果
   */
  submit(id) {
    return window.$http.post(`/api/clarifications/submit/${id}`);
  },

  /**
   * 审核操作
   * @param {string|number} id - 补遗澄清答疑ID
   * @param {Object} data - 审核数据
   * @param {string} data.action - 审核动作（approve/reject）
   * @param {string} data.comment - 审核意见
   * @returns {Promise} 返回审核结果
   */
  audit(id, data) {
    return window.$http.post(`/api/clarifications/audit/${id}`, {
      action: data.action,
      comment: data.comment || ''
    });
  },

  /**
   * 发布补遗澄清答疑
   * @param {string|number} id - 补遗澄清答疑ID
   * @returns {Promise} 返回发布结果
   */
  publish(id) {
    return window.$http.post(`/api/clarifications/publish/${id}`);
  },

  /**
   * 撤销补遗澄清答疑
   * @param {string|number} id - 补遗澄清答疑ID
   * @param {string} reason - 撤销原因
   * @returns {Promise} 返回撤销结果
   */
  revoke(id, reason) {
    return window.$http.post(`/api/clarifications/revoke/${id}`, {
      reason: reason || ''
    });
  },

  /**
   * 批量删除补遗澄清答疑
   * @param {Array} ids - 补遗澄清答疑ID数组
   * @returns {Promise} 返回批量删除结果
   */
  batchDelete(ids) {
    return window.$http.post('/api/clarifications/batch-delete', {
      ids: ids
    });
  },

  /**
   * 导出补遗澄清答疑数据
   * @param {Object} params - 导出参数
   * @param {string} params.keyword - 搜索关键词
   * @param {string} params.type - 类型筛选
   * @param {number} params.status - 状态筛选
   * @returns {Promise} 返回导出文件信息
   */
  export(params = {}) {
    return window.$http.post('/api/clarifications/export', {
      keyword: params.keyword || '',
      type: params.type || '',
      status: params.status
    });
  }
};

// 默认导出
export default clarificationApi;
