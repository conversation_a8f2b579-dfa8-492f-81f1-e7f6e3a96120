/**
 * 补遗澄清答疑管理 - 路由配置
 *
 * 路由结构：
 * - 列表页：/clarification-management
 * - 详情页：/clarification-management/detail/:id
 * - 新建页：/clarification-management/create
 * - 编辑页：/clarification-management/edit/:id
 * - 审核页：/clarification-management/audit/:id
 */

export default {
  path: 'clarification-management',
  name: 'ClarificationManagement',
  meta: {
    title: '补遗澄清答疑管理',
    isMenu: true
  },
  children: [
    {
      path: 'list',
      name: 'ClarificationList',
      component: () => import('./views/list/index.vue'),
      meta: {
        title: '补遗澄清答疑列表',
        keepAlive: true
      }
    },
    {
      path: 'detail/:id',
      name: 'ClarificationDetail',
      component: () => import('./views/detail/index.vue'),
      meta: {
        title: '补遗澄清答疑详情',
        activeMenu: '/procurement-execution/clarification-management'
      }
    },
    {
      path: 'create',
      name: 'ClarificationCreate',
      component: () => import('./views/create/index.vue'),
      meta: {
        title: '新建补遗澄清答疑',
        activeMenu: '/procurement-execution/clarification-management'
      }
    },
    {
      path: 'edit/:id',
      name: 'ClarificationEdit',
      component: () => import('./views/create/index.vue'),
      meta: {
        title: '编辑补遗澄清答疑',
        activeMenu: '/procurement-execution/clarification-management'
      }
    },
    {
      path: 'audit/:id',
      name: 'ClarificationAudit',
      component: () => import('./views/audit/index.vue'),
      meta: {
        title: '审核补遗澄清答疑',
        activeMenu: '/procurement-execution/clarification-management'
      }
    }
  ]
};
