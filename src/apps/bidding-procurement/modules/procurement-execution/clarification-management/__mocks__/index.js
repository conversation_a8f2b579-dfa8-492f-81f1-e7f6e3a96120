/**
 * 补遗澄清答疑管理 - Mock数据
 * 
 * 用于开发和测试阶段的模拟数据
 */

export const mockClarificationList = [
  {
    id: '1',
    title: '某某公司某某项目第一标段补遗公告',
    projectName: '某某市政道路建设项目',
    projectCode: 'PROJ-2024-001',
    sectionName: '第一标段',
    sectionCode: 'SEC-001',
    type: 'addendum',
    typeText: '补遗',
    isPublic: true,
    isPublicText: '是',
    content: '根据招标文件要求，现对以下内容进行补遗说明...',
    status: 4,
    statusText: '已发布',
    attachments: [
      {
        id: 'att1',
        name: '补遗说明文件.pdf',
        url: '/files/addendum-1.pdf',
        size: '2.5MB'
      }
    ],
    createTime: '2024-01-15 09:30:00',
    updateTime: '2024-01-15 14:20:00',
    publishTime: '2024-01-15 16:00:00',
    createUser: '张三',
    updateUser: '李四',
    auditUser: '王五',
    auditTime: '2024-01-15 15:30:00',
    auditComment: '审核通过'
  },
  {
    id: '2',
    title: '某某公司某某项目第二标段澄清公告',
    projectName: '某某市政道路建设项目',
    projectCode: 'PROJ-2024-001',
    sectionName: '第二标段',
    sectionCode: 'SEC-002',
    type: 'clarification',
    typeText: '澄清',
    isPublic: false,
    isPublicText: '否',
    content: '针对投标人提出的疑问，现澄清如下...',
    status: 2,
    statusText: '审核中',
    attachments: [],
    createTime: '2024-01-16 10:00:00',
    updateTime: '2024-01-16 11:30:00',
    publishTime: '',
    createUser: '赵六',
    updateUser: '赵六',
    auditUser: '',
    auditTime: '',
    auditComment: ''
  },
  {
    id: '3',
    title: '某某公司某某项目第三标段答疑公告',
    projectName: '某某水利工程项目',
    projectCode: 'PROJ-2024-002',
    sectionName: '第三标段',
    sectionCode: 'SEC-003',
    type: 'qa',
    typeText: '答疑',
    isPublic: true,
    isPublicText: '是',
    content: '现对投标人提出的问题统一答疑...',
    status: 0,
    statusText: '草稿',
    attachments: [
      {
        id: 'att2',
        name: '答疑文件.docx',
        url: '/files/qa-1.docx',
        size: '1.2MB'
      }
    ],
    createTime: '2024-01-17 08:45:00',
    updateTime: '2024-01-17 08:45:00',
    publishTime: '',
    createUser: '孙七',
    updateUser: '孙七',
    auditUser: '',
    auditTime: '',
    auditComment: ''
  }
];

export const mockClarificationDetail = {
  id: '1',
  title: '某某公司某某项目第一标段补遗公告',
  projectName: '某某市政道路建设项目',
  projectCode: 'PROJ-2024-001',
  sectionName: '第一标段',
  sectionCode: 'SEC-001',
  type: 'addendum',
  typeText: '补遗',
  isPublic: true,
  isPublicText: '是',
  content: '<p>根据招标文件要求，现对以下内容进行补遗说明：</p><p>1. 施工工期调整为180天</p><p>2. 材料规格要求更新</p>',
  status: 4,
  statusText: '已发布',
  attachments: [
    {
      id: 'att1',
      name: '补遗说明文件.pdf',
      url: '/files/addendum-1.pdf',
      size: '2.5MB'
    }
  ],
  createTime: '2024-01-15 09:30:00',
  updateTime: '2024-01-15 14:20:00',
  publishTime: '2024-01-15 16:00:00',
  createUser: '张三',
  updateUser: '李四',
  auditUser: '王五',
  auditTime: '2024-01-15 15:30:00',
  auditComment: '审核通过',
  projectInfo: {
    projectName: '某某市政道路建设项目',
    projectCode: 'PROJ-2024-001',
    projectOwner: '某某市政府',
    procurementMethod: '公开招标',
    sectionName: '第一标段',
    sectionCode: 'SEC-001',
    budgetAmount: '5000000',
    procurementType: '工程类',
    agencyName: '某某招标代理有限公司',
    contactPerson: '李经理',
    contactPhone: '138-0000-0000',
    documentObtainMethod: '现场购买',
    bidDeadline: '2024-02-15 14:30:00',
    openingTime: '2024-02-15 15:00:00',
    openingLocation: '某某市政府会议室'
  }
};

export const mockProjectSections = [
  {
    id: 'ps1',
    projectName: '某某市政道路建设项目',
    projectCode: 'PROJ-2024-001',
    sectionName: '第一标段',
    sectionCode: 'SEC-001',
    budgetAmount: 5000000,
    projectOwner: '某某市政府'
  },
  {
    id: 'ps2',
    projectName: '某某市政道路建设项目',
    projectCode: 'PROJ-2024-001',
    sectionName: '第二标段',
    sectionCode: 'SEC-002',
    budgetAmount: 3000000,
    projectOwner: '某某市政府'
  },
  {
    id: 'ps3',
    projectName: '某某水利工程项目',
    projectCode: 'PROJ-2024-002',
    sectionName: '第三标段',
    sectionCode: 'SEC-003',
    budgetAmount: 8000000,
    projectOwner: '某某水利局'
  }
];

export const mockTypeOptions = [
  { value: 'addendum', label: '补遗' },
  { value: 'clarification', label: '澄清' },
  { value: 'qa', label: '答疑' }
];

export const mockStatusOptions = [
  { value: 0, label: '草稿' },
  { value: 1, label: '待审核' },
  { value: 2, label: '审核中' },
  { value: 3, label: '审核通过' },
  { value: 4, label: '已发布' },
  { value: 5, label: '审核驳回' },
  { value: 6, label: '已撤销' }
];

export default {
  mockClarificationList,
  mockClarificationDetail,
  mockProjectSections,
  mockTypeOptions,
  mockStatusOptions
};
