/**
 * 补遗澄清答疑管理 - Store层
 * 
 * 模板说明：
 * - 基于 Pinia 的状态管理模板
 * - 集成API层和Adapters层调用
 * - 包含完整的业务逻辑封装
 * 
 * 占位符说明：
 * - 补遗澄清答疑管理: 模块中文名称
 * - 补遗澄清答疑: 实体中文名称
 * - clarification: 实体英文名称
 * - useClarificationStore: Store名称
 */

import { defineStore } from 'pinia';
import { ref, reactive } from 'vue';
import { clarificationApi } from './api/index.js';
import { clarificationAdapters } from './adapters/index.js';

/**
 * 补遗澄清答疑管理 Store
 */
export const useClarificationStore = defineStore('clarification', () => {
  // 状态定义
  const clarificationList = ref([]);
  const currentClarification = ref(null);
  const loading = ref(false);
  const pagination = reactive({
    page: 1,
    size: 10,
    total: 0
  });

  // 搜索条件
  const searchParams = reactive({
    keyword: '',
    projectName: '',
    sectionName: '',
    type: '',
    status: null
  });

  /**
   * 获取补遗澄清答疑列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回处理后的列表数据
   */
  const getClarificationList = async (params = {}) => {
    try {
      loading.value = true;
      
      // 合并查询参数
      const queryParams = {
        ...searchParams,
        ...params,
        page: pagination.page,
        size: pagination.size
      };

      // 调用API
      const response = await clarificationApi.getList(queryParams);
      
      // 数据转换
      const adaptedData = clarificationAdapters.adaptListData(response);
      
      // 更新状态
      clarificationList.value = adaptedData.list;
      pagination.total = adaptedData.total;
      
      return adaptedData;
    } catch (error) {
      console.error('获取补遗澄清答疑列表失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 根据ID获取补遗澄清答疑详情
   * @param {string|number} id - 补遗澄清答疑ID
   * @returns {Promise} 返回处理后的详情数据
   */
  const getClarification = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await clarificationApi.getDetail(id);
      
      // 数据转换
      const adaptedData = clarificationAdapters.adaptDetailData(response);
      
      // 更新状态
      currentClarification.value = adaptedData;
      
      return adaptedData;
    } catch (error) {
      console.error('获取补遗澄清答疑详情失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 创建新补遗澄清答疑
   * @param {Object} data - 补遗澄清答疑数据
   * @returns {Promise} 返回创建结果
   */
  const createClarification = async (data) => {
    try {
      loading.value = true;
      
      // 数据转换
      const apiData = clarificationAdapters.adaptCreateData(data);
      
      // 调用API
      const response = await clarificationApi.create(apiData);
      
      // 数据转换
      const adaptedData = clarificationAdapters.adaptDetailData(response);
      
      return adaptedData;
    } catch (error) {
      console.error('创建补遗澄清答疑失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新补遗澄清答疑信息
   * @param {string|number} id - 补遗澄清答疑ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 返回更新结果
   */
  const updateClarification = async (id, data) => {
    try {
      loading.value = true;
      
      // 数据转换
      const apiData = clarificationAdapters.adaptUpdateData(data);
      
      // 调用API
      const response = await clarificationApi.update(id, apiData);
      
      // 数据转换
      const adaptedData = clarificationAdapters.adaptDetailData(response);
      
      // 更新当前补遗澄清答疑状态
      if (currentClarification.value && currentClarification.value.id === id) {
        currentClarification.value = adaptedData;
      }
      
      return adaptedData;
    } catch (error) {
      console.error('更新补遗澄清答疑失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 删除补遗澄清答疑
   * @param {string|number} id - 补遗澄清答疑ID
   * @returns {Promise} 返回删除结果
   */
  const deleteClarification = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await clarificationApi.delete(id);
      
      // 从列表中移除
      const index = clarificationList.value.findIndex(item => item.id === id);
      if (index > -1) {
        clarificationList.value.splice(index, 1);
        pagination.total--;
      }
      
      return response;
    } catch (error) {
      console.error('删除补遗澄清答疑失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 提交审核
   * @param {string|number} id - 补遗澄清答疑ID
   * @returns {Promise} 返回提交结果
   */
  const submitClarification = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await clarificationApi.submit(id);
      
      // 更新当前补遗澄清答疑状态
      if (currentClarification.value && currentClarification.value.id === id) {
        currentClarification.value.status = 1; // 待审核
        currentClarification.value.statusText = '待审核';
      }
      
      return response;
    } catch (error) {
      console.error('提交审核失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 审核操作
   * @param {string|number} id - 补遗澄清答疑ID
   * @param {Object} data - 审核数据
   * @returns {Promise} 返回审核结果
   */
  const auditClarification = async (id, data) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await clarificationApi.audit(id, data);
      
      // 更新当前补遗澄清答疑状态
      if (currentClarification.value && currentClarification.value.id === id) {
        const newStatus = data.action === 'approve' ? 3 : 5; // 审核通过或驳回
        currentClarification.value.status = newStatus;
        currentClarification.value.statusText = clarificationAdapters.getStatusText(newStatus);
        currentClarification.value.auditComment = data.comment;
      }
      
      return response;
    } catch (error) {
      console.error('审核操作失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 发布补遗澄清答疑
   * @param {string|number} id - 补遗澄清答疑ID
   * @returns {Promise} 返回发布结果
   */
  const publishClarification = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await clarificationApi.publish(id);
      
      // 更新当前补遗澄清答疑状态
      if (currentClarification.value && currentClarification.value.id === id) {
        currentClarification.value.status = 4; // 已发布
        currentClarification.value.statusText = '已发布';
        currentClarification.value.publishTime = new Date().toISOString();
      }
      
      return response;
    } catch (error) {
      console.error('发布补遗澄清答疑失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 撤销补遗澄清答疑
   * @param {string|number} id - 补遗澄清答疑ID
   * @param {string} reason - 撤销原因
   * @returns {Promise} 返回撤销结果
   */
  const revokeClarification = async (id, reason) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await clarificationApi.revoke(id, reason);
      
      // 更新当前补遗澄清答疑状态
      if (currentClarification.value && currentClarification.value.id === id) {
        currentClarification.value.status = 6; // 已撤销
        currentClarification.value.statusText = '已撤销';
      }
      
      return response;
    } catch (error) {
      console.error('撤销补遗澄清答疑失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新搜索条件
   * @param {Object} params - 搜索参数
   */
  const updateSearchParams = (params) => {
    Object.assign(searchParams, params);
    pagination.page = 1; // 重置页码
  };

  /**
   * 更新分页信息
   * @param {Object} paginationData - 分页数据
   */
  const updatePagination = (paginationData) => {
    Object.assign(pagination, paginationData);
  };

  /**
   * 重置状态
   */
  const resetState = () => {
    clarificationList.value = [];
    currentClarification.value = null;
    loading.value = false;
    pagination.page = 1;
    pagination.total = 0;
    searchParams.keyword = '';
    searchParams.projectName = '';
    searchParams.sectionName = '';
    searchParams.type = '';
    searchParams.status = null;
  };

  // 返回状态和方法
  return {
    // 状态
    clarificationList,
    currentClarification,
    loading,
    pagination,
    searchParams,
    
    // 方法
    getClarificationList,
    getClarification,
    createClarification,
    updateClarification,
    deleteClarification,
    submitClarification,
    auditClarification,
    publishClarification,
    revokeClarification,
    updateSearchParams,
    updatePagination,
    resetState
  };
});
