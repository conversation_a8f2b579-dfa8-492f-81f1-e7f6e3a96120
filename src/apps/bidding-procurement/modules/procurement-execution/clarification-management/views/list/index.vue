<!-- 补遗澄清答疑管理 - 列表页面 -->
<template>
  <funi-list-page-v2 ref="listPageRef" :cardTab="cardTabConfig" @headBtnClick="handleHeadBtnClick" />
</template>

<script setup lang="jsx">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElNotification, ElMessageBox } from 'element-plus';
import { useClarificationStore } from '../../store.js';

// ==================== 基础变量配置 ====================
const listPageRef = ref();
const router = useRouter();
const clarificationStore = useClarificationStore();

// ==================== 权限配置 ====================
const auths = reactive({
  export: 'clarification_export', // 导出权限
  add: 'clarification_add', // 新增权限
  delete: 'clarification_delete', // 删除权限
  audit: 'clarification_audit', // 审核权限
  edit: 'clarification_edit', // 编辑权限
  submit: 'clarification_submit', // 提交权限
  publish: 'clarification_publish', // 发布权限
  revoke: 'clarification_revoke', // 撤销权限
  detail: 'clarification_detail' // 详情权限
});

// ==================== 数据状态管理 ====================
const listPageParams = ref({});

// ==================== 核心业务函数 ====================

/**
 * 跳转到详情页面
 */
const goDetail = row => {
  router.push({
    path: `/procurement-execution/clarification-management/detail/${row.id}`,
    query: {
      title: row.title
    }
  });
};

/**
 * 删除记录
 */
const deleteItem = async id => {
  try {
    await clarificationStore.deleteClarification(id);
    ElNotification({
      title: '提示',
      message: '删除成功',
      type: 'success',
      duration: 2000
    });
    listPageRef.value.reload({ resetPage: false });
  } catch (error) {
    console.error('删除失败:', error);
    ElNotification({
      title: '错误',
      message: '删除失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

/**
 * 跳转到编辑页面
 */
const editItem = row => {
  router.push({
    path: `/procurement-execution/clarification-management/edit/${row.id}`,
    query: {
      title: row.title
    }
  });
};

/**
 * 跳转到审核页面
 */
const auditItem = row => {
  router.push({
    path: `/procurement-execution/clarification-management/audit/${row.id}`,
    query: {
      title: row.title
    }
  });
};

/**
 * 提交审核
 */
const submitItem = async row => {
  try {
    await clarificationStore.submitClarification(row.id);
    ElNotification({
      title: '提示',
      message: '提交成功',
      type: 'success',
      duration: 2000
    });
    listPageRef.value.reload({ resetPage: false });
  } catch (error) {
    console.error('提交失败:', error);
    ElNotification({
      title: '错误',
      message: '提交失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

/**
 * 发布
 */
const publishItem = async row => {
  try {
    await clarificationStore.publishClarification(row.id);
    ElNotification({
      title: '提示',
      message: '发布成功',
      type: 'success',
      duration: 2000
    });
    listPageRef.value.reload({ resetPage: false });
  } catch (error) {
    console.error('发布失败:', error);
    ElNotification({
      title: '错误',
      message: '发布失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

/**
 * 撤销
 */
const revokeItem = row => {
  ElMessageBox.prompt('请输入撤销原因', '撤销确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /.+/,
    inputErrorMessage: '撤销原因不能为空'
  })
    .then(async ({ value }) => {
      try {
        await clarificationStore.revokeClarification(row.id, value);
        ElNotification({
          title: '提示',
          message: '撤销成功',
          type: 'success',
          duration: 2000
        });
        listPageRef.value.reload({ resetPage: false });
      } catch (error) {
        console.error('撤销失败:', error);
        ElNotification({
          title: '错误',
          message: '撤销失败，请重试',
          type: 'error',
          duration: 2000
        });
      }
    })
    .catch(() => {
      // 用户取消操作
    });
};

// ==================== 动态按钮生成器 ====================
const generateActionButtons = row => {
  const buttons = [];

  // 根据状态显示不同的操作按钮
  switch (row.status) {
    case 0: // 草稿
      buttons.push(
        <el-link type="primary" v-auth={auths.edit} onClick={() => editItem(row)} style={{ marginRight: '10px' }}>
          编辑
        </el-link>,
        <el-link type="primary" v-auth={auths.submit} onClick={() => submitItem(row)} style={{ marginRight: '10px' }}>
          提交
        </el-link>,
        <el-popconfirm title="是否删除?" onConfirm={() => deleteItem(row.id)}>
          {{
            reference: () => (
              <el-link type="danger" v-auth={auths.delete}>
                删除
              </el-link>
            )
          }}
        </el-popconfirm>
      );
      break;
    case 1: // 待审核
    case 2: // 审核中
      buttons.push(
        <el-link type="primary" v-auth={auths.audit} onClick={() => auditItem(row)} style={{ marginRight: '10px' }}>
          审核
        </el-link>
      );
      break;
    case 3: // 审核通过
      buttons.push(
        <el-link type="primary" v-auth={auths.publish} onClick={() => publishItem(row)} style={{ marginRight: '10px' }}>
          发布
        </el-link>
      );
      break;
    case 4: // 已发布
      buttons.push(
        <el-link type="warning" v-auth={auths.revoke} onClick={() => revokeItem(row)} style={{ marginRight: '10px' }}>
          撤销
        </el-link>
      );
      break;
    case 5: // 审核驳回
      buttons.push(
        <el-link type="primary" v-auth={auths.edit} onClick={() => editItem(row)} style={{ marginRight: '10px' }}>
          编辑
        </el-link>
      );
      break;
  }

  // 详情按钮始终显示
  buttons.push(
    <el-link type="primary" v-auth={auths.detail} onClick={() => goDetail(row)}>
      详情
    </el-link>
  );

  return buttons;
};

// 表格列配置
const columns = reactive([
  {
    label: '公告标题',
    prop: 'title',
    fixed: 'left',
    width: 200,
    render: ({ row }) => {
      return (
        <el-link type="primary" onClick={() => goDetail(row)}>
          {row.title || '--'}
        </el-link>
      );
    }
  },
  {
    label: '项目名称',
    prop: 'projectName',
    width: 180
  },
  {
    label: '标段名称',
    prop: 'sectionName',
    width: 120
  },
  {
    label: '类型',
    prop: 'typeText',
    width: 80,
    render: ({ row }) => (
      <el-tag type={row.type === 'addendum' ? 'warning' : row.type === 'clarification' ? 'info' : 'success'}>
        {row.typeText || '--'}
      </el-tag>
    )
  },
  {
    label: '是否公示',
    prop: 'isPublicText',
    width: 80
  },
  {
    label: '状态',
    prop: 'statusText',
    width: 100,
    render: ({ row }) => {
      const statusColors = {
        0: 'info', // 草稿
        1: 'warning', // 待审核
        2: 'warning', // 审核中
        3: 'success', // 审核通过
        4: 'success', // 已发布
        5: 'danger', // 审核驳回
        6: 'info' // 已撤销
      };
      return <el-tag type={statusColors[row.status] || 'info'}>{row.statusText || '--'}</el-tag>;
    }
  },
  {
    label: '创建人',
    prop: 'createUser',
    width: 100
  },
  {
    label: '创建时间',
    prop: 'createTime',
    width: 150
  },
  {
    label: '操作',
    prop: 'actions',
    width: 200,
    fixed: 'right',
    render: ({ row }) => {
      return <div>{generateActionButtons(row)}</div>;
    }
  }
]);

// 数据加载函数
const loadData = async (pageParams, searchParams) => {
  listPageParams.value = searchParams;
  try {
    const result = await clarificationStore.getClarificationList({
      ...pageParams,
      ...searchParams
    });
    return {
      data: result.list,
      total: result.total
    };
  } catch (error) {
    console.error('加载数据失败:', error);
    return {
      data: [],
      total: 0
    };
  }
};

// 页签配置
const cardTabConfig = computed(() => {
  return [
    {
      label: '全部',
      key: 'all',
      curdOption: {
        columns,
        lodaData: loadData,
        btns: [
          { key: 'add', label: '新建', auth: auths.add, type: 'primary' },
          { key: 'export', label: '导出', auth: auths.export }
        ],
        fixedButtons: true,
        reloadOnActive: true
      }
    }
  ];
});

// 新增功能
const addItem = () => {
  router.push({
    path: '/procurement-execution/clarification-management/create'
  });
};

// 导出功能
const exportData = async () => {
  try {
    await clarificationStore.exportData(listPageParams.value);
    ElNotification({
      title: '提示',
      message: '导出成功',
      type: 'success',
      duration: 2000
    });
  } catch (error) {
    console.error('导出失败:', error);
    ElNotification({
      title: '错误',
      message: '导出失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

// 头部按钮点击事件处理
const handleHeadBtnClick = key => {
  switch (key) {
    case 'add':
      addItem();
      break;
    case 'export':
      exportData();
      break;
    default:
      break;
  }
};

// ==================== 生命周期钩子 ====================
onMounted(() => {
  console.log('补遗澄清答疑管理列表页面已挂载');
});
</script>
