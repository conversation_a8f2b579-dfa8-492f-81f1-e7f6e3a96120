<!-- 补遗澄清答疑管理 - 详情页面 -->
<template>
  <funi-detail 
    :steps="stepsConfig"
    :detailHeadOption="detailHeadOption"
    @onStepChange="handleStepChange"
  >
    <template #step1>
      <!-- 项目标段信息 -->
      <el-card class="mb-4" shadow="never">
        <template #header>
          <div class="card-header">
            <span>项目标段信息</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>项目名称：</label>
              <span>{{ detailData.projectInfo?.projectName || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>项目编号：</label>
              <span>{{ detailData.projectInfo?.projectCode || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>项目业主：</label>
              <span>{{ detailData.projectInfo?.projectOwner || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>采购方式：</label>
              <span>{{ detailData.projectInfo?.procurementMethod || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>标段名称：</label>
              <span>{{ detailData.projectInfo?.sectionName || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>标段编号：</label>
              <span>{{ detailData.projectInfo?.sectionCode || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>预算金额：</label>
              <span>{{ formatAmount(detailData.projectInfo?.budgetAmount) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>采购类型：</label>
              <span>{{ detailData.projectInfo?.procurementType || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>招标代理机构：</label>
              <span>{{ detailData.projectInfo?.agencyName || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>联系人：</label>
              <span>{{ detailData.projectInfo?.contactPerson || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>联系电话：</label>
              <span>{{ detailData.projectInfo?.contactPhone || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>招标文件获取方式：</label>
              <span>{{ detailData.projectInfo?.documentObtainMethod || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>投标截止时间：</label>
              <span>{{ detailData.projectInfo?.bidDeadline || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>开标时间：</label>
              <span>{{ detailData.projectInfo?.openingTime || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>开标地点：</label>
              <span>{{ detailData.projectInfo?.openingLocation || '--' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 补遗澄清答疑信息 -->
      <el-card class="mb-4" shadow="never">
        <template #header>
          <div class="card-header">
            <span>补遗澄清答疑信息</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>公告标题：</label>
              <span>{{ detailData.title || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <label>类型：</label>
              <el-tag :type="getTypeTagType(detailData.type)">
                {{ detailData.typeText || '--' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <label>是否公示：</label>
              <span>{{ detailData.isPublicText || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="info-item">
              <label>补遗澄清答疑说明：</label>
              <div class="content-display" v-html="detailData.content || '--'"></div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 审核依据文件 -->
      <el-card class="mb-4" shadow="never" v-if="detailData.attachments && detailData.attachments.length > 0">
        <template #header>
          <div class="card-header">
            <span>审核依据文件</span>
          </div>
        </template>
        <funi-file-table 
          :fileList="detailData.attachments"
          :readonly="true"
        />
      </el-card>

      <!-- 状态信息 -->
      <el-card class="mb-4" shadow="never">
        <template #header>
          <div class="card-header">
            <span>状态信息</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="info-item">
              <label>当前状态：</label>
              <el-tag :type="getStatusTagType(detailData.status)">
                {{ detailData.statusText || '--' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <label>创建人：</label>
              <span>{{ detailData.createUser || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <label>创建时间：</label>
              <span>{{ detailData.createTime || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <label>更新时间：</label>
              <span>{{ detailData.updateTime || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="6" v-if="detailData.auditUser">
            <div class="info-item">
              <label>审核人：</label>
              <span>{{ detailData.auditUser || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="6" v-if="detailData.auditTime">
            <div class="info-item">
              <label>审核时间：</label>
              <span>{{ detailData.auditTime || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="6" v-if="detailData.publishTime">
            <div class="info-item">
              <label>发布时间：</label>
              <span>{{ detailData.publishTime || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="24" v-if="detailData.auditComment">
            <div class="info-item">
              <label>审核意见：</label>
              <span>{{ detailData.auditComment || '--' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </template>
  </funi-detail>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';
import { reactive, ref, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { useClarificationStore } from '../../store.js';

// 路由和Store
const router = useRouter();
const route = useRoute();
const clarificationStore = useClarificationStore();

// 详情数据
const detailData = ref({});

// 步骤配置
const stepsConfig = reactive([
  {
    title: '补遗澄清答疑详情',
    name: 'step1'
  }
]);

// 详情页头部配置
const detailHeadOption = reactive({
  title: computed(() => detailData.value.title || '补遗澄清答疑详情'),
  btns: computed(() => {
    const btns = [];
    
    // 根据状态显示不同的操作按钮
    if (detailData.value.status === 0 || detailData.value.status === 5) {
      btns.push({
        label: '编辑',
        type: 'primary',
        auth: 'clarification_edit',
        on: {
          click: () => handleEdit()
        }
      });
    }
    
    btns.push({
      label: '返回',
      on: {
        click: () => handleBack()
      }
    });
    
    return btns;
  })
});

// 事件处理函数
const handleStepChange = (step) => {
  console.log('步骤切换:', step);
};

const handleEdit = () => {
  router.push(`/procurement-execution/clarification-management/edit/${route.params.id}`);
};

const handleBack = () => {
  router.push('/procurement-execution/clarification-management');
};

// 工具函数
const formatAmount = (amount) => {
  if (!amount) return '--';
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount);
};

const getTypeTagType = (type) => {
  const typeMap = {
    'addendum': 'warning',
    'clarification': 'info',
    'qa': 'success'
  };
  return typeMap[type] || 'info';
};

const getStatusTagType = (status) => {
  const statusMap = {
    0: 'info',     // 草稿
    1: 'warning',  // 待审核
    2: 'warning',  // 审核中
    3: 'success',  // 审核通过
    4: 'success',  // 已发布
    5: 'danger',   // 审核驳回
    6: 'info'      // 已撤销
  };
  return statusMap[status] || 'info';
};

// 加载数据
const loadData = async () => {
  if (route.params.id) {
    try {
      const data = await clarificationStore.getClarification(route.params.id);
      detailData.value = data;
    } catch (error) {
      ElMessage.error('加载数据失败');
      console.error('加载详情失败:', error);
    }
  }
};

// 组件挂载
onMounted(() => {
  loadData();
  console.log('补遗澄清答疑详情页面已挂载');
});
</script>

<style scoped>
.info-item {
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 120px;
  margin-right: 8px;
}

.info-item span {
  color: #303133;
  word-break: break-all;
}

.content-display {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  min-height: 100px;
  background-color: #fafafa;
  margin-top: 8px;
}

.card-header {
  font-weight: 500;
  color: #303133;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
