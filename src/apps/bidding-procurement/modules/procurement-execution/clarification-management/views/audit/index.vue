<!-- 补遗澄清答疑管理 - 审核页面 -->
<template>
  <funi-detail 
    :steps="stepsConfig"
    :detailHeadOption="detailHeadOption"
    @onStepChange="handleStepChange"
  >
    <template #step1>
      <!-- 补遗澄清答疑信息展示（只读） -->
      <el-card class="mb-4" shadow="never">
        <template #header>
          <div class="card-header">
            <span>补遗澄清答疑信息</span>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>公告标题：</label>
              <span>{{ detailData.title || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <label>类型：</label>
              <el-tag :type="getTypeTagType(detailData.type)">
                {{ detailData.typeText || '--' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="info-item">
              <label>是否公示：</label>
              <span>{{ detailData.isPublicText || '--' }}</span>
            </div>
          </el-col>
          <el-col :span="24">
            <div class="info-item">
              <label>补遗澄清答疑说明：</label>
              <div class="content-display" v-html="detailData.content || '--'"></div>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 审核依据文件 -->
      <el-card class="mb-4" shadow="never" v-if="detailData.attachments && detailData.attachments.length > 0">
        <template #header>
          <div class="card-header">
            <span>审核依据文件</span>
          </div>
        </template>
        <funi-file-table 
          :fileList="detailData.attachments"
          :readonly="true"
        />
      </el-card>

      <!-- 审核操作 -->
      <el-card class="mb-4" shadow="never">
        <template #header>
          <div class="card-header">
            <span>审核操作</span>
          </div>
        </template>
        <funi-form
          ref="auditFormRef"
          :schema="auditFormSchema"
          :model="auditFormData"
          :rules="auditFormRules"
          label-width="120px"
        />
      </el-card>

      <!-- 审核历史记录 -->
      <el-card class="mb-4" shadow="never" v-if="auditHistory.length > 0">
        <template #header>
          <div class="card-header">
            <span>审核历史记录</span>
          </div>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in auditHistory"
            :key="index"
            :timestamp="item.auditTime"
            placement="top"
          >
            <el-card>
              <h4>{{ item.auditUser }} - {{ item.actionText }}</h4>
              <p v-if="item.comment">审核意见：{{ item.comment }}</p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </template>
  </funi-detail>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';
import { reactive, ref, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useClarificationStore } from '../../store.js';

// 路由和Store
const router = useRouter();
const route = useRoute();
const clarificationStore = useClarificationStore();

// 表单引用
const auditFormRef = ref(null);

// 详情数据
const detailData = ref({});

// 审核表单数据
const auditFormData = reactive({
  action: '',
  comment: ''
});

// 审核历史记录
const auditHistory = ref([]);

// 步骤配置
const stepsConfig = reactive([
  {
    title: '审核补遗澄清答疑',
    name: 'step1'
  }
]);

// 详情页头部配置
const detailHeadOption = reactive({
  title: computed(() => detailData.value.title || '审核补遗澄清答疑'),
  btns: computed(() => {
    const btns = [];
    
    // 只有在待审核或审核中状态才显示审核按钮
    if (detailData.value.status === 1 || detailData.value.status === 2) {
      btns.push({
        label: '审核通过',
        type: 'success',
        auth: 'clarification_audit',
        on: {
          click: () => handleAudit('approve')
        }
      });
      
      btns.push({
        label: '审核驳回',
        type: 'danger',
        auth: 'clarification_audit',
        on: {
          click: () => handleAudit('reject')
        }
      });
    }
    
    btns.push({
      label: '返回',
      on: {
        click: () => handleBack()
      }
    });
    
    return btns;
  })
});

// 审核表单配置
const auditFormSchema = reactive([
  {
    prop: 'action',
    label: '审核结果',
    component: 'el-radio-group',
    componentProps: {
      options: [
        { label: '通过', value: 'approve' },
        { label: '驳回', value: 'reject' }
      ]
    },
    span: 24
  },
  {
    prop: 'comment',
    label: '审核意见',
    component: 'el-input',
    componentProps: {
      type: 'textarea',
      rows: 4,
      placeholder: '请输入审核意见',
      maxlength: 500,
      showWordLimit: true
    },
    span: 24
  }
]);

// 审核表单验证规则
const auditFormRules = reactive({
  action: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  comment: [
    { required: true, message: '请输入审核意见', trigger: 'blur' },
    { min: 5, max: 500, message: '审核意见长度在 5 到 500 个字符', trigger: 'blur' }
  ]
});

// 事件处理函数
const handleStepChange = (step) => {
  console.log('步骤切换:', step);
};

const handleAudit = async (action) => {
  try {
    // 设置审核动作
    auditFormData.action = action;
    
    // 表单验证
    const valid = await auditFormRef.value.validate();
    if (!valid) return;

    // 确认审核操作
    const actionText = action === 'approve' ? '通过' : '驳回';
    await ElMessageBox.confirm(
      `确认${actionText}该补遗澄清答疑吗？`,
      '审核确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 执行审核
    await clarificationStore.auditClarification(route.params.id, auditFormData);
    
    ElMessage.success(`审核${actionText}成功`);
    
    // 返回列表页
    handleBack();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('审核操作失败');
      console.error('审核失败:', error);
    }
  }
};

const handleBack = () => {
  router.push('/procurement-execution/clarification-management');
};

// 工具函数
const getTypeTagType = (type) => {
  const typeMap = {
    'addendum': 'warning',
    'clarification': 'info',
    'qa': 'success'
  };
  return typeMap[type] || 'info';
};

// 加载数据
const loadData = async () => {
  if (route.params.id) {
    try {
      const data = await clarificationStore.getClarification(route.params.id);
      detailData.value = data;
      
      // 加载审核历史记录（模拟数据）
      auditHistory.value = [
        {
          auditUser: '张三',
          actionText: '提交审核',
          auditTime: '2024-01-15 10:30:00',
          comment: '提交审核，请审核人员审核'
        }
      ];
    } catch (error) {
      ElMessage.error('加载数据失败');
      console.error('加载详情失败:', error);
    }
  }
};

// 组件挂载
onMounted(() => {
  loadData();
  console.log('补遗澄清答疑审核页面已挂载');
});
</script>

<style scoped>
.info-item {
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 120px;
  margin-right: 8px;
}

.info-item span {
  color: #303133;
  word-break: break-all;
}

.content-display {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  min-height: 100px;
  background-color: #fafafa;
  margin-top: 8px;
}

.card-header {
  font-weight: 500;
  color: #303133;
}

.mb-4 {
  margin-bottom: 16px;
}

:deep(.el-timeline-item__content) {
  padding-bottom: 20px;
}
</style>
