<!-- 补遗澄清答疑管理 - 新建/编辑页面 -->
<template>
  <funi-detail 
    :steps="stepsConfig"
    :detailHeadOption="detailHeadOption"
    @onStepChange="handleStepChange"
  >
    <template #step1>
      <funi-form
        ref="formRef"
        :schema="formSchema"
        :model="formData"
        :rules="formRules"
        label-width="140px"
      />
    </template>
  </funi-detail>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';
import { reactive, ref, onMounted, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useClarificationStore } from '../../store.js';
import { clarificationAdapters } from '../../adapters/index.js';

// 路由和Store
const router = useRouter();
const route = useRoute();
const clarificationStore = useClarificationStore();

// 表单引用
const formRef = ref(null);

// 页面模式
const pageMode = computed(() => {
  if (route.path.includes('/create')) return 'create';
  if (route.path.includes('/edit')) return 'edit';
  return 'view';
});

// 是否为编辑模式
const isEditMode = computed(() => pageMode.value !== 'view');

// 表单数据
const formData = reactive({
  projectSectionId: '',
  title: '',
  type: '',
  isPublic: false,
  content: '',
  attachments: []
});

// 项目标段选项
const projectSectionOptions = ref([]);

// 步骤配置
const stepsConfig = reactive([
  {
    title: pageMode.value === 'create' ? '新建补遗澄清答疑' : '编辑补遗澄清答疑',
    name: 'step1'
  }
]);

// 详情页头部配置
const detailHeadOption = reactive({
  title: computed(() => {
    if (pageMode.value === 'create') return '新建补遗澄清答疑';
    if (pageMode.value === 'edit') return '编辑补遗澄清答疑';
    return '补遗澄清答疑详情';
  }),
  btns: computed(() => {
    const btns = [];
    
    if (isEditMode.value) {
      btns.push({
        label: '保存',
        type: 'primary',
        on: {
          click: () => handleSave()
        }
      });
    }
    
    btns.push({
      label: '返回',
      on: {
        click: () => handleBack()
      }
    });
    
    return btns;
  })
});

// 表单配置
const formSchema = reactive([
  {
    prop: 'projectSectionId',
    label: '项目标段',
    component: 'el-select',
    componentProps: {
      placeholder: '请选择项目标段',
      filterable: true,
      clearable: true,
      options: computed(() => projectSectionOptions.value),
      onChange: handleProjectSectionChange
    },
    span: 24
  },
  {
    prop: 'title',
    label: '公告标题',
    component: 'el-input',
    componentProps: {
      placeholder: '将根据项目标段和类型自动生成',
      readonly: true
    },
    span: 24
  },
  {
    prop: 'type',
    label: '类型',
    component: 'el-select',
    componentProps: {
      placeholder: '请选择类型',
      options: [
        { label: '补遗', value: 'addendum' },
        { label: '澄清', value: 'clarification' },
        { label: '答疑', value: 'qa' }
      ],
      onChange: handleTypeChange
    },
    span: 12
  },
  {
    prop: 'isPublic',
    label: '是否公示',
    component: 'el-switch',
    componentProps: {
      activeText: '是',
      inactiveText: '否'
    },
    span: 12
  },
  {
    prop: 'content',
    label: '补遗澄清答疑说明',
    component: 'funi-rich-text-editor',
    componentProps: {
      placeholder: '请输入补遗澄清答疑说明',
      height: '300px'
    },
    span: 24
  },
  {
    prop: 'attachments',
    label: '审核依据文件',
    component: 'funi-file-table',
    componentProps: {
      accept: '.pdf,.doc,.docx,.xls,.xlsx',
      maxSize: 10,
      multiple: true,
      tip: '支持PDF、DOC、DOCX、XLS、XLSX格式，单个文件不超过10MB'
    },
    span: 24
  }
]);

// 表单验证规则
const formRules = reactive({
  projectSectionId: [
    { required: true, message: '请选择项目标段', trigger: 'change' }
  ],
  title: [
    { required: true, message: '公告标题不能为空', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入补遗澄清答疑说明', trigger: 'blur' }
  ]
});

// 事件处理函数
const handleStepChange = (step) => {
  console.log('步骤切换:', step);
};

// 项目标段变化处理
const handleProjectSectionChange = (value) => {
  const selectedProject = projectSectionOptions.value.find(item => item.value === value);
  if (selectedProject && formData.type) {
    // 自动生成公告标题
    formData.title = clarificationAdapters.generateTitle(selectedProject.projectInfo, formData.type);
    
    // 根据预算金额自动判断是否需要公示
    formData.isPublic = clarificationAdapters.shouldPublic(selectedProject.projectInfo.budgetAmount);
  }
};

// 类型变化处理
const handleTypeChange = (value) => {
  if (formData.projectSectionId && value) {
    const selectedProject = projectSectionOptions.value.find(item => item.value === formData.projectSectionId);
    if (selectedProject) {
      // 自动生成公告标题
      formData.title = clarificationAdapters.generateTitle(selectedProject.projectInfo, value);
    }
  }
};

const handleSave = async () => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) return;

    // 保存数据
    if (pageMode.value === 'create') {
      await clarificationStore.createClarification(formData);
      ElMessage.success('创建成功');
    } else {
      await clarificationStore.updateClarification(route.params.id, formData);
      ElMessage.success('更新成功');
    }
    
    // 返回列表页
    handleBack();
  } catch (error) {
    ElMessage.error(pageMode.value === 'create' ? '创建失败' : '更新失败');
    console.error('保存失败:', error);
  }
};

const handleBack = () => {
  router.push('/procurement-execution/clarification-management');
};

// 加载项目标段选项
const loadProjectSectionOptions = async () => {
  try {
    // 这里应该调用实际的API获取项目标段列表
    // 暂时使用Mock数据
    const mockData = [
      {
        value: 'ps1',
        label: '某某市政道路建设项目 - 第一标段',
        projectInfo: {
          projectName: '某某市政道路建设项目',
          projectCode: 'PROJ-2024-001',
          projectOwner: '某某市政府',
          sectionName: '第一标段',
          sectionCode: 'SEC-001',
          budgetAmount: 5000000
        }
      },
      {
        value: 'ps2',
        label: '某某市政道路建设项目 - 第二标段',
        projectInfo: {
          projectName: '某某市政道路建设项目',
          projectCode: 'PROJ-2024-001',
          projectOwner: '某某市政府',
          sectionName: '第二标段',
          sectionCode: 'SEC-002',
          budgetAmount: 3000000
        }
      }
    ];
    projectSectionOptions.value = mockData;
  } catch (error) {
    console.error('加载项目标段选项失败:', error);
  }
};

// 加载数据
const loadData = async () => {
  if (pageMode.value === 'edit' && route.params.id) {
    try {
      const data = await clarificationStore.getClarification(route.params.id);
      Object.assign(formData, {
        projectSectionId: data.projectSectionId || '',
        title: data.title || '',
        type: data.type || '',
        isPublic: data.isPublic || false,
        content: data.content || '',
        attachments: data.attachments || []
      });
    } catch (error) {
      ElMessage.error('加载数据失败');
      console.error('加载数据失败:', error);
    }
  }
};

// 组件挂载
onMounted(async () => {
  await loadProjectSectionOptions();
  await loadData();
  console.log('补遗澄清答疑新建/编辑页面已挂载，模式:', pageMode.value);
});
</script>

<style scoped>
/* 页面特定样式 */
</style>
