/**
 * 补遗澄清答疑管理 - Adapters层
 * 
 * 模板说明：
 * - 纯函数数据转换模板
 * - 负责API数据与前端数据结构的转换
 * - 不调用其他层，保持纯函数特性
 * 
 * 占位符说明：
 * - 补遗澄清答疑管理: 模块中文名称
 * - 补遗澄清答疑: 实体中文名称
 * - clarification: 实体英文名称
 */

/**
 * 补遗澄清答疑管理 数据转换适配器
 */
export const clarificationAdapters = {
  /**
   * 适配列表数据
   * 将API返回的列表数据转换为前端需要的格式
   * @param {Object} apiResponse - API响应数据
   * @returns {Object} 转换后的列表数据
   */
  adaptListData(apiResponse) {
    if (!apiResponse || !apiResponse.data) {
      return {
        list: [],
        total: 0,
        page: 1,
        size: 10
      };
    }

    const { data, total, page, size } = apiResponse;
    
    return {
      list: data.map(item => this.adaptItemData(item)),
      total: total || 0,
      page: page || 1,
      size: size || 10
    };
  },

  /**
   * 适配单个补遗澄清答疑数据
   * 将API返回的单个补遗澄清答疑数据转换为前端格式
   * @param {Object} apiItem - API返回的单个补遗澄清答疑数据
   * @returns {Object} 转换后的补遗澄清答疑数据
   */
  adaptItemData(apiItem) {
    if (!apiItem) return null;

    return {
      id: apiItem.id || '',
      title: apiItem.title || '',
      projectName: apiItem.projectName || '',
      projectCode: apiItem.projectCode || '',
      sectionName: apiItem.sectionName || '',
      sectionCode: apiItem.sectionCode || '',
      type: apiItem.type || '',
      typeText: this.getTypeText(apiItem.type),
      isPublic: apiItem.isPublic || false,
      isPublicText: apiItem.isPublic ? '是' : '否',
      content: apiItem.content || '',
      status: apiItem.status !== undefined ? apiItem.status : 0,
      statusText: this.getStatusText(apiItem.status),
      attachments: apiItem.attachments || [],
      createTime: this.formatDateTime(apiItem.createTime),
      updateTime: this.formatDateTime(apiItem.updateTime),
      publishTime: this.formatDateTime(apiItem.publishTime),
      createUser: apiItem.createUser || '',
      updateUser: apiItem.updateUser || '',
      auditUser: apiItem.auditUser || '',
      auditTime: this.formatDateTime(apiItem.auditTime),
      auditComment: apiItem.auditComment || ''
    };
  },

  /**
   * 适配详情数据
   * 将API返回的详情数据转换为前端格式
   * @param {Object} apiResponse - API响应数据
   * @returns {Object} 转换后的详情数据
   */
  adaptDetailData(apiResponse) {
    if (!apiResponse) return null;
    
    const baseData = this.adaptItemData(apiResponse);
    
    // 详情页需要额外的项目标段信息
    return {
      ...baseData,
      projectInfo: {
        projectName: apiResponse.projectName || '',
        projectCode: apiResponse.projectCode || '',
        projectOwner: apiResponse.projectOwner || '',
        procurementMethod: apiResponse.procurementMethod || '',
        sectionName: apiResponse.sectionName || '',
        sectionCode: apiResponse.sectionCode || '',
        budgetAmount: apiResponse.budgetAmount || '',
        procurementType: apiResponse.procurementType || '',
        agencyName: apiResponse.agencyName || '',
        contactPerson: apiResponse.contactPerson || '',
        contactPhone: apiResponse.contactPhone || '',
        documentObtainMethod: apiResponse.documentObtainMethod || '',
        bidDeadline: this.formatDateTime(apiResponse.bidDeadline),
        openingTime: this.formatDateTime(apiResponse.openingTime),
        openingLocation: apiResponse.openingLocation || ''
      }
    };
  },

  /**
   * 适配创建数据
   * 将前端表单数据转换为API需要的格式
   * @param {Object} formData - 前端表单数据
   * @returns {Object} 转换后的API数据
   */
  adaptCreateData(formData) {
    if (!formData) return {};

    return {
      projectSectionId: formData.projectSectionId || '',
      title: formData.title || '',
      type: formData.type || '',
      isPublic: formData.isPublic || false,
      content: formData.content || '',
      attachments: formData.attachments || []
    };
  },

  /**
   * 适配更新数据
   * 将前端表单数据转换为API需要的格式
   * @param {Object} formData - 前端表单数据
   * @returns {Object} 转换后的API数据
   */
  adaptUpdateData(formData) {
    if (!formData) return {};

    return {
      title: formData.title || '',
      type: formData.type || '',
      isPublic: formData.isPublic || false,
      content: formData.content || '',
      attachments: formData.attachments || []
    };
  },

  /**
   * 获取类型文本
   * @param {string} type - 类型值
   * @returns {string} 类型文本
   */
  getTypeText(type) {
    const typeMap = {
      'addendum': '补遗',
      'clarification': '澄清',
      'qa': '答疑'
    };
    return typeMap[type] || '未知';
  },

  /**
   * 获取状态文本
   * @param {number} status - 状态值
   * @returns {string} 状态文本
   */
  getStatusText(status) {
    const statusMap = {
      0: '草稿',
      1: '待审核',
      2: '审核中',
      3: '审核通过',
      4: '已发布',
      5: '审核驳回',
      6: '已撤销'
    };
    return statusMap[status] || '未知';
  },

  /**
   * 格式化日期时间
   * @param {string|Date} dateTime - 日期时间
   * @returns {string} 格式化后的日期时间字符串
   */
  formatDateTime(dateTime) {
    if (!dateTime) return '';
    
    try {
      const date = new Date(dateTime);
      if (isNaN(date.getTime())) return '';
      
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (error) {
      console.warn('日期格式化失败:', error);
      return '';
    }
  },

  /**
   * 格式化日期
   * @param {string|Date} date - 日期
   * @returns {string} 格式化后的日期字符串
   */
  formatDate(date) {
    if (!date) return '';
    
    try {
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) return '';
      
      return dateObj.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      console.warn('日期格式化失败:', error);
      return '';
    }
  },

  /**
   * 适配搜索参数
   * 将前端搜索条件转换为API参数
   * @param {Object} searchParams - 前端搜索参数
   * @returns {Object} 转换后的API参数
   */
  adaptSearchParams(searchParams) {
    if (!searchParams) return {};

    return {
      keyword: searchParams.keyword || '',
      projectName: searchParams.projectName || '',
      sectionName: searchParams.sectionName || '',
      type: searchParams.type || '',
      status: searchParams.status !== undefined ? searchParams.status : null,
      startDate: searchParams.startDate || '',
      endDate: searchParams.endDate || ''
    };
  },

  /**
   * 生成公告标题
   * 根据项目业主、标段名称和类型自动生成公告标题
   * @param {Object} projectInfo - 项目信息
   * @param {string} type - 公告类型
   * @returns {string} 生成的公告标题
   */
  generateTitle(projectInfo, type) {
    if (!projectInfo || !type) return '';
    
    const typeText = this.getTypeText(type);
    const projectOwner = projectInfo.projectOwner || '';
    const sectionName = projectInfo.sectionName || '';
    
    return `${projectOwner}${sectionName}${typeText}公告`;
  },

  /**
   * 判断是否需要公示
   * 根据采购金额自动判断是否需要公示
   * @param {number} budgetAmount - 预算金额
   * @returns {boolean} 是否需要公示
   */
  shouldPublic(budgetAmount) {
    // 根据业务规则，超过一定金额需要公示
    const threshold = 1000000; // 100万元
    return budgetAmount >= threshold;
  }
};

// 默认导出
export default clarificationAdapters;
