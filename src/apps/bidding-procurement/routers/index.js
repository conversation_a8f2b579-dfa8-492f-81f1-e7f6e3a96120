/**
 * 招标采购系统 - 系统级路由配置
 *
 * 基于 router-standards.md 规范配置
 * - 系统级路由配置
 * - 负责集成模块路由到系统中
 * - 提供系统级菜单和导航结构
 */

// 导入模块路由
import procurementPlanRouter from '../modules/procurement-plan/router.js';
import procurementExecutionRouter from '../modules/procurement-execution/router.js';

/**
 * 招标采购系统路由配置
 */
export default {
  path: '/bidding-procurement',
  name: 'BiddingProcurement',
  redirect: '/bidding-procurement/procurement-plan',
  meta: {
    title: '招标采购系统',
    icon: 'bidding',
    isMenu: true
  },
  children: [procurementPlanRouter, procurementExecutionRouter]
};
