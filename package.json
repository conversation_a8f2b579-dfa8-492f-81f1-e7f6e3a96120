{"name": "funi-pass-cs-web-cli", "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build:default": "node ./__v_cache.js && NODE_ENV=production node --max_old_space_size=8196 ./node_modules/vite/bin/vite.js build", "build:cli": "node ./__v_env.js cli && nr build:default", "build:app": "node ./__v_env.js app && rimraf ./output/dist  && nr build:default", "build": "rimraf ./.env.production  && nr build:default", "preview": "vite preview --port 4173", "lint": "eslint src --ext .vue,.js,.jsx --fix --ignore-path .gitignore", "update-cli": "cli-update"}, "dependencies": {"@chenfengyuan/vue-qrcode": "^2.0.0", "@codemirror/commands": "6.6.0", "@codemirror/lang-javascript": "6.2.2", "@codemirror/language": "6.10.2", "@codemirror/state": "6.4.1", "@codemirror/theme-one-dark": "6.1.2", "@codemirror/view": "6.29.1", "@element-plus/icons-vue": "2.3.1", "@funi-lib/utils": "0.1.4", "@funi/bpmn-js": "1.1.3", "@funi/fodash": "0.2.3", "@funi/funi-paas-ops-ui": "1.1.42", "@funi/funi-unplugin-versions": "^0.2.6", "@microsoft/fetch-event-source": "^2.0.1", "@onlyoffice/document-editor-vue": "^1.4.0", "@popperjs/core": "^2.11.8", "@tinymce/tinymce-vue": "4.0.7", "@turf/turf": "^7.2.0", "@vue-office/docx": "1.6.2", "@vue-office/excel": "1.7.11", "@vue/compiler-sfc": "3.5.12", "@vue/repl": "4.4.2", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "11.2.0", "ace-builds": "1.35.4", "animate.css": "4.1.1", "bpmn-js": "17.9.1", "camunda-bpmn-moddle": "7.0.1", "clipboard": "2.0.11", "codemirror": "6.0.1", "compressorjs": "^1.2.1", "core-js": "3.37.1", "dhtmlx-gantt": "8.0.9", "dingtalk-jsapi": "3.0.36", "driver.js": "^1.3.5", "echarts": "5.5.1", "element-plus": "2.7.5", "fast-glob": "3.3.2", "file-saver": "2.0.5", "github-markdown-css": "5.7.0", "gsap": "^3.12.5", "highlight.js": "11.10.0", "hls.js": "^1.5.20", "html2canvas": "1.4.1", "js-base64": "3.7.7", "lodash-es": "^4.17.21", "lz-string": "1.5.0", "marked": "^15.0.0", "md5": "^2.3.0", "minimatch": "^10.0.1", "mitt": "3.0.1", "moment": "^2.30.1", "mpegts.js": "1.7.3", "ol": "^10.4.0", "parse5": "^7.2.1", "pinia": "2.2.6", "pinia-plugin-persist": "1.0.0", "proj4": "^2.15.0", "qrcode": "^1.5.4", "qrcode.vue": "^3.5.0", "screenfull": "6.0.2", "sm-crypto": "0.3.13", "snowflake-id": "1.1.0", "sortablejs": "1.15.2", "spark-md5": "^3.0.2", "terraformer-wkt-parser": "^1.2.1", "theme-colors": "0.1.0", "tinymce": "5.10.9", "vditor": "3.10.4", "vue": "3.5.0", "vue-codemirror": "6.1.1", "vue-color-kit": "1.0.6", "vue-demi": "0.14.10", "vue-i18n": "10.0.4", "vue-router": "4.4.5", "vue3-print-nb": "^0.1.4", "vxe-table": "4.8.10", "vxe-table-plugin-element": "4.0.4", "x2js": "^3.4.4", "xe-utils": "3.5.31", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "xss": "1.0.15"}, "devDependencies": {"@antfu/ni": "^23.3.1", "@funi/cli-update": "^1.0.3", "@funi/unplugin-funi-vue-drop": "0.0.9", "@funi/vite-plugin-vue-marionette": "^0.0.28", "@funi/vite-plugin-vue-puppeteer": "^0.0.13", "@iconify/vue": "4.1.2", "@rollup/plugin-commonjs": "28.0.1", "@rollup/plugin-node-resolve": "15.3.0", "@rushstack/eslint-patch": "1.10.4", "@types/lodash": "4.17.10", "@vitejs/plugin-vue": "5.1.4", "@vitejs/plugin-vue-jsx": "5.0.1", "@vue/eslint-config-prettier": "10.0.0", "cross-env": "^7.0.3", "eslint": "9.8.0", "eslint-plugin-vue": "9.29.0", "fs-extra": "^11.3.0", "globby": "14.0.2", "less": "4.2.0", "postcss": "8.4.40", "prettier": "3.6.2", "rimraf": "5.0.10", "rollup": "4.25.0", "sass": "~1.79.0", "unocss": "66.3.3", "vite": "^6.2.0", "vite-plugin-svg-icons": "2.0.1"}, "engines": {"node": "18", "pnpm": ">=7"}}