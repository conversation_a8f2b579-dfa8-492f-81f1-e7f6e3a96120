# FuniCard ElementPlus API 支持

## 基础组件说明

FuniCard 是基于 ElementPlus 的 `el-card` 组件进行二次封装的卡片组件。它完全兼容 `el-card` 的所有原生 API，同时扩展了更多的业务功能和交互特性。

### 基础 el-card 组件
```vue
<!-- ElementPlus 原生用法 -->
<el-card header="卡片标题" shadow="hover">
  <p>卡片内容</p>
</el-card>

<!-- FuniCard 等效用法 -->
<FuniCard header="卡片标题" shadow="hover">
  <p>卡片内容</p>
</FuniCard>
```

## 支持的 ElementPlus API

### Props 透传支持

| ElementPlus 属性 | 类型 | 默认值 | 说明 | FuniCard 支持 |
|-----------------|------|--------|------|---------------|
| header | String | - | 卡片标题 | ✅ 完全支持 |
| body-style | Object | - | 设置 body 的样式 | ✅ 完全支持 |
| shadow | String | always | 设置阴影显示时机 | ✅ 完全支持 |

#### shadow 属性详细说明
```vue
<template>
  <div class="shadow-examples">
    <!-- 始终显示阴影 -->
    <FuniCard header="始终显示阴影" shadow="always">
      <p>这个卡片始终显示阴影效果</p>
    </FuniCard>
    
    <!-- 悬停时显示阴影 -->
    <FuniCard header="悬停显示阴影" shadow="hover">
      <p>鼠标悬停时显示阴影效果</p>
    </FuniCard>
    
    <!-- 从不显示阴影 -->
    <FuniCard header="从不显示阴影" shadow="never">
      <p>这个卡片从不显示阴影效果</p>
    </FuniCard>
  </div>
</template>
```

#### body-style 属性详细说明
```vue
<template>
  <div class="body-style-examples">
    <!-- 自定义内边距 -->
    <FuniCard 
      header="自定义内边距" 
      :body-style="{ padding: '30px' }"
    >
      <p>这个卡片有更大的内边距</p>
    </FuniCard>
    
    <!-- 自定义背景色 -->
    <FuniCard 
      header="自定义背景色" 
      :body-style="{ backgroundColor: '#f5f7fa', padding: '20px' }"
    >
      <p>这个卡片有自定义的背景色</p>
    </FuniCard>
    
    <!-- 自定义文字样式 -->
    <FuniCard 
      header="自定义文字样式" 
      :body-style="{ 
        color: '#606266', 
        fontSize: '16px',
        lineHeight: '1.6'
      }"
    >
      <p>这个卡片有自定义的文字样式</p>
    </FuniCard>
  </div>
</template>
```

### 插槽透传支持

| ElementPlus 插槽 | 说明 | FuniCard 支持 |
|-----------------|------|---------------|
| default | 卡片内容 | ✅ 完全支持 |
| header | 自定义头部内容 | ✅ 完全支持 |

#### 插槽使用示例
```vue
<template>
  <div class="slot-examples">
    <!-- 使用默认插槽 -->
    <FuniCard>
      <p>这是默认插槽的内容</p>
      <el-button type="primary">操作按钮</el-button>
    </FuniCard>
    
    <!-- 使用头部插槽 -->
    <FuniCard>
      <template #header>
        <div class="custom-header">
          <span>自定义头部</span>
          <el-button size="small">操作</el-button>
        </div>
      </template>
      <p>这是卡片内容</p>
    </FuniCard>
  </div>
</template>

<style scoped>
.custom-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
```

## FuniCard 扩展功能

### 新增 Props

| 扩展属性 | 类型 | 默认值 | 说明 |
|---------|------|--------|------|
| size | String | default | 卡片尺寸：large/default/small |
| bordered | Boolean | true | 是否显示边框 |
| hoverable | Boolean | false | 是否可悬停 |
| clickable | Boolean | false | 是否可点击 |
| selectable | Boolean | false | 是否可选择 |
| selected | Boolean | false | 是否选中 |
| disabled | Boolean | false | 是否禁用 |
| loading | Boolean | false | 是否加载中 |
| skeleton | Boolean | false | 是否显示骨架屏 |
| actions | Array | [] | 操作按钮配置 |
| collapsible | Boolean | false | 是否可折叠 |
| cover | String | - | 封面图片URL |
| meta | Object | - | 元信息配置 |

### 新增 Events

| 扩展事件 | 参数 | 说明 |
|---------|------|------|
| click | event: Event | 点击卡片时触发 |
| select | selected: Boolean | 选择状态变化时触发 |
| action | action: Object, index: Number | 点击操作按钮时触发 |
| collapse | collapsed: Boolean | 折叠状态变化时触发 |
| cover-click | event: Event | 点击封面时触发 |

### 新增插槽

| 扩展插槽 | 参数 | 说明 |
|---------|------|------|
| extra | - | 头部额外内容 |
| cover | - | 自定义封面 |
| actions | { actions } | 自定义操作区域 |
| footer | - | 自定义底部 |
| loading | - | 自定义加载状态 |
| skeleton | - | 自定义骨架屏 |
| meta | { meta } | 自定义元信息 |

## 透传机制说明

### v-bind 透传
FuniCard 使用 `v-bind="$attrs"` 将所有未声明的属性透传给内部的 `el-card` 组件：

```vue
<template>
  <!-- FuniCard 内部实现 -->
  <el-card 
    v-bind="$attrs"
    :header="header"
    :body-style="bodyStyle"
    :shadow="shadow"
    :class="cardClasses"
    @click="handleClick"
  >
    <!-- 插槽内容 -->
  </el-card>
</template>
```

### 属性优先级
1. **FuniCard 显式声明的 props** - 最高优先级
2. **透传的 ElementPlus 属性** - 中等优先级
3. **默认值** - 最低优先级

### 使用示例
```vue
<template>
  <!-- 混合使用 FuniCard 和 ElementPlus 属性 -->
  <FuniCard
    header="混合属性示例"
    :body-style="{ padding: '24px' }"
    shadow="hover"
    size="large"
    hoverable
    :actions="cardActions"
    @click="handleCardClick"
  >
    <p>这个卡片同时使用了 ElementPlus 和 FuniCard 的属性</p>
  </FuniCard>
</template>

<script setup>
const cardActions = [
  { key: 'edit', label: '编辑', icon: 'Edit' }
]

const handleCardClick = (event) => {
  console.log('卡片被点击', event)
}
</script>
```

## 样式继承

### CSS 类名继承
FuniCard 保持了 ElementPlus el-card 的所有 CSS 类名：

```css
/* ElementPlus 原生类名 */
.el-card {
  /* 卡片容器样式 */
}

.el-card__header {
  /* 头部样式 */
}

.el-card__body {
  /* 内容区域样式 */
}

/* FuniCard 扩展类名 */
.funi-card {
  /* FuniCard 特有样式 */
}

.funi-card--large {
  /* 大尺寸样式 */
}

.funi-card--hoverable {
  /* 可悬停样式 */
}

.funi-card--selected {
  /* 选中状态样式 */
}
```

### 样式覆盖示例
```vue
<template>
  <FuniCard 
    header="自定义样式卡片"
    class="custom-card"
    :body-style="customBodyStyle"
  >
    <p>这是自定义样式的卡片</p>
  </FuniCard>
</template>

<script setup>
const customBodyStyle = {
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  color: 'white',
  padding: '30px'
}
</script>

<style scoped>
.custom-card :deep(.el-card__header) {
  background-color: #409eff;
  color: white;
  border-bottom: none;
}

.custom-card :deep(.el-card) {
  border: 2px solid #409eff;
  border-radius: 12px;
}
</style>
```

## 兼容性说明

### 版本兼容性
- **ElementPlus 版本要求**: >= 2.0.0
- **Vue 版本要求**: >= 3.0.0
- **完全向后兼容**: 所有 el-card 的用法都可以直接迁移到 FuniCard

### 迁移指南
```vue
<!-- 迁移前：使用 el-card -->
<el-card header="标题" shadow="hover" :body-style="{ padding: '20px' }">
  <p>内容</p>
</el-card>

<!-- 迁移后：使用 FuniCard -->
<FuniCard header="标题" shadow="hover" :body-style="{ padding: '20px' }">
  <p>内容</p>
</FuniCard>
```

### 注意事项
1. **事件监听**: FuniCard 的事件监听器会正确透传给 el-card
2. **ref 引用**: 可以通过 ref 访问内部的 el-card 实例
3. **样式隔离**: FuniCard 的样式不会影响原有的 el-card 样式

```vue
<template>
  <FuniCard ref="cardRef" header="引用示例">
    <p>可以通过 ref 访问卡片实例</p>
  </FuniCard>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const cardRef = ref()

onMounted(() => {
  // 访问内部 el-card 实例
  console.log(cardRef.value)
})
</script>
```
