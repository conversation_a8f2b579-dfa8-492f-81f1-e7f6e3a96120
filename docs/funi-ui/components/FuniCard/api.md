# FuniCard API文档

## 组件概述

FuniCard是基于ElementPlus的el-card封装的卡片组件，提供了更丰富的样式配置、交互效果、状态指示等功能，支持头部、底部、操作区域的自定义，适用于信息展示、内容容器、数据卡片等场景。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 | ElementPlus对应 |
|--------|------|--------|------|------|----------------|
| header | String | '' | - | 卡片标题 | el-card.header |
| bodyStyle | Object | {} | - | 卡片内容样式 | el-card.body-style |
| shadow | String | 'always' | - | 阴影显示时机 | el-card.shadow |
| size | String | 'default' | - | 卡片尺寸 | - |
| bordered | Boolean | true | - | 是否显示边框 | - |
| hoverable | Boolean | false | - | 是否可悬停 | - |
| clickable | Boolean | false | - | 是否可点击 | - |
| selectable | Boolean | false | - | 是否可选择 | - |
| selected | Boolean | false | - | 是否选中 | - |
| disabled | Boolean | false | - | 是否禁用 | - |
| loading | Boolean | false | - | 是否加载中 | - |
| skeleton | Boolean | false | - | 是否显示骨架屏 | - |
| skeletonRows | Number | 3 | - | 骨架屏行数 | - |
| status | String | '' | - | 状态类型 | - |
| statusColor | String | '' | - | 状态颜色 | - |
| statusPosition | String | 'top' | - | 状态位置 | - |
| icon | String | '' | - | 头部图标 | - |
| avatar | String | '' | - | 头部头像 | - |
| extra | String | '' | - | 头部额外内容 | - |
| actions | Array | [] | - | 操作按钮配置 | - |
| actionsPosition | String | 'bottom' | - | 操作按钮位置 | - |
| cover | String | '' | - | 封面图片 | - |
| coverHeight | String | '200px' | - | 封面高度 | - |
| coverFit | String | 'cover' | - | 封面适应方式 | - |
| meta | Object | {} | - | 元信息配置 | - |
| collapsible | Boolean | false | - | 是否可折叠 | - |
| collapsed | Boolean | false | - | 是否折叠 | - |
| collapseIcon | String | 'ArrowDown' | - | 折叠图标 | - |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| click | event: Event | 点击事件 | 点击卡片时 |
| select | selected: Boolean | 选择事件 | 选择状态变化时 |
| action | action: Object, index: Number | 操作事件 | 点击操作按钮时 |
| collapse | collapsed: Boolean | 折叠事件 | 折叠状态变化时 |
| cover-click | event: Event | 封面点击事件 | 点击封面时 |
| cover-load | event: Event | 封面加载事件 | 封面加载完成时 |
| cover-error | event: Event | 封面错误事件 | 封面加载失败时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| select | (selected?: Boolean) | void | 设置选择状态 |
| collapse | (collapsed?: Boolean) | void | 设置折叠状态 |
| refresh | - | void | 刷新卡片内容 |

## Slots

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| default | - | 卡片内容 |
| header | - | 自定义头部 |
| extra | - | 头部额外内容 |
| cover | - | 自定义封面 |
| actions | { actions } | 自定义操作区域 |
| footer | - | 自定义底部 |
| loading | - | 自定义加载状态 |
| skeleton | - | 自定义骨架屏 |

## 配置结构

### 操作按钮配置
```typescript
interface ActionConfig {
  key: string;                     // 操作键
  label: string;                   // 显示文本
  icon?: string;                   // 图标
  type?: string;                   // 按钮类型
  disabled?: boolean;              // 是否禁用
  loading?: boolean;               // 是否加载中
  permission?: string;             // 权限标识
  tooltip?: string;                // 提示信息
  [key: string]: any;              // 其他配置
}
```

### 元信息配置
```typescript
interface MetaConfig {
  title?: string;                  // 标题
  description?: string;            // 描述
  avatar?: string;                 // 头像
  author?: string;                 // 作者
  date?: string;                   // 日期
  tags?: string[];                 // 标签
  [key: string]: any;              // 其他信息
}
```

## 使用示例

### 基础卡片
```vue
<template>
  <div class="card-examples">
    <div class="card-row">
      <FuniCard header="基础卡片">
        <p>这是一个基础的卡片内容。</p>
        <p>可以包含任意的HTML内容。</p>
      </FuniCard>
      
      <FuniCard header="带阴影的卡片" shadow="hover">
        <p>鼠标悬停时显示阴影效果。</p>
      </FuniCard>
      
      <FuniCard header="无边框卡片" :bordered="false">
        <p>没有边框的卡片样式。</p>
      </FuniCard>
    </div>
  </div>
</template>

<style scoped>
.card-examples {
  padding: 20px;
}

.card-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}
</style>
```

### 交互式卡片
```vue
<template>
  <div class="interactive-cards">
    <div class="card-row">
      <FuniCard 
        header="可点击卡片"
        clickable
        hoverable
        @click="handleCardClick('card1')"
      >
        <p>点击这个卡片会触发点击事件。</p>
      </FuniCard>
      
      <FuniCard 
        header="可选择卡片"
        selectable
        :selected="selectedCards.includes('card2')"
        @select="handleCardSelect('card2', $event)"
      >
        <p>这个卡片可以被选中。</p>
      </FuniCard>
      
      <FuniCard 
        header="禁用卡片"
        disabled
      >
        <p>这个卡片处于禁用状态。</p>
      </FuniCard>
    </div>
    
    <div class="card-row">
      <FuniCard 
        header="加载中卡片"
        loading
      >
        <p>卡片内容正在加载中...</p>
      </FuniCard>
      
      <FuniCard 
        header="骨架屏卡片"
        skeleton
        :skeleton-rows="4"
      >
        <p>这里会显示骨架屏效果。</p>
      </FuniCard>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const selectedCards = ref([])

const handleCardClick = (cardId) => {
  console.log('点击卡片:', cardId)
  ElMessage.info(`点击了${cardId}`)
}

const handleCardSelect = (cardId, selected) => {
  if (selected) {
    if (!selectedCards.value.includes(cardId)) {
      selectedCards.value.push(cardId)
    }
  } else {
    const index = selectedCards.value.indexOf(cardId)
    if (index > -1) {
      selectedCards.value.splice(index, 1)
    }
  }
  console.log('选择卡片:', cardId, selected)
}
</script>

<style scoped>
.interactive-cards {
  padding: 20px;
}

.card-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}
</style>
```

### 带操作按钮的卡片
```vue
<template>
  <div class="action-cards">
    <div class="card-row">
      <FuniCard 
        header="用户信息"
        :actions="userActions"
        @action="handleUserAction"
      >
        <div class="user-info">
          <p><strong>姓名：</strong>张三</p>
          <p><strong>邮箱：</strong><EMAIL></p>
          <p><strong>部门：</strong>技术部</p>
        </div>
      </FuniCard>
      
      <FuniCard 
        header="项目信息"
        :actions="projectActions"
        actions-position="top"
        @action="handleProjectAction"
      >
        <div class="project-info">
          <p><strong>项目名称：</strong>管理系统</p>
          <p><strong>状态：</strong>进行中</p>
          <p><strong>进度：</strong>75%</p>
        </div>
      </FuniCard>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { ElMessage } from 'element-plus'

const userActions = reactive([
  { key: 'edit', label: '编辑', icon: 'Edit', type: 'primary' },
  { key: 'delete', label: '删除', icon: 'Delete', type: 'danger' },
  { key: 'view', label: '查看', icon: 'View' }
])

const projectActions = reactive([
  { key: 'start', label: '开始', icon: 'VideoPlay', type: 'success' },
  { key: 'pause', label: '暂停', icon: 'VideoPause', type: 'warning' },
  { key: 'stop', label: '停止', icon: 'VideoStop', type: 'danger' }
])

const handleUserAction = (action, index) => {
  console.log('用户操作:', action, index)
  ElMessage.info(`执行${action.label}操作`)
}

const handleProjectAction = (action, index) => {
  console.log('项目操作:', action, index)
  ElMessage.info(`执行${action.label}操作`)
}
</script>

<style scoped>
.action-cards {
  padding: 20px;
}

.card-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.user-info, .project-info {
  padding: 8px 0;
}

.user-info p, .project-info p {
  margin: 8px 0;
  font-size: 14px;
}
</style>
```

### 带封面的卡片
```vue
<template>
  <div class="cover-cards">
    <div class="card-row">
      <FuniCard 
        cover="https://example.com/image1.jpg"
        cover-height="180px"
        :meta="articleMeta1"
        :actions="articleActions"
        @cover-click="handleCoverClick"
        @action="handleArticleAction"
      >
        <p>这是一篇关于Vue.js开发的技术文章，介绍了Vue 3的新特性和最佳实践...</p>
      </FuniCard>
      
      <FuniCard 
        cover="https://example.com/image2.jpg"
        cover-height="180px"
        :meta="articleMeta2"
        :actions="articleActions"
        @cover-click="handleCoverClick"
        @action="handleArticleAction"
      >
        <p>这是一篇关于React开发的技术文章，深入探讨了React Hooks的使用技巧...</p>
      </FuniCard>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'

const articleMeta1 = reactive({
  title: 'Vue.js 3.0 新特性详解',
  author: '张三',
  date: '2023-12-01',
  tags: ['Vue.js', '前端', '技术']
})

const articleMeta2 = reactive({
  title: 'React Hooks 最佳实践',
  author: '李四',
  date: '2023-11-28',
  tags: ['React', '前端', 'Hooks']
})

const articleActions = reactive([
  { key: 'like', label: '点赞', icon: 'Star' },
  { key: 'share', label: '分享', icon: 'Share' },
  { key: 'comment', label: '评论', icon: 'ChatDotRound' }
])

const handleCoverClick = (event) => {
  console.log('点击封面:', event)
}

const handleArticleAction = (action, index) => {
  console.log('文章操作:', action, index)
}
</script>

<style scoped>
.cover-cards {
  padding: 20px;
}

.card-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 16px;
}
</style>
```

### 可折叠卡片
```vue
<template>
  <div class="collapsible-cards">
    <FuniCard 
      header="系统设置"
      collapsible
      :collapsed="collapsed1"
      @collapse="collapsed1 = $event"
    >
      <div class="settings-content">
        <el-form label-width="100px">
          <el-form-item label="主题色">
            <el-color-picker v-model="settings.themeColor" />
          </el-form-item>
          <el-form-item label="语言">
            <el-select v-model="settings.language">
              <el-option label="中文" value="zh" />
              <el-option label="English" value="en" />
            </el-select>
          </el-form-item>
          <el-form-item label="通知">
            <el-switch v-model="settings.notification" />
          </el-form-item>
        </el-form>
      </div>
    </FuniCard>
    
    <FuniCard 
      header="高级配置"
      collapsible
      :collapsed="collapsed2"
      collapse-icon="ArrowUp"
      @collapse="collapsed2 = $event"
    >
      <div class="advanced-content">
        <el-form label-width="120px">
          <el-form-item label="缓存大小">
            <el-input-number v-model="advanced.cacheSize" :min="1" :max="1000" />
          </el-form-item>
          <el-form-item label="自动保存">
            <el-switch v-model="advanced.autoSave" />
          </el-form-item>
          <el-form-item label="调试模式">
            <el-switch v-model="advanced.debugMode" />
          </el-form-item>
        </el-form>
      </div>
    </FuniCard>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const collapsed1 = ref(false)
const collapsed2 = ref(true)

const settings = reactive({
  themeColor: '#409eff',
  language: 'zh',
  notification: true
})

const advanced = reactive({
  cacheSize: 100,
  autoSave: true,
  debugMode: false
})
</script>

<style scoped>
.collapsible-cards {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 600px;
}

.settings-content, .advanced-content {
  padding: 16px 0;
}
</style>
```

## ElementPlus API支持

FuniCard基于el-card封装，支持所有el-card的API：

```vue
<template>
  <FuniCard
    <!-- ElementPlus el-card 所有属性 -->
    header="卡片标题"
    :body-style="{ padding: '20px' }"
    shadow="always"
  >
    卡片内容
  </FuniCard>
</template>
```

## 注意事项

### 1. 布局设计
- 合理设置卡片的尺寸和间距
- 考虑响应式布局的适配
- 保持卡片内容的层次结构
- 避免卡片内容过于复杂

### 2. 交互体验
- 提供清晰的交互反馈
- 合理设置悬停和点击效果
- 支持键盘导航
- 处理加载和错误状态

### 3. 性能考虑
- 大量卡片时使用虚拟滚动
- 合理使用懒加载
- 避免频繁的状态更新
- 优化图片和媒体资源

### 4. 可访问性
- 提供合适的语义化标签
- 支持屏幕阅读器
- 合理设置焦点管理
- 提供键盘操作支持

## 常见问题

### Q: 如何实现卡片的拖拽排序？
A: 结合拖拽库（如Sortable.js）实现卡片的拖拽排序功能

### Q: 如何自定义卡片的样式？
A: 通过CSS变量、bodyStyle属性或自定义类名来修改样式

### Q: 如何实现卡片的懒加载？
A: 结合Intersection Observer API实现卡片内容的懒加载

### Q: 如何处理卡片内容的溢出？
A: 设置合适的高度和overflow属性，或使用省略号显示
