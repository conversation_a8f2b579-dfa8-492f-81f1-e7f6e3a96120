# FuniCard 最佳实践

## 设计原则

### 1. 内容优先
卡片的主要目的是展示内容，设计时应该：
- 保持内容的可读性和层次结构
- 避免过度装饰影响内容阅读
- 合理使用留白和间距
- 确保重要信息突出显示

### 2. 一致性
在同一个应用中使用卡片时：
- 保持相同类型卡片的尺寸一致
- 统一阴影、圆角等视觉效果
- 保持操作按钮的位置和样式一致
- 使用统一的颜色和字体规范

### 3. 响应式设计
卡片应该适应不同屏幕尺寸：
- 使用弹性布局和网格系统
- 设置合适的最小和最大宽度
- 考虑移动端的触摸交互
- 优化小屏幕上的内容显示

## 布局最佳实践

### 网格布局推荐
```vue
<template>
  <div class="card-grid">
    <!-- 响应式网格布局 -->
    <FuniCard 
      v-for="item in items" 
      :key="item.id"
      :header="item.title"
      class="grid-card"
    >
      {{ item.content }}
    </FuniCard>
  </div>
</template>

<style scoped>
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  padding: 16px;
}

.grid-card {
  min-height: 200px;
}

/* 响应式断点 */
@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 12px;
  }
}
</style>
```

### 间距规范
```css
/* 推荐的间距规范 */
.card-spacing {
  /* 卡片之间的间距 */
  margin-bottom: 16px;
  
  /* 卡片内容间距 */
  --card-padding-small: 12px;
  --card-padding-default: 16px;
  --card-padding-large: 24px;
}

/* 不同尺寸的内边距 */
.funi-card--small {
  --el-card-padding: var(--card-padding-small);
}

.funi-card--large {
  --el-card-padding: var(--card-padding-large);
}
```

## 交互设计最佳实践

### 状态反馈
```vue
<template>
  <div class="interactive-cards">
    <!-- 提供清晰的交互反馈 -->
    <FuniCard 
      v-for="card in cards" 
      :key="card.id"
      :header="card.title"
      :clickable="card.clickable"
      :hoverable="card.hoverable"
      :selected="selectedCards.includes(card.id)"
      :disabled="card.disabled"
      @click="handleCardClick(card)"
      @select="handleCardSelect(card.id, $event)"
      class="interactive-card"
    >
      <p>{{ card.content }}</p>
      
      <!-- 状态指示器 -->
      <div class="card-status">
        <el-tag 
          v-if="card.status" 
          :type="getStatusType(card.status)"
          size="small"
        >
          {{ card.status }}
        </el-tag>
      </div>
    </FuniCard>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const selectedCards = ref([])

const getStatusType = (status) => {
  const statusMap = {
    'active': 'success',
    'pending': 'warning',
    'inactive': 'info',
    'error': 'danger'
  }
  return statusMap[status] || 'info'
}

const handleCardClick = (card) => {
  if (!card.disabled) {
    // 提供视觉反馈
    console.log('卡片点击:', card.title)
  }
}

const handleCardSelect = (cardId, selected) => {
  if (selected) {
    selectedCards.value.push(cardId)
  } else {
    const index = selectedCards.value.indexOf(cardId)
    if (index > -1) {
      selectedCards.value.splice(index, 1)
    }
  }
}
</script>

<style scoped>
.interactive-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.interactive-card:hover {
  transform: translateY(-2px);
}

.interactive-card.is-selected {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
}

.interactive-card.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.card-status {
  margin-top: 12px;
  text-align: right;
}
</style>
```

### 键盘导航支持
```vue
<template>
  <div class="keyboard-nav-cards">
    <FuniCard 
      v-for="(card, index) in cards" 
      :key="card.id"
      :header="card.title"
      :tabindex="0"
      @keydown="handleKeydown($event, index)"
      @focus="handleFocus(index)"
      class="nav-card"
    >
      {{ card.content }}
    </FuniCard>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const focusedIndex = ref(0)

const handleKeydown = (event, index) => {
  switch (event.key) {
    case 'Enter':
    case ' ':
      event.preventDefault()
      // 触发点击事件
      handleCardActivate(index)
      break
    case 'ArrowDown':
    case 'ArrowRight':
      event.preventDefault()
      focusNext(index)
      break
    case 'ArrowUp':
    case 'ArrowLeft':
      event.preventDefault()
      focusPrevious(index)
      break
  }
}

const handleFocus = (index) => {
  focusedIndex.value = index
}

const focusNext = (currentIndex) => {
  const nextIndex = (currentIndex + 1) % cards.length
  focusCard(nextIndex)
}

const focusPrevious = (currentIndex) => {
  const prevIndex = currentIndex === 0 ? cards.length - 1 : currentIndex - 1
  focusCard(prevIndex)
}

const focusCard = (index) => {
  const cards = document.querySelectorAll('.nav-card')
  if (cards[index]) {
    cards[index].focus()
  }
}
</script>

<style scoped>
.nav-card:focus {
  outline: 2px solid var(--el-color-primary);
  outline-offset: 2px;
}
</style>
```

## 性能优化最佳实践

### 虚拟滚动
对于大量卡片的场景，使用虚拟滚动：

```vue
<template>
  <div class="virtual-card-list">
    <el-virtual-list 
      :data="cardData"
      :height="600"
      :item-size="200"
    >
      <template #default="{ item, index }">
        <FuniCard 
          :key="item.id"
          :header="item.title"
          class="virtual-card"
        >
          {{ item.content }}
        </FuniCard>
      </template>
    </el-virtual-list>
  </div>
</template>

<style scoped>
.virtual-card {
  margin: 8px;
  height: 180px;
}
</style>
```

### 懒加载图片
```vue
<template>
  <FuniCard 
    :cover="card.cover"
    :header="card.title"
  >
    <!-- 使用懒加载图片 -->
    <el-image 
      v-for="img in card.images"
      :key="img.id"
      :src="img.url"
      :lazy="true"
      fit="cover"
      class="card-image"
    >
      <template #placeholder>
        <div class="image-placeholder">
          <el-icon><Picture /></el-icon>
        </div>
      </template>
      <template #error>
        <div class="image-error">
          <el-icon><PictureFilled /></el-icon>
        </div>
      </template>
    </el-image>
  </FuniCard>
</template>

<style scoped>
.card-image {
  width: 100%;
  height: 200px;
  margin-bottom: 8px;
}

.image-placeholder,
.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: var(--el-fill-color-light);
  color: var(--el-text-color-placeholder);
}
</style>
```

### 骨架屏优化
```vue
<template>
  <FuniCard 
    :header="card.title"
    :skeleton="isLoading"
    :skeleton-rows="3"
  >
    <template #skeleton>
      <!-- 自定义骨架屏 -->
      <div class="custom-skeleton">
        <el-skeleton :rows="2" animated />
        <div class="skeleton-actions">
          <el-skeleton-item variant="button" style="width: 80px; margin-right: 8px;" />
          <el-skeleton-item variant="button" style="width: 60px;" />
        </div>
      </div>
    </template>
    
    <!-- 实际内容 -->
    <div v-if="!isLoading">
      <p>{{ card.content }}</p>
      <div class="card-actions">
        <el-button type="primary">编辑</el-button>
        <el-button>删除</el-button>
      </div>
    </div>
  </FuniCard>
</template>

<style scoped>
.custom-skeleton {
  padding: 16px 0;
}

.skeleton-actions {
  margin-top: 16px;
  display: flex;
}
</style>
```

## 可访问性最佳实践

### ARIA 属性
```vue
<template>
  <FuniCard 
    :header="card.title"
    :aria-label="card.ariaLabel"
    :aria-describedby="card.id + '-description'"
    role="article"
    tabindex="0"
  >
    <p :id="card.id + '-description'">{{ card.description }}</p>
    
    <!-- 操作按钮的可访问性 -->
    <div class="card-actions" role="group" aria-label="卡片操作">
      <el-button 
        v-for="action in card.actions"
        :key="action.key"
        :aria-label="action.ariaLabel || action.label"
        @click="handleAction(action)"
      >
        {{ action.label }}
      </el-button>
    </div>
  </FuniCard>
</template>
```

### 颜色对比度
```css
/* 确保足够的颜色对比度 */
.funi-card {
  /* 文字与背景对比度至少 4.5:1 */
  color: #303133;
  background-color: #ffffff;
}

.funi-card--dark {
  color: #ffffff;
  background-color: #2d3748;
}

/* 状态颜色的可访问性 */
.card-status--success {
  color: #067f23; /* 深绿色，确保对比度 */
  background-color: #f0f9ff;
}

.card-status--error {
  color: #c53030; /* 深红色，确保对比度 */
  background-color: #fed7d7;
}
```

## 常见错误和避免方法

### 1. 避免过度嵌套
```vue
<!-- ❌ 错误：过度嵌套 -->
<FuniCard>
  <FuniCard>
    <FuniCard>
      内容
    </FuniCard>
  </FuniCard>
</FuniCard>

<!-- ✅ 正确：合理的层次结构 -->
<FuniCard header="主卡片">
  <div class="content-section">
    <h4>子内容标题</h4>
    <p>子内容</p>
  </div>
</FuniCard>
```

### 2. 避免内容溢出
```vue
<template>
  <FuniCard header="内容管理" class="content-card">
    <!-- 使用适当的文本截断 -->
    <p class="card-description">{{ truncatedDescription }}</p>
    
    <!-- 为长内容提供展开选项 -->
    <el-button 
      v-if="description.length > 100"
      text 
      @click="toggleExpanded"
    >
      {{ isExpanded ? '收起' : '展开' }}
    </el-button>
  </FuniCard>
</template>

<style scoped>
.content-card {
  max-width: 400px;
}

.card-description {
  word-break: break-word;
  overflow-wrap: break-word;
  line-height: 1.5;
}
</style>
```

### 3. 避免过多操作按钮
```vue
<template>
  <!-- ❌ 错误：操作按钮过多 -->
  <FuniCard :actions="tooManyActions">
    内容
  </FuniCard>

  <!-- ✅ 正确：合理的操作数量 -->
  <FuniCard :actions="primaryActions">
    内容
    
    <!-- 更多操作放在下拉菜单中 -->
    <template #extra>
      <el-dropdown>
        <el-icon><MoreFilled /></el-icon>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item 
              v-for="action in secondaryActions"
              :key="action.key"
              @click="handleAction(action)"
            >
              {{ action.label }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </template>
  </FuniCard>
</template>

<script setup>
// 主要操作（最多3-4个）
const primaryActions = [
  { key: 'edit', label: '编辑', icon: 'Edit', type: 'primary' },
  { key: 'share', label: '分享', icon: 'Share' }
]

// 次要操作放在下拉菜单中
const secondaryActions = [
  { key: 'copy', label: '复制' },
  { key: 'move', label: '移动' },
  { key: 'delete', label: '删除' }
]
</script>
```

## 业务场景最佳实践

### 数据展示卡片
```vue
<template>
  <div class="data-cards">
    <!-- 统计数据卡片 -->
    <FuniCard
      v-for="stat in statistics"
      :key="stat.key"
      :header="stat.title"
      class="stat-card"
      hoverable
    >
      <div class="stat-content">
        <div class="stat-value" :class="stat.trend">
          {{ stat.value }}
        </div>
        <div class="stat-change">
          <el-icon>
            <ArrowUp v-if="stat.change > 0" />
            <ArrowDown v-else />
          </el-icon>
          {{ Math.abs(stat.change) }}%
        </div>
      </div>
      <div class="stat-description">{{ stat.description }}</div>
    </FuniCard>
  </div>
</template>

<style scoped>
.data-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.stat-card {
  text-align: center;
  min-height: 150px;
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-value.positive {
  color: var(--el-color-success);
}

.stat-value.negative {
  color: var(--el-color-danger);
}
</style>
```

### 表单卡片
```vue
<template>
  <FuniCard header="用户信息" class="form-card">
    <el-form :model="form" label-width="100px">
      <el-form-item label="姓名" required>
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="邮箱" required>
        <el-input v-model="form.email" type="email" />
      </el-form-item>
      <el-form-item label="部门">
        <el-select v-model="form.department">
          <el-option label="技术部" value="tech" />
          <el-option label="产品部" value="product" />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </div>
    </template>
  </FuniCard>
</template>

<style scoped>
.form-card {
  max-width: 500px;
  margin: 0 auto;
}

.form-actions {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-light);
}
</style>
```

### 列表卡片
```vue
<template>
  <FuniCard header="最近活动" class="list-card">
    <div class="activity-list">
      <div
        v-for="activity in activities"
        :key="activity.id"
        class="activity-item"
      >
        <el-avatar :src="activity.avatar" :size="32" />
        <div class="activity-content">
          <p class="activity-text">{{ activity.text }}</p>
          <span class="activity-time">{{ activity.time }}</span>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button text @click="loadMore">查看更多</el-button>
    </template>
  </FuniCard>
</template>

<style scoped>
.list-card {
  max-width: 400px;
}

.activity-item {
  display: flex;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-content {
  flex: 1;
}

.activity-text {
  margin: 0 0 4px 0;
  font-size: 14px;
}

.activity-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}
</style>
```

## 主题定制最佳实践

### CSS 变量定制
```css
/* 定制卡片主题 */
:root {
  /* 基础颜色 */
  --funi-card-bg-color: #ffffff;
  --funi-card-border-color: #ebeef5;
  --funi-card-text-color: #303133;

  /* 阴影 */
  --funi-card-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.12);
  --funi-card-shadow-base: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --funi-card-shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.12);

  /* 圆角 */
  --funi-card-border-radius: 4px;

  /* 间距 */
  --funi-card-padding: 20px;
  --funi-card-header-padding: 18px 20px;
}

/* 暗色主题 */
[data-theme="dark"] {
  --funi-card-bg-color: #2d3748;
  --funi-card-border-color: #4a5568;
  --funi-card-text-color: #ffffff;
  --funi-card-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.3);
  --funi-card-shadow-base: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}
```

### 品牌色定制
```vue
<template>
  <div class="branded-cards">
    <FuniCard
      header="品牌卡片"
      class="brand-card"
      hoverable
    >
      <p>使用品牌色的卡片设计</p>
    </FuniCard>
  </div>
</template>

<style scoped>
.brand-card {
  border-top: 4px solid var(--brand-primary-color);
}

.brand-card :deep(.el-card__header) {
  background: linear-gradient(135deg, var(--brand-primary-color), var(--brand-secondary-color));
  color: white;
}

.brand-card:hover {
  box-shadow: 0 8px 24px rgba(var(--brand-primary-rgb), 0.15);
}
</style>
```

## 测试最佳实践

### 单元测试示例
```javascript
import { mount } from '@vue/test-utils'
import FuniCard from '@/components/FuniCard'

describe('FuniCard', () => {
  test('renders header correctly', () => {
    const wrapper = mount(FuniCard, {
      props: {
        header: 'Test Header'
      }
    })

    expect(wrapper.find('.el-card__header').text()).toBe('Test Header')
  })

  test('emits click event when clickable', async () => {
    const wrapper = mount(FuniCard, {
      props: {
        clickable: true
      }
    })

    await wrapper.find('.el-card').trigger('click')
    expect(wrapper.emitted('click')).toBeTruthy()
  })

  test('shows skeleton when loading', () => {
    const wrapper = mount(FuniCard, {
      props: {
        skeleton: true,
        skeletonRows: 3
      }
    })

    expect(wrapper.find('.el-skeleton').exists()).toBe(true)
  })
})
```

### E2E 测试示例
```javascript
// cypress/integration/funi-card.spec.js
describe('FuniCard Component', () => {
  beforeEach(() => {
    cy.visit('/components/funi-card')
  })

  it('should be clickable when clickable prop is true', () => {
    cy.get('[data-testid="clickable-card"]')
      .should('be.visible')
      .click()

    cy.get('[data-testid="click-count"]')
      .should('contain', '1')
  })

  it('should show actions when provided', () => {
    cy.get('[data-testid="action-card"]')
      .find('.card-actions')
      .should('be.visible')
      .find('button')
      .should('have.length', 2)
  })
})
```

## 性能监控

### 性能指标监控
```javascript
// 监控卡片渲染性能
const observer = new PerformanceObserver((list) => {
  for (const entry of list.getEntries()) {
    if (entry.name.includes('funi-card')) {
      console.log(`FuniCard ${entry.name}: ${entry.duration}ms`)
    }
  }
})

observer.observe({ entryTypes: ['measure'] })

// 在组件中标记性能
export default {
  mounted() {
    performance.mark('funi-card-mount-start')
  },
  updated() {
    performance.mark('funi-card-update-end')
    performance.measure(
      'funi-card-update',
      'funi-card-mount-start',
      'funi-card-update-end'
    )
  }
}
```

### 内存泄漏预防
```vue
<script setup>
import { onBeforeUnmount } from 'vue'

// 清理定时器
const timers = []

const addTimer = (timer) => {
  timers.push(timer)
}

onBeforeUnmount(() => {
  // 清理所有定时器
  timers.forEach(timer => clearTimeout(timer))
  timers.length = 0
})

// 清理事件监听器
const cleanup = []

const addEventListener = (element, event, handler) => {
  element.addEventListener(event, handler)
  cleanup.push(() => element.removeEventListener(event, handler))
}

onBeforeUnmount(() => {
  cleanup.forEach(fn => fn())
})
</script>
```
