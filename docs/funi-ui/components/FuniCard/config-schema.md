# FuniCard 配置结构定义

## 基础配置接口

### FuniCardProps
```typescript
interface FuniCardProps {
  // 基础属性
  header?: string;                     // 卡片标题
  bodyStyle?: Record<string, any>;     // 卡片内容样式
  shadow?: 'always' | 'hover' | 'never'; // 阴影显示时机
  
  // 尺寸和外观
  size?: 'large' | 'default' | 'small'; // 卡片尺寸
  bordered?: boolean;                   // 是否显示边框
  hoverable?: boolean;                  // 是否可悬停
  
  // 交互状态
  clickable?: boolean;                  // 是否可点击
  selectable?: boolean;                 // 是否可选择
  selected?: boolean;                   // 是否选中
  disabled?: boolean;                   // 是否禁用
  
  // 加载状态
  loading?: boolean;                    // 是否加载中
  skeleton?: boolean;                   // 是否显示骨架屏
  skeletonRows?: number;                // 骨架屏行数
  
  // 封面配置
  cover?: string;                       // 封面图片URL
  coverHeight?: string;                 // 封面高度
  coverFit?: 'fill' | 'contain' | 'cover' | 'none' | 'scale-down'; // 封面适应方式
  
  // 操作配置
  actions?: ActionConfig[];             // 操作按钮配置
  actionsPosition?: 'top' | 'bottom';   // 操作按钮位置
  
  // 折叠配置
  collapsible?: boolean;                // 是否可折叠
  collapsed?: boolean;                  // 是否折叠
  collapseIcon?: string;                // 折叠图标
  
  // 元信息
  meta?: MetaConfig;                    // 元信息配置
  
  // 自定义样式
  customClass?: string;                 // 自定义CSS类名
  customStyle?: Record<string, any>;    // 自定义样式
}
```

### ActionConfig - 操作按钮配置
```typescript
interface ActionConfig {
  key: string;                         // 操作唯一标识
  label: string;                       // 显示文本
  icon?: string;                       // 图标名称
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text'; // 按钮类型
  size?: 'large' | 'default' | 'small'; // 按钮尺寸
  disabled?: boolean;                  // 是否禁用
  loading?: boolean;                   // 是否加载中
  permission?: string;                 // 权限标识
  tooltip?: string;                    // 提示信息
  visible?: boolean;                   // 是否可见
  order?: number;                      // 排序权重
  [key: string]: any;                  // 其他自定义属性
}
```

### MetaConfig - 元信息配置
```typescript
interface MetaConfig {
  title?: string;                      // 标题
  description?: string;                // 描述
  avatar?: string;                     // 头像URL
  author?: string;                     // 作者
  date?: string | Date;                // 日期
  tags?: string[];                     // 标签列表
  category?: string;                   // 分类
  status?: string;                     // 状态
  priority?: 'high' | 'medium' | 'low'; // 优先级
  extra?: Record<string, any>;         // 额外信息
}
```

### SkeletonConfig - 骨架屏配置
```typescript
interface SkeletonConfig {
  rows?: number;                       // 行数
  animated?: boolean;                  // 是否动画
  avatar?: boolean;                    // 是否显示头像
  loading?: boolean;                   // 是否加载中
  throttle?: number;                   // 节流时间
}
```

## 事件配置

### CardEvents
```typescript
interface CardEvents {
  // 基础事件
  click?: (event: Event) => void;      // 点击事件
  select?: (selected: boolean) => void; // 选择事件
  
  // 操作事件
  action?: (action: ActionConfig, index: number) => void; // 操作事件
  
  // 折叠事件
  collapse?: (collapsed: boolean) => void; // 折叠事件
  
  // 封面事件
  'cover-click'?: (event: Event) => void; // 封面点击事件
  'cover-load'?: (event: Event) => void;  // 封面加载事件
  'cover-error'?: (event: Event) => void; // 封面错误事件
  
  // 状态事件
  'loading-change'?: (loading: boolean) => void; // 加载状态变化
  'visible-change'?: (visible: boolean) => void; // 可见状态变化
}
```

## 插槽配置

### CardSlots
```typescript
interface CardSlots {
  default?: () => VNode[];             // 默认内容插槽
  header?: () => VNode[];              // 头部插槽
  extra?: () => VNode[];               // 头部额外内容插槽
  cover?: () => VNode[];               // 封面插槽
  actions?: (props: { actions: ActionConfig[] }) => VNode[]; // 操作区域插槽
  footer?: () => VNode[];              // 底部插槽
  loading?: () => VNode[];             // 加载状态插槽
  skeleton?: () => VNode[];            // 骨架屏插槽
  meta?: (props: { meta: MetaConfig }) => VNode[]; // 元信息插槽
}
```

## 方法配置

### CardMethods
```typescript
interface CardMethods {
  select: (selected?: boolean) => void; // 设置选择状态
  collapse: (collapsed?: boolean) => void; // 设置折叠状态
  refresh: () => void;                  // 刷新卡片内容
  scrollIntoView: (options?: ScrollIntoViewOptions) => void; // 滚动到视图
  focus: () => void;                    // 获取焦点
  blur: () => void;                     // 失去焦点
}
```

## 常用配置组合

### 基础信息卡片
```typescript
const basicCardConfig: FuniCardProps = {
  header: '用户信息',
  bordered: true,
  shadow: 'hover',
  bodyStyle: { padding: '20px' }
}
```

### 交互式卡片
```typescript
const interactiveCardConfig: FuniCardProps = {
  header: '可选择卡片',
  clickable: true,
  selectable: true,
  hoverable: true,
  actions: [
    { key: 'edit', label: '编辑', icon: 'Edit', type: 'primary' },
    { key: 'delete', label: '删除', icon: 'Delete', type: 'danger' }
  ]
}
```

### 内容卡片
```typescript
const contentCardConfig: FuniCardProps = {
  cover: 'https://example.com/image.jpg',
  coverHeight: '200px',
  meta: {
    title: '文章标题',
    author: '作者名称',
    date: '2023-12-01',
    tags: ['技术', '前端']
  },
  actions: [
    { key: 'like', label: '点赞', icon: 'Star' },
    { key: 'share', label: '分享', icon: 'Share' }
  ]
}
```

### 可折叠卡片
```typescript
const collapsibleCardConfig: FuniCardProps = {
  header: '设置选项',
  collapsible: true,
  collapsed: false,
  collapseIcon: 'ArrowDown'
}
```

### 加载状态卡片
```typescript
const loadingCardConfig: FuniCardProps = {
  header: '数据加载中',
  loading: true,
  skeleton: true,
  skeletonRows: 4
}
```

## 样式配置

### 主题样式变量
```css
:root {
  /* 卡片基础样式 */
  --funi-card-border-radius: 4px;
  --funi-card-border-color: #ebeef5;
  --funi-card-background-color: #ffffff;
  
  /* 阴影样式 */
  --funi-card-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.12);
  --funi-card-shadow-base: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  /* 头部样式 */
  --funi-card-header-padding: 18px 20px;
  --funi-card-header-border-bottom: 1px solid #ebeef5;
  
  /* 内容样式 */
  --funi-card-body-padding: 20px;
  
  /* 状态样式 */
  --funi-card-hover-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  --funi-card-selected-border-color: #409eff;
  --funi-card-disabled-opacity: 0.6;
}
```

### 尺寸配置
```typescript
const sizeConfig = {
  large: {
    headerPadding: '24px',
    bodyPadding: '24px',
    fontSize: '16px'
  },
  default: {
    headerPadding: '18px 20px',
    bodyPadding: '20px',
    fontSize: '14px'
  },
  small: {
    headerPadding: '12px 16px',
    bodyPadding: '16px',
    fontSize: '12px'
  }
}
```

## 最佳实践配置

### 响应式配置
```typescript
const responsiveCardConfig: FuniCardProps = {
  customStyle: {
    width: '100%',
    maxWidth: '400px',
    minHeight: '200px'
  },
  bodyStyle: {
    padding: 'clamp(16px, 4vw, 24px)'
  }
}
```

### 无障碍配置
```typescript
const accessibleCardConfig: FuniCardProps = {
  customClass: 'accessible-card',
  // 通过CSS添加适当的ARIA属性和焦点样式
}
```

### 性能优化配置
```typescript
const optimizedCardConfig: FuniCardProps = {
  skeleton: true,
  skeletonRows: 3,
  // 使用懒加载和虚拟滚动
  loading: false
}
```
