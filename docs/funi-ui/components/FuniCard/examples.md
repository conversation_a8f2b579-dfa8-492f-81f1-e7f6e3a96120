# FuniCard 使用示例

## 基础卡片示例

### 简单卡片
```vue
<template>
  <div class="card-examples">
    <div class="card-row">
      <!-- 基础卡片 -->
      <FuniCard header="基础卡片">
        <p>这是一个基础的卡片内容。</p>
        <p>可以包含任意的HTML内容。</p>
      </FuniCard>
      
      <!-- 带阴影的卡片 -->
      <FuniCard header="悬停阴影" shadow="hover">
        <p>鼠标悬停时显示阴影效果。</p>
      </FuniCard>
      
      <!-- 无边框卡片 -->
      <FuniCard header="无边框卡片" :bordered="false">
        <p>没有边框的卡片样式。</p>
      </FuniCard>
    </div>
  </div>
</template>

<style scoped>
.card-examples {
  padding: 20px;
}

.card-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}
</style>
```

### 不同尺寸的卡片
```vue
<template>
  <div class="size-examples">
    <div class="card-row">
      <FuniCard header="小尺寸卡片" size="small">
        <p>这是小尺寸的卡片。</p>
      </FuniCard>
      
      <FuniCard header="默认尺寸卡片" size="default">
        <p>这是默认尺寸的卡片。</p>
      </FuniCard>
      
      <FuniCard header="大尺寸卡片" size="large">
        <p>这是大尺寸的卡片。</p>
      </FuniCard>
    </div>
  </div>
</template>
```

## 交互式卡片示例

### 可点击和可选择的卡片
```vue
<template>
  <div class="interactive-examples">
    <div class="card-row">
      <!-- 可点击卡片 -->
      <FuniCard 
        header="可点击卡片"
        clickable
        hoverable
        @click="handleCardClick('card1')"
      >
        <p>点击这个卡片会触发点击事件。</p>
        <p>鼠标悬停时会有视觉反馈。</p>
      </FuniCard>
      
      <!-- 可选择卡片 -->
      <FuniCard 
        header="可选择卡片"
        selectable
        :selected="selectedCards.includes('card2')"
        @select="handleCardSelect('card2', $event)"
      >
        <p>这个卡片可以被选中。</p>
        <p>选中状态：{{ selectedCards.includes('card2') ? '已选中' : '未选中' }}</p>
      </FuniCard>
      
      <!-- 禁用卡片 -->
      <FuniCard 
        header="禁用卡片"
        disabled
      >
        <p>这个卡片处于禁用状态。</p>
        <p>无法进行交互操作。</p>
      </FuniCard>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const selectedCards = ref([])

const handleCardClick = (cardId) => {
  console.log('点击卡片:', cardId)
  ElMessage.info(`点击了${cardId}`)
}

const handleCardSelect = (cardId, selected) => {
  if (selected) {
    if (!selectedCards.value.includes(cardId)) {
      selectedCards.value.push(cardId)
    }
  } else {
    const index = selectedCards.value.indexOf(cardId)
    if (index > -1) {
      selectedCards.value.splice(index, 1)
    }
  }
  console.log('选择卡片:', cardId, selected)
  ElMessage.success(`卡片${selected ? '已选中' : '已取消选中'}`)
}
</script>
```

## 加载状态示例

### 加载中和骨架屏
```vue
<template>
  <div class="loading-examples">
    <div class="card-row">
      <!-- 加载中卡片 -->
      <FuniCard 
        header="加载中卡片"
        :loading="isLoading"
      >
        <p>卡片内容正在加载中...</p>
        <p>加载完成后会显示实际内容。</p>
        <el-button @click="toggleLoading">切换加载状态</el-button>
      </FuniCard>
      
      <!-- 骨架屏卡片 -->
      <FuniCard 
        header="骨架屏卡片"
        :skeleton="showSkeleton"
        :skeleton-rows="4"
      >
        <div v-if="!showSkeleton">
          <p>这是实际的内容。</p>
          <p>骨架屏用于优化加载体验。</p>
          <p>可以自定义骨架屏的行数。</p>
          <el-button @click="showSkeleton = true">显示骨架屏</el-button>
        </div>
      </FuniCard>
    </div>
    
    <div class="controls">
      <el-button @click="showSkeleton = false" v-if="showSkeleton">
        隐藏骨架屏
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const isLoading = ref(false)
const showSkeleton = ref(false)

const toggleLoading = () => {
  isLoading.value = !isLoading.value
}
</script>
```

## 带操作按钮的卡片示例

### 用户信息卡片
```vue
<template>
  <div class="action-examples">
    <div class="card-row">
      <!-- 用户信息卡片 -->
      <FuniCard 
        header="用户信息"
        :actions="userActions"
        @action="handleUserAction"
      >
        <div class="user-info">
          <div class="user-avatar">
            <el-avatar :size="60" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
          </div>
          <div class="user-details">
            <p><strong>姓名：</strong>张三</p>
            <p><strong>邮箱：</strong><EMAIL></p>
            <p><strong>部门：</strong>技术部</p>
            <p><strong>职位：</strong>前端工程师</p>
          </div>
        </div>
      </FuniCard>
      
      <!-- 项目信息卡片 -->
      <FuniCard 
        header="项目信息"
        :actions="projectActions"
        actions-position="top"
        @action="handleProjectAction"
      >
        <div class="project-info">
          <p><strong>项目名称：</strong>管理系统</p>
          <p><strong>状态：</strong><el-tag type="success">进行中</el-tag></p>
          <p><strong>进度：</strong>75%</p>
          <el-progress :percentage="75" />
          <p><strong>截止日期：</strong>2024-01-15</p>
        </div>
      </FuniCard>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { ElMessage } from 'element-plus'

const userActions = reactive([
  { key: 'edit', label: '编辑', icon: 'Edit', type: 'primary' },
  { key: 'message', label: '发消息', icon: 'ChatDotRound', type: 'success' },
  { key: 'delete', label: '删除', icon: 'Delete', type: 'danger' }
])

const projectActions = reactive([
  { key: 'start', label: '开始', icon: 'VideoPlay', type: 'success' },
  { key: 'pause', label: '暂停', icon: 'VideoPause', type: 'warning' },
  { key: 'stop', label: '停止', icon: 'VideoStop', type: 'danger' },
  { key: 'settings', label: '设置', icon: 'Setting' }
])

const handleUserAction = (action, index) => {
  console.log('用户操作:', action, index)
  ElMessage.info(`执行${action.label}操作`)
}

const handleProjectAction = (action, index) => {
  console.log('项目操作:', action, index)
  ElMessage.info(`执行${action.label}操作`)
}
</script>

<style scoped>
.user-info {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.user-details p {
  margin: 8px 0;
}

.project-info p {
  margin: 8px 0;
}
</style>
```

## 带封面的卡片示例

### 文章卡片
```vue
<template>
  <div class="cover-examples">
    <div class="card-row">
      <!-- 技术文章卡片 -->
      <FuniCard 
        cover="https://picsum.photos/400/200?random=1"
        cover-height="180px"
        :meta="articleMeta1"
        :actions="articleActions"
        @cover-click="handleCoverClick"
        @action="handleArticleAction"
      >
        <p>这是一篇关于Vue.js开发的技术文章，介绍了Vue 3的新特性和最佳实践，包括Composition API、响应式系统优化等内容...</p>
        <div class="article-stats">
          <span><el-icon><View /></el-icon> 1.2k</span>
          <span><el-icon><Star /></el-icon> 89</span>
          <span><el-icon><ChatDotRound /></el-icon> 23</span>
        </div>
      </FuniCard>
      
      <!-- 产品卡片 -->
      <FuniCard 
        cover="https://picsum.photos/400/200?random=2"
        cover-height="180px"
        :meta="productMeta"
        :actions="productActions"
        @cover-click="handleCoverClick"
        @action="handleProductAction"
      >
        <p>这是一款创新的智能设备，具有先进的AI功能和优雅的设计，为用户提供便捷的智能生活体验...</p>
        <div class="product-price">
          <span class="price">¥2,999</span>
          <span class="original-price">¥3,999</span>
        </div>
      </FuniCard>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { View, Star, ChatDotRound } from '@element-plus/icons-vue'

const articleMeta1 = reactive({
  title: 'Vue.js 3.0 新特性详解',
  author: '张三',
  date: '2023-12-01',
  tags: ['Vue.js', '前端', '技术']
})

const productMeta = reactive({
  title: '智能音响 Pro',
  category: '智能设备',
  date: '2023-12-01',
  tags: ['智能家居', 'AI', '音响']
})

const articleActions = reactive([
  { key: 'like', label: '点赞', icon: 'Star' },
  { key: 'share', label: '分享', icon: 'Share' },
  { key: 'comment', label: '评论', icon: 'ChatDotRound' }
])

const productActions = reactive([
  { key: 'cart', label: '加入购物车', icon: 'ShoppingCart', type: 'primary' },
  { key: 'favorite', label: '收藏', icon: 'Star' },
  { key: 'compare', label: '对比', icon: 'Scale' }
])

const handleCoverClick = (event) => {
  console.log('点击封面:', event)
  ElMessage.info('点击了封面图片')
}

const handleArticleAction = (action, index) => {
  console.log('文章操作:', action, index)
  ElMessage.success(`${action.label}成功`)
}

const handleProductAction = (action, index) => {
  console.log('产品操作:', action, index)
  ElMessage.success(`${action.label}成功`)
}
</script>

<style scoped>
.article-stats {
  display: flex;
  gap: 16px;
  margin-top: 12px;
  color: #666;
}

.article-stats span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.product-price {
  margin-top: 12px;
}

.price {
  font-size: 20px;
  font-weight: bold;
  color: #f56c6c;
}

.original-price {
  margin-left: 8px;
  text-decoration: line-through;
  color: #999;
}
</style>
```

## 可折叠卡片示例

### 设置面板
```vue
<template>
  <div class="collapsible-examples">
    <!-- 系统设置卡片 -->
    <FuniCard
      header="系统设置"
      collapsible
      :collapsed="collapsed1"
      @collapse="collapsed1 = $event"
    >
      <div class="settings-content">
        <el-form label-width="100px">
          <el-form-item label="主题色">
            <el-color-picker v-model="settings.themeColor" />
          </el-form-item>
          <el-form-item label="语言">
            <el-select v-model="settings.language">
              <el-option label="中文" value="zh" />
              <el-option label="English" value="en" />
            </el-select>
          </el-form-item>
          <el-form-item label="通知">
            <el-switch v-model="settings.notification" />
          </el-form-item>
        </el-form>
      </div>
    </FuniCard>

    <!-- 高级配置卡片 -->
    <FuniCard
      header="高级配置"
      collapsible
      :collapsed="collapsed2"
      collapse-icon="ArrowUp"
      @collapse="collapsed2 = $event"
    >
      <div class="advanced-content">
        <el-form label-width="120px">
          <el-form-item label="缓存大小">
            <el-input-number v-model="advanced.cacheSize" :min="1" :max="1000" />
          </el-form-item>
          <el-form-item label="自动保存">
            <el-switch v-model="advanced.autoSave" />
          </el-form-item>
          <el-form-item label="调试模式">
            <el-switch v-model="advanced.debugMode" />
          </el-form-item>
        </el-form>
      </div>
    </FuniCard>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const collapsed1 = ref(false)
const collapsed2 = ref(true)

const settings = reactive({
  themeColor: '#409eff',
  language: 'zh',
  notification: true
})

const advanced = reactive({
  cacheSize: 100,
  autoSave: true,
  debugMode: false
})
</script>
```

## 自定义插槽示例

### 复杂布局卡片
```vue
<template>
  <div class="slot-examples">
    <FuniCard>
      <!-- 自定义头部 -->
      <template #header>
        <div class="custom-header">
          <div class="header-left">
            <el-avatar :size="32" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
            <div class="header-info">
              <h4>用户动态</h4>
              <span class="header-time">2小时前</span>
            </div>
          </div>
          <div class="header-right">
            <el-dropdown>
              <el-icon><MoreFilled /></el-icon>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>编辑</el-dropdown-item>
                  <el-dropdown-item>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>

      <!-- 自定义内容 -->
      <div class="post-content">
        <p>今天学习了Vue 3的新特性，Composition API真的很强大！</p>
        <div class="post-images">
          <el-image
            v-for="img in postImages"
            :key="img"
            :src="img"
            fit="cover"
            style="width: 100px; height: 100px; margin-right: 8px;"
          />
        </div>
      </div>

      <!-- 自定义底部 -->
      <template #footer>
        <div class="post-actions">
          <el-button text @click="handleLike">
            <el-icon><Star /></el-icon>
            点赞 ({{ likeCount }})
          </el-button>
          <el-button text @click="handleComment">
            <el-icon><ChatDotRound /></el-icon>
            评论 ({{ commentCount }})
          </el-button>
          <el-button text @click="handleShare">
            <el-icon><Share /></el-icon>
            分享
          </el-button>
        </div>
      </template>
    </FuniCard>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { MoreFilled, Star, ChatDotRound, Share } from '@element-plus/icons-vue'

const likeCount = ref(12)
const commentCount = ref(5)
const postImages = ref([
  'https://picsum.photos/100/100?random=1',
  'https://picsum.photos/100/100?random=2',
  'https://picsum.photos/100/100?random=3'
])

const handleLike = () => {
  likeCount.value++
}

const handleComment = () => {
  console.log('打开评论')
}

const handleShare = () => {
  console.log('分享内容')
}
</script>

<style scoped>
.custom-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-info h4 {
  margin: 0;
  font-size: 16px;
}

.header-time {
  color: #999;
  font-size: 12px;
}

.post-content {
  margin: 16px 0;
}

.post-images {
  margin-top: 12px;
}

.post-actions {
  display: flex;
  gap: 16px;
  padding-top: 12px;
  border-top: 1px solid #ebeef5;
}
</style>
```

## 业务场景示例

### 数据统计卡片
```vue
<template>
  <div class="business-examples">
    <div class="stats-grid">
      <FuniCard
        v-for="stat in statsData"
        :key="stat.key"
        :header="stat.title"
        class="stat-card"
        hoverable
      >
        <div class="stat-content">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-change" :class="stat.trend">
            <el-icon><ArrowUp v-if="stat.trend === 'up'" /><ArrowDown v-else /></el-icon>
            {{ stat.change }}
          </div>
        </div>
        <div class="stat-description">{{ stat.description }}</div>
      </FuniCard>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'

const statsData = reactive([
  {
    key: 'users',
    title: '总用户数',
    value: '12,345',
    change: '+12.5%',
    trend: 'up',
    description: '较上月增长'
  },
  {
    key: 'orders',
    title: '订单数量',
    value: '8,901',
    change: '+8.2%',
    trend: 'up',
    description: '较上月增长'
  },
  {
    key: 'revenue',
    title: '营收金额',
    value: '¥234,567',
    change: '-2.1%',
    trend: 'down',
    description: '较上月下降'
  },
  {
    key: 'conversion',
    title: '转化率',
    value: '3.45%',
    change: '+0.8%',
    trend: 'up',
    description: '较上月提升'
  }
])
</script>

<style scoped>
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
}

.stat-change.up {
  color: #67c23a;
}

.stat-change.down {
  color: #f56c6c;
}

.stat-description {
  color: #909399;
  font-size: 12px;
}
</style>
```
