# FuniDialog 配置结构定义

## 基础配置结构

### FuniDialogProps 接口定义

```typescript
interface FuniDialogProps {
  // 基础配置
  modelValue?: boolean
  size?: 'small' | 'default' | 'large' | 'max'
  hideFooter?: boolean
  
  // ElementPlus el-dialog 透传属性
  title?: string
  width?: string | number
  height?: string | number
  fullscreen?: boolean
  top?: string
  modal?: boolean
  modalClass?: string
  appendToBody?: boolean
  lockScroll?: boolean
  customClass?: string
  openDelay?: number
  closeDelay?: number
  closeOnClickModal?: boolean
  closeOnPressEscape?: boolean
  showClose?: boolean
  beforeClose?: (done: () => void) => void
  draggable?: boolean
  overflow?: boolean
  center?: boolean
  alignCenter?: boolean
  destroyOnClose?: boolean
  closeIcon?: string | Component
  zIndex?: number
  headerStyle?: CSSProperties
  bodyStyle?: CSSProperties
  
  // 扩展功能配置
  resizable?: boolean
  maximizable?: boolean
  minimizable?: boolean
  confirmLoading?: boolean
  confirmDisabled?: boolean
  cancelText?: string
  confirmText?: string
}
```

### 尺寸配置枚举

```typescript
enum DialogSize {
  SMALL = 'small',    // 600px
  DEFAULT = 'default', // 800px
  LARGE = 'large',    // 1000px
  MAX = 'max'         // calc(100vw - 80px)
}

interface SizeConfig {
  width: string
  height?: string
  description: string
}

const SIZE_CONFIG: Record<DialogSize, SizeConfig> = {
  [DialogSize.SMALL]: {
    width: '600px',
    description: '小尺寸对话框，适用于简单确认、提示等场景'
  },
  [DialogSize.DEFAULT]: {
    width: '800px',
    description: '默认尺寸对话框，适用于表单编辑、详情查看等场景'
  },
  [DialogSize.LARGE]: {
    width: '1000px',
    description: '大尺寸对话框，适用于复杂表单、数据展示等场景'
  },
  [DialogSize.MAX]: {
    width: 'calc(100vw - 80px)',
    height: 'calc(100vh - 80px)',
    description: '最大尺寸对话框，适用于全屏展示、复杂业务等场景'
  }
}
```

### 事件配置接口

```typescript
interface FuniDialogEvents {
  // 基础事件
  'update:modelValue': (value: boolean) => void
  
  // ElementPlus 透传事件
  open: () => void
  opened: () => void
  close: () => void
  closed: () => void
  openAutoFocus: () => void
  closeAutoFocus: () => void
  
  // 扩展事件
  confirm: () => void
  cancel: () => void
  maximize?: () => void
  minimize?: () => void
  restore?: () => void
  resize?: (width: number, height: number) => void
  drag?: (x: number, y: number) => void
}
```

### 插槽配置接口

```typescript
interface FuniDialogSlots {
  // 默认插槽
  default?: () => VNode[]
  
  // ElementPlus 透传插槽
  header?: (props: { close: () => void, titleId: string, titleClass: string }) => VNode[]
  title?: () => VNode[]
  
  // 自定义插槽
  footer?: () => VNode[]
}
```

## 详细配置说明

### 基础配置项

#### modelValue
- **类型**: `boolean`
- **默认值**: `false`
- **说明**: 控制对话框的显示隐藏状态
- **示例**:
  ```javascript
  const dialogVisible = ref(false)
  
  // 显示对话框
  dialogVisible.value = true
  
  // 隐藏对话框
  dialogVisible.value = false
  ```

#### size
- **类型**: `'small' | 'default' | 'large' | 'max'`
- **默认值**: `'default'`
- **说明**: 对话框预设尺寸
- **示例**:
  ```javascript
  // 小尺寸 - 600px
  size: 'small'
  
  // 默认尺寸 - 800px
  size: 'default'
  
  // 大尺寸 - 1000px
  size: 'large'
  
  // 最大尺寸 - calc(100vw - 80px)
  size: 'max'
  ```

#### hideFooter
- **类型**: `boolean`
- **默认值**: `false`
- **说明**: 是否隐藏底部按钮区域
- **示例**:
  ```javascript
  // 显示底部按钮
  hideFooter: false
  
  // 隐藏底部按钮
  hideFooter: true
  ```

### ElementPlus 透传配置

#### title
- **类型**: `string`
- **说明**: 对话框标题
- **示例**:
  ```javascript
  title: '编辑用户信息'
  ```

#### width / height
- **类型**: `string | number`
- **说明**: 自定义对话框尺寸，会覆盖size配置
- **示例**:
  ```javascript
  // 字符串形式
  width: '600px'
  height: '400px'
  
  // 数字形式（像素）
  width: 600
  height: 400
  
  // 百分比
  width: '50%'
  height: '60%'
  ```

#### modal
- **类型**: `boolean`
- **默认值**: `true`
- **说明**: 是否显示遮罩层
- **示例**:
  ```javascript
  // 显示遮罩
  modal: true
  
  // 不显示遮罩
  modal: false
  ```

#### appendToBody
- **类型**: `boolean`
- **默认值**: `false`
- **说明**: 是否将对话框插入到body元素上
- **示例**:
  ```javascript
  // 插入到body（推荐用于嵌套对话框）
  appendToBody: true
  
  // 插入到父元素
  appendToBody: false
  ```

#### closeOnClickModal
- **类型**: `boolean`
- **默认值**: `false`
- **说明**: 是否可以通过点击遮罩关闭对话框
- **示例**:
  ```javascript
  // 点击遮罩可关闭
  closeOnClickModal: true
  
  // 点击遮罩不关闭
  closeOnClickModal: false
  ```

#### beforeClose
- **类型**: `(done: () => void) => void`
- **说明**: 关闭前的回调函数
- **示例**:
  ```javascript
  const beforeClose = (done) => {
    // 确认是否关闭
    ElMessageBox.confirm('确定要关闭吗？')
      .then(() => {
        done() // 确认关闭
      })
      .catch(() => {
        // 取消关闭
      })
  }
  ```

### 扩展功能配置

#### confirmLoading
- **类型**: `boolean`
- **默认值**: `false`
- **说明**: 确认按钮加载状态
- **示例**:
  ```javascript
  const confirmLoading = ref(false)
  
  const handleConfirm = async () => {
    confirmLoading.value = true
    try {
      await submitData()
    } finally {
      confirmLoading.value = false
    }
  }
  ```

#### confirmDisabled
- **类型**: `boolean`
- **默认值**: `false`
- **说明**: 确认按钮禁用状态
- **示例**:
  ```javascript
  // 表单验证通过才能确认
  const confirmDisabled = computed(() => !isFormValid.value)
  ```

## 常用配置组合示例

### 基础信息对话框
```javascript
const basicConfig = {
  modelValue: false,
  size: 'default',
  title: '基础信息',
  hideFooter: false,
  closeOnClickModal: true,
  closeOnPressEscape: true
}
```

### 确认对话框
```javascript
const confirmConfig = {
  modelValue: false,
  size: 'small',
  title: '确认操作',
  hideFooter: false,
  closeOnClickModal: false,
  closeOnPressEscape: false,
  confirmText: '确认',
  cancelText: '取消'
}
```

### 表单编辑对话框
```javascript
const formConfig = {
  modelValue: false,
  size: 'large',
  title: '编辑信息',
  hideFooter: false,
  destroyOnClose: true,
  closeOnClickModal: false,
  confirmLoading: false,
  confirmDisabled: false
}
```

### 全屏展示对话框
```javascript
const fullscreenConfig = {
  modelValue: false,
  size: 'max',
  title: '详细信息',
  hideFooter: true,
  appendToBody: true,
  destroyOnClose: true
}
```

### 嵌套对话框配置
```javascript
// 外层对话框
const outerConfig = {
  modelValue: false,
  size: 'large',
  title: '外层对话框',
  appendToBody: true,
  zIndex: 2000
}

// 内层对话框
const innerConfig = {
  modelValue: false,
  size: 'default',
  title: '内层对话框',
  appendToBody: true,
  zIndex: 3000
}
```

### 自定义样式对话框
```javascript
const customConfig = {
  modelValue: false,
  size: 'default',
  title: '自定义对话框',
  customClass: 'custom-dialog',
  headerStyle: {
    backgroundColor: '#f5f7fa',
    borderBottom: '1px solid #e4e7ed'
  },
  bodyStyle: {
    padding: '20px'
  }
}
```

## 最佳实践建议

### 1. 尺寸选择建议
- **small**: 简单确认、提示信息
- **default**: 表单编辑、详情查看
- **large**: 复杂表单、数据展示
- **max**: 全屏展示、复杂业务

### 2. 层级管理建议
- 嵌套对话框使用不同的zIndex
- 内层对话框设置appendToBody为true
- 合理控制对话框的嵌套层数

### 3. 用户体验建议
- 提供明确的操作反馈
- 合理设置关闭条件
- 支持键盘操作
- 考虑移动端适配

### 4. 性能优化建议
- 使用destroyOnClose销毁不常用的对话框
- 避免在对话框中放置过多复杂组件
- 合理使用懒加载

### 5. 安全性建议
- 重要操作使用beforeClose确认
- 敏感信息对话框禁用点击遮罩关闭
- 合理设置权限控制
