# FuniDialog API文档

## 组件概述

FuniDialog是基于ElementPlus的el-dialog封装的对话框组件，增加了拖拽、全屏、最大化、最小化等功能，提供了更好的用户体验，适用于各种弹窗场景。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 | ElementPlus对应 |
|--------|------|--------|------|------|----------------|
| modelValue | Boolean | false | - | 是否显示对话框 | el-dialog.model-value |
| title | String | '' | - | 对话框标题 | el-dialog.title |
| width | String/Number | '50%' | - | 对话框宽度 | el-dialog.width |
| height | String/Number | 'auto' | - | 对话框高度 | - |
| fullscreen | Boolean | false | - | 是否全屏显示 | el-dialog.fullscreen |
| top | String | '15vh' | - | 对话框距离顶部的距离 | el-dialog.top |
| modal | Boolean | true | - | 是否需要遮罩层 | el-dialog.modal |
| modalClass | String | '' | - | 遮罩层的自定义类名 | el-dialog.modal-class |
| appendToBody | Boolean | false | - | 是否插入至body元素上 | el-dialog.append-to-body |
| lockScroll | Boolean | true | - | 是否在对话框出现时将body滚动锁定 | el-dialog.lock-scroll |
| customClass | String | '' | - | 对话框的自定义类名 | el-dialog.custom-class |
| openDelay | Number | 0 | - | 打开的延时时间，单位毫秒 | el-dialog.open-delay |
| closeDelay | Number | 0 | - | 关闭的延时时间，单位毫秒 | el-dialog.close-delay |
| closeOnClickModal | Boolean | true | - | 是否可以通过点击modal关闭对话框 | el-dialog.close-on-click-modal |
| closeOnPressEscape | Boolean | true | - | 是否可以通过按下ESC关闭对话框 | el-dialog.close-on-press-escape |
| showClose | Boolean | true | - | 是否显示关闭按钮 | el-dialog.show-close |
| beforeClose | Function | - | - | 关闭前的回调 | el-dialog.before-close |
| draggable | Boolean | false | - | 是否可拖拽 | el-dialog.draggable |
| overflow | Boolean | false | - | 拖拽时是否可以超出可视区域 | el-dialog.overflow |
| center | Boolean | false | - | 是否居中布局 | el-dialog.center |
| alignCenter | Boolean | false | - | 是否水平垂直对齐至屏幕中心 | el-dialog.align-center |
| destroyOnClose | Boolean | false | - | 关闭时销毁子元素 | el-dialog.destroy-on-close |
| closeIcon | String | 'Close' | - | 自定义关闭图标 | el-dialog.close-icon |
| zIndex | Number | - | - | 设置z-index | el-dialog.z-index |
| headerStyle | Object/String | {} | - | 头部样式 | el-dialog.header-style |
| bodyStyle | Object/String | {} | - | 内容区域样式 | el-dialog.body-style |
| resizable | Boolean | false | - | 是否可调整大小 | - |
| maximizable | Boolean | false | - | 是否显示最大化按钮 | - |
| minimizable | Boolean | false | - | 是否显示最小化按钮 | - |
| showFooter | Boolean | true | - | 是否显示底部 | - |
| confirmText | String | '确定' | - | 确认按钮文本 | - |
| cancelText | String | '取消' | - | 取消按钮文本 | - |
| confirmLoading | Boolean | false | - | 确认按钮加载状态 | - |
| confirmDisabled | Boolean | false | - | 确认按钮禁用状态 | - |
| hideCancel | Boolean | false | - | 是否隐藏取消按钮 | - |
| hideConfirm | Boolean | false | - | 是否隐藏确认按钮 | - |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| update:modelValue | value: Boolean | 显示状态更新事件 | 对话框显示/隐藏时 |
| open | - | 打开事件 | 对话框打开时 |
| opened | - | 打开动画结束事件 | 对话框打开动画结束时 |
| close | - | 关闭事件 | 对话框关闭时 |
| closed | - | 关闭动画结束事件 | 对话框关闭动画结束时 |
| openAutoFocus | - | 输入焦点聚焦在对话框内容时的事件 | 对话框打开且焦点聚焦时 |
| closeAutoFocus | - | 输入焦点从对话框内容失焦时的事件 | 对话框关闭且焦点失焦时 |
| confirm | - | 确认事件 | 点击确认按钮时 |
| cancel | - | 取消事件 | 点击取消按钮时 |
| maximize | - | 最大化事件 | 点击最大化按钮时 |
| minimize | - | 最小化事件 | 点击最小化按钮时 |
| restore | - | 还原事件 | 从最大化/最小化状态还原时 |
| resize | (width: Number, height: Number) | 调整大小事件 | 调整对话框大小时 |
| drag | (x: Number, y: Number) | 拖拽事件 | 拖拽对话框时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| open | - | void | 打开对话框 |
| close | - | void | 关闭对话框 |
| maximize | - | void | 最大化对话框 |
| minimize | - | void | 最小化对话框 |
| restore | - | void | 还原对话框 |
| center | - | void | 居中对话框 |

## Slots

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| default | - | 对话框内容 |
| header | { close, titleId, titleClass } | 对话框头部内容 |
| title | - | 对话框标题内容 |
| footer | - | 对话框底部内容 |

## 使用示例

### 基础对话框
```vue
<template>
  <div>
    <el-button @click="dialogVisible = true">打开对话框</el-button>
    
    <FuniDialog
      v-model="dialogVisible"
      title="基础对话框"
      width="600px"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    >
      <p>这是一个基础的对话框内容。</p>
      <p>可以在这里放置任何内容。</p>
    </FuniDialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const dialogVisible = ref(false)

const handleConfirm = () => {
  console.log('确认操作')
  dialogVisible.value = false
}

const handleCancel = () => {
  console.log('取消操作')
  dialogVisible.value = false
}
</script>
```

### 可拖拽对话框
```vue
<template>
  <div>
    <el-button @click="draggableDialogVisible = true">打开可拖拽对话框</el-button>
    
    <FuniDialog
      v-model="draggableDialogVisible"
      title="可拖拽对话框"
      width="500px"
      height="400px"
      draggable
      resizable
      @drag="handleDrag"
      @resize="handleResize"
    >
      <div style="height: 300px; padding: 20px;">
        <p>这个对话框可以拖拽和调整大小。</p>
        <p>拖拽标题栏可以移动对话框。</p>
        <p>拖拽右下角可以调整大小。</p>
      </div>
    </FuniDialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const draggableDialogVisible = ref(false)

const handleDrag = (x, y) => {
  console.log('对话框位置:', x, y)
}

const handleResize = (width, height) => {
  console.log('对话框大小:', width, height)
}
</script>
```

### 带最大化最小化的对话框
```vue
<template>
  <div>
    <el-button @click="maxDialogVisible = true">打开功能完整对话框</el-button>
    
    <FuniDialog
      v-model="maxDialogVisible"
      title="功能完整对话框"
      width="800px"
      height="600px"
      draggable
      resizable
      maximizable
      minimizable
      @maximize="handleMaximize"
      @minimize="handleMinimize"
      @restore="handleRestore"
    >
      <div style="height: 500px; padding: 20px;">
        <h3>功能说明</h3>
        <ul>
          <li>可以拖拽移动</li>
          <li>可以调整大小</li>
          <li>可以最大化</li>
          <li>可以最小化</li>
          <li>支持键盘ESC关闭</li>
        </ul>
        
        <el-form :model="form" label-width="100px">
          <el-form-item label="用户名">
            <el-input v-model="form.username" />
          </el-form-item>
          <el-form-item label="邮箱">
            <el-input v-model="form.email" />
          </el-form-item>
          <el-form-item label="描述">
            <el-input v-model="form.description" type="textarea" rows="4" />
          </el-form-item>
        </el-form>
      </div>
    </FuniDialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const maxDialogVisible = ref(false)

const form = reactive({
  username: '',
  email: '',
  description: ''
})

const handleMaximize = () => {
  console.log('对话框已最大化')
}

const handleMinimize = () => {
  console.log('对话框已最小化')
}

const handleRestore = () => {
  console.log('对话框已还原')
}
</script>
```

### 自定义头部和底部
```vue
<template>
  <div>
    <el-button @click="customDialogVisible = true">打开自定义对话框</el-button>
    
    <FuniDialog
      v-model="customDialogVisible"
      width="600px"
      :show-footer="false"
    >
      <template #header>
        <div class="custom-header">
          <el-icon class="header-icon"><InfoFilled /></el-icon>
          <span class="header-title">自定义标题</span>
          <el-tag type="success" size="small">重要</el-tag>
        </div>
      </template>
      
      <div class="custom-content">
        <el-alert
          title="提示信息"
          type="info"
          description="这是一个带有自定义头部和底部的对话框示例。"
          show-icon
          :closable="false"
        />
        
        <div style="margin-top: 20px;">
          <p>自定义内容区域</p>
          <el-progress :percentage="progressValue" />
        </div>
      </div>
      
      <template #footer>
        <div class="custom-footer">
          <div class="footer-left">
            <el-checkbox v-model="agreeTerms">我已阅读并同意相关条款</el-checkbox>
          </div>
          <div class="footer-right">
            <el-button @click="customDialogVisible = false">取消</el-button>
            <el-button 
              type="primary" 
              :disabled="!agreeTerms"
              @click="handleCustomConfirm"
            >
              确认
            </el-button>
          </div>
        </div>
      </template>
    </FuniDialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { InfoFilled } from '@element-plus/icons-vue'

const customDialogVisible = ref(false)
const agreeTerms = ref(false)
const progressValue = ref(75)

const handleCustomConfirm = () => {
  console.log('自定义确认操作')
  customDialogVisible.value = false
}
</script>

<style scoped>
.custom-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  color: #409eff;
}

.header-title {
  font-size: 16px;
  font-weight: 500;
  flex: 1;
}

.custom-content {
  padding: 20px;
}

.custom-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-top: 1px solid #ebeef5;
}

.footer-right {
  display: flex;
  gap: 8px;
}
</style>
```

### 嵌套对话框
```vue
<template>
  <div>
    <el-button @click="outerDialogVisible = true">打开嵌套对话框</el-button>
    
    <!-- 外层对话框 -->
    <FuniDialog
      v-model="outerDialogVisible"
      title="外层对话框"
      width="800px"
      height="600px"
      append-to-body
    >
      <div style="padding: 20px;">
        <p>这是外层对话框的内容。</p>
        <el-button @click="innerDialogVisible = true">打开内层对话框</el-button>
        
        <el-table :data="tableData" style="margin-top: 20px;">
          <el-table-column prop="name" label="姓名" />
          <el-table-column prop="age" label="年龄" />
          <el-table-column prop="address" label="地址" />
        </el-table>
      </div>
    </FuniDialog>
    
    <!-- 内层对话框 -->
    <FuniDialog
      v-model="innerDialogVisible"
      title="内层对话框"
      width="500px"
      append-to-body
      :z-index="3000"
    >
      <div style="padding: 20px;">
        <p>这是内层对话框的内容。</p>
        <el-form :model="innerForm" label-width="80px">
          <el-form-item label="名称">
            <el-input v-model="innerForm.name" />
          </el-form-item>
          <el-form-item label="描述">
            <el-input v-model="innerForm.description" type="textarea" />
          </el-form-item>
        </el-form>
      </div>
    </FuniDialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const outerDialogVisible = ref(false)
const innerDialogVisible = ref(false)

const tableData = ref([
  { name: '张三', age: 25, address: '北京市朝阳区' },
  { name: '李四', age: 30, address: '上海市浦东新区' },
  { name: '王五', age: 28, address: '广州市天河区' }
])

const innerForm = reactive({
  name: '',
  description: ''
})
</script>
```

### 确认对话框
```vue
<template>
  <div>
    <el-button @click="showConfirmDialog" type="danger">删除数据</el-button>
    
    <FuniDialog
      v-model="confirmDialogVisible"
      title="确认删除"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :confirm-loading="deleteLoading"
      @confirm="handleDelete"
      @cancel="confirmDialogVisible = false"
    >
      <div style="text-align: center; padding: 20px;">
        <el-icon size="48" color="#f56c6c" style="margin-bottom: 16px;">
          <WarningFilled />
        </el-icon>
        <p style="font-size: 16px; margin-bottom: 8px;">确认删除选中的数据吗？</p>
        <p style="color: #999; font-size: 14px;">删除后数据将无法恢复，请谨慎操作。</p>
      </div>
    </FuniDialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { WarningFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const confirmDialogVisible = ref(false)
const deleteLoading = ref(false)

const showConfirmDialog = () => {
  confirmDialogVisible.value = true
}

const handleDelete = async () => {
  deleteLoading.value = true
  
  try {
    // 模拟删除操作
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('删除成功')
    confirmDialogVisible.value = false
  } catch (error) {
    ElMessage.error('删除失败')
  } finally {
    deleteLoading.value = false
  }
}
</script>
```

### 表单对话框
```vue
<template>
  <div>
    <el-button @click="formDialogVisible = true">打开表单对话框</el-button>
    
    <FuniDialog
      v-model="formDialogVisible"
      title="编辑用户信息"
      width="600px"
      :confirm-loading="submitLoading"
      :confirm-disabled="!isFormValid"
      @confirm="handleSubmit"
      @cancel="handleCancel"
      @closed="resetForm"
    >
      <el-form
        ref="formRef"
        :model="userForm"
        :rules="formRules"
        label-width="100px"
        style="padding: 20px;"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" />
        </el-form-item>
        
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="userForm.phone" />
        </el-form-item>
        
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色">
            <el-option label="管理员" value="admin" />
            <el-option label="普通用户" value="user" />
            <el-option label="访客" value="guest" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="userForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="备注" prop="remark">
          <el-input 
            v-model="userForm.remark" 
            type="textarea" 
            rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
    </FuniDialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

const formDialogVisible = ref(false)
const submitLoading = ref(false)
const formRef = ref()

const userForm = reactive({
  username: '',
  email: '',
  phone: '',
  role: '',
  status: 1,
  remark: ''
})

const formRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在3到20个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
}

const isFormValid = computed(() => {
  return userForm.username && userForm.email && userForm.phone && userForm.role
})

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    // 模拟提交操作
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('保存成功')
    formDialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const handleCancel = () => {
  formDialogVisible.value = false
}

const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(userForm, {
    username: '',
    email: '',
    phone: '',
    role: '',
    status: 1,
    remark: ''
  })
}
</script>
```

## ElementPlus API支持

FuniDialog基于el-dialog封装，支持所有el-dialog的API：

```vue
<template>
  <FuniDialog
    v-model="visible"
    
    <!-- ElementPlus el-dialog 所有属性 -->
    title="对话框标题"
    width="50%"
    :fullscreen="false"
    top="15vh"
    :modal="true"
    modal-class=""
    :append-to-body="false"
    :lock-scroll="true"
    custom-class=""
    :open-delay="0"
    :close-delay="0"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    :show-close="true"
    :before-close="beforeCloseHandler"
    :draggable="false"
    :overflow="false"
    :center="false"
    :align-center="false"
    :destroy-on-close="false"
    close-icon="Close"
    :z-index="2000"
    :header-style="{}"
    :body-style="{}"
    
    <!-- ElementPlus el-dialog 所有事件 -->
    @open="handleOpen"
    @opened="handleOpened"
    @close="handleClose"
    @closed="handleClosed"
    @open-auto-focus="handleOpenAutoFocus"
    @close-auto-focus="handleCloseAutoFocus"
  />
</template>
```

## 注意事项

### 1. 层级管理
- 嵌套对话框需要设置不同的z-index
- 使用append-to-body避免层级问题
- 合理控制对话框的数量

### 2. 性能优化
- 使用destroy-on-close销毁不常用的对话框
- 避免在对话框中放置过多复杂组件
- 合理使用懒加载

### 3. 用户体验
- 提供明确的操作反馈
- 合理设置对话框大小
- 支持键盘操作

### 4. 移动端适配
- 小屏幕设备考虑全屏显示
- 触摸设备的拖拽体验
- 合适的按钮大小和间距

## 常见问题

### Q: 如何实现对话框的拖拽功能？
A: 设置draggable属性为true，可以拖拽标题栏移动对话框

### Q: 如何自定义对话框的头部和底部？
A: 使用header和footer插槽自定义内容

### Q: 如何处理对话框的嵌套问题？
A: 设置append-to-body为true，并为内层对话框设置更高的z-index

### Q: 如何实现对话框的最大化最小化？
A: 设置maximizable和minimizable属性为true，组件会自动添加对应按钮
