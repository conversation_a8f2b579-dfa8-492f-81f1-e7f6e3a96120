# FuniDialog ElementPlus API 支持

## 基础组件说明

FuniDialog 基于 ElementPlus 的 `el-dialog` 组件封装，在保持原有功能的基础上，增加了预设尺寸、底部按钮区域等业务功能。

### 核心组件构成
- **el-dialog**: 基础对话框组件，提供核心的弹窗功能
- **el-button**: 底部操作按钮
- **自定义布局**: 头部、内容、底部的结构化布局

## 支持的 ElementPlus API

### el-dialog 相关 API

#### Props 透传支持
| ElementPlus API | 支持状态 | 透传方式 | 说明 |
|----------------|----------|----------|------|
| model-value | ✅ | 内部处理 | 对话框显示状态 |
| title | ✅ | 直接透传 | 对话框标题 |
| width | ✅ | 条件透传 | 宽度，会被size覆盖 |
| height | ✅ | 直接透传 | 高度 |
| fullscreen | ✅ | 直接透传 | 是否全屏 |
| top | ✅ | 直接透传 | 距离顶部距离 |
| modal | ✅ | 直接透传 | 是否显示遮罩 |
| modal-class | ✅ | 直接透传 | 遮罩的自定义类名 |
| append-to-body | ✅ | 直接透传 | 是否插入到body |
| lock-scroll | ✅ | 直接透传 | 是否锁定滚动 |
| custom-class | ✅ | 直接透传 | 自定义类名 |
| open-delay | ✅ | 直接透传 | 打开延时 |
| close-delay | ✅ | 直接透传 | 关闭延时 |
| close-on-click-modal | ✅ | 直接透传 | 点击遮罩是否关闭 |
| close-on-press-escape | ✅ | 直接透传 | 按ESC是否关闭 |
| show-close | ✅ | 直接透传 | 是否显示关闭按钮 |
| before-close | ✅ | 直接透传 | 关闭前回调 |
| draggable | ✅ | 直接透传 | 是否可拖拽 |
| overflow | ✅ | 直接透传 | 拖拽时是否可以超出视口 |
| center | ✅ | 直接透传 | 是否居中布局 |
| align-center | ✅ | 直接透传 | 是否水平垂直居中 |
| destroy-on-close | ✅ | 直接透传 | 关闭时是否销毁 |
| close-icon | ✅ | 直接透传 | 自定义关闭图标 |
| z-index | ✅ | 直接透传 | 层级 |
| header-style | ✅ | 直接透传 | 头部样式 |
| body-style | ✅ | 直接透传 | 内容区域样式 |

#### Events 透传支持
| ElementPlus Event | 支持状态 | 透传方式 | 说明 |
|------------------|----------|----------|------|
| open | ✅ | 直接透传 | 打开事件 |
| opened | ✅ | 直接透传 | 打开动画结束事件 |
| close | ✅ | 直接透传 | 关闭事件 |
| closed | ✅ | 直接透传 | 关闭动画结束事件 |
| open-auto-focus | ✅ | 直接透传 | 打开时自动聚焦事件 |
| close-auto-focus | ✅ | 直接透传 | 关闭时自动聚焦事件 |

#### Slots 透传支持
| ElementPlus Slot | 支持状态 | 透传方式 | 说明 |
|-----------------|----------|----------|------|
| default | ✅ | 直接透传 | 默认内容插槽 |
| header | ✅ | 直接透传 | 头部插槽 |
| title | ✅ | 直接透传 | 标题插槽 |

#### Methods 透传支持
| ElementPlus Method | 支持状态 | 透传方式 | 说明 |
|-------------------|----------|----------|------|
| handleClose | ✅ | 内部处理 | 关闭对话框 |

### el-button 相关 API

#### Props 透传支持
| ElementPlus API | 支持状态 | 透传方式 | 说明 |
|----------------|----------|----------|------|
| size | ✅ | 直接透传 | 按钮尺寸 |
| type | ✅ | 直接透传 | 按钮类型 |
| plain | ✅ | 直接透传 | 是否朴素按钮 |
| round | ✅ | 直接透传 | 是否圆角按钮 |
| circle | ✅ | 直接透传 | 是否圆形按钮 |
| loading | ✅ | 内部处理 | 加载状态 |
| disabled | ✅ | 内部处理 | 禁用状态 |
| icon | ✅ | 直接透传 | 图标 |
| autofocus | ✅ | 直接透传 | 自动聚焦 |
| native-type | ✅ | 直接透传 | 原生type属性 |

#### Events 透传支持
| ElementPlus Event | 支持状态 | 透传方式 | 说明 |
|------------------|----------|----------|------|
| click | ✅ | 内部处理 | 按钮点击事件 |

## 透传方式说明

### 1. 直接透传
组件属性直接传递给对应的 ElementPlus 组件，无需额外处理。

```vue
<template>
  <FuniDialog
    v-model="visible"
    title="标题"
    :modal="true"
    :append-to-body="true"
    :close-on-click-modal="false"
    draggable
  />
</template>
```

### 2. 内部处理
组件内部处理后再传递给 ElementPlus 组件。

```vue
<template>
  <FuniDialog
    v-model="visible"
    :confirm-loading="loading"
    :confirm-disabled="disabled"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  />
</template>

<script setup>
// confirm-loading 会被处理为确认按钮的 loading 属性
// confirm-disabled 会被处理为确认按钮的 disabled 属性
// confirm/cancel 事件会被内部处理
</script>
```

### 3. 条件透传
根据组件配置决定是否透传某些属性。

```javascript
// 组件内部逻辑
const dialogWidth = computed(() => {
  // 如果设置了 size，使用预设宽度
  if (props.size) {
    return SIZE_CONFIG[props.size].width
  }
  // 否则使用自定义 width
  return props.width
})
```

## 使用示例

### 基础透传示例
```vue
<template>
  <div class="example-container">
    <h3>ElementPlus API 透传示例</h3>
    
    <!-- 基础配置透传 -->
    <FuniDialog
      v-model="visible1"
      title="基础对话框"
      :width="600"
      :modal="true"
      :close-on-click-modal="false"
      @open="handleOpen"
      @close="handleClose"
    >
      <p>基础内容</p>
    </FuniDialog>
    
    <!-- 高级配置透传 -->
    <FuniDialog
      v-model="visible2"
      title="高级对话框"
      :append-to-body="true"
      :destroy-on-close="true"
      :before-close="beforeClose"
      draggable
      center
    >
      <p>高级配置内容</p>
    </FuniDialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const visible1 = ref(false)
const visible2 = ref(false)

const handleOpen = () => {
  console.log('对话框打开')
}

const handleClose = () => {
  console.log('对话框关闭')
}

const beforeClose = (done) => {
  // 确认关闭逻辑
  done()
}
</script>
```

### 自定义插槽示例
```vue
<template>
  <div class="example-container">
    <h3>自定义插槽示例</h3>
    
    <FuniDialog
      v-model="visible"
      size="default"
    >
      <!-- 自定义头部 -->
      <template #header="{ close, titleId, titleClass }">
        <div class="custom-header">
          <h4 :id="titleId" :class="titleClass">自定义标题</h4>
          <el-button type="text" @click="close">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </template>
      
      <!-- 默认内容 -->
      <div class="dialog-content">
        <p>这是自定义头部的对话框内容。</p>
      </div>
      
      <!-- 自定义底部 -->
      <template #footer>
        <div class="custom-footer">
          <el-button @click="visible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确认</el-button>
        </div>
      </template>
    </FuniDialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Close } from '@element-plus/icons-vue'

const visible = ref(false)

const handleConfirm = () => {
  visible.value = false
}
</script>

<style scoped>
.custom-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
}

.custom-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 16px 20px;
}
</style>
```

### 事件处理示例
```vue
<template>
  <div class="example-container">
    <h3>事件处理示例</h3>
    
    <el-button @click="openDialog">打开对话框</el-button>
    
    <FuniDialog
      v-model="visible"
      title="事件处理示例"
      size="default"
      @open="handleOpen"
      @opened="handleOpened"
      @close="handleClose"
      @closed="handleClosed"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    >
      <div class="event-content">
        <h4>事件日志：</h4>
        <div class="event-log">
          <div v-for="log in eventLogs" :key="log.id" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-event">{{ log.event }}</span>
          </div>
        </div>
      </div>
    </FuniDialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const visible = ref(false)
const eventLogs = ref([])

const addLog = (event) => {
  eventLogs.value.unshift({
    id: Date.now(),
    time: new Date().toLocaleTimeString(),
    event
  })
  
  // 保持日志数量
  if (eventLogs.value.length > 10) {
    eventLogs.value = eventLogs.value.slice(0, 10)
  }
}

const openDialog = () => {
  visible.value = true
}

const handleOpen = () => {
  addLog('对话框开始打开')
}

const handleOpened = () => {
  addLog('对话框打开完成')
}

const handleClose = () => {
  addLog('对话框开始关闭')
}

const handleClosed = () => {
  addLog('对话框关闭完成')
}

const handleConfirm = () => {
  addLog('点击确认按钮')
  visible.value = false
}

const handleCancel = () => {
  addLog('点击取消按钮')
  visible.value = false
}
</script>

<style scoped>
.event-content {
  padding: 20px;
}

.event-log {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
}

.log-item {
  display: flex;
  gap: 10px;
  padding: 2px 0;
  font-size: 12px;
  font-family: monospace;
}

.log-time {
  color: #909399;
  width: 80px;
}

.log-event {
  color: #409eff;
}
</style>
```

## 注意事项

### 1. API 兼容性
- 确保使用的 ElementPlus 版本支持相应的 API
- 建议使用 ElementPlus 2.0+ 版本以获得最佳兼容性
- 某些新增的 API 可能在旧版本中不可用

### 2. 尺寸配置优先级
- `size` 属性会覆盖 `width` 属性
- 如需自定义宽度，请不要设置 `size` 属性
- `height` 属性不受 `size` 影响

### 3. 事件处理
- 组件内部会处理 `confirm` 和 `cancel` 事件
- ElementPlus 原生事件会直接透传
- 建议使用组件提供的事件而非直接监听 ElementPlus 事件

### 4. 插槽使用
- `footer` 插槽会覆盖默认的底部按钮
- `header` 插槽会覆盖默认的头部布局
- 使用自定义插槽时需要自行处理相关逻辑

### 5. 样式定制
- 可以通过 `custom-class` 添加自定义样式类
- 使用 `header-style` 和 `body-style` 定制局部样式
- 注意样式优先级，避免样式冲突

## 版本兼容性

| FuniDialog 版本 | ElementPlus 版本 | 兼容性 |
|----------------|-----------------|--------|
| 1.0.x | 2.0.x | ✅ 完全兼容 |
| 1.0.x | 2.1.x | ✅ 完全兼容 |
| 1.0.x | 2.2.x | ✅ 完全兼容 |
| 1.0.x | 2.3.x | ✅ 完全兼容 |
| 1.0.x | 1.x.x | ⚠️ 部分兼容 |
