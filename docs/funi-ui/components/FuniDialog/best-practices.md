# FuniDialog 最佳实践

## 推荐用法和配置

### 1. 尺寸选择最佳实践

#### 根据内容复杂度选择尺寸
```vue
<template>
  <!-- ✅ 推荐：简单确认对话框使用小尺寸 -->
  <FuniDialog
    v-model="confirmVisible"
    title="确认删除"
    size="small"
    :close-on-click-modal="false"
    @confirm="handleDelete"
  >
    <div class="confirm-content">
      <p>确定要删除这条记录吗？</p>
      <p class="warning-text">删除后无法恢复</p>
    </div>
  </FuniDialog>
  
  <!-- ✅ 推荐：表单编辑使用默认或大尺寸 -->
  <FuniDialog
    v-model="formVisible"
    title="编辑用户信息"
    size="large"
    :confirm-loading="submitLoading"
    @confirm="handleSubmit"
  >
    <UserForm v-model="userForm" />
  </FuniDialog>
  
  <!-- ✅ 推荐：复杂业务使用最大尺寸 -->
  <FuniDialog
    v-model="complexVisible"
    title="数据分析"
    size="max"
    :hide-footer="true"
    :destroy-on-close="true"
  >
    <DataAnalysisPanel />
  </FuniDialog>
  
  <!-- ❌ 不推荐：尺寸与内容不匹配 -->
  <FuniDialog
    v-model="badVisible"
    title="复杂表单"
    size="small"
  >
    <ComplexForm />
  </FuniDialog>
</template>

<script setup>
import { ref } from 'vue'

const confirmVisible = ref(false)
const formVisible = ref(false)
const complexVisible = ref(false)
const badVisible = ref(false)
const submitLoading = ref(false)

// ✅ 推荐：根据业务场景选择合适的尺寸
const openDialog = (type) => {
  switch (type) {
    case 'confirm':
      confirmVisible.value = true
      break
    case 'form':
      formVisible.value = true
      break
    case 'complex':
      complexVisible.value = true
      break
  }
}
</script>

<style scoped>
.confirm-content {
  text-align: center;
  padding: 20px;
}

.warning-text {
  color: #f56c6c;
  font-size: 14px;
  margin-top: 10px;
}
</style>
```

#### 响应式尺寸配置
```vue
<template>
  <!-- ✅ 推荐：响应式尺寸配置 -->
  <FuniDialog
    v-model="responsiveVisible"
    title="响应式对话框"
    :size="dialogSize"
    :width="customWidth"
  >
    <div class="responsive-content">
      <p>当前屏幕尺寸：{{ screenSize }}</p>
      <p>对话框尺寸：{{ dialogSize }}</p>
    </div>
  </FuniDialog>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const responsiveVisible = ref(false)
const screenWidth = ref(window.innerWidth)

// ✅ 推荐：根据屏幕尺寸动态调整对话框大小
const dialogSize = computed(() => {
  if (screenWidth.value < 768) return 'small'
  if (screenWidth.value < 1200) return 'default'
  return 'large'
})

const customWidth = computed(() => {
  if (screenWidth.value < 768) return '90%'
  return undefined // 使用预设尺寸
})

const screenSize = computed(() => {
  if (screenWidth.value < 768) return '小屏幕'
  if (screenWidth.value < 1200) return '中等屏幕'
  return '大屏幕'
})

const handleResize = () => {
  screenWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>
```

### 2. 用户体验最佳实践

#### 加载状态和禁用控制
```vue
<template>
  <div class="ux-examples">
    <!-- ✅ 推荐：提供清晰的加载状态 -->
    <FuniDialog
      v-model="loadingVisible"
      title="数据处理"
      size="default"
      :confirm-loading="processing"
      :confirm-disabled="!canSubmit"
      :close-on-click-modal="!processing"
      :close-on-press-escape="!processing"
      @confirm="handleProcess"
    >
      <div class="process-content">
        <div v-if="processing" class="loading-section">
          <el-progress 
            :percentage="progressValue" 
            :status="progressStatus"
            :stroke-width="8"
          />
          <p class="progress-text">{{ progressText }}</p>
        </div>
        
        <div v-else class="form-section">
          <el-form :model="processForm" :rules="processRules" ref="formRef">
            <el-form-item label="处理类型" prop="type">
              <el-select v-model="processForm.type" placeholder="请选择处理类型">
                <el-option label="数据清理" value="clean" />
                <el-option label="数据导入" value="import" />
                <el-option label="数据导出" value="export" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="处理范围" prop="range">
              <el-checkbox-group v-model="processForm.range">
                <el-checkbox label="用户数据">用户数据</el-checkbox>
                <el-checkbox label="订单数据">订单数据</el-checkbox>
                <el-checkbox label="日志数据">日志数据</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </FuniDialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

const loadingVisible = ref(false)
const processing = ref(false)
const progressValue = ref(0)
const formRef = ref()

const processForm = reactive({
  type: '',
  range: []
})

const processRules = {
  type: [
    { required: true, message: '请选择处理类型', trigger: 'change' }
  ],
  range: [
    { type: 'array', min: 1, message: '请至少选择一个处理范围', trigger: 'change' }
  ]
}

// ✅ 推荐：动态计算按钮状态
const canSubmit = computed(() => {
  return processForm.type && processForm.range.length > 0
})

const progressStatus = computed(() => {
  if (progressValue.value === 100) return 'success'
  if (progressValue.value >= 80) return 'warning'
  return undefined
})

const progressText = computed(() => {
  if (progressValue.value === 100) return '处理完成'
  if (progressValue.value >= 80) return '即将完成...'
  if (progressValue.value >= 50) return '处理中...'
  return '准备中...'
})

// ✅ 推荐：完整的处理流程
const handleProcess = async () => {
  try {
    await formRef.value.validate()
    
    processing.value = true
    progressValue.value = 0
    
    // 模拟处理过程
    const timer = setInterval(() => {
      progressValue.value += 10
      if (progressValue.value >= 100) {
        clearInterval(timer)
        setTimeout(() => {
          processing.value = false
          loadingVisible.value = false
          ElMessage.success('处理完成')
        }, 1000)
      }
    }, 300)
    
  } catch (error) {
    ElMessage.error('请检查表单输入')
  }
}
</script>

<style scoped>
.process-content {
  padding: 20px;
  min-height: 200px;
}

.loading-section {
  text-align: center;
}

.progress-text {
  margin-top: 15px;
  color: #606266;
}

.form-section {
  margin-top: 10px;
}
</style>
```

### 3. 层级管理最佳实践

#### 嵌套对话框处理
```vue
<template>
  <div class="nested-dialog-examples">
    <!-- ✅ 推荐：正确的嵌套对话框配置 -->

    <!-- 第一层对话框 -->
    <FuniDialog
      v-model="level1Visible"
      title="第一层对话框"
      size="large"
      :append-to-body="true"
      :z-index="2000"
      :destroy-on-close="false"
    >
      <div class="level1-content">
        <p>这是第一层对话框的内容。</p>
        <el-button type="primary" @click="openLevel2">
          打开第二层对话框
        </el-button>
      </div>
    </FuniDialog>

    <!-- 第二层对话框 -->
    <FuniDialog
      v-model="level2Visible"
      title="第二层对话框"
      size="default"
      :append-to-body="true"
      :z-index="3000"
      :destroy-on-close="true"
      @closed="handleLevel2Closed"
    >
      <div class="level2-content">
        <p>这是第二层对话框的内容。</p>
        <el-button type="warning" @click="openLevel3">
          打开第三层对话框
        </el-button>
      </div>
    </FuniDialog>

    <!-- 第三层对话框 -->
    <FuniDialog
      v-model="level3Visible"
      title="第三层对话框"
      size="small"
      :append-to-body="true"
      :z-index="4000"
      :destroy-on-close="true"
    >
      <div class="level3-content">
        <p>这是第三层对话框，建议不要超过三层嵌套。</p>
        <el-alert
          title="提示"
          type="warning"
          description="过多的嵌套对话框会影响用户体验"
          :closable="false"
        />
      </div>
    </FuniDialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const level1Visible = ref(false)
const level2Visible = ref(false)
const level3Visible = ref(false)

// ✅ 推荐：层级化的打开逻辑
const openLevel1 = () => {
  level1Visible.value = true
}

const openLevel2 = () => {
  level2Visible.value = true
}

const openLevel3 = () => {
  level3Visible.value = true
}

// ✅ 推荐：处理嵌套关闭逻辑
const handleLevel2Closed = () => {
  // 第二层关闭时，确保第三层也关闭
  level3Visible.value = false
}
</script>

<style scoped>
.level1-content,
.level2-content,
.level3-content {
  padding: 20px;
  text-align: center;
}
</style>
```

#### 全局对话框管理
```javascript
// ✅ 推荐：全局对话框管理器
class DialogManager {
  constructor() {
    this.dialogs = new Map()
    this.zIndexBase = 2000
    this.zIndexStep = 100
  }

  // 注册对话框
  register(id, config = {}) {
    const zIndex = this.zIndexBase + (this.dialogs.size * this.zIndexStep)

    this.dialogs.set(id, {
      id,
      zIndex,
      visible: false,
      config: {
        appendToBody: true,
        destroyOnClose: true,
        ...config
      }
    })

    return this.dialogs.get(id)
  }

  // 打开对话框
  open(id) {
    const dialog = this.dialogs.get(id)
    if (dialog) {
      dialog.visible = true
      return dialog
    }
    throw new Error(`Dialog ${id} not found`)
  }

  // 关闭对话框
  close(id) {
    const dialog = this.dialogs.get(id)
    if (dialog) {
      dialog.visible = false
    }
  }

  // 关闭所有对话框
  closeAll() {
    this.dialogs.forEach(dialog => {
      dialog.visible = false
    })
  }

  // 获取当前打开的对话框数量
  getOpenCount() {
    return Array.from(this.dialogs.values()).filter(d => d.visible).length
  }
}

// 使用示例
const dialogManager = new DialogManager()

// 注册对话框
const userDialog = dialogManager.register('user-edit', {
  size: 'large',
  title: '编辑用户'
})

const confirmDialog = dialogManager.register('confirm-delete', {
  size: 'small',
  title: '确认删除'
})

// 在组件中使用
export const useDialogManager = () => {
  return {
    dialogManager,
    openUserDialog: () => dialogManager.open('user-edit'),
    openConfirmDialog: () => dialogManager.open('confirm-delete'),
    closeAllDialogs: () => dialogManager.closeAll()
  }
}
```

### 4. 性能优化最佳实践

#### 懒加载和按需渲染
```vue
<template>
  <div class="performance-examples">
    <!-- ✅ 推荐：使用 destroy-on-close 优化内存 -->
    <FuniDialog
      v-model="heavyDialogVisible"
      title="重型组件对话框"
      size="max"
      :destroy-on-close="true"
      :append-to-body="true"
    >
      <!-- 懒加载重型组件 -->
      <HeavyComponent v-if="heavyDialogVisible" />
    </FuniDialog>

    <!-- ✅ 推荐：条件渲染优化 -->
    <FuniDialog
      v-model="conditionalVisible"
      title="条件渲染对话框"
      size="large"
    >
      <div class="conditional-content">
        <!-- 只在需要时渲染复杂组件 -->
        <DataTable v-if="shouldShowTable" :data="tableData" />
        <ChartComponent v-else-if="shouldShowChart" :data="chartData" />
        <SimpleContent v-else />
      </div>
    </FuniDialog>

    <!-- ✅ 推荐：使用 keep-alive 缓存状态 -->
    <FuniDialog
      v-model="cachedDialogVisible"
      title="缓存状态对话框"
      size="default"
      :destroy-on-close="false"
    >
      <keep-alive>
        <FormComponent v-if="cachedDialogVisible" :key="formKey" />
      </keep-alive>
    </FuniDialog>
  </div>
</template>

<script setup>
import { ref, computed, defineAsyncComponent } from 'vue'

// ✅ 推荐：异步组件懒加载
const HeavyComponent = defineAsyncComponent({
  loader: () => import('@/components/HeavyComponent.vue'),
  loadingComponent: () => h('div', { class: 'loading' }, '加载中...'),
  errorComponent: () => h('div', { class: 'error' }, '加载失败'),
  delay: 200,
  timeout: 3000
})

const DataTable = defineAsyncComponent(() => import('@/components/DataTable.vue'))
const ChartComponent = defineAsyncComponent(() => import('@/components/ChartComponent.vue'))

const heavyDialogVisible = ref(false)
const conditionalVisible = ref(false)
const cachedDialogVisible = ref(false)
const currentView = ref('simple')
const formKey = ref(0)

// ✅ 推荐：计算属性优化渲染条件
const shouldShowTable = computed(() => currentView.value === 'table')
const shouldShowChart = computed(() => currentView.value === 'chart')

// 模拟数据
const tableData = ref([])
const chartData = ref([])

// ✅ 推荐：数据懒加载
const loadTableData = async () => {
  if (tableData.value.length === 0) {
    // 模拟API调用
    const data = await fetchTableData()
    tableData.value = data
  }
}

const loadChartData = async () => {
  if (chartData.value.length === 0) {
    // 模拟API调用
    const data = await fetchChartData()
    chartData.value = data
  }
}

// 模拟API函数
const fetchTableData = () => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve(Array.from({ length: 100 }, (_, i) => ({ id: i, name: `Item ${i}` })))
    }, 1000)
  })
}

const fetchChartData = () => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve(Array.from({ length: 12 }, (_, i) => ({ month: i + 1, value: Math.random() * 100 })))
    }, 800)
  })
}
</script>

<style scoped>
.conditional-content {
  min-height: 300px;
  padding: 20px;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #909399;
}

.error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #f56c6c;
}
</style>
```

## 避免的用法和常见错误

### 1. 尺寸配置错误

```vue
<!-- ❌ 错误：内容与尺寸不匹配 -->
<FuniDialog
  v-model="visible"
  title="复杂数据表格"
  size="small"
>
  <ComplexDataTable />
</FuniDialog>

<!-- ✅ 正确：根据内容选择合适尺寸 -->
<FuniDialog
  v-model="visible"
  title="复杂数据表格"
  size="max"
>
  <ComplexDataTable />
</FuniDialog>

<!-- ❌ 错误：简单内容使用过大尺寸 -->
<FuniDialog
  v-model="visible"
  title="确认删除"
  size="max"
>
  <p>确定要删除吗？</p>
</FuniDialog>

<!-- ✅ 正确：简单内容使用小尺寸 -->
<FuniDialog
  v-model="visible"
  title="确认删除"
  size="small"
>
  <p>确定要删除吗？</p>
</FuniDialog>
```

### 2. 层级管理错误

```javascript
// ❌ 错误：没有设置正确的层级
const badNestedConfig = {
  level1: { zIndex: 2000 },
  level2: { zIndex: 2000 }, // 相同层级会导致问题
  level3: { zIndex: 2000 }
}

// ✅ 正确：递增的层级设置
const goodNestedConfig = {
  level1: { zIndex: 2000, appendToBody: true },
  level2: { zIndex: 3000, appendToBody: true },
  level3: { zIndex: 4000, appendToBody: true }
}

// ❌ 错误：过多的嵌套层级
const tooManyLevels = () => {
  // 超过3层的嵌套会严重影响用户体验
  openLevel4Dialog()
}

// ✅ 正确：限制嵌套层级
const reasonableLevels = () => {
  // 最多3层嵌套，超过时考虑重新设计交互流程
  if (currentLevel < 3) {
    openNextDialog()
  } else {
    // 提示用户或采用其他交互方式
    ElMessage.warning('请先关闭其他对话框')
  }
}
```

### 3. 性能问题

```vue
<!-- ❌ 错误：没有使用 destroy-on-close -->
<FuniDialog
  v-model="visible"
  title="重型组件"
  :destroy-on-close="false"
>
  <HeavyComponent />
</FuniDialog>

<!-- ✅ 正确：销毁重型组件 -->
<FuniDialog
  v-model="visible"
  title="重型组件"
  :destroy-on-close="true"
>
  <HeavyComponent v-if="visible" />
</FuniDialog>

<!-- ❌ 错误：没有懒加载 -->
<FuniDialog v-model="visible">
  <ExpensiveComponent />
</FuniDialog>

<!-- ✅ 正确：条件渲染和懒加载 -->
<FuniDialog v-model="visible">
  <ExpensiveComponent v-if="visible" />
</FuniDialog>
```

## 总结

### 核心原则
1. **尺寸匹配**：根据内容复杂度选择合适的对话框尺寸
2. **层级管理**：合理设置嵌套对话框的层级和配置
3. **用户体验**：提供清晰的状态反馈和错误处理
4. **性能优化**：使用懒加载和按需渲染优化性能
5. **响应式设计**：考虑不同屏幕尺寸的适配

### 开发建议
1. 制定对话框使用规范和尺寸标准
2. 建立统一的错误处理和用户反馈机制
3. 限制嵌套层级，避免过度复杂的交互
4. 定期review对话框的性能和用户体验
5. 考虑移动端的适配和优化

#### 错误处理和用户反馈
```vue
<template>
  <div class="error-handling-examples">
    <!-- ✅ 推荐：完善的错误处理 -->
    <FuniDialog
      v-model="errorHandlingVisible"
      title="数据提交"
      size="default"
      :before-close="handleBeforeClose"
      @confirm="handleSubmitWithValidation"
    >
      <div class="error-content">
        <!-- 错误提示区域 -->
        <el-alert
          v-if="errorMessage"
          :title="errorMessage"
          type="error"
          :closable="true"
          @close="clearError"
          class="error-alert"
        />
        
        <!-- 表单区域 -->
        <el-form 
          :model="submitForm" 
          :rules="submitRules" 
          ref="submitFormRef"
          @submit.prevent
        >
          <el-form-item label="用户名" prop="username">
            <el-input 
              v-model="submitForm.username" 
              placeholder="请输入用户名"
              :disabled="submitting"
            />
          </el-form-item>
          
          <el-form-item label="邮箱" prop="email">
            <el-input 
              v-model="submitForm.email" 
              placeholder="请输入邮箱"
              :disabled="submitting"
            />
          </el-form-item>
          
          <el-form-item label="密码" prop="password">
            <el-input 
              v-model="submitForm.password" 
              type="password" 
              placeholder="请输入密码"
              :disabled="submitting"
              show-password
            />
          </el-form-item>
        </el-form>
        
        <!-- 提交状态提示 -->
        <div v-if="submitting" class="submitting-tip">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>正在提交数据，请稍候...</span>
        </div>
      </div>
    </FuniDialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Loading } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const errorHandlingVisible = ref(false)
const submitting = ref(false)
const errorMessage = ref('')
const submitFormRef = ref()

const submitForm = reactive({
  username: '',
  email: '',
  password: ''
})

const submitRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在3到20个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// ✅ 推荐：关闭前确认
const handleBeforeClose = (done) => {
  if (submitting.value) {
    ElMessage.warning('正在提交数据，请稍候...')
    return
  }
  
  const hasChanges = submitForm.username || submitForm.email || submitForm.password
  
  if (hasChanges) {
    ElMessageBox.confirm('有未保存的更改，确定要关闭吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      done()
    }).catch(() => {
      // 取消关闭
    })
  } else {
    done()
  }
}

// ✅ 推荐：完整的提交验证流程
const handleSubmitWithValidation = async () => {
  try {
    // 清除之前的错误
    clearError()
    
    // 表单验证
    await submitFormRef.value.validate()
    
    submitting.value = true
    
    // 模拟API调用
    const success = await simulateApiCall()
    
    if (success) {
      ElMessage.success('提交成功')
      errorHandlingVisible.value = false
      resetForm()
    } else {
      throw new Error('服务器返回错误')
    }
    
  } catch (error) {
    console.error('提交失败:', error)
    
    if (error.message.includes('网络')) {
      errorMessage.value = '网络连接失败，请检查网络设置后重试'
    } else if (error.message.includes('权限')) {
      errorMessage.value = '权限不足，请联系管理员'
    } else if (error.message.includes('用户名')) {
      errorMessage.value = '用户名已存在，请使用其他用户名'
    } else {
      errorMessage.value = '提交失败，请稍后重试'
    }
  } finally {
    submitting.value = false
  }
}

const simulateApiCall = () => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 模拟不同的错误情况
      const random = Math.random()
      if (random < 0.3) {
        reject(new Error('网络连接失败'))
      } else if (random < 0.5) {
        reject(new Error('用户名已存在'))
      } else if (random < 0.7) {
        reject(new Error('权限不足'))
      } else {
        resolve(true)
      }
    }, 2000)
  })
}

const clearError = () => {
  errorMessage.value = ''
}

const resetForm = () => {
  submitFormRef.value?.resetFields()
  Object.assign(submitForm, {
    username: '',
    email: '',
    password: ''
  })
}
</script>

<style scoped>
.error-content {
  padding: 20px;
}

.error-alert {
  margin-bottom: 20px;
}

.submitting-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 15px;
  padding: 10px;
  background-color: #f0f9ff;
  border-radius: 4px;
  color: #1e40af;
}
</style>
```
