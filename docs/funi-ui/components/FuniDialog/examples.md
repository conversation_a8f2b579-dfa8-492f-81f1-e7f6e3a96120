# FuniDialog 使用示例

## 基础示例

### 1. 基础对话框
```vue
<template>
  <div class="example-container">
    <h3>基础对话框</h3>
    <el-button type="primary" @click="basicDialogVisible = true">
      打开基础对话框
    </el-button>
    
    <FuniDialog
      v-model="basicDialogVisible"
      title="基础对话框"
      size="default"
      @confirm="handleBasicConfirm"
      @cancel="handleBasicCancel"
    >
      <div class="dialog-content">
        <p>这是一个基础的对话框示例。</p>
        <p>包含标题、内容区域和底部按钮。</p>
        <el-alert
          title="提示信息"
          type="info"
          description="这里可以放置任何内容，如表单、表格、图表等。"
          show-icon
          :closable="false"
        />
      </div>
    </FuniDialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const basicDialogVisible = ref(false)

const handleBasicConfirm = () => {
  ElMessage.success('确认操作')
  basicDialogVisible.value = false
}

const handleBasicCancel = () => {
  ElMessage.info('取消操作')
  basicDialogVisible.value = false
}
</script>

<style scoped>
.example-container {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 20px;
}

.dialog-content {
  padding: 20px;
  min-height: 200px;
}
</style>
```

### 2. 不同尺寸对话框
```vue
<template>
  <div class="example-container">
    <h3>不同尺寸对话框</h3>
    <div class="button-group">
      <el-button @click="openSizeDialog('small')">小尺寸 (600px)</el-button>
      <el-button @click="openSizeDialog('default')">默认尺寸 (800px)</el-button>
      <el-button @click="openSizeDialog('large')">大尺寸 (1000px)</el-button>
      <el-button @click="openSizeDialog('max')">最大尺寸</el-button>
    </div>
    
    <FuniDialog
      v-model="sizeDialogVisible"
      :title="`${currentSize}尺寸对话框`"
      :size="currentSize"
    >
      <div class="size-demo-content">
        <h4>当前尺寸：{{ getSizeDescription(currentSize) }}</h4>
        <div class="size-info">
          <p><strong>宽度：</strong>{{ getSizeWidth(currentSize) }}</p>
          <p><strong>适用场景：</strong>{{ getSizeUsage(currentSize) }}</p>
        </div>
        
        <el-divider />
        
        <div class="demo-content">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card header="示例内容A">
                <p>这里是一些示例内容，用于展示不同尺寸对话框的效果。</p>
                <el-progress :percentage="75" />
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card header="示例内容B">
                <p>可以根据内容的复杂程度选择合适的对话框尺寸。</p>
                <el-tag type="success">推荐使用</el-tag>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </FuniDialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const sizeDialogVisible = ref(false)
const currentSize = ref('default')

const openSizeDialog = (size) => {
  currentSize.value = size
  sizeDialogVisible.value = true
}

const getSizeDescription = (size) => {
  const descriptions = {
    small: '小尺寸',
    default: '默认尺寸',
    large: '大尺寸',
    max: '最大尺寸'
  }
  return descriptions[size] || '未知尺寸'
}

const getSizeWidth = (size) => {
  const widths = {
    small: '600px',
    default: '800px',
    large: '1000px',
    max: 'calc(100vw - 80px)'
  }
  return widths[size] || '未知'
}

const getSizeUsage = (size) => {
  const usages = {
    small: '简单确认、提示信息',
    default: '表单编辑、详情查看',
    large: '复杂表单、数据展示',
    max: '全屏展示、复杂业务'
  }
  return usages[size] || '未知'
}
</script>

<style scoped>
.button-group {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.size-demo-content {
  padding: 20px;
}

.size-info {
  background-color: #f8fafc;
  padding: 15px;
  border-radius: 6px;
  margin: 15px 0;
}

.demo-content {
  margin-top: 20px;
}
</style>
```

### 3. 自定义头部和底部
```vue
<template>
  <div class="example-container">
    <h3>自定义头部和底部</h3>
    <el-button type="success" @click="customDialogVisible = true">
      打开自定义对话框
    </el-button>
    
    <FuniDialog
      v-model="customDialogVisible"
      size="default"
      :hide-footer="false"
    >
      <!-- 自定义头部 -->
      <template #header="{ close }">
        <div class="custom-header">
          <div class="header-left">
            <el-icon class="header-icon" color="#409eff">
              <InfoFilled />
            </el-icon>
            <span class="header-title">自定义标题</span>
            <el-tag type="warning" size="small">重要</el-tag>
          </div>
          <div class="header-right">
            <el-button type="text" @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
            </el-button>
            <el-button type="text" @click="close">
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 内容区域 -->
      <div class="custom-content">
        <el-alert
          title="自定义对话框"
          type="info"
          description="这个对话框使用了自定义的头部和底部。"
          show-icon
          :closable="false"
        />
        
        <div class="content-section">
          <h4>操作进度</h4>
          <el-progress 
            :percentage="progressValue" 
            :status="progressStatus"
            :stroke-width="8"
          />
          <p class="progress-text">{{ progressText }}</p>
        </div>
        
        <div class="content-section">
          <h4>选项设置</h4>
          <el-checkbox-group v-model="selectedOptions">
            <el-checkbox label="option1">启用自动保存</el-checkbox>
            <el-checkbox label="option2">发送通知</el-checkbox>
            <el-checkbox label="option3">生成报告</el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      
      <!-- 自定义底部 -->
      <template #footer>
        <div class="custom-footer">
          <div class="footer-left">
            <el-checkbox v-model="agreeTerms">
              我已阅读并同意相关条款
            </el-checkbox>
          </div>
          <div class="footer-right">
            <el-button @click="customDialogVisible = false">
              取消
            </el-button>
            <el-button 
              type="primary" 
              :disabled="!agreeTerms || progressValue < 100"
              :loading="submitLoading"
              @click="handleCustomSubmit"
            >
              {{ submitLoading ? '处理中...' : '确认提交' }}
            </el-button>
          </div>
        </div>
      </template>
    </FuniDialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { InfoFilled, Refresh, Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const customDialogVisible = ref(false)
const progressValue = ref(0)
const selectedOptions = ref(['option1'])
const agreeTerms = ref(false)
const submitLoading = ref(false)

const progressStatus = computed(() => {
  if (progressValue.value === 100) return 'success'
  if (progressValue.value >= 80) return 'warning'
  return undefined
})

const progressText = computed(() => {
  if (progressValue.value === 100) return '准备完成，可以提交'
  if (progressValue.value >= 80) return '即将完成...'
  if (progressValue.value >= 50) return '进行中...'
  return '准备中...'
})

// 模拟进度更新
watch(customDialogVisible, (visible) => {
  if (visible) {
    progressValue.value = 0
    const timer = setInterval(() => {
      progressValue.value += 10
      if (progressValue.value >= 100) {
        clearInterval(timer)
      }
    }, 500)
  }
})

const handleRefresh = () => {
  progressValue.value = 0
  ElMessage.info('已刷新')
}

const handleCustomSubmit = async () => {
  submitLoading.value = true
  
  try {
    // 模拟提交操作
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    ElMessage.success('提交成功')
    customDialogVisible.value = false
  } catch (error) {
    ElMessage.error('提交失败')
  } finally {
    submitLoading.value = false
  }
}
</script>

<style scoped>
.custom-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-icon {
  font-size: 18px;
}

.header-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.header-right {
  display: flex;
  gap: 5px;
}

.custom-content {
  padding: 20px;
}

.content-section {
  margin: 20px 0;
}

.progress-text {
  margin-top: 8px;
  color: #606266;
  font-size: 14px;
}

.custom-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-top: 1px solid #e4e7ed;
}

.footer-right {
  display: flex;
  gap: 10px;
}
</style>
```

## 高级示例

### 4. 表单对话框
```vue
<template>
  <div class="example-container">
    <h3>表单对话框</h3>
    <el-button type="primary" @click="formDialogVisible = true">
      编辑用户信息
    </el-button>

    <FuniDialog
      v-model="formDialogVisible"
      title="编辑用户信息"
      size="large"
      :confirm-loading="submitLoading"
      :confirm-disabled="!isFormValid"
      @confirm="handleFormSubmit"
      @cancel="handleFormCancel"
      @closed="resetForm"
    >
      <div class="form-container">
        <el-form
          ref="formRef"
          :model="userForm"
          :rules="formRules"
          label-width="120px"
          label-position="right"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="用户名" prop="username">
                <el-input
                  v-model="userForm.username"
                  placeholder="请输入用户名"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="邮箱" prop="email">
                <el-input
                  v-model="userForm.email"
                  placeholder="请输入邮箱"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="手机号" prop="phone">
                <el-input
                  v-model="userForm.phone"
                  placeholder="请输入手机号"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="部门" prop="department">
                <el-select
                  v-model="userForm.department"
                  placeholder="请选择部门"
                  style="width: 100%"
                >
                  <el-option label="技术部" value="tech" />
                  <el-option label="产品部" value="product" />
                  <el-option label="运营部" value="operation" />
                  <el-option label="市场部" value="marketing" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="个人简介" prop="bio">
            <el-input
              v-model="userForm.bio"
              type="textarea"
              :rows="4"
              placeholder="请输入个人简介"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
    </FuniDialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'

const formDialogVisible = ref(false)
const formRef = ref()
const submitLoading = ref(false)

const userForm = reactive({
  username: '',
  email: '',
  phone: '',
  department: '',
  bio: ''
})

const formRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在3到20个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  department: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ]
}

const isFormValid = computed(() => {
  return userForm.username &&
         userForm.email &&
         userForm.phone &&
         userForm.department
})

const handleFormSubmit = async () => {
  try {
    await formRef.value.validate()

    submitLoading.value = true

    // 模拟提交操作
    await new Promise(resolve => setTimeout(resolve, 2000))

    ElMessage.success('用户信息保存成功')
    formDialogVisible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单输入')
  } finally {
    submitLoading.value = false
  }
}

const handleFormCancel = () => {
  formDialogVisible.value = false
}

const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(userForm, {
    username: '',
    email: '',
    phone: '',
    department: '',
    bio: ''
  })
}
</script>

<style scoped>
.form-container {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}
</style>
```

## 注意事项

### 使用建议
1. **尺寸选择**：根据内容复杂度选择合适的对话框尺寸
2. **层级管理**：嵌套对话框需要设置不同的z-index和append-to-body
3. **用户体验**：提供明确的操作反馈和加载状态
4. **表单验证**：在表单对话框中实现完整的验证逻辑

### 性能优化
1. **懒加载**：对于复杂内容使用懒加载
2. **销毁机制**：使用destroy-on-close销毁不常用的对话框
3. **内容限制**：避免在对话框中放置过多复杂组件

### 移动端适配
1. **响应式设计**：小屏幕设备考虑全屏显示
2. **触摸优化**：确保按钮大小和间距适合触摸操作
3. **滚动处理**：长内容时提供合适的滚动体验
