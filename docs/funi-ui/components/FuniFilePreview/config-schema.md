# FuniFilePreview 配置结构定义

## 组件配置接口

### IFuniFilePreviewProps

```typescript
// 该组件不接受直接 props，通过路由参数获取配置
interface IFuniFilePreviewProps {
  // 无直接 props
}
```

### IRouteQueryParams

```typescript
interface IRouteQueryParams {
  /** 文件预览地址（URL 编码） */
  src: string
  /** 文件类型 */
  type: 'docx' | 'xlsx' | 'pdf' | 'PDF'
}
```

## 内部状态结构

### IComponentState

```typescript
interface IComponentState {
  /** 文件源地址 */
  src: Ref<string>
  /** 文件类型 */
  type: Ref<string>
  /** 路由对象 */
  route: RouteLocationNormalized
}
```

## 文件类型配置

### ISupportedFileTypes

```typescript
interface ISupportedFileTypes {
  /** Word 文档 */
  docx: 'docx'
  /** Excel 表格 */
  xlsx: 'xlsx'
  /** PDF 文档 */
  pdf: 'pdf' | 'PDF'
}
```

### IFileTypeConfig

```typescript
interface IFileTypeConfig {
  /** 支持的文件类型列表 */
  supportedTypes: string[]
  /** 文件类型验证函数 */
  isSupported: (type: string) => boolean
  /** 获取预览组件类型 */
  getPreviewComponent: (type: string) => 'docx' | 'xlsx' | 'pdf' | 'iframe'
}
```

## Vue Office 组件配置

### IVueOfficeDocxProps

```typescript
interface IVueOfficeDocxProps {
  /** 文档源地址 */
  src: string
  /** 文档配置选项 */
  options?: IDocxOptions
}
```

### IVueOfficeExcelProps

```typescript
interface IVueOfficeExcelProps {
  /** 表格源地址 */
  src: string
  /** 表格配置选项 */
  options?: IExcelOptions
}
```

### IDocxOptions

```typescript
interface IDocxOptions {
  /** 是否显示工具栏 */
  toolbar?: boolean
  /** 缩放比例 */
  zoom?: number
  /** 是否只读 */
  readonly?: boolean
  /** 主题配置 */
  theme?: 'light' | 'dark'
}
```

### IExcelOptions

```typescript
interface IExcelOptions {
  /** 是否显示网格线 */
  showGridLines?: boolean
  /** 是否显示行号 */
  showRowNumbers?: boolean
  /** 是否显示列标题 */
  showColumnHeaders?: boolean
  /** 缩放比例 */
  zoom?: number
}
```

## 路由配置结构

### IRouteConfig

```typescript
interface IRouteConfig {
  /** 路由路径 */
  path: string
  /** 路由名称 */
  name: string
  /** 组件 */
  component: Component
  /** 路由元信息 */
  meta?: IRouteMeta
}
```

### IRouteMeta

```typescript
interface IRouteMeta {
  /** 页面标题 */
  title?: string
  /** 是否需要认证 */
  requiresAuth?: boolean
  /** 页面图标 */
  icon?: string
}
```

## 文件处理配置

### IFileHandler

```typescript
interface IFileHandler {
  /** URL 解码处理 */
  decodeUrl: (encodedUrl: string) => string
  /** 文件类型检测 */
  detectFileType: (url: string) => string
  /** 文件下载处理 */
  downloadFile: (url: string) => void
  /** 文件验证 */
  validateFile: (url: string, type: string) => boolean
}
```

### IUrlProcessor

```typescript
interface IUrlProcessor {
  /** URL 编码 */
  encode: (url: string) => string
  /** URL 解码 */
  decode: (encodedUrl: string) => string
  /** URL 验证 */
  validate: (url: string) => boolean
  /** 相对路径转绝对路径 */
  toAbsolute: (url: string) => string
}
```

## 预览器配置

### IPreviewerConfig

```typescript
interface IPreviewerConfig {
  /** Word 文档预览器配置 */
  docx: {
    component: 'vue-office-docx'
    styles: string[]
    options: IDocxOptions
  }
  /** Excel 表格预览器配置 */
  xlsx: {
    component: 'vue-office-excel'
    styles: string[]
    options: IExcelOptions
  }
  /** PDF 预览器配置 */
  pdf: {
    component: 'iframe'
    attributes: IIframeAttributes
  }
}
```

### IIframeAttributes

```typescript
interface IIframeAttributes {
  /** 样式 */
  style: string
  /** 框架边框 */
  frameborder: string
  /** 沙箱模式 */
  sandbox?: string
  /** 允许全屏 */
  allowfullscreen?: boolean
}
```

## 错误处理配置

### IErrorHandling

```typescript
interface IErrorHandling {
  /** 不支持文件类型处理 */
  unsupportedFileType: (type: string, url: string) => void
  /** 文件加载失败处理 */
  fileLoadError: (error: Error) => void
  /** 网络错误处理 */
  networkError: (error: Error) => void
  /** 权限错误处理 */
  permissionError: (error: Error) => void
}
```

### IErrorMessages

```typescript
interface IErrorMessages {
  /** 不支持的文件类型 */
  unsupportedType: string
  /** 文件加载失败 */
  loadFailed: string
  /** 网络连接错误 */
  networkError: string
  /** 权限不足 */
  permissionDenied: string
}
```

## 样式配置

### IStyleConfig

```typescript
interface IStyleConfig {
  /** 容器样式 */
  container: {
    width: '100%'
    height: '100%'
    display: 'flex'
    justifyContent: 'center'
    background: 'gray'
  }
  /** iframe 样式 */
  iframe: {
    width: '100%'
    height: '100%'
    border: 'none'
  }
}
```

## 配置示例

### 基础路由配置

```typescript
const filePreviewRoute: IRouteConfig = {
  path: '/file-preview',
  name: 'FilePreview',
  component: () => import('@/components/FuniFilePreview/index.vue'),
  meta: {
    title: '文件预览',
    requiresAuth: false
  }
}
```

### 文件类型配置

```typescript
const fileTypeConfig: IFileTypeConfig = {
  supportedTypes: ['docx', 'xlsx', 'pdf', 'PDF'],
  isSupported: (type: string) => 
    ['docx', 'xlsx', 'pdf', 'PDF'].includes(type),
  getPreviewComponent: (type: string) => {
    switch (type.toLowerCase()) {
      case 'docx': return 'docx'
      case 'xlsx': return 'xlsx'
      case 'pdf': return 'pdf'
      default: return 'iframe'
    }
  }
}
```

### 预览器配置

```typescript
const previewerConfig: IPreviewerConfig = {
  docx: {
    component: 'vue-office-docx',
    styles: ['@vue-office/docx/lib/index.css'],
    options: {
      toolbar: false,
      zoom: 1,
      readonly: true,
      theme: 'light'
    }
  },
  xlsx: {
    component: 'vue-office-excel',
    styles: ['@vue-office/excel/lib/index.css'],
    options: {
      showGridLines: true,
      showRowNumbers: true,
      showColumnHeaders: true,
      zoom: 1
    }
  },
  pdf: {
    component: 'iframe',
    attributes: {
      style: 'width: 100%; height: 100%',
      frameborder: '0',
      allowfullscreen: true
    }
  }
}
```

## 工具函数配置

### IUtilityFunctions

```typescript
interface IUtilityFunctions {
  /** 构建预览 URL */
  buildPreviewUrl: (src: string, type: string) => string
  /** 解析查询参数 */
  parseQueryParams: (query: Record<string, any>) => IRouteQueryParams
  /** 验证文件 URL */
  validateFileUrl: (url: string) => boolean
  /** 获取文件扩展名 */
  getFileExtension: (filename: string) => string
  /** 检查文件是否可预览 */
  isPreviewable: (type: string) => boolean
}
```

## 性能优化配置

### IPerformanceConfig

```typescript
interface IPerformanceConfig {
  /** 预加载配置 */
  preload: {
    enabled: boolean
    types: string[]
  }
  /** 缓存配置 */
  cache: {
    enabled: boolean
    maxSize: number
    ttl: number
  }
  /** 懒加载配置 */
  lazyLoad: {
    enabled: boolean
    threshold: number
  }
}
```

## 安全配置

### ISecurityConfig

```typescript
interface ISecurityConfig {
  /** 允许的域名列表 */
  allowedDomains: string[]
  /** 是否验证文件来源 */
  validateOrigin: boolean
  /** 是否启用沙箱模式 */
  enableSandbox: boolean
  /** CSP 配置 */
  contentSecurityPolicy: string
}
```

## 默认配置

```typescript
const defaultConfig = {
  supportedTypes: ['docx', 'xlsx', 'pdf', 'PDF'],
  
  errorMessages: {
    unsupportedType: '不支持的文件类型',
    loadFailed: '文件加载失败',
    networkError: '网络连接错误',
    permissionDenied: '权限不足'
  },
  
  styleConfig: {
    container: {
      width: '100%',
      height: '100%',
      display: 'flex',
      justifyContent: 'center',
      background: 'gray'
    },
    iframe: {
      width: '100%',
      height: '100%',
      border: 'none'
    }
  },
  
  performanceConfig: {
    preload: {
      enabled: false,
      types: ['docx', 'xlsx']
    },
    cache: {
      enabled: true,
      maxSize: 50,
      ttl: 3600000 // 1 hour
    },
    lazyLoad: {
      enabled: true,
      threshold: 0.1
    }
  }
}
```
