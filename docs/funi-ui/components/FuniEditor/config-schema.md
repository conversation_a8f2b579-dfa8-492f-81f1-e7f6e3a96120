# FuniEditor 配置结构定义

## 基础配置结构

### FuniEditorProps 接口定义

```typescript
interface FuniEditorProps {
  // 基础配置
  modelValue?: string
  mode?: '1' | '2' | 'wysiwyg' | 'markdown' | 'ir'
  showMode?: boolean
  
  // 编辑器尺寸
  height?: string | number
  width?: string | number
  
  // 状态控制
  disabled?: boolean
  readonly?: boolean
  placeholder?: string
  
  // TinyMCE 配置
  plugins?: string | string[]
  toolbar?: string | string[] | boolean
  menubar?: boolean | string
  statusbar?: boolean
  resize?: boolean | string
  branding?: boolean
  elementpath?: boolean
  
  // Vditor 配置
  lang?: string
  
  // 上传配置
  uploadUrl?: string
  srcPrefix?: string
  
  // 自动保存
  autosave?: boolean
  autosaveInterval?: number
}
```

### TinyMCE 编辑器配置

```typescript
interface TinyMCEConfig {
  // 基础配置
  height: number | string
  language: string
  language_url: string
  
  // 皮肤和主题
  skin_url: string
  content_css: string
  content_style: string
  
  // 插件配置
  plugins: string | string[]
  toolbar: string | string[] | boolean
  toolbar_mode: 'floating' | 'sliding' | 'scrolling' | 'wrap'
  
  // 菜单配置
  menubar: boolean | string
  menu: Record<string, MenuConfig>
  
  // 字体配置
  fontsize_formats: string
  font_formats: string
  
  // 功能开关
  branding: boolean
  resize: boolean | string
  statusbar: boolean
  elementpath: boolean
  
  // 图片上传
  images_upload_handler: (
    blobInfo: BlobInfo,
    success: (url: string) => void,
    failure: (message: string) => void
  ) => void
}

interface MenuConfig {
  title: string
  items: string
}

interface BlobInfo {
  blob: () => File
  base64: () => string
}
```

### Vditor 编辑器配置

```typescript
interface VditorConfig {
  // 基础配置
  height: number | string
  lang: string
  placeholder: string
  
  // 模式配置
  mode: 'wysiwyg' | 'markdown' | 'ir'
  
  // 工具栏配置
  toolbar: string[]
  
  // 回调函数
  after?: () => void
  blur?: (value: string) => void
  input?: (value: string) => void
  focus?: (value: string) => void
  
  // 上传配置
  upload?: {
    url: string
    max: number
    accept: string
    handler?: (files: File[]) => Promise<string[]>
  }
  
  // 预览配置
  preview?: {
    delay: number
    show: boolean
    parse: (element: HTMLElement) => void
  }
}
```

## 详细配置说明

### 基础配置项

#### modelValue
- **类型**: `string`
- **默认值**: `''`
- **说明**: 编辑器内容，支持HTML和Markdown格式
- **示例**:
  ```javascript
  // HTML内容
  const htmlContent = '<h1>标题</h1><p>段落内容</p>'
  
  // Markdown内容
  const markdownContent = '# 标题\n\n段落内容'
  ```

#### mode
- **类型**: `'1' | '2' | 'wysiwyg' | 'markdown' | 'ir'`
- **默认值**: `'1'`
- **说明**: 编辑器模式
- **示例**:
  ```javascript
  // 富文本模式
  mode: '1' // 或 'wysiwyg'
  
  // Markdown模式
  mode: '2' // 或 'markdown'
  
  // 即时渲染模式
  mode: 'ir'
  ```

#### showMode
- **类型**: `boolean`
- **默认值**: `true`
- **说明**: 是否显示模式切换按钮
- **示例**:
  ```javascript
  // 显示模式切换
  showMode: true
  
  // 隐藏模式切换
  showMode: false
  ```

### 尺寸配置项

#### height
- **类型**: `string | number`
- **默认值**: `400`
- **说明**: 编辑器高度
- **示例**:
  ```javascript
  // 数字形式（像素）
  height: 500
  
  // 字符串形式
  height: '60vh'
  height: '500px'
  ```

#### width
- **类型**: `string | number`
- **默认值**: `'100%'`
- **说明**: 编辑器宽度
- **示例**:
  ```javascript
  // 百分比
  width: '100%'
  
  // 固定宽度
  width: 800
  width: '800px'
  ```

### 工具栏配置

#### plugins
- **类型**: `string | string[]`
- **默认值**: 预设插件列表
- **说明**: TinyMCE插件配置
- **示例**:
  ```javascript
  // 字符串形式
  plugins: 'print preview searchreplace autolink directionality'
  
  // 数组形式
  plugins: [
    'print', 'preview', 'searchreplace', 'autolink',
    'directionality', 'visualblocks', 'visualchars',
    'fullscreen', 'image', 'link', 'media', 'template',
    'code', 'codesample', 'table', 'charmap', 'hr'
  ]
  ```

#### toolbar
- **类型**: `string | string[] | boolean`
- **默认值**: 预设工具栏
- **说明**: 工具栏按钮配置
- **示例**:
  ```javascript
  // 字符串形式
  toolbar: 'undo redo | bold italic | alignleft aligncenter alignright'
  
  // 数组形式（多行工具栏）
  toolbar: [
    'undo redo | bold italic underline',
    'alignleft aligncenter alignright | bullist numlist',
    'link image | code preview'
  ]
  
  // 隐藏工具栏
  toolbar: false
  ```

### 上传配置

#### uploadUrl
- **类型**: `string`
- **默认值**: `'/csccs/file/upload'`
- **说明**: 文件上传接口地址
- **示例**:
  ```javascript
  uploadUrl: '/api/upload/image'
  ```

#### srcPrefix
- **类型**: `string`
- **说明**: 图片URL前缀
- **示例**:
  ```javascript
  srcPrefix: '/api/file/download/'
  ```

## 常用配置组合示例

### 基础富文本编辑器
```javascript
const basicConfig = {
  modelValue: '',
  mode: '1',
  showMode: false,
  height: 400,
  placeholder: '请输入内容...',
  toolbar: 'undo redo | bold italic | alignleft aligncenter alignright',
  plugins: 'link image table code',
  menubar: false,
  statusbar: false
}
```

### Markdown编辑器
```javascript
const markdownConfig = {
  modelValue: '',
  mode: '2',
  showMode: false,
  height: 500,
  placeholder: '请输入Markdown内容...',
  lang: 'zh_CN'
}
```

### 双模式编辑器
```javascript
const dualModeConfig = {
  modelValue: '',
  mode: '1',
  showMode: true,
  height: 600,
  placeholder: '请选择编辑模式并输入内容...'
}
```

### 简化编辑器
```javascript
const simpleConfig = {
  modelValue: '',
  mode: '1',
  showMode: false,
  height: 300,
  toolbar: 'bold italic | link',
  plugins: 'link',
  menubar: false,
  statusbar: false,
  branding: false
}
```

### 完整功能编辑器
```javascript
const fullConfig = {
  modelValue: '',
  mode: '1',
  showMode: true,
  height: 700,
  plugins: [
    'print', 'preview', 'searchreplace', 'autolink',
    'directionality', 'visualblocks', 'visualchars',
    'fullscreen', 'image', 'link', 'media', 'template',
    'code', 'codesample', 'table', 'charmap', 'hr',
    'pagebreak', 'nonbreaking', 'anchor', 'insertdatetime',
    'advlist', 'lists', 'wordcount', 'textpattern', 'autosave'
  ],
  toolbar: [
    'fullscreen undo redo restoredraft | cut copy paste pastetext',
    'forecolor backcolor bold italic underline strikethrough link anchor',
    'alignleft aligncenter alignright alignjustify outdent indent',
    'styleselect formatselect fontselect fontsizeselect',
    'bullist numlist | blockquote subscript superscript removeformat',
    'table image media charmap emoticons hr pagebreak insertdatetime',
    'print preview | code selectall'
  ],
  uploadUrl: '/api/upload/image',
  srcPrefix: '/api/file/download/',
  autosave: true,
  autosaveInterval: 30000
}
```

### 只读显示器
```javascript
const readonlyConfig = {
  modelValue: '<h1>标题</h1><p>只读内容</p>',
  mode: '1',
  showMode: false,
  readonly: true,
  toolbar: false,
  menubar: false,
  statusbar: false,
  height: 300
}
```

## 最佳实践建议

### 1. 模式选择建议
- **富文本模式**：适用于一般用户，提供所见即所得的编辑体验
- **Markdown模式**：适用于技术用户，支持快速格式化和代码编写
- **双模式**：适用于混合用户群体，提供灵活的编辑选择

### 2. 工具栏配置建议
- **简化工具栏**：适用于评论、简单内容编辑
- **标准工具栏**：适用于文章编辑、新闻发布
- **完整工具栏**：适用于专业内容创作、技术文档

### 3. 性能优化建议
- 按需加载插件，避免加载不必要的功能
- 合理设置编辑器高度，避免过高影响页面性能
- 使用自动保存功能，防止内容丢失

### 4. 上传配置建议
- 配置合适的上传接口和文件大小限制
- 设置正确的图片URL前缀
- 实现上传进度和错误处理

### 5. 用户体验建议
- 提供清晰的占位符文本
- 合理配置工具栏，避免功能过多造成困扰
- 支持快捷键操作，提高编辑效率
