# FuniEditor 使用示例

## 基础示例

### 1. 基础富文本编辑器
```vue
<template>
  <div class="example-container">
    <h3>基础富文本编辑器</h3>
    <FuniEditor
      v-model="content"
      :mode="'1'"
      :showMode="false"
      :height="400"
      placeholder="请输入文章内容..."
      @change="handleContentChange"
    />
    
    <div class="content-preview">
      <h4>内容预览：</h4>
      <div v-html="content" class="preview-content"></div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const content = ref('<h2>欢迎使用FuniEditor</h2><p>这是一个功能强大的富文本编辑器。</p>')

const handleContentChange = (value) => {
  console.log('内容变化:', value)
}
</script>

<style scoped>
.example-container {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 20px;
}

.content-preview {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.preview-content {
  border: 1px solid #e0e0e0;
  padding: 10px;
  background-color: white;
  border-radius: 4px;
  min-height: 100px;
}
</style>
```

### 2. Markdown编辑器
```vue
<template>
  <div class="example-container">
    <h3>Markdown编辑器</h3>
    <FuniEditor
      v-model="markdownContent"
      :mode="'2'"
      :showMode="false"
      :height="500"
      placeholder="请输入Markdown内容..."
      @change="handleMarkdownChange"
    />
    
    <div class="markdown-info">
      <h4>Markdown源码：</h4>
      <pre class="markdown-source">{{ markdownSource }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const markdownContent = ref(`
<h1>Markdown示例</h1>
<h2>基础语法</h2>
<p>这是一段<strong>粗体</strong>和<em>斜体</em>文本。</p>
<h2>列表</h2>
<ul>
<li>项目1</li>
<li>项目2</li>
<li>项目3</li>
</ul>
<h2>代码</h2>
<pre><code class="language-javascript">console.log('Hello World!')
</code></pre>
<h2>链接</h2>
<p><a href="https://example.com">链接文本</a></p>
`)

const markdownSource = ref('')

const handleMarkdownChange = (value) => {
  console.log('Markdown内容变化:', value)
}

// 模拟从HTML转换为Markdown源码
watch(markdownContent, (newValue) => {
  // 这里应该使用实际的HTML到Markdown转换逻辑
  markdownSource.value = convertHtmlToMarkdown(newValue)
}, { immediate: true })

const convertHtmlToMarkdown = (html) => {
  // 简化的转换示例
  return html
    .replace(/<h1>(.*?)<\/h1>/g, '# $1')
    .replace(/<h2>(.*?)<\/h2>/g, '## $1')
    .replace(/<strong>(.*?)<\/strong>/g, '**$1**')
    .replace(/<em>(.*?)<\/em>/g, '*$1*')
    .replace(/<p>(.*?)<\/p>/g, '$1\n')
    .replace(/<li>(.*?)<\/li>/g, '- $1')
    .replace(/<ul>|<\/ul>/g, '')
    .replace(/<pre><code[^>]*>(.*?)<\/code><\/pre>/gs, '```\n$1\n```')
    .replace(/<a href="([^"]*)"[^>]*>(.*?)<\/a>/g, '[$2]($1)')
}
</script>

<style scoped>
.markdown-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.markdown-source {
  background-color: #2d3748;
  color: #e2e8f0;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}
</style>
```

### 3. 双模式编辑器
```vue
<template>
  <div class="example-container">
    <h3>双模式编辑器</h3>
    <FuniEditor
      v-model="dualContent"
      :mode="currentMode"
      :showMode="true"
      :height="600"
      placeholder="请选择编辑模式并输入内容..."
      @change="handleDualModeChange"
    />
    
    <div class="mode-info">
      <h4>当前模式：{{ getModeText(currentMode) }}</h4>
      <div class="mode-controls">
        <el-button 
          type="primary" 
          :disabled="currentMode === '1'"
          @click="switchMode('1')"
        >
          切换到富文本模式
        </el-button>
        <el-button 
          type="success" 
          :disabled="currentMode === '2'"
          @click="switchMode('2')"
        >
          切换到Markdown模式
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const dualContent = ref('<h1>双模式编辑器</h1><p>支持富文本和Markdown两种编辑模式。</p>')
const currentMode = ref('1')

const getModeText = (mode) => {
  return mode === '1' ? '富文本编辑器' : 'Markdown编辑器'
}

const switchMode = (mode) => {
  currentMode.value = mode
}

const handleDualModeChange = (value) => {
  console.log('双模式内容变化:', value)
}
</script>

<style scoped>
.mode-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f0f9ff;
  border-radius: 4px;
  border-left: 4px solid #3b82f6;
}

.mode-controls {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}
</style>
```

## 高级示例

### 4. 自定义工具栏编辑器
```vue
<template>
  <div class="example-container">
    <h3>自定义工具栏编辑器</h3>
    
    <div class="toolbar-config">
      <h4>工具栏配置：</h4>
      <el-select v-model="selectedToolbar" placeholder="选择工具栏配置">
        <el-option 
          v-for="config in toolbarConfigs" 
          :key="config.name"
          :label="config.name" 
          :value="config.value"
        />
      </el-select>
    </div>
    
    <FuniEditor
      v-model="customContent"
      :mode="'1'"
      :showMode="false"
      :height="400"
      :toolbar="selectedToolbar"
      :plugins="customPlugins"
      placeholder="自定义工具栏编辑器..."
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const customContent = ref('<p>自定义工具栏编辑器示例</p>')

const selectedToolbar = ref('basic')

const toolbarConfigs = [
  {
    name: '基础工具栏',
    value: 'undo redo | bold italic | alignleft aligncenter alignright'
  },
  {
    name: '标准工具栏',
    value: 'undo redo | bold italic underline | alignleft aligncenter alignright | bullist numlist | link image'
  },
  {
    name: '完整工具栏',
    value: 'fullscreen undo redo | cut copy paste | forecolor backcolor bold italic underline strikethrough | alignleft aligncenter alignright | styleselect formatselect fontselect fontsizeselect | bullist numlist | blockquote subscript superscript | table image media | code preview'
  },
  {
    name: '简化工具栏',
    value: 'bold italic | link'
  }
]

const customPlugins = [
  'link', 'image', 'table', 'code', 'preview', 'fullscreen',
  'lists', 'textcolor', 'colorpicker', 'paste', 'searchreplace'
]
</script>

<style scoped>
.toolbar-config {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8fafc;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}
</style>
```

## 业务场景示例

### 6. 文章发布系统
```vue
<template>
  <div class="example-container">
    <h3>文章发布系统</h3>

    <el-form :model="articleForm" :rules="articleRules" ref="articleFormRef" label-width="120px">
      <el-form-item label="文章标题" prop="title">
        <el-input v-model="articleForm.title" placeholder="请输入文章标题" />
      </el-form-item>

      <el-form-item label="文章摘要" prop="summary">
        <el-input
          v-model="articleForm.summary"
          type="textarea"
          :rows="3"
          placeholder="请输入文章摘要"
        />
      </el-form-item>

      <el-form-item label="文章分类" prop="category">
        <el-select v-model="articleForm.category" placeholder="请选择分类">
          <el-option label="技术文章" value="tech" />
          <el-option label="产品介绍" value="product" />
          <el-option label="公司新闻" value="news" />
          <el-option label="行业动态" value="industry" />
        </el-select>
      </el-form-item>

      <el-form-item label="文章内容" prop="content">
        <FuniEditor
          v-model="articleForm.content"
          :mode="'1'"
          :showMode="true"
          :height="600"
          :uploadUrl="'/api/upload/article-image'"
          :srcPrefix="'/api/file/'"
          placeholder="请输入文章内容..."
          @change="handleContentChange"
        />
      </el-form-item>

      <el-form-item label="发布设置">
        <el-checkbox v-model="articleForm.isPublic">公开发布</el-checkbox>
        <el-checkbox v-model="articleForm.allowComment">允许评论</el-checkbox>
        <el-checkbox v-model="articleForm.isTop">置顶文章</el-checkbox>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="publishArticle">发布文章</el-button>
        <el-button @click="saveDraft">保存草稿</el-button>
        <el-button @click="previewArticle">预览文章</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 文章预览弹窗 -->
    <el-dialog v-model="previewVisible" title="文章预览" width="80%" top="5vh">
      <div class="article-preview">
        <h1>{{ articleForm.title }}</h1>
        <div class="article-meta">
          <span>分类：{{ getCategoryName(articleForm.category) }}</span>
          <span>字数：{{ getWordCount(articleForm.content) }}</span>
          <span>预计阅读时间：{{ getReadTime(articleForm.content) }}分钟</span>
        </div>
        <div class="article-summary">
          <strong>摘要：</strong>{{ articleForm.summary }}
        </div>
        <div class="article-content" v-html="articleForm.content"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const articleFormRef = ref()
const previewVisible = ref(false)

const articleForm = reactive({
  title: '',
  summary: '',
  category: '',
  content: '',
  isPublic: true,
  allowComment: true,
  isTop: false
})

const articleRules = {
  title: [
    { required: true, message: '请输入文章标题', trigger: 'blur' },
    { min: 5, max: 100, message: '标题长度在5到100个字符', trigger: 'blur' }
  ],
  summary: [
    { required: true, message: '请输入文章摘要', trigger: 'blur' },
    { min: 10, max: 200, message: '摘要长度在10到200个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择文章分类', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入文章内容', trigger: 'blur' },
    { min: 50, message: '文章内容不能少于50个字符', trigger: 'blur' }
  ]
}

const getCategoryName = (value) => {
  const categories = {
    tech: '技术文章',
    product: '产品介绍',
    news: '公司新闻',
    industry: '行业动态'
  }
  return categories[value] || value
}

const getWordCount = (content) => {
  // 移除HTML标签后计算字数
  const text = content.replace(/<[^>]*>/g, '')
  return text.length
}

const getReadTime = (content) => {
  const wordCount = getWordCount(content)
  return Math.ceil(wordCount / 300) // 假设每分钟阅读300字
}

const handleContentChange = (value) => {
  articleForm.content = value
}

const publishArticle = async () => {
  try {
    await articleFormRef.value.validate()

    const articleData = {
      ...articleForm,
      publishTime: new Date().toISOString(),
      wordCount: getWordCount(articleForm.content),
      readTime: getReadTime(articleForm.content)
    }

    console.log('发布文章:', articleData)
    ElMessage.success('文章发布成功')
  } catch (error) {
    ElMessage.error('请检查表单输入')
  }
}

const saveDraft = () => {
  const draftData = {
    ...articleForm,
    isDraft: true,
    saveTime: new Date().toISOString()
  }

  console.log('保存草稿:', draftData)
  ElMessage.success('草稿保存成功')
}

const previewArticle = () => {
  if (!articleForm.title || !articleForm.content) {
    ElMessage.warning('请先输入标题和内容')
    return
  }
  previewVisible.value = true
}

const resetForm = () => {
  articleFormRef.value.resetFields()
}
</script>

<style scoped>
.article-preview {
  padding: 20px;
}

.article-preview h1 {
  color: #333;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.article-meta {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  color: #666;
  font-size: 14px;
}

.article-summary {
  background-color: #f8fafc;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  border-left: 4px solid #3b82f6;
}

.article-content {
  line-height: 1.8;
  color: #333;
}
</style>
```

### 7. 评论系统
```vue
<template>
  <div class="example-container">
    <h3>评论系统</h3>

    <div class="comment-section">
      <!-- 发表评论 -->
      <div class="comment-form">
        <h4>发表评论</h4>
        <FuniEditor
          v-model="newComment"
          :mode="'1'"
          :showMode="false"
          :height="200"
          :toolbar="commentToolbar"
          :plugins="commentPlugins"
          placeholder="请输入您的评论..."
        />
        <div class="comment-actions">
          <el-button type="primary" @click="submitComment">发表评论</el-button>
          <el-button @click="clearComment">清空</el-button>
        </div>
      </div>

      <!-- 评论列表 -->
      <div class="comment-list">
        <h4>评论列表 ({{ comments.length }})</h4>
        <div v-for="comment in comments" :key="comment.id" class="comment-item">
          <div class="comment-header">
            <div class="user-info">
              <img :src="comment.avatar" :alt="comment.username" class="user-avatar" />
              <span class="username">{{ comment.username }}</span>
              <span class="comment-time">{{ comment.time }}</span>
            </div>
            <div class="comment-actions">
              <el-button type="text" size="small" @click="replyComment(comment)">回复</el-button>
              <el-button type="text" size="small" @click="likeComment(comment)">
                👍 {{ comment.likes }}
              </el-button>
            </div>
          </div>

          <div class="comment-content" v-html="comment.content"></div>

          <!-- 回复表单 -->
          <div v-if="replyingTo === comment.id" class="reply-form">
            <FuniEditor
              v-model="replyContent"
              :mode="'1'"
              :showMode="false"
              :height="150"
              :toolbar="replyToolbar"
              :placeholder="`回复 @${comment.username}...`"
            />
            <div class="reply-actions">
              <el-button type="primary" size="small" @click="submitReply(comment)">发表回复</el-button>
              <el-button size="small" @click="cancelReply">取消</el-button>
            </div>
          </div>

          <!-- 回复列表 -->
          <div v-if="comment.replies && comment.replies.length" class="replies">
            <div v-for="reply in comment.replies" :key="reply.id" class="reply-item">
              <div class="reply-header">
                <span class="username">{{ reply.username }}</span>
                <span class="reply-time">{{ reply.time }}</span>
              </div>
              <div class="reply-content" v-html="reply.content"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const newComment = ref('')
const replyContent = ref('')
const replyingTo = ref(null)

// 评论工具栏配置
const commentToolbar = 'bold italic | link | emoticons'
const commentPlugins = ['link', 'emoticons', 'paste']

// 回复工具栏配置
const replyToolbar = 'bold italic | link'

const comments = ref([
  {
    id: 1,
    username: '张三',
    avatar: 'https://via.placeholder.com/40',
    time: '2024-01-01 10:00',
    content: '<p>这篇文章写得很好，学到了很多东西！</p>',
    likes: 5,
    replies: [
      {
        id: 11,
        username: '李四',
        time: '2024-01-01 10:30',
        content: '<p>同感，作者的观点很有见地。</p>'
      }
    ]
  },
  {
    id: 2,
    username: '王五',
    avatar: 'https://via.placeholder.com/40',
    time: '2024-01-01 11:00',
    content: '<p>有个问题想请教一下，关于<strong>第三部分</strong>的内容...</p>',
    likes: 2,
    replies: []
  }
])

const submitComment = () => {
  if (!newComment.value.trim()) {
    ElMessage.warning('请输入评论内容')
    return
  }

  const comment = {
    id: Date.now(),
    username: '当前用户',
    avatar: 'https://via.placeholder.com/40',
    time: new Date().toLocaleString(),
    content: newComment.value,
    likes: 0,
    replies: []
  }

  comments.value.unshift(comment)
  newComment.value = ''
  ElMessage.success('评论发表成功')
}

const clearComment = () => {
  newComment.value = ''
}

const replyComment = (comment) => {
  replyingTo.value = comment.id
  replyContent.value = ''
}

const submitReply = (comment) => {
  if (!replyContent.value.trim()) {
    ElMessage.warning('请输入回复内容')
    return
  }

  const reply = {
    id: Date.now(),
    username: '当前用户',
    time: new Date().toLocaleString(),
    content: replyContent.value
  }

  if (!comment.replies) {
    comment.replies = []
  }
  comment.replies.push(reply)

  replyingTo.value = null
  replyContent.value = ''
  ElMessage.success('回复发表成功')
}

const cancelReply = () => {
  replyingTo.value = null
  replyContent.value = ''
}

const likeComment = (comment) => {
  comment.likes++
  ElMessage.success('点赞成功')
}
</script>

<style scoped>
.comment-section {
  max-width: 800px;
}

.comment-form {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8fafc;
  border-radius: 8px;
}

.comment-actions {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.comment-list {
  margin-top: 20px;
}

.comment-item {
  padding: 20px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 15px;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.username {
  font-weight: bold;
  color: #333;
}

.comment-time {
  color: #666;
  font-size: 12px;
}

.comment-content {
  margin: 10px 0;
  line-height: 1.6;
}

.reply-form {
  margin-top: 15px;
  padding: 15px;
  background-color: #f9fafb;
  border-radius: 4px;
}

.reply-actions {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.replies {
  margin-top: 15px;
  padding-left: 20px;
  border-left: 3px solid #e2e8f0;
}

.reply-item {
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.reply-item:last-child {
  border-bottom: none;
}

.reply-header {
  display: flex;
  gap: 10px;
  margin-bottom: 5px;
}

.reply-time {
  color: #666;
  font-size: 12px;
}

.reply-content {
  font-size: 14px;
  line-height: 1.5;
}
</style>
```

## 注意事项

### 使用建议
1. **模式选择**：根据用户群体选择合适的编辑模式
2. **工具栏配置**：根据使用场景配置合适的工具栏
3. **文件上传**：配置正确的上传接口和文件处理
4. **内容验证**：对用户输入的内容进行适当的验证和过滤

### 性能优化
1. **按需加载**：只加载需要的插件和功能
2. **内容缓存**：对编辑内容进行本地缓存
3. **图片优化**：对上传的图片进行压缩和优化
4. **懒加载**：大量内容时使用懒加载

### 安全考虑
1. **XSS防护**：对用户输入的HTML内容进行安全过滤
2. **文件上传安全**：限制上传文件类型和大小
3. **内容审核**：对发布的内容进行审核
4. **权限控制**：根据用户权限控制编辑功能

### 5. 带文件上传的编辑器
```vue
<template>
  <div class="example-container">
    <h3>带文件上传的编辑器</h3>
    
    <div class="upload-config">
      <h4>上传配置：</h4>
      <div class="config-item">
        <label>上传URL：</label>
        <el-input v-model="uploadConfig.url" placeholder="上传接口地址" />
      </div>
      <div class="config-item">
        <label>文件前缀：</label>
        <el-input v-model="uploadConfig.prefix" placeholder="文件URL前缀" />
      </div>
    </div>
    
    <FuniEditor
      v-model="uploadContent"
      :mode="'1'"
      :showMode="false"
      :height="500"
      :uploadUrl="uploadConfig.url"
      :srcPrefix="uploadConfig.prefix"
      :plugins="uploadPlugins"
      :toolbar="uploadToolbar"
      placeholder="支持图片和文件上传的编辑器..."
      @upload="handleUpload"
    />
    
    <div class="upload-log">
      <h4>上传日志：</h4>
      <div v-for="log in uploadLogs" :key="log.id" class="log-item">
        <span class="log-time">{{ log.time }}</span>
        <span class="log-message">{{ log.message }}</span>
        <span :class="['log-status', log.status]">{{ log.status }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const uploadContent = ref('<p>支持图片和文件上传的编辑器</p>')

const uploadConfig = ref({
  url: '/api/upload/file',
  prefix: '/api/file/download/'
})

const uploadPlugins = [
  'image', 'media', 'link', 'table', 'code', 'preview',
  'paste', 'searchreplace', 'fullscreen'
]

const uploadToolbar = 'undo redo | bold italic | alignleft aligncenter alignright | image media link | table | code preview fullscreen'

const uploadLogs = ref([
  {
    id: 1,
    time: '2024-01-01 10:00:00',
    message: '图片上传成功: image1.jpg',
    status: 'success'
  },
  {
    id: 2,
    time: '2024-01-01 10:05:00',
    message: '文件上传失败: 文件过大',
    status: 'error'
  }
])

const handleUpload = (file, success, failure) => {
  const logId = Date.now()
  
  // 模拟上传过程
  uploadLogs.value.unshift({
    id: logId,
    time: new Date().toLocaleString(),
    message: `开始上传: ${file.name}`,
    status: 'uploading'
  })
  
  // 模拟上传延迟
  setTimeout(() => {
    if (file.size > 5 * 1024 * 1024) { // 5MB限制
      uploadLogs.value[0] = {
        id: logId,
        time: new Date().toLocaleString(),
        message: `上传失败: ${file.name} (文件过大)`,
        status: 'error'
      }
      failure('文件大小不能超过5MB')
    } else {
      const url = URL.createObjectURL(file)
      uploadLogs.value[0] = {
        id: logId,
        time: new Date().toLocaleString(),
        message: `上传成功: ${file.name}`,
        status: 'success'
      }
      success(url)
    }
  }, 1000)
}
</script>

<style scoped>
.upload-config {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8fafc;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

.config-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.config-item label {
  width: 100px;
  text-align: right;
}

.upload-log {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9fafb;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  align-items: center;
  padding: 5px 0;
  border-bottom: 1px solid #e5e7eb;
  gap: 10px;
}

.log-time {
  font-size: 12px;
  color: #6b7280;
  width: 150px;
}

.log-message {
  flex: 1;
  font-size: 14px;
}

.log-status {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.log-status.success {
  background-color: #d1fae5;
  color: #065f46;
}

.log-status.error {
  background-color: #fee2e2;
  color: #991b1b;
}

.log-status.uploading {
  background-color: #dbeafe;
  color: #1e40af;
}
</style>
```
