# FuniEditor ElementPlus API 支持

## 基础组件说明

FuniEditor 是一个复合编辑器组件，根据不同的模式集成了不同的编辑器引擎：

### 编辑器引擎构成
- **TinyMCE**: 富文本编辑器（mode='1' 或 'wysiwyg'）
- **Vditor**: Markdown编辑器（mode='2' 或 'markdown'）
- **ElementPlus 组件**: 用于模式切换和界面控制

### 核心 ElementPlus 组件
- **el-radio-group**: 模式切换控制
- **el-radio**: 模式选项
- **el-button**: 操作按钮
- **el-loading**: 加载状态

## 支持的 ElementPlus API

### el-radio-group 相关 API

#### Props 透传支持
| ElementPlus API | 支持状态 | 透传方式 | 说明 |
|----------------|----------|----------|------|
| model-value | ✅ | 内部处理 | 当前选中的编辑模式 |
| size | ✅ | 直接透传 | 模式切换按钮尺寸 |
| disabled | ✅ | 直接透传 | 是否禁用模式切换 |
| text-color | ✅ | 直接透传 | 文字颜色 |
| fill | ✅ | 直接透传 | 填充颜色 |

#### Events 透传支持
| ElementPlus Event | 支持状态 | 透传方式 | 说明 |
|------------------|----------|----------|------|
| change | ✅ | 内部处理 | 模式切换事件 |

### el-radio 相关 API

#### Props 透传支持
| ElementPlus API | 支持状态 | 透传方式 | 说明 |
|----------------|----------|----------|------|
| label | ✅ | 内部处理 | 模式标识值 |
| disabled | ✅ | 直接透传 | 是否禁用特定模式 |
| border | ✅ | 直接透传 | 是否显示边框 |
| size | ✅ | 直接透传 | 按钮尺寸 |

### el-button 相关 API

#### Props 透传支持
| ElementPlus API | 支持状态 | 透传方式 | 说明 |
|----------------|----------|----------|------|
| size | ✅ | 直接透传 | 按钮尺寸 |
| type | ✅ | 直接透传 | 按钮类型 |
| plain | ✅ | 直接透传 | 是否朴素按钮 |
| round | ✅ | 直接透传 | 是否圆角按钮 |
| circle | ✅ | 直接透传 | 是否圆形按钮 |
| loading | ✅ | 直接透传 | 是否加载中状态 |
| disabled | ✅ | 直接透传 | 是否禁用 |
| icon | ✅ | 直接透传 | 图标组件 |

#### Events 透传支持
| ElementPlus Event | 支持状态 | 透传方式 | 说明 |
|------------------|----------|----------|------|
| click | ✅ | 直接透传 | 点击事件 |

### el-loading 相关 API

#### Props 透传支持
| ElementPlus API | 支持状态 | 透传方式 | 说明 |
|----------------|----------|----------|------|
| loading | ✅ | 内部处理 | 加载状态控制 |
| text | ✅ | 直接透传 | 加载文字 |
| spinner | ✅ | 直接透传 | 自定义加载图标 |
| background | ✅ | 直接透传 | 遮罩背景色 |

## 透传方式说明

### 1. 直接透传
组件属性直接传递给对应的 ElementPlus 组件。

```vue
<template>
  <FuniEditor
    v-model="content"
    :mode="'1'"
    :showMode="true"
    size="large"
    :disabled="false"
  />
</template>
```

### 2. 内部处理
组件内部处理后再传递给 ElementPlus 组件。

```vue
<template>
  <FuniEditor
    v-model="content"
    :mode="currentMode"
    @mode-change="handleModeChange"
  />
</template>

<script setup>
// mode 值会被内部处理为 el-radio-group 的 model-value
const handleModeChange = (mode) => {
  console.log('编辑模式切换:', mode)
}
</script>
```

### 3. 条件透传
根据组件状态决定是否透传某些属性。

```javascript
// 组件内部逻辑
const radioGroupProps = computed(() => ({
  modelValue: currentMode.value,
  size: props.size,
  disabled: props.disabled || props.readonly,
  // 只有在 showMode 为 true 时才显示
  style: { display: props.showMode ? 'block' : 'none' }
}))
```

## 使用示例

### 基础透传示例
```vue
<template>
  <div class="example-container">
    <h3>ElementPlus API 透传示例</h3>
    
    <!-- 基础配置 -->
    <FuniEditor
      v-model="content1"
      :mode="'1'"
      :showMode="true"
      size="default"
      @mode-change="handleModeChange"
    />
    
    <!-- 大尺寸配置 -->
    <FuniEditor
      v-model="content2"
      :mode="'2'"
      :showMode="true"
      size="large"
      :disabled="false"
    />
    
    <!-- 禁用状态 -->
    <FuniEditor
      v-model="content3"
      :mode="'1'"
      :showMode="true"
      :disabled="true"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const content1 = ref('')
const content2 = ref('')
const content3 = ref('<p>禁用状态的编辑器</p>')

const handleModeChange = (mode) => {
  console.log('模式切换:', mode)
}
</script>
```

### 自定义样式示例
```vue
<template>
  <div class="example-container">
    <h3>自定义样式示例</h3>
    
    <!-- 自定义模式切换样式 -->
    <FuniEditor
      v-model="styledContent"
      :mode="'1'"
      :showMode="true"
      class="custom-editor"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const styledContent = ref('')
</script>

<style scoped>
.custom-editor {
  border: 2px solid #3b82f6;
  border-radius: 8px;
  padding: 10px;
}

/* 自定义模式切换按钮样式 */
.custom-editor :deep(.el-radio-group) {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8fafc;
  border-radius: 6px;
}

.custom-editor :deep(.el-radio-button__inner) {
  border-radius: 4px;
  margin: 0 2px;
}

.custom-editor :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
  background-color: #3b82f6;
  border-color: #3b82f6;
}
</style>
```

### 动态配置示例
```vue
<template>
  <div class="example-container">
    <h3>动态配置示例</h3>
    
    <div class="controls">
      <el-form :model="editorConfig" label-width="120px">
        <el-form-item label="编辑器尺寸">
          <el-radio-group v-model="editorConfig.size">
            <el-radio label="small">小</el-radio>
            <el-radio label="default">默认</el-radio>
            <el-radio label="large">大</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="显示模式切换">
          <el-switch v-model="editorConfig.showMode" />
        </el-form-item>
        
        <el-form-item label="禁用状态">
          <el-switch v-model="editorConfig.disabled" />
        </el-form-item>
        
        <el-form-item label="只读状态">
          <el-switch v-model="editorConfig.readonly" />
        </el-form-item>
      </el-form>
    </div>
    
    <FuniEditor
      v-model="dynamicContent"
      :mode="editorConfig.mode"
      :showMode="editorConfig.showMode"
      :size="editorConfig.size"
      :disabled="editorConfig.disabled"
      :readonly="editorConfig.readonly"
      :height="400"
      placeholder="动态配置编辑器..."
    />
    
    <div class="config-display">
      <h4>当前配置：</h4>
      <pre>{{ JSON.stringify(editorConfig, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const dynamicContent = ref('<p>动态配置编辑器示例</p>')

const editorConfig = reactive({
  mode: '1',
  showMode: true,
  size: 'default',
  disabled: false,
  readonly: false
})
</script>

<style scoped>
.controls {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8fafc;
  border-radius: 8px;
}

.config-display {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9fafb;
  border-radius: 4px;
}

.config-display pre {
  background-color: #2d3748;
  color: #e2e8f0;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
```

### 事件处理示例
```vue
<template>
  <div class="example-container">
    <h3>事件处理示例</h3>
    
    <FuniEditor
      v-model="eventContent"
      :mode="currentMode"
      :showMode="true"
      @mode-change="handleModeChange"
      @change="handleContentChange"
      @focus="handleFocus"
      @blur="handleBlur"
    />
    
    <div class="event-log">
      <h4>事件日志：</h4>
      <div class="log-list">
        <div v-for="log in eventLogs" :key="log.id" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-event">{{ log.event }}</span>
          <span class="log-data">{{ log.data }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const eventContent = ref('<p>事件处理示例</p>')
const currentMode = ref('1')
const eventLogs = ref([])

const addLog = (event, data) => {
  eventLogs.value.unshift({
    id: Date.now(),
    time: new Date().toLocaleTimeString(),
    event,
    data: JSON.stringify(data)
  })
  
  // 保持日志数量在合理范围内
  if (eventLogs.value.length > 10) {
    eventLogs.value = eventLogs.value.slice(0, 10)
  }
}

const handleModeChange = (mode) => {
  currentMode.value = mode
  addLog('mode-change', { mode })
}

const handleContentChange = (content) => {
  addLog('content-change', { length: content.length })
}

const handleFocus = () => {
  addLog('focus', {})
}

const handleBlur = () => {
  addLog('blur', {})
}
</script>

<style scoped>
.event-log {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8fafc;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}

.log-list {
  font-family: monospace;
  font-size: 12px;
}

.log-item {
  display: flex;
  gap: 10px;
  padding: 2px 0;
  border-bottom: 1px solid #e2e8f0;
}

.log-time {
  color: #666;
  width: 80px;
}

.log-event {
  color: #3b82f6;
  width: 120px;
  font-weight: bold;
}

.log-data {
  color: #059669;
  flex: 1;
}
</style>
```

## 注意事项

### 1. API 兼容性
- 确保使用的 ElementPlus 版本支持相应的 API
- 建议使用 ElementPlus 2.0+ 版本以获得最佳兼容性
- 某些新增的 API 可能在旧版本中不可用

### 2. 样式定制
- 可以通过 CSS 深度选择器定制 ElementPlus 组件样式
- 注意样式优先级，避免样式冲突
- 建议使用 CSS 变量进行主题定制

### 3. 事件处理
- 模式切换事件会被组件内部处理
- 建议使用组件提供的自定义事件
- 复杂的事件处理逻辑建议在组件外部实现

### 4. 性能考虑
- 避免频繁切换编辑模式
- 合理使用事件监听，避免内存泄漏
- 大内容时注意编辑器性能

## 版本兼容性

| FuniEditor 版本 | ElementPlus 版本 | 兼容性 |
|----------------|-----------------|--------|
| 1.0.x | 2.0.x | ✅ 完全兼容 |
| 1.0.x | 2.1.x | ✅ 完全兼容 |
| 1.0.x | 2.2.x | ✅ 完全兼容 |
| 1.0.x | 2.3.x | ✅ 完全兼容 |
| 1.0.x | 1.x.x | ⚠️ 部分兼容 |
