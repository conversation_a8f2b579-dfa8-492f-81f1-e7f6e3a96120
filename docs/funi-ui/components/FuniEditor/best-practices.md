# FuniEditor 最佳实践

## 推荐用法和配置

### 1. 编辑模式选择最佳实践

#### 根据用户群体选择模式
```vue
<template>
  <!-- ✅ 推荐：为一般用户提供富文本编辑器 -->
  <FuniEditor
    v-model="generalContent"
    :mode="'1'"
    :showMode="false"
    :height="400"
    placeholder="请输入内容..."
  />
  
  <!-- ✅ 推荐：为技术用户提供Markdown编辑器 -->
  <FuniEditor
    v-model="techContent"
    :mode="'2'"
    :showMode="false"
    :height="500"
    placeholder="请输入Markdown内容..."
  />
  
  <!-- ✅ 推荐：为混合用户群体提供双模式 -->
  <FuniEditor
    v-model="mixedContent"
    :mode="userPreferredMode"
    :showMode="true"
    :height="600"
    placeholder="请选择编辑模式..."
  />
  
  <!-- ❌ 不推荐：没有考虑用户需求的默认配置 -->
  <FuniEditor v-model="content" />
</template>

<script setup>
import { ref, computed } from 'vue'
import { useUserProfile } from '@/composables/useUserProfile'

const { userProfile } = useUserProfile()

// ✅ 推荐：根据用户偏好设置默认模式
const userPreferredMode = computed(() => {
  return userProfile.value.preferMarkdown ? '2' : '1'
})

const generalContent = ref('')
const techContent = ref('')
const mixedContent = ref('')
</script>
```

#### 场景化配置
```vue
<template>
  <div class="editor-scenarios">
    <!-- ✅ 推荐：文章编辑场景 -->
    <div class="article-editor">
      <h3>文章编辑</h3>
      <FuniEditor
        v-model="articleContent"
        :mode="'1'"
        :showMode="true"
        :height="600"
        :plugins="articlePlugins"
        :toolbar="articleToolbar"
        :uploadUrl="'/api/upload/article'"
        placeholder="请输入文章内容..."
      />
    </div>
    
    <!-- ✅ 推荐：评论编辑场景 -->
    <div class="comment-editor">
      <h3>评论编辑</h3>
      <FuniEditor
        v-model="commentContent"
        :mode="'1'"
        :showMode="false"
        :height="200"
        :toolbar="commentToolbar"
        :plugins="commentPlugins"
        placeholder="请输入评论..."
      />
    </div>
    
    <!-- ✅ 推荐：技术文档场景 -->
    <div class="doc-editor">
      <h3>技术文档</h3>
      <FuniEditor
        v-model="docContent"
        :mode="'2'"
        :showMode="true"
        :height="700"
        placeholder="请输入技术文档..."
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const articleContent = ref('')
const commentContent = ref('')
const docContent = ref('')

// ✅ 推荐：文章编辑器配置
const articlePlugins = [
  'image', 'media', 'link', 'table', 'code', 'preview',
  'fullscreen', 'searchreplace', 'wordcount', 'autosave'
]

const articleToolbar = [
  'fullscreen undo redo | cut copy paste',
  'bold italic underline strikethrough | alignleft aligncenter alignright',
  'styleselect formatselect fontselect fontsizeselect',
  'bullist numlist | blockquote | table image media link',
  'code preview | searchreplace wordcount'
]

// ✅ 推荐：评论编辑器配置
const commentPlugins = ['link', 'emoticons', 'paste']
const commentToolbar = 'bold italic | link | emoticons'
</script>
```

### 2. 工具栏配置最佳实践

#### 渐进式工具栏配置
```javascript
// ✅ 推荐：基础工具栏（适用于简单编辑）
const basicToolbar = 'undo redo | bold italic | alignleft aligncenter alignright'

// ✅ 推荐：标准工具栏（适用于一般编辑）
const standardToolbar = [
  'undo redo | bold italic underline',
  'alignleft aligncenter alignright | bullist numlist',
  'link image | removeformat'
]

// ✅ 推荐：完整工具栏（适用于专业编辑）
const fullToolbar = [
  'fullscreen undo redo restoredraft | cut copy paste pastetext',
  'forecolor backcolor bold italic underline strikethrough link',
  'alignleft aligncenter alignright alignjustify outdent indent',
  'styleselect formatselect fontselect fontsizeselect',
  'bullist numlist | blockquote subscript superscript removeformat',
  'table image media charmap emoticons hr pagebreak insertdatetime',
  'print preview | code selectall searchreplace wordcount'
]

// ❌ 避免：过于复杂的工具栏配置
const overComplexToolbar = 'undo redo restoredraft cut copy paste pastetext bold italic underline strikethrough subscript superscript forecolor backcolor link unlink anchor image media insertdatetime preview print hr pagebreak spellchecker searchreplace visualblocks visualchars code fullscreen insertdatetime media table emoticons template codesample'
```

#### 动态工具栏配置
```vue
<template>
  <div class="dynamic-toolbar-example">
    <!-- ✅ 推荐：根据权限动态配置工具栏 -->
    <FuniEditor
      v-model="content"
      :mode="'1'"
      :toolbar="dynamicToolbar"
      :plugins="dynamicPlugins"
      :height="500"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useUserPermissions } from '@/composables/usePermissions'

const content = ref('')
const { hasPermission } = useUserPermissions()

// ✅ 推荐：基于权限的动态工具栏
const dynamicToolbar = computed(() => {
  const baseToolbar = ['undo redo', 'bold italic', 'alignleft aligncenter alignright']
  
  if (hasPermission('editor.advanced')) {
    baseToolbar.push('styleselect formatselect')
  }
  
  if (hasPermission('editor.media')) {
    baseToolbar.push('image media link')
  }
  
  if (hasPermission('editor.table')) {
    baseToolbar.push('table')
  }
  
  if (hasPermission('editor.code')) {
    baseToolbar.push('code preview')
  }
  
  return baseToolbar.join(' | ')
})

const dynamicPlugins = computed(() => {
  const basePlugins = ['paste', 'searchreplace']
  
  if (hasPermission('editor.media')) {
    basePlugins.push('image', 'media', 'link')
  }
  
  if (hasPermission('editor.table')) {
    basePlugins.push('table')
  }
  
  if (hasPermission('editor.code')) {
    basePlugins.push('code', 'preview')
  }
  
  return basePlugins
})
</script>
```

### 3. 文件上传最佳实践

#### 安全的文件上传配置
```vue
<template>
  <div class="upload-example">
    <!-- ✅ 推荐：安全的文件上传配置 -->
    <FuniEditor
      v-model="uploadContent"
      :mode="'1'"
      :uploadUrl="uploadConfig.url"
      :srcPrefix="uploadConfig.prefix"
      @upload="handleSecureUpload"
      @upload-error="handleUploadError"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const uploadContent = ref('')

const uploadConfig = reactive({
  url: '/api/upload/editor-file',
  prefix: '/api/file/download/',
  maxSize: 5 * 1024 * 1024, // 5MB
  allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp']
})

// ✅ 推荐：安全的文件上传处理
const handleSecureUpload = async (file, success, failure) => {
  try {
    // 文件类型验证
    if (!uploadConfig.allowedTypes.includes(file.type)) {
      throw new Error('不支持的文件类型')
    }
    
    // 文件大小验证
    if (file.size > uploadConfig.maxSize) {
      throw new Error('文件大小超过限制')
    }
    
    // 文件扩展名验证
    const extension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
    if (!uploadConfig.allowedExtensions.includes(extension)) {
      throw new Error('不支持的文件扩展名')
    }
    
    // 创建FormData
    const formData = new FormData()
    formData.append('file', file)
    formData.append('type', 'editor-image')
    
    // 上传文件
    const response = await fetch(uploadConfig.url, {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`
      }
    })
    
    if (!response.ok) {
      throw new Error('上传失败')
    }
    
    const result = await response.json()
    
    if (result.success) {
      const fileUrl = uploadConfig.prefix + result.data.filename
      success(fileUrl)
      ElMessage.success('文件上传成功')
    } else {
      throw new Error(result.message || '上传失败')
    }
    
  } catch (error) {
    console.error('文件上传错误:', error)
    failure(error.message)
    ElMessage.error(error.message)
  }
}

const handleUploadError = (error) => {
  console.error('上传错误:', error)
  ElMessage.error('文件上传失败')
}

const getAuthToken = () => {
  return localStorage.getItem('auth_token') || ''
}
</script>
```

#### 上传进度和状态管理
```vue
<template>
  <div class="upload-progress-example">
    <FuniEditor
      v-model="progressContent"
      :mode="'1'"
      :uploadUrl="'/api/upload/file'"
      @upload="handleUploadWithProgress"
    />
    
    <!-- ✅ 推荐：显示上传进度 -->
    <div v-if="uploadProgress.show" class="upload-progress">
      <div class="progress-info">
        <span>{{ uploadProgress.filename }}</span>
        <span>{{ uploadProgress.percent }}%</span>
      </div>
      <el-progress 
        :percentage="uploadProgress.percent" 
        :status="uploadProgress.status"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const progressContent = ref('')

const uploadProgress = reactive({
  show: false,
  filename: '',
  percent: 0,
  status: 'success'
})

// ✅ 推荐：带进度的文件上传
const handleUploadWithProgress = (file, success, failure) => {
  uploadProgress.show = true
  uploadProgress.filename = file.name
  uploadProgress.percent = 0
  uploadProgress.status = 'success'
  
  const xhr = new XMLHttpRequest()
  const formData = new FormData()
  formData.append('file', file)
  
  // 上传进度监听
  xhr.upload.addEventListener('progress', (event) => {
    if (event.lengthComputable) {
      uploadProgress.percent = Math.round((event.loaded / event.total) * 100)
    }
  })
  
  // 上传完成监听
  xhr.addEventListener('load', () => {
    uploadProgress.show = false
    
    if (xhr.status === 200) {
      try {
        const response = JSON.parse(xhr.responseText)
        if (response.success) {
          success(response.data.url)
        } else {
          failure(response.message)
        }
      } catch (error) {
        failure('响应解析失败')
      }
    } else {
      failure('上传失败')
    }
  })
  
  // 上传错误监听
  xhr.addEventListener('error', () => {
    uploadProgress.show = false
    uploadProgress.status = 'exception'
    failure('网络错误')
  })
  
  xhr.open('POST', '/api/upload/file')
  xhr.send(formData)
}
</script>

<style scoped>
.upload-progress {
  margin-top: 10px;
  padding: 15px;
  background-color: #f8fafc;
  border-radius: 4px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}
</style>
```

### 4. 内容处理最佳实践

#### 内容验证和过滤
```javascript
// ✅ 推荐：内容安全过滤
const sanitizeContent = (content) => {
  // 移除危险的脚本标签
  content = content.replace(/<script[^>]*>.*?<\/script>/gi, '')
  
  // 移除事件处理器
  content = content.replace(/on\w+="[^"]*"/gi, '')
  content = content.replace(/on\w+='[^']*'/gi, '')
  
  // 移除危险的链接
  content = content.replace(/javascript:/gi, '')
  
  // 移除iframe（可选）
  content = content.replace(/<iframe[^>]*>.*?<\/iframe>/gi, '')
  
  return content
}

// ✅ 推荐：内容长度验证
const validateContentLength = (content, minLength = 10, maxLength = 50000) => {
  const textContent = content.replace(/<[^>]*>/g, '').trim()
  
  if (textContent.length < minLength) {
    throw new Error(`内容长度不能少于${minLength}个字符`)
  }
  
  if (textContent.length > maxLength) {
    throw new Error(`内容长度不能超过${maxLength}个字符`)
  }
  
  return true
}

// ✅ 推荐：内容格式化
const formatContent = (content) => {
  // 统一换行符
  content = content.replace(/\r\n/g, '\n').replace(/\r/g, '\n')
  
  // 移除多余的空白
  content = content.replace(/\n\s*\n\s*\n/g, '\n\n')
  
  // 格式化HTML
  content = content.replace(/>\s+</g, '><')
  
  return content
}
```

#### 自动保存和恢复
```vue
<template>
  <div class="autosave-example">
    <!-- ✅ 推荐：带自动保存的编辑器 -->
    <FuniEditor
      v-model="autosaveContent"
      :mode="'1'"
      :height="500"
      @change="handleContentChange"
      @blur="handleBlur"
    />

    <div class="autosave-status">
      <span :class="['status-indicator', saveStatus.type]">
        {{ saveStatus.message }}
      </span>
      <span class="last-save-time">
        最后保存：{{ lastSaveTime }}
      </span>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { debounce } from 'lodash-es'

const autosaveContent = ref('')
const lastSaveTime = ref('')

const saveStatus = reactive({
  type: 'saved', // 'saving', 'saved', 'error'
  message: '已保存'
})

// ✅ 推荐：防抖的自动保存
const debouncedSave = debounce(async (content) => {
  if (!content.trim()) return

  saveStatus.type = 'saving'
  saveStatus.message = '保存中...'

  try {
    await saveToServer(content)
    saveToLocal(content)

    saveStatus.type = 'saved'
    saveStatus.message = '已保存'
    lastSaveTime.value = new Date().toLocaleTimeString()
  } catch (error) {
    saveStatus.type = 'error'
    saveStatus.message = '保存失败'
    console.error('自动保存失败:', error)
  }
}, 2000)

const handleContentChange = (content) => {
  debouncedSave(content)
}

const handleBlur = () => {
  // 失焦时立即保存
  if (autosaveContent.value.trim()) {
    debouncedSave.flush()
  }
}

// ✅ 推荐：本地存储备份
const saveToLocal = (content) => {
  const key = `editor_backup_${getCurrentPageId()}`
  localStorage.setItem(key, JSON.stringify({
    content,
    timestamp: Date.now()
  }))
}

const loadFromLocal = () => {
  const key = `editor_backup_${getCurrentPageId()}`
  const backup = localStorage.getItem(key)

  if (backup) {
    try {
      const data = JSON.parse(backup)
      // 只恢复24小时内的备份
      if (Date.now() - data.timestamp < 24 * 60 * 60 * 1000) {
        return data.content
      }
    } catch (error) {
      console.error('恢复本地备份失败:', error)
    }
  }

  return ''
}

const saveToServer = async (content) => {
  const response = await fetch('/api/editor/autosave', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${getAuthToken()}`
    },
    body: JSON.stringify({
      content,
      pageId: getCurrentPageId()
    })
  })

  if (!response.ok) {
    throw new Error('服务器保存失败')
  }
}

const getCurrentPageId = () => {
  return window.location.pathname
}

const getAuthToken = () => {
  return localStorage.getItem('auth_token') || ''
}

// 组件挂载时恢复内容
onMounted(() => {
  const localContent = loadFromLocal()
  if (localContent && !autosaveContent.value) {
    autosaveContent.value = localContent
  }
})

// 组件卸载时清理
onUnmounted(() => {
  debouncedSave.cancel()
})
</script>

<style scoped>
.autosave-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
  padding: 8px 12px;
  background-color: #f8fafc;
  border-radius: 4px;
  font-size: 12px;
}

.status-indicator {
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: bold;
}

.status-indicator.saving {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-indicator.saved {
  background-color: #d1fae5;
  color: #065f46;
}

.status-indicator.error {
  background-color: #fee2e2;
  color: #991b1b;
}

.last-save-time {
  color: #6b7280;
}
</style>
```

## 避免的用法和常见错误

### 1. 配置错误

```vue
<!-- ❌ 错误：没有考虑用户体验的配置 -->
<FuniEditor
  v-model="content"
  :height="100"
  :toolbar="false"
  placeholder=""
/>

<!-- ✅ 正确：用户友好的配置 -->
<FuniEditor
  v-model="content"
  :height="400"
  :toolbar="'undo redo | bold italic | link'"
  placeholder="请输入内容..."
/>
```

### 2. 性能问题

```javascript
// ❌ 错误：没有防抖的内容处理
const handleContentChange = (content) => {
  // 每次变化都处理，可能导致性能问题
  processContent(content)
  saveToServer(content)
}

// ✅ 正确：使用防抖优化
const debouncedHandler = debounce((content) => {
  processContent(content)
  saveToServer(content)
}, 1000)
```

### 3. 安全问题

```javascript
// ❌ 错误：直接使用用户输入的内容
const saveContent = (content) => {
  // 直接保存，可能包含恶意脚本
  database.save(content)
}

// ✅ 正确：内容安全过滤
const saveContent = (content) => {
  const sanitizedContent = sanitizeContent(content)
  database.save(sanitizedContent)
}
```

## 总结

### 核心原则
1. **用户体验优先**：根据用户群体选择合适的编辑模式和工具栏
2. **性能考虑**：使用懒加载、防抖和缓存优化性能
3. **安全第一**：对用户输入进行验证和过滤
4. **渐进增强**：提供基础功能，按需添加高级功能
5. **可维护性**：保持配置的一致性和可读性

### 开发建议
1. 在项目初期制定编辑器使用规范
2. 根据实际需求选择合适的插件和工具栏
3. 实现完善的错误处理和用户反馈
4. 定期review编辑器配置，优化用户体验
5. 建立内容安全和审核机制
