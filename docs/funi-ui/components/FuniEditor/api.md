# FuniEditor API文档

## 组件概述

FuniEditor是基于TinyMCE或Vditor封装的富文本编辑器组件，支持多种编辑模式，提供了丰富的编辑功能和自定义配置，适用于内容编辑场景。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| modelValue | String | '' | - | 编辑器内容 |
| mode | String | 'wysiwyg' | - | 编辑模式：'wysiwyg'、'markdown'、'ir' |
| height | String/Number | '400px' | - | 编辑器高度 |
| width | String/Number | '100%' | - | 编辑器宽度 |
| placeholder | String | '请输入内容...' | - | 占位符文本 |
| disabled | Boolean | false | - | 是否禁用 |
| readonly | Boolean | false | - | 是否只读 |
| toolbar | Array/String | 'default' | - | 工具栏配置 |
| plugins | Array | [] | - | 插件配置 |
| menubar | Boolean/String | true | - | 菜单栏配置 |
| statusbar | Boolean | true | - | 状态栏显示 |
| resize | Boolean/String | true | - | 是否可调整大小 |
| branding | Boolean | false | - | 是否显示品牌信息 |
| elementpath | Boolean | true | - | 是否显示元素路径 |
| wordcount | Boolean | true | - | 是否显示字数统计 |
| autosave | Boolean | false | - | 是否自动保存 |
| autosaveInterval | Number | 30000 | - | 自动保存间隔（毫秒） |
| imageUpload | Function | - | - | 图片上传处理函数 |
| fileUpload | Function | - | - | 文件上传处理函数 |
| baseUrl | String | '' | - | 基础URL |
| language | String | 'zh_CN' | - | 语言设置 |
| theme | String | 'silver' | - | 主题设置 |
| skin | String | 'oxide' | - | 皮肤设置 |
| contentCss | Array/String | [] | - | 内容样式 |
| contentStyle | String | '' | - | 内容样式字符串 |
| formats | Object | {} | - | 自定义格式 |
| styleFormats | Array | [] | - | 样式格式 |
| fontFormats | String | '' | - | 字体格式 |
| fontSizes | String | '' | - | 字体大小 |
| lineHeights | String | '' | - | 行高设置 |
| indentUseMargin | Boolean | false | - | 缩进使用margin |
| tabSize | Number | 4 | - | Tab大小 |
| maxLength | Number | 0 | - | 最大长度限制 |
| minLength | Number | 0 | - | 最小长度限制 |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| update:modelValue | content: String | 内容更新事件 | 编辑器内容变化时 |
| change | content: String | 内容变化事件 | 编辑器内容变化时 |
| input | content: String | 输入事件 | 用户输入时 |
| focus | event: Event | 获得焦点事件 | 编辑器获得焦点时 |
| blur | event: Event | 失去焦点事件 | 编辑器失去焦点时 |
| init | editor: Object | 初始化完成事件 | 编辑器初始化完成时 |
| setup | editor: Object | 设置事件 | 编辑器设置时 |
| beforeinput | event: Event | 输入前事件 | 输入前触发 |
| keydown | event: Event | 按键按下事件 | 按键按下时 |
| keyup | event: Event | 按键抬起事件 | 按键抬起时 |
| paste | event: Event | 粘贴事件 | 粘贴内容时 |
| cut | event: Event | 剪切事件 | 剪切内容时 |
| copy | event: Event | 复制事件 | 复制内容时 |
| selectionchange | event: Event | 选择变化事件 | 选择内容变化时 |
| nodechange | event: Event | 节点变化事件 | 当前节点变化时 |
| undo | event: Event | 撤销事件 | 执行撤销操作时 |
| redo | event: Event | 重做事件 | 执行重做操作时 |
| save | content: String | 保存事件 | 执行保存操作时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| getContent | (format?: String) | String | 获取编辑器内容 |
| setContent | (content: String, format?: String) | void | 设置编辑器内容 |
| insertContent | (content: String) | void | 插入内容 |
| focus | - | void | 使编辑器获得焦点 |
| blur | - | void | 使编辑器失去焦点 |
| save | - | void | 保存内容 |
| undo | - | void | 撤销操作 |
| redo | - | void | 重做操作 |
| execCommand | (command: String, ui?: Boolean, value?: Any) | Boolean | 执行命令 |
| queryCommandState | (command: String) | Boolean | 查询命令状态 |
| queryCommandValue | (command: String) | String | 查询命令值 |
| getSelectedContent | (format?: String) | String | 获取选中内容 |
| selection.getContent | (format?: String) | String | 获取选择内容 |
| selection.setContent | (content: String, format?: String) | void | 设置选择内容 |
| selection.select | (node: Element) | void | 选择节点 |
| selection.collapse | (toStart?: Boolean) | void | 折叠选择 |
| dom.getParent | (node: Element, selector?: String) | Element | 获取父节点 |
| dom.select | (selector: String, scope?: Element) | Array | 选择元素 |
| windowManager.open | (config: Object) | void | 打开窗口 |
| windowManager.close | - | void | 关闭窗口 |

## 工具栏配置

### 默认工具栏
```javascript
const defaultToolbar = [
  'undo redo | formatselect | bold italic underline strikethrough | forecolor backcolor',
  'alignleft aligncenter alignright alignjustify | bullist numlist outdent indent',
  'link image media table | code preview | fullscreen'
]
```

### 自定义工具栏
```javascript
const customToolbar = [
  'bold italic underline | alignleft aligncenter alignright',
  'bullist numlist | link image | code'
]
```

### 工具栏按钮说明
- **格式**: formatselect, fontselect, fontsizeselect
- **文本**: bold, italic, underline, strikethrough, superscript, subscript
- **颜色**: forecolor, backcolor
- **对齐**: alignleft, aligncenter, alignright, alignjustify
- **列表**: bullist, numlist, outdent, indent
- **插入**: link, image, media, table, hr, pagebreak, anchor
- **编辑**: undo, redo, cut, copy, paste, selectall, searchreplace
- **视图**: code, preview, fullscreen, visualblocks, visualchars
- **其他**: removeformat, cleanup, help

## 使用示例

### 基础编辑器
```vue
<template>
  <FuniEditor
    v-model="content"
    height="400px"
    placeholder="请输入文章内容..."
    @change="handleChange"
  />
</template>

<script setup>
import { ref } from 'vue'

const content = ref('')

const handleChange = (newContent) => {
  console.log('内容变化:', newContent)
}
</script>
```

### 自定义工具栏
```vue
<template>
  <FuniEditor
    v-model="content"
    :toolbar="customToolbar"
    :plugins="plugins"
    height="500px"
  />
</template>

<script setup>
import { ref } from 'vue'

const content = ref('')

const customToolbar = [
  'undo redo | formatselect fontselect fontsizeselect',
  'bold italic underline strikethrough | forecolor backcolor',
  'alignleft aligncenter alignright alignjustify',
  'bullist numlist outdent indent | link image table',
  'code preview fullscreen'
]

const plugins = [
  'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
  'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
  'insertdatetime', 'media', 'table', 'help', 'wordcount'
]
</script>
```

### 图片上传配置
```vue
<template>
  <FuniEditor
    v-model="content"
    :image-upload="handleImageUpload"
    :file-upload="handleFileUpload"
  />
</template>

<script setup>
import { ref } from 'vue'

const content = ref('')

const handleImageUpload = async (file) => {
  const formData = new FormData()
  formData.append('file', file)
  
  try {
    const response = await fetch('/api/upload/image', {
      method: 'POST',
      body: formData
    })
    
    const result = await response.json()
    
    if (result.success) {
      return {
        url: result.data.url,
        alt: file.name
      }
    } else {
      throw new Error(result.message)
    }
  } catch (error) {
    console.error('图片上传失败:', error)
    throw error
  }
}

const handleFileUpload = async (file) => {
  const formData = new FormData()
  formData.append('file', file)
  
  try {
    const response = await fetch('/api/upload/file', {
      method: 'POST',
      body: formData
    })
    
    const result = await response.json()
    
    if (result.success) {
      return {
        url: result.data.url,
        title: file.name
      }
    } else {
      throw new Error(result.message)
    }
  } catch (error) {
    console.error('文件上传失败:', error)
    throw error
  }
}
</script>
```

### Markdown模式
```vue
<template>
  <FuniEditor
    v-model="markdownContent"
    mode="markdown"
    height="600px"
    :toolbar="markdownToolbar"
  />
</template>

<script setup>
import { ref } from 'vue'

const markdownContent = ref(`# 标题

这是一段**粗体**文本和*斜体*文本。

## 列表

- 项目1
- 项目2
- 项目3

## 代码

\`\`\`javascript
console.log('Hello World!')
\`\`\`

## 链接

[链接文本](https://example.com)
`)

const markdownToolbar = [
  'headings', 'bold', 'italic', 'strike', 'link', 'list', 'ordered-list',
  'check', 'outdent', 'indent', 'quote', 'line', 'code', 'inline-code',
  'insert-before', 'insert-after', 'table', 'undo', 'redo', 'upload',
  'record', 'edit-mode', 'both', 'preview', 'fullscreen', 'outline'
]
</script>
```

### 只读模式
```vue
<template>
  <FuniEditor
    v-model="content"
    readonly
    :toolbar="false"
    :menubar="false"
    :statusbar="false"
    height="300px"
  />
</template>

<script setup>
import { ref } from 'vue'

const content = ref(`
<h2>文章标题</h2>
<p>这是一篇只读的文章内容，用户无法编辑。</p>
<p>可以用于展示已发布的内容。</p>
`)
</script>
```

### 自动保存
```vue
<template>
  <div>
    <div class="save-status">
      {{ saveStatus }}
    </div>
    <FuniEditor
      v-model="content"
      :autosave="true"
      :autosave-interval="10000"
      @save="handleSave"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const content = ref('')
const saveStatus = ref('已保存')

const handleSave = async (content) => {
  saveStatus.value = '保存中...'
  
  try {
    await saveToServer(content)
    saveStatus.value = '已保存'
  } catch (error) {
    saveStatus.value = '保存失败'
    console.error('保存失败:', error)
  }
}

const saveToServer = async (content) => {
  const response = await fetch('/api/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ content })
  })
  
  if (!response.ok) {
    throw new Error('保存失败')
  }
  
  return response.json()
}
</script>

<style scoped>
.save-status {
  text-align: right;
  padding: 8px;
  font-size: 12px;
  color: #666;
}
</style>
```

## 插件配置

### 常用插件
- **advlist**: 高级列表
- **autolink**: 自动链接
- **lists**: 列表
- **link**: 链接
- **image**: 图片
- **charmap**: 字符映射
- **preview**: 预览
- **anchor**: 锚点
- **searchreplace**: 搜索替换
- **visualblocks**: 可视化块
- **code**: 代码
- **fullscreen**: 全屏
- **insertdatetime**: 插入日期时间
- **media**: 媒体
- **table**: 表格
- **help**: 帮助
- **wordcount**: 字数统计

### 高级插件
- **autosave**: 自动保存
- **save**: 保存
- **directionality**: 文本方向
- **emoticons**: 表情符号
- **template**: 模板
- **paste**: 粘贴
- **textcolor**: 文本颜色
- **colorpicker**: 颜色选择器
- **textpattern**: 文本模式
- **noneditable**: 不可编辑
- **pagebreak**: 分页符
- **permanentpen**: 永久笔
- **powerpaste**: 强力粘贴
- **spellchecker**: 拼写检查
- **toc**: 目录

## 注意事项

### 1. 性能优化
- 大文档时考虑分页或懒加载
- 合理配置插件，避免加载不必要的功能
- 图片上传时进行压缩处理

### 2. 安全性
- 对用户输入内容进行XSS过滤
- 文件上传时验证文件类型和大小
- 使用HTTPS传输敏感内容

### 3. 移动端适配
- 使用响应式工具栏配置
- 考虑触摸设备的交互体验
- 适配不同屏幕尺寸

### 4. 内容格式
- 统一内容格式标准
- 处理不同来源的粘贴内容
- 保持内容的一致性

## 常见问题

### Q: 如何自定义编辑器样式？
A: 通过contentCss和contentStyle属性设置编辑区域样式

### Q: 如何实现图片拖拽上传？
A: 配置imageUpload函数，编辑器会自动处理拖拽上传

### Q: 如何限制编辑器内容长度？
A: 使用maxLength属性限制最大长度，使用wordcount插件显示字数

### Q: 如何实现多人协作编辑？
A: 需要结合WebSocket和操作转换算法实现实时协作功能
