# FuniEmpty 配置结构定义

## 基础配置接口

### FuniEmptyProps
```typescript
interface FuniEmptyProps {
  // ElementPlus 基础属性
  image?: string;                      // 图片地址
  imageSize?: number;                  // 图片大小
  description?: string;                // 描述文字
  
  // 扩展属性
  type?: EmptyType;                    // 空状态类型
  size?: 'large' | 'default' | 'small'; // 组件尺寸
  
  // 图标配置
  icon?: string;                       // 图标名称
  iconSize?: string | number;          // 图标大小
  iconColor?: string;                  // 图标颜色
  
  // 标题配置
  title?: string;                      // 标题文字
  titleSize?: string;                  // 标题字体大小
  titleColor?: string;                 // 标题颜色
  
  // 描述配置
  descriptionSize?: string;            // 描述字体大小
  descriptionColor?: string;           // 描述颜色
  
  // 操作配置
  showAction?: boolean;                // 是否显示操作按钮
  actionText?: string;                 // 操作按钮文字
  actionType?: ButtonType;             // 操作按钮类型
  actionSize?: ButtonSize;             // 操作按钮尺寸
  actionLoading?: boolean;             // 操作按钮加载状态
  actions?: ActionConfig[];            // 多个操作按钮配置
  
  // 样式配置
  background?: string;                 // 背景样式
  padding?: string;                    // 内边距
  minHeight?: string;                  // 最小高度
  animation?: AnimationType;           // 动画类型
  
  // 自定义样式
  customClass?: string;                // 自定义CSS类名
  customStyle?: Record<string, any>;   // 自定义样式
}
```

### EmptyType - 空状态类型
```typescript
type EmptyType = 
  | 'default'        // 默认状态
  | 'no-data'        // 无数据
  | 'no-result'      // 无搜索结果
  | 'no-network'     // 网络错误
  | 'no-permission'  // 无权限
  | 'error'          // 错误状态
  | 'maintenance'    // 维护中
  | 'loading'        // 加载中
  | 'success'        // 成功状态
  | 'custom';        // 自定义
```

### ActionConfig - 操作按钮配置
```typescript
interface ActionConfig {
  key: string;                         // 操作唯一标识
  label: string;                       // 显示文本
  icon?: string;                       // 图标名称
  type?: ButtonType;                   // 按钮类型
  size?: ButtonSize;                   // 按钮尺寸
  disabled?: boolean;                  // 是否禁用
  loading?: boolean;                   // 是否加载中
  permission?: string;                 // 权限标识
  tooltip?: string;                    // 提示信息
  visible?: boolean;                   // 是否可见
  order?: number;                      // 排序权重
  [key: string]: any;                  // 其他自定义属性
}
```

### ButtonType - 按钮类型
```typescript
type ButtonType = 
  | 'primary' 
  | 'success' 
  | 'warning' 
  | 'danger' 
  | 'info' 
  | 'text' 
  | 'default';
```

### ButtonSize - 按钮尺寸
```typescript
type ButtonSize = 'large' | 'default' | 'small';
```

### AnimationType - 动画类型
```typescript
type AnimationType = 
  | 'none'           // 无动画
  | 'fade'           // 淡入淡出
  | 'pulse'          // 脉冲
  | 'bounce'         // 弹跳
  | 'shake'          // 摇摆
  | 'rotate'         // 旋转
  | 'custom';        // 自定义
```

## 事件配置

### EmptyEvents
```typescript
interface EmptyEvents {
  // 操作事件
  action?: (action: ActionConfig, index: number) => void; // 操作按钮点击事件
  retry?: () => void;                  // 重试事件
  click?: (event: Event) => void;      // 点击事件
  
  // 状态事件
  'visible-change'?: (visible: boolean) => void; // 可见状态变化
  'animation-end'?: () => void;        // 动画结束事件
}
```

## 插槽配置

### EmptySlots
```typescript
interface EmptySlots {
  default?: () => VNode[];             // 默认内容插槽
  image?: () => VNode[];               // 自定义图片插槽
  icon?: () => VNode[];                // 自定义图标插槽
  title?: () => VNode[];               // 自定义标题插槽
  description?: () => VNode[];         // 自定义描述插槽
  action?: (props: { actions: ActionConfig[] }) => VNode[]; // 自定义操作区域插槽
}
```

## 方法配置

### EmptyMethods
```typescript
interface EmptyMethods {
  refresh: () => void;                 // 刷新状态
  retry: () => void;                   // 触发重试
  show: () => void;                    // 显示组件
  hide: () => void;                    // 隐藏组件
}
```

## 预设类型配置

### 无数据类型
```typescript
const noDataConfig: Partial<FuniEmptyProps> = {
  type: 'no-data',
  icon: 'DocumentRemove',
  iconColor: '#c0c4cc',
  iconSize: '64px',
  title: '暂无数据',
  titleColor: '#303133',
  description: '当前没有数据，请添加数据或刷新页面',
  descriptionColor: '#909399',
  showAction: true,
  actionText: '添加数据',
  actionType: 'primary'
}
```

### 无搜索结果类型
```typescript
const noResultConfig: Partial<FuniEmptyProps> = {
  type: 'no-result',
  icon: 'Search',
  iconColor: '#e6a23c',
  iconSize: '64px',
  title: '无搜索结果',
  titleColor: '#303133',
  description: '未找到符合条件的内容，请尝试其他关键词',
  descriptionColor: '#909399',
  showAction: true,
  actionText: '重新搜索',
  actionType: 'warning'
}
```

### 网络错误类型
```typescript
const noNetworkConfig: Partial<FuniEmptyProps> = {
  type: 'no-network',
  icon: 'WifiOff',
  iconColor: '#f56c6c',
  iconSize: '64px',
  title: '网络连接失败',
  titleColor: '#303133',
  description: '请检查网络连接后重试',
  descriptionColor: '#909399',
  showAction: true,
  actionText: '重新加载',
  actionType: 'danger'
}
```

### 无权限类型
```typescript
const noPermissionConfig: Partial<FuniEmptyProps> = {
  type: 'no-permission',
  icon: 'Lock',
  iconColor: '#909399',
  iconSize: '64px',
  title: '暂无访问权限',
  titleColor: '#303133',
  description: '您没有权限访问此内容，请联系管理员',
  descriptionColor: '#909399',
  showAction: true,
  actionText: '申请权限',
  actionType: 'info'
}
```

### 错误状态类型
```typescript
const errorConfig: Partial<FuniEmptyProps> = {
  type: 'error',
  icon: 'Warning',
  iconColor: '#f56c6c',
  iconSize: '64px',
  title: '加载失败',
  titleColor: '#303133',
  description: '数据加载过程中发生错误',
  descriptionColor: '#909399',
  actions: [
    { key: 'retry', label: '重试', type: 'primary', icon: 'Refresh' },
    { key: 'report', label: '反馈问题', type: 'text', icon: 'Warning' }
  ]
}
```

### 维护中类型
```typescript
const maintenanceConfig: Partial<FuniEmptyProps> = {
  type: 'maintenance',
  icon: 'Tools',
  iconColor: '#e6a23c',
  iconSize: '64px',
  title: '系统维护中',
  titleColor: '#303133',
  description: '系统正在维护升级，预计2小时后恢复',
  descriptionColor: '#909399',
  showAction: true,
  actionText: '刷新页面',
  actionType: 'warning'
}
```

## 常用配置组合

### 表格空状态
```typescript
const tableEmptyConfig: FuniEmptyProps = {
  type: 'no-data',
  icon: 'Grid',
  iconSize: '48px',
  title: '暂无数据',
  description: '当前表格没有数据',
  minHeight: '200px',
  showAction: true,
  actionText: '添加数据',
  actionType: 'primary'
}
```

### 搜索结果空状态
```typescript
const searchEmptyConfig: FuniEmptyProps = {
  type: 'no-result',
  icon: 'Search',
  iconSize: '56px',
  title: '无搜索结果',
  description: '未找到相关内容',
  actions: [
    { key: 'clear', label: '清空搜索', type: 'text' },
    { key: 'retry', label: '重新搜索', type: 'primary' }
  ]
}
```

### 页面级空状态
```typescript
const pageEmptyConfig: FuniEmptyProps = {
  type: 'no-data',
  icon: 'Document',
  iconSize: '80px',
  title: '还没有内容',
  description: '创建您的第一个项目，开始您的工作',
  minHeight: '400px',
  background: '#fafafa',
  padding: '60px 40px',
  actions: [
    { key: 'create', label: '创建项目', type: 'primary', icon: 'Plus' },
    { key: 'import', label: '导入项目', type: 'default', icon: 'Upload' }
  ]
}
```

### 加载状态配置
```typescript
const loadingConfig: FuniEmptyProps = {
  type: 'loading',
  icon: 'Loading',
  iconSize: '48px',
  iconColor: '#409eff',
  title: '加载中...',
  description: '正在获取数据，请稍候',
  animation: 'rotate'
}
```

## 样式配置

### 主题样式变量
```css
:root {
  /* 空状态基础样式 */
  --funi-empty-padding: 40px 20px;
  --funi-empty-text-align: center;
  --funi-empty-background: transparent;
  
  /* 图标样式 */
  --funi-empty-icon-size: 64px;
  --funi-empty-icon-color: #c0c4cc;
  --funi-empty-icon-margin-bottom: 16px;
  
  /* 标题样式 */
  --funi-empty-title-font-size: 16px;
  --funi-empty-title-color: #303133;
  --funi-empty-title-margin-bottom: 8px;
  --funi-empty-title-font-weight: 500;
  
  /* 描述样式 */
  --funi-empty-description-font-size: 14px;
  --funi-empty-description-color: #909399;
  --funi-empty-description-line-height: 1.5;
  --funi-empty-description-margin-bottom: 16px;
  
  /* 操作按钮样式 */
  --funi-empty-action-margin-top: 16px;
  --funi-empty-action-gap: 12px;
}
```

### 尺寸配置
```typescript
const sizeConfig = {
  large: {
    iconSize: '80px',
    titleSize: '18px',
    descriptionSize: '16px',
    padding: '60px 40px',
    actionSize: 'large'
  },
  default: {
    iconSize: '64px',
    titleSize: '16px',
    descriptionSize: '14px',
    padding: '40px 20px',
    actionSize: 'default'
  },
  small: {
    iconSize: '48px',
    titleSize: '14px',
    descriptionSize: '12px',
    padding: '24px 16px',
    actionSize: 'small'
  }
}
```

## 最佳实践配置

### 响应式配置
```typescript
const responsiveEmptyConfig: FuniEmptyProps = {
  customStyle: {
    padding: 'clamp(24px, 5vw, 60px) clamp(16px, 4vw, 40px)'
  },
  iconSize: 'clamp(48px, 8vw, 80px)',
  titleSize: 'clamp(14px, 2.5vw, 18px)',
  descriptionSize: 'clamp(12px, 2vw, 16px)'
}
```

### 无障碍配置
```typescript
const accessibleEmptyConfig: FuniEmptyProps = {
  customClass: 'accessible-empty',
  // 通过CSS添加适当的ARIA属性
}
```

### 动画配置
```typescript
const animatedEmptyConfig: FuniEmptyProps = {
  animation: 'fade',
  customClass: 'animated-empty'
}
```
