# FuniEmpty ElementPlus API 支持

## 基础组件说明

FuniEmpty 是基于 ElementPlus 的 `el-empty` 组件进行二次封装的空状态组件。它完全兼容 `el-empty` 的所有原生 API，同时扩展了更多的业务功能和交互特性。

### 基础 el-empty 组件
```vue
<!-- ElementPlus 原生用法 -->
<el-empty description="暂无数据" />

<!-- FuniEmpty 等效用法 -->
<FuniEmpty description="暂无数据" />
```

## 支持的 ElementPlus API

### Props 透传支持

| ElementPlus 属性 | 类型 | 默认值 | 说明 | FuniEmpty 支持 |
|-----------------|------|--------|------|---------------|
| image | String | - | 图片地址 | ✅ 完全支持 |
| image-size | Number | - | 图片大小 | ✅ 完全支持 |
| description | String | - | 描述文字 | ✅ 完全支持 |

#### image 属性详细说明
```vue
<template>
  <div class="image-examples">
    <!-- 使用默认图片 -->
    <FuniEmpty description="使用默认图片" />
    
    <!-- 自定义图片 -->
    <FuniEmpty 
      image="https://shadow.elemecdn.com/app/element/hamburger.9cf7b091-55e9-11e9-a976-7f4d0b07eef6.png"
      description="自定义图片"
    />
    
    <!-- 设置图片大小 -->
    <FuniEmpty 
      image="https://shadow.elemecdn.com/app/element/hamburger.9cf7b091-55e9-11e9-a976-7f4d0b07eef6.png"
      :image-size="120"
      description="自定义图片大小"
    />
  </div>
</template>
```

#### description 属性详细说明
```vue
<template>
  <div class="description-examples">
    <!-- 简单描述 -->
    <FuniEmpty description="暂无数据" />
    
    <!-- 长描述文本 -->
    <FuniEmpty description="当前没有任何数据，请添加数据后再查看。您可以通过点击添加按钮来创建新的数据项。" />
    
    <!-- 空描述 -->
    <FuniEmpty />
  </div>
</template>
```

### 插槽透传支持

| ElementPlus 插槽 | 说明 | FuniEmpty 支持 |
|-----------------|------|---------------|
| default | 自定义内容 | ✅ 完全支持 |
| image | 自定义图片 | ✅ 完全支持 |
| description | 自定义描述 | ✅ 完全支持 |

#### 插槽使用示例
```vue
<template>
  <div class="slot-examples">
    <!-- 使用默认插槽 -->
    <FuniEmpty>
      <div class="custom-content">
        <h3>自定义内容</h3>
        <p>这是通过默认插槽添加的自定义内容</p>
        <el-button type="primary">自定义操作</el-button>
      </div>
    </FuniEmpty>
    
    <!-- 使用图片插槽 -->
    <FuniEmpty description="自定义图片插槽">
      <template #image>
        <div class="custom-image">
          <el-icon :size="80" color="#409eff">
            <Picture />
          </el-icon>
        </div>
      </template>
    </FuniEmpty>
    
    <!-- 使用描述插槽 -->
    <FuniEmpty>
      <template #description>
        <div class="custom-description">
          <h4>自定义描述标题</h4>
          <p>这是通过插槽自定义的描述内容</p>
          <el-tag type="info">提示标签</el-tag>
        </div>
      </template>
    </FuniEmpty>
  </div>
</template>

<script setup>
import { Picture } from '@element-plus/icons-vue'
</script>

<style scoped>
.custom-content {
  text-align: center;
  padding: 20px;
}

.custom-image {
  margin-bottom: 16px;
}

.custom-description {
  text-align: center;
}

.custom-description h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.custom-description p {
  margin: 0 0 12px 0;
  color: #606266;
}
</style>
```

## FuniEmpty 扩展功能

### 新增 Props

| 扩展属性 | 类型 | 默认值 | 说明 |
|---------|------|--------|------|
| type | String | default | 空状态类型：default/no-data/no-result/no-network/no-permission/error/maintenance |
| size | String | default | 组件尺寸：large/default/small |
| icon | String | - | 图标名称 |
| iconSize | String/Number | 64px | 图标大小 |
| iconColor | String | #c0c4cc | 图标颜色 |
| title | String | - | 标题文字 |
| titleSize | String | 16px | 标题字体大小 |
| titleColor | String | #303133 | 标题颜色 |
| descriptionSize | String | 14px | 描述字体大小 |
| descriptionColor | String | #909399 | 描述颜色 |
| showAction | Boolean | false | 是否显示操作按钮 |
| actionText | String | - | 操作按钮文字 |
| actionType | String | primary | 操作按钮类型 |
| actionSize | String | default | 操作按钮尺寸 |
| actionLoading | Boolean | false | 操作按钮加载状态 |
| actions | Array | [] | 多个操作按钮配置 |
| background | String | - | 背景样式 |
| padding | String | - | 内边距 |
| minHeight | String | - | 最小高度 |
| animation | String | none | 动画类型 |

### 新增 Events

| 扩展事件 | 参数 | 说明 |
|---------|------|------|
| action | action: Object, index: Number | 点击操作按钮时触发 |
| retry | - | 点击重试按钮时触发 |
| click | event: Event | 点击空状态区域时触发 |

### 新增插槽

| 扩展插槽 | 参数 | 说明 |
|---------|------|------|
| icon | - | 自定义图标 |
| title | - | 自定义标题 |
| action | { actions } | 自定义操作区域 |

## 透传机制说明

### v-bind 透传
FuniEmpty 使用 `v-bind="$attrs"` 将所有未声明的属性透传给内部的 `el-empty` 组件：

```vue
<template>
  <!-- FuniEmpty 内部实现 -->
  <el-empty 
    v-bind="$attrs"
    :image="computedImage"
    :image-size="computedImageSize"
    :description="computedDescription"
    :class="emptyClasses"
    @click="handleClick"
  >
    <!-- 插槽内容 -->
  </el-empty>
</template>
```

### 属性优先级
1. **FuniEmpty 显式声明的 props** - 最高优先级
2. **透传的 ElementPlus 属性** - 中等优先级
3. **默认值** - 最低优先级

### 使用示例
```vue
<template>
  <!-- 混合使用 FuniEmpty 和 ElementPlus 属性 -->
  <FuniEmpty
    description="混合属性示例"
    :image-size="100"
    type="no-data"
    icon="DocumentRemove"
    title="暂无文档"
    show-action
    action-text="添加文档"
    @action="handleAddDocument"
    @click="handleEmptyClick"
  />
</template>

<script setup>
const handleAddDocument = () => {
  console.log('添加文档')
}

const handleEmptyClick = (event) => {
  console.log('点击空状态区域', event)
}
</script>
```

## 样式继承

### CSS 类名继承
FuniEmpty 保持了 ElementPlus el-empty 的所有 CSS 类名：

```css
/* ElementPlus 原生类名 */
.el-empty {
  /* 空状态容器样式 */
}

.el-empty__image {
  /* 图片样式 */
}

.el-empty__description {
  /* 描述样式 */
}

/* FuniEmpty 扩展类名 */
.funi-empty {
  /* FuniEmpty 特有样式 */
}

.funi-empty--large {
  /* 大尺寸样式 */
}

.funi-empty--no-data {
  /* 无数据类型样式 */
}

.funi-empty--with-action {
  /* 带操作按钮样式 */
}
```

### 样式覆盖示例
```vue
<template>
  <FuniEmpty 
    description="自定义样式空状态"
    class="custom-empty"
    type="no-data"
    show-action
    action-text="自定义操作"
  />
</template>

<style scoped>
.custom-empty :deep(.el-empty__image) {
  margin-bottom: 24px;
}

.custom-empty :deep(.el-empty__description) {
  color: #409eff;
  font-size: 16px;
  font-weight: 500;
}

.custom-empty {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  padding: 40px;
}
</style>
```

## 兼容性说明

### 版本兼容性
- **ElementPlus 版本要求**: >= 2.0.0
- **Vue 版本要求**: >= 3.0.0
- **完全向后兼容**: 所有 el-empty 的用法都可以直接迁移到 FuniEmpty

### 迁移指南
```vue
<!-- 迁移前：使用 el-empty -->
<el-empty 
  description="暂无数据"
  :image-size="100"
>
  <template #image>
    <img src="/custom-empty.png" />
  </template>
</el-empty>

<!-- 迁移后：使用 FuniEmpty -->
<FuniEmpty 
  description="暂无数据"
  :image-size="100"
>
  <template #image>
    <img src="/custom-empty.png" />
  </template>
</FuniEmpty>
```

### 注意事项
1. **事件监听**: FuniEmpty 的事件监听器会正确透传给 el-empty
2. **ref 引用**: 可以通过 ref 访问内部的 el-empty 实例
3. **样式隔离**: FuniEmpty 的样式不会影响原有的 el-empty 样式

```vue
<template>
  <FuniEmpty ref="emptyRef" description="引用示例" />
</template>

<script setup>
import { ref, onMounted } from 'vue'

const emptyRef = ref()

onMounted(() => {
  // 访问内部 el-empty 实例
  console.log(emptyRef.value)
})
</script>
```
