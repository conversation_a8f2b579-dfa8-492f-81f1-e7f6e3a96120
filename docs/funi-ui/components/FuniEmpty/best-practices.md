# FuniEmpty 最佳实践

## 设计原则

### 1. 用户体验优先
空状态设计应该：
- 提供清晰的状态说明和指导
- 避免让用户感到困惑或沮丧
- 提供有意义的下一步操作
- 保持积极和鼓励的语调

### 2. 情境化设计
根据不同场景选择合适的空状态：
- **数据为空**：引导用户添加数据
- **搜索无结果**：建议调整搜索条件
- **网络错误**：提供重试机制
- **权限不足**：说明如何获取权限

### 3. 视觉一致性
在整个应用中保持：
- 统一的图标和插图风格
- 一致的颜色和字体规范
- 相似的布局和间距
- 统一的交互模式

## 内容策略最佳实践

### 文案编写指南
```vue
<template>
  <div class="content-examples">
    <!-- ✅ 好的文案：具体、有帮助 -->
    <FuniEmpty 
      type="no-data"
      title="还没有项目"
      description="创建您的第一个项目，开始管理您的工作"
      show-action
      action-text="创建项目"
    />
    
    <!-- ❌ 避免：模糊、技术性 -->
    <!-- 
    <FuniEmpty 
      description="No data found in database table"
    />
    -->
    
    <!-- ✅ 好的文案：友好、鼓励性 -->
    <FuniEmpty 
      type="no-result"
      title="没有找到匹配的内容"
      description="试试其他关键词，或者浏览推荐内容"
      :actions="searchActions"
    />
    
    <!-- ❌ 避免：消极、指责性 -->
    <!-- 
    <FuniEmpty 
      description="Your search returned 0 results"
    />
    -->
  </div>
</template>

<script setup>
const searchActions = [
  { key: 'clear', label: '清空搜索', type: 'text' },
  { key: 'browse', label: '浏览推荐', type: 'primary' }
]
</script>
```

### 多语言支持
```vue
<template>
  <FuniEmpty 
    :title="$t('empty.noData.title')"
    :description="$t('empty.noData.description')"
    :action-text="$t('empty.noData.action')"
    show-action
  />
</template>

<script setup>
// i18n 配置示例
const messages = {
  zh: {
    empty: {
      noData: {
        title: '暂无数据',
        description: '当前没有数据，请添加数据',
        action: '添加数据'
      }
    }
  },
  en: {
    empty: {
      noData: {
        title: 'No Data',
        description: 'No data available, please add some data',
        action: 'Add Data'
      }
    }
  }
}
</script>
```

## 视觉设计最佳实践

### 图标选择指南
```vue
<template>
  <div class="icon-examples">
    <!-- 数据相关场景 -->
    <FuniEmpty 
      icon="DocumentRemove"
      title="暂无文档"
      description="还没有上传任何文档"
    />
    
    <!-- 搜索相关场景 -->
    <FuniEmpty 
      icon="Search"
      title="无搜索结果"
      description="未找到相关内容"
    />
    
    <!-- 网络相关场景 -->
    <FuniEmpty 
      icon="WifiOff"
      title="网络连接失败"
      description="请检查网络连接"
    />
    
    <!-- 权限相关场景 -->
    <FuniEmpty 
      icon="Lock"
      title="暂无访问权限"
      description="请联系管理员获取权限"
    />
  </div>
</template>
```

### 颜色使用规范
```vue
<template>
  <div class="color-examples">
    <!-- 中性状态：使用灰色系 -->
    <FuniEmpty 
      icon="Document"
      icon-color="#c0c4cc"
      title="暂无内容"
      title-color="#303133"
      description="还没有任何内容"
      description-color="#909399"
    />
    
    <!-- 错误状态：使用红色系 -->
    <FuniEmpty 
      icon="Warning"
      icon-color="#f56c6c"
      title="加载失败"
      title-color="#f56c6c"
      description="数据加载过程中发生错误"
      description-color="#909399"
    />
    
    <!-- 成功状态：使用绿色系 -->
    <FuniEmpty 
      icon="CircleCheck"
      icon-color="#67c23a"
      title="任务完成"
      title-color="#67c23a"
      description="所有任务已完成"
      description-color="#909399"
    />
  </div>
</template>
```

### 响应式设计
```vue
<template>
  <FuniEmpty 
    :icon-size="responsiveIconSize"
    :title-size="responsiveTitleSize"
    :description-size="responsiveDescSize"
    :padding="responsivePadding"
    title="响应式空状态"
    description="自动适应不同屏幕尺寸"
    class="responsive-empty"
  />
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'

const screenWidth = ref(window.innerWidth)

const responsiveIconSize = computed(() => {
  if (screenWidth.value < 768) return '48px'
  if (screenWidth.value < 1024) return '64px'
  return '80px'
})

const responsiveTitleSize = computed(() => {
  if (screenWidth.value < 768) return '14px'
  return '16px'
})

const responsiveDescSize = computed(() => {
  if (screenWidth.value < 768) return '12px'
  return '14px'
})

const responsivePadding = computed(() => {
  if (screenWidth.value < 768) return '20px 16px'
  return '40px 20px'
})

const handleResize = () => {
  screenWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.responsive-empty {
  transition: all 0.3s ease;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .responsive-empty :deep(.el-empty__description) {
    line-height: 1.4;
    padding: 0 8px;
  }
}
</style>
```

## 交互设计最佳实践

### 操作按钮设计
```vue
<template>
  <div class="action-examples">
    <!-- 单个主要操作 -->
    <FuniEmpty 
      type="no-data"
      title="暂无数据"
      description="开始添加您的第一条数据"
      show-action
      action-text="添加数据"
      action-type="primary"
      @action="handlePrimaryAction"
    />
    
    <!-- 多个操作选项 -->
    <FuniEmpty 
      type="no-result"
      title="无搜索结果"
      description="尝试其他搜索方式"
      :actions="multipleActions"
      @action="handleMultipleAction"
    />
    
    <!-- 带加载状态的操作 -->
    <FuniEmpty 
      type="no-network"
      title="网络连接失败"
      description="请检查网络连接后重试"
      show-action
      action-text="重新加载"
      :action-loading="isRetrying"
      @action="handleRetry"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const isRetrying = ref(false)

const multipleActions = [
  { key: 'clear', label: '清空搜索', type: 'text' },
  { key: 'advanced', label: '高级搜索', type: 'default' },
  { key: 'browse', label: '浏览分类', type: 'primary' }
]

const handlePrimaryAction = () => {
  ElMessage.success('跳转到添加数据页面')
}

const handleMultipleAction = (action, index) => {
  switch (action.key) {
    case 'clear':
      ElMessage.info('清空搜索条件')
      break
    case 'advanced':
      ElMessage.info('打开高级搜索')
      break
    case 'browse':
      ElMessage.info('浏览分类页面')
      break
  }
}

const handleRetry = async () => {
  isRetrying.value = true
  try {
    // 模拟重试操作
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('重新加载成功')
  } catch (error) {
    ElMessage.error('重新加载失败')
  } finally {
    isRetrying.value = false
  }
}
</script>
```

### 状态转换
```vue
<template>
  <div class="state-transition-example">
    <div class="controls">
      <el-button @click="setState('loading')">加载中</el-button>
      <el-button @click="setState('empty')">空状态</el-button>
      <el-button @click="setState('error')">错误状态</el-button>
      <el-button @click="setState('success')">成功状态</el-button>
    </div>
    
    <div class="state-container">
      <!-- 加载状态 -->
      <FuniEmpty 
        v-if="currentState === 'loading'"
        icon="Loading"
        icon-color="#409eff"
        title="加载中..."
        description="正在获取数据，请稍候"
        animation="rotate"
      />
      
      <!-- 空状态 -->
      <FuniEmpty 
        v-else-if="currentState === 'empty'"
        type="no-data"
        title="暂无数据"
        description="还没有任何数据"
        show-action
        action-text="添加数据"
        @action="handleAddData"
      />
      
      <!-- 错误状态 -->
      <FuniEmpty 
        v-else-if="currentState === 'error'"
        type="error"
        title="加载失败"
        description="数据加载过程中发生错误"
        :actions="errorActions"
        @action="handleErrorAction"
      />
      
      <!-- 成功状态 -->
      <FuniEmpty 
        v-else-if="currentState === 'success'"
        icon="CircleCheck"
        icon-color="#67c23a"
        title="操作成功"
        description="数据已成功加载"
        show-action
        action-text="继续操作"
        action-type="success"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const currentState = ref('empty')

const errorActions = [
  { key: 'retry', label: '重试', type: 'primary' },
  { key: 'report', label: '反馈问题', type: 'text' }
]

const setState = (state) => {
  currentState.value = state
}

const handleAddData = () => {
  setState('loading')
  setTimeout(() => setState('success'), 2000)
}

const handleErrorAction = (action) => {
  if (action.key === 'retry') {
    setState('loading')
    setTimeout(() => setState('empty'), 2000)
  }
}
</script>

<style scoped>
.state-transition-example {
  padding: 20px;
}

.controls {
  margin-bottom: 20px;
  text-align: center;
}

.controls .el-button {
  margin: 0 8px;
}

.state-container {
  min-height: 300px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
```

## 业务场景最佳实践

### 表格空状态
```vue
<template>
  <div class="table-best-practices">
    <!-- 数据表格 -->
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="name" label="姓名" />
      <el-table-column prop="email" label="邮箱" />
      <el-table-column prop="role" label="角色" />
      <template #empty>
        <FuniEmpty
          icon="User"
          icon-size="48px"
          title="暂无用户数据"
          description="当前没有用户信息，请添加用户"
          show-action
          action-text="添加用户"
          action-type="primary"
          @action="handleAddUser"
        />
      </template>
    </el-table>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const tableData = ref([])

const handleAddUser = () => {
  console.log('添加用户')
}
</script>
```

## 常见错误和避免方法

### 1. 避免过于复杂的空状态
```vue
<!-- ❌ 错误：信息过载 -->
<FuniEmpty
  title="系统检测到您当前没有任何数据项目文件内容信息"
  description="这可能是因为您还没有创建任何项目，或者您的项目已被删除，或者系统出现了技术故障"
/>

<!-- ✅ 正确：简洁明了 -->
<FuniEmpty
  title="暂无项目"
  description="创建您的第一个项目开始工作"
  show-action
  action-text="创建项目"
/>
```

### 2. 避免技术性错误信息
```vue
<!-- ❌ 错误：技术性错误 -->
<FuniEmpty
  title="HTTP 404 Error"
  description="Resource not found in database table 'projects'"
/>

<!-- ✅ 正确：用户友好的信息 -->
<FuniEmpty
  type="error"
  title="页面未找到"
  description="您访问的页面不存在，请检查链接是否正确"
  show-action
  action-text="返回首页"
/>
```

### 3. 避免死胡同体验
```vue
<!-- ❌ 错误：没有出路 -->
<FuniEmpty
  title="访问被拒绝"
  description="您没有权限访问此页面"
/>

<!-- ✅ 正确：提供解决方案 -->
<FuniEmpty
  type="no-permission"
  title="暂无访问权限"
  description="您没有权限访问此页面，请联系管理员"
  :actions="permissionActions"
/>
```

## 测试最佳实践

### 单元测试示例
```javascript
import { mount } from '@vue/test-utils'
import FuniEmpty from '@/components/FuniEmpty'

describe('FuniEmpty', () => {
  test('renders title and description correctly', () => {
    const wrapper = mount(FuniEmpty, {
      props: {
        title: 'Test Title',
        description: 'Test Description'
      }
    })

    expect(wrapper.text()).toContain('Test Title')
    expect(wrapper.text()).toContain('Test Description')
  })

  test('emits action event when button clicked', async () => {
    const wrapper = mount(FuniEmpty, {
      props: {
        showAction: true,
        actionText: 'Test Action'
      }
    })

    await wrapper.find('button').trigger('click')
    expect(wrapper.emitted('action')).toBeTruthy()
  })

  test('shows correct icon for different types', () => {
    const wrapper = mount(FuniEmpty, {
      props: {
        type: 'no-data'
      }
    })

    expect(wrapper.find('.el-icon').exists()).toBe(true)
  })
})
```

### E2E 测试示例
```javascript
// cypress/integration/funi-empty.spec.js
describe('FuniEmpty Component', () => {
  beforeEach(() => {
    cy.visit('/components/funi-empty')
  })

  it('should display empty state when no data', () => {
    cy.get('[data-testid="empty-table"]')
      .should('be.visible')
      .find('.funi-empty')
      .should('contain', '暂无数据')
  })

  it('should trigger action when button clicked', () => {
    cy.get('[data-testid="empty-with-action"]')
      .find('button')
      .click()

    cy.get('[data-testid="action-result"]')
      .should('contain', '操作已执行')
  })

  it('should be accessible via keyboard', () => {
    cy.get('[data-testid="keyboard-empty"]')
      .focus()
      .type('{enter}')

    cy.get('[data-testid="keyboard-result"]')
      .should('be.visible')
  })
})
```

## 性能监控

### 渲染性能监控
```javascript
// 监控空状态组件渲染性能
const observer = new PerformanceObserver((list) => {
  for (const entry of list.getEntries()) {
    if (entry.name.includes('funi-empty')) {
      console.log(`FuniEmpty ${entry.name}: ${entry.duration}ms`)
    }
  }
})

observer.observe({ entryTypes: ['measure'] })

// 在组件中标记性能
export default {
  mounted() {
    performance.mark('funi-empty-mount-start')
  },
  updated() {
    performance.mark('funi-empty-update-end')
    performance.measure(
      'funi-empty-update',
      'funi-empty-mount-start',
      'funi-empty-update-end'
    )
  }
}
```
