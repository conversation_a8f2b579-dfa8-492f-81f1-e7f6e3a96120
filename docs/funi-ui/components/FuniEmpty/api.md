# FuniEmpty API文档

## 组件概述

FuniEmpty是基于ElementPlus的el-empty封装的空状态组件，提供了多种空状态场景、自定义图标、操作按钮等功能，适用于数据为空、搜索无结果、网络错误等各种空状态展示场景。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 | ElementPlus对应 |
|--------|------|--------|------|------|----------------|
| image | String | '' | - | 图片地址 | el-empty.image |
| imageSize | Number | - | - | 图片大小 | el-empty.image-size |
| description | String | '' | - | 描述文字 | el-empty.description |
| type | String | 'default' | - | 空状态类型 | - |
| size | String | 'default' | - | 组件尺寸 | - |
| icon | String | '' | - | 图标名称 | - |
| iconSize | String/Number | '64px' | - | 图标大小 | - |
| iconColor | String | '#c0c4cc' | - | 图标颜色 | - |
| title | String | '' | - | 标题文字 | - |
| titleSize | String | '16px' | - | 标题字体大小 | - |
| titleColor | String | '#303133' | - | 标题颜色 | - |
| descriptionSize | String | '14px' | - | 描述字体大小 | - |
| descriptionColor | String | '#909399' | - | 描述颜色 | - |
| showAction | Boolean | false | - | 是否显示操作区域 | - |
| actionText | String | '重试' | - | 操作按钮文字 | - |
| actionType | String | 'primary' | - | 操作按钮类型 | - |
| actionSize | String | 'default' | - | 操作按钮尺寸 | - |
| actionLoading | Boolean | false | - | 操作按钮加载状态 | - |
| actions | Array | [] | - | 多个操作按钮配置 | - |
| background | String | '' | - | 背景色 | - |
| padding | String | '40px 20px' | - | 内边距 | - |
| minHeight | String | '200px' | - | 最小高度 | - |
| centered | Boolean | true | - | 是否居中显示 | - |
| animation | String | '' | - | 动画效果 | - |
| customClass | String | '' | - | 自定义类名 | - |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| action | action: Object, index: Number | 操作事件 | 点击操作按钮时 |
| retry | - | 重试事件 | 点击重试按钮时 |
| click | event: Event | 点击事件 | 点击空状态区域时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| refresh | - | void | 刷新状态 |
| retry | - | void | 触发重试 |

## Slots

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| default | - | 自定义内容 |
| image | - | 自定义图片 |
| icon | - | 自定义图标 |
| title | - | 自定义标题 |
| description | - | 自定义描述 |
| action | { actions } | 自定义操作区域 |

## 空状态类型

```typescript
type EmptyType = 
  | 'default'     // 默认空状态
  | 'no-data'     // 无数据
  | 'no-result'   // 无搜索结果
  | 'no-network'  // 网络错误
  | 'no-permission' // 无权限
  | 'error'       // 错误状态
  | 'maintenance' // 维护中
  | 'coming-soon'; // 即将上线
```

## 操作按钮配置

```typescript
interface ActionConfig {
  key: string;                     // 操作键
  text: string;                    // 按钮文字
  type?: string;                   // 按钮类型
  size?: string;                   // 按钮尺寸
  icon?: string;                   // 按钮图标
  loading?: boolean;               // 加载状态
  disabled?: boolean;              // 禁用状态
  permission?: string;             // 权限标识
  [key: string]: any;              // 其他配置
}
```

## 使用示例

### 基础空状态
```vue
<template>
  <div class="empty-examples">
    <div class="example-group">
      <h4>默认空状态</h4>
      <FuniEmpty description="暂无数据" />
    </div>
    
    <div class="example-group">
      <h4>自定义图片</h4>
      <FuniEmpty 
        image="https://example.com/empty.png"
        description="没有找到相关内容"
      />
    </div>
    
    <div class="example-group">
      <h4>自定义图标</h4>
      <FuniEmpty 
        icon="DocumentRemove"
        title="暂无文档"
        description="您还没有上传任何文档"
      />
    </div>
  </div>
</template>

<style scoped>
.empty-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px;
}

.example-group {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
}

.example-group h4 {
  margin: 0 0 16px 0;
  text-align: center;
  color: #303133;
}
</style>
```

### 不同类型的空状态
```vue
<template>
  <div class="type-examples">
    <div class="example-grid">
      <div class="example-item">
        <h4>无数据</h4>
        <FuniEmpty 
          type="no-data"
          title="暂无数据"
          description="当前列表为空，请添加数据"
          show-action
          action-text="添加数据"
          @action="handleAddData"
        />
      </div>
      
      <div class="example-item">
        <h4>无搜索结果</h4>
        <FuniEmpty 
          type="no-result"
          title="无搜索结果"
          description="未找到符合条件的内容，请尝试其他关键词"
          show-action
          action-text="重新搜索"
          @action="handleRetrySearch"
        />
      </div>
      
      <div class="example-item">
        <h4>网络错误</h4>
        <FuniEmpty 
          type="no-network"
          title="网络连接失败"
          description="请检查网络连接后重试"
          show-action
          action-text="重新加载"
          :action-loading="networkLoading"
          @action="handleRetryNetwork"
        />
      </div>
      
      <div class="example-item">
        <h4>无权限</h4>
        <FuniEmpty 
          type="no-permission"
          title="暂无访问权限"
          description="您没有权限访问此内容，请联系管理员"
          show-action
          action-text="申请权限"
          @action="handleRequestPermission"
        />
      </div>
      
      <div class="example-item">
        <h4>错误状态</h4>
        <FuniEmpty 
          type="error"
          title="加载失败"
          description="数据加载过程中发生错误"
          :actions="errorActions"
          @action="handleErrorAction"
        />
      </div>
      
      <div class="example-item">
        <h4>维护中</h4>
        <FuniEmpty 
          type="maintenance"
          title="系统维护中"
          description="系统正在维护升级，预计2小时后恢复"
          show-action
          action-text="刷新页面"
          @action="handleRefresh"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const networkLoading = ref(false)

const errorActions = reactive([
  { key: 'retry', text: '重试', type: 'primary', icon: 'Refresh' },
  { key: 'report', text: '反馈问题', type: 'default', icon: 'Warning' }
])

const handleAddData = () => {
  ElMessage.info('跳转到添加数据页面')
}

const handleRetrySearch = () => {
  ElMessage.info('重新搜索')
}

const handleRetryNetwork = async () => {
  networkLoading.value = true
  try {
    // 模拟网络重试
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('网络连接已恢复')
  } catch (error) {
    ElMessage.error('网络连接仍然失败')
  } finally {
    networkLoading.value = false
  }
}

const handleRequestPermission = () => {
  ElMessage.info('已提交权限申请')
}

const handleErrorAction = (action) => {
  if (action.key === 'retry') {
    ElMessage.info('正在重试...')
  } else if (action.key === 'report') {
    ElMessage.info('打开问题反馈页面')
  }
}

const handleRefresh = () => {
  location.reload()
}
</script>

<style scoped>
.type-examples {
  padding: 20px;
}

.example-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.example-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  min-height: 300px;
}

.example-item h4 {
  margin: 0 0 16px 0;
  text-align: center;
  color: #303133;
}
</style>
```

### 自定义样式的空状态
```vue
<template>
  <div class="custom-examples">
    <div class="example-item">
      <h4>自定义颜色</h4>
      <FuniEmpty 
        icon="Star"
        icon-color="#f39c12"
        icon-size="80px"
        title="收藏为空"
        title-color="#e67e22"
        description="您还没有收藏任何内容"
        description-color="#95a5a6"
        show-action
        action-text="去收藏"
        action-type="warning"
      />
    </div>
    
    <div class="example-item">
      <h4>自定义背景</h4>
      <FuniEmpty 
        icon="Moon"
        icon-color="#ffffff"
        icon-size="72px"
        title="夜间模式"
        title-color="#ffffff"
        description="当前处于夜间模式"
        description-color="#cccccc"
        background="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
        padding="60px 40px"
      />
    </div>
    
    <div class="example-item">
      <h4>动画效果</h4>
      <FuniEmpty 
        icon="Loading"
        icon-size="64px"
        title="加载中..."
        description="正在获取数据，请稍候"
        animation="pulse"
      />
    </div>
  </div>
</template>

<style scoped>
.custom-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px;
}

.example-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  min-height: 300px;
}

.example-item h4 {
  margin: 0 0 16px 0;
  text-align: center;
  color: #303133;
}
</style>
```

### 表格和列表中的空状态
```vue
<template>
  <div class="table-empty-examples">
    <div class="example-section">
      <h4>表格空状态</h4>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="role" label="角色" />
        <template #empty>
          <FuniEmpty 
            icon="User"
            title="暂无用户数据"
            description="当前没有用户信息，请添加用户"
            show-action
            action-text="添加用户"
            action-type="primary"
            @action="handleAddUser"
          />
        </template>
      </el-table>
    </div>
    
    <div class="example-section">
      <h4>搜索结果空状态</h4>
      <div class="search-container">
        <el-input 
          v-model="searchKeyword"
          placeholder="搜索用户"
          @input="handleSearch"
        >
          <template #append>
            <el-button icon="Search" @click="handleSearch" />
          </template>
        </el-input>
        
        <div class="search-results">
          <div v-if="searchResults.length === 0 && searchKeyword" class="empty-container">
            <FuniEmpty 
              type="no-result"
              title="无搜索结果"
              :description="`未找到包含"${searchKeyword}"的用户`"
              :actions="searchActions"
              @action="handleSearchAction"
            />
          </div>
          <div v-else-if="searchResults.length === 0" class="empty-container">
            <FuniEmpty 
              icon="Search"
              title="开始搜索"
              description="请输入关键词搜索用户"
            />
          </div>
          <div v-else>
            <div 
              v-for="user in searchResults"
              :key="user.id"
              class="result-item"
            >
              {{ user.name }} - {{ user.email }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const tableData = ref([])
const searchKeyword = ref('')
const searchResults = ref([])

const searchActions = reactive([
  { key: 'clear', text: '清空搜索', type: 'default' },
  { key: 'add', text: '添加用户', type: 'primary' }
])

const allUsers = [
  { id: 1, name: '张三', email: '<EMAIL>' },
  { id: 2, name: '李四', email: '<EMAIL>' },
  { id: 3, name: '王五', email: '<EMAIL>' }
]

const handleAddUser = () => {
  ElMessage.info('打开添加用户对话框')
}

const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    searchResults.value = []
    return
  }
  
  searchResults.value = allUsers.filter(user => 
    user.name.includes(searchKeyword.value) || 
    user.email.includes(searchKeyword.value)
  )
}

const handleSearchAction = (action) => {
  if (action.key === 'clear') {
    searchKeyword.value = ''
    searchResults.value = []
  } else if (action.key === 'add') {
    ElMessage.info('添加新用户')
  }
}
</script>

<style scoped>
.table-empty-examples {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.example-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
}

.search-container {
  max-width: 600px;
}

.search-results {
  margin-top: 16px;
  min-height: 200px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.empty-container {
  padding: 20px;
}

.result-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}
</style>
```

### 页面级空状态
```vue
<template>
  <div class="page-empty-examples">
    <div class="page-container">
      <div class="page-header">
        <h2>我的项目</h2>
        <el-button type="primary" @click="showCreateDialog = true">
          新建项目
        </el-button>
      </div>
      
      <div class="page-content">
        <FuniEmpty 
          v-if="projects.length === 0"
          type="no-data"
          title="还没有项目"
          description="创建您的第一个项目，开始您的工作"
          :actions="projectActions"
          min-height="400px"
          @action="handleProjectAction"
        />
        <div v-else class="project-list">
          <!-- 项目列表内容 -->
        </div>
      </div>
    </div>
    
    <!-- 创建项目对话框 -->
    <el-dialog 
      v-model="showCreateDialog"
      title="新建项目"
      width="500px"
    >
      <el-form :model="newProject" label-width="80px">
        <el-form-item label="项目名称">
          <el-input v-model="newProject.name" />
        </el-form-item>
        <el-form-item label="项目描述">
          <el-input 
            v-model="newProject.description"
            type="textarea"
            rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleCreateProject">创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const projects = ref([])
const showCreateDialog = ref(false)

const newProject = reactive({
  name: '',
  description: ''
})

const projectActions = reactive([
  { key: 'create', text: '创建项目', type: 'primary', icon: 'Plus' },
  { key: 'import', text: '导入项目', type: 'default', icon: 'Upload' },
  { key: 'template', text: '使用模板', type: 'default', icon: 'Document' }
])

const handleProjectAction = (action) => {
  switch (action.key) {
    case 'create':
      showCreateDialog.value = true
      break
    case 'import':
      ElMessage.info('打开导入项目对话框')
      break
    case 'template':
      ElMessage.info('选择项目模板')
      break
  }
}

const handleCreateProject = () => {
  if (!newProject.name.trim()) {
    ElMessage.warning('请输入项目名称')
    return
  }
  
  // 模拟创建项目
  projects.value.push({
    id: Date.now(),
    name: newProject.name,
    description: newProject.description
  })
  
  showCreateDialog.value = false
  newProject.name = ''
  newProject.description = ''
  ElMessage.success('项目创建成功')
}
</script>

<style scoped>
.page-empty-examples {
  padding: 20px;
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.page-content {
  min-height: 500px;
}
</style>
```

## ElementPlus API支持

FuniEmpty基于el-empty封装，支持所有el-empty的API：

```vue
<template>
  <FuniEmpty
    <!-- ElementPlus el-empty 所有属性 -->
    image=""
    :image-size="200"
    description="暂无数据"
  />
</template>
```

## 注意事项

### 1. 用户体验
- 提供清晰的状态说明和指导
- 合理设置操作按钮和引导
- 避免过于复杂的空状态设计
- 考虑不同场景的用户心理

### 2. 视觉设计
- 保持空状态的视觉一致性
- 合理使用图标和插图
- 注意颜色搭配和对比度
- 适配不同的主题风格

### 3. 交互设计
- 提供有意义的操作选项
- 避免死胡同的用户体验
- 支持快速恢复和重试
- 考虑键盘导航支持

### 4. 内容策略
- 使用友好和鼓励性的文案
- 提供具体的解决方案
- 避免技术性的错误信息
- 考虑国际化和本地化

## 常见问题

### Q: 如何根据不同的业务场景自定义空状态？
A: 使用type属性选择预设类型，或通过插槽完全自定义内容

### Q: 如何在空状态中添加复杂的操作？
A: 使用actions属性配置多个操作按钮，或使用action插槽自定义

### Q: 如何实现空状态的动画效果？
A: 使用animation属性或通过CSS自定义动画效果

### Q: 如何处理空状态的响应式布局？
A: 通过CSS媒体查询调整不同屏幕尺寸下的显示效果
