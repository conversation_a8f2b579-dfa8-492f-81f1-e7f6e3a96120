# FuniEmpty 使用示例

## 基础空状态示例

### 简单空状态
```vue
<template>
  <div class="basic-examples">
    <div class="example-grid">
      <!-- 默认空状态 -->
      <div class="example-item">
        <h4>默认空状态</h4>
        <FuniEmpty description="暂无数据" />
      </div>
      
      <!-- 自定义图片 -->
      <div class="example-item">
        <h4>自定义图片</h4>
        <FuniEmpty 
          image="https://shadow.elemecdn.com/app/element/hamburger.9cf7b091-55e9-11e9-a976-7f4d0b07eef6.png"
          description="没有找到相关内容"
        />
      </div>
      
      <!-- 自定义图标 -->
      <div class="example-item">
        <h4>自定义图标</h4>
        <FuniEmpty 
          icon="DocumentRemove"
          title="暂无文档"
          description="您还没有上传任何文档"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.basic-examples {
  padding: 20px;
}

.example-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.example-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  min-height: 250px;
}

.example-item h4 {
  margin: 0 0 16px 0;
  text-align: center;
  color: #303133;
}
</style>
```

### 不同尺寸的空状态
```vue
<template>
  <div class="size-examples">
    <div class="size-grid">
      <div class="example-item">
        <h4>小尺寸</h4>
        <FuniEmpty 
          size="small"
          icon="Star"
          title="收藏为空"
          description="还没有收藏内容"
        />
      </div>
      
      <div class="example-item">
        <h4>默认尺寸</h4>
        <FuniEmpty 
          size="default"
          icon="User"
          title="用户列表为空"
          description="当前没有用户数据"
        />
      </div>
      
      <div class="example-item">
        <h4>大尺寸</h4>
        <FuniEmpty 
          size="large"
          icon="Folder"
          title="文件夹为空"
          description="这个文件夹还没有任何文件"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.size-examples {
  padding: 20px;
}

.size-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}
</style>
```

## 不同类型的空状态示例

### 业务场景空状态
```vue
<template>
  <div class="type-examples">
    <div class="type-grid">
      <!-- 无数据 -->
      <div class="example-item">
        <h4>无数据</h4>
        <FuniEmpty 
          type="no-data"
          title="暂无数据"
          description="当前列表为空，请添加数据"
          show-action
          action-text="添加数据"
          @action="handleAddData"
        />
      </div>
      
      <!-- 无搜索结果 -->
      <div class="example-item">
        <h4>无搜索结果</h4>
        <FuniEmpty 
          type="no-result"
          title="无搜索结果"
          description="未找到符合条件的内容，请尝试其他关键词"
          show-action
          action-text="重新搜索"
          @action="handleRetrySearch"
        />
      </div>
      
      <!-- 网络错误 -->
      <div class="example-item">
        <h4>网络错误</h4>
        <FuniEmpty 
          type="no-network"
          title="网络连接失败"
          description="请检查网络连接后重试"
          show-action
          action-text="重新加载"
          :action-loading="networkLoading"
          @action="handleRetryNetwork"
        />
      </div>
      
      <!-- 无权限 -->
      <div class="example-item">
        <h4>无权限</h4>
        <FuniEmpty 
          type="no-permission"
          title="暂无访问权限"
          description="您没有权限访问此内容，请联系管理员"
          show-action
          action-text="申请权限"
          @action="handleRequestPermission"
        />
      </div>
      
      <!-- 错误状态 -->
      <div class="example-item">
        <h4>错误状态</h4>
        <FuniEmpty 
          type="error"
          title="加载失败"
          description="数据加载过程中发生错误"
          :actions="errorActions"
          @action="handleErrorAction"
        />
      </div>
      
      <!-- 维护中 -->
      <div class="example-item">
        <h4>维护中</h4>
        <FuniEmpty 
          type="maintenance"
          title="系统维护中"
          description="系统正在维护升级，预计2小时后恢复"
          show-action
          action-text="刷新页面"
          @action="handleRefresh"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const networkLoading = ref(false)

const errorActions = reactive([
  { key: 'retry', label: '重试', type: 'primary', icon: 'Refresh' },
  { key: 'report', label: '反馈问题', type: 'text', icon: 'Warning' }
])

const handleAddData = () => {
  ElMessage.success('跳转到添加数据页面')
}

const handleRetrySearch = () => {
  ElMessage.info('重新搜索')
}

const handleRetryNetwork = async () => {
  networkLoading.value = true
  try {
    // 模拟网络请求
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('重新加载成功')
  } catch (error) {
    ElMessage.error('重新加载失败')
  } finally {
    networkLoading.value = false
  }
}

const handleRequestPermission = () => {
  ElMessage.info('申请权限请求已发送')
}

const handleErrorAction = (action, index) => {
  if (action.key === 'retry') {
    ElMessage.info('正在重试...')
  } else if (action.key === 'report') {
    ElMessage.info('反馈问题')
  }
}

const handleRefresh = () => {
  location.reload()
}
</script>

<style scoped>
.type-examples {
  padding: 20px;
}

.type-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
}

.example-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  min-height: 280px;
}

.example-item h4 {
  margin: 0 0 16px 0;
  text-align: center;
  color: #303133;
}
</style>
```

## 自定义样式示例

### 颜色和样式定制
```vue
<template>
  <div class="custom-examples">
    <div class="custom-grid">
      <!-- 自定义颜色 -->
      <div class="example-item">
        <h4>自定义颜色</h4>
        <FuniEmpty 
          icon="Star"
          icon-color="#f39c12"
          icon-size="80px"
          title="收藏为空"
          title-color="#e67e22"
          description="您还没有收藏任何内容"
          description-color="#95a5a6"
          show-action
          action-text="去收藏"
          action-type="warning"
        />
      </div>
      
      <!-- 自定义背景 -->
      <div class="example-item">
        <h4>自定义背景</h4>
        <FuniEmpty 
          icon="Moon"
          icon-color="#ffffff"
          icon-size="72px"
          title="夜间模式"
          title-color="#ffffff"
          description="当前处于夜间模式"
          description-color="#cccccc"
          background="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
          padding="60px 40px"
        />
      </div>
      
      <!-- 动画效果 -->
      <div class="example-item">
        <h4>动画效果</h4>
        <FuniEmpty 
          icon="Loading"
          icon-size="64px"
          title="加载中..."
          description="正在获取数据，请稍候"
          animation="pulse"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.custom-examples {
  padding: 20px;
}

.custom-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.example-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  min-height: 300px;
}

.example-item h4 {
  margin: 0 0 16px 0;
  text-align: center;
  color: #303133;
}
</style>
```

## 表格和列表中的空状态示例

### 表格空状态
```vue
<template>
  <div class="table-empty-examples">
    <div class="example-section">
      <h4>表格空状态</h4>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="role" label="角色" />
        <template #empty>
          <FuniEmpty
            icon="User"
            title="暂无用户数据"
            description="当前没有用户信息，请添加用户"
            show-action
            action-text="添加用户"
            action-type="primary"
            @action="handleAddUser"
          />
        </template>
      </el-table>
    </div>

    <div class="example-section">
      <h4>搜索结果空状态</h4>
      <div class="search-container">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索用户"
          @input="handleSearch"
        >
          <template #append>
            <el-button icon="Search" @click="handleSearch" />
          </template>
        </el-input>

        <div class="search-results">
          <div v-if="searchResults.length > 0" class="result-list">
            <div
              v-for="result in searchResults"
              :key="result.id"
              class="result-item"
            >
              {{ result.name }} - {{ result.email }}
            </div>
          </div>
          <div v-else class="empty-container">
            <FuniEmpty
              type="no-result"
              title="无搜索结果"
              description="未找到匹配的用户，请尝试其他关键词"
              :actions="searchActions"
              @action="handleSearchAction"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const tableData = ref([])
const searchKeyword = ref('')
const searchResults = ref([])

const searchActions = reactive([
  { key: 'clear', label: '清空搜索', type: 'text' },
  { key: 'advanced', label: '高级搜索', type: 'primary' }
])

const handleAddUser = () => {
  ElMessage.success('跳转到添加用户页面')
}

const handleSearch = () => {
  // 模拟搜索
  if (searchKeyword.value.trim()) {
    searchResults.value = []
  }
}

const handleSearchAction = (action, index) => {
  if (action.key === 'clear') {
    searchKeyword.value = ''
    searchResults.value = []
  } else if (action.key === 'advanced') {
    ElMessage.info('打开高级搜索')
  }
}
</script>

<style scoped>
.table-empty-examples {
  padding: 20px;
}

.example-section {
  margin-bottom: 40px;
}

.example-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
}

.search-container {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
}

.search-results {
  margin-top: 16px;
  min-height: 200px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.result-list {
  padding: 16px;
}

.empty-container {
  padding: 20px;
}

.result-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.result-item:last-child {
  border-bottom: none;
}
</style>
```

## 页面级空状态示例

### 完整页面空状态
```vue
<template>
  <div class="page-empty-examples">
    <div class="page-container">
      <div class="page-header">
        <h2>我的项目</h2>
        <el-button type="primary" @click="showCreateDialog = true">
          新建项目
        </el-button>
      </div>

      <div class="page-content">
        <FuniEmpty
          v-if="projects.length === 0"
          type="no-data"
          title="还没有项目"
          description="创建您的第一个项目，开始您的工作"
          :actions="projectActions"
          min-height="400px"
          @action="handleProjectAction"
        />
        <div v-else class="project-list">
          <!-- 项目列表内容 -->
        </div>
      </div>
    </div>

    <!-- 创建项目对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建项目"
      width="500px"
    >
      <el-form :model="newProject" label-width="80px">
        <el-form-item label="项目名称">
          <el-input v-model="newProject.name" />
        </el-form-item>
        <el-form-item label="项目描述">
          <el-input
            v-model="newProject.description"
            type="textarea"
            rows="3"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleCreateProject">创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const projects = ref([])
const showCreateDialog = ref(false)

const newProject = reactive({
  name: '',
  description: ''
})

const projectActions = reactive([
  { key: 'create', label: '创建项目', type: 'primary', icon: 'Plus' },
  { key: 'import', label: '导入项目', type: 'default', icon: 'Upload' },
  { key: 'template', label: '使用模板', type: 'text', icon: 'Document' }
])

const handleProjectAction = (action, index) => {
  switch (action.key) {
    case 'create':
      showCreateDialog.value = true
      break
    case 'import':
      ElMessage.info('导入项目功能')
      break
    case 'template':
      ElMessage.info('选择项目模板')
      break
  }
}

const handleCreateProject = () => {
  if (!newProject.name.trim()) {
    ElMessage.warning('请输入项目名称')
    return
  }

  // 模拟创建项目
  projects.value.push({
    id: Date.now(),
    name: newProject.name,
    description: newProject.description
  })

  // 重置表单
  newProject.name = ''
  newProject.description = ''
  showCreateDialog.value = false

  ElMessage.success('项目创建成功')
}
</script>

<style scoped>
.page-empty-examples {
  padding: 20px;
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.page-content {
  min-height: 500px;
}

.project-list {
  /* 项目列表样式 */
}
</style>
```

## 自定义插槽示例

### 完全自定义空状态
```vue
<template>
  <div class="slot-examples">
    <!-- 自定义图标插槽 -->
    <div class="example-item">
      <h4>自定义图标</h4>
      <FuniEmpty title="自定义图标" description="使用插槽自定义图标">
        <template #icon>
          <div class="custom-icon">
            <el-icon :size="64" color="#409eff">
              <Trophy />
            </el-icon>
          </div>
        </template>
      </FuniEmpty>
    </div>

    <!-- 自定义标题和描述 -->
    <div class="example-item">
      <h4>自定义标题和描述</h4>
      <FuniEmpty>
        <template #title>
          <h3 class="custom-title">🎉 恭喜您！</h3>
        </template>
        <template #description>
          <div class="custom-description">
            <p>您已经完成了所有任务</p>
            <p>现在可以休息一下了</p>
          </div>
        </template>
      </FuniEmpty>
    </div>

    <!-- 自定义操作区域 -->
    <div class="example-item">
      <h4>自定义操作区域</h4>
      <FuniEmpty
        icon="Gift"
        title="特殊优惠"
        description="限时活动，不要错过"
      >
        <template #action>
          <div class="custom-actions">
            <el-button type="primary" size="large">
              <el-icon><Star /></el-icon>
              立即参与
            </el-button>
            <el-button type="text" @click="handleLearnMore">
              了解更多
            </el-button>
          </div>
        </template>
      </FuniEmpty>
    </div>

    <!-- 完全自定义内容 -->
    <div class="example-item">
      <h4>完全自定义</h4>
      <FuniEmpty>
        <div class="completely-custom">
          <div class="custom-animation">
            <div class="floating-icon">
              <el-icon :size="48"><Sunny /></el-icon>
            </div>
          </div>
          <h3>美好的一天</h3>
          <p>今天是个好日子，适合做任何事情</p>
          <div class="weather-info">
            <span>☀️ 晴天</span>
            <span>🌡️ 25°C</span>
            <span>💨 微风</span>
          </div>
          <el-button type="primary" round>
            开始新的一天
          </el-button>
        </div>
      </FuniEmpty>
    </div>
  </div>
</template>

<script setup>
import { Trophy, Star, Sunny } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const handleLearnMore = () => {
  ElMessage.info('了解更多信息')
}
</script>

<style scoped>
.slot-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px;
}

.example-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  min-height: 300px;
}

.example-item h4 {
  margin: 0 0 16px 0;
  text-align: center;
  color: #303133;
}

.custom-icon {
  margin-bottom: 16px;
}

.custom-title {
  margin: 0 0 16px 0;
  color: #409eff;
  font-size: 20px;
}

.custom-description {
  margin-bottom: 20px;
}

.custom-description p {
  margin: 4px 0;
  color: #606266;
}

.custom-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.completely-custom {
  text-align: center;
  padding: 20px;
}

.custom-animation {
  margin-bottom: 20px;
}

.floating-icon {
  animation: float 3s ease-in-out infinite;
  color: #f39c12;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.completely-custom h3 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 18px;
}

.completely-custom p {
  margin: 0 0 16px 0;
  color: #606266;
}

.weather-info {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 20px;
  font-size: 14px;
}

.weather-info span {
  padding: 4px 8px;
  background: #f5f7fa;
  border-radius: 12px;
}
</style>
```

## 响应式和移动端示例

### 响应式空状态
```vue
<template>
  <div class="responsive-examples">
    <div class="responsive-container">
      <h4>响应式空状态</h4>
      <FuniEmpty
        :icon-size="responsiveIconSize"
        :title-size="responsiveTitleSize"
        :description-size="responsiveDescSize"
        title="响应式设计"
        description="这个空状态会根据屏幕尺寸自动调整"
        :padding="responsivePadding"
        show-action
        action-text="了解更多"
        :action-size="responsiveActionSize"
      />
    </div>

    <div class="mobile-container">
      <h4>移动端优化</h4>
      <FuniEmpty
        class="mobile-empty"
        icon="MobilePhone"
        icon-size="56px"
        title="移动端优化"
        description="专为移动设备优化的空状态设计"
        :actions="mobileActions"
        @action="handleMobileAction"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'

const screenWidth = ref(window.innerWidth)

const responsiveIconSize = computed(() => {
  if (screenWidth.value < 768) return '48px'
  if (screenWidth.value < 1024) return '64px'
  return '80px'
})

const responsiveTitleSize = computed(() => {
  if (screenWidth.value < 768) return '14px'
  if (screenWidth.value < 1024) return '16px'
  return '18px'
})

const responsiveDescSize = computed(() => {
  if (screenWidth.value < 768) return '12px'
  if (screenWidth.value < 1024) return '14px'
  return '16px'
})

const responsivePadding = computed(() => {
  if (screenWidth.value < 768) return '20px 16px'
  if (screenWidth.value < 1024) return '30px 20px'
  return '40px 24px'
})

const responsiveActionSize = computed(() => {
  if (screenWidth.value < 768) return 'small'
  return 'default'
})

const mobileActions = [
  { key: 'download', label: '下载APP', type: 'primary', icon: 'Download' },
  { key: 'browser', label: '继续使用浏览器', type: 'text' }
]

const handleResize = () => {
  screenWidth.value = window.innerWidth
}

const handleMobileAction = (action, index) => {
  if (action.key === 'download') {
    ElMessage.success('跳转到应用商店')
  } else if (action.key === 'browser') {
    ElMessage.info('继续使用浏览器版本')
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.responsive-examples {
  padding: 20px;
}

.responsive-container,
.mobile-container {
  margin-bottom: 40px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
}

.responsive-container h4,
.mobile-container h4 {
  margin: 0 0 20px 0;
  text-align: center;
  color: #303133;
}

.mobile-empty {
  /* 移动端特殊样式 */
}

/* 移动端媒体查询 */
@media (max-width: 768px) {
  .responsive-examples {
    padding: 12px;
  }

  .responsive-container,
  .mobile-container {
    padding: 16px;
    margin-bottom: 20px;
  }

  .mobile-empty :deep(.el-empty__description) {
    font-size: 12px;
    line-height: 1.4;
  }

  .mobile-empty :deep(.el-button) {
    width: 100%;
    margin-bottom: 8px;
  }
}

/* 平板端媒体查询 */
@media (min-width: 769px) and (max-width: 1024px) {
  .responsive-examples {
    padding: 16px;
  }
}
</style>
```
