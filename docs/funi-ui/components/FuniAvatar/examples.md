# FuniAvatar 使用示例

## 基础使用示例

### 1. 图片头像

```vue
<template>
  <div class="avatar-demo">
    <h3>不同尺寸的图片头像</h3>
    <div class="avatar-row">
      <FuniAvatar :size="30" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
      <FuniAvatar :size="40" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
      <FuniAvatar size="default" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
      <FuniAvatar :size="60" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
      <FuniAvatar :size="80" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
    </div>
    
    <h3>不同形状的头像</h3>
    <div class="avatar-row">
      <FuniAvatar 
        shape="circle" 
        :size="50"
        src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" 
      />
      <FuniAvatar 
        shape="square" 
        :size="50"
        src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" 
      />
    </div>
  </div>
</template>

<style scoped>
.avatar-demo {
  padding: 20px;
}

.avatar-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 16px 0;
}

h3 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 16px;
}
</style>
```

### 2. 文字头像

```vue
<template>
  <div class="text-avatar-demo">
    <h3>文字头像示例</h3>
    <div class="avatar-row">
      <FuniAvatar 
        text="张" 
        background-color="#f56c6c" 
        text-color="#ffffff"
        :size="40"
      />
      <FuniAvatar 
        text="李" 
        background-color="#67c23a" 
        text-color="#ffffff"
        :size="40"
      />
      <FuniAvatar 
        text="王" 
        background-color="#e6a23c" 
        text-color="#ffffff"
        :size="40"
      />
      <FuniAvatar 
        text="Admin" 
        background-color="#409eff" 
        text-color="#ffffff"
        :size="40"
      />
    </div>
    
    <h3>英文名称头像</h3>
    <div class="avatar-row">
      <FuniAvatar 
        text="JD" 
        background-color="#909399" 
        text-color="#ffffff"
        :size="40"
      />
      <FuniAvatar 
        text="AB" 
        background-color="#606266" 
        text-color="#ffffff"
        :size="40"
      />
      <FuniAvatar 
        text="CD" 
        background-color="#303133" 
        text-color="#ffffff"
        :size="40"
      />
    </div>
  </div>
</template>

<style scoped>
.text-avatar-demo {
  padding: 20px;
}

.avatar-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 16px 0;
}

h3 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 16px;
}
</style>
```

### 3. 图标头像

```vue
<template>
  <div class="icon-avatar-demo">
    <h3>图标头像示例</h3>
    <div class="avatar-row">
      <FuniAvatar 
        icon="User" 
        background-color="#909399" 
        :size="40"
      />
      <FuniAvatar 
        icon="Avatar" 
        background-color="#409eff" 
        :size="40"
      />
      <FuniAvatar 
        icon="UserFilled" 
        background-color="#67c23a" 
        :size="40"
      />
      <FuniAvatar 
        icon="Setting" 
        background-color="#e6a23c" 
        :size="40"
      />
    </div>
  </div>
</template>

<style scoped>
.icon-avatar-demo {
  padding: 20px;
}

.avatar-row {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 16px 0;
}

h3 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 16px;
}
</style>
```

## 高级功能示例

### 4. 状态指示头像

```vue
<template>
  <div class="status-avatar-demo">
    <h3>在线状态指示</h3>
    <div class="avatar-row">
      <FuniAvatar 
        src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        :size="50"
        show-status
        status="online"
        tooltip="在线"
      />
      <FuniAvatar 
        src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        :size="50"
        show-status
        status="offline"
        tooltip="离线"
      />
      <FuniAvatar 
        src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        :size="50"
        show-status
        status="busy"
        tooltip="忙碌"
      />
      <FuniAvatar 
        src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        :size="50"
        show-status
        status="away"
        tooltip="离开"
      />
      <FuniAvatar 
        src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        :size="50"
        show-status
        status="dnd"
        tooltip="勿扰"
      />
    </div>
    
    <h3>自定义状态位置</h3>
    <div class="avatar-row">
      <FuniAvatar 
        src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        :size="60"
        show-status
        status="online"
        status-position="top-right"
        tooltip="状态在右上角"
      />
      <FuniAvatar 
        src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        :size="60"
        show-status
        status="online"
        status-position="top-left"
        tooltip="状态在左上角"
      />
      <FuniAvatar 
        src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        :size="60"
        show-status
        status="online"
        status-position="bottom-left"
        tooltip="状态在左下角"
      />
      <FuniAvatar 
        src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        :size="60"
        show-status
        status="online"
        status-position="bottom-right"
        tooltip="状态在右下角"
      />
    </div>
  </div>
</template>

<style scoped>
.status-avatar-demo {
  padding: 20px;
}

.avatar-row {
  display: flex;
  align-items: center;
  gap: 20px;
  margin: 16px 0;
}

h3 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 16px;
}
</style>
```

### 5. 徽章头像

```vue
<template>
  <div class="badge-avatar-demo">
    <h3>消息徽章</h3>
    <div class="avatar-row">
      <FuniAvatar 
        src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        :size="50"
        show-badge
        badge="5"
        badge-type="danger"
      />
      <FuniAvatar 
        src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        :size="50"
        show-badge
        badge="99+"
        badge-type="warning"
      />
      <FuniAvatar 
        src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        :size="50"
        show-badge
        badge="NEW"
        badge-type="success"
      />
      <FuniAvatar 
        src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        :size="50"
        show-badge
        badge="•"
        badge-type="info"
      />
    </div>
    
    <h3>状态 + 徽章组合</h3>
    <div class="avatar-row">
      <FuniAvatar 
        src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        :size="60"
        show-status
        status="online"
        show-badge
        badge="3"
        badge-position="top-right"
        status-position="bottom-right"
      />
      <FuniAvatar 
        src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        :size="60"
        show-status
        status="busy"
        show-badge
        badge="12"
        badge-position="top-left"
        status-position="bottom-right"
      />
    </div>
  </div>
</template>

<style scoped>
.badge-avatar-demo {
  padding: 20px;
}

.avatar-row {
  display: flex;
  align-items: center;
  gap: 20px;
  margin: 16px 0;
}

h3 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 16px;
}
</style>
```

### 6. 可交互头像

```vue
<template>
  <div class="interactive-avatar-demo">
    <h3>可点击头像</h3>
    <div class="avatar-row">
      <FuniAvatar 
        src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
        :size="50"
        clickable
        hoverable
        tooltip="点击查看详情"
        @click="handleAvatarClick('user1')"
      />
      <FuniAvatar 
        text="管理员"
        :size="50"
        clickable
        hoverable
        background-color="#409eff"
        tooltip="管理员"
        @click="handleAvatarClick('admin')"
      />
    </div>
    
    <h3>头像上传</h3>
    <div class="avatar-row">
      <FuniAvatar 
        :src="uploadedAvatar"
        :size="80"
        uploadable
        :upload-config="uploadConfig"
        placeholder="点击上传头像"
        @upload-success="handleUploadSuccess"
        @upload-error="handleUploadError"
      />
    </div>
    
    <div class="upload-tips">
      <p>支持 jpg、png 格式，文件大小不超过 2MB</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const uploadedAvatar = ref('')

const uploadConfig = reactive({
  action: '/api/upload/avatar',
  accept: 'image/*',
  maxSize: 2,
  beforeUpload: (file) => {
    const isImage = file.type.startsWith('image/')
    const isLt2M = file.size / 1024 / 1024 < 2
    
    if (!isImage) {
      ElMessage.error('只能上传图片文件!')
      return false
    }
    if (!isLt2M) {
      ElMessage.error('图片大小不能超过 2MB!')
      return false
    }
    return true
  }
})

const handleAvatarClick = (userId) => {
  console.log('点击头像:', userId)
  ElMessage.info(`查看用户 ${userId} 的详情`)
}

const handleUploadSuccess = (response, file) => {
  console.log('上传成功:', response, file)
  uploadedAvatar.value = response.url
  ElMessage.success('头像上传成功')
}

const handleUploadError = (error, file) => {
  console.error('上传失败:', error, file)
  ElMessage.error('头像上传失败')
}
</script>

<style scoped>
.interactive-avatar-demo {
  padding: 20px;
}

.avatar-row {
  display: flex;
  align-items: center;
  gap: 20px;
  margin: 16px 0;
}

h3 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 16px;
}

.upload-tips {
  margin-top: 10px;
  color: #909399;
  font-size: 12px;
}
</style>
```

## 业务场景示例

### 7. 用户列表头像

```vue
<template>
  <div class="user-list-demo">
    <h3>用户列表</h3>
    <div class="user-list">
      <div
        v-for="user in userList"
        :key="user.id"
        class="user-item"
        @click="selectUser(user)"
        :class="{ active: selectedUser?.id === user.id }"
      >
        <FuniAvatar
          :src="user.avatar"
          :text="user.name.charAt(0)"
          :size="40"
          show-status
          :status="user.status"
          show-badge
          :badge="user.unreadCount"
          :badge-type="user.unreadCount > 0 ? 'danger' : 'info'"
        />
        <div class="user-info">
          <div class="user-name">{{ user.name }}</div>
          <div class="user-desc">{{ user.description }}</div>
        </div>
        <div class="user-time">{{ user.lastActiveTime }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const selectedUser = ref(null)

const userList = reactive([
  {
    id: 1,
    name: '张三',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    status: 'online',
    unreadCount: 3,
    description: '前端开发工程师',
    lastActiveTime: '2分钟前'
  },
  {
    id: 2,
    name: '李四',
    avatar: '',
    status: 'busy',
    unreadCount: 0,
    description: '后端开发工程师',
    lastActiveTime: '10分钟前'
  },
  {
    id: 3,
    name: '王五',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    status: 'away',
    unreadCount: 1,
    description: '产品经理',
    lastActiveTime: '1小时前'
  },
  {
    id: 4,
    name: '赵六',
    avatar: '',
    status: 'offline',
    unreadCount: 0,
    description: '设计师',
    lastActiveTime: '昨天'
  }
])

const selectUser = (user) => {
  selectedUser.value = user
}
</script>

<style scoped>
.user-list-demo {
  padding: 20px;
}

.user-list {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.user-item:hover {
  background-color: #f5f7fa;
}

.user-item.active {
  background-color: #ecf5ff;
}

.user-item:last-child {
  border-bottom: none;
}

.user-info {
  flex: 1;
  margin-left: 12px;
}

.user-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.user-desc {
  font-size: 12px;
  color: #909399;
}

.user-time {
  font-size: 12px;
  color: #c0c4cc;
}

h3 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 16px;
}
</style>
```

### 8. 团队成员头像组

```vue
<template>
  <div class="team-avatar-demo">
    <h3>团队成员展示</h3>
    <div class="team-section">
      <h4>开发团队</h4>
      <div class="avatar-group">
        <FuniAvatar
          v-for="(member, index) in devTeam"
          :key="member.id"
          :src="member.avatar"
          :text="member.name.charAt(0)"
          :size="40"
          :tooltip="member.name + ' - ' + member.role"
          :style="{
            marginLeft: index > 0 ? '-8px' : '0',
            zIndex: devTeam.length - index,
            border: '2px solid #fff'
          }"
          clickable
          @click="handleMemberClick(member)"
        />
        <FuniAvatar
          text="+3"
          :size="40"
          background-color="#f0f0f0"
          text-color="#666"
          :style="{
            marginLeft: '-8px',
            zIndex: 0,
            border: '2px solid #fff'
          }"
          clickable
          tooltip="查看更多成员"
          @click="showMoreMembers"
        />
      </div>
    </div>

    <div class="team-section">
      <h4>产品团队</h4>
      <div class="avatar-group">
        <FuniAvatar
          v-for="(member, index) in productTeam"
          :key="member.id"
          :src="member.avatar"
          :text="member.name.charAt(0)"
          :size="40"
          :tooltip="member.name + ' - ' + member.role"
          :style="{
            marginLeft: index > 0 ? '-8px' : '0',
            zIndex: productTeam.length - index,
            border: '2px solid #fff'
          }"
          clickable
          show-status
          :status="member.status"
          @click="handleMemberClick(member)"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { ElMessage } from 'element-plus'

const devTeam = reactive([
  {
    id: 1,
    name: '张三',
    role: '前端开发',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    status: 'online'
  },
  {
    id: 2,
    name: '李四',
    role: '后端开发',
    avatar: '',
    status: 'busy'
  },
  {
    id: 3,
    name: '王五',
    role: '全栈开发',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    status: 'online'
  },
  {
    id: 4,
    name: '赵六',
    role: '测试工程师',
    avatar: '',
    status: 'away'
  }
])

const productTeam = reactive([
  {
    id: 5,
    name: '钱七',
    role: '产品经理',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    status: 'online'
  },
  {
    id: 6,
    name: '孙八',
    role: 'UI设计师',
    avatar: '',
    status: 'online'
  }
])

const handleMemberClick = (member) => {
  ElMessage.info(`查看 ${member.name} 的详细信息`)
}

const showMoreMembers = () => {
  ElMessage.info('显示更多团队成员')
}
</script>

<style scoped>
.team-avatar-demo {
  padding: 20px;
}

.team-section {
  margin-bottom: 24px;
}

.avatar-group {
  display: flex;
  align-items: center;
}

h3 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 16px;
}

h4 {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}
</style>
```

### 9. 评论系统头像

```vue
<template>
  <div class="comment-avatar-demo">
    <h3>评论列表</h3>
    <div class="comment-list">
      <div
        v-for="comment in comments"
        :key="comment.id"
        class="comment-item"
      >
        <FuniAvatar
          :src="comment.user.avatar"
          :text="comment.user.name.charAt(0)"
          :size="36"
          clickable
          :tooltip="comment.user.name"
          @click="viewUserProfile(comment.user)"
        />
        <div class="comment-content">
          <div class="comment-header">
            <span class="user-name">{{ comment.user.name }}</span>
            <span class="comment-time">{{ comment.time }}</span>
          </div>
          <div class="comment-text">{{ comment.content }}</div>
          <div class="comment-actions">
            <button class="action-btn">回复</button>
            <button class="action-btn">点赞</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { ElMessage } from 'element-plus'

const comments = reactive([
  {
    id: 1,
    user: {
      id: 1,
      name: '张三',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    },
    content: '这个功能很不错，使用起来很方便！',
    time: '2小时前'
  },
  {
    id: 2,
    user: {
      id: 2,
      name: '李四',
      avatar: ''
    },
    content: '同意楼上的观点，确实很实用。',
    time: '1小时前'
  },
  {
    id: 3,
    user: {
      id: 3,
      name: '王五',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    },
    content: '希望能增加更多的自定义选项。',
    time: '30分钟前'
  }
])

const viewUserProfile = (user) => {
  ElMessage.info(`查看 ${user.name} 的个人资料`)
}
</script>

<style scoped>
.comment-avatar-demo {
  padding: 20px;
}

.comment-list {
  max-width: 600px;
}

.comment-item {
  display: flex;
  margin-bottom: 16px;
  padding: 12px;
  background: #f9f9f9;
  border-radius: 8px;
}

.comment-content {
  flex: 1;
  margin-left: 12px;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.user-name {
  font-weight: 500;
  color: #303133;
  margin-right: 12px;
}

.comment-time {
  font-size: 12px;
  color: #909399;
}

.comment-text {
  color: #606266;
  line-height: 1.5;
  margin-bottom: 8px;
}

.comment-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  background: none;
  border: none;
  color: #409eff;
  cursor: pointer;
  font-size: 12px;
  padding: 0;
}

.action-btn:hover {
  text-decoration: underline;
}

h3 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 16px;
}
</style>
```
