# FuniAvatar 最佳实践

## 推荐用法和配置

### 1. 尺寸选择最佳实践

#### 不同场景的推荐尺寸

```vue
<template>
  <div class="size-practices">
    <!-- 导航栏用户头像：28-32px -->
    <div class="navbar">
      <FuniAvatar 
        :size="28"
        src="user-avatar.jpg"
        clickable
        tooltip="个人中心"
      />
    </div>
    
    <!-- 列表项头像：32-40px -->
    <div class="user-list-item">
      <FuniAvatar 
        :size="36"
        src="user-avatar.jpg"
        show-status
        status="online"
      />
      <span class="user-name">张三</span>
    </div>
    
    <!-- 卡片头像：48-56px -->
    <div class="user-card">
      <FuniAvatar 
        :size="52"
        src="user-avatar.jpg"
        show-badge
        badge="VIP"
        badge-type="warning"
      />
    </div>
    
    <!-- 详情页头像：60-80px -->
    <div class="user-profile">
      <FuniAvatar 
        :size="72"
        src="user-avatar.jpg"
        uploadable
        :upload-config="uploadConfig"
      />
    </div>
  </div>
</template>
```

#### 响应式尺寸设计

```vue
<template>
  <div class="responsive-avatar">
    <FuniAvatar 
      :size="avatarSize"
      src="user-avatar.jpg"
      class="responsive-size"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useBreakpoints } from '@vueuse/core'

const breakpoints = useBreakpoints({
  mobile: 768,
  tablet: 1024,
  desktop: 1280
})

const avatarSize = computed(() => {
  if (breakpoints.mobile.value) return 32
  if (breakpoints.tablet.value) return 40
  return 48
})
</script>

<style scoped>
.responsive-size {
  transition: all 0.3s ease;
}
</style>
```

### 2. 状态指示最佳实践

#### 合理的状态使用

```vue
<template>
  <div class="status-practices">
    <!-- 即时通讯场景 -->
    <div class="chat-user">
      <FuniAvatar 
        :src="user.avatar"
        :size="40"
        show-status
        :status="user.onlineStatus"
        :tooltip="getStatusText(user.onlineStatus)"
        status-position="bottom-right"
      />
    </div>
    
    <!-- 团队协作场景 -->
    <div class="team-member">
      <FuniAvatar 
        :src="member.avatar"
        :size="36"
        show-status
        :status="member.workStatus"
        :tooltip="`${member.name} - ${getWorkStatusText(member.workStatus)}`"
      />
    </div>
  </div>
</template>

<script setup>
const getStatusText = (status) => {
  const statusMap = {
    online: '在线',
    offline: '离线',
    busy: '忙碌',
    away: '离开',
    dnd: '勿扰'
  }
  return statusMap[status] || '未知状态'
}

const getWorkStatusText = (status) => {
  const workStatusMap = {
    online: '工作中',
    busy: '忙碌中',
    away: '暂时离开',
    offline: '已下班'
  }
  return workStatusMap[status] || '状态未知'
}
</script>
```

#### 避免状态滥用

```vue
<!-- ❌ 不推荐：在不需要实时状态的场景使用 -->
<template>
  <div class="article-author">
    <!-- 文章作者不需要显示在线状态 -->
    <FuniAvatar 
      :src="author.avatar"
      :size="32"
      <!-- 不要添加 show-status -->
    />
    <span>{{ author.name }}</span>
  </div>
</template>

<!-- ✅ 推荐：只在需要实时状态的场景使用 -->
<template>
  <div class="online-users">
    <FuniAvatar 
      v-for="user in onlineUsers"
      :key="user.id"
      :src="user.avatar"
      :size="32"
      show-status
      status="online"
      :tooltip="`${user.name} 在线`"
    />
  </div>
</template>
```

### 3. 徽章使用最佳实践

#### 数字徽章的合理使用

```vue
<template>
  <div class="badge-practices">
    <!-- 消息通知徽章 -->
    <FuniAvatar 
      :src="user.avatar"
      :size="40"
      show-badge
      :badge="user.unreadCount"
      :badge-type="user.unreadCount > 0 ? 'danger' : 'info'"
      :badge-max="99"
      badge-position="top-right"
    />
    
    <!-- 角色标识徽章 -->
    <FuniAvatar 
      :src="admin.avatar"
      :size="40"
      show-badge
      badge="ADMIN"
      badge-type="warning"
      badge-position="top-right"
    />
    
    <!-- VIP 标识徽章 -->
    <FuniAvatar 
      :src="vipUser.avatar"
      :size="40"
      show-badge
      badge="VIP"
      badge-type="success"
      badge-position="top-right"
    />
  </div>
</template>
```

#### 徽章位置避免冲突

```vue
<template>
  <div class="badge-position-practice">
    <!-- 状态 + 徽章组合，避免位置重叠 -->
    <FuniAvatar 
      :src="user.avatar"
      :size="50"
      show-status
      status="online"
      status-position="bottom-right"
      show-badge
      :badge="user.messageCount"
      badge-type="danger"
      badge-position="top-right"
    />
  </div>
</template>
```

### 4. 交互设计最佳实践

#### 点击反馈设计

```vue
<template>
  <div class="interactive-practices">
    <!-- 提供明确的点击反馈 -->
    <FuniAvatar 
      :src="user.avatar"
      :size="40"
      clickable
      hoverable
      :tooltip="user.name"
      class="clickable-avatar"
      @click="handleUserClick(user)"
    />
  </div>
</template>

<style scoped>
.clickable-avatar {
  cursor: pointer;
  transition: all 0.2s ease;
}

.clickable-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.clickable-avatar:active {
  transform: scale(0.98);
}
</style>
```

#### 键盘导航支持

```vue
<template>
  <div class="keyboard-navigation">
    <FuniAvatar 
      :src="user.avatar"
      :size="40"
      clickable
      tabindex="0"
      @click="handleClick"
      @keydown.enter="handleClick"
      @keydown.space.prevent="handleClick"
    />
  </div>
</template>
```

### 5. 上传功能最佳实践

#### 完整的上传配置

```vue
<template>
  <div class="upload-practices">
    <FuniAvatar 
      :src="userAvatar"
      :size="80"
      uploadable
      :upload-config="uploadConfig"
      placeholder="点击上传头像"
      @upload-success="handleUploadSuccess"
      @upload-error="handleUploadError"
      @upload-progress="handleUploadProgress"
    />
    
    <!-- 上传进度显示 -->
    <div v-if="uploading" class="upload-progress">
      <el-progress :percentage="uploadProgress" />
      <p>正在上传头像...</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const userAvatar = ref('')
const uploading = ref(false)
const uploadProgress = ref(0)

const uploadConfig = reactive({
  action: '/api/upload/avatar',
  accept: 'image/jpeg,image/png,image/gif',
  maxSize: 2, // 2MB
  headers: {
    'Authorization': `Bearer ${getToken()}`
  },
  beforeUpload: (file) => {
    // 文件类型检查
    const isImage = ['image/jpeg', 'image/png', 'image/gif'].includes(file.type)
    if (!isImage) {
      ElMessage.error('只能上传 JPG、PNG、GIF 格式的图片!')
      return false
    }
    
    // 文件大小检查
    const isLt2M = file.size / 1024 / 1024 < 2
    if (!isLt2M) {
      ElMessage.error('图片大小不能超过 2MB!')
      return false
    }
    
    uploading.value = true
    uploadProgress.value = 0
    return true
  }
})

const handleUploadSuccess = (response, file) => {
  userAvatar.value = response.url
  uploading.value = false
  uploadProgress.value = 100
  ElMessage.success('头像上传成功!')
}

const handleUploadError = (error, file) => {
  uploading.value = false
  uploadProgress.value = 0
  ElMessage.error('头像上传失败，请重试!')
}

const handleUploadProgress = (event, file) => {
  uploadProgress.value = Math.round((event.loaded / event.total) * 100)
}

const getToken = () => {
  // 获取用户认证token
  return localStorage.getItem('token')
}
</script>
```

### 6. 性能优化最佳实践

#### 懒加载实现

```vue
<template>
  <div class="lazy-loading-practice">
    <!-- 大量头像列表使用懒加载 -->
    <div 
      v-for="user in userList"
      :key="user.id"
      class="user-item"
    >
      <FuniAvatar 
        :src="user.avatar"
        :size="40"
        lazy
        :placeholder="getPlaceholderAvatar(user.name)"
        :alt="`${user.name}的头像`"
      />
      <span>{{ user.name }}</span>
    </div>
  </div>
</template>

<script setup>
const getPlaceholderAvatar = (name) => {
  // 生成基于用户名的占位头像
  const firstChar = name.charAt(0).toUpperCase()
  return `data:image/svg+xml,${encodeURIComponent(`
    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40">
      <rect width="40" height="40" fill="#409eff"/>
      <text x="20" y="25" text-anchor="middle" fill="white" font-size="16">${firstChar}</text>
    </svg>
  `)}`
}
</script>
```

#### 图片缓存策略

```vue
<template>
  <div class="cache-practice">
    <FuniAvatar 
      :src="getCachedAvatarUrl(user.avatar)"
      :size="40"
      :alt="user.name"
    />
  </div>
</template>

<script setup>
const getCachedAvatarUrl = (originalUrl) => {
  if (!originalUrl) return ''
  
  // 添加缓存参数，提高缓存命中率
  const url = new URL(originalUrl)
  url.searchParams.set('v', getAvatarVersion(originalUrl))
  return url.toString()
}

const getAvatarVersion = (url) => {
  // 基于URL生成版本号，用于缓存控制
  return btoa(url).slice(0, 8)
}
</script>
```

## 避免的用法和常见错误

### 1. 尺寸使用错误

```vue
<!-- ❌ 错误：尺寸过大影响布局 -->
<template>
  <div class="user-list-item">
    <FuniAvatar :size="80" src="avatar.jpg" />
    <span>用户名</span>
  </div>
</template>

<!-- ✅ 正确：合适的尺寸 -->
<template>
  <div class="user-list-item">
    <FuniAvatar :size="36" src="avatar.jpg" />
    <span>用户名</span>
  </div>
</template>
```

### 2. 状态指示错误

```vue
<!-- ❌ 错误：状态与徽章位置重叠 -->
<template>
  <FuniAvatar 
    src="avatar.jpg"
    show-status
    status-position="top-right"
    show-badge
    badge-position="top-right"
  />
</template>

<!-- ✅ 正确：避免位置冲突 -->
<template>
  <FuniAvatar 
    src="avatar.jpg"
    show-status
    status-position="bottom-right"
    show-badge
    badge-position="top-right"
  />
</template>
```

### 3. 无障碍访问错误

```vue
<!-- ❌ 错误：缺少alt属性 -->
<template>
  <FuniAvatar src="avatar.jpg" clickable />
</template>

<!-- ✅ 正确：提供完整的无障碍支持 -->
<template>
  <FuniAvatar 
    src="avatar.jpg"
    :alt="`${user.name}的头像`"
    clickable
    :tooltip="user.name"
    tabindex="0"
    @keydown.enter="handleClick"
  />
</template>
```

### 4. 上传配置错误

```vue
<!-- ❌ 错误：缺少必要的验证 -->
<template>
  <FuniAvatar 
    uploadable
    :upload-config="{ action: '/upload' }"
  />
</template>

<!-- ✅ 正确：完整的上传配置 -->
<template>
  <FuniAvatar 
    uploadable
    :upload-config="{
      action: '/upload',
      accept: 'image/*',
      maxSize: 2,
      beforeUpload: validateFile
    }"
    @upload-error="handleError"
  />
</template>
```

## 性能优化建议

### 1. 图片优化

- 使用适当的图片格式（WebP > JPEG > PNG）
- 提供多种尺寸的图片资源
- 实现图片懒加载
- 使用 CDN 加速图片加载

### 2. 组件优化

- 避免不必要的状态更新
- 合理使用 v-memo 缓存渲染结果
- 大量头像列表使用虚拟滚动
- 预加载关键用户的头像

### 3. 内存管理

- 及时清理不再使用的头像缓存
- 避免内存泄漏
- 合理设置图片缓存策略

## 业务场景最佳实践

### 1. 用户管理系统

- 列表页使用 32-36px 头像
- 详情页使用 72-80px 头像
- 支持头像上传和裁剪
- 提供默认头像生成

### 2. 即时通讯系统

- 显示在线状态
- 支持消息徽章
- 快速用户识别
- 群组头像展示

### 3. 团队协作平台

- 团队成员状态展示
- 权限角色标识
- 头像组合展示
- 活跃度指示

### 4. 社交平台

- 多尺寸头像适配
- 个性化头像设置
- 头像认证标识
- 社交关系展示
