# FuniAvatar API文档

## 组件概述

FuniAvatar是基于ElementPlus的el-avatar封装的头像组件，支持图片头像、文字头像、图标头像等多种类型，提供了状态指示、徽章显示、点击交互、头像上传等功能，适用于用户信息展示、评论系统、团队成员展示等场景。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 | ElementPlus对应 |
|--------|------|--------|------|------|----------------|
| size | Number/String | 'default' | - | 头像尺寸 | el-avatar.size |
| shape | String | 'circle' | - | 头像形状 | el-avatar.shape |
| icon | String | '' | - | 图标类名 | el-avatar.icon |
| src | String | '' | - | 图片地址 | el-avatar.src |
| alt | String | '' | - | 图片描述 | el-avatar.alt |
| srcSet | String | '' | - | 图片srcSet | el-avatar.src-set |
| fit | String | 'cover' | - | 图片适应方式 | el-avatar.fit |
| text | String | '' | - | 显示文字 | - |
| textColor | String | '#ffffff' | - | 文字颜色 | - |
| backgroundColor | String | '#409eff' | - | 背景颜色 | - |
| showStatus | Boolean | false | - | 是否显示状态指示 | - |
| status | String | 'online' | - | 状态类型 | - |
| statusColor | String | '' | - | 状态颜色 | - |
| statusPosition | String | 'bottom-right' | - | 状态位置 | - |
| showBadge | Boolean | false | - | 是否显示徽章 | - |
| badge | String/Number | '' | - | 徽章内容 | - |
| badgeType | String | 'danger' | - | 徽章类型 | - |
| badgeMax | Number | 99 | - | 徽章最大值 | - |
| badgePosition | String | 'top-right' | - | 徽章位置 | - |
| clickable | Boolean | false | - | 是否可点击 | - |
| hoverable | Boolean | false | - | 是否有悬停效果 | - |
| uploadable | Boolean | false | - | 是否支持上传 | - |
| uploadConfig | Object | {} | - | 上传配置 | - |
| lazy | Boolean | false | - | 是否懒加载 | - |
| placeholder | String | '' | - | 占位图片 | - |
| errorImage | String | '' | - | 错误图片 | - |
| tooltip | String | '' | - | 提示信息 | - |
| tooltipPlacement | String | 'top' | - | 提示位置 | - |
| border | Boolean | false | - | 是否显示边框 | - |
| borderColor | String | '#dcdfe6' | - | 边框颜色 | - |
| borderWidth | String | '1px' | - | 边框宽度 | - |
| shadow | String | 'never' | - | 阴影效果 | - |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| click | event: Event | 点击事件 | 点击头像时 |
| error | event: Event | 图片加载错误事件 | 图片加载失败时 |
| load | event: Event | 图片加载成功事件 | 图片加载完成时 |
| upload-success | response: any, file: File | 上传成功事件 | 文件上传成功时 |
| upload-error | error: any, file: File | 上传失败事件 | 文件上传失败时 |
| upload-progress | progress: number, file: File | 上传进度事件 | 文件上传过程中 |
| status-change | status: string | 状态变化事件 | 状态改变时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| refresh | - | void | 刷新头像 |
| upload | - | void | 触发上传 |
| setStatus | (status: string) | void | 设置状态 |
| setBadge | (badge: string/number) | void | 设置徽章 |

## Slots

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| default | - | 自定义头像内容 |
| status | { status } | 自定义状态指示器 |
| badge | { badge } | 自定义徽章内容 |
| upload | - | 自定义上传区域 |
| error | - | 自定义错误状态 |

## 状态类型

```typescript
type StatusType = 
  | 'online'     // 在线（绿色）
  | 'offline'    // 离线（灰色）
  | 'busy'       // 忙碌（红色）
  | 'away'       // 离开（黄色）
  | 'dnd'        // 勿扰（紫色）
  | 'invisible'; // 隐身（透明）
```

## 上传配置

```typescript
interface UploadConfig {
  action?: string;                 // 上传地址
  headers?: Record<string, any>;   // 请求头
  data?: Record<string, any>;      // 额外参数
  name?: string;                   // 文件字段名
  withCredentials?: boolean;       // 是否携带cookie
  accept?: string;                 // 接受的文件类型
  maxSize?: number;                // 最大文件大小（MB）
  beforeUpload?: Function;         // 上传前钩子
  onProgress?: Function;           // 上传进度回调
  onSuccess?: Function;            // 上传成功回调
  onError?: Function;              // 上传失败回调
}
```

## 使用示例

### 基础头像
```vue
<template>
  <div class="avatar-examples">
    <div class="example-group">
      <h4>不同尺寸</h4>
      <div class="avatar-row">
        <FuniAvatar :size="30" src="https://example.com/avatar1.jpg" />
        <FuniAvatar :size="40" src="https://example.com/avatar1.jpg" />
        <FuniAvatar size="default" src="https://example.com/avatar1.jpg" />
        <FuniAvatar :size="60" src="https://example.com/avatar1.jpg" />
        <FuniAvatar :size="80" src="https://example.com/avatar1.jpg" />
      </div>
    </div>
    
    <div class="example-group">
      <h4>不同形状</h4>
      <div class="avatar-row">
        <FuniAvatar shape="circle" src="https://example.com/avatar1.jpg" />
        <FuniAvatar shape="square" src="https://example.com/avatar1.jpg" />
      </div>
    </div>
    
    <div class="example-group">
      <h4>文字头像</h4>
      <div class="avatar-row">
        <FuniAvatar text="张" background-color="#f56c6c" />
        <FuniAvatar text="李" background-color="#67c23a" />
        <FuniAvatar text="王" background-color="#e6a23c" />
        <FuniAvatar text="Admin" background-color="#409eff" />
      </div>
    </div>
    
    <div class="example-group">
      <h4>图标头像</h4>
      <div class="avatar-row">
        <FuniAvatar icon="User" background-color="#909399" />
        <FuniAvatar icon="Avatar" background-color="#409eff" />
        <FuniAvatar icon="UserFilled" background-color="#67c23a" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.avatar-examples {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.example-group h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.avatar-row {
  display: flex;
  align-items: center;
  gap: 12px;
}
</style>
```

### 状态指示头像
```vue
<template>
  <div class="status-avatars">
    <div class="example-group">
      <h4>在线状态</h4>
      <div class="avatar-row">
        <FuniAvatar 
          src="https://example.com/avatar1.jpg"
          show-status
          status="online"
          tooltip="在线"
        />
        <FuniAvatar 
          src="https://example.com/avatar2.jpg"
          show-status
          status="offline"
          tooltip="离线"
        />
        <FuniAvatar 
          src="https://example.com/avatar3.jpg"
          show-status
          status="busy"
          tooltip="忙碌"
        />
        <FuniAvatar 
          src="https://example.com/avatar4.jpg"
          show-status
          status="away"
          tooltip="离开"
        />
        <FuniAvatar 
          src="https://example.com/avatar5.jpg"
          show-status
          status="dnd"
          tooltip="勿扰"
        />
      </div>
    </div>
    
    <div class="example-group">
      <h4>自定义状态位置</h4>
      <div class="avatar-row">
        <FuniAvatar 
          src="https://example.com/avatar1.jpg"
          show-status
          status="online"
          status-position="top-right"
        />
        <FuniAvatar 
          src="https://example.com/avatar1.jpg"
          show-status
          status="online"
          status-position="top-left"
        />
        <FuniAvatar 
          src="https://example.com/avatar1.jpg"
          show-status
          status="online"
          status-position="bottom-left"
        />
        <FuniAvatar 
          src="https://example.com/avatar1.jpg"
          show-status
          status="online"
          status-position="bottom-right"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.status-avatars {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.example-group h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.avatar-row {
  display: flex;
  align-items: center;
  gap: 16px;
}
</style>
```

### 徽章头像
```vue
<template>
  <div class="badge-avatars">
    <div class="example-group">
      <h4>消息徽章</h4>
      <div class="avatar-row">
        <FuniAvatar 
          src="https://example.com/avatar1.jpg"
          show-badge
          badge="5"
          badge-type="danger"
        />
        <FuniAvatar 
          src="https://example.com/avatar2.jpg"
          show-badge
          badge="99+"
          badge-type="warning"
        />
        <FuniAvatar 
          src="https://example.com/avatar3.jpg"
          show-badge
          badge="NEW"
          badge-type="success"
        />
        <FuniAvatar 
          src="https://example.com/avatar4.jpg"
          show-badge
          badge="•"
          badge-type="info"
        />
      </div>
    </div>
    
    <div class="example-group">
      <h4>状态 + 徽章</h4>
      <div class="avatar-row">
        <FuniAvatar 
          src="https://example.com/avatar1.jpg"
          show-status
          status="online"
          show-badge
          badge="3"
          badge-position="top-right"
          status-position="bottom-right"
        />
        <FuniAvatar 
          src="https://example.com/avatar2.jpg"
          show-status
          status="busy"
          show-badge
          badge="12"
          badge-position="top-left"
          status-position="bottom-right"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.badge-avatars {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.example-group h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.avatar-row {
  display: flex;
  align-items: center;
  gap: 16px;
}
</style>
```

### 可交互头像
```vue
<template>
  <div class="interactive-avatars">
    <div class="example-group">
      <h4>可点击头像</h4>
      <div class="avatar-row">
        <FuniAvatar 
          src="https://example.com/avatar1.jpg"
          clickable
          hoverable
          tooltip="点击查看详情"
          @click="handleAvatarClick('user1')"
        />
        <FuniAvatar 
          text="管理员"
          clickable
          hoverable
          background-color="#409eff"
          tooltip="管理员"
          @click="handleAvatarClick('admin')"
        />
      </div>
    </div>
    
    <div class="example-group">
      <h4>头像上传</h4>
      <div class="avatar-row">
        <FuniAvatar 
          :src="uploadedAvatar"
          uploadable
          :upload-config="uploadConfig"
          placeholder="点击上传头像"
          @upload-success="handleUploadSuccess"
          @upload-error="handleUploadError"
        />
      </div>
    </div>
    
    <div class="example-group">
      <h4>头像组</h4>
      <div class="avatar-group">
        <FuniAvatar 
          v-for="(user, index) in teamMembers"
          :key="user.id"
          :src="user.avatar"
          :text="user.name.charAt(0)"
          :tooltip="user.name"
          :style="{ marginLeft: index > 0 ? '-8px' : '0', zIndex: teamMembers.length - index }"
          clickable
          border
          @click="handleMemberClick(user)"
        />
        <FuniAvatar 
          text="+5"
          background-color="#f0f0f0"
          text-color="#666"
          :style="{ marginLeft: '-8px', zIndex: 0 }"
          clickable
          @click="showMoreMembers"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const uploadedAvatar = ref('')

const uploadConfig = reactive({
  action: '/api/upload/avatar',
  accept: 'image/*',
  maxSize: 2,
  beforeUpload: (file) => {
    const isImage = file.type.startsWith('image/')
    const isLt2M = file.size / 1024 / 1024 < 2
    
    if (!isImage) {
      ElMessage.error('只能上传图片文件!')
      return false
    }
    if (!isLt2M) {
      ElMessage.error('图片大小不能超过 2MB!')
      return false
    }
    return true
  }
})

const teamMembers = reactive([
  { id: 1, name: '张三', avatar: 'https://example.com/avatar1.jpg' },
  { id: 2, name: '李四', avatar: 'https://example.com/avatar2.jpg' },
  { id: 3, name: '王五', avatar: 'https://example.com/avatar3.jpg' },
  { id: 4, name: '赵六', avatar: 'https://example.com/avatar4.jpg' }
])

const handleAvatarClick = (userId) => {
  console.log('点击头像:', userId)
  ElMessage.info(`查看用户 ${userId} 的详情`)
}

const handleUploadSuccess = (response, file) => {
  console.log('上传成功:', response, file)
  uploadedAvatar.value = response.url
  ElMessage.success('头像上传成功')
}

const handleUploadError = (error, file) => {
  console.error('上传失败:', error, file)
  ElMessage.error('头像上传失败')
}

const handleMemberClick = (user) => {
  console.log('点击团队成员:', user)
  ElMessage.info(`查看 ${user.name} 的详情`)
}

const showMoreMembers = () => {
  console.log('显示更多成员')
  ElMessage.info('显示更多团队成员')
}
</script>

<style scoped>
.interactive-avatars {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.example-group h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.avatar-row {
  display: flex;
  align-items: center;
  gap: 16px;
}

.avatar-group {
  display: flex;
  align-items: center;
}
</style>
```

### 头像列表
```vue
<template>
  <div class="avatar-list">
    <div class="user-list">
      <div 
        v-for="user in userList"
        :key="user.id"
        class="user-item"
        @click="selectUser(user)"
        :class="{ active: selectedUser?.id === user.id }"
      >
        <FuniAvatar 
          :src="user.avatar"
          :text="user.name.charAt(0)"
          :size="40"
          show-status
          :status="user.status"
          show-badge
          :badge="user.unreadCount"
          :badge-type="user.unreadCount > 0 ? 'danger' : 'info'"
        />
        <div class="user-info">
          <div class="user-name">{{ user.name }}</div>
          <div class="user-desc">{{ user.description }}</div>
        </div>
        <div class="user-time">{{ user.lastActiveTime }}</div>
      </div>
    </div>
    
    <div class="selected-user" v-if="selectedUser">
      <h4>选中的用户</h4>
      <div class="user-detail">
        <FuniAvatar 
          :src="selectedUser.avatar"
          :text="selectedUser.name.charAt(0)"
          :size="80"
          show-status
          :status="selectedUser.status"
          clickable
          uploadable
          :upload-config="avatarUploadConfig"
          @upload-success="handleAvatarUpdate"
        />
        <div class="detail-info">
          <h3>{{ selectedUser.name }}</h3>
          <p>{{ selectedUser.email }}</p>
          <p>状态：{{ getStatusText(selectedUser.status) }}</p>
          <p>最后活跃：{{ selectedUser.lastActiveTime }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const selectedUser = ref(null)

const userList = reactive([
  {
    id: 1,
    name: '张三',
    email: '<EMAIL>',
    avatar: 'https://example.com/avatar1.jpg',
    status: 'online',
    unreadCount: 3,
    description: '前端开发工程师',
    lastActiveTime: '2分钟前'
  },
  {
    id: 2,
    name: '李四',
    email: '<EMAIL>',
    avatar: 'https://example.com/avatar2.jpg',
    status: 'busy',
    unreadCount: 0,
    description: '后端开发工程师',
    lastActiveTime: '10分钟前'
  },
  {
    id: 3,
    name: '王五',
    email: '<EMAIL>',
    avatar: 'https://example.com/avatar3.jpg',
    status: 'away',
    unreadCount: 1,
    description: '产品经理',
    lastActiveTime: '1小时前'
  },
  {
    id: 4,
    name: '赵六',
    email: '<EMAIL>',
    avatar: '',
    status: 'offline',
    unreadCount: 0,
    description: '设计师',
    lastActiveTime: '昨天'
  }
])

const avatarUploadConfig = reactive({
  action: '/api/upload/avatar',
  accept: 'image/*',
  maxSize: 5
})

const selectUser = (user) => {
  selectedUser.value = user
}

const getStatusText = (status) => {
  const statusMap = {
    online: '在线',
    offline: '离线',
    busy: '忙碌',
    away: '离开',
    dnd: '勿扰'
  }
  return statusMap[status] || status
}

const handleAvatarUpdate = (response) => {
  if (selectedUser.value) {
    selectedUser.value.avatar = response.url
    ElMessage.success('头像更新成功')
  }
}
</script>

<style scoped>
.avatar-list {
  display: flex;
  gap: 20px;
}

.user-list {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.user-item:hover {
  background-color: #f5f7fa;
}

.user-item.active {
  background-color: #e6f7ff;
  border-color: #409eff;
}

.user-item:last-child {
  border-bottom: none;
}

.user-info {
  flex: 1;
  margin-left: 12px;
}

.user-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.user-desc {
  font-size: 12px;
  color: #999;
}

.user-time {
  font-size: 12px;
  color: #999;
}

.selected-user {
  width: 300px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fafafa;
}

.selected-user h4 {
  margin: 0 0 16px 0;
}

.user-detail {
  display: flex;
  gap: 16px;
}

.detail-info h3 {
  margin: 0 0 8px 0;
}

.detail-info p {
  margin: 4px 0;
  font-size: 14px;
  color: #666;
}
</style>
```

## ElementPlus API支持

FuniAvatar基于el-avatar封装，支持所有el-avatar的API：

```vue
<template>
  <FuniAvatar
    :src="avatarSrc"
    
    <!-- ElementPlus el-avatar 所有属性 -->
    :size="avatarSize"
    shape="circle"
    icon="User"
    alt="用户头像"
    src-set=""
    fit="cover"
    
    <!-- ElementPlus el-avatar 所有事件 -->
    @error="handleError"
  />
</template>
```

## 注意事项

### 1. 图片处理
- 提供合适尺寸的头像图片
- 处理图片加载失败的情况
- 支持多种图片格式
- 考虑图片的压缩和优化

### 2. 用户体验
- 提供清晰的状态指示
- 合理设置头像尺寸
- 支持键盘导航
- 提供无障碍访问支持

### 3. 性能考虑
- 使用懒加载优化大量头像的渲染
- 合理缓存头像图片
- 避免频繁的状态更新
- 优化头像上传的用户体验

### 4. 安全性
- 验证上传文件的类型和大小
- 处理恶意文件上传
- 保护用户隐私信息
- 提供头像审核机制

## 常见问题

### Q: 如何实现头像的懒加载？
A: 设置lazy属性为true，组件会在进入视口时才加载图片

### Q: 如何自定义头像的状态指示器？
A: 使用status插槽自定义状态指示器的样式和内容

### Q: 如何处理头像上传的进度显示？
A: 监听upload-progress事件，显示上传进度条

### Q: 如何实现头像的批量操作？
A: 结合选择器组件，支持多选头像进行批量操作
