# FuniAvatar ElementPlus API 支持

## 基础组件说明

FuniAvatar 是基于 ElementPlus 的 `el-avatar` 组件进行二次封装的头像组件。它完全兼容 `el-avatar` 的所有 API，并在此基础上扩展了状态指示、徽章显示、交互功能等特性。

## ElementPlus el-avatar 原生 API 支持

### Props 属性

| 属性名 | 类型 | 默认值 | 说明 | FuniAvatar 支持 |
|--------|------|--------|------|----------------|
| size | number/string | 'default' | 头像尺寸 | ✅ 完全支持 |
| shape | string | 'circle' | 头像形状，circle / square | ✅ 完全支持 |
| icon | string | - | 设置头像的图标类型 | ✅ 完全支持 |
| src | string | - | 图片头像的资源地址 | ✅ 完全支持 |
| alt | string | - | 图片头像的替代文本 | ✅ 完全支持 |
| srcSet | string | - | 图片头像的 srcset 属性 | ✅ 完全支持 |
| fit | string | 'cover' | 当展示类型为图片时，设置图片如何适应容器框 | ✅ 完全支持 |

### Events 事件

| 事件名 | 说明 | 回调参数 | FuniAvatar 支持 |
|--------|------|----------|----------------|
| error | 图片加载失败时触发 | (e: Event) | ✅ 完全支持 |

### Slots 插槽

| 插槽名 | 说明 | FuniAvatar 支持 |
|--------|------|----------------|
| default | 自定义头像展示内容 | ✅ 完全支持 |

## API 透传方式

FuniAvatar 通过 `v-bind="$attrs"` 的方式将所有 ElementPlus el-avatar 的属性透传给底层组件，确保 100% 兼容性。

### 基础使用示例

```vue
<template>
  <!-- 直接使用 ElementPlus el-avatar 的所有属性 -->
  <FuniAvatar
    :size="50"
    shape="circle"
    icon="User"
    fit="cover"
    alt="用户头像"
    @error="handleImageError"
  />
</template>

<script setup>
const handleImageError = (e) => {
  console.log('图片加载失败:', e)
}
</script>
```

### 尺寸设置

```vue
<template>
  <div class="size-demo">
    <!-- 预设尺寸 -->
    <FuniAvatar size="large" src="avatar.jpg" />
    <FuniAvatar size="default" src="avatar.jpg" />
    <FuniAvatar size="small" src="avatar.jpg" />
    
    <!-- 自定义数字尺寸 -->
    <FuniAvatar :size="60" src="avatar.jpg" />
    <FuniAvatar :size="80" src="avatar.jpg" />
  </div>
</template>
```

### 形状设置

```vue
<template>
  <div class="shape-demo">
    <!-- 圆形头像 -->
    <FuniAvatar shape="circle" src="avatar.jpg" />
    
    <!-- 方形头像 -->
    <FuniAvatar shape="square" src="avatar.jpg" />
  </div>
</template>
```

### 图标头像

```vue
<template>
  <div class="icon-demo">
    <!-- 使用 ElementPlus 图标 -->
    <FuniAvatar icon="User" />
    <FuniAvatar icon="Avatar" />
    <FuniAvatar icon="UserFilled" />
  </div>
</template>
```

### 图片适应方式

```vue
<template>
  <div class="fit-demo">
    <!-- 不同的图片适应方式 -->
    <FuniAvatar src="avatar.jpg" fit="fill" />
    <FuniAvatar src="avatar.jpg" fit="contain" />
    <FuniAvatar src="avatar.jpg" fit="cover" />
    <FuniAvatar src="avatar.jpg" fit="none" />
    <FuniAvatar src="avatar.jpg" fit="scale-down" />
  </div>
</template>
```

### 图片加载错误处理

```vue
<template>
  <div class="error-demo">
    <FuniAvatar 
      src="invalid-url.jpg"
      alt="用户头像"
      @error="handleError"
    >
      <!-- 错误时显示的内容 -->
      <template #default>
        <el-icon><User /></el-icon>
      </template>
    </FuniAvatar>
  </div>
</template>

<script setup>
import { User } from '@element-plus/icons-vue'

const handleError = (e) => {
  console.log('头像加载失败，显示默认图标')
}
</script>
```

### 响应式图片

```vue
<template>
  <div class="responsive-demo">
    <FuniAvatar 
      src="avatar-small.jpg"
      srcSet="avatar-small.jpg 1x, avatar-large.jpg 2x"
      alt="响应式头像"
    />
  </div>
</template>
```

## FuniAvatar 扩展功能

在完全支持 ElementPlus el-avatar API 的基础上，FuniAvatar 还提供了以下扩展功能：

### 1. 状态指示功能

```vue
<template>
  <FuniAvatar 
    src="avatar.jpg"
    
    <!-- ElementPlus 原生属性 -->
    :size="50"
    shape="circle"
    
    <!-- FuniAvatar 扩展属性 -->
    show-status
    status="online"
    status-position="bottom-right"
  />
</template>
```

### 2. 徽章显示功能

```vue
<template>
  <FuniAvatar 
    src="avatar.jpg"
    
    <!-- ElementPlus 原生属性 -->
    :size="50"
    
    <!-- FuniAvatar 扩展属性 -->
    show-badge
    badge="5"
    badge-type="danger"
    badge-position="top-right"
  />
</template>
```

### 3. 交互功能

```vue
<template>
  <FuniAvatar 
    src="avatar.jpg"
    
    <!-- ElementPlus 原生属性 -->
    :size="50"
    
    <!-- FuniAvatar 扩展属性 -->
    clickable
    hoverable
    tooltip="点击查看详情"
    @click="handleClick"
  />
</template>
```

### 4. 上传功能

```vue
<template>
  <FuniAvatar 
    :src="avatarUrl"
    
    <!-- ElementPlus 原生属性 -->
    :size="80"
    
    <!-- FuniAvatar 扩展属性 -->
    uploadable
    :upload-config="uploadConfig"
    @upload-success="handleUploadSuccess"
  />
</template>
```

## 兼容性说明

### 完全兼容

- ✅ 所有 ElementPlus el-avatar 的 Props 属性
- ✅ 所有 ElementPlus el-avatar 的 Events 事件
- ✅ 所有 ElementPlus el-avatar 的 Slots 插槽
- ✅ ElementPlus el-avatar 的样式类名
- ✅ ElementPlus el-avatar 的 CSS 变量

### 扩展兼容

- ✅ 在原有功能基础上增加新特性
- ✅ 不影响原有 API 的使用方式
- ✅ 向后兼容，可以直接替换 el-avatar

### 迁移指南

如果你正在使用 ElementPlus 的 el-avatar，可以直接替换为 FuniAvatar：

```vue
<!-- 原来的 el-avatar -->
<el-avatar 
  :size="50"
  shape="circle"
  src="avatar.jpg"
  @error="handleError"
/>

<!-- 替换为 FuniAvatar，功能完全一致 -->
<FuniAvatar 
  :size="50"
  shape="circle"
  src="avatar.jpg"
  @error="handleError"
/>

<!-- 还可以使用扩展功能 -->
<FuniAvatar 
  :size="50"
  shape="circle"
  src="avatar.jpg"
  @error="handleError"
  
  <!-- 新增的扩展功能 -->
  show-status
  status="online"
  clickable
  tooltip="用户在线"
/>
```

## 注意事项

1. **样式继承**：FuniAvatar 继承了 el-avatar 的所有样式，确保视觉效果一致
2. **事件透传**：所有 el-avatar 的事件都会正确透传，不会丢失
3. **插槽支持**：default 插槽完全兼容，可以自定义头像内容
4. **CSS 变量**：支持 ElementPlus 主题定制的 CSS 变量
5. **无障碍访问**：保持 el-avatar 的无障碍访问特性

## 最佳实践

1. **渐进式升级**：可以逐步将项目中的 el-avatar 替换为 FuniAvatar
2. **功能按需使用**：不需要扩展功能时，FuniAvatar 表现与 el-avatar 完全一致
3. **主题兼容**：使用 ElementPlus 主题时，FuniAvatar 会自动适配
4. **性能优化**：扩展功能采用按需渲染，不影响基础性能
