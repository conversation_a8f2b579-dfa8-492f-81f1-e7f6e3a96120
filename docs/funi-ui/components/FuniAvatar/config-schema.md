# FuniAvatar 配置结构定义

## 基础配置结构

### AvatarConfig 接口定义

```typescript
interface AvatarConfig {
  // 基础属性
  size?: number | string;
  shape?: 'circle' | 'square';
  src?: string;
  alt?: string;
  srcSet?: string;
  fit?: 'fill' | 'contain' | 'cover' | 'none' | 'scale-down';
  
  // 文字头像
  text?: string;
  textColor?: string;
  backgroundColor?: string;
  
  // 图标头像
  icon?: string;
  
  // 状态指示
  showStatus?: boolean;
  status?: StatusType;
  statusColor?: string;
  statusPosition?: StatusPosition;
  
  // 徽章配置
  showBadge?: boolean;
  badge?: string | number;
  badgeType?: BadgeType;
  badgeMax?: number;
  badgePosition?: BadgePosition;
  
  // 交互配置
  clickable?: boolean;
  hoverable?: boolean;
  tooltip?: string;
  tooltipPlacement?: TooltipPlacement;
  
  // 上传配置
  uploadable?: boolean;
  uploadConfig?: UploadConfig;
  
  // 高级配置
  lazy?: boolean;
  placeholder?: string;
  errorImage?: string;
  border?: boolean;
  borderColor?: string;
  borderWidth?: number;
}
```

### 状态类型定义

```typescript
type StatusType = 
  | 'online'     // 在线状态（绿色圆点）
  | 'offline'    // 离线状态（灰色圆点）
  | 'busy'       // 忙碌状态（红色圆点）
  | 'away'       // 离开状态（黄色圆点）
  | 'dnd'        // 勿扰状态（紫色圆点）
  | 'invisible'; // 隐身状态（透明圆点）

type StatusPosition = 
  | 'top-left'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-right';
```

### 徽章类型定义

```typescript
type BadgeType = 
  | 'primary'
  | 'success'
  | 'warning'
  | 'danger'
  | 'info';

type BadgePosition = 
  | 'top-left'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-right';
```

### 上传配置定义

```typescript
interface UploadConfig {
  action: string;                    // 上传地址
  method?: string;                   // 上传方法，默认POST
  accept?: string;                   // 接受的文件类型
  maxSize?: number;                  // 最大文件大小（MB）
  headers?: Record<string, string>;  // 请求头
  data?: Record<string, any>;        // 额外数据
  name?: string;                     // 文件字段名
  withCredentials?: boolean;         // 是否携带cookie
  beforeUpload?: (file: File) => boolean | Promise<boolean>;
  onProgress?: (event: ProgressEvent, file: File) => void;
  onSuccess?: (response: any, file: File) => void;
  onError?: (error: Error, file: File) => void;
}
```

### 提示配置定义

```typescript
type TooltipPlacement = 
  | 'top'
  | 'top-start'
  | 'top-end'
  | 'bottom'
  | 'bottom-start'
  | 'bottom-end'
  | 'left'
  | 'left-start'
  | 'left-end'
  | 'right'
  | 'right-start'
  | 'right-end';
```

## 详细配置说明

### 基础显示配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| size | number/string | 'default' | 头像尺寸，可以是数字（像素）或预设值 |
| shape | string | 'circle' | 头像形状，circle（圆形）或square（方形） |
| src | string | '' | 头像图片地址 |
| alt | string | '' | 图片替代文本，用于无障碍访问 |
| srcSet | string | '' | 响应式图片源集合 |
| fit | string | 'cover' | 图片适应方式 |

**尺寸预设值：**
- `'large'`: 40px
- `'default'`: 32px  
- `'small'`: 28px
- 数字值：直接指定像素大小

### 文字头像配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| text | string | '' | 显示的文字内容 |
| textColor | string | '#ffffff' | 文字颜色 |
| backgroundColor | string | '#409eff' | 背景颜色 |

**文字头像最佳实践：**
- 中文姓名：取姓氏，如"张三" → "张"
- 英文姓名：取首字母，如"John Doe" → "JD"
- 用户名：取前1-2个字符
- 团队/组织：取关键词首字母

### 状态指示配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| showStatus | boolean | false | 是否显示状态指示器 |
| status | StatusType | 'online' | 状态类型 |
| statusColor | string | '' | 自定义状态颜色（覆盖默认） |
| statusPosition | StatusPosition | 'bottom-right' | 状态指示器位置 |

**状态颜色映射：**
- `online`: #67c23a（绿色）
- `offline`: #909399（灰色）
- `busy`: #f56c6c（红色）
- `away`: #e6a23c（黄色）
- `dnd`: #9c27b0（紫色）
- `invisible`: transparent（透明）

### 徽章配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| showBadge | boolean | false | 是否显示徽章 |
| badge | string/number | '' | 徽章内容 |
| badgeType | BadgeType | 'danger' | 徽章类型 |
| badgeMax | number | 99 | 数字徽章最大显示值 |
| badgePosition | BadgePosition | 'top-right' | 徽章位置 |

**徽章内容建议：**
- 数字：未读消息数量
- 文字：状态标识（如"NEW"、"VIP"）
- 符号：简单标记（如"•"）

## 常用配置组合

### 1. 基础用户头像

```typescript
const basicAvatarConfig: AvatarConfig = {
  size: 40,
  shape: 'circle',
  src: 'https://example.com/avatar.jpg',
  alt: '用户头像',
  showStatus: true,
  status: 'online'
};
```

### 2. 团队成员头像

```typescript
const teamMemberConfig: AvatarConfig = {
  size: 32,
  shape: 'circle',
  text: '张',
  backgroundColor: '#409eff',
  textColor: '#ffffff',
  clickable: true,
  hoverable: true,
  tooltip: '张三 - 前端开发'
};
```

### 3. 消息列表头像

```typescript
const messageAvatarConfig: AvatarConfig = {
  size: 48,
  shape: 'circle',
  src: 'https://example.com/avatar.jpg',
  showStatus: true,
  status: 'online',
  showBadge: true,
  badge: 5,
  badgeType: 'danger',
  badgePosition: 'top-right',
  statusPosition: 'bottom-right'
};
```

### 4. 可上传头像

```typescript
const uploadableAvatarConfig: AvatarConfig = {
  size: 80,
  shape: 'circle',
  src: '',
  placeholder: '点击上传头像',
  uploadable: true,
  uploadConfig: {
    action: '/api/upload/avatar',
    accept: 'image/*',
    maxSize: 2,
    beforeUpload: (file) => {
      const isImage = file.type.startsWith('image/');
      const isLt2M = file.size / 1024 / 1024 < 2;
      return isImage && isLt2M;
    }
  }
};
```

### 5. 管理员头像

```typescript
const adminAvatarConfig: AvatarConfig = {
  size: 36,
  shape: 'circle',
  icon: 'UserFilled',
  backgroundColor: '#f56c6c',
  showBadge: true,
  badge: 'ADMIN',
  badgeType: 'warning',
  tooltip: '系统管理员',
  clickable: true
};
```

## 最佳实践建议

### 1. 尺寸选择
- **列表项头像**: 32-40px
- **详情页头像**: 60-80px
- **导航栏头像**: 28-32px
- **卡片头像**: 48-56px

### 2. 状态指示
- 仅在需要实时状态的场景使用
- 状态位置避免与徽章重叠
- 提供状态说明的tooltip

### 3. 徽章使用
- 数字徽章：显示未读消息、通知数量
- 文字徽章：显示用户角色、特殊标识
- 避免徽章内容过长

### 4. 交互设计
- 可点击头像提供明确的视觉反馈
- 悬停效果要适度，不影响用户体验
- 提供有意义的tooltip信息

### 5. 上传功能
- 限制文件类型和大小
- 提供上传进度反馈
- 处理上传失败的情况
- 支持图片预览和裁剪

### 6. 无障碍访问
- 提供alt属性描述
- 支持键盘导航
- 确保颜色对比度符合标准
- 为状态指示提供文字说明
