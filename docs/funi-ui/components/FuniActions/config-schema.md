# FuniActions 配置结构定义

## 组件配置接口

### IFuniActionsProps

```typescript
interface IFuniActionsProps {
  // FuniActions组件本身不接受任何props
  // 所有配置通过插槽内容进行
}
```

## 操作按钮配置接口

### IActionButton

```typescript
interface IActionButton {
  /** 按钮唯一标识 */
  key: string
  /** 按钮文本 */
  label: string
  /** 按钮类型 */
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text' | 'default'
  /** 按钮图标 */
  icon?: string | Component
  /** 是否加载中 */
  loading?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 按钮尺寸 */
  size?: 'large' | 'default' | 'small'
  /** 是否朴素按钮 */
  plain?: boolean
  /** 是否圆角按钮 */
  round?: boolean
  /** 是否圆形按钮 */
  circle?: boolean
  /** 是否自动聚焦 */
  autofocus?: boolean
  /** 原生type属性 */
  nativeType?: 'button' | 'submit' | 'reset'
  /** 点击事件处理函数 */
  onClick?: () => void | Promise<void>
  /** 显示条件函数 */
  show?: boolean | (() => boolean)
  /** 权限标识 */
  auth?: string
}
```

### IActionGroup

```typescript
interface IActionGroup {
  /** 操作组标题 */
  title?: string
  /** 操作按钮列表 */
  actions: IActionButton[]
  /** 操作组位置 */
  position?: 'left' | 'center' | 'right'
  /** 是否显示分隔符 */
  divider?: boolean
  /** 自定义样式类名 */
  className?: string
}
```

## 布局配置接口

### IActionsLayout

```typescript
interface IActionsLayout {
  /** 布局方向 */
  direction?: 'horizontal' | 'vertical'
  /** 对齐方式 */
  align?: 'left' | 'center' | 'right' | 'space-between' | 'space-around'
  /** 按钮间距 */
  gap?: number | string
  /** 是否换行 */
  wrap?: boolean
  /** 最大宽度 */
  maxWidth?: number | string
  /** 响应式断点配置 */
  responsive?: IResponsiveConfig
}
```

### IResponsiveConfig

```typescript
interface IResponsiveConfig {
  /** 移动端配置 */
  mobile?: {
    direction?: 'horizontal' | 'vertical'
    align?: string
    gap?: number | string
    maxButtons?: number
  }
  /** 平板端配置 */
  tablet?: {
    direction?: 'horizontal' | 'vertical'
    align?: string
    gap?: number | string
    maxButtons?: number
  }
  /** 桌面端配置 */
  desktop?: {
    direction?: 'horizontal' | 'vertical'
    align?: string
    gap?: number | string
    maxButtons?: number
  }
}
```

## 权限配置接口

### IPermissionConfig

```typescript
interface IPermissionConfig {
  /** 权限检查函数 */
  hasPermission?: (permission: string) => boolean
  /** 无权限时的处理方式 */
  onNoPermission?: 'hide' | 'disable' | 'show'
  /** 权限提示信息 */
  permissionMessage?: string
}
```

## 配置示例

### 基础配置

```typescript
const basicActions: IActionButton[] = [
  {
    key: 'save',
    label: '保存',
    type: 'primary',
    icon: 'Document',
    onClick: async () => {
      await saveData()
    }
  },
  {
    key: 'cancel',
    label: '取消',
    type: 'default',
    onClick: () => {
      goBack()
    }
  }
]
```

### 高级配置

```typescript
const advancedActions: IActionButton[] = [
  {
    key: 'submit',
    label: '提交审核',
    type: 'primary',
    icon: 'Check',
    loading: false,
    disabled: false,
    auth: 'submit:permission',
    show: () => status.value === 'draft',
    onClick: async () => {
      await submitForReview()
    }
  },
  {
    key: 'approve',
    label: '审核通过',
    type: 'success',
    icon: 'CircleCheck',
    auth: 'approve:permission',
    show: () => status.value === 'pending' && hasRole('approver'),
    onClick: async () => {
      await approveItem()
    }
  },
  {
    key: 'reject',
    label: '审核拒绝',
    type: 'danger',
    icon: 'CircleClose',
    auth: 'approve:permission',
    show: () => status.value === 'pending' && hasRole('approver'),
    onClick: async () => {
      await rejectItem()
    }
  },
  {
    key: 'edit',
    label: '编辑',
    type: 'warning',
    icon: 'Edit',
    auth: 'edit:permission',
    show: () => ['draft', 'rejected'].includes(status.value),
    onClick: () => {
      enterEditMode()
    }
  },
  {
    key: 'delete',
    label: '删除',
    type: 'danger',
    icon: 'Delete',
    plain: true,
    auth: 'delete:permission',
    show: () => status.value !== 'approved',
    onClick: async () => {
      await confirmDelete()
    }
  }
]
```

### 分组配置

```typescript
const groupedActions: IActionGroup[] = [
  {
    title: '主要操作',
    position: 'right',
    actions: [
      {
        key: 'save',
        label: '保存',
        type: 'primary',
        icon: 'Document'
      },
      {
        key: 'submit',
        label: '提交',
        type: 'success',
        icon: 'Check'
      }
    ]
  },
  {
    title: '次要操作',
    position: 'left',
    divider: true,
    actions: [
      {
        key: 'preview',
        label: '预览',
        type: 'info',
        icon: 'View'
      },
      {
        key: 'reset',
        label: '重置',
        type: 'warning',
        icon: 'Refresh'
      }
    ]
  }
]
```

### 响应式配置

```typescript
const responsiveLayout: IActionsLayout = {
  direction: 'horizontal',
  align: 'right',
  gap: '12px',
  wrap: true,
  responsive: {
    mobile: {
      direction: 'vertical',
      align: 'center',
      gap: '8px',
      maxButtons: 2
    },
    tablet: {
      direction: 'horizontal',
      align: 'right',
      gap: '10px',
      maxButtons: 4
    },
    desktop: {
      direction: 'horizontal',
      align: 'right',
      gap: '12px',
      maxButtons: 6
    }
  }
}
```

### 权限配置

```typescript
const permissionConfig: IPermissionConfig = {
  hasPermission: (permission: string) => {
    return userStore.permissions.includes(permission)
  },
  onNoPermission: 'hide',
  permissionMessage: '您没有权限执行此操作'
}
```

## 样式配置接口

### IActionsStyle

```typescript
interface IActionsStyle {
  /** 容器样式 */
  container?: {
    position?: 'static' | 'relative' | 'absolute' | 'fixed' | 'sticky'
    top?: string | number
    right?: string | number
    bottom?: string | number
    left?: string | number
    zIndex?: number
    background?: string
    padding?: string
    margin?: string
    borderRadius?: string
    boxShadow?: string
  }
  /** 按钮样式 */
  button?: {
    minWidth?: string | number
    height?: string | number
    fontSize?: string | number
    fontWeight?: string | number
    borderRadius?: string
    margin?: string
  }
}
```

### 样式配置示例

```typescript
const styleConfig: IActionsStyle = {
  container: {
    position: 'fixed',
    bottom: '20px',
    right: '20px',
    zIndex: 1000,
    background: '#fff',
    padding: '12px',
    borderRadius: '8px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
  },
  button: {
    minWidth: '80px',
    height: '36px',
    fontSize: '14px',
    borderRadius: '6px',
    margin: '0 4px'
  }
}
```

## 事件配置接口

### IActionsEvents

```typescript
interface IActionsEvents {
  /** 按钮点击前事件 */
  onBeforeClick?: (action: IActionButton) => boolean | Promise<boolean>
  /** 按钮点击后事件 */
  onAfterClick?: (action: IActionButton, result: any) => void
  /** 按钮点击错误事件 */
  onClickError?: (action: IActionButton, error: Error) => void
  /** 权限检查失败事件 */
  onPermissionDenied?: (action: IActionButton) => void
}
```

## 默认配置

```typescript
const defaultConfig = {
  layout: {
    direction: 'horizontal',
    align: 'right',
    gap: '12px',
    wrap: false
  },
  button: {
    type: 'default',
    size: 'default',
    loading: false,
    disabled: false,
    plain: false,
    round: false,
    circle: false,
    autofocus: false,
    nativeType: 'button'
  },
  permission: {
    onNoPermission: 'hide'
  }
}
```

## 配置验证

### 按钮配置验证

```typescript
function validateActionButton(action: IActionButton): boolean {
  if (!action.key || typeof action.key !== 'string') {
    console.warn('FuniActions: action.key is required and must be a string')
    return false
  }
  
  if (!action.label || typeof action.label !== 'string') {
    console.warn('FuniActions: action.label is required and must be a string')
    return false
  }
  
  if (action.type && !['primary', 'success', 'warning', 'danger', 'info', 'text', 'default'].includes(action.type)) {
    console.warn('FuniActions: invalid action.type')
    return false
  }
  
  return true
}
```

### 布局配置验证

```typescript
function validateLayout(layout: IActionsLayout): boolean {
  if (layout.direction && !['horizontal', 'vertical'].includes(layout.direction)) {
    console.warn('FuniActions: invalid layout.direction')
    return false
  }
  
  if (layout.align && !['left', 'center', 'right', 'space-between', 'space-around'].includes(layout.align)) {
    console.warn('FuniActions: invalid layout.align')
    return false
  }
  
  return true
}
```
