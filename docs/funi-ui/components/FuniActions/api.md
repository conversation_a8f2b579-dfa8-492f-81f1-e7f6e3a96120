# FuniActions API文档

## 组件概述

FuniActions是一个基于FuniTeleport封装的操作按钮组传送组件。它的主要作用是将操作按钮传送到指定的DOM容器（`.funi-actions`），通常用于页面级别的操作按钮布局管理。

## Props

FuniActions组件本身不接受任何props，它只是一个简单的传送容器。

## Events

FuniActions组件本身不触发任何事件，所有事件由内部的子组件处理。

## Slots

### 默认插槽

| 插槽名 | 说明 | 参数 |
|--------|------|------|
| default | 默认插槽，用于放置操作按钮或其他内容 | - |

## 使用方式

### 基础使用

```vue
<template>
  <div class="page-container">
    <!-- 页面内容 -->
    <div class="page-content">
      <h1>页面标题</h1>
      <p>页面内容...</p>
    </div>
    
    <!-- 操作按钮区域 -->
    <div class="funi-actions"></div>
    
    <!-- 使用FuniActions传送按钮 -->
    <FuniActions>
      <el-button type="primary" @click="handleSave">保存</el-button>
      <el-button @click="handleCancel">取消</el-button>
    </FuniActions>
  </div>
</template>

<script setup>
const handleSave = () => {
  console.log('保存操作')
}

const handleCancel = () => {
  console.log('取消操作')
}
</script>
```

### 多个操作按钮

```vue
<template>
  <div class="page-layout">
    <!-- 页面主体内容 -->
    <div class="main-content">
      <!-- 内容区域 -->
    </div>
    
    <!-- 操作按钮容器 -->
    <div class="funi-actions"></div>
    
    <!-- 传送操作按钮 -->
    <FuniActions>
      <el-button type="primary" icon="Check" @click="handleSubmit">
        提交
      </el-button>
      <el-button type="success" icon="Document" @click="handleSave">
        保存草稿
      </el-button>
      <el-button type="warning" icon="View" @click="handlePreview">
        预览
      </el-button>
      <el-button icon="Back" @click="handleBack">
        返回
      </el-button>
    </FuniActions>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'

const handleSubmit = () => {
  ElMessage.success('提交成功')
}

const handleSave = () => {
  ElMessage.info('保存草稿')
}

const handlePreview = () => {
  ElMessage.info('打开预览')
}

const handleBack = () => {
  ElMessage.info('返回上一页')
}
</script>
```

### 条件渲染按钮

```vue
<template>
  <div class="conditional-actions">
    <div class="funi-actions"></div>
    
    <FuniActions>
      <!-- 根据权限显示按钮 -->
      <el-button 
        v-if="hasPermission('edit')"
        type="primary" 
        @click="handleEdit"
      >
        编辑
      </el-button>
      
      <el-button 
        v-if="hasPermission('delete')"
        type="danger" 
        @click="handleDelete"
      >
        删除
      </el-button>
      
      <!-- 根据状态显示按钮 -->
      <el-button 
        v-if="status === 'draft'"
        type="success" 
        @click="handlePublish"
      >
        发布
      </el-button>
      
      <el-button 
        v-if="status === 'published'"
        type="warning" 
        @click="handleUnpublish"
      >
        下线
      </el-button>
      
      <!-- 通用按钮 -->
      <el-button @click="handleCancel">
        取消
      </el-button>
    </FuniActions>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const status = ref('draft')
const userPermissions = ref(['edit', 'delete'])

const hasPermission = (permission) => {
  return userPermissions.value.includes(permission)
}

const handleEdit = () => {
  console.log('编辑操作')
}

const handleDelete = () => {
  console.log('删除操作')
}

const handlePublish = () => {
  status.value = 'published'
  console.log('发布操作')
}

const handleUnpublish = () => {
  status.value = 'draft'
  console.log('下线操作')
}

const handleCancel = () => {
  console.log('取消操作')
}
</script>
```

### 动态按钮组

```vue
<template>
  <div class="dynamic-actions">
    <div class="funi-actions"></div>
    
    <FuniActions>
      <el-button
        v-for="action in actions"
        :key="action.key"
        :type="action.type"
        :icon="action.icon"
        :loading="action.loading"
        :disabled="action.disabled"
        @click="handleAction(action)"
      >
        {{ action.label }}
      </el-button>
    </FuniActions>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const actions = reactive([
  {
    key: 'save',
    label: '保存',
    type: 'primary',
    icon: 'Document',
    loading: false,
    disabled: false
  },
  {
    key: 'submit',
    label: '提交',
    type: 'success',
    icon: 'Check',
    loading: false,
    disabled: false
  },
  {
    key: 'reset',
    label: '重置',
    type: 'warning',
    icon: 'Refresh',
    loading: false,
    disabled: false
  },
  {
    key: 'cancel',
    label: '取消',
    type: 'default',
    icon: 'Close',
    loading: false,
    disabled: false
  }
])

const handleAction = async (action) => {
  // 设置loading状态
  action.loading = true
  
  try {
    switch (action.key) {
      case 'save':
        await handleSave()
        break
      case 'submit':
        await handleSubmit()
        break
      case 'reset':
        await handleReset()
        break
      case 'cancel':
        handleCancel()
        break
    }
  } finally {
    action.loading = false
  }
}

const handleSave = async () => {
  // 模拟异步操作
  await new Promise(resolve => setTimeout(resolve, 1000))
  console.log('保存完成')
}

const handleSubmit = async () => {
  await new Promise(resolve => setTimeout(resolve, 1500))
  console.log('提交完成')
}

const handleReset = async () => {
  await new Promise(resolve => setTimeout(resolve, 500))
  console.log('重置完成')
}

const handleCancel = () => {
  console.log('取消操作')
}
</script>
```

## 与FuniTeleport的关系

FuniActions内部使用FuniTeleport组件实现DOM传送功能：

```vue
<template>
  <funi-teleport to=".funi-actions">
    <slot></slot>
  </funi-teleport>
</template>
```

## 使用场景

### 1. 页面级操作按钮

适用于需要将操作按钮固定在页面特定位置的场景，如：
- 表单页面的提交/取消按钮
- 详情页面的编辑/删除按钮
- 列表页面的批量操作按钮

### 2. 布局分离

将按钮定义与页面布局分离，提高代码的可维护性：
- 按钮逻辑在组件内部定义
- 按钮位置由页面布局控制
- 支持动态显示/隐藏按钮

### 3. 响应式布局

配合CSS媒体查询实现响应式按钮布局：
- 桌面端显示在页面底部
- 移动端显示为浮动按钮
- 不同屏幕尺寸下的按钮排列

## 样式定制

### 基础样式

```css
.funi-actions {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  gap: 12px;
}

.funi-actions .el-button {
  min-width: 80px;
}
```

### 响应式样式

```css
/* 桌面端 */
@media (min-width: 768px) {
  .funi-actions {
    position: static;
    bottom: auto;
    right: auto;
    justify-content: flex-end;
    padding: 20px 0;
    border-top: 1px solid #e4e7ed;
  }
}

/* 移动端 */
@media (max-width: 767px) {
  .funi-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 12px 16px;
    background: #fff;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    justify-content: space-between;
  }
  
  .funi-actions .el-button {
    flex: 1;
    margin: 0 4px;
  }
}
```

## 注意事项

1. **目标容器**: 确保页面中存在`.funi-actions`容器元素
2. **传送时机**: 组件会在挂载后立即传送内容
3. **样式继承**: 传送后的元素会继承目标容器的样式上下文
4. **事件处理**: 事件监听器在传送后仍然有效
5. **生命周期**: 组件销毁时会自动清理传送的内容

## 最佳实践

1. **统一容器**: 在应用中统一使用`.funi-actions`作为操作按钮容器
2. **按钮数量**: 建议操作按钮不超过5个，避免界面过于复杂
3. **按钮顺序**: 主要操作放在右侧，次要操作放在左侧
4. **响应式设计**: 考虑不同屏幕尺寸下的按钮布局
5. **权限控制**: 根据用户权限动态显示/隐藏按钮
