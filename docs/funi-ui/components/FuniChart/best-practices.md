# FuniChart 最佳实践

## 组件使用最佳实践

### 1. 数据准备和格式化

#### 数据结构标准化
```typescript
// 推荐：使用标准化的数据结构
interface ChartDataStructure {
  categories: string[];           // X轴分类数据
  series: SeriesData[];          // 系列数据
  metadata?: ChartMetadata;      // 元数据信息
}

interface SeriesData {
  name: string;                  // 系列名称
  type: string;                  // 图表类型
  data: number[] | object[];     // 数据数组
  color?: string;                // 自定义颜色
}

// 示例：标准化数据结构
const chartData = {
  categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
  series: [
    {
      name: '销售额',
      type: 'bar',
      data: [120, 200, 150, 80, 70, 110],
      color: '#5470c6'
    },
    {
      name: '利润',
      type: 'line',
      data: [60, 100, 75, 40, 35, 55],
      color: '#91cc75'
    }
  ]
};
```

#### 数据验证和清洗
```typescript
// 数据验证函数
const validateChartData = (data: any[]): boolean => {
  if (!Array.isArray(data)) return false;
  
  return data.every(item => {
    // 检查数据类型
    if (typeof item === 'number') return !isNaN(item);
    if (typeof item === 'object') return item.value !== undefined;
    return false;
  });
};

// 数据清洗函数
const cleanChartData = (data: any[]): number[] => {
  return data
    .filter(item => item !== null && item !== undefined)
    .map(item => {
      if (typeof item === 'number') return item;
      if (typeof item === 'object' && item.value) return Number(item.value);
      return 0;
    });
};

// 使用示例
const rawData = [120, null, 150, undefined, '80', { value: 70 }];
const cleanData = cleanChartData(rawData); // [120, 150, 80, 70]
```

### 2. 性能优化策略

#### 大数据量处理
```vue
<template>
  <FuniChart
    :option="optimizedOption"
    :auto-resize="false"
    height="400px"
  />
</template>

<script setup>
import { ref, computed } from 'vue';

const rawData = ref([]); // 大量数据

// 性能优化配置
const optimizedOption = computed(() => ({
  // 关闭动画以提升性能
  animation: false,
  
  // 渐进式渲染配置
  progressive: 1000,
  progressiveThreshold: 3000,
  
  // 悬停层阈值
  hoverLayerThreshold: 3000,
  
  // 使用采样器减少数据点
  dataZoom: [{
    type: 'inside',
    start: 0,
    end: 100
  }],
  
  // 系列配置
  series: [{
    type: 'line',
    data: rawData.value,
    // 大数据量时使用采样
    sampling: 'average',
    // 关闭符号显示
    showSymbol: false,
    // 线条优化
    lineStyle: {
      width: 1
    }
  }]
}));
</script>
```

#### 内存管理
```vue
<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';

const chartRef = ref();
const resizeObserver = ref();

// 响应式调整大小
const setupResizeObserver = () => {
  if (!window.ResizeObserver) return;
  
  resizeObserver.value = new ResizeObserver(() => {
    nextTick(() => {
      chartRef.value?.refresh();
    });
  });
  
  const container = chartRef.value?.$el?.parentElement;
  if (container) {
    resizeObserver.value.observe(container);
  }
};

onMounted(() => {
  setupResizeObserver();
});

onUnmounted(() => {
  // 清理资源
  if (resizeObserver.value) {
    resizeObserver.value.disconnect();
  }
});
</script>
```

### 3. 响应式设计

#### 自适应布局
```vue
<template>
  <div class="chart-responsive-container">
    <FuniChart
      :option="responsiveOption"
      :width="chartWidth"
      :height="chartHeight"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

const containerWidth = ref(0);
const containerHeight = ref(0);

// 响应式尺寸计算
const chartWidth = computed(() => {
  if (containerWidth.value < 768) return '100%';
  return containerWidth.value - 40; // 留出边距
});

const chartHeight = computed(() => {
  if (containerWidth.value < 768) return '300px';
  return '400px';
});

// 响应式配置
const responsiveOption = computed(() => ({
  title: {
    text: '响应式图表',
    textStyle: {
      fontSize: containerWidth.value < 768 ? 14 : 18
    }
  },
  legend: {
    orient: containerWidth.value < 768 ? 'horizontal' : 'vertical',
    top: containerWidth.value < 768 ? 'bottom' : 'top'
  },
  grid: {
    left: containerWidth.value < 768 ? '10%' : '3%',
    right: containerWidth.value < 768 ? '10%' : '4%',
    bottom: containerWidth.value < 768 ? '15%' : '3%',
    containLabel: true
  }
}));

// 监听容器尺寸变化
const updateContainerSize = () => {
  const container = document.querySelector('.chart-responsive-container');
  if (container) {
    containerWidth.value = container.clientWidth;
    containerHeight.value = container.clientHeight;
  }
};

onMounted(() => {
  updateContainerSize();
  window.addEventListener('resize', updateContainerSize);
});

onUnmounted(() => {
  window.removeEventListener('resize', updateContainerSize);
});
</script>

<style scoped>
.chart-responsive-container {
  width: 100%;
  height: 100%;
  min-height: 300px;
}

@media (max-width: 768px) {
  .chart-responsive-container {
    min-height: 250px;
  }
}
</style>
```

### 4. 主题和样式定制

#### 自定义主题
```typescript
// 企业主题配置
const enterpriseTheme = {
  color: [
    '#1890ff', '#52c41a', '#faad14', '#f5222d', 
    '#722ed1', '#13c2c2', '#eb2f96', '#fa541c'
  ],
  backgroundColor: '#ffffff',
  textStyle: {
    fontFamily: 'PingFang SC, Microsoft YaHei, sans-serif',
    fontSize: 12,
    color: '#333333'
  },
  title: {
    textStyle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: '#262626'
    }
  },
  legend: {
    textStyle: {
      fontSize: 12,
      color: '#595959'
    }
  },
  categoryAxis: {
    axisLine: {
      lineStyle: { color: '#d9d9d9' }
    },
    axisTick: {
      lineStyle: { color: '#d9d9d9' }
    },
    axisLabel: {
      color: '#8c8c8c'
    },
    splitLine: {
      lineStyle: { color: '#f0f0f0' }
    }
  },
  valueAxis: {
    axisLine: {
      lineStyle: { color: '#d9d9d9' }
    },
    axisTick: {
      lineStyle: { color: '#d9d9d9' }
    },
    axisLabel: {
      color: '#8c8c8c'
    },
    splitLine: {
      lineStyle: { color: '#f0f0f0' }
    }
  }
};

// 注册和使用主题
import * as echarts from 'echarts';

echarts.registerTheme('enterprise', enterpriseTheme);
```

#### 动态主题切换
```vue
<template>
  <div>
    <el-radio-group v-model="currentTheme" @change="handleThemeChange">
      <el-radio-button label="default">默认</el-radio-button>
      <el-radio-button label="dark">深色</el-radio-button>
      <el-radio-button label="enterprise">企业</el-radio-button>
    </el-radio-group>
    
    <FuniChart
      :option="chartOption"
      :theme="currentTheme"
      height="400px"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';

const currentTheme = ref('default');
const chartOption = ref({
  // 图表配置
});

const handleThemeChange = (theme) => {
  console.log('主题切换至:', theme);
  // 可以在这里添加主题切换的额外逻辑
};
</script>
```

### 5. 交互和事件处理

#### 事件处理最佳实践
```vue
<template>
  <FuniChart
    :option="chartOption"
    @click="handleChartClick"
    @dblclick="handleChartDblClick"
    @mouseover="handleChartHover"
    @mouseout="handleChartLeave"
  />
</template>

<script setup>
import { ref } from 'vue';

// 事件处理函数
const handleChartClick = (params) => {
  // 参数验证
  if (!params || !params.data) {
    console.warn('无效的点击事件参数');
    return;
  }
  
  // 根据图表类型处理不同的点击逻辑
  switch (params.componentType) {
    case 'series':
      handleSeriesClick(params);
      break;
    case 'legend':
      handleLegendClick(params);
      break;
    default:
      console.log('其他组件点击:', params);
  }
};

const handleSeriesClick = (params) => {
  console.log('系列点击:', {
    seriesName: params.seriesName,
    dataIndex: params.dataIndex,
    value: params.value,
    name: params.name
  });
  
  // 可以触发数据钻取、详情查看等操作
  // emit('drill-down', params);
};

const handleLegendClick = (params) => {
  console.log('图例点击:', params.name);
  // 可以处理图例的显示/隐藏逻辑
};

const handleChartDblClick = (params) => {
  // 双击事件，可以用于全屏显示等
  console.log('图表双击:', params);
};

const handleChartHover = (params) => {
  // 悬停事件，可以用于显示详细信息
  if (params.data) {
    console.log('悬停数据:', params.data);
  }
};

const handleChartLeave = () => {
  // 鼠标离开事件，可以用于隐藏详细信息
  console.log('鼠标离开图表');
};
</script>
```

### 6. 数据更新策略

#### 增量数据更新
```vue
<script setup>
import { ref, watch } from 'vue';

const chartData = ref([]);
const chartOption = ref({});

// 增量更新数据
const appendData = (newData) => {
  const maxDataPoints = 100; // 最大数据点数
  
  chartData.value.push(...newData);
  
  // 保持数据点数量在合理范围内
  if (chartData.value.length > maxDataPoints) {
    chartData.value = chartData.value.slice(-maxDataPoints);
  }
  
  // 更新图表配置
  updateChartOption();
};

// 批量更新数据
const updateData = (newData) => {
  chartData.value = [...newData];
  updateChartOption();
};

const updateChartOption = () => {
  chartOption.value = {
    ...chartOption.value,
    series: [{
      ...chartOption.value.series[0],
      data: chartData.value
    }]
  };
};

// 监听数据变化
watch(chartData, (newData) => {
  console.log('数据更新:', newData.length, '个数据点');
}, { deep: true });
</script>
```

### 7. 错误处理和容错

#### 数据异常处理
```vue
<script setup>
import { ref, computed } from 'vue';

const rawData = ref([]);
const errorMessage = ref('');

// 安全的图表配置
const safeChartOption = computed(() => {
  try {
    // 数据验证
    if (!rawData.value || rawData.value.length === 0) {
      return getEmptyChartOption();
    }
    
    // 数据处理
    const processedData = processChartData(rawData.value);
    
    return {
      title: { text: '数据图表' },
      tooltip: { trigger: 'axis' },
      xAxis: { type: 'category', data: processedData.categories },
      yAxis: { type: 'value' },
      series: processedData.series
    };
  } catch (error) {
    console.error('图表配置生成失败:', error);
    errorMessage.value = '图表配置生成失败';
    return getErrorChartOption();
  }
});

// 空数据图表配置
const getEmptyChartOption = () => ({
  title: {
    text: '暂无数据',
    left: 'center',
    top: 'center',
    textStyle: { color: '#999' }
  },
  xAxis: { show: false },
  yAxis: { show: false },
  series: []
});

// 错误状态图表配置
const getErrorChartOption = () => ({
  title: {
    text: '数据加载失败',
    left: 'center',
    top: 'center',
    textStyle: { color: '#ff4d4f' }
  },
  xAxis: { show: false },
  yAxis: { show: false },
  series: []
});

// 数据处理函数
const processChartData = (data) => {
  // 数据处理逻辑
  return {
    categories: data.map(item => item.name),
    series: [{
      type: 'bar',
      data: data.map(item => item.value)
    }]
  };
};
</script>
```

## 避免的常见错误

### 1. 性能问题
```typescript
// ❌ 错误：频繁更新大量数据
const badPractice = () => {
  setInterval(() => {
    chartData.value = generateLargeDataset(10000); // 每秒更新1万个数据点
  }, 1000);
};

// ✅ 正确：合理的数据更新频率
const goodPractice = () => {
  setInterval(() => {
    const newPoint = generateDataPoint();
    chartData.value.push(newPoint);
    if (chartData.value.length > 100) {
      chartData.value.shift(); // 保持数据量稳定
    }
  }, 5000); // 5秒更新一次
};
```

### 2. 内存泄漏
```typescript
// ❌ 错误：未清理事件监听器
const badCleanup = () => {
  window.addEventListener('resize', handleResize);
  // 组件销毁时未移除监听器
};

// ✅ 正确：正确清理资源
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (resizeObserver.value) {
    resizeObserver.value.disconnect();
  }
});
```

### 3. 数据格式错误
```typescript
// ❌ 错误：不一致的数据格式
const inconsistentData = [
  { name: 'A', value: 10 },
  ['B', 20],  // 格式不一致
  30          // 格式不一致
];

// ✅ 正确：统一的数据格式
const consistentData = [
  { name: 'A', value: 10 },
  { name: 'B', value: 20 },
  { name: 'C', value: 30 }
];
```

## 总结

FuniChart的最佳实践包括：

1. **数据管理** - 标准化数据结构，验证和清洗数据
2. **性能优化** - 合理配置渲染参数，管理数据量
3. **响应式设计** - 自适应布局，动态配置
4. **主题定制** - 统一的视觉风格，支持主题切换
5. **交互处理** - 完善的事件处理，用户体验优化
6. **错误处理** - 容错机制，优雅降级
7. **资源管理** - 正确的生命周期管理，避免内存泄漏

遵循这些最佳实践可以确保FuniChart在各种场景下都能提供稳定、高效的数据可视化体验。
