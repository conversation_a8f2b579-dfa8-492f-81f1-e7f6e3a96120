# FuniChart ElementPlus API支持

## 组件基础说明

FuniChart是基于ECharts图表库封装的图表组件，**不是基于ElementPlus组件封装**，因此不支持ElementPlus API透传。

## 技术架构

### 核心依赖
- **ECharts**: Apache ECharts图表库
- **Vue 3**: 组合式API
- **响应式系统**: Vue 3 Reactivity

### 组件特点
```typescript
// FuniChart的技术特点
interface FuniChartFeatures {
  baseLibrary: 'ECharts';           // 基于ECharts，非ElementPlus
  renderEngine: 'Canvas | SVG';     // 支持Canvas和SVG渲染
  dataBinding: 'Reactive';          // Vue响应式数据绑定
  eventSystem: 'ECharts Events';    // ECharts原生事件系统
  themeSystem: 'ECharts Themes';    // ECharts主题系统
}
```

## ECharts API支持

### 完整ECharts配置支持
FuniChart通过`option`属性支持完整的ECharts配置：

```vue
<template>
  <FuniChart :option="echartsOption" />
</template>

<script setup>
const echartsOption = {
  // 完整的ECharts配置对象
  title: { text: '图表标题' },
  tooltip: { trigger: 'axis' },
  legend: { data: ['系列1', '系列2'] },
  xAxis: { type: 'category', data: ['A', 'B', 'C'] },
  yAxis: { type: 'value' },
  series: [
    { name: '系列1', type: 'bar', data: [10, 20, 30] },
    { name: '系列2', type: 'line', data: [15, 25, 35] }
  ]
}
</script>
```

### 支持的ECharts功能

#### 1. 图表类型
```typescript
// 支持所有ECharts图表类型
const supportedChartTypes = [
  'line',          // 折线图
  'bar',           // 柱状图
  'pie',           // 饼图
  'scatter',       // 散点图
  'radar',         // 雷达图
  'map',           // 地图
  'tree',          // 树图
  'treemap',       // 矩形树图
  'sunburst',      // 旭日图
  'parallel',      // 平行坐标
  'sankey',        // 桑基图
  'funnel',        // 漏斗图
  'gauge',         // 仪表盘
  'graph',         // 关系图
  'heatmap',       // 热力图
  'boxplot',       // 箱线图
  'candlestick',   // K线图
  'themeRiver',    // 主题河流图
  'custom'         // 自定义图表
];
```

#### 2. 交互功能
```typescript
// 支持的ECharts交互功能
const supportedInteractions = {
  brush: true,           // 刷选
  dataZoom: true,        // 数据缩放
  legend: true,          // 图例交互
  tooltip: true,         // 提示框
  animation: true,       // 动画效果
  emphasis: true,        // 高亮效果
  select: true,          // 选择功能
  focus: true,           // 聚焦功能
  blur: true             // 模糊效果
};
```

#### 3. 事件系统
```vue
<template>
  <FuniChart 
    :option="option"
    @click="handleClick"
    @dblclick="handleDblClick"
    @mousedown="handleMouseDown"
    @mousemove="handleMouseMove"
    @mouseup="handleMouseUp"
    @mouseover="handleMouseOver"
    @mouseout="handleMouseOut"
    @globalout="handleGlobalOut"
    @contextmenu="handleContextMenu"
  />
</template>

<script setup>
// 所有事件参数都是ECharts原生事件参数
const handleClick = (params) => {
  // params: ECharts点击事件参数
  console.log('图表点击:', params);
};

const handleDblClick = (params) => {
  // params: ECharts双击事件参数
  console.log('图表双击:', params);
};
</script>
```

## 与ElementPlus的区别

### 设计理念差异
```typescript
// ElementPlus组件特点
interface ElementPlusComponent {
  baseFramework: 'Vue 3';
  designSystem: 'Element Design';
  apiStyle: 'Props + Events + Slots';
  themeSystem: 'CSS Variables';
  accessibility: 'ARIA Support';
}

// FuniChart特点
interface FuniChartComponent {
  baseLibrary: 'ECharts';
  designSystem: 'Data Visualization';
  apiStyle: 'ECharts Configuration';
  themeSystem: 'ECharts Themes';
  accessibility: 'ECharts ARIA';
}
```

### API风格对比
```vue
<!-- ElementPlus风格 (如el-table) -->
<el-table 
  :data="tableData"
  :columns="columns"
  @selection-change="handleSelectionChange"
>
  <el-table-column prop="name" label="姓名" />
  <el-table-column prop="age" label="年龄" />
</el-table>

<!-- FuniChart风格 -->
<FuniChart :option="chartOption" @click="handleClick" />
```

## 主题系统

### ECharts主题支持
```vue
<template>
  <!-- 使用内置主题 -->
  <FuniChart :option="option" theme="dark" />
  
  <!-- 使用自定义主题 -->
  <FuniChart :option="option" theme="custom-theme" />
</template>

<script setup>
import * as echarts from 'echarts';

// 注册自定义主题
echarts.registerTheme('custom-theme', {
  color: ['#ff7f50', '#87ceeb', '#da70d6', '#32cd32', '#6495ed'],
  backgroundColor: '#f5f5f5',
  textStyle: {
    color: '#333'
  }
});
</script>
```

### 主题配置示例
```typescript
// 支持的主题类型
type ThemeType = 'default' | 'dark' | 'light' | string;

// 主题配置
const themeConfig = {
  default: {
    backgroundColor: '#ffffff',
    textStyle: { color: '#333333' }
  },
  dark: {
    backgroundColor: '#1e1e1e',
    textStyle: { color: '#ffffff' }
  }
};
```

## 响应式支持

### 自动调整大小
```vue
<template>
  <div class="chart-container">
    <!-- 自动响应容器大小变化 -->
    <FuniChart 
      :option="option" 
      :auto-resize="true"
      width="100%"
      height="400px"
    />
  </div>
</template>

<style>
.chart-container {
  width: 100%;
  height: 100%;
  resize: both;
  overflow: auto;
}
</style>
```

### 手动调整大小
```vue
<script setup>
import { ref, onMounted } from 'vue';

const chartRef = ref();

const handleResize = () => {
  // 手动触发图表重新调整大小
  chartRef.value?.refresh();
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
});
</script>
```

## 性能优化

### 大数据量处理
```typescript
// 大数据量优化配置
const largeDataOption = {
  animation: false,              // 关闭动画
  progressive: 1000,             // 渐进式渲染
  progressiveThreshold: 3000,    // 渐进式阈值
  hoverLayerThreshold: 3000,     // 悬停层阈值
  useUTC: true                   // 使用UTC时间
};
```

### 内存管理
```vue
<script setup>
import { onUnmounted } from 'vue';

onUnmounted(() => {
  // 组件会自动清理ECharts实例
  // 无需手动调用dispose
});
</script>
```

## 使用注意事项

### 1. 数据格式要求
```typescript
// 确保数据格式符合ECharts要求
const correctDataFormat = {
  xAxis: {
    type: 'category',
    data: ['A', 'B', 'C']  // 字符串数组
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    type: 'bar',
    data: [10, 20, 30]     // 数值数组
  }]
};
```

### 2. 响应式数据更新
```vue
<script setup>
import { ref, watch } from 'vue';

const chartData = ref([]);
const chartOption = ref({});

// 监听数据变化，自动更新图表
watch(chartData, (newData) => {
  chartOption.value = {
    ...chartOption.value,
    series: [{ data: newData }]
  };
}, { deep: true });
</script>
```

### 3. 事件处理最佳实践
```vue
<script setup>
const handleChartEvent = (params) => {
  // 检查事件参数有效性
  if (!params || !params.data) {
    return;
  }
  
  // 处理事件逻辑
  console.log('事件数据:', params.data);
};
</script>
```

## 总结

FuniChart作为ECharts的Vue 3封装组件，提供了：

1. **完整的ECharts API支持** - 通过option属性支持所有ECharts配置
2. **Vue 3响应式集成** - 自动响应数据变化
3. **事件系统透传** - 支持所有ECharts事件
4. **主题系统支持** - 支持ECharts主题切换
5. **性能优化** - 自动内存管理和大数据优化
6. **响应式布局** - 自动适应容器大小变化

**重要提醒**: FuniChart不支持ElementPlus API，所有配置和交互都基于ECharts标准。
