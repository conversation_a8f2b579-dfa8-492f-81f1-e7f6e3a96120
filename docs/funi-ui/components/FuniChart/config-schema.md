# FuniChart 配置结构

## 基础配置结构

```typescript
interface FuniChartConfig {
  // 图表配置
  option: EChartsOption;           // ECharts配置选项（必填）
  
  // 尺寸配置
  width?: string | number;         // 图表宽度，默认'100%'
  height?: string | number;        // 图表高度，默认'400px'
  
  // 主题配置
  theme?: string;                  // 图表主题，默认'default'
  
  // 状态配置
  loading?: boolean;               // 是否显示加载状态
  loadingOption?: LoadingOption;   // 加载配置选项
  
  // 图表配置
  group?: string;                  // 图表分组名称
  autoResize?: boolean;            // 是否自动调整大小，默认true
  notMerge?: boolean;              // 是否不合并配置，默认false
  lazyUpdate?: boolean;            // 是否延迟更新，默认false
  silent?: boolean;                // 是否静默模式，默认false
}
```

## ECharts配置结构

### 基础图表配置
```typescript
interface EChartsOption {
  // 标题配置
  title?: TitleOption;
  
  // 图例配置
  legend?: LegendOption;
  
  // 网格配置
  grid?: GridOption | GridOption[];
  
  // 坐标轴配置
  xAxis?: XAxisOption | XAxisOption[];
  yAxis?: YAxisOption | YAxisOption[];
  
  // 极坐标配置
  polar?: PolarOption | PolarOption[];
  radiusAxis?: RadiusAxisOption | RadiusAxisOption[];
  angleAxis?: AngleAxisOption | AngleAxisOption[];
  
  // 雷达图配置
  radar?: RadarOption | RadarOption[];
  
  // 数据区域缩放配置
  dataZoom?: DataZoomOption | DataZoomOption[];
  
  // 视觉映射配置
  visualMap?: VisualMapOption | VisualMapOption[];
  
  // 提示框配置
  tooltip?: TooltipOption;
  
  // 坐标轴指示器配置
  axisPointer?: AxisPointerOption;
  
  // 工具箱配置
  toolbox?: ToolboxOption;
  
  // 刷选配置
  brush?: BrushOption;
  
  // 地理坐标配置
  geo?: GeoOption | GeoOption[];
  
  // 平行坐标配置
  parallel?: ParallelOption | ParallelOption[];
  parallelAxis?: ParallelAxisOption | ParallelAxisOption[];
  
  // 单轴配置
  singleAxis?: SingleAxisOption | SingleAxisOption[];
  
  // 时间轴配置
  timeline?: TimelineOption;
  
  // 图形配置
  graphic?: GraphicOption | GraphicOption[];
  
  // 日历配置
  calendar?: CalendarOption | CalendarOption[];
  
  // 数据集配置
  dataset?: DatasetOption | DatasetOption[];
  
  // 无障碍访问配置
  aria?: AriaOption;
  
  // 系列配置
  series?: SeriesOption | SeriesOption[];
  
  // 颜色配置
  color?: ColorOption[];
  
  // 背景色
  backgroundColor?: string;
  
  // 文本样式
  textStyle?: TextStyleOption;
  
  // 动画配置
  animation?: boolean;
  animationThreshold?: number;
  animationDuration?: number | Function;
  animationEasing?: string;
  animationDelay?: number | Function;
  animationDurationUpdate?: number | Function;
  animationEasingUpdate?: string;
  animationDelayUpdate?: number | Function;
  
  // 混合模式
  blendMode?: string;
  
  // 鼠标悬停层配置
  hoverLayerThreshold?: number;
  
  // 是否使用UTC时间
  useUTC?: boolean;
  
  // 媒体查询配置
  media?: MediaOption[];
}
```

### 系列配置结构
```typescript
interface SeriesOption {
  // 基础配置
  type: 'line' | 'bar' | 'pie' | 'scatter' | 'effectScatter' | 'radar' | 'tree' | 'treemap' | 'sunburst' | 'boxplot' | 'candlestick' | 'heatmap' | 'map' | 'parallel' | 'lines' | 'graph' | 'sankey' | 'funnel' | 'gauge' | 'pictorialBar' | 'themeRiver' | 'custom';
  name?: string;                   // 系列名称
  id?: string;                     // 系列ID
  
  // 数据配置
  data?: any[];                    // 系列数据
  datasetIndex?: number;           // 数据集索引
  datasetId?: string;              // 数据集ID
  
  // 坐标系配置
  coordinateSystem?: 'cartesian2d' | 'polar' | 'geo' | 'parallel' | 'singleAxis' | 'calendar';
  xAxisIndex?: number;             // x轴索引
  yAxisIndex?: number;             // y轴索引
  polarIndex?: number;             // 极坐标索引
  geoIndex?: number;               // 地理坐标索引
  parallelIndex?: number;          // 平行坐标索引
  calendarIndex?: number;          // 日历坐标索引
  
  // 样式配置
  itemStyle?: ItemStyleOption;     // 图形样式
  lineStyle?: LineStyleOption;     // 线条样式
  areaStyle?: AreaStyleOption;     // 区域样式
  emphasis?: EmphasisOption;       // 高亮样式
  blur?: BlurOption;               // 淡出样式
  select?: SelectOption;           // 选中样式
  
  // 标签配置
  label?: LabelOption;             // 标签配置
  labelLine?: LabelLineOption;     // 标签引导线配置
  labelLayout?: LabelLayoutOption; // 标签布局配置
  
  // 标记配置
  markPoint?: MarkPointOption;     // 标记点配置
  markLine?: MarkLineOption;       // 标记线配置
  markArea?: MarkAreaOption;       // 标记区域配置
  
  // 动画配置
  animation?: boolean;
  animationThreshold?: number;
  animationDuration?: number | Function;
  animationEasing?: string;
  animationDelay?: number | Function;
  animationDurationUpdate?: number | Function;
  animationEasingUpdate?: string;
  animationDelayUpdate?: number | Function;
  
  // 其他配置
  silent?: boolean;                // 是否响应鼠标事件
  legendHoverLink?: boolean;       // 是否启用图例hover联动高亮
  cursor?: string;                 // 鼠标样式
  
  // 特定图表类型配置
  [key: string]: any;              // 其他特定配置
}
```

## 常用图表配置模板

### 柱状图配置
```typescript
const barChartOption: EChartsOption = {
  title: {
    text: '柱状图示例',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  legend: {
    data: ['销售额', '利润'],
    top: 30
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月'],
    axisTick: {
      alignWithLabel: true
    }
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '销售额',
      type: 'bar',
      data: [120, 200, 150, 80, 70, 110],
      itemStyle: {
        color: '#5470c6'
      }
    },
    {
      name: '利润',
      type: 'bar',
      data: [60, 100, 75, 40, 35, 55],
      itemStyle: {
        color: '#91cc75'
      }
    }
  ]
};
```

### 折线图配置
```typescript
const lineChartOption: EChartsOption = {
  title: {
    text: '折线图示例'
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['邮件营销', '联盟广告', '视频广告', '直接访问', '搜索引擎']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  toolbox: {
    feature: {
      saveAsImage: {}
    }
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '邮件营销',
      type: 'line',
      stack: '总量',
      data: [120, 132, 101, 134, 90, 230, 210]
    },
    {
      name: '联盟广告',
      type: 'line',
      stack: '总量',
      data: [220, 182, 191, 234, 290, 330, 310]
    }
  ]
};
```

### 饼图配置
```typescript
const pieChartOption: EChartsOption = {
  title: {
    text: '饼图示例',
    subtext: '纯属虚构',
    left: 'center'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b} : {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '访问来源',
      type: 'pie',
      radius: '50%',
      center: ['50%', '60%'],
      data: [
        { value: 1048, name: '搜索引擎' },
        { value: 735, name: '直接访问' },
        { value: 580, name: '邮件营销' },
        { value: 484, name: '联盟广告' },
        { value: 300, name: '视频广告' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
};
```

### 散点图配置
```typescript
const scatterChartOption: EChartsOption = {
  title: {
    text: '散点图示例'
  },
  tooltip: {
    trigger: 'axis',
    showDelay: 0,
    formatter: (params: any) => {
      if (params.value.length > 1) {
        return params.seriesName + ' :<br/>'
          + params.value[0] + 'cm '
          + params.value[1] + 'kg ';
      } else {
        return params.seriesName + ' :<br/>'
          + params.name + ' : '
          + params.value + 'kg ';
      }
    },
    axisPointer: {
      show: true,
      type: 'cross',
      lineStyle: {
        type: 'dashed',
        width: 1
      }
    }
  },
  legend: {
    data: ['女性', '男性']
  },
  toolbox: {
    feature: {
      dataZoom: {},
      brush: {
        type: ['rect', 'polygon', 'clear']
      }
    }
  },
  xAxis: {
    type: 'value',
    scale: true,
    axisLabel: {
      formatter: '{value} cm'
    },
    splitLine: {
      show: false
    }
  },
  yAxis: {
    type: 'value',
    scale: true,
    axisLabel: {
      formatter: '{value} kg'
    },
    splitLine: {
      show: false
    }
  },
  series: [
    {
      name: '女性',
      type: 'scatter',
      data: [[161.2, 51.6], [167.5, 59.0], [159.5, 49.2]],
      markArea: {
        silent: true,
        itemStyle: {
          color: 'transparent',
          borderWidth: 1,
          borderType: 'dashed'
        },
        data: [[{
          name: '女性分布区间',
          xAxis: 'min',
          yAxis: 'min'
        }, {
          xAxis: 'max',
          yAxis: 'max'
        }]]
      },
      markPoint: {
        data: [
          { type: 'max', name: '最大值' },
          { type: 'min', name: '最小值' }
        ]
      },
      markLine: {
        lineStyle: {
          type: 'solid'
        },
        data: [
          { type: 'average', name: '平均值' },
          { xAxis: 160 }
        ]
      }
    }
  ]
};
```

## 主题配置结构

### 内置主题
```typescript
type ThemeName = 
  | 'default'      // 默认主题
  | 'dark'         // 暗色主题
  | 'vintage'      // 复古主题
  | 'macarons'     // 马卡龙主题
  | 'infographic'  // 信息图主题
  | 'shine'        // 闪亮主题
  | 'roma'         // 罗马主题
  | string;        // 自定义主题名
```

### 自定义主题结构
```typescript
interface CustomTheme {
  // 颜色配置
  color?: string[];                // 调色盘颜色列表
  backgroundColor?: string;        // 背景色
  
  // 文本样式
  textStyle?: {
    color?: string;
    fontStyle?: string;
    fontWeight?: string;
    fontFamily?: string;
    fontSize?: number;
  };
  
  // 标题样式
  title?: {
    textStyle?: TextStyleOption;
    subtextStyle?: TextStyleOption;
  };
  
  // 图例样式
  legend?: {
    textStyle?: TextStyleOption;
  };
  
  // 坐标轴样式
  categoryAxis?: AxisStyleOption;
  valueAxis?: AxisStyleOption;
  logAxis?: AxisStyleOption;
  timeAxis?: AxisStyleOption;
  
  // 线条样式
  line?: {
    itemStyle?: ItemStyleOption;
    lineStyle?: LineStyleOption;
    symbolSize?: number;
    symbol?: string;
    smooth?: boolean;
  };
  
  // 柱状图样式
  bar?: {
    itemStyle?: ItemStyleOption;
  };
  
  // 饼图样式
  pie?: {
    itemStyle?: ItemStyleOption;
  };
  
  // 散点图样式
  scatter?: {
    itemStyle?: ItemStyleOption;
  };
  
  // 其他图表样式
  [key: string]: any;
}
```

## 加载配置结构

```typescript
interface LoadingOption {
  text?: string;                   // 加载文本
  color?: string;                  // 加载颜色
  textColor?: string;              // 文本颜色
  maskColor?: string;              // 遮罩颜色
  zlevel?: number;                 // 加载层级
  
  // 加载动画配置
  fontSize?: number;               // 字体大小
  showSpinner?: boolean;           // 是否显示旋转器
  spinnerRadius?: number;          // 旋转器半径
  lineWidth?: number;              // 线条宽度
  fontWeight?: string;             // 字体粗细
  fontStyle?: string;              // 字体样式
  fontFamily?: string;             // 字体族
}
```

## 响应式配置

### 媒体查询配置
```typescript
interface MediaOption {
  query?: {
    minWidth?: number;
    maxWidth?: number;
    minHeight?: number;
    maxHeight?: number;
    minAspectRatio?: number;
    maxAspectRatio?: number;
  };
  option: EChartsOption;           // 对应的图表配置
}

// 使用示例
const responsiveOption: EChartsOption = {
  baseOption: {
    // 基础配置
  },
  media: [
    {
      query: { maxWidth: 500 },
      option: {
        // 小屏幕配置
        grid: { left: '10%', right: '10%' },
        legend: { orient: 'horizontal', bottom: 0 }
      }
    },
    {
      query: { minWidth: 500, maxWidth: 800 },
      option: {
        // 中等屏幕配置
        grid: { left: '5%', right: '5%' }
      }
    },
    {
      query: { minWidth: 800 },
      option: {
        // 大屏幕配置
        grid: { left: '3%', right: '3%' }
      }
    }
  ]
};
```

## 最佳实践建议

### 1. 性能优化配置
```typescript
const performanceOption = {
  // 大数据量时使用采样
  animation: false,              // 关闭动画
  progressive: 1000,             // 渐进式渲染阈值
  progressiveThreshold: 3000,    // 渐进式渲染数据阈值
  
  // 使用Canvas渲染器
  renderer: 'canvas',
  
  // 优化配置
  hoverLayerThreshold: 3000,     // 悬停层阈值
  useUTC: true                   // 使用UTC时间
};
```

### 2. 无障碍访问配置
```typescript
const accessibilityOption = {
  aria: {
    enabled: true,
    label: {
      description: '这是一个关于销售数据的柱状图'
    }
  }
};
```

### 3. 数据更新配置
```typescript
const updateOption = {
  notMerge: false,               // 合并配置
  lazyUpdate: false,             // 立即更新
  silent: false                  // 触发事件
};
```
