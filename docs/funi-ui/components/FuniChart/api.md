# FuniChart API文档

## 组件概述

FuniChart是基于ECharts封装的图表组件，支持多种图表类型，提供了简化的配置方式和响应式功能，适用于数据可视化场景。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| option | Object | {} | ✅ | ECharts配置选项 |
| width | String/Number | '100%' | - | 图表宽度 |
| height | String/Number | '400px' | - | 图表高度 |
| theme | String | 'default' | - | 图表主题 |
| loading | Boolean | false | - | 是否显示加载状态 |
| loadingOption | Object | {} | - | 加载配置选项 |
| group | String | - | - | 图表分组名称 |
| autoResize | Boolean | true | - | 是否自动调整大小 |
| notMerge | Boolean | false | - | 是否不合并配置 |
| lazyUpdate | Boolean | false | - | 是否延迟更新 |
| silent | Boolean | false | - | 是否静默模式 |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| click | params: Object | 点击事件 | 点击图表元素时 |
| dblclick | params: Object | 双击事件 | 双击图表元素时 |
| mousedown | params: Object | 鼠标按下事件 | 鼠标按下时 |
| mousemove | params: Object | 鼠标移动事件 | 鼠标移动时 |
| mouseup | params: Object | 鼠标抬起事件 | 鼠标抬起时 |
| mouseover | params: Object | 鼠标悬停事件 | 鼠标悬停时 |
| mouseout | params: Object | 鼠标离开事件 | 鼠标离开时 |
| globalout | params: Object | 全局鼠标离开事件 | 鼠标离开图表区域时 |
| contextmenu | params: Object | 右键菜单事件 | 右键点击时 |
| legendselectchanged | params: Object | 图例选择变化事件 | 图例选择状态变化时 |
| datazoom | params: Object | 数据缩放事件 | 数据缩放时 |
| datarangeselected | params: Object | 数据范围选择事件 | 数据范围选择时 |
| timelinechanged | params: Object | 时间轴变化事件 | 时间轴变化时 |
| timelineplaychanged | params: Object | 时间轴播放状态变化事件 | 时间轴播放状态变化时 |
| restore | params: Object | 还原事件 | 图表还原时 |
| dataviewchanged | params: Object | 数据视图变化事件 | 数据视图变化时 |
| magictypechanged | params: Object | 动态类型切换事件 | 图表类型切换时 |
| geoselectchanged | params: Object | 地理坐标选择变化事件 | 地理坐标选择变化时 |
| geoselected | params: Object | 地理坐标选择事件 | 地理坐标选择时 |
| geounselected | params: Object | 地理坐标取消选择事件 | 地理坐标取消选择时 |
| pieselectchanged | params: Object | 饼图选择变化事件 | 饼图选择状态变化时 |
| pieselected | params: Object | 饼图选择事件 | 饼图选择时 |
| pieunselected | params: Object | 饼图取消选择事件 | 饼图取消选择时 |
| mapselectchanged | params: Object | 地图选择变化事件 | 地图选择状态变化时 |
| mapselected | params: Object | 地图选择事件 | 地图选择时 |
| mapunselected | params: Object | 地图取消选择事件 | 地图取消选择时 |
| axisareaselected | params: Object | 坐标轴区域选择事件 | 坐标轴区域选择时 |
| focusnodeadjacency | params: Object | 聚焦节点邻接事件 | 聚焦节点邻接时 |
| unfocusnodeadjacency | params: Object | 取消聚焦节点邻接事件 | 取消聚焦节点邻接时 |
| brush | params: Object | 刷选事件 | 刷选时 |
| brushselected | params: Object | 刷选选择事件 | 刷选选择时 |
| rendered | - | 渲染完成事件 | 图表渲染完成时 |
| finished | - | 渲染结束事件 | 图表渲染结束时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| setOption | (option: Object, notMerge?: Boolean, lazyUpdate?: Boolean) | void | 设置图表配置 |
| getWidth | - | Number | 获取图表宽度 |
| getHeight | - | Number | 获取图表高度 |
| getDom | - | HTMLElement | 获取图表DOM元素 |
| getOption | - | Object | 获取当前图表配置 |
| resize | (opts?: Object) | void | 调整图表大小 |
| dispatchAction | (payload: Object) | void | 触发图表行为 |
| on | (eventName: String, handler: Function, context?: Object) | void | 绑定事件 |
| off | (eventName: String, handler?: Function) | void | 解绑事件 |
| convertToPixel | (finder: Object, value: Array) | Array | 转换为像素坐标 |
| convertFromPixel | (finder: Object, value: Array) | Array | 从像素坐标转换 |
| containPixel | (finder: Object, value: Array) | Boolean | 判断像素点是否在指定区域内 |
| showLoading | (type?: String, opts?: Object) | void | 显示加载动画 |
| hideLoading | - | void | 隐藏加载动画 |
| getDataURL | (opts?: Object) | String | 获取图表图片的dataURL |
| getConnectedDataURL | (opts?: Object) | String | 获取联动图表的dataURL |
| appendData | (opts: Object) | void | 追加数据 |
| clear | - | void | 清空图表 |
| dispose | - | void | 销毁图表实例 |

## 使用示例

### 基础柱状图
```vue
<template>
  <FuniChart
    :option="chartOption"
    height="400px"
    @click="handleChartClick"
  />
</template>

<script setup>
import { ref } from 'vue'

const chartOption = ref({
  title: {
    text: '销售数据统计'
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['销售额', '利润']
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '销售额',
      type: 'bar',
      data: [120, 200, 150, 80, 70, 110]
    },
    {
      name: '利润',
      type: 'bar',
      data: [60, 100, 75, 40, 35, 55]
    }
  ]
})

const handleChartClick = (params) => {
  console.log('图表点击:', params)
}
</script>
```

### 饼图
```vue
<template>
  <FuniChart
    :option="pieOption"
    height="400px"
    theme="dark"
  />
</template>

<script setup>
import { ref } from 'vue'

const pieOption = ref({
  title: {
    text: '用户来源统计',
    left: 'center'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '访问来源',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 1048, name: '搜索引擎' },
        { value: 735, name: '直接访问' },
        { value: 580, name: '邮件营销' },
        { value: 484, name: '联盟广告' },
        { value: 300, name: '视频广告' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
})
</script>
```

### 折线图
```vue
<template>
  <FuniChart
    :option="lineOption"
    height="400px"
    :loading="loading"
  />
</template>

<script setup>
import { ref, onMounted } from 'vue'

const loading = ref(true)
const lineOption = ref({})

onMounted(async () => {
  // 模拟数据加载
  await loadData()
})

const loadData = async () => {
  loading.value = true
  
  // 模拟API请求
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  lineOption.value = {
    title: {
      text: '温度变化'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00']
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value} °C'
      }
    },
    series: [
      {
        name: '温度',
        type: 'line',
        data: [15, 12, 18, 25, 22, 16],
        smooth: true,
        itemStyle: {
          color: '#5470c6'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(84, 112, 198, 0.3)' },
              { offset: 1, color: 'rgba(84, 112, 198, 0.1)' }
            ]
          }
        }
      }
    ]
  }
  
  loading.value = false
}
</script>
```

### 响应式图表
```vue
<template>
  <div class="chart-container">
    <FuniChart
      :option="chartOption"
      :width="chartWidth"
      :height="chartHeight"
      :auto-resize="true"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const containerRef = ref()
const windowWidth = ref(window.innerWidth)
const windowHeight = ref(window.innerHeight)

const chartWidth = computed(() => {
  return windowWidth.value > 768 ? '100%' : '100%'
})

const chartHeight = computed(() => {
  return windowWidth.value > 768 ? '400px' : '300px'
})

const chartOption = ref({
  // 图表配置...
})

const handleResize = () => {
  windowWidth.value = window.innerWidth
  windowHeight.value = window.innerHeight
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
```

### 动态数据更新
```vue
<template>
  <div>
    <el-button @click="updateData">更新数据</el-button>
    <FuniChart
      :option="chartOption"
      height="400px"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const chartOption = ref({
  title: {
    text: '实时数据'
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: []
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '数值',
      type: 'line',
      data: []
    }
  ]
})

const updateData = () => {
  const now = new Date()
  const timeStr = now.toLocaleTimeString()
  const value = Math.floor(Math.random() * 100)
  
  // 更新x轴数据
  chartOption.value.xAxis.data.push(timeStr)
  if (chartOption.value.xAxis.data.length > 10) {
    chartOption.value.xAxis.data.shift()
  }
  
  // 更新系列数据
  chartOption.value.series[0].data.push(value)
  if (chartOption.value.series[0].data.length > 10) {
    chartOption.value.series[0].data.shift()
  }
}

// 定时更新数据
setInterval(updateData, 2000)
</script>
```

## 主题配置

### 内置主题
- **default**: 默认主题
- **dark**: 暗色主题
- **vintage**: 复古主题
- **macarons**: 马卡龙主题
- **infographic**: 信息图主题
- **shine**: 闪亮主题
- **roma**: 罗马主题

### 自定义主题
```javascript
// 注册自定义主题
import * as echarts from 'echarts'

echarts.registerTheme('custom', {
  color: ['#c23531', '#2f4554', '#61a0a8', '#d48265', '#91c7ae'],
  backgroundColor: 'rgba(0,0,0,0)',
  textStyle: {},
  title: {
    textStyle: {
      color: '#333333'
    }
  },
  // 更多主题配置...
})
```

## 注意事项

### 1. 性能优化
- 大数据量时使用数据采样或分页加载
- 合理设置动画效果，避免过度动画
- 使用lazyUpdate延迟更新提高性能

### 2. 响应式设计
- 启用autoResize自动调整大小
- 监听窗口大小变化手动调整图表
- 移动端适配考虑触摸交互

### 3. 内存管理
- 组件销毁时自动调用dispose方法
- 避免在option中使用大量闭包函数
- 及时清理事件监听器

### 4. 数据更新
- 使用setOption方法更新配置
- 合理使用notMerge参数控制合并策略
- 大量数据更新时考虑使用appendData

## 常见问题

### Q: 如何实现图表的动态主题切换？
A: 通过修改theme属性，或者使用setOption更新主题相关配置

### Q: 如何处理图表的点击事件？
A: 监听click事件，通过params参数获取点击的数据信息

### Q: 如何实现图表的数据钻取？
A: 在点击事件中根据点击的数据动态更新图表配置

### Q: 如何优化大数据量图表的性能？
A: 使用数据采样、虚拟滚动、分页加载等技术，避免一次性渲染大量数据
