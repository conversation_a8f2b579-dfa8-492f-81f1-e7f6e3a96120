# FuniChart 使用示例

## 基础图表示例

### 简单柱状图
```vue
<template>
  <FuniChart
    :option="barOption"
    height="400px"
    @click="handleChartClick"
  />
</template>

<script setup>
import { ref } from 'vue'

const barOption = ref({
  title: {
    text: '月度销售数据'
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    name: '销售额',
    type: 'bar',
    data: [120, 200, 150, 80, 70, 110],
    itemStyle: {
      color: '#5470c6'
    }
  }]
})

const handleChartClick = (params) => {
  console.log('点击了:', params.name, params.value)
}
</script>
```

### 多系列折线图
```vue
<template>
  <FuniChart
    :option="lineOption"
    height="400px"
    theme="dark"
  />
</template>

<script setup>
import { ref } from 'vue'

const lineOption = ref({
  title: {
    text: '网站流量统计',
    subtext: '数据来源：统计系统'
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['邮件营销', '联盟广告', '视频广告', '直接访问', '搜索引擎']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  toolbox: {
    feature: {
      saveAsImage: {}
    }
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '邮件营销',
      type: 'line',
      data: [120, 132, 101, 134, 90, 230, 210],
      smooth: true
    },
    {
      name: '联盟广告',
      type: 'line',
      data: [220, 182, 191, 234, 290, 330, 310],
      smooth: true
    },
    {
      name: '视频广告',
      type: 'line',
      data: [150, 232, 201, 154, 190, 330, 410],
      smooth: true
    },
    {
      name: '直接访问',
      type: 'line',
      data: [320, 332, 301, 334, 390, 330, 320],
      smooth: true
    },
    {
      name: '搜索引擎',
      type: 'line',
      data: [820, 932, 901, 934, 1290, 1330, 1320],
      smooth: true
    }
  ]
})
</script>
```

## 高级图表示例

### 环形饼图
```vue
<template>
  <FuniChart
    :option="doughnutOption"
    height="500px"
  />
</template>

<script setup>
import { ref } from 'vue'

const doughnutOption = ref({
  title: {
    text: '用户来源分析',
    left: 'center',
    top: 20,
    textStyle: {
      color: '#333'
    }
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 10,
    data: ['直接访问', '邮件营销', '联盟广告', '视频广告', '搜索引擎']
  },
  series: [
    {
      name: '访问来源',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '60%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '30',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [
        { value: 335, name: '直接访问' },
        { value: 310, name: '邮件营销' },
        { value: 274, name: '联盟广告' },
        { value: 235, name: '视频广告' },
        { value: 400, name: '搜索引擎' }
      ]
    }
  ]
})
</script>
```

### 散点图
```vue
<template>
  <FuniChart
    :option="scatterOption"
    height="400px"
  />
</template>

<script setup>
import { ref } from 'vue'

const scatterOption = ref({
  title: {
    text: '身高体重分布'
  },
  tooltip: {
    trigger: 'axis',
    showDelay: 0,
    formatter: (params) => {
      if (params.value.length > 1) {
        return params.seriesName + ' :<br/>'
          + params.value[0] + 'cm '
          + params.value[1] + 'kg ';
      }
    },
    axisPointer: {
      show: true,
      type: 'cross',
      lineStyle: {
        type: 'dashed',
        width: 1
      }
    }
  },
  legend: {
    data: ['女性', '男性']
  },
  xAxis: {
    type: 'value',
    scale: true,
    axisLabel: {
      formatter: '{value} cm'
    },
    splitLine: {
      show: false
    }
  },
  yAxis: {
    type: 'value',
    scale: true,
    axisLabel: {
      formatter: '{value} kg'
    },
    splitLine: {
      show: false
    }
  },
  series: [
    {
      name: '女性',
      type: 'scatter',
      data: [
        [161.2, 51.6], [167.5, 59.0], [159.5, 49.2], [157.0, 63.0], [155.8, 53.6],
        [170.0, 59.0], [159.1, 47.6], [166.0, 69.8], [176.2, 66.8], [160.2, 75.2]
      ],
      symbolSize: 8,
      itemStyle: {
        color: '#ff7f50'
      }
    },
    {
      name: '男性',
      type: 'scatter',
      data: [
        [174.0, 65.6], [175.3, 71.8], [193.5, 80.7], [186.5, 72.6], [187.2, 78.8],
        [181.5, 74.8], [184.0, 86.4], [184.5, 78.4], [175.0, 62.0], [184.0, 81.6]
      ],
      symbolSize: 8,
      itemStyle: {
        color: '#87ceeb'
      }
    }
  ]
})
</script>
```

## 动态数据示例

### 实时数据更新
```vue
<template>
  <div>
    <el-button @click="startUpdate">开始更新</el-button>
    <el-button @click="stopUpdate">停止更新</el-button>
    <FuniChart
      :option="realtimeOption"
      height="400px"
    />
  </div>
</template>

<script setup>
import { ref, onUnmounted } from 'vue'

const realtimeOption = ref({
  title: {
    text: '实时数据监控'
  },
  tooltip: {
    trigger: 'axis',
    formatter: (params) => {
      return params[0].name + '<br/>' + params[0].value + ' 次/秒'
    }
  },
  xAxis: {
    type: 'category',
    data: []
  },
  yAxis: {
    type: 'value',
    boundaryGap: [0, '100%']
  },
  series: [{
    name: '访问量',
    type: 'line',
    showSymbol: false,
    hoverAnimation: false,
    data: [],
    lineStyle: {
      color: '#5470c6'
    },
    areaStyle: {
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [{
          offset: 0, color: 'rgba(84, 112, 198, 0.3)'
        }, {
          offset: 1, color: 'rgba(84, 112, 198, 0.1)'
        }]
      }
    }
  }]
})

let timer = null
const maxDataLength = 20

const startUpdate = () => {
  if (timer) return
  
  timer = setInterval(() => {
    const now = new Date()
    const timeStr = now.toLocaleTimeString()
    const value = Math.floor(Math.random() * 100)
    
    // 更新数据
    realtimeOption.value.xAxis.data.push(timeStr)
    realtimeOption.value.series[0].data.push(value)
    
    // 保持数据长度
    if (realtimeOption.value.xAxis.data.length > maxDataLength) {
      realtimeOption.value.xAxis.data.shift()
      realtimeOption.value.series[0].data.shift()
    }
  }, 1000)
}

const stopUpdate = () => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}

onUnmounted(() => {
  stopUpdate()
})
</script>
```

### 数据钻取示例
```vue
<template>
  <div>
    <el-breadcrumb separator="/">
      <el-breadcrumb-item 
        v-for="(item, index) in breadcrumb" 
        :key="index"
        @click="drillUp(index)"
        style="cursor: pointer;"
      >
        {{ item }}
      </el-breadcrumb-item>
    </el-breadcrumb>
    
    <FuniChart
      :option="drillOption"
      height="400px"
      @click="handleDrillDown"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const breadcrumb = ref(['全国'])
const currentLevel = ref(0)

const data = {
  0: { // 全国数据
    title: '全国销售数据',
    data: [
      { name: '华北', value: 1200 },
      { name: '华东', value: 1800 },
      { name: '华南', value: 1500 },
      { name: '华中', value: 900 },
      { name: '西南', value: 800 },
      { name: '西北', value: 600 }
    ]
  },
  1: { // 华北数据
    '华北': {
      title: '华北地区销售数据',
      data: [
        { name: '北京', value: 400 },
        { name: '天津', value: 200 },
        { name: '河北', value: 300 },
        { name: '山西', value: 150 },
        { name: '内蒙古', value: 150 }
      ]
    },
    '华东': {
      title: '华东地区销售数据',
      data: [
        { name: '上海', value: 500 },
        { name: '江苏', value: 450 },
        { name: '浙江', value: 400 },
        { name: '安徽', value: 200 },
        { name: '福建', value: 250 }
      ]
    }
  }
}

const drillOption = ref({
  title: {
    text: '全国销售数据'
  },
  tooltip: {
    trigger: 'item'
  },
  series: [{
    type: 'pie',
    radius: '50%',
    data: data[0].data,
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    }
  }]
})

const handleDrillDown = (params) => {
  if (currentLevel.value === 0 && data[1][params.name]) {
    // 钻取到下一级
    currentLevel.value = 1
    breadcrumb.value.push(params.name)
    
    const nextData = data[1][params.name]
    drillOption.value.title.text = nextData.title
    drillOption.value.series[0].data = nextData.data
  }
}

const drillUp = (index) => {
  if (index === 0) {
    // 回到顶级
    currentLevel.value = 0
    breadcrumb.value = ['全国']
    drillOption.value.title.text = data[0].title
    drillOption.value.series[0].data = data[0].data
  }
}
</script>
```

## 响应式图表示例

### 移动端适配
```vue
<template>
  <FuniChart
    :option="responsiveOption"
    :height="chartHeight"
    :auto-resize="true"
  />
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const windowWidth = ref(window.innerWidth)

const chartHeight = computed(() => {
  return windowWidth.value > 768 ? '400px' : '300px'
})

const responsiveOption = computed(() => ({
  title: {
    text: '响应式图表',
    left: 'center',
    textStyle: {
      fontSize: windowWidth.value > 768 ? 18 : 14
    }
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['销售额', '利润'],
    orient: windowWidth.value > 768 ? 'horizontal' : 'vertical',
    left: windowWidth.value > 768 ? 'center' : 'left',
    top: windowWidth.value > 768 ? 30 : 40
  },
  grid: {
    left: windowWidth.value > 768 ? '3%' : '10%',
    right: windowWidth.value > 768 ? '4%' : '10%',
    bottom: '3%',
    top: windowWidth.value > 768 ? '15%' : '25%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月'],
    axisLabel: {
      fontSize: windowWidth.value > 768 ? 12 : 10
    }
  },
  yAxis: {
    type: 'value',
    axisLabel: {
      fontSize: windowWidth.value > 768 ? 12 : 10
    }
  },
  series: [
    {
      name: '销售额',
      type: 'bar',
      data: [120, 200, 150, 80, 70, 110],
      itemStyle: {
        color: '#5470c6'
      }
    },
    {
      name: '利润',
      type: 'bar',
      data: [60, 100, 75, 40, 35, 55],
      itemStyle: {
        color: '#91cc75'
      }
    }
  ]
}))

const handleResize = () => {
  windowWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>
```

## 主题切换示例

### 动态主题切换
```vue
<template>
  <div>
    <el-radio-group v-model="currentTheme" @change="handleThemeChange">
      <el-radio-button label="default">默认</el-radio-button>
      <el-radio-button label="dark">暗色</el-radio-button>
      <el-radio-button label="vintage">复古</el-radio-button>
      <el-radio-button label="macarons">马卡龙</el-radio-button>
    </el-radio-group>
    
    <FuniChart
      :option="chartOption"
      :theme="currentTheme"
      height="400px"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const currentTheme = ref('default')

const chartOption = ref({
  title: {
    text: '主题切换示例'
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['销售额', '利润']
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '销售额',
      type: 'line',
      data: [120, 200, 150, 80, 70, 110],
      smooth: true
    },
    {
      name: '利润',
      type: 'line',
      data: [60, 100, 75, 40, 35, 55],
      smooth: true
    }
  ]
})

const handleThemeChange = (theme) => {
  console.log('切换主题:', theme)
}
</script>
```

## 加载状态示例

### 异步数据加载
```vue
<template>
  <div>
    <el-button @click="loadData" :loading="loading">
      {{ loading ? '加载中...' : '重新加载' }}
    </el-button>
    
    <FuniChart
      :option="chartOption"
      :loading="loading"
      :loading-option="loadingOption"
      height="400px"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const loading = ref(false)
const chartOption = ref({})

const loadingOption = ref({
  text: '数据加载中...',
  color: '#5470c6',
  textColor: '#000',
  maskColor: 'rgba(255, 255, 255, 0.8)',
  fontSize: 14,
  showSpinner: true,
  spinnerRadius: 10,
  lineWidth: 5
})

const loadData = async () => {
  loading.value = true
  
  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟数据
    const mockData = {
      categories: ['1月', '2月', '3月', '4月', '5月', '6月'],
      sales: Array.from({ length: 6 }, () => Math.floor(Math.random() * 200) + 50),
      profit: Array.from({ length: 6 }, () => Math.floor(Math.random() * 100) + 20)
    }
    
    chartOption.value = {
      title: {
        text: '异步加载数据示例'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['销售额', '利润']
      },
      xAxis: {
        type: 'category',
        data: mockData.categories
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '销售额',
          type: 'bar',
          data: mockData.sales,
          itemStyle: {
            color: '#5470c6'
          }
        },
        {
          name: '利润',
          type: 'bar',
          data: mockData.profit,
          itemStyle: {
            color: '#91cc75'
          }
        }
      ]
    }
  } catch (error) {
    console.error('数据加载失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>
```

## 图表联动示例

### 多图表联动
```vue
<template>
  <div class="chart-container">
    <div class="chart-row">
      <FuniChart
        :option="pieOption"
        height="300px"
        @click="handlePieClick"
        class="chart-item"
      />
      <FuniChart
        :option="barOption"
        height="300px"
        class="chart-item"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const selectedCategory = ref('')

const pieData = [
  { name: '电子产品', value: 1048 },
  { name: '服装', value: 735 },
  { name: '食品', value: 580 },
  { name: '图书', value: 484 },
  { name: '家具', value: 300 }
]

const detailData = {
  '电子产品': [120, 200, 150, 80, 70, 110],
  '服装': [80, 120, 100, 60, 50, 90],
  '食品': [60, 80, 70, 40, 30, 50],
  '图书': [40, 60, 50, 30, 20, 40],
  '家具': [30, 40, 35, 20, 15, 25]
}

const pieOption = ref({
  title: {
    text: '产品类别销售占比',
    left: 'center'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  series: [{
    name: '销售额',
    type: 'pie',
    radius: '50%',
    data: pieData,
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    }
  }]
})

const barOption = ref({
  title: {
    text: '月度销售趋势',
    left: 'center'
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月']
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    name: '销售额',
    type: 'line',
    data: [],
    smooth: true,
    itemStyle: {
      color: '#5470c6'
    }
  }]
})

const handlePieClick = (params) => {
  selectedCategory.value = params.name
  
  // 更新柱状图数据
  barOption.value.title.text = `${params.name} - 月度销售趋势`
  barOption.value.series[0].data = detailData[params.name] || []
  
  // 高亮选中的饼图扇区
  pieOption.value.series[0].data = pieData.map(item => ({
    ...item,
    selected: item.name === params.name
  }))
}
</script>

<style scoped>
.chart-container {
  width: 100%;
}

.chart-row {
  display: flex;
  gap: 20px;
}

.chart-item {
  flex: 1;
}
</style>
```
