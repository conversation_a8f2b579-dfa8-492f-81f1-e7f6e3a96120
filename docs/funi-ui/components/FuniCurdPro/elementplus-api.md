# FuniCurdPro ElementPlus API 支持

## 基础组件说明

FuniCurdPro 是基于 VXE Table 封装的专业数据表格组件，通过自定义渲染器的方式集成了 ElementPlus 的表单组件。与 FuniCurd（基于 el-table）不同，FuniCurdPro 主要通过编辑渲染器（editRender）和单元格渲染器（cellRender）来支持 ElementPlus 组件。

### 架构对比
```vue
<!-- FuniCurd: 基于 ElementPlus el-table -->
<el-table :data="data">
  <el-table-column prop="name" label="姓名" />
</el-table>

<!-- FuniCurdPro: 基于 VXE Table + ElementPlus 渲染器 -->
<vxe-grid :columns="columns" :data="data">
  <!-- 通过渲染器集成 ElementPlus 组件 -->
</vxe-grid>
```

## 支持的 ElementPlus 组件

### 表单输入组件

| ElementPlus 组件 | 渲染器名称 | 支持场景 | 说明 |
|-----------------|-----------|----------|------|
| el-input | el-input | 编辑/单元格 | 文本输入框 |
| el-input-number | el-input-number | 编辑/单元格 | 数字输入框 |
| el-textarea | el-textarea | 编辑 | 多行文本输入 |
| el-autocomplete | el-autocomplete | 编辑 | 自动完成输入 |
| el-select | el-select | 编辑/单元格 | 下拉选择器 |
| el-cascader | el-cascader | 编辑 | 级联选择器 |
| el-switch | el-switch | 编辑/单元格 | 开关 |
| el-slider | el-slider | 编辑 | 滑块 |
| el-time-picker | el-time-picker | 编辑 | 时间选择器 |
| el-date-picker | el-date-picker | 编辑 | 日期选择器 |
| el-rate | el-rate | 编辑/单元格 | 评分 |
| el-color-picker | el-color-picker | 编辑 | 颜色选择器 |
| el-upload | el-upload | 编辑 | 文件上传 |

### 编辑渲染器使用示例

#### 文本输入组件
```vue
<template>
  <FuniCurdPro
    :columns="inputColumns"
    :data="data"
    :editable="true"
  />
</template>

<script setup>
const inputColumns = [
  {
    field: 'name',
    title: '姓名',
    editRender: {
      name: 'el-input',
      attrs: {
        placeholder: '请输入姓名',
        maxlength: 20,
        showWordLimit: true
      }
    }
  },
  {
    field: 'age',
    title: '年龄',
    editRender: {
      name: 'el-input-number',
      attrs: {
        min: 0,
        max: 150,
        step: 1,
        placeholder: '请输入年龄'
      }
    }
  },
  {
    field: 'description',
    title: '描述',
    editRender: {
      name: 'el-textarea',
      attrs: {
        rows: 3,
        placeholder: '请输入描述',
        maxlength: 500,
        showWordLimit: true
      }
    }
  }
]
</script>
```

#### 选择器组件
```vue
<template>
  <FuniCurdPro
    :columns="selectColumns"
    :data="data"
    :editable="true"
  />
</template>

<script setup>
const selectColumns = [
  {
    field: 'department',
    title: '部门',
    editRender: {
      name: 'el-select',
      options: [
        { value: 'tech', label: '技术部' },
        { value: 'product', label: '产品部' },
        { value: 'design', label: '设计部' }
      ],
      optionProps: { value: 'value', label: 'label' },
      attrs: {
        placeholder: '请选择部门',
        clearable: true,
        filterable: true
      }
    }
  },
  {
    field: 'skills',
    title: '技能',
    editRender: {
      name: 'el-select',
      options: [
        { value: 'vue', label: 'Vue.js' },
        { value: 'react', label: 'React' },
        { value: 'angular', label: 'Angular' }
      ],
      optionProps: { value: 'value', label: 'label' },
      attrs: {
        multiple: true,
        placeholder: '请选择技能',
        collapseTags: true,
        collapseTagsTooltip: true
      }
    }
  },
  {
    field: 'region',
    title: '地区',
    editRender: {
      name: 'el-cascader',
      options: [
        {
          value: 'beijing',
          label: '北京',
          children: [
            { value: 'haidian', label: '海淀区' },
            { value: 'chaoyang', label: '朝阳区' }
          ]
        }
      ],
      attrs: {
        placeholder: '请选择地区',
        clearable: true,
        showAllLevels: false
      }
    }
  }
]
</script>
```

#### 日期时间组件
```vue
<template>
  <FuniCurdPro
    :columns="dateColumns"
    :data="data"
    :editable="true"
  />
</template>

<script setup>
const dateColumns = [
  {
    field: 'birthDate',
    title: '出生日期',
    editRender: {
      name: 'el-date-picker',
      attrs: {
        type: 'date',
        placeholder: '选择日期',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        clearable: true
      }
    }
  },
  {
    field: 'workTime',
    title: '工作时间',
    editRender: {
      name: 'el-time-picker',
      attrs: {
        placeholder: '选择时间',
        format: 'HH:mm:ss',
        valueFormat: 'HH:mm:ss',
        clearable: true
      }
    }
  },
  {
    field: 'dateRange',
    title: '日期范围',
    editRender: {
      name: 'el-date-picker',
      attrs: {
        type: 'daterange',
        rangeSeparator: '至',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD'
      }
    }
  }
]
</script>
```

#### 其他组件
```vue
<template>
  <FuniCurdPro
    :columns="otherColumns"
    :data="data"
    :editable="true"
  />
</template>

<script setup>
const otherColumns = [
  {
    field: 'status',
    title: '状态',
    editRender: {
      name: 'el-switch',
      attrs: {
        activeText: '启用',
        inactiveText: '禁用',
        activeValue: 1,
        inactiveValue: 0
      }
    }
  },
  {
    field: 'rating',
    title: '评分',
    editRender: {
      name: 'el-rate',
      attrs: {
        max: 5,
        allowHalf: true,
        showText: true,
        texts: ['极差', '失望', '一般', '满意', '惊喜']
      }
    }
  },
  {
    field: 'progress',
    title: '进度',
    editRender: {
      name: 'el-slider',
      attrs: {
        min: 0,
        max: 100,
        step: 5,
        showStops: true,
        showTooltip: true,
        formatTooltip: (val) => `${val}%`
      }
    }
  },
  {
    field: 'color',
    title: '颜色',
    editRender: {
      name: 'el-color-picker',
      attrs: {
        showAlpha: true,
        colorFormat: 'hex'
      }
    }
  }
]
</script>
```

## 单元格渲染器支持

### 显示组件
```vue
<template>
  <FuniCurdPro
    :columns="displayColumns"
    :data="data"
  />
</template>

<script setup>
const displayColumns = [
  {
    field: 'status',
    title: '状态',
    cellRender: {
      name: 'el-tag',
      props: ({ row }) => ({
        type: row.status === 'active' ? 'success' : 'danger'
      }),
      children: ({ row }) => row.status === 'active' ? '激活' : '禁用'
    }
  },
  {
    field: 'progress',
    title: '进度',
    cellRender: {
      name: 'el-progress',
      props: ({ row }) => ({
        percentage: row.progress,
        status: row.progress === 100 ? 'success' : null
      })
    }
  },
  {
    field: 'avatar',
    title: '头像',
    cellRender: {
      name: 'el-avatar',
      props: ({ row }) => ({
        src: row.avatar,
        size: 40
      })
    }
  }
]
</script>
```

## 事件处理

### 编辑器事件
```vue
<template>
  <FuniCurdPro
    :columns="eventColumns"
    :data="data"
    :editable="true"
  />
</template>

<script setup>
const eventColumns = [
  {
    field: 'category',
    title: '分类',
    editRender: {
      name: 'el-select',
      options: categories,
      optionProps: { value: 'id', label: 'name' },
      events: {
        change: ({ row, column }, value) => {
          console.log('分类变化:', value)
          // 可以根据分类变化更新其他字段
          row.subcategory = null
        }
      }
    }
  },
  {
    field: 'amount',
    title: '金额',
    editRender: {
      name: 'el-input-number',
      attrs: { precision: 2 },
      events: {
        change: ({ row }, value) => {
          // 自动计算总价
          row.total = (value || 0) * (row.quantity || 0)
        }
      }
    }
  }
]
</script>
```

## 属性透传机制

### 渲染器属性映射
FuniCurdPro 通过渲染器配置将 ElementPlus 组件属性进行透传：

```typescript
interface RenderConfig {
  name: string;                        // ElementPlus 组件名称
  attrs?: Record<string, any>;         // 组件属性（透传给组件）
  props?: Record<string, any>;         // 组件props（动态计算）
  events?: Record<string, Function>;   // 事件处理
  options?: any[];                     // 选项数据（用于select等）
  optionProps?: OptionPropsConfig;     // 选项属性映射
}
```

### 属性透传示例
```vue
<template>
  <FuniCurdPro
    :columns="transparentColumns"
    :data="data"
    :editable="true"
  />
</template>

<script setup>
const transparentColumns = [
  {
    field: 'email',
    title: '邮箱',
    editRender: {
      name: 'el-input',
      // 直接透传 ElementPlus el-input 的所有属性
      attrs: {
        type: 'email',
        placeholder: '请输入邮箱地址',
        clearable: true,
        prefixIcon: 'Message',
        maxlength: 50,
        showWordLimit: true,
        validateEvent: true,
        // 支持所有 el-input 的原生属性
        autocomplete: 'email',
        readonly: false,
        disabled: false
      }
    }
  }
]
</script>
```

## 兼容性说明

### 版本要求
- **VXE Table**: >= 4.0.0
- **ElementPlus**: >= 2.0.0
- **Vue**: >= 3.0.0

### 功能对比

| 功能特性 | FuniCurd (el-table) | FuniCurdPro (VXE Table) |
|---------|-------------------|------------------------|
| 基础表格 | ✅ 完全支持 | ✅ 完全支持 |
| 行内编辑 | ❌ 不支持 | ✅ 完全支持 |
| 单元格编辑 | ❌ 不支持 | ✅ 完全支持 |
| 数据验证 | ❌ 不支持 | ✅ 完全支持 |
| 虚拟滚动 | ❌ 不支持 | ✅ 完全支持 |
| 树形数据 | ✅ 支持 | ✅ 完全支持 |
| 导出功能 | ❌ 不支持 | ✅ 完全支持 |
| ElementPlus集成 | ✅ 原生支持 | ✅ 渲染器支持 |

### 迁移指南

#### 从 FuniCurd 迁移到 FuniCurdPro
```vue
<!-- 迁移前：FuniCurd -->
<FuniCurd
  :columns="columns"
  :loadData="loadData"
  selection
  stripe
  border
/>

<!-- 迁移后：FuniCurdPro -->
<FuniCurdPro
  :columns="proColumns"
  :data="data"
  :editable="true"
  border
  stripe
/>

<script setup>
// 列配置需要调整
const proColumns = [
  {
    type: 'checkbox', // 替代 selection
    width: 50
  },
  {
    field: 'name',
    title: '姓名',
    editRender: {
      name: 'el-input' // 添加编辑器
    }
  }
]
</script>
```

## 注意事项

### 1. 渲染器限制
- 不是所有 ElementPlus 组件都适合在表格中使用
- 复杂组件可能影响表格性能
- 建议优先使用轻量级的表单组件

### 2. 事件处理
- 编辑器事件需要通过 events 配置
- 避免在事件处理中进行复杂计算
- 注意事件的触发时机和参数

### 3. 样式兼容
- VXE Table 和 ElementPlus 的样式可能存在冲突
- 建议使用深度选择器进行样式定制
- 注意验证错误状态的样式处理

### 4. 数据绑定
- 编辑器的数据绑定是双向的
- 修改会直接影响原始数据
- 建议在必要时使用数据副本
