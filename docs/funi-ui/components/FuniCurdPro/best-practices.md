# FuniCurdPro 最佳实践

## 设计原则

### 1. 数据驱动
FuniCurdPro 采用数据驱动的设计理念：
- 通过配置而非编码实现功能
- 数据结构决定表格行为
- 最小化手动DOM操作

### 2. 性能优先
在设计表格时应考虑性能：
- 合理使用虚拟滚动
- 避免不必要的数据监听
- 优化渲染器性能
- 控制同时编辑的单元格数量

### 3. 用户体验
提供良好的编辑体验：
- 清晰的编辑状态指示
- 及时的数据验证反馈
- 直观的操作流程
- 合理的键盘导航

## 架构设计最佳实践

### 数据结构设计
```vue
<template>
  <FuniCurdPro
    ref="curdProRef"
    :columns="columns"
    :data="tableData"
    :editable="true"
    row-key="id"
    @edit-closed="handleEditClosed"
    @valid-error="handleValidError"
  />
</template>

<script setup>
import { ref, reactive, computed } from 'vue'

// ✅ 推荐：结构化的数据设计
const tableData = ref([
  {
    id: 1,                             // 必须：唯一标识
    name: '张三',                      // 基础字段
    email: 'zhang<PERSON>@example.com',
    department: 'tech',                // 关联字段
    status: 'active',                  // 状态字段
    createdAt: '2023-12-01',          // 时间字段
    metadata: {                        // 扩展字段
      lastLogin: '2023-12-15',
      permissions: ['read', 'write']
    }
  }
])

// ✅ 推荐：分离配置和数据
const columns = reactive([
  {
    field: 'name',
    title: '姓名',
    width: 120,
    editRender: {
      name: 'el-input',
      attrs: { placeholder: '请输入姓名' }
    },
    rules: [
      { required: true, message: '姓名不能为空' }
    ]
  }
])
</script>
```

### 列配置最佳实践
```vue
<script setup>
// ✅ 推荐：模块化的列配置
const createInputColumn = (field, title, options = {}) => ({
  field,
  title,
  width: options.width || 120,
  editRender: {
    name: 'el-input',
    attrs: {
      placeholder: `请输入${title}`,
      ...options.attrs
    }
  },
  rules: options.rules || []
})

const createSelectColumn = (field, title, options, selectOptions = {}) => ({
  field,
  title,
  width: options.width || 150,
  editRender: {
    name: 'el-select',
    options: options.options,
    optionProps: { value: 'value', label: 'label' },
    attrs: {
      placeholder: `请选择${title}`,
      ...selectOptions
    }
  }
})

// 使用工厂函数创建列
const columns = [
  createInputColumn('name', '姓名', {
    width: 120,
    rules: [{ required: true, message: '姓名不能为空' }]
  }),
  createSelectColumn('department', '部门', {
    width: 150,
    options: [
      { value: 'tech', label: '技术部' },
      { value: 'hr', label: '人事部' }
    ]
  }, { clearable: true })
]
</script>
```

## 编辑功能最佳实践

### 编辑模式选择
```vue
<template>
  <div class="edit-modes-demo">
    <!-- 行编辑模式：适合表单式编辑 -->
    <FuniCurdPro
      :columns="rowEditColumns"
      :data="data"
      :editable="true"
      :edit-config="rowEditConfig"
      row-key="id"
    />
    
    <!-- 单元格编辑模式：适合快速编辑 -->
    <FuniCurdPro
      :columns="cellEditColumns"
      :data="data"
      :editable="true"
      :edit-config="cellEditConfig"
      row-key="id"
    />
  </div>
</template>

<script setup>
// 行编辑配置：适合复杂表单
const rowEditConfig = {
  mode: 'row',                         // 行编辑模式
  trigger: 'click',                    // 点击触发
  showIcon: false,                     // 不显示编辑图标
  showStatus: true,                    // 显示编辑状态
  autoClear: false                     // 不自动清除编辑状态
}

// 单元格编辑配置：适合快速编辑
const cellEditConfig = {
  mode: 'cell',                        // 单元格编辑模式
  trigger: 'click',                    // 点击触发
  showStatus: true,                    // 显示编辑状态
  autoClear: true                      // 自动清除编辑状态
}
</script>
```

### 数据验证策略
```vue
<template>
  <FuniCurdPro
    ref="validationRef"
    :columns="validationColumns"
    :data="data"
    :editable="true"
    :edit-config="editConfig"
    :valid-config="validConfig"
    @valid-error="handleValidError"
  />
</template>

<script setup>
// ✅ 推荐：分层验证策略
const validationColumns = [
  {
    field: 'email',
    title: '邮箱',
    editRender: {
      name: 'el-input',
      attrs: { type: 'email' }
    },
    rules: [
      // 第一层：基础验证
      { required: true, message: '邮箱不能为空' },
      // 第二层：格式验证
      { type: 'email', message: '邮箱格式不正确' },
      // 第三层：业务验证
      {
        validator: async (rule, value, callback, { row }) => {
          if (value && value !== row._originalEmail) {
            // 异步验证邮箱唯一性
            const exists = await checkEmailExists(value)
            if (exists) {
              callback(new Error('邮箱已存在'))
            } else {
              callback()
            }
          } else {
            callback()
          }
        }
      }
    ]
  }
]

// 验证配置
const validConfig = {
  autoPos: true,                       // 自动定位错误
  message: 'inline'                    // 内联显示错误信息
}

const handleValidError = ({ rule, row, column }) => {
  console.log('验证失败:', {
    field: column.field,
    value: row[column.field],
    message: rule.message
  })
}
</script>
```

### 数据操作最佳实践
```vue
<template>
  <div class="data-operations">
    <div class="toolbar">
      <el-button type="primary" @click="addRow">添加行</el-button>
      <el-button type="success" @click="saveData">保存数据</el-button>
      <el-button type="warning" @click="validateData">验证数据</el-button>
      <el-button type="danger" @click="removeSelected">删除选中</el-button>
    </div>
    
    <FuniCurdPro
      ref="dataRef"
      :columns="columns"
      :data="tableData"
      :editable="true"
      row-key="id"
      @checkbox-change="handleSelectionChange"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const dataRef = ref()
const selectedRows = ref([])

// ✅ 推荐：统一的数据操作方法
const addRow = async () => {
  try {
    const newRow = {
      id: Date.now(),                  // 临时ID
      name: '',
      email: '',
      department: '',
      status: 'active',
      _isNew: true                     // 标记新增行
    }
    
    await dataRef.value.insertRow(newRow)
    ElMessage.success('添加行成功')
  } catch (error) {
    ElMessage.error('添加行失败')
  }
}

const saveData = async () => {
  try {
    // 验证数据
    const data = await dataRef.value.getTableData({ validate: true })
    
    // 分离新增和修改的数据
    const newRows = data.filter(row => row._isNew)
    const updatedRows = data.filter(row => !row._isNew && row._hasChanged)
    
    // 批量保存
    if (newRows.length > 0) {
      await batchCreateRows(newRows)
    }
    if (updatedRows.length > 0) {
      await batchUpdateRows(updatedRows)
    }
    
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败：' + error.message)
  }
}

const validateData = async () => {
  try {
    const isValid = await dataRef.value.validate()
    if (isValid) {
      ElMessage.success('数据验证通过')
    }
  } catch (error) {
    ElMessage.error('数据验证失败')
  }
}

const removeSelected = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的行')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 行吗？`,
      '确认删除',
      { type: 'warning' }
    )
    
    await dataRef.value.removeRows(selectedRows.value)
    selectedRows.value = []
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSelectionChange = ({ records }) => {
  selectedRows.value = records
}

// 模拟API调用
const batchCreateRows = async (rows) => {
  // 实际项目中调用创建API
  console.log('创建行:', rows)
}

const batchUpdateRows = async (rows) => {
  // 实际项目中调用更新API
  console.log('更新行:', rows)
}
</script>
```

## 性能优化最佳实践

### 大数据量处理
```vue
<template>
  <FuniCurdPro
    ref="performanceRef"
    :columns="columns"
    :data="largeData"
    :editable="true"
    :scroll-y="scrollYConfig"
    :scroll-x="scrollXConfig"
    height="600"
    show-overflow="tooltip"
    show-header-overflow="tooltip"
    keep-source
  />
</template>

<script setup>
import { ref, computed } from 'vue'

// ✅ 推荐：虚拟滚动配置
const scrollYConfig = {
  enabled: true,                       // 启用纵向虚拟滚动
  gt: 100,                            // 数据量超过100时启用
  oSize: 50                           // 预渲染行数
}

const scrollXConfig = {
  enabled: true,                       // 启用横向虚拟滚动
  gt: 10                              // 列数超过10时启用
}

// ✅ 推荐：优化列配置
const columns = [
  {
    field: 'id',
    title: 'ID',
    width: 80,                         // 固定宽度提升性能
    sortable: true
  },
  {
    field: 'name',
    title: '姓名',
    width: 120,
    editRender: {
      name: 'el-input',
      attrs: { placeholder: '姓名' }
    },
    // ✅ 推荐：使用简单的验证规则
    rules: [
      { required: true, message: '必填' }
    ]
  }
]

// ✅ 推荐：数据懒加载
const largeData = ref([])

const loadData = async (page = 1, size = 1000) => {
  try {
    const response = await fetchLargeData(page, size)
    largeData.value = response.data
  } catch (error) {
    console.error('数据加载失败:', error)
  }
}
</script>
```

### 渲染器性能优化
```vue
<script setup>
// ✅ 推荐：缓存渲染器配置
const memoizedRenderConfig = {
  name: 'el-select',
  options: [], // 在组件外部定义，避免重复创建
  optionProps: { value: 'id', label: 'name' },
  attrs: { placeholder: '请选择' }
}

// ❌ 避免：在渲染函数中创建复杂对象
const badColumn = {
  field: 'status',
  title: '状态',
  editRender: {
    name: 'el-select',
    options: computed(() => {
      // 避免在这里进行复杂计算
      return expensiveComputation()
    })
  }
}

// ✅ 推荐：预计算选项数据
const statusOptions = [
  { id: 'active', name: '激活' },
  { id: 'inactive', name: '禁用' }
]

const goodColumn = {
  field: 'status',
  title: '状态',
  editRender: {
    name: 'el-select',
    options: statusOptions,
    optionProps: { value: 'id', label: 'name' }
  }
}
</script>
```

## 常见问题和解决方案

### 1. 性能问题
```vue
<!-- ❌ 问题：大数据量时卡顿 -->
<FuniCurdPro
  :columns="columns"
  :data="largeData"
  :editable="true"
/>

<!-- ✅ 解决：启用虚拟滚动 -->
<FuniCurdPro
  :columns="columns"
  :data="largeData"
  :editable="true"
  :scroll-y="{ enabled: true, gt: 100 }"
  height="600"
  show-overflow="tooltip"
/>
```

### 2. 验证问题
```javascript
// ❌ 问题：验证规则不生效
const badColumn = {
  field: 'email',
  title: '邮箱',
  editRender: { name: 'el-input' },
  // 错误：rules 位置不对
  attrs: {
    rules: [{ required: true }]
  }
}

// ✅ 解决：正确的验证配置
const goodColumn = {
  field: 'email',
  title: '邮箱',
  editRender: { name: 'el-input' },
  // 正确：rules 在列配置的根级别
  rules: [
    { required: true, message: '邮箱不能为空' },
    { type: 'email', message: '邮箱格式不正确' }
  ]
}
```

### 3. 数据绑定问题
```javascript
// ❌ 问题：数据不更新
const handleEdit = ({ row, column }, value) => {
  // 错误：直接赋值可能不会触发响应式更新
  row[column.field] = value
}

// ✅ 解决：确保响应式更新
const handleEdit = ({ row, column }, value) => {
  // 正确：使用Vue的响应式API
  if (row && column) {
    row[column.field] = value
    // 如果需要，手动触发更新
    nextTick(() => {
      // 执行后续逻辑
    })
  }
}
```

## 部署和维护最佳实践

### 代码组织
```
src/
├── components/
│   └── FuniCurdPro/
│       ├── index.vue                 # 主组件
│       ├── renderers/                # 自定义渲染器
│       │   ├── input-renderer.js
│       │   └── select-renderer.js
│       ├── validators/               # 自定义验证器
│       │   ├── email-validator.js
│       │   └── phone-validator.js
│       └── utils/                    # 工具函数
│           ├── column-factory.js
│           └── data-processor.js
├── views/
│   └── examples/
│       ├── BasicExample.vue         # 基础示例
│       ├── AdvancedExample.vue      # 高级示例
│       └── PerformanceExample.vue   # 性能示例
└── docs/
    └── funi-curd-pro/
        ├── api.md                    # API文档
        ├── examples.md               # 示例文档
        └── best-practices.md         # 最佳实践
```

### 版本管理
```javascript
// package.json
{
  "dependencies": {
    "vxe-table": "^4.3.0",           // 固定主版本
    "element-plus": "^2.3.0",        // 固定主版本
    "vue": "^3.3.0"                  // 固定主版本
  }
}

// 版本兼容性检查
const checkCompatibility = () => {
  const vxeVersion = require('vxe-table/package.json').version
  const elementVersion = require('element-plus/package.json').version

  if (!semver.satisfies(vxeVersion, '^4.0.0')) {
    console.warn('VXE Table版本不兼容')
  }

  if (!semver.satisfies(elementVersion, '^2.0.0')) {
    console.warn('Element Plus版本不兼容')
  }
}
```

### 监控和调试
```javascript
// 性能监控
const performanceMonitor = {
  startTime: 0,

  start() {
    this.startTime = performance.now()
  },

  end(operation) {
    const duration = performance.now() - this.startTime
    if (duration > 100) {
      console.warn(`${operation} 耗时过长: ${duration}ms`)
    }
  }
}

// 在组件中使用
const handleEdit = ({ row, column }, value) => {
  performanceMonitor.start()

  // 执行编辑逻辑
  row[column.field] = value

  performanceMonitor.end('单元格编辑')
}
```
