# FuniCurdPro 配置结构定义

## 基础配置接口

### FuniCurdProProps
```typescript
interface FuniCurdProProps {
  // 基础属性
  editable?: boolean;                  // 是否可编辑
  columns: ColumnConfig[];             // 列配置
  data: any[];                         // 表格数据
  rowKey?: string;                     // 行数据唯一标识
  
  // VXE Table 透传属性
  border?: boolean;                    // 是否显示边框
  stripe?: boolean;                    // 是否显示斑马纹
  height?: number | string;            // 表格高度
  maxHeight?: number | string;         // 最大高度
  loading?: boolean;                   // 是否显示加载状态
  showHeader?: boolean;                // 是否显示表头
  showFooter?: boolean;                // 是否显示表尾
  showOverflow?: boolean | string;     // 内容溢出处理
  showHeaderOverflow?: boolean | string; // 表头溢出处理
  keepSource?: boolean;                // 是否保持原始数据
  columnConfig?: ColumnGlobalConfig;   // 全局列配置
  rowConfig?: RowConfig;               // 行配置
  editConfig?: EditConfig;             // 编辑配置
  validConfig?: ValidConfig;           // 验证配置
  sortConfig?: SortConfig;             // 排序配置
  filterConfig?: FilterConfig;         // 筛选配置
  exportConfig?: ExportConfig;         // 导出配置
  importConfig?: ImportConfig;         // 导入配置
  printConfig?: PrintConfig;           // 打印配置
  scrollX?: ScrollConfig;              // 横向滚动配置
  scrollY?: ScrollConfig;              // 纵向滚动配置
  customConfig?: CustomConfig;         // 自定义配置
  [key: string]: any;                  // 其他VXE Table配置
}
```

### ColumnConfig - 列配置
```typescript
interface ColumnConfig {
  // 基础属性
  field: string;                       // 字段名
  title: string;                       // 列标题
  width?: number | string;             // 列宽度
  minWidth?: number | string;          // 最小宽度
  maxWidth?: number | string;          // 最大宽度
  
  // 列类型
  type?: ColumnType;                   // 列类型
  fixed?: 'left' | 'right';           // 固定列
  align?: AlignType;                   // 对齐方式
  headerAlign?: AlignType;             // 表头对齐
  footerAlign?: AlignType;             // 表尾对齐
  
  // 显示控制
  visible?: boolean;                   // 是否显示
  resizable?: boolean;                 // 是否可调整大小
  sortable?: boolean;                  // 是否可排序
  filters?: FilterOption[];            // 筛选选项
  
  // 内容处理
  showOverflow?: boolean | string;     // 内容溢出处理
  showHeaderOverflow?: boolean | string; // 表头溢出处理
  showTooltip?: boolean;               // 是否显示提示
  
  // 渲染器
  cellRender?: CellRenderConfig;       // 单元格渲染器
  editRender?: EditRenderConfig;       // 编辑渲染器
  headerRender?: HeaderRenderConfig;   // 表头渲染器
  footerRender?: FooterRenderConfig;   // 表尾渲染器
  filterRender?: FilterRenderConfig;   // 筛选渲染器
  
  // 验证规则
  rules?: ValidationRule[];            // 验证规则
  
  // 树形结构
  treeNode?: boolean;                  // 是否为树节点
  
  // 其他配置
  className?: string | Function;       // 自定义类名
  headerClassName?: string | Function; // 表头自定义类名
  footerClassName?: string | Function; // 表尾自定义类名
  formatter?: Function;                // 格式化函数
  indexMethod?: Function;              // 序号生成方法
  seqMethod?: Function;                // 序号生成方法
  sortMethod?: Function;               // 排序方法
  filterMethod?: Function;             // 筛选方法
  footerMethod?: Function;             // 表尾数据生成方法
  exportMethod?: Function;             // 导出方法
  titleHelp?: TitleHelpConfig;         // 标题帮助配置
  [key: string]: any;                  // 其他配置
}
```

### EditRenderConfig - 编辑渲染器配置
```typescript
interface EditRenderConfig {
  name: string;                        // 渲染器名称
  attrs?: Record<string, any>;         // 组件属性
  props?: Record<string, any>;         // 组件props
  options?: OptionConfig[];            // 选项数据
  optionProps?: OptionPropsConfig;     // 选项属性映射
  optionGroups?: OptionGroupConfig[];  // 选项分组
  optionGroupProps?: OptionGroupPropsConfig; // 分组属性映射
  events?: Record<string, Function>;   // 事件处理
  defaultValue?: any;                  // 默认值
  immediate?: boolean;                 // 是否立即触发
  autoselect?: boolean;                // 是否自动选中
  autofocus?: string;                  // 自动聚焦选择器
  [key: string]: any;                  // 其他配置
}
```

### CellRenderConfig - 单元格渲染器配置
```typescript
interface CellRenderConfig {
  name: string;                        // 渲染器名称
  attrs?: Record<string, any>;         // 组件属性
  props?: Record<string, any>;         // 组件props
  options?: OptionConfig[];            // 选项数据
  optionProps?: OptionPropsConfig;     // 选项属性映射
  events?: Record<string, Function>;   // 事件处理
  [key: string]: any;                  // 其他配置
}
```

### ValidationRule - 验证规则
```typescript
interface ValidationRule {
  required?: boolean;                  // 是否必填
  min?: number;                        // 最小值/长度
  max?: number;                        // 最大值/长度
  type?: ValidationType;               // 验证类型
  pattern?: RegExp | string;           // 正则表达式
  validator?: ValidatorFunction;       // 自定义验证函数
  message?: string;                    // 错误信息
  trigger?: TriggerType;               // 触发时机
  [key: string]: any;                  // 其他配置
}
```

### EditConfig - 编辑配置
```typescript
interface EditConfig {
  enabled?: boolean;                   // 是否启用编辑
  mode?: 'cell' | 'row';              // 编辑模式
  trigger?: 'click' | 'dblclick' | 'manual'; // 触发方式
  showIcon?: boolean;                  // 是否显示编辑图标
  showStatus?: boolean;                // 是否显示编辑状态
  showAsterisk?: boolean;              // 是否显示必填星号
  showInvalidTip?: boolean;            // 是否显示无效提示
  autoClear?: boolean;                 // 是否自动清除
  [key: string]: any;                  // 其他配置
}
```

### RowConfig - 行配置
```typescript
interface RowConfig {
  keyField?: string;                   // 行唯一标识字段
  isHover?: boolean;                   // 是否启用悬停高亮
  isCurrent?: boolean;                 // 是否启用当前行高亮
  height?: number;                     // 行高度
  [key: string]: any;                  // 其他配置
}
```

## 事件配置

### CurdProEvents
```typescript
interface CurdProEvents {
  // 单元格事件
  'cell-click'?: (params: CellClickParams) => void;
  'cell-dblclick'?: (params: CellDblclickParams) => void;
  'cell-mouseenter'?: (params: CellMouseenterParams) => void;
  'cell-mouseleave'?: (params: CellMouseleaveParams) => void;
  
  // 行事件
  'row-click'?: (params: RowClickParams) => void;
  'row-dblclick'?: (params: RowDblclickParams) => void;
  
  // 编辑事件
  'edit-closed'?: (params: EditClosedParams) => void;
  'edit-actived'?: (params: EditActivedParams) => void;
  'edit-disabled'?: (params: EditDisabledParams) => void;
  
  // 验证事件
  'valid-error'?: (params: ValidErrorParams) => void;
  
  // 选择事件
  'checkbox-change'?: (params: CheckboxChangeParams) => void;
  'checkbox-all'?: (params: CheckboxAllParams) => void;
  'radio-change'?: (params: RadioChangeParams) => void;
  
  // 排序筛选事件
  'sort-change'?: (params: SortChangeParams) => void;
  'filter-change'?: (params: FilterChangeParams) => void;
  
  // 滚动事件
  'scroll'?: (params: ScrollParams) => void;
  
  // 其他事件
  'resizable-change'?: (params: ResizableChangeParams) => void;
  [key: string]: any;                  // 其他VXE Table事件
}
```

## 方法配置

### CurdProMethods
```typescript
interface CurdProMethods {
  // 数据操作
  getTableData: (options?: GetTableDataOptions) => Promise<any[]>;
  setTableData: (data: any[]) => void;
  insertRow: (record?: any) => Promise<any>;
  insertRowAt: (record: any, index: number) => Promise<any>;
  removeRow: (row: any) => Promise<void>;
  removeRows: (rows: any[]) => Promise<void>;
  removeSelecteds: () => Promise<void>;
  
  // 编辑操作
  setActiveCell: (row: any, field: string) => void;
  setActiveRow: (row: any) => void;
  clearActived: () => void;
  hasActiveRow: () => boolean;
  getActiveRecord: () => any;
  
  // 验证操作
  validate: (full?: boolean) => Promise<boolean>;
  validateRow: (row: any) => Promise<boolean>;
  clearValidate: () => void;
  
  // 选择操作
  setCheckboxRow: (rows: any[], checked: boolean) => void;
  setAllCheckboxRow: (checked: boolean) => void;
  getCheckboxRecords: () => any[];
  clearCheckboxRow: () => void;
  setRadioRow: (row: any) => void;
  clearRadioRow: () => void;
  getRadioRecord: () => any;
  
  // 排序筛选
  sort: (field: string, order?: string) => void;
  clearSort: () => void;
  setFilter: (field: string, values: any[]) => void;
  clearFilter: (field?: string) => void;
  
  // 滚动操作
  scrollTo: (x?: number, y?: number) => void;
  scrollToRow: (row: any) => void;
  scrollToColumn: (column: any) => void;
  
  // 布局操作
  recalculate: () => void;
  refreshColumn: () => void;
  refreshData: () => void;
  
  // 导出导入
  exportData: (options?: ExportOptions) => void;
  importData: (options?: ImportOptions) => void;
  
  // 其他操作
  focus: () => void;
  blur: () => void;
  [key: string]: any;                  // 其他VXE Table方法
}
```

## 常用配置组合

### 基础可编辑表格
```typescript
const basicEditableConfig: Partial<FuniCurdProProps> = {
  editable: true,
  border: true,
  stripe: true,
  showOverflow: true,
  keepSource: true,
  editConfig: {
    mode: 'row',
    trigger: 'click',
    showIcon: false,
    showStatus: true
  },
  validConfig: {
    autoPos: false
  }
}
```

### 高性能大数据表格
```typescript
const performanceConfig: Partial<FuniCurdProProps> = {
  height: 600,
  showOverflow: 'tooltip',
  showHeaderOverflow: 'tooltip',
  scrollY: {
    enabled: true,
    gt: 100
  },
  columnConfig: {
    resizable: true,
    isCurrent: true,
    isHover: true
  }
}
```

### 复杂验证表格
```typescript
const validationConfig: Partial<FuniCurdProProps> = {
  editable: true,
  editConfig: {
    mode: 'cell',
    trigger: 'click',
    showStatus: true,
    showInvalidTip: true
  },
  validConfig: {
    autoPos: true,
    message: 'inline'
  }
}
```

## 类型定义

### 基础类型
```typescript
type ColumnType = 'seq' | 'checkbox' | 'radio' | 'expand' | 'html';
type AlignType = 'left' | 'center' | 'right';
type ValidationType = 'number' | 'integer' | 'float' | 'string' | 'email' | 'url' | 'date';
type TriggerType = 'blur' | 'change' | 'manual';

interface OptionConfig {
  value: any;
  label: string;
  disabled?: boolean;
  [key: string]: any;
}

interface OptionPropsConfig {
  value?: string;
  label?: string;
  disabled?: string;
  children?: string;
}

interface ValidatorFunction {
  (rule: ValidationRule, value: any, callback: Function, params: any): void;
}
```

### 事件参数类型
```typescript
interface CellClickParams {
  row: any;
  column: ColumnConfig;
  cell: HTMLElement;
  event: Event;
}

interface EditClosedParams {
  row: any;
  column: ColumnConfig;
  cell: HTMLElement;
}

interface ValidErrorParams {
  rule: ValidationRule;
  row: any;
  column: ColumnConfig;
  cell: HTMLElement;
}
```

## 样式配置

### CSS变量定制
```css
:root {
  /* 表格基础样式 */
  --vxe-table-border-color: #e8eaec;
  --vxe-table-background-color: #ffffff;
  --vxe-table-header-background-color: #f8f8f9;
  
  /* 编辑状态样式 */
  --vxe-table-edit-background-color: #ffffff;
  --vxe-table-edit-border-color: #409eff;
  
  /* 验证错误样式 */
  --vxe-table-validate-error-color: #f56c6c;
  --vxe-table-validate-error-background-color: #fef0f0;
  
  /* 选中状态样式 */
  --vxe-table-row-current-background-color: #e6f7ff;
  --vxe-table-row-hover-background-color: #f5f7fa;
}
```

### 主题定制
```typescript
const themeConfig = {
  customConfig: {
    storage: true,
    checkMethod: ({ column }) => {
      return ['name', 'email'].includes(column.field)
    }
  }
}
```
