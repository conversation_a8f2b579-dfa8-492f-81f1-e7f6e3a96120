# FuniCurdPro 使用示例

## 基础可编辑表格示例

### 简单行编辑表格
```vue
<template>
  <div class="basic-example">
    <div class="toolbar">
      <el-button type="primary" @click="handleAdd">添加行</el-button>
      <el-button type="success" @click="handleSave">保存数据</el-button>
      <el-button type="warning" @click="handleValidate">验证数据</el-button>
    </div>
    
    <FuniCurdPro
      ref="curdProRef"
      :columns="basicColumns"
      :data="basicData"
      :editable="true"
      row-key="id"
      border
      stripe
      @edit-closed="handleEditClosed"
      @valid-error="handleValidError"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const curdProRef = ref()

const basicColumns = reactive([
  {
    type: 'seq',
    title: '序号',
    width: 60,
    fixed: 'left'
  },
  {
    field: 'name',
    title: '姓名',
    width: 120,
    editRender: {
      name: 'el-input',
      attrs: { placeholder: '请输入姓名' }
    },
    rules: [
      { required: true, message: '姓名不能为空' }
    ]
  },
  {
    field: 'age',
    title: '年龄',
    width: 100,
    editRender: {
      name: 'el-input-number',
      attrs: { min: 0, max: 150, placeholder: '请输入年龄' }
    },
    rules: [
      { required: true, message: '年龄不能为空' },
      { type: 'number', min: 0, max: 150, message: '年龄必须在0-150之间' }
    ]
  },
  {
    field: 'email',
    title: '邮箱',
    width: 200,
    editRender: {
      name: 'el-input',
      attrs: { placeholder: '请输入邮箱' }
    },
    rules: [
      { required: true, message: '邮箱不能为空' },
      { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '邮箱格式不正确' }
    ]
  },
  {
    field: 'phone',
    title: '电话',
    width: 150,
    editRender: {
      name: 'el-input',
      attrs: { placeholder: '请输入电话' }
    },
    rules: [
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
    ]
  }
])

const basicData = ref([
  {
    id: 1,
    name: '张三',
    age: 28,
    email: '<EMAIL>',
    phone: '13800138001'
  },
  {
    id: 2,
    name: '李四',
    age: 32,
    email: '<EMAIL>',
    phone: '13800138002'
  }
])

const handleAdd = async () => {
  try {
    await curdProRef.value.insertRow({
      id: Date.now(),
      name: '',
      age: null,
      email: '',
      phone: ''
    })
    ElMessage.success('添加行成功')
  } catch (error) {
    ElMessage.error('添加行失败')
  }
}

const handleSave = async () => {
  try {
    const data = await curdProRef.value.getTableData({ validate: true })
    console.log('保存数据:', data)
    // 这里可以调用API保存数据
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('数据验证失败，请检查输入')
  }
}

const handleValidate = async () => {
  try {
    const isValid = await curdProRef.value.validate()
    if (isValid) {
      ElMessage.success('数据验证通过')
    }
  } catch (error) {
    ElMessage.error('数据验证失败')
  }
}

const handleEditClosed = ({ row, column }) => {
  console.log('编辑结束:', row[column.field])
}

const handleValidError = ({ rule, row, column }) => {
  console.log('验证错误:', rule.message)
}
</script>

<style scoped>
.basic-example {
  padding: 20px;
}

.toolbar {
  margin-bottom: 16px;
}

.toolbar .el-button {
  margin-right: 8px;
}
</style>
```

### 单元格编辑表格
```vue
<template>
  <div class="cell-edit-example">
    <h3>单元格编辑模式</h3>
    <p>点击单元格即可编辑，支持Tab键切换</p>
    
    <FuniCurdPro
      ref="cellEditRef"
      :columns="cellEditColumns"
      :data="cellEditData"
      :editable="true"
      :edit-config="cellEditConfig"
      row-key="id"
      border
      @edit-actived="handleEditActived"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const cellEditRef = ref()

const cellEditConfig = {
  mode: 'cell',
  trigger: 'click',
  showStatus: true
}

const cellEditColumns = reactive([
  {
    field: 'product',
    title: '产品名称',
    width: 150,
    editRender: {
      name: 'el-input'
    }
  },
  {
    field: 'price',
    title: '单价',
    width: 120,
    editRender: {
      name: 'el-input-number',
      attrs: { min: 0, precision: 2 }
    }
  },
  {
    field: 'quantity',
    title: '数量',
    width: 100,
    editRender: {
      name: 'el-input-number',
      attrs: { min: 1 }
    }
  },
  {
    field: 'total',
    title: '总价',
    width: 120,
    formatter: ({ row }) => {
      return ((row.price || 0) * (row.quantity || 0)).toFixed(2)
    }
  }
])

const cellEditData = ref([
  { id: 1, product: '笔记本电脑', price: 5999.00, quantity: 2 },
  { id: 2, product: '无线鼠标', price: 99.00, quantity: 5 },
  { id: 3, product: '机械键盘', price: 299.00, quantity: 3 }
])

const handleEditActived = ({ row, column }) => {
  console.log('开始编辑:', row[column.field])
}
</script>
```

## 高级功能示例

### 下拉选择和级联选择
```vue
<template>
  <div class="advanced-example">
    <h3>高级编辑器示例</h3>
    
    <FuniCurdPro
      ref="advancedRef"
      :columns="advancedColumns"
      :data="advancedData"
      :editable="true"
      row-key="id"
      border
      stripe
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const advancedRef = ref()

const advancedColumns = reactive([
  {
    field: 'name',
    title: '员工姓名',
    width: 120,
    editRender: {
      name: 'el-input'
    }
  },
  {
    field: 'department',
    title: '部门',
    width: 150,
    editRender: {
      name: 'el-select',
      options: [
        { value: 'tech', label: '技术部' },
        { value: 'product', label: '产品部' },
        { value: 'design', label: '设计部' },
        { value: 'marketing', label: '市场部' }
      ],
      optionProps: { value: 'value', label: 'label' },
      attrs: { placeholder: '请选择部门' }
    }
  },
  {
    field: 'position',
    title: '职位',
    width: 150,
    editRender: {
      name: 'el-select',
      options: [
        { value: 'junior', label: '初级工程师' },
        { value: 'senior', label: '高级工程师' },
        { value: 'lead', label: '技术主管' },
        { value: 'manager', label: '部门经理' }
      ],
      optionProps: { value: 'value', label: 'label' },
      attrs: { placeholder: '请选择职位' }
    }
  },
  {
    field: 'status',
    title: '状态',
    width: 120,
    editRender: {
      name: 'el-switch',
      attrs: {
        activeText: '在职',
        inactiveText: '离职',
        activeValue: 'active',
        inactiveValue: 'inactive'
      }
    },
    cellRender: {
      name: 'el-tag',
      props: ({ row }) => {
        return {
          type: row.status === 'active' ? 'success' : 'danger'
        }
      }
    }
  },
  {
    field: 'joinDate',
    title: '入职日期',
    width: 150,
    editRender: {
      name: 'el-date-picker',
      attrs: {
        type: 'date',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        placeholder: '选择日期'
      }
    }
  }
])

const advancedData = ref([
  {
    id: 1,
    name: '张三',
    department: 'tech',
    position: 'senior',
    status: 'active',
    joinDate: '2023-01-15'
  },
  {
    id: 2,
    name: '李四',
    department: 'product',
    position: 'manager',
    status: 'active',
    joinDate: '2022-08-20'
  }
])
</script>
```

### 复杂验证规则示例
```vue
<template>
  <div class="validation-example">
    <h3>复杂验证规则</h3>
    <p>支持必填、格式、自定义验证等多种规则</p>

    <FuniCurdPro
      ref="validationRef"
      :columns="validationColumns"
      :data="validationData"
      :editable="true"
      :edit-config="validationEditConfig"
      :valid-config="validationValidConfig"
      row-key="id"
      border
      @valid-error="handleValidationError"
    />

    <div class="validation-actions">
      <el-button type="primary" @click="validateAll">验证所有数据</el-button>
      <el-button @click="clearValidation">清除验证状态</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const validationRef = ref()

const validationEditConfig = {
  mode: 'row',
  trigger: 'click',
  showStatus: true,
  showInvalidTip: true
}

const validationValidConfig = {
  autoPos: true,
  message: 'inline'
}

const validationColumns = reactive([
  {
    field: 'username',
    title: '用户名',
    width: 150,
    editRender: {
      name: 'el-input',
      attrs: { placeholder: '请输入用户名' }
    },
    rules: [
      { required: true, message: '用户名不能为空' },
      { min: 3, max: 20, message: '用户名长度在3-20个字符' },
      { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
    ]
  },
  {
    field: 'password',
    title: '密码',
    width: 150,
    editRender: {
      name: 'el-input',
      attrs: { type: 'password', placeholder: '请输入密码' }
    },
    rules: [
      { required: true, message: '密码不能为空' },
      { min: 6, message: '密码至少6位' },
      {
        validator: (rule, value, callback, { row }) => {
          if (value && !/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
            callback(new Error('密码必须包含大小写字母和数字'))
          } else {
            callback()
          }
        }
      }
    ]
  },
  {
    field: 'confirmPassword',
    title: '确认密码',
    width: 150,
    editRender: {
      name: 'el-input',
      attrs: { type: 'password', placeholder: '请确认密码' }
    },
    rules: [
      { required: true, message: '请确认密码' },
      {
        validator: (rule, value, callback, { row }) => {
          if (value && value !== row.password) {
            callback(new Error('两次输入的密码不一致'))
          } else {
            callback()
          }
        }
      }
    ]
  },
  {
    field: 'email',
    title: '邮箱',
    width: 200,
    editRender: {
      name: 'el-input',
      attrs: { placeholder: '请输入邮箱' }
    },
    rules: [
      { required: true, message: '邮箱不能为空' },
      { type: 'email', message: '请输入正确的邮箱格式' }
    ]
  },
  {
    field: 'age',
    title: '年龄',
    width: 100,
    editRender: {
      name: 'el-input-number',
      attrs: { min: 18, max: 65, placeholder: '年龄' }
    },
    rules: [
      { required: true, message: '年龄不能为空' },
      { type: 'number', min: 18, max: 65, message: '年龄必须在18-65之间' }
    ]
  }
])

const validationData = ref([
  {
    id: 1,
    username: '',
    password: '',
    confirmPassword: '',
    email: '',
    age: null
  }
])

const validateAll = async () => {
  try {
    const isValid = await validationRef.value.validate()
    if (isValid) {
      ElMessage.success('所有数据验证通过')
    }
  } catch (error) {
    ElMessage.error('数据验证失败，请检查输入')
  }
}

const clearValidation = () => {
  validationRef.value.clearValidate()
  ElMessage.info('已清除验证状态')
}

const handleValidationError = ({ rule, row, column }) => {
  console.log('验证错误:', {
    field: column.field,
    message: rule.message,
    value: row[column.field]
  })
}
</script>

<style scoped>
.validation-example {
  padding: 20px;
}

.validation-actions {
  margin-top: 16px;
}

.validation-actions .el-button {
  margin-right: 8px;
}
</style>
```

### 动态列和行操作示例
```vue
<template>
  <div class="dynamic-example">
    <h3>动态列和行操作</h3>

    <div class="controls">
      <el-button type="primary" @click="addRow">添加行</el-button>
      <el-button type="danger" @click="removeSelected">删除选中</el-button>
      <el-button @click="toggleColumn">切换列显示</el-button>
    </div>

    <FuniCurdPro
      ref="dynamicRef"
      :columns="dynamicColumns"
      :data="dynamicData"
      :editable="true"
      row-key="id"
      border
      stripe
      @checkbox-change="handleSelectionChange"
    />

    <div class="info">
      <p>已选中 {{ selectedRows.length }} 行</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const dynamicRef = ref()
const selectedRows = ref([])
const showOptionalColumn = ref(true)

const baseColumns = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left'
  },
  {
    field: 'name',
    title: '姓名',
    width: 120,
    editRender: {
      name: 'el-input'
    }
  },
  {
    field: 'department',
    title: '部门',
    width: 120,
    editRender: {
      name: 'el-select',
      options: [
        { value: 'tech', label: '技术部' },
        { value: 'hr', label: '人事部' },
        { value: 'finance', label: '财务部' }
      ],
      optionProps: { value: 'value', label: 'label' }
    }
  }
]

const optionalColumn = {
  field: 'salary',
  title: '薪资',
  width: 120,
  editRender: {
    name: 'el-input-number',
    attrs: { min: 0 }
  }
}

const actionColumn = {
  title: '操作',
  width: 120,
  fixed: 'right',
  cellRender: {
    name: 'el-button-group',
    children: [
      {
        name: 'el-button',
        props: { size: 'small', type: 'primary' },
        events: {
          click: ({ row }) => editRow(row)
        },
        children: '编辑'
      },
      {
        name: 'el-button',
        props: { size: 'small', type: 'danger' },
        events: {
          click: ({ row }) => deleteRow(row)
        },
        children: '删除'
      }
    ]
  }
}

const dynamicColumns = computed(() => {
  const columns = [...baseColumns]
  if (showOptionalColumn.value) {
    columns.push(optionalColumn)
  }
  columns.push(actionColumn)
  return columns
})

const dynamicData = ref([
  { id: 1, name: '张三', department: 'tech', salary: 8000 },
  { id: 2, name: '李四', department: 'hr', salary: 6000 },
  { id: 3, name: '王五', department: 'finance', salary: 7000 }
])

const addRow = async () => {
  const newRow = {
    id: Date.now(),
    name: '',
    department: '',
    salary: null
  }

  try {
    await dynamicRef.value.insertRow(newRow)
    ElMessage.success('添加行成功')
  } catch (error) {
    ElMessage.error('添加行失败')
  }
}

const removeSelected = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要删除的行')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 行吗？`,
      '确认删除',
      { type: 'warning' }
    )

    await dynamicRef.value.removeRows(selectedRows.value)
    selectedRows.value = []
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const toggleColumn = () => {
  showOptionalColumn.value = !showOptionalColumn.value
  ElMessage.info(`薪资列已${showOptionalColumn.value ? '显示' : '隐藏'}`)
}

const editRow = (row) => {
  dynamicRef.value.setActiveRow(row)
  ElMessage.info(`开始编辑: ${row.name}`)
}

const deleteRow = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除 ${row.name} 吗？`,
      '确认删除',
      { type: 'warning' }
    )

    await dynamicRef.value.removeRow(row)
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSelectionChange = ({ records }) => {
  selectedRows.value = records
}
</script>

<style scoped>
.dynamic-example {
  padding: 20px;
}

.controls {
  margin-bottom: 16px;
}

.controls .el-button {
  margin-right: 8px;
}

.info {
  margin-top: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
}
</style>
```
