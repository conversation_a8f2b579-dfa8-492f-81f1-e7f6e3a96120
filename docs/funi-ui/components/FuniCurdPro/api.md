# FuniCurdPro API文档

## 组件概述

FuniCurdPro是基于VXE Table封装的专业数据表格组件，提供了强大的表格编辑、数据验证、行操作等高级功能。相比FuniCurd和FuniCurdV2，FuniCurdPro专注于复杂的数据编辑场景，支持行内编辑、单元格编辑、数据验证等专业功能。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 | VXE Table对应 |
|--------|------|--------|------|------|---------------|
| editable | Boolean | true | - | 是否可编辑 | edit-config.enabled |
| columns | Array | [] | ✅ | 列配置 | columns |
| data | Array | [] | ✅ | 表格数据 | data |
| rowKey | String | - | - | 行数据的唯一标识 | row-config.keyField |

### columns 列配置

```typescript
interface ColumnConfig {
  field: string;                       // 字段名
  title: string;                       // 列标题
  width?: number | string;             // 列宽度
  minWidth?: number | string;          // 最小宽度
  type?: 'checkbox' | 'radio' | 'seq' | 'expand'; // 列类型
  fixed?: 'left' | 'right';           // 固定列
  align?: 'left' | 'center' | 'right'; // 对齐方式
  headerAlign?: 'left' | 'center' | 'right'; // 表头对齐
  sortable?: boolean;                  // 是否可排序
  filters?: any[];                     // 筛选配置
  editRender?: EditRenderConfig;       // 编辑渲染器
  cellRender?: CellRenderConfig;       // 单元格渲染器
  rules?: ValidationRule[];            // 验证规则
  visible?: boolean;                   // 是否显示
  resizable?: boolean;                 // 是否可调整大小
  showOverflow?: boolean | string;     // 内容溢出处理
  showHeaderOverflow?: boolean | string; // 表头溢出处理
  [key: string]: any;                  // 其他VXE Table列配置
}
```

### EditRenderConfig 编辑渲染器配置

```typescript
interface EditRenderConfig {
  name: string;                        // 渲染器名称
  attrs?: Record<string, any>;         // 组件属性
  options?: any[];                     // 选项数据（用于select等）
  optionProps?: {                      // 选项属性映射
    value?: string;
    label?: string;
    disabled?: string;
  };
  events?: Record<string, Function>;   // 事件处理
  [key: string]: any;                  // 其他配置
}
```

### ValidationRule 验证规则

```typescript
interface ValidationRule {
  required?: boolean;                  // 是否必填
  min?: number;                        // 最小值/长度
  max?: number;                        // 最大值/长度
  pattern?: RegExp;                    // 正则表达式
  validator?: (rule: any, value: any, callback: Function) => void; // 自定义验证
  message?: string;                    // 错误信息
  trigger?: 'blur' | 'change';         // 触发时机
}
```

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| cell-click | { row, column, event } | 单元格点击事件 | 点击单元格时 |
| cell-dblclick | { row, column, event } | 单元格双击事件 | 双击单元格时 |
| edit-closed | { row, column } | 编辑关闭事件 | 结束编辑时 |
| edit-actived | { row, column } | 编辑激活事件 | 开始编辑时 |
| valid-error | { rule, row, column } | 验证错误事件 | 验证失败时 |
| checkbox-change | { records, reserves, indeterminates, checked } | 复选框变化 | 选择变化时 |
| radio-change | { row } | 单选框变化 | 单选变化时 |
| sort-change | { column, property, order } | 排序变化 | 排序时 |
| filter-change | { column, property, values } | 筛选变化 | 筛选时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| getTableData | { validate?: boolean } | Promise\<any[]\> | 获取表格数据 |
| insertRow | - | Promise\<any\> | 在末尾插入新行 |
| insertRowAt | index: number | Promise\<any\> | 在指定位置插入新行 |
| removeRow | row: any | Promise\<void\> | 删除指定行 |
| validate | full?: boolean | Promise\<boolean\> | 验证表格数据 |
| clearValidate | - | void | 清除验证状态 |
| setActiveCell | row: any, field: string | void | 设置活动单元格 |
| clearActived | - | void | 清除活动状态 |

## Slots

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| default | - | 默认插槽，用于自定义列内容 |
| toolbar | - | 工具栏插槽 |
| pager | - | 分页器插槽 |

## 配置结构

### 基础表格配置
```typescript
const basicConfig = {
  editable: true,
  rowKey: 'id',
  columns: [
    {
      field: 'name',
      title: '姓名',
      width: 120,
      editRender: {
        name: 'el-input',
        attrs: { placeholder: '请输入姓名' }
      },
      rules: [
        { required: true, message: '姓名不能为空' }
      ]
    },
    {
      field: 'age',
      title: '年龄',
      width: 80,
      editRender: {
        name: 'el-input-number',
        attrs: { min: 0, max: 150 }
      }
    }
  ],
  data: []
}
```

### 高级编辑配置
```typescript
const advancedConfig = {
  editable: true,
  columns: [
    {
      field: 'status',
      title: '状态',
      width: 120,
      editRender: {
        name: 'el-select',
        options: [
          { value: 'active', label: '激活' },
          { value: 'inactive', label: '禁用' }
        ],
        optionProps: { value: 'value', label: 'label' }
      }
    },
    {
      field: 'date',
      title: '日期',
      width: 180,
      editRender: {
        name: 'el-date-picker',
        attrs: {
          type: 'date',
          format: 'YYYY-MM-DD'
        }
      }
    }
  ]
}
```

## 使用示例

### 基础可编辑表格
```vue
<template>
  <div class="curd-pro-demo">
    <div class="toolbar">
      <el-button type="primary" @click="handleAdd">添加行</el-button>
      <el-button type="success" @click="handleSave">保存</el-button>
      <el-button type="warning" @click="handleValidate">验证</el-button>
    </div>
    
    <FuniCurdPro
      ref="curdProRef"
      :columns="columns"
      :data="tableData"
      :editable="true"
      row-key="id"
      @edit-closed="handleEditClosed"
      @valid-error="handleValidError"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const curdProRef = ref()

const columns = reactive([
  {
    field: 'name',
    title: '姓名',
    width: 120,
    editRender: {
      name: 'el-input',
      attrs: { placeholder: '请输入姓名' }
    },
    rules: [
      { required: true, message: '姓名不能为空' }
    ]
  },
  {
    field: 'age',
    title: '年龄',
    width: 100,
    editRender: {
      name: 'el-input-number',
      attrs: { min: 0, max: 150 }
    },
    rules: [
      { required: true, message: '年龄不能为空' }
    ]
  },
  {
    field: 'email',
    title: '邮箱',
    width: 200,
    editRender: {
      name: 'el-input',
      attrs: { placeholder: '请输入邮箱' }
    },
    rules: [
      { required: true, message: '邮箱不能为空' },
      { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '邮箱格式不正确' }
    ]
  },
  {
    field: 'status',
    title: '状态',
    width: 120,
    editRender: {
      name: 'el-select',
      options: [
        { value: 'active', label: '激活' },
        { value: 'inactive', label: '禁用' }
      ],
      optionProps: { value: 'value', label: 'label' }
    }
  }
])

const tableData = ref([
  {
    id: 1,
    name: '张三',
    age: 28,
    email: '<EMAIL>',
    status: 'active'
  },
  {
    id: 2,
    name: '李四',
    age: 32,
    email: '<EMAIL>',
    status: 'inactive'
  }
])

const handleAdd = async () => {
  try {
    await curdProRef.value.insertRow()
    ElMessage.success('添加行成功')
  } catch (error) {
    ElMessage.error('添加行失败')
  }
}

const handleSave = async () => {
  try {
    const data = await curdProRef.value.getTableData({ validate: true })
    console.log('保存数据:', data)
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('数据验证失败，请检查输入')
  }
}

const handleValidate = async () => {
  try {
    const isValid = await curdProRef.value.validate()
    if (isValid) {
      ElMessage.success('数据验证通过')
    }
  } catch (error) {
    ElMessage.error('数据验证失败')
  }
}

const handleEditClosed = ({ row, column }) => {
  console.log('编辑结束:', row, column)
}

const handleValidError = ({ rule, row, column }) => {
  console.log('验证错误:', rule, row, column)
}
</script>

<style scoped>
.curd-pro-demo {
  padding: 20px;
}

.toolbar {
  margin-bottom: 16px;
}

.toolbar .el-button {
  margin-right: 8px;
}
</style>
```

## VXE Table API支持

FuniCurdPro基于VXE Table封装，支持VXE Table的大部分API：

```vue
<template>
  <FuniCurdPro
    v-bind="vxeTableOptions"
    :columns="columns"
    :data="data"
    @cell-click="handleCellClick"
    @edit-closed="handleEditClosed"
  />
</template>

<script setup>
const vxeTableOptions = {
  border: true,
  stripe: true,
  showHeaderOverflow: true,
  showOverflow: true,
  keepSource: true,
  columnConfig: { resizable: true },
  headerAlign: 'center'
}
</script>
```

## 注意事项

### 1. 数据结构
- 表格数据必须是数组格式
- 每行数据建议包含唯一标识字段
- 编辑时会直接修改原始数据

### 2. 编辑功能
- 默认启用行编辑模式
- 支持单元格级别的验证
- 编辑器基于ElementPlus组件

### 3. 性能考虑
- 大数据量时建议启用虚拟滚动
- 避免在编辑器中进行复杂计算
- 合理使用数据验证规则

### 4. 兼容性
- 需要安装VXE Table依赖
- 与ElementPlus组件库集成
- 支持Vue 3.x版本

## 常见问题

### Q: 如何自定义编辑器？
A: 通过editRender配置自定义编辑器，支持所有ElementPlus表单组件

### Q: 如何实现数据验证？
A: 在列配置中添加rules属性，支持多种验证规则

### Q: 如何获取编辑后的数据？
A: 使用getTableData方法获取表格数据，可选择是否进行验证

### Q: 如何处理大数据量？
A: 建议使用VXE Table的虚拟滚动功能，或实现分页加载
