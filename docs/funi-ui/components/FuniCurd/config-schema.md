# FuniCurd 配置结构

## 基础配置结构

```typescript
interface FuniCurdConfig {
  // 数据配置
  columns: ColumnConfig[];         // 表格列配置（必填）
  loadData?: LoadDataFunction;     // 数据加载函数
  data?: any[];                    // 静态数据
  
  // 状态配置
  loading?: boolean;               // 加载状态
  
  // 功能配置
  selection?: boolean;             // 是否显示选择框
  actions?: ActionConfig[];        // 操作按钮配置
  pagination?: PaginationConfig | boolean; // 分页配置
  
  // 请求配置
  requestParams?: Record<string, any>; // 请求参数
  autoLoad?: boolean;              // 是否自动加载数据
  
  // 显示配置
  showIndex?: boolean;             // 是否显示序号列
  indexLabel?: string;             // 序号列标题
  emptyText?: string;              // 空数据提示文本
  
  // 表格配置
  rowKey?: string | Function;      // 行数据的Key
  defaultSort?: SortConfig;        // 默认排序
  showSummary?: boolean;           // 是否显示合计行
  sumText?: string;                // 合计行第一列文本
  summaryMethod?: SummaryMethod;   // 自定义合计方法
  
  // ElementPlus el-table 属性
  stripe?: boolean;                // 斑马纹
  border?: boolean;                // 边框
  size?: 'large' | 'default' | 'small'; // 尺寸
  height?: string | number;        // 表格高度
  maxHeight?: string | number;     // 最大高度
  fit?: boolean;                   // 列宽自适应
  showHeader?: boolean;            // 是否显示表头
  highlightCurrentRow?: boolean;   // 是否高亮当前行
  currentRowKey?: string | number; // 当前行的key
  rowClassName?: string | Function; // 行的className
  rowStyle?: object | Function;    // 行的style
  cellClassName?: string | Function; // 单元格的className
  cellStyle?: object | Function;   // 单元格的style
  headerRowClassName?: string | Function; // 表头行的className
  headerRowStyle?: object | Function; // 表头行的style
  headerCellClassName?: string | Function; // 表头单元格的className
  headerCellStyle?: object | Function; // 表头单元格的style
  defaultExpandAll?: boolean;      // 是否默认展开所有行
  expandRowKeys?: any[];           // 展开行的keys
  tooltipEffect?: 'dark' | 'light'; // tooltip效果
  selectOnIndeterminate?: boolean; // 在多选表格中，当仅有部分行被选中时，点击表头的多选框时的行为
  indent?: number;                 // 展示树形数据时，树节点的缩进
  lazy?: boolean;                  // 是否懒加载子节点数据
  load?: Function;                 // 加载子节点数据的函数
  treeProps?: TreeProps;           // 渲染嵌套数据的配置选项
  tableLayout?: 'fixed' | 'auto';  // 表格的布局方式
  scrollbarAlwaysOn?: boolean;     // 总是显示滚动条
  flexible?: boolean;              // 确保主轴的最小尺寸
}
```

## 列配置结构

```typescript
interface ColumnConfig {
  // 基础配置
  label: string;                   // 列标题（必填）
  prop: string;                    // 数据字段名（必填）
  width?: number;                  // 列宽度
  minWidth?: number;               // 最小宽度
  fixed?: 'left' | 'right' | boolean; // 固定列位置
  
  // 显示配置
  align?: 'left' | 'center' | 'right'; // 对齐方式
  headerAlign?: 'left' | 'center' | 'right'; // 表头对齐
  showOverflowTooltip?: boolean;   // 超出显示tooltip
  sortable?: boolean | 'custom';   // 排序配置
  resizable?: boolean;             // 是否可调整宽度
  
  // 自定义渲染
  slots?: {
    default?: string;              // 内容插槽名
    header?: string;               // 表头插槽名
  };
  formatter?: (row: any, column: any, cellValue: any, index: number) => string;
  
  // 条件显示
  show?: boolean | ComputedRef<boolean>; // 显示条件
  
  // ElementPlus el-table-column属性
  type?: 'selection' | 'index' | 'expand'; // 列类型
  index?: number | ((index: number) => number); // 索引
  columnKey?: string;              // 列的key
  className?: string;              // 列的className
  labelClassName?: string;         // 表头的className
  selectable?: (row: any, index: number) => boolean; // 选择函数
  reserveSelection?: boolean;      // 保留选择
  filters?: FilterConfig[];        // 过滤器
  filterPlacement?: string;        // 过滤器位置
  filterMultiple?: boolean;        // 多选过滤
  filterMethod?: (value: any, row: any, column: any) => boolean;
  filteredValue?: any[];           // 过滤值
}

interface FilterConfig {
  text: string;                    // 过滤选项显示的文本
  value: any;                      // 过滤选项的值
}
```

## 操作按钮配置结构

```typescript
interface ActionConfig {
  // 按钮基础信息
  name: string;                    // 按钮显示文本（必填）
  key: string;                     // 按钮唯一标识（必填）
  
  // ElementPlus按钮属性
  props?: {
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text' | 'default';
    size?: 'large' | 'default' | 'small';
    icon?: string;                 // 图标
    disabled?: boolean;            // 是否禁用
    loading?: boolean;             // 加载状态
    plain?: boolean;               // 朴素按钮
    round?: boolean;               // 圆角按钮
    circle?: boolean;              // 圆形按钮
    link?: boolean;                // 链接按钮
    autofocus?: boolean;           // 是否默认聚焦
    nativeType?: 'button' | 'submit' | 'reset'; // 原生type属性
    autoInsertSpace?: boolean;     // 自动在两个中文字符之间插入空格
    color?: string;                // 自定义按钮颜色
    dark?: boolean;                // dark模式
    bg?: boolean;                  // 是否为朴素按钮添加背景色
  };
  
  // 事件处理
  on?: {
    click?: (row?: any, index?: number) => void; // 点击事件
    [key: string]: Function;       // 其他事件
  };
  
  // 权限控制
  auth?: string | string[];        // 权限标识
  show?: (row?: any, index?: number) => boolean; // 显示条件
  
  // 位置配置
  position?: 'header' | 'row';     // 按钮位置：表头或行内
}
```

## 分页配置结构

```typescript
interface PaginationConfig {
  // 基础配置
  pageSize?: number;               // 每页大小，默认20
  pageSizes?: number[];            // 每页大小选项，默认[10, 20, 50, 100]
  layout?: string;                 // 分页布局，默认'total, sizes, prev, pager, next, jumper'
  
  // ElementPlus el-pagination属性
  small?: boolean;                 // 小型分页
  background?: boolean;            // 背景色
  pagerCount?: number;             // 页码按钮数量，默认7
  hideOnSinglePage?: boolean;      // 单页时隐藏
  prevText?: string;               // 上一页文本
  nextText?: string;               // 下一页文本
  disabled?: boolean;              // 是否禁用
  teleported?: boolean;            // 是否将下拉菜单teleport至body
  popperClass?: string;            // 下拉菜单的类名
  prevIcon?: string;               // 上一页图标
  nextIcon?: string;               // 下一页图标
  jumperIcon?: string;             // 跳转图标
}
```

## 数据加载函数结构

```typescript
interface LoadDataFunction {
  (params: LoadDataParams): Promise<LoadDataResult>;
}

interface LoadDataParams {
  pageNum: number;                 // 当前页码
  pageSize: number;                // 每页大小
  sortField?: string;              // 排序字段
  sortOrder?: 'asc' | 'desc';      // 排序方向
  filters?: Record<string, any>;   // 过滤条件
  [key: string]: any;              // 其他查询参数
}

interface LoadDataResult {
  list: any[];                     // 数据列表（必填）
  total: number;                   // 总数（必填）
  pageNum?: number;                // 当前页码
  pageSize?: number;               // 每页大小
  [key: string]: any;              // 其他返回数据
}
```

## 排序配置结构

```typescript
interface SortConfig {
  prop: string;                    // 排序字段
  order: 'ascending' | 'descending'; // 排序方向
}
```

## 合计方法结构

```typescript
interface SummaryMethod {
  (params: SummaryParams): string[];
}

interface SummaryParams {
  columns: any[];                  // 列配置
  data: any[];                     // 表格数据
}
```

## 树形数据配置

```typescript
interface TreeProps {
  hasChildren?: string;            // 指定哪些行是包含子节点，默认'hasChildren'
  children?: string;               // 指定子节点为节点对象的某个属性值，默认'children'
  checkStrictly?: boolean;         // 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法
}
```

## 常用配置组合

### 基础表格配置
```typescript
const basicTableConfig: FuniCurdConfig = {
  columns: [
    { label: 'ID', prop: 'id', width: 80 },
    { label: '名称', prop: 'name', minWidth: 120 },
    { label: '状态', prop: 'status', width: 100 },
    { label: '创建时间', prop: 'createTime', width: 180 }
  ],
  loadData: async ({ pageNum, pageSize, ...params }) => {
    const response = await api.getList({ pageNum, pageSize, ...params });
    return {
      list: response.data.list,
      total: response.data.total
    };
  },
  pagination: {
    pageSize: 20,
    pageSizes: [10, 20, 50, 100]
  }
};
```

### 带操作按钮的表格配置
```typescript
const actionTableConfig: FuniCurdConfig = {
  columns: [
    { label: 'ID', prop: 'id', width: 80 },
    { label: '用户名', prop: 'username', minWidth: 120 },
    { label: '邮箱', prop: 'email', minWidth: 180 },
    { label: '状态', prop: 'status', width: 100, slots: { default: 'status' } }
  ],
  actions: [
    {
      name: '编辑',
      key: 'edit',
      props: { type: 'primary', size: 'small' },
      on: { click: (row) => handleEdit(row) }
    },
    {
      name: '删除',
      key: 'delete',
      props: { type: 'danger', size: 'small' },
      on: { click: (row) => handleDelete(row) },
      show: (row) => row.status !== 'deleted'
    }
  ],
  selection: true,
  loadData: loadUserData
};
```

### 带搜索和排序的表格配置
```typescript
const searchSortTableConfig: FuniCurdConfig = {
  columns: [
    { 
      label: 'ID', 
      prop: 'id', 
      width: 80, 
      sortable: true 
    },
    { 
      label: '名称', 
      prop: 'name', 
      minWidth: 120, 
      sortable: 'custom',
      showOverflowTooltip: true
    },
    { 
      label: '类型', 
      prop: 'type', 
      width: 120,
      filters: [
        { text: '类型A', value: 'A' },
        { text: '类型B', value: 'B' },
        { text: '类型C', value: 'C' }
      ],
      filterMethod: (value, row) => row.type === value
    },
    { 
      label: '创建时间', 
      prop: 'createTime', 
      width: 180, 
      sortable: true 
    }
  ],
  defaultSort: { prop: 'createTime', order: 'descending' },
  loadData: async (params) => {
    // 处理排序参数
    if (params.sortField && params.sortOrder) {
      params.orderBy = params.sortField;
      params.order = params.sortOrder === 'asc' ? 'ASC' : 'DESC';
    }
    
    const response = await api.getList(params);
    return {
      list: response.data.list,
      total: response.data.total
    };
  }
};
```

### 树形表格配置
```typescript
const treeTableConfig: FuniCurdConfig = {
  columns: [
    { label: '名称', prop: 'name', minWidth: 200 },
    { label: '类型', prop: 'type', width: 100 },
    { label: '大小', prop: 'size', width: 100 },
    { label: '修改时间', prop: 'modifyTime', width: 180 }
  ],
  rowKey: 'id',
  treeProps: {
    children: 'children',
    hasChildren: 'hasChildren'
  },
  lazy: true,
  load: async (tree, treeNode, resolve) => {
    const children = await api.getChildren(tree.id);
    resolve(children);
  },
  defaultExpandAll: false
};
```

### 自定义合计行配置
```typescript
const summaryTableConfig: FuniCurdConfig = {
  columns: [
    { label: '商品', prop: 'name', minWidth: 120 },
    { label: '数量', prop: 'quantity', width: 100 },
    { label: '单价', prop: 'price', width: 100 },
    { label: '总价', prop: 'total', width: 100 }
  ],
  showSummary: true,
  sumText: '总计',
  summaryMethod: ({ columns, data }) => {
    const sums = [];
    columns.forEach((column, index) => {
      if (index === 0) {
        sums[index] = '总计';
        return;
      }
      
      if (column.property === 'quantity' || column.property === 'total') {
        const values = data.map(item => Number(item[column.property]));
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
        } else {
          sums[index] = 'N/A';
        }
      } else {
        sums[index] = '';
      }
    });
    
    return sums;
  },
  loadData: loadOrderData
};
```

## 最佳实践建议

### 1. 列配置优化
- 为重要列设置minWidth而不是固定width
- 长文本列启用showOverflowTooltip
- 合理使用fixed固定重要列
- 为可排序列设置sortable属性

### 2. 性能优化
- 大数据量时使用虚拟滚动
- 合理设置分页大小
- 避免在formatter中进行复杂计算
- 使用rowKey提高渲染性能

### 3. 用户体验
- 提供清晰的空数据提示
- 合理设置加载状态
- 为操作按钮提供权限控制
- 使用合适的表格尺寸

### 4. 数据处理
- 统一数据格式和字段命名
- 合理处理排序和过滤参数
- 提供数据转换和格式化
- 处理异常情况和错误状态
