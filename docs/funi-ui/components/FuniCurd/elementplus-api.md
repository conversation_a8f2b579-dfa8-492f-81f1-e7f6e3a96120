# FuniCurd ElementPlus API 支持

## 基础组件说明

FuniCurd 是基于 ElementPlus 的 `el-table` 和 `el-pagination` 组件进行二次封装的数据表格组件。它通过 `v-bind="$attrs"` 的方式透传所有 ElementPlus el-table 的原生属性和事件。

## 支持的 ElementPlus el-table API

### 基础属性透传

| ElementPlus 属性 | 类型 | 默认值 | 说明 | FuniCurd 支持 |
|-----------------|------|--------|------|---------------|
| data | Array | [] | 显示的数据 | ✅ 透传支持 |
| height | String/Number | - | Table 的高度 | ✅ 透传支持 |
| max-height | String/Number | - | Table 的最大高度 | ✅ 透传支持 |
| stripe | Boolean | false | 是否为斑马纹 table | ✅ 透传支持 |
| border | Boolean | false | 是否带有纵向边框 | ✅ 透传支持 |
| size | String | - | Table 的尺寸 | ✅ 透传支持 |
| fit | Boolean | true | 列的宽度是否自撑开 | ✅ 透传支持 |
| show-header | Boolean | true | 是否显示表头 | ✅ 透传支持 |
| highlight-current-row | Boolean | false | 是否要高亮当前行 | ✅ 透传支持 |
| current-row-key | String/Number | - | 当前行的 key | ✅ 透传支持 |
| row-class-name | String/Function | - | 行的 className | ✅ 透传支持 |
| row-style | Object/Function | - | 行的 style | ✅ 透传支持 |
| cell-class-name | String/Function | - | 单元格的 className | ✅ 透传支持 |
| cell-style | Object/Function | - | 单元格的 style | ✅ 透传支持 |
| header-row-class-name | String/Function | - | 表头行的 className | ✅ 透传支持 |
| header-row-style | Object/Function | - | 表头行的 style | ✅ 透传支持 |
| header-cell-class-name | String/Function | - | 表头单元格的 className | ✅ 透传支持 |
| header-cell-style | Object/Function | - | 表头单元格的 style | ✅ 透传支持 |
| row-key | String/Function | - | 行数据的 Key | ✅ 透传支持 |
| empty-text | String | 暂无数据 | 空数据时显示的文本内容 | ✅ 透传支持 |
| default-expand-all | Boolean | false | 是否默认展开所有行 | ✅ 透传支持 |
| expand-row-keys | Array | - | 可以通过该属性设置展开行 | ✅ 透传支持 |
| default-sort | Object | - | 默认的排序列的 prop 和顺序 | ✅ 透传支持 |
| tooltip-effect | String | dark | tooltip effect 属性 | ✅ 透传支持 |
| tooltip-options | Object | - | tooltip 选项 | ✅ 透传支持 |
| show-summary | Boolean | false | 是否在表尾显示合计行 | ✅ 透传支持 |
| sum-text | String | 合计 | 显示摘要行第一列的文本 | ✅ 透传支持 |
| summary-method | Function | - | 自定义的合计计算方法 | ✅ 透传支持 |
| span-method | Function | - | 合并行或列的计算方法 | ✅ 透传支持 |
| select-on-indeterminate | Boolean | true | 在多选表格中，当仅有部分行被选中时，点击表头的多选框时的行为 | ✅ 透传支持 |
| indent | Number | 16 | 展示树形数据时，树节点的缩进 | ✅ 透传支持 |
| lazy | Boolean | false | 是否懒加载子节点数据 | ✅ 透传支持 |
| load | Function | - | 加载子节点数据的函数 | ✅ 透传支持 |
| tree-props | Object | - | 渲染嵌套数据的配置选项 | ✅ 透传支持 |
| table-layout | String | fixed | 设置表格单元、行和列的布局方式 | ✅ 透传支持 |
| scrollbar-always-on | Boolean | false | 总是显示滚动条 | ✅ 透传支持 |
| flexible | Boolean | false | 确保主轴的最小尺寸 | ✅ 透传支持 |

### 支持的 ElementPlus el-table 事件

| 事件名 | 说明 | 参数 | FuniCurd 支持 |
|--------|------|------|---------------|
| select | 当用户手动勾选数据行的 Checkbox 时触发的事件 | selection, row | ✅ 透传支持 |
| select-all | 当用户手动勾选全选 Checkbox 时触发的事件 | selection | ✅ 透传支持 |
| selection-change | 当选择项发生变化时会触发该事件 | selection | ✅ 透传支持 |
| cell-mouse-enter | 当单元格 hover 进入时会触发该事件 | row, column, cell, event | ✅ 透传支持 |
| cell-mouse-leave | 当单元格 hover 退出时会触发该事件 | row, column, cell, event | ✅ 透传支持 |
| cell-click | 当某个单元格被点击时会触发该事件 | row, column, cell, event | ✅ 透传支持 |
| cell-dblclick | 当某个单元格被双击击时会触发该事件 | row, column, cell, event | ✅ 透传支持 |
| cell-contextmenu | 当某个单元格被鼠标右键点击时会触发该事件 | row, column, cell, event | ✅ 透传支持 |
| row-click | 当某一行被点击时会触发该事件 | row, column, event | ✅ 透传支持 |
| row-contextmenu | 当某一行被鼠标右键点击时会触发该事件 | row, column, event | ✅ 透传支持 |
| row-dblclick | 当某一行被双击时会触发该事件 | row, column, event | ✅ 透传支持 |
| header-click | 当某一列的表头被点击时会触发该事件 | column, event | ✅ 透传支持 |
| header-contextmenu | 当某一列的表头被鼠标右键点击时触发该事件 | column, event | ✅ 透传支持 |
| sort-change | 当表格的排序条件发生变化的时候会触发该事件 | { column, prop, order } | ✅ 透传支持 |
| filter-change | column 的 key，如果需要使用 filter-change 事件，则需要此属性标识是哪个 column 的筛选条件 | filters | ✅ 透传支持 |
| current-change | 当表格的当前行发生变化的时候会触发该事件 | currentRow, oldCurrentRow | ✅ 透传支持 |
| header-dragend | 当拖动表头改变了列的宽度的时候会触发该事件 | newWidth, oldWidth, column, event | ✅ 透传支持 |
| expand-change | 当用户对某一行展开或者关闭的时候会触发该事件 | row, expandedRows | ✅ 透传支持 |

### 支持的 ElementPlus el-table 方法

| 方法名 | 说明 | 参数 | FuniCurd 支持 |
|--------|------|------|---------------|
| clearSelection | 用于多选表格，清空用户的选择 | - | ✅ 透传支持 |
| getSelectionRows | 返回当前选中的行 | - | ✅ 透传支持 |
| toggleRowSelection | 用于多选表格，切换某一行的选中状态 | row, selected | ✅ 透传支持 |
| toggleAllSelection | 用于多选表格，切换全选和全不选 | - | ✅ 透传支持 |
| toggleRowExpansion | 用于可展开表格与树形表格，切换某一行的展开状态 | row, expanded | ✅ 透传支持 |
| setCurrentRow | 用于单选表格，设定某一行为选中行 | row | ✅ 透传支持 |
| clearSort | 用于清空排序条件，数据会恢复成未排序的状态 | - | ✅ 透传支持 |
| clearFilter | 不传入参数时用于清空所有过滤条件 | columnKeys | ✅ 透传支持 |
| doLayout | 对 Table 进行重新布局 | - | ✅ 透传支持 |
| sort | 手动对 Table 进行排序 | prop, order | ✅ 透传支持 |
| scrollTo | 滚动到一组特定坐标 | options / x, y | ✅ 透传支持 |
| setScrollTop | 设置垂直滚动位置 | top | ✅ 透传支持 |
| setScrollLeft | 设置水平滚动位置 | left | ✅ 透传支持 |

## 支持的 ElementPlus el-pagination API

### 分页属性透传

| ElementPlus 属性 | 类型 | 默认值 | 说明 | FuniCurd 支持 |
|-----------------|------|--------|------|---------------|
| small | Boolean | false | 是否使用小型分页样式 | ✅ 透传支持 |
| background | Boolean | false | 是否为分页按钮添加背景色 | ✅ 透传支持 |
| page-size | Number | 10 | 每页显示条目个数 | ✅ 透传支持 |
| total | Number | - | 总条目数 | ✅ 透传支持 |
| page-count | Number | - | 总页数 | ✅ 透传支持 |
| pager-count | Number | 7 | 设置最大页码按钮数 | ✅ 透传支持 |
| current-page | Number | 1 | 当前页数 | ✅ 透传支持 |
| layout | String | prev, pager, next, jumper, ->, total | 组件布局 | ✅ 透传支持 |
| page-sizes | Array | [10, 20, 30, 40, 50, 100] | 每页显示个数选择器的选项设置 | ✅ 透传支持 |
| popper-class | String | - | 每页显示个数选择器的下拉框类名 | ✅ 透传支持 |
| prev-text | String | - | 替代图标显示的上一页文字 | ✅ 透传支持 |
| prev-icon | String/Component | ArrowLeft | 上一页的图标 | ✅ 透传支持 |
| next-text | String | - | 替代图标显示的下一页文字 | ✅ 透传支持 |
| next-icon | String/Component | ArrowRight | 下一页的图标 | ✅ 透传支持 |
| disabled | Boolean | false | 是否禁用分页 | ✅ 透传支持 |
| hide-on-single-page | Boolean | false | 只有一页时是否隐藏 | ✅ 透传支持 |

### 分页事件透传

| 事件名 | 说明 | 参数 | FuniCurd 支持 |
|--------|------|------|---------------|
| size-change | pageSize 改变时会触发 | 每页条数 | ✅ 透传支持 |
| current-change | currentPage 改变时会触发 | 当前页 | ✅ 透传支持 |
| prev-click | 用户点击上一页按钮改变当前页后触发 | 当前页 | ✅ 透传支持 |
| next-click | 用户点击下一页按钮改变当前页后触发 | 当前页 | ✅ 透传支持 |

## 透传方式说明

### 1. 属性透传

FuniCurd 通过 `v-bind="$attrs"` 将所有未在 props 中声明的属性直接透传给内部的 el-table 组件：

```vue
<template>
  <el-table
    ref="curdRef"
    v-bind="$attrs"
    :data="visibleData"
    :rowKey="rowKey"
    :stripe="stripe"
    :scrollbar-always-on="scrollbarAlwaysOn"
    :size="size"
    <!-- 其他 FuniCurd 特有属性 -->
  >
    <!-- 表格内容 -->
  </el-table>
</template>
```

### 2. 事件透传

大部分 ElementPlus el-table 事件会自动透传，部分事件被 FuniCurd 拦截并重新封装：

```javascript
// 被重新封装的事件
@row-click="handleRowClick"           // 增加了选择逻辑
@row-dblclick="handleRowDoubleClick"  // 增加了选择逻辑
@select="handleSelect"                // 增加了跨页选择逻辑
@select-all="handleSelectAll"        // 增加了跨页选择逻辑

// 直接透传的事件
@cell-click="$attrs.onCellClick"
@header-click="$attrs.onHeaderClick"
@sort-change="$attrs.onSortChange"
// ... 其他事件
```

### 3. 方法透传

通过 ref 可以直接访问 ElementPlus el-table 的所有方法：

```javascript
// 获取 FuniCurd 实例
const curdRef = ref()

// 访问内部 el-table 的方法
curdRef.value.ref.clearSelection()
curdRef.value.ref.toggleRowSelection(row, true)
curdRef.value.ref.doLayout()
```

## 使用示例

### 基础透传使用

```vue
<template>
  <FuniCurd
    :columns="columns"
    :lodaData="loadData"
    
    <!-- ElementPlus el-table 属性透传 -->
    stripe
    border
    size="small"
    height="400"
    :default-sort="{ prop: 'date', order: 'descending' }"
    :row-class-name="tableRowClassName"
    show-summary
    :summary-method="getSummaries"
    
    <!-- ElementPlus el-table 事件透传 -->
    @cell-click="handleCellClick"
    @header-click="handleHeaderClick"
    @sort-change="handleSortChange"
    @selection-change="handleSelectionChange"
  />
</template>

<script setup>
const tableRowClassName = ({ row, rowIndex }) => {
  if (rowIndex === 1) {
    return 'warning-row'
  } else if (rowIndex === 3) {
    return 'success-row'
  }
  return ''
}

const getSummaries = (param) => {
  const { columns, data } = param
  const sums = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '总价'
      return
    }
    const values = data.map(item => Number(item[column.property]))
    if (!values.every(value => Number.isNaN(value))) {
      sums[index] = values.reduce((prev, curr) => {
        const value = Number(curr)
        if (!Number.isNaN(value)) {
          return prev + curr
        } else {
          return prev
        }
      }, 0)
      sums[index] += ' 元'
    } else {
      sums[index] = 'N/A'
    }
  })
  return sums
}

const handleCellClick = (row, column, cell, event) => {
  console.log('单元格点击', { row, column, cell, event })
}

const handleHeaderClick = (column, event) => {
  console.log('表头点击', { column, event })
}

const handleSortChange = ({ column, prop, order }) => {
  console.log('排序变化', { column, prop, order })
}

const handleSelectionChange = (selection) => {
  console.log('选择变化', selection)
}
</script>
```

### 高级透传功能

```vue
<template>
  <FuniCurd
    ref="curdRef"
    :columns="columns"
    :lodaData="loadData"
    
    <!-- 树形表格相关属性 -->
    :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    :lazy="true"
    :load="loadNode"
    
    <!-- 展开行相关属性 -->
    :default-expand-all="false"
    :expand-row-keys="expandedKeys"
    @expand-change="handleExpandChange"
    
    <!-- 合并单元格 -->
    :span-method="objectSpanMethod"
    
    <!-- 自定义空状态 -->
    empty-text="暂无相关数据"
  >
    <!-- 展开行内容 -->
    <template #expand="{ row }">
      <div class="expand-content">
        <p>详细信息：{{ row.description }}</p>
        <p>创建时间：{{ row.createTime }}</p>
      </div>
    </template>
  </FuniCurd>
</template>

<script setup>
const expandedKeys = ref([1, 3])

const loadNode = (tree, treeNode, resolve) => {
  setTimeout(() => {
    resolve([
      {
        id: Math.random(),
        name: `子节点${Math.random()}`,
        leaf: true
      }
    ])
  }, 1000)
}

const handleExpandChange = (row, expandedRows) => {
  console.log('展开状态变化', { row, expandedRows })
}

const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex === 0) {
    if (rowIndex % 2 === 0) {
      return {
        rowspan: 2,
        colspan: 1
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0
      }
    }
  }
}
</script>
```

## 注意事项

1. **属性优先级**：FuniCurd 自有的 props 会覆盖透传的同名属性
2. **事件处理**：部分事件被 FuniCurd 重新封装，如需原始事件请使用 ref 访问内部组件
3. **方法调用**：通过 `curdRef.value.ref` 访问 ElementPlus el-table 的原生方法
4. **样式继承**：ElementPlus 的样式类名和 CSS 变量完全支持
5. **版本兼容**：确保 ElementPlus 版本与 FuniCurd 兼容
6. **性能考虑**：大数据量时建议使用虚拟滚动等 ElementPlus 高级特性
