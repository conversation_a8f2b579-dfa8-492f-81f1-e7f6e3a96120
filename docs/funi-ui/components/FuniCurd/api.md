# FuniCurd API文档

## 组件概述

FuniCurd是基于ElementPlus的el-table和el-pagination封装的数据表格组件，集成了表格展示、分页、操作按钮等功能，适用于数据列表展示和操作场景。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 | ElementPlus对应 |
|--------|------|--------|------|------|----------------|
| columns | Array | [] | ✅ | 表格列配置数组 | - |
| loadData | Function | - | - | 数据加载函数 | - |
| data | Array | [] | - | 静态数据（与loadData二选一） | el-table.data |
| loading | Boolean | false | - | 加载状态 | el-table.v-loading |
| selection | Boolean | false | - | 是否显示选择框 | - |
| actions | Array | [] | - | 操作按钮配置 | - |
| pagination | Object/Boolean | true | - | 分页配置，false时不显示分页 | - |
| requestParams | Object | {} | - | 请求参数 | - |
| autoLoad | Boolean | true | - | 是否自动加载数据 | - |
| showIndex | Boolean | false | - | 是否显示序号列 | - |
| indexLabel | String | '序号' | - | 序号列标题 | - |
| emptyText | String | '暂无数据' | - | 空数据提示文本 | el-table.empty-text |
| rowKey | String/Function | 'id' | - | 行数据的Key | el-table.row-key |
| defaultSort | Object | - | - | 默认排序 | el-table.default-sort |
| showSummary | Boolean | false | - | 是否显示合计行 | el-table.show-summary |
| sumText | String | '合计' | - | 合计行第一列文本 | el-table.sum-text |
| summaryMethod | Function | - | - | 自定义合计方法 | el-table.summary-method |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| selection-change | selection: Array | 选择项变化事件 | 选择项发生变化时 |
| row-click | (row, column, event) | 行点击事件 | 点击表格行时 |
| row-dblclick | (row, column, event) | 行双击事件 | 双击表格行时 |
| sort-change | ({ column, prop, order }) | 排序变化事件 | 排序发生变化时 |
| filter-change | filters: Object | 过滤变化事件 | 过滤条件变化时 |
| current-change | (currentRow, oldCurrentRow) | 当前行变化事件 | 当前行发生变化时 |
| header-click | (column, event) | 表头点击事件 | 点击表头时 |
| cell-click | (row, column, cell, event) | 单元格点击事件 | 点击单元格时 |
| cell-dblclick | (row, column, cell, event) | 单元格双击事件 | 双击单元格时 |
| page-change | (page, pageSize) | 分页变化事件 | 分页发生变化时 |
| refresh | - | 刷新事件 | 手动刷新数据时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| refresh | (resetPage?: boolean) | Promise | 刷新表格数据 |
| getSelectionRows | - | Array | 获取当前选中的行数据 |
| toggleRowSelection | (row, selected?) | void | 切换某一行的选中状态 |
| toggleAllSelection | - | void | 切换全选状态 |
| clearSelection | - | void | 清空选择 |
| setCurrentRow | (row) | void | 设置当前行 |
| sort | (prop, order) | void | 手动排序 |
| clearSort | - | void | 清空排序 |
| clearFilter | (columnKeys?) | void | 清空过滤器 |
| doLayout | - | void | 重新布局表格 |
| scrollTo | (options, yCoord?) | void | 滚动到指定位置 |

## columns配置结构

```typescript
interface ColumnConfig {
  // 基础配置
  label: string;                   // 列标题
  prop: string;                    // 数据字段名
  width?: number;                  // 列宽度
  minWidth?: number;               // 最小宽度
  fixed?: 'left' | 'right' | boolean; // 固定列位置
  
  // 显示配置
  align?: 'left' | 'center' | 'right'; // 对齐方式
  headerAlign?: 'left' | 'center' | 'right'; // 表头对齐
  showOverflowTooltip?: boolean;   // 超出显示tooltip
  sortable?: boolean | 'custom';   // 排序配置
  resizable?: boolean;             // 是否可调整宽度
  
  // 自定义渲染
  slots?: {
    default?: string;              // 内容插槽名
    header?: string;               // 表头插槽名
  };
  formatter?: (row: any, column: any, cellValue: any, index: number) => string;
  
  // 条件显示
  show?: boolean | ComputedRef<boolean>; // 显示条件
  
  // ElementPlus el-table-column属性
  type?: 'selection' | 'index' | 'expand'; // 列类型
  index?: number | ((index: number) => number); // 索引
  columnKey?: string;              // 列的key
  className?: string;              // 列的className
  labelClassName?: string;         // 表头的className
  selectable?: (row: any, index: number) => boolean; // 选择函数
  reserveSelection?: boolean;      // 保留选择
  filters?: Array<{               // 过滤器
    text: string;
    value: any;
  }>;
  filterPlacement?: string;        // 过滤器位置
  filterMultiple?: boolean;        // 多选过滤
  filterMethod?: (value: any, row: any, column: any) => boolean;
  filteredValue?: any[];           // 过滤值
}
```

## actions配置结构

```typescript
interface ActionConfig {
  // 按钮基础信息
  name: string;                    // 按钮显示文本
  key: string;                     // 按钮唯一标识
  
  // ElementPlus按钮属性
  props?: {
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text';
    size?: 'large' | 'default' | 'small';
    icon?: string;                 // 图标
    disabled?: boolean;            // 是否禁用
    loading?: boolean;             // 加载状态
    plain?: boolean;               // 朴素按钮
    round?: boolean;               // 圆角按钮
    circle?: boolean;              // 圆形按钮
    link?: boolean;                // 链接按钮
  };
  
  // 事件处理
  on?: {
    click?: (row?: any, index?: number) => void; // 点击事件
  };
  
  // 权限控制
  auth?: string | string[];        // 权限标识
  show?: (row?: any, index?: number) => boolean; // 显示条件
  
  // 位置配置
  position?: 'header' | 'row';     // 按钮位置：表头或行内
}
```

## pagination配置结构

```typescript
interface PaginationConfig {
  // 基础配置
  pageSize?: number;               // 每页大小，默认20
  pageSizes?: number[];            // 每页大小选项，默认[10, 20, 50, 100]
  layout?: string;                 // 分页布局，默认'total, sizes, prev, pager, next, jumper'
  
  // ElementPlus el-pagination属性
  small?: boolean;                 // 小型分页
  background?: boolean;            // 背景色
  pagerCount?: number;             // 页码按钮数量
  hideOnSinglePage?: boolean;      // 单页时隐藏
  prevText?: string;               // 上一页文本
  nextText?: string;               // 下一页文本
  disabled?: boolean;              // 是否禁用
}
```

## loadData函数结构

```typescript
interface LoadDataFunction {
  (params: {
    pageNum: number;               // 当前页码
    pageSize: number;              // 每页大小
    [key: string]: any;            // 其他查询参数
  }): Promise<{
    list: any[];                   // 数据列表
    total: number;                 // 总数
    pageNum?: number;              // 当前页码
    pageSize?: number;             // 每页大小
  }>;
}
```

## 使用示例

### 基础表格
```vue
<template>
  <FuniCurd
    :columns="columns"
    :loadData="loadData"
    :actions="actions"
    selection
    show-index
    @selection-change="handleSelectionChange"
    @row-click="handleRowClick"
  >
    <!-- 状态列插槽 -->
    <template #status="{ row }">
      <el-tag :type="getStatusType(row.status)">
        {{ getStatusText(row.status) }}
      </el-tag>
    </template>
  </FuniCurd>
</template>

<script setup>
import { ref } from 'vue'

const columns = ref([
  { label: 'ID', prop: 'id', width: 80 },
  { label: '用户名', prop: 'username', minWidth: 120 },
  { label: '邮箱', prop: 'email', minWidth: 180 },
  { 
    label: '状态', 
    prop: 'status', 
    width: 100, 
    slots: { default: 'status' } 
  },
  { label: '创建时间', prop: 'createTime', width: 180 }
])

const actions = ref([
  {
    name: '编辑',
    key: 'edit',
    props: { type: 'primary', size: 'small' },
    on: { click: handleEdit }
  },
  {
    name: '删除',
    key: 'delete',
    props: { type: 'danger', size: 'small' },
    on: { click: handleDelete },
    show: (row) => row.status !== 'deleted'
  }
])

const loadData = async ({ pageNum, pageSize, ...params }) => {
  const response = await userApi.getList({
    pageNum,
    pageSize,
    ...params
  })
  
  return {
    list: response.data.list,
    total: response.data.total
  }
}

const handleSelectionChange = (selection) => {
  console.log('选择变化:', selection)
}

const handleRowClick = (row, column, event) => {
  console.log('行点击:', row)
}

const handleEdit = (row) => {
  console.log('编辑:', row)
}

const handleDelete = (row) => {
  console.log('删除:', row)
}

const getStatusType = (status) => {
  const typeMap = {
    'active': 'success',
    'inactive': 'danger',
    'pending': 'warning'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    'active': '启用',
    'inactive': '禁用',
    'pending': '待审核'
  }
  return textMap[status] || '未知'
}
</script>
```

## ElementPlus API支持

FuniCurd基于el-table和el-pagination封装，支持所有对应的API：

```vue
<template>
  <FuniCurd
    :columns="columns"
    :loadData="loadData"
    
    <!-- ElementPlus el-table 所有属性 -->
    stripe
    border
    size="default"
    :height="400"
    :max-height="600"
    :fit="true"
    :show-header="true"
    :highlight-current-row="true"
    :current-row-key="currentRowKey"
    :row-class-name="rowClassName"
    :row-style="rowStyle"
    :cell-class-name="cellClassName"
    :cell-style="cellStyle"
    :header-row-class-name="headerRowClassName"
    :header-row-style="headerRowStyle"
    :header-cell-class-name="headerCellClassName"
    :header-cell-style="headerCellStyle"
    :empty-text="emptyText"
    :default-expand-all="false"
    :expand-row-keys="expandRowKeys"
    :default-sort="defaultSort"
    :tooltip-effect="tooltipEffect"
    :show-summary="showSummary"
    :sum-text="sumText"
    :summary-method="summaryMethod"
    :span-method="spanMethod"
    :select-on-indeterminate="true"
    :indent="16"
    :lazy="false"
    :load="loadMethod"
    :tree-props="treeProps"
    :table-layout="tableLayout"
    :scrollbar-always-on="false"
    :flexible="false"
    
    <!-- ElementPlus el-table 所有事件 -->
    @select="handleSelect"
    @select-all="handleSelectAll"
    @selection-change="handleSelectionChange"
    @cell-mouse-enter="handleCellMouseEnter"
    @cell-mouse-leave="handleCellMouseLeave"
    @cell-click="handleCellClick"
    @cell-dblclick="handleCellDblclick"
    @cell-contextmenu="handleCellContextmenu"
    @row-click="handleRowClick"
    @row-contextmenu="handleRowContextmenu"
    @row-dblclick="handleRowDblclick"
    @header-click="handleHeaderClick"
    @header-contextmenu="handleHeaderContextmenu"
    @sort-change="handleSortChange"
    @filter-change="handleFilterChange"
    @current-change="handleCurrentChange"
    @header-dragend="handleHeaderDragend"
    @expand-change="handleExpandChange"
  />
</template>
```

## 注意事项

### 1. 数据加载
- loadData函数必须返回包含list和total的对象
- 支持异步数据加载，自动处理loading状态
- 可以通过requestParams传递额外的查询参数

### 2. 列配置
- prop属性必须与数据字段名对应
- 使用slots自定义列内容
- 支持动态显示/隐藏列

### 3. 操作按钮
- 支持表头按钮和行内按钮
- 可以通过show函数控制按钮显示条件
- 支持权限控制

### 4. 分页功能
- 默认启用分页，可以通过pagination=false禁用
- 支持自定义分页配置
- 自动处理分页参数传递

## 常见问题

### Q: 如何自定义列内容？
A: 在columns配置中设置slots.default，然后使用对应的插槽

### Q: 如何处理表格排序？
A: 在columns中设置sortable为true，组件会自动处理排序参数传递

### Q: 如何实现表格刷新？
A: 调用组件的refresh方法，或者修改requestParams触发自动刷新

### Q: 如何获取选中的行数据？
A: 调用getSelectionRows方法，或者监听selection-change事件
