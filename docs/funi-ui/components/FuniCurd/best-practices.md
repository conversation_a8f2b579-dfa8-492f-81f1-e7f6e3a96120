# FuniCurd 最佳实践

## 推荐用法和配置

### 1. 数据加载最佳实践

#### 推荐：使用 lodaData 函数进行远程数据加载

```javascript
// ✅ 推荐：标准的数据加载函数
const loadData = async (page, searchParams) => {
  try {
    const response = await api.getTableData({
      pageNo: page.pageNo,
      pageSize: page.pageSize,
      ...searchParams
    })
    
    return {
      list: response.data.records,
      total: response.data.total
    }
  } catch (error) {
    console.error('数据加载失败:', error)
    throw error
  }
}

// ✅ 推荐：带错误处理的数据加载
const loadDataWithErrorHandling = async (page, searchParams) => {
  const loading = ref(true)
  
  try {
    loading.value = true
    const result = await apiService.fetchData(page, searchParams)
    return result
  } catch (error) {
    // 统一错误处理
    ElMessage.error('数据加载失败，请重试')
    return { list: [], total: 0 }
  } finally {
    loading.value = false
  }
}
```

#### 避免：直接使用 data 属性进行大量数据展示

```javascript
// ❌ 避免：大量静态数据直接传入
const tableData = ref(Array.from({ length: 10000 }, (_, i) => ({ id: i })))

// ✅ 推荐：使用分页加载
const loadData = async (page) => {
  const start = (page.pageNo - 1) * page.pageSize
  const end = start + page.pageSize
  return {
    list: allData.slice(start, end),
    total: allData.length
  }
}
```

### 2. 列配置最佳实践

#### 推荐：合理的列宽设置

```javascript
// ✅ 推荐：根据内容设置合适的列宽
const columns = ref([
  {
    prop: 'id',
    label: 'ID',
    width: 80,              // 固定宽度，适用于ID等短内容
    align: 'center'
  },
  {
    prop: 'name',
    label: '名称',
    minWidth: 120,          // 最小宽度，内容较长时自动扩展
    showOverflowTooltip: true
  },
  {
    prop: 'description',
    label: '描述',
    minWidth: 200,          // 长文本使用最小宽度
    showOverflowTooltip: true
  },
  {
    prop: 'actions',
    label: '操作',
    width: 150,             // 操作列固定宽度
    fixed: 'right'          // 固定在右侧
  }
])
```

#### 推荐：使用 render 函数进行复杂渲染

```javascript
// ✅ 推荐：使用 render 函数
const columns = ref([
  {
    prop: 'status',
    label: '状态',
    width: 100,
    render: ({ row }) => {
      const statusConfig = {
        1: { text: '启用', type: 'success' },
        0: { text: '禁用', type: 'danger' }
      }
      const config = statusConfig[row.status]
      return <el-tag type={config.type}>{config.text}</el-tag>
    }
  },
  {
    prop: 'avatar',
    label: '头像',
    width: 80,
    render: ({ row }) => (
      <el-avatar size={40} src={row.avatar} alt={row.name} />
    )
  }
])
```

### 3. 搜索表单最佳实践

#### 推荐：合理的搜索表单配置

```javascript
// ✅ 推荐：结构化的搜索配置
const searchConfig = ref({
  schema: [
    {
      prop: 'keyword',
      label: '关键词',
      component: 'el-input',
      componentProps: {
        placeholder: '请输入名称或编码',
        clearable: true
      }
    },
    {
      prop: 'status',
      label: '状态',
      component: 'el-select',
      componentProps: {
        placeholder: '请选择状态',
        clearable: true
      },
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    },
    {
      prop: 'dateRange',
      label: '创建时间',
      component: 'el-date-picker',
      componentProps: {
        type: 'daterange',
        rangeSeparator: '至',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期',
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD'
      }
    }
  ],
  // 自定义参数处理
  setParams: (params) => {
    const result = { ...params }
    
    // 处理日期范围
    if (params.dateRange && params.dateRange.length === 2) {
      result.startDate = params.dateRange[0]
      result.endDate = params.dateRange[1]
      delete result.dateRange
    }
    
    return result
  }
})
```

### 4. 性能优化最佳实践

#### 推荐：合理使用分页和虚拟滚动

```javascript
// ✅ 推荐：合理的分页配置
const paginationConfig = {
  pagination: true,
  defaultPage: { pageSize: 20, pageNo: 1 },  // 合理的页面大小
  pageSizes: [10, 20, 50, 100],              // 提供多种选择
  pagerCount: 7                              // 适中的页码按钮数量
}

// ✅ 推荐：大数据量时使用虚拟滚动
const largeDataConfig = {
  height: 400,                               // 固定高度
  scrollbarAlwaysOn: true,                   // 始终显示滚动条
  // 配合 ElementPlus 的虚拟滚动功能
}
```

#### 推荐：避免不必要的重新渲染

```javascript
// ✅ 推荐：使用 computed 优化列配置
const computedColumns = computed(() => {
  return baseColumns.map(column => ({
    ...column,
    // 只在必要时添加动态属性
    ...(column.prop === 'actions' && hasPermission.value ? {
      render: ({ row }) => <ActionButtons row={row} />
    } : {})
  }))
})

// ✅ 推荐：使用 rowKey 优化渲染性能
const tableConfig = {
  rowKey: 'id',                              // 使用唯一标识
  // 或者使用函数
  rowKey: (row) => `${row.type}_${row.id}`
}
```

### 5. 用户体验最佳实践

#### 推荐：提供良好的加载状态

```javascript
// ✅ 推荐：完整的加载状态管理
const loading = ref(false)

const loadData = async (page, searchParams) => {
  loading.value = true
  
  try {
    const result = await api.getData(page, searchParams)
    return result
  } catch (error) {
    ElMessage.error('数据加载失败')
    return { list: [], total: 0 }
  } finally {
    loading.value = false
  }
}

// 在模板中使用
<FuniCurd
  :loading="loading"
  :lodaData="loadData"
/>
```

#### 推荐：合理的空状态处理

```javascript
// ✅ 推荐：自定义空状态
<FuniCurd
  empty-text="暂无相关数据"
  :lodaData="loadData"
>
  <template #empty>
    <div class="custom-empty">
      <el-empty description="暂无数据">
        <el-button type="primary" @click="handleRefresh">
          刷新数据
        </el-button>
      </el-empty>
    </div>
  </template>
</FuniCurd>
```

### 6. 操作按钮最佳实践

#### 推荐：结构化的操作按钮设计

```javascript
// ✅ 推荐：使用插槽组织操作按钮
<FuniCurd>
  <!-- 主要操作按钮 -->
  <template #buttonGroup>
    <el-button type="primary" @click="handleCreate">
      <el-icon><Plus /></el-icon>
      新建
    </el-button>
    <el-button 
      @click="handleBatchDelete" 
      :disabled="!selectedRows.length"
      type="danger"
    >
      批量删除
    </el-button>
  </template>
  
  <!-- 扩展操作按钮 -->
  <template #extendButtonGroup>
    <el-button @click="handleExport">导出</el-button>
    <el-button @click="handleImport">导入</el-button>
  </template>
  
  <!-- 行操作按钮 -->
  <template #actions="{ row }">
    <el-button type="text" @click="handleEdit(row)">编辑</el-button>
    <el-popconfirm
      title="确定删除这条记录吗？"
      @confirm="handleDelete(row)"
    >
      <template #reference>
        <el-button type="text" style="color: var(--el-color-danger)">
          删除
        </el-button>
      </template>
    </el-popconfirm>
  </template>
</FuniCurd>
```

## 避免的用法和常见错误

### 1. 避免的数据处理方式

```javascript
// ❌ 避免：在模板中进行复杂计算
<FuniCurd :data="tableData.filter(item => item.status === 1)" />

// ✅ 推荐：使用 computed 或在数据加载时处理
const filteredData = computed(() => 
  tableData.value.filter(item => item.status === 1)
)
```

### 2. 避免的列配置错误

```javascript
// ❌ 避免：所有列都设置固定宽度
const badColumns = [
  { prop: 'name', label: '名称', width: 100 },      // 可能截断长名称
  { prop: 'desc', label: '描述', width: 200 }       // 可能截断长描述
]

// ✅ 推荐：合理使用 width 和 minWidth
const goodColumns = [
  { prop: 'name', label: '名称', minWidth: 120 },
  { prop: 'desc', label: '描述', minWidth: 200, showOverflowTooltip: true }
]
```

### 3. 避免的性能问题

```javascript
// ❌ 避免：在 render 函数中进行异步操作
{
  prop: 'user',
  render: async ({ row }) => {
    const user = await getUserInfo(row.userId)  // 会导致性能问题
    return user.name
  }
}

// ✅ 推荐：预先加载数据或使用缓存
{
  prop: 'user',
  render: ({ row }) => {
    const user = userCache.value[row.userId] || { name: '加载中...' }
    return user.name
  }
}
```

## 业务场景最佳实践

### 1. 管理后台列表页面

```javascript
// 完整的管理后台列表页面配置
const useTablePage = () => {
  const curdRef = ref()
  const loading = ref(false)
  const selectedRows = ref([])
  
  // 搜索配置
  const searchConfig = {
    schema: [
      // 搜索字段配置
    ]
  }
  
  // 列配置
  const columns = [
    { type: 'selection', width: 55, fixed: 'left' },
    // 数据列配置
    { prop: 'actions', label: '操作', width: 150, fixed: 'right' }
  ]
  
  // 数据加载
  const loadData = async (page, searchParams) => {
    // 数据加载逻辑
  }
  
  // 操作方法
  const operations = {
    create: () => {},
    edit: (row) => {},
    delete: (row) => {},
    batchDelete: () => {}
  }
  
  return {
    curdRef,
    loading,
    selectedRows,
    searchConfig,
    columns,
    loadData,
    ...operations
  }
}
```

### 2. 移动端适配

```javascript
// 移动端表格配置
const mobileColumns = computed(() => {
  if (isMobile.value) {
    return [
      {
        prop: 'summary',
        label: '信息',
        minWidth: 200,
        render: ({ row }) => (
          <div class="mobile-cell">
            <div class="title">{row.name}</div>
            <div class="subtitle">{row.description}</div>
            <div class="meta">{row.createTime}</div>
          </div>
        )
      },
      {
        prop: 'actions',
        label: '操作',
        width: 80,
        render: ({ row }) => (
          <el-button type="text" @click="handleRowAction(row)">
            详情
          </el-button>
        )
      }
    ]
  }
  return desktopColumns.value
})
```

### 3. 权限控制

```javascript
// 基于权限的列配置
const permissionColumns = computed(() => {
  return baseColumns.filter(column => {
    if (column.permission) {
      return hasPermission(column.permission)
    }
    return true
  })
})

// 基于权限的操作按钮
const renderActions = ({ row }) => {
  const actions = []
  
  if (hasPermission('edit')) {
    actions.push(
      <el-button type="text" onClick={() => handleEdit(row)}>
        编辑
      </el-button>
    )
  }
  
  if (hasPermission('delete')) {
    actions.push(
      <el-button type="text" onClick={() => handleDelete(row)}>
        删除
      </el-button>
    )
  }
  
  return <div>{actions}</div>
}
```

## 性能优化建议

1. **合理设置分页大小**：根据数据复杂度调整，一般建议 10-50 条
2. **使用虚拟滚动**：大数据量时启用 ElementPlus 的虚拟滚动功能
3. **避免频繁重新渲染**：使用 `rowKey` 和合理的响应式数据结构
4. **懒加载图片**：在 render 函数中使用图片懒加载
5. **缓存计算结果**：使用 `computed` 缓存复杂的列配置计算

## 可访问性建议

1. **键盘导航**：确保表格支持键盘操作
2. **屏幕阅读器**：为重要操作添加 `aria-label`
3. **颜色对比度**：确保状态标识有足够的对比度
4. **焦点管理**：合理管理焦点状态

## 国际化支持

```javascript
// 国际化配置示例
const i18nColumns = computed(() => [
  {
    prop: 'name',
    label: t('table.name'),
    // ...
  },
  {
    prop: 'status',
    label: t('table.status'),
    formatter: (row) => t(`status.${row.status}`)
  }
])

const i18nSearchConfig = computed(() => ({
  schema: [
    {
      prop: 'keyword',
      label: t('search.keyword'),
      componentProps: {
        placeholder: t('search.keywordPlaceholder')
      }
    }
  ]
}))
```
