# FuniCurd 使用示例

## 基础使用示例

### 1. 静态数据表格

```vue
<template>
  <div class="demo-container">
    <FuniCurd
      :columns="columns"
      :data="tableData"
      :pagination="false"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const columns = ref([
  {
    prop: 'name',
    label: '姓名',
    width: 120
  },
  {
    prop: 'age',
    label: '年龄',
    width: 80
  },
  {
    prop: 'email',
    label: '邮箱',
    minWidth: 200
  },
  {
    prop: 'department',
    label: '部门',
    width: 150
  }
])

const tableData = ref([
  {
    id: 1,
    name: '张三',
    age: 28,
    email: '<EMAIL>',
    department: '技术部'
  },
  {
    id: 2,
    name: '李四',
    age: 32,
    email: '<EMAIL>',
    department: '产品部'
  },
  {
    id: 3,
    name: '王五',
    age: 25,
    email: '<EMAIL>',
    department: '设计部'
  }
])
</script>
```

### 2. 远程数据加载

```vue
<template>
  <div class="demo-container">
    <FuniCurd
      :columns="columns"
      :lodaData="loadData"
      :loading="loading"
      :pagination="true"
      :defaultPage="{ pageSize: 10, pageNo: 1 }"
      @beforeRequest="handleBeforeRequest"
      @afterRequest="handleAfterRequest"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const loading = ref(false)

const columns = ref([
  {
    prop: 'id',
    label: 'ID',
    width: 80
  },
  {
    prop: 'title',
    label: '标题',
    minWidth: 200
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    formatter: (row, column, cellValue) => {
      const statusMap = {
        1: '启用',
        0: '禁用'
      }
      return statusMap[cellValue] || '未知'
    }
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: 180
  }
])

// 模拟API请求
const loadData = async (page, searchParams) => {
  loading.value = true
  
  try {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟分页数据
    const total = 100
    const list = Array.from({ length: page.pageSize }, (_, index) => ({
      id: (page.pageNo - 1) * page.pageSize + index + 1,
      title: `数据项 ${(page.pageNo - 1) * page.pageSize + index + 1}`,
      status: Math.random() > 0.5 ? 1 : 0,
      createTime: new Date().toLocaleString()
    }))
    
    return {
      list,
      total
    }
  } finally {
    loading.value = false
  }
}

const handleBeforeRequest = () => {
  console.log('请求开始')
}

const handleAfterRequest = (data) => {
  console.log('请求完成', data)
}
</script>
```

## 高级功能示例

### 3. 带搜索表单的表格

```vue
<template>
  <div class="demo-container">
    <FuniCurd
      :columns="columns"
      :lodaData="loadData"
      :isShowSearch="true"
      :searchConfig="searchConfig"
      :loading="loading"
      @row-click="handleRowClick"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const loading = ref(false)

const searchConfig = ref({
  schema: [
    {
      prop: 'name',
      label: '姓名',
      component: 'el-input',
      componentProps: {
        placeholder: '请输入姓名'
      }
    },
    {
      prop: 'status',
      label: '状态',
      component: 'el-select',
      componentProps: {
        placeholder: '请选择状态'
      },
      options: [
        { label: '全部', value: '' },
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    }
  ]
})

const columns = ref([
  {
    type: 'selection',
    width: 55
  },
  {
    prop: 'name',
    label: '姓名',
    width: 120
  },
  {
    prop: 'status',
    label: '状态',
    width: 100,
    render: ({ row }) => {
      const statusMap = {
        1: { text: '启用', type: 'success' },
        0: { text: '禁用', type: 'danger' }
      }
      const status = statusMap[row.status] || { text: '未知', type: 'info' }
      return <el-tag type={status.type}>{status.text}</el-tag>
    }
  },
  {
    prop: 'actions',
    label: '操作',
    width: 150,
    slots: {
      default: 'actions'
    }
  }
])

const loadData = async (page, searchParams) => {
  loading.value = true
  
  try {
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 根据搜索参数过滤数据
    let mockData = Array.from({ length: 50 }, (_, index) => ({
      id: index + 1,
      name: `用户${index + 1}`,
      status: Math.random() > 0.5 ? 1 : 0,
      email: `user${index + 1}@example.com`
    }))
    
    // 应用搜索过滤
    if (searchParams.name) {
      mockData = mockData.filter(item => 
        item.name.includes(searchParams.name)
      )
    }
    if (searchParams.status !== undefined && searchParams.status !== '') {
      mockData = mockData.filter(item => 
        item.status === searchParams.status
      )
    }
    
    // 分页处理
    const total = mockData.length
    const start = (page.pageNo - 1) * page.pageSize
    const list = mockData.slice(start, start + page.pageSize)
    
    return { list, total }
  } finally {
    loading.value = false
  }
}

const handleRowClick = ({ row, selection, currentRow }) => {
  console.log('行点击', { row, selection, currentRow })
}
</script>

<template #actions="{ row }">
  <el-button type="primary" size="small" @click="handleEdit(row)">
    编辑
  </el-button>
  <el-button type="danger" size="small" @click="handleDelete(row)">
    删除
  </el-button>
</template>
```

### 4. 自定义列渲染和操作按钮

```vue
<template>
  <div class="demo-container">
    <FuniCurd
      ref="curdRef"
      :columns="columns"
      :lodaData="loadData"
      :useTools="true"
      :draggable="true"
      @draggableEnd="handleDragEnd"
    >
      <!-- 头部操作按钮 -->
      <template #buttonGroup>
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增
        </el-button>
        <el-button @click="handleBatchDelete" :disabled="!selectedRows.length">
          批量删除
        </el-button>
      </template>
      
      <!-- 自定义操作列 -->
      <template #actions="{ row }">
        <el-button type="text" @click="handleView(row)">查看</el-button>
        <el-button type="text" @click="handleEdit(row)">编辑</el-button>
        <el-popconfirm
          title="确定删除这条记录吗？"
          @confirm="handleDelete(row)"
        >
          <template #reference>
            <el-button type="text" style="color: var(--el-color-danger)">
              删除
            </el-button>
          </template>
        </el-popconfirm>
      </template>
      
      <!-- 自定义状态列 -->
      <template #status="{ row }">
        <el-switch
          v-model="row.status"
          :active-value="1"
          :inactive-value="0"
          @change="handleStatusChange(row)"
        />
      </template>
    </FuniCurd>
  </div>
</template>
```

## 注意事项

1. **数据加载函数**：`lodaData` 函数必须返回包含 `list` 和 `total` 字段的对象
2. **列配置**：支持 ElementPlus el-table-column 的所有属性
3. **自定义渲染**：可以使用 `render` 函数或 `slots` 进行自定义渲染
4. **分页处理**：当 `pagination` 为 `true` 时，组件会自动处理分页逻辑
5. **选择功能**：使用 `type: 'selection'` 的列来启用行选择功能
6. **工具栏**：设置 `useTools: true` 可以显示刷新、斑马纹、紧凑度等工具按钮
7. **拖拽排序**：设置 `draggable: true` 可以启用行拖拽排序功能
8. **固定高度**：设置 `fixedHeight: true` 可以让表格自适应容器高度
9. **跨页选择**：设置 `useReserveSelection: true` 可以启用跨页选择功能
