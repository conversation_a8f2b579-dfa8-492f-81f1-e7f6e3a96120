# FuniAuthButton API文档

## 组件概述

FuniAuthButton是基于ElementPlus的el-button封装的权限按钮组件，根据用户权限自动控制按钮的显示和禁用状态，支持多种权限验证方式和权限策略，是权限管理系统的核心组件。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 | ElementPlus对应 |
|--------|------|--------|------|------|----------------|
| permission | String/Array | '' | - | 权限标识 | - |
| permissionMode | String | 'any' | - | 权限模式：'any'、'all' | - |
| hideWhenNoPermission | Boolean | true | - | 无权限时是否隐藏 | - |
| disableWhenNoPermission | Boolean | false | - | 无权限时是否禁用 | - |
| showTooltipWhenDisabled | Boolean | true | - | 禁用时是否显示提示 | - |
| noPermissionTooltip | String | '暂无权限' | - | 无权限提示文本 | - |
| permissionCheck | Function | - | - | 自定义权限检查函数 | - |
| size | String | 'default' | - | 按钮尺寸 | el-button.size |
| type | String | 'default' | - | 按钮类型 | el-button.type |
| plain | Boolean | false | - | 是否朴素按钮 | el-button.plain |
| text | Boolean | false | - | 是否文字按钮 | el-button.text |
| bg | Boolean | false | - | 是否显示文字按钮背景颜色 | el-button.bg |
| link | Boolean | false | - | 是否链接按钮 | el-button.link |
| round | Boolean | false | - | 是否圆角按钮 | el-button.round |
| circle | Boolean | false | - | 是否圆形按钮 | el-button.circle |
| loading | Boolean | false | - | 是否加载中状态 | el-button.loading |
| loadingIcon | String | 'Loading' | - | 加载中图标 | el-button.loading-icon |
| disabled | Boolean | false | - | 是否禁用 | el-button.disabled |
| icon | String | '' | - | 图标 | el-button.icon |
| autofocus | Boolean | false | - | 是否默认聚焦 | el-button.autofocus |
| nativeType | String | 'button' | - | 原生type属性 | el-button.native-type |
| autoInsertSpace | Boolean | - | - | 自动在两个中文字符之间插入空格 | el-button.auto-insert-space |
| color | String | '' | - | 自定义按钮颜色 | el-button.color |
| dark | Boolean | false | - | 是否为暗色模式 | el-button.dark |
| tag | String | 'button' | - | 自定义元素标签 | el-button.tag |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| click | event: Event | 点击事件 | 点击按钮时（有权限） |
| permission-denied | permission: String/Array | 权限拒绝事件 | 点击按钮时（无权限） |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| focus | - | void | 使按钮获得焦点 |
| blur | - | void | 使按钮失去焦点 |
| checkPermission | - | Boolean | 检查当前权限状态 |
| refreshPermission | - | void | 刷新权限状态 |

## Slots

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| default | - | 按钮内容 |
| loading | - | 自定义加载中内容 |

## 权限配置

### 权限模式说明

#### any模式（默认）
当permission为数组时，用户拥有其中任意一个权限即可访问：

```vue
<FuniAuthButton 
  :permission="['user:edit', 'user:delete']" 
  permission-mode="any"
>
  编辑或删除
</FuniAuthButton>
```

#### all模式
当permission为数组时，用户必须拥有所有权限才能访问：

```vue
<FuniAuthButton 
  :permission="['user:edit', 'admin:approve']" 
  permission-mode="all"
>
  编辑并审批
</FuniAuthButton>
```

### 权限检查函数

```typescript
interface PermissionCheckFunction {
  (permission: string | string[], userPermissions: string[]): boolean;
}

// 示例：自定义权限检查
const customPermissionCheck = (permission, userPermissions) => {
  // 自定义权限逻辑
  if (Array.isArray(permission)) {
    return permission.some(p => userPermissions.includes(p));
  }
  return userPermissions.includes(permission);
};
```

## 使用示例

### 基础权限按钮
```vue
<template>
  <div class="auth-buttons">
    <!-- 单个权限 -->
    <FuniAuthButton permission="user:create" type="primary">
      新增用户
    </FuniAuthButton>
    
    <!-- 多个权限（任意一个） -->
    <FuniAuthButton 
      :permission="['user:edit', 'user:update']" 
      permission-mode="any"
      type="warning"
    >
      编辑用户
    </FuniAuthButton>
    
    <!-- 多个权限（全部需要） -->
    <FuniAuthButton 
      :permission="['user:delete', 'admin:approve']" 
      permission-mode="all"
      type="danger"
    >
      删除用户
    </FuniAuthButton>
    
    <!-- 无权限时禁用而不是隐藏 -->
    <FuniAuthButton 
      permission="user:export"
      :hide-when-no-permission="false"
      :disable-when-no-permission="true"
      type="success"
    >
      导出数据
    </FuniAuthButton>
  </div>
</template>

<style scoped>
.auth-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}
</style>
```

### 自定义权限检查
```vue
<template>
  <div>
    <FuniAuthButton 
      permission="special:action"
      :permission-check="customPermissionCheck"
      type="primary"
      @click="handleSpecialAction"
      @permission-denied="handlePermissionDenied"
    >
      特殊操作
    </FuniAuthButton>
  </div>
</template>

<script setup>
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

const customPermissionCheck = (permission, userPermissions) => {
  // 自定义权限逻辑：VIP用户或管理员可以执行特殊操作
  const user = userStore.currentUser
  if (user.isVip || user.role === 'admin') {
    return true
  }
  
  // 普通用户需要特定权限
  return userPermissions.includes(permission)
}

const handleSpecialAction = () => {
  console.log('执行特殊操作')
}

const handlePermissionDenied = (permission) => {
  console.log('权限不足:', permission)
  ElMessage.warning('您没有权限执行此操作')
}
</script>
```

### 表格操作按钮
```vue
<template>
  <el-table :data="tableData">
    <el-table-column prop="name" label="姓名" />
    <el-table-column prop="email" label="邮箱" />
    <el-table-column label="操作" width="200">
      <template #default="{ row }">
        <div class="table-actions">
          <FuniAuthButton 
            permission="user:view"
            type="primary"
            size="small"
            text
            @click="handleView(row)"
          >
            查看
          </FuniAuthButton>
          
          <FuniAuthButton 
            permission="user:edit"
            type="warning"
            size="small"
            text
            @click="handleEdit(row)"
          >
            编辑
          </FuniAuthButton>
          
          <FuniAuthButton 
            permission="user:delete"
            type="danger"
            size="small"
            text
            @click="handleDelete(row)"
          >
            删除
          </FuniAuthButton>
          
          <!-- 条件权限：只有创建者或管理员可以删除 -->
          <FuniAuthButton 
            :permission="getDeletePermission(row)"
            type="danger"
            size="small"
            text
            @click="handleForceDelete(row)"
          >
            强制删除
          </FuniAuthButton>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
import { ref } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

const tableData = ref([
  { id: 1, name: '张三', email: '<EMAIL>', createdBy: 'user1' },
  { id: 2, name: '李四', email: '<EMAIL>', createdBy: 'user2' }
])

const getDeletePermission = (row) => {
  const currentUser = userStore.currentUser
  
  // 如果是创建者或管理员，返回删除权限
  if (row.createdBy === currentUser.id || currentUser.role === 'admin') {
    return 'user:force-delete'
  }
  
  // 否则返回一个不存在的权限，确保按钮隐藏
  return 'user:force-delete:denied'
}

const handleView = (row) => {
  console.log('查看用户:', row)
}

const handleEdit = (row) => {
  console.log('编辑用户:', row)
}

const handleDelete = (row) => {
  console.log('删除用户:', row)
}

const handleForceDelete = (row) => {
  console.log('强制删除用户:', row)
}
</script>

<style scoped>
.table-actions {
  display: flex;
  gap: 8px;
}
</style>
```

### 工具栏权限按钮
```vue
<template>
  <div class="toolbar">
    <div class="toolbar-left">
      <h3>用户管理</h3>
    </div>
    
    <div class="toolbar-right">
      <FuniAuthButton 
        permission="user:create"
        type="primary"
        icon="Plus"
        @click="handleCreate"
      >
        新增用户
      </FuniAuthButton>
      
      <FuniAuthButton 
        permission="user:import"
        type="success"
        icon="Upload"
        @click="handleImport"
      >
        导入用户
      </FuniAuthButton>
      
      <FuniAuthButton 
        permission="user:export"
        type="warning"
        icon="Download"
        :loading="exportLoading"
        @click="handleExport"
      >
        导出用户
      </FuniAuthButton>
      
      <!-- 批量操作按钮 -->
      <el-dropdown @command="handleBatchAction">
        <FuniAuthButton 
          :permission="['user:batch-edit', 'user:batch-delete']"
          permission-mode="any"
          type="info"
        >
          批量操作
          <el-icon class="el-icon--right"><arrow-down /></el-icon>
        </FuniAuthButton>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="batch-edit">
              <FuniAuthButton 
                permission="user:batch-edit"
                text
                size="small"
                style="width: 100%;"
              >
                批量编辑
              </FuniAuthButton>
            </el-dropdown-item>
            <el-dropdown-item command="batch-delete">
              <FuniAuthButton 
                permission="user:batch-delete"
                text
                size="small"
                type="danger"
                style="width: 100%;"
              >
                批量删除
              </FuniAuthButton>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'

const exportLoading = ref(false)

const handleCreate = () => {
  console.log('新增用户')
}

const handleImport = () => {
  console.log('导入用户')
}

const handleExport = async () => {
  exportLoading.value = true
  try {
    // 模拟导出操作
    await new Promise(resolve => setTimeout(resolve, 2000))
    console.log('导出完成')
  } finally {
    exportLoading.value = false
  }
}

const handleBatchAction = (command) => {
  console.log('批量操作:', command)
}
</script>

<style scoped>
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 16px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
  align-items: center;
}
</style>
```

### 表单权限按钮
```vue
<template>
  <el-form :model="form" :rules="rules" ref="formRef">
    <el-form-item label="用户名" prop="username">
      <el-input v-model="form.username" />
    </el-form-item>
    
    <el-form-item label="邮箱" prop="email">
      <el-input v-model="form.email" />
    </el-form-item>
    
    <el-form-item label="角色" prop="role">
      <el-select v-model="form.role">
        <el-option label="管理员" value="admin" />
        <el-option label="普通用户" value="user" />
      </el-select>
    </el-form-item>
    
    <el-form-item>
      <div class="form-actions">
        <FuniAuthButton 
          permission="user:save"
          type="primary"
          :loading="saveLoading"
          @click="handleSave"
        >
          保存
        </FuniAuthButton>
        
        <FuniAuthButton 
          permission="user:save-and-continue"
          type="success"
          :loading="saveLoading"
          @click="handleSaveAndContinue"
        >
          保存并继续
        </FuniAuthButton>
        
        <el-button @click="handleCancel">
          取消
        </el-button>
        
        <!-- 只有管理员可以重置密码 -->
        <FuniAuthButton 
          permission="user:reset-password"
          type="warning"
          @click="handleResetPassword"
        >
          重置密码
        </FuniAuthButton>
      </div>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref, reactive } from 'vue'

const formRef = ref()
const saveLoading = ref(false)

const form = reactive({
  username: '',
  email: '',
  role: ''
})

const rules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  email: [{ required: true, type: 'email', message: '请输入正确的邮箱', trigger: 'blur' }],
  role: [{ required: true, message: '请选择角色', trigger: 'change' }]
}

const handleSave = async () => {
  try {
    await formRef.value.validate()
    saveLoading.value = true
    
    // 模拟保存操作
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    console.log('保存成功')
    ElMessage.success('保存成功')
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    saveLoading.value = false
  }
}

const handleSaveAndContinue = async () => {
  await handleSave()
  // 清空表单继续添加
  formRef.value.resetFields()
}

const handleCancel = () => {
  console.log('取消操作')
}

const handleResetPassword = () => {
  console.log('重置密码')
}
</script>

<style scoped>
.form-actions {
  display: flex;
  gap: 12px;
}
</style>
```

### 动态权限按钮
```vue
<template>
  <div>
    <div class="permission-info">
      <p>当前用户权限：{{ userPermissions.join(', ') }}</p>
      <el-button @click="togglePermission">切换权限</el-button>
    </div>
    
    <div class="dynamic-buttons">
      <FuniAuthButton 
        permission="dynamic:action1"
        type="primary"
        @click="handleAction1"
      >
        动作1
      </FuniAuthButton>
      
      <FuniAuthButton 
        permission="dynamic:action2"
        type="success"
        @click="handleAction2"
      >
        动作2
      </FuniAuthButton>
      
      <FuniAuthButton 
        :permission="['dynamic:action1', 'dynamic:action2']"
        permission-mode="all"
        type="warning"
        @click="handleBothActions"
      >
        需要两个权限
      </FuniAuthButton>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

const userPermissions = ref(['dynamic:action1'])

const togglePermission = () => {
  if (userPermissions.value.includes('dynamic:action2')) {
    userPermissions.value = ['dynamic:action1']
  } else {
    userPermissions.value = ['dynamic:action1', 'dynamic:action2']
  }
  
  // 更新用户权限
  userStore.updatePermissions(userPermissions.value)
}

const handleAction1 = () => {
  console.log('执行动作1')
}

const handleAction2 = () => {
  console.log('执行动作2')
}

const handleBothActions = () => {
  console.log('执行需要两个权限的动作')
}
</script>

<style scoped>
.permission-info {
  padding: 16px;
  background: #f0f9ff;
  border-radius: 4px;
  margin-bottom: 16px;
}

.dynamic-buttons {
  display: flex;
  gap: 12px;
}
</style>
```

## 权限集成

### 与权限系统集成
```javascript
// stores/user.js
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    currentUser: null,
    permissions: []
  }),
  
  getters: {
    hasPermission: (state) => (permission) => {
      if (!permission) return true
      
      if (Array.isArray(permission)) {
        return permission.some(p => state.permissions.includes(p))
      }
      
      return state.permissions.includes(permission)
    },
    
    hasAllPermissions: (state) => (permissions) => {
      if (!Array.isArray(permissions)) return true
      return permissions.every(p => state.permissions.includes(p))
    }
  },
  
  actions: {
    updatePermissions(permissions) {
      this.permissions = permissions
    },
    
    checkPermission(permission, mode = 'any') {
      if (!permission) return true
      
      if (Array.isArray(permission)) {
        return mode === 'all' 
          ? this.hasAllPermissions(permission)
          : this.hasPermission(permission)
      }
      
      return this.hasPermission(permission)
    }
  }
})
```

### 全局权限配置
```javascript
// main.js
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)

// 全局权限检查函数
app.config.globalProperties.$checkPermission = (permission, mode = 'any') => {
  const userStore = useUserStore()
  return userStore.checkPermission(permission, mode)
}

app.mount('#app')
```

## ElementPlus API支持

FuniAuthButton基于el-button封装，支持所有el-button的API：

```vue
<template>
  <FuniAuthButton
    permission="user:edit"
    
    <!-- ElementPlus el-button 所有属性 -->
    size="default"
    type="primary"
    :plain="false"
    :text="false"
    :bg="false"
    :link="false"
    :round="false"
    :circle="false"
    :loading="false"
    loading-icon="Loading"
    :disabled="false"
    icon=""
    :autofocus="false"
    native-type="button"
    :auto-insert-space="true"
    color=""
    :dark="false"
    tag="button"
    
    <!-- ElementPlus el-button 所有事件 -->
    @click="handleClick"
  />
</template>
```

## 注意事项

### 1. 权限设计
- 合理设计权限粒度和层级
- 避免权限标识过于复杂
- 提供清晰的权限文档和说明
- 考虑权限的继承和组合

### 2. 用户体验
- 无权限时提供友好的提示信息
- 合理选择隐藏或禁用策略
- 避免权限检查影响页面性能
- 提供权限申请的入口

### 3. 安全考虑
- 前端权限控制仅用于用户体验
- 后端必须进行权限验证
- 避免在前端暴露敏感权限信息
- 定期审查和更新权限配置

### 4. 维护性
- 统一权限标识的命名规范
- 提供权限管理的可视化界面
- 支持权限的批量操作
- 记录权限变更的审计日志

## 常见问题

### Q: 如何实现角色权限的继承？
A: 在权限检查函数中实现角色权限的继承逻辑

### Q: 如何处理动态权限？
A: 使用响应式的权限状态，当权限变化时自动更新按钮状态

### Q: 如何实现权限的缓存？
A: 在权限store中实现缓存逻辑，避免频繁的权限查询

### Q: 如何调试权限问题？
A: 提供权限检查的日志输出，方便开发和调试
