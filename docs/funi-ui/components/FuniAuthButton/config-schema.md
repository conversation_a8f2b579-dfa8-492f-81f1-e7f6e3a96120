# FuniAuthButton 配置结构

## 基础配置结构

```typescript
interface FuniAuthButtonConfig {
  // 权限配置
  auth?: string | string[];              // 权限标识（v-auth指令值）
  permission?: string | string[];        // 权限标识（新版API）
  permissionMode?: 'any' | 'all';       // 权限模式
  hideWhenNoPermission?: boolean;        // 无权限时是否隐藏
  disableWhenNoPermission?: boolean;     // 无权限时是否禁用
  showTooltipWhenDisabled?: boolean;     // 禁用时是否显示提示
  noPermissionTooltip?: string;          // 无权限提示文本
  permissionCheck?: PermissionCheckFunction; // 自定义权限检查函数
  
  // ElementPlus el-button 配置
  size?: 'large' | 'default' | 'small';
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text' | 'default';
  plain?: boolean;
  text?: boolean;
  bg?: boolean;
  link?: boolean;
  round?: boolean;
  circle?: boolean;
  loading?: boolean;
  loadingIcon?: string | Component;
  disabled?: boolean;
  icon?: string | Component;
  autofocus?: boolean;
  nativeType?: 'button' | 'submit' | 'reset';
  autoInsertSpace?: boolean;
  color?: string;
  dark?: boolean;
  tag?: string | Component;
}

// 权限检查函数类型
interface PermissionCheckFunction {
  (permission: string | string[], userPermissions: string[]): boolean;
}
```

## 权限配置详解

### 1. 权限标识配置
```typescript
// 单个权限
interface SinglePermissionConfig {
  auth: string;                    // 如: 'user:create'
  permission: string;              // 新版API，与auth等效
}

// 多个权限
interface MultiplePermissionConfig {
  auth: string[];                  // 如: ['user:edit', 'user:delete']
  permission: string[];            // 新版API
  permissionMode: 'any' | 'all';   // 权限模式
}

// 示例配置
const singlePermission = {
  auth: 'user:create'
};

const multiplePermissionAny = {
  auth: ['user:edit', 'user:update'],
  permissionMode: 'any'  // 拥有任意一个权限即可
};

const multiplePermissionAll = {
  auth: ['user:delete', 'admin:approve'],
  permissionMode: 'all'  // 必须拥有所有权限
};
```

### 2. 权限行为配置
```typescript
interface PermissionBehaviorConfig {
  hideWhenNoPermission: boolean;        // 无权限时隐藏按钮
  disableWhenNoPermission: boolean;     // 无权限时禁用按钮
  showTooltipWhenDisabled: boolean;     // 禁用时显示提示
  noPermissionTooltip: string;          // 提示文本内容
}

// 配置示例
const hideConfig = {
  hideWhenNoPermission: true,      // 默认行为：隐藏
  disableWhenNoPermission: false
};

const disableConfig = {
  hideWhenNoPermission: false,
  disableWhenNoPermission: true,   // 禁用而不隐藏
  showTooltipWhenDisabled: true,
  noPermissionTooltip: '您没有执行此操作的权限'
};
```

### 3. 自定义权限检查配置
```typescript
interface CustomPermissionConfig {
  permissionCheck: PermissionCheckFunction;
}

// 自定义权限检查函数示例
const customPermissionCheck: PermissionCheckFunction = (permission, userPermissions) => {
  // 超级管理员拥有所有权限
  if (userPermissions.includes('super:admin')) {
    return true;
  }
  
  // 普通权限检查
  if (Array.isArray(permission)) {
    return permission.some(p => userPermissions.includes(p));
  }
  
  return userPermissions.includes(permission);
};

const customConfig = {
  auth: 'special:action',
  permissionCheck: customPermissionCheck
};
```

## 常用配置组合

### 1. 基础权限按钮
```typescript
const basicConfig = {
  auth: 'user:create',
  type: 'primary',
  size: 'default'
};
```

### 2. 多权限按钮（任意一个）
```typescript
const anyPermissionConfig = {
  auth: ['user:edit', 'user:update'],
  permissionMode: 'any',
  type: 'warning',
  hideWhenNoPermission: true
};
```

### 3. 多权限按钮（全部需要）
```typescript
const allPermissionConfig = {
  auth: ['user:delete', 'admin:approve'],
  permissionMode: 'all',
  type: 'danger',
  hideWhenNoPermission: true
};
```

### 4. 禁用模式按钮
```typescript
const disabledModeConfig = {
  auth: 'user:export',
  hideWhenNoPermission: false,
  disableWhenNoPermission: true,
  showTooltipWhenDisabled: true,
  noPermissionTooltip: '您没有导出权限，请联系管理员',
  type: 'success'
};
```

### 5. 自定义权限检查按钮
```typescript
const customCheckConfig = {
  auth: 'vip:feature',
  permissionCheck: (permission, userPermissions) => {
    // VIP用户或管理员可以使用
    const userStore = useUserStore();
    return userStore.isVip || userPermissions.includes('admin:all');
  },
  type: 'primary',
  plain: true
};
```

## 权限系统集成配置

### 1. v-auth指令配置
```typescript
// 当前实现基于v-auth指令
interface VAuthDirectiveConfig {
  // 指令值配置
  value: string | string[];
  
  // 修饰符配置
  modifiers?: {
    menu?: boolean;  // 使用父级菜单权限
  };
}

// 使用示例
const vAuthConfig = {
  // 单权限
  'user:create': true,
  
  // 多权限（逗号分隔）
  'user:edit,user:update': true,
  
  // 菜单权限
  'menu:manage': { modifiers: { menu: true } }
};
```

### 2. 权限存储配置
```typescript
interface PermissionStoreConfig {
  // 当前页面权限
  permissionsInCurrentPage: string[];
  
  // 父级菜单权限
  permissionsOfParentMenu: string[];
  
  // 权限检查方法
  checkPermission: (permission: string | string[], mode?: 'any' | 'all') => boolean;
}

// 权限存储示例
const permissionStore = {
  permissionsInCurrentPage: ['user:create', 'user:edit', 'user:view'],
  permissionsOfParentMenu: ['user:manage', 'system:config'],
  
  checkPermission(permission, mode = 'any') {
    if (Array.isArray(permission)) {
      return mode === 'all' 
        ? permission.every(p => this.permissionsInCurrentPage.includes(p))
        : permission.some(p => this.permissionsInCurrentPage.includes(p));
    }
    return this.permissionsInCurrentPage.includes(permission);
  }
};
```

## 配置验证规则

### 1. 权限标识验证
```typescript
const validatePermission = (permission: string | string[]): boolean => {
  if (!permission) return false;
  
  if (Array.isArray(permission)) {
    return permission.every(p => 
      typeof p === 'string' && 
      p.length > 0 && 
      /^[a-zA-Z0-9:_-]+$/.test(p)
    );
  }
  
  return typeof permission === 'string' && 
         permission.length > 0 && 
         /^[a-zA-Z0-9:_-]+$/.test(permission);
};
```

### 2. 配置完整性验证
```typescript
const validateConfig = (config: FuniAuthButtonConfig): boolean => {
  // 必须有权限配置
  if (!config.auth && !config.permission) {
    console.warn('FuniAuthButton: 缺少权限配置');
    return false;
  }
  
  // 权限模式验证
  if (config.permissionMode && !['any', 'all'].includes(config.permissionMode)) {
    console.warn('FuniAuthButton: 权限模式必须是 "any" 或 "all"');
    return false;
  }
  
  // 行为配置验证
  if (config.hideWhenNoPermission && config.disableWhenNoPermission) {
    console.warn('FuniAuthButton: hideWhenNoPermission 和 disableWhenNoPermission 不能同时为true');
    return false;
  }
  
  return true;
};
```

## 最佳实践配置

### 1. 推荐的权限命名规范
```typescript
const permissionNamingConvention = {
  // 模块:操作 格式
  'user:create',     // 用户创建
  'user:edit',       // 用户编辑
  'user:delete',     // 用户删除
  'user:view',       // 用户查看
  'user:export',     // 用户导出
  
  // 层级权限
  'system:config',   // 系统配置
  'system:log',      // 系统日志
  'admin:all',       // 管理员全权限
  
  // 特殊权限
  'super:admin',     // 超级管理员
  'guest:readonly'   // 访客只读
};
```

### 2. 权限分组配置
```typescript
const permissionGroups = {
  // 用户管理权限组
  userManagement: ['user:create', 'user:edit', 'user:delete', 'user:view'],
  
  // 系统管理权限组
  systemManagement: ['system:config', 'system:log', 'system:backup'],
  
  // 数据权限组
  dataPermissions: ['data:export', 'data:import', 'data:backup'],
  
  // 审核权限组
  auditPermissions: ['audit:approve', 'audit:reject', 'audit:view']
};
```

### 3. 环境配置
```typescript
const environmentConfig = {
  development: {
    // 开发环境：宽松的权限检查
    strictMode: false,
    debugMode: true,
    defaultPermissions: ['dev:all']
  },
  
  production: {
    // 生产环境：严格的权限检查
    strictMode: true,
    debugMode: false,
    defaultPermissions: []
  },
  
  testing: {
    // 测试环境：模拟权限
    strictMode: false,
    debugMode: true,
    mockPermissions: true
  }
};
```

## 配置示例总结

```typescript
// 完整的FuniAuthButton配置示例
const completeConfig: FuniAuthButtonConfig = {
  // 权限配置
  auth: ['user:edit', 'user:update'],
  permissionMode: 'any',
  hideWhenNoPermission: true,
  disableWhenNoPermission: false,
  showTooltipWhenDisabled: true,
  noPermissionTooltip: '您没有编辑用户的权限',
  
  // 按钮样式配置
  type: 'primary',
  size: 'default',
  plain: false,
  round: false,
  circle: false,
  
  // 状态配置
  loading: false,
  disabled: false,
  autofocus: false,
  
  // 其他配置
  icon: 'Edit',
  nativeType: 'button',
  autoInsertSpace: true
};
```

这个配置结构为FuniAuthButton提供了完整的权限控制和按钮样式配置能力，支持灵活的权限验证策略和用户体验优化。
