# FuniAuthButton 最佳实践

## 权限设计最佳实践

### 1. 权限标识命名规范

#### 推荐的命名模式
```typescript
// 模块:操作 格式
const permissionNaming = {
  // 用户管理模块
  'user:create',     // 创建用户
  'user:edit',       // 编辑用户
  'user:delete',     // 删除用户
  'user:view',       // 查看用户
  'user:export',     // 导出用户
  'user:import',     // 导入用户
  
  // 系统管理模块
  'system:config',   // 系统配置
  'system:log',      // 系统日志
  'system:backup',   // 系统备份
  
  // 数据管理模块
  'data:read',       // 数据读取
  'data:write',      // 数据写入
  'data:delete',     // 数据删除
  
  // 特殊权限
  'admin:all',       // 管理员全权限
  'super:admin',     // 超级管理员
  'guest:readonly'   // 访客只读
};
```

#### 避免的命名方式
```typescript
// ❌ 错误的命名方式
const badNaming = [
  'btn1',           // 无意义的命名
  'userCreate',     // 驼峰命名不一致
  'USER_DELETE',    // 全大写不一致
  'user-edit',      // 连字符不一致
  'createUser',     // 动词在前不一致
  'user.view'       // 点号分隔不一致
];

// ✅ 正确的命名方式
const goodNaming = [
  'user:create',    // 模块:操作
  'user:delete',    // 一致的格式
  'user:edit',      // 清晰的含义
  'user:view',      // 标准的动词
  'user:export',    // 具体的操作
  'user:import'     // 对应的操作
];
```

### 2. 权限粒度控制

#### 合理的权限粒度
```vue
<template>
  <div class="user-management">
    <!-- 页面级权限：用户管理页面访问 -->
    <div v-auth="'user:manage'">
      
      <!-- 功能级权限：具体操作按钮 -->
      <div class="toolbar">
        <FuniAuthButton auth="user:create" type="primary">
          新增用户
        </FuniAuthButton>
        
        <FuniAuthButton auth="user:import" type="success">
          批量导入
        </FuniAuthButton>
        
        <FuniAuthButton auth="user:export" type="info">
          导出数据
        </FuniAuthButton>
      </div>
      
      <!-- 行级权限：表格操作 -->
      <el-table :data="users">
        <el-table-column label="操作">
          <template #default="{ row }">
            <FuniAuthButton 
              auth="user:edit" 
              type="warning" 
              text
              @click="editUser(row)"
            >
              编辑
            </FuniAuthButton>
            
            <FuniAuthButton 
              auth="user:delete" 
              type="danger" 
              text
              @click="deleteUser(row)"
            >
              删除
            </FuniAuthButton>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
```

#### 权限层级设计
```typescript
// 权限层级结构
const permissionHierarchy = {
  // 一级：模块权限
  'user': {
    description: '用户管理模块',
    children: {
      // 二级：功能权限
      'user:manage': {
        description: '用户管理页面访问',
        children: {
          // 三级：操作权限
          'user:create': '创建用户',
          'user:edit': '编辑用户',
          'user:delete': '删除用户',
          'user:view': '查看用户详情',
          'user:export': '导出用户数据'
        }
      }
    }
  }
};
```

### 3. 用户体验优化

#### 权限反馈策略
```vue
<template>
  <div class="permission-feedback-examples">
    <!-- 策略1: 隐藏无权限按钮（默认） -->
    <FuniAuthButton auth="admin:panel" type="primary">
      管理面板
    </FuniAuthButton>
    
    <!-- 策略2: 禁用并提示 -->
    <FuniAuthButton 
      auth="data:export"
      :hide-when-no-permission="false"
      :disable-when-no-permission="true"
      :show-tooltip-when-disabled="true"
      no-permission-tooltip="您没有数据导出权限，请联系管理员申请"
      type="success"
    >
      导出数据
    </FuniAuthButton>
    
    <!-- 策略3: 显示申请权限入口 -->
    <FuniAuthButton 
      auth="vip:feature"
      :hide-when-no-permission="false"
      :disable-when-no-permission="true"
      no-permission-tooltip="VIP功能，点击了解如何开通"
      type="warning"
      @permission-denied="showVipUpgrade"
    >
      VIP功能
    </FuniAuthButton>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'

const showVipUpgrade = () => {
  ElMessageBox.confirm(
    '此功能需要VIP权限，是否前往开通？',
    'VIP功能',
    {
      confirmButtonText: '去开通',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    // 跳转到VIP开通页面
    console.log('跳转到VIP开通页面')
  }).catch(() => {
    ElMessage.info('已取消')
  })
}
</script>
```

#### 权限状态可视化
```vue
<template>
  <div class="permission-status-visualization">
    <!-- 权限状态指示器 -->
    <div class="permission-indicator">
      <el-badge 
        :value="availablePermissions.length" 
        :max="99"
        class="permission-badge"
      >
        <el-icon><User /></el-icon>
      </el-badge>
      <span>可用权限: {{ availablePermissions.length }}</span>
    </div>
    
    <!-- 权限分组显示 -->
    <div class="permission-groups">
      <div 
        v-for="group in permissionGroups" 
        :key="group.name"
        class="permission-group"
      >
        <h4>{{ group.label }}</h4>
        <div class="group-buttons">
          <FuniAuthButton 
            v-for="permission in group.permissions"
            :key="permission.code"
            :auth="permission.code"
            :type="permission.type"
            size="small"
            @click="handleAction(permission)"
          >
            {{ permission.label }}
          </FuniAuthButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { usePermissionStore } from '@/stores/permission'

const permissionStore = usePermissionStore()

const permissionGroups = ref([
  {
    name: 'user',
    label: '用户管理',
    permissions: [
      { code: 'user:create', label: '新增', type: 'primary' },
      { code: 'user:edit', label: '编辑', type: 'warning' },
      { code: 'user:delete', label: '删除', type: 'danger' },
      { code: 'user:export', label: '导出', type: 'success' }
    ]
  },
  {
    name: 'system',
    label: '系统管理',
    permissions: [
      { code: 'system:config', label: '配置', type: 'info' },
      { code: 'system:log', label: '日志', type: 'info' },
      { code: 'system:backup', label: '备份', type: 'warning' }
    ]
  }
])

const availablePermissions = computed(() => {
  return permissionStore.userPermissions || []
})

const handleAction = (permission) => {
  console.log('执行操作:', permission.label)
}
</script>

<style scoped>
.permission-status-visualization {
  padding: 20px;
}

.permission-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
}

.permission-groups {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.permission-group h4 {
  margin: 0 0 8px 0;
  color: #606266;
}

.group-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
</style>
```

### 4. 性能优化策略

#### 权限检查缓存
```typescript
// 权限检查缓存实现
class PermissionCache {
  private cache = new Map<string, boolean>()
  private cacheTimeout = 5 * 60 * 1000 // 5分钟缓存

  checkPermission(permission: string | string[]): boolean {
    const key = Array.isArray(permission) ? permission.join(',') : permission
    
    // 检查缓存
    if (this.cache.has(key)) {
      return this.cache.get(key)!
    }
    
    // 执行权限检查
    const hasPermission = this.doPermissionCheck(permission)
    
    // 缓存结果
    this.cache.set(key, hasPermission)
    
    // 设置缓存过期
    setTimeout(() => {
      this.cache.delete(key)
    }, this.cacheTimeout)
    
    return hasPermission
  }
  
  private doPermissionCheck(permission: string | string[]): boolean {
    // 实际的权限检查逻辑
    const userPermissions = usePermissionStore().userPermissions
    
    if (Array.isArray(permission)) {
      return permission.some(p => userPermissions.includes(p))
    }
    
    return userPermissions.includes(permission)
  }
  
  clearCache(): void {
    this.cache.clear()
  }
}

// 全局权限缓存实例
export const permissionCache = new PermissionCache()
```

#### 批量权限检查
```vue
<template>
  <div class="batch-permission-check">
    <!-- 批量权限检查，避免重复计算 -->
    <div v-if="userPermissions.canManageUsers">
      <FuniAuthButton auth="user:create" type="primary">
        新增用户
      </FuniAuthButton>
      
      <FuniAuthButton auth="user:edit" type="warning">
        编辑用户
      </FuniAuthButton>
      
      <FuniAuthButton auth="user:delete" type="danger">
        删除用户
      </FuniAuthButton>
    </div>
    
    <div v-if="userPermissions.canManageSystem">
      <FuniAuthButton auth="system:config" type="info">
        系统配置
      </FuniAuthButton>
      
      <FuniAuthButton auth="system:backup" type="warning">
        系统备份
      </FuniAuthButton>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { usePermissionStore } from '@/stores/permission'

const permissionStore = usePermissionStore()

// 批量权限检查，减少重复计算
const userPermissions = computed(() => {
  const permissions = permissionStore.userPermissions
  
  return {
    canManageUsers: permissions.some(p => 
      ['user:create', 'user:edit', 'user:delete'].includes(p)
    ),
    canManageSystem: permissions.some(p => 
      ['system:config', 'system:backup', 'admin:all'].includes(p)
    ),
    isAdmin: permissions.includes('admin:all'),
    isSuperAdmin: permissions.includes('super:admin')
  }
})
</script>
```

### 5. 安全性最佳实践

#### 前后端权限一致性
```typescript
// 前端权限检查
const frontendPermissionCheck = (permission: string): boolean => {
  const userPermissions = usePermissionStore().userPermissions
  return userPermissions.includes(permission)
}

// API请求权限验证
const apiRequest = async (url: string, options: RequestOptions) => {
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        'Authorization': `Bearer ${getToken()}`,
        'X-Requested-With': 'XMLHttpRequest'
      }
    })
    
    if (response.status === 403) {
      // 权限不足，同步更新前端权限状态
      await permissionStore.refreshPermissions()
      throw new Error('权限不足')
    }
    
    return response
  } catch (error) {
    console.error('API请求失败:', error)
    throw error
  }
}
```

#### 敏感操作二次确认
```vue
<template>
  <div class="sensitive-operations">
    <!-- 敏感操作需要二次确认 -->
    <FuniAuthButton 
      auth="user:delete"
      type="danger"
      @click="handleSensitiveDelete"
    >
      删除用户
    </FuniAuthButton>
    
    <FuniAuthButton 
      auth="system:reset"
      type="danger"
      @click="handleSystemReset"
    >
      系统重置
    </FuniAuthButton>
  </div>
</template>

<script setup>
import { ElMessageBox, ElMessage } from 'element-plus'

const handleSensitiveDelete = async () => {
  try {
    await ElMessageBox.confirm(
      '此操作将永久删除用户数据，无法恢复。确认继续？',
      '危险操作确认',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'error',
        confirmButtonClass: 'el-button--danger'
      }
    )
    
    // 执行删除操作
    console.log('执行删除操作')
    ElMessage.success('删除成功')
  } catch {
    ElMessage.info('已取消删除')
  }
}

const handleSystemReset = async () => {
  try {
    // 多重确认
    await ElMessageBox.confirm(
      '系统重置将清除所有数据，此操作不可逆！',
      '系统重置警告',
      {
        confirmButtonText: '我了解风险，继续',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    // 要求输入确认文本
    const { value } = await ElMessageBox.prompt(
      '请输入 "RESET" 确认系统重置',
      '最终确认',
      {
        confirmButtonText: '确认重置',
        cancelButtonText: '取消',
        inputPattern: /^RESET$/,
        inputErrorMessage: '请输入正确的确认文本'
      }
    )
    
    if (value === 'RESET') {
      console.log('执行系统重置')
      ElMessage.success('系统重置成功')
    }
  } catch {
    ElMessage.info('已取消系统重置')
  }
}
</script>
```

### 6. 错误处理和降级

#### 权限加载失败处理
```vue
<template>
  <div class="permission-error-handling">
    <!-- 权限加载中状态 -->
    <div v-if="permissionLoading" class="permission-loading">
      <el-skeleton :rows="3" animated />
      <p>权限加载中...</p>
    </div>
    
    <!-- 权限加载失败状态 -->
    <div v-else-if="permissionError" class="permission-error">
      <el-alert
        title="权限加载失败"
        type="error"
        :description="permissionError"
        show-icon
      />
      <el-button @click="retryLoadPermissions" type="primary">
        重新加载
      </el-button>
    </div>
    
    <!-- 正常权限控制 -->
    <div v-else class="normal-permissions">
      <FuniAuthButton auth="user:create" type="primary">
        新增用户
      </FuniAuthButton>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { usePermissionStore } from '@/stores/permission'

const permissionStore = usePermissionStore()
const permissionLoading = ref(true)
const permissionError = ref('')

const loadPermissions = async () => {
  try {
    permissionLoading.value = true
    permissionError.value = ''
    
    await permissionStore.loadUserPermissions()
  } catch (error) {
    permissionError.value = error.message || '权限加载失败'
    console.error('权限加载失败:', error)
  } finally {
    permissionLoading.value = false
  }
}

const retryLoadPermissions = () => {
  loadPermissions()
}

onMounted(() => {
  loadPermissions()
})
</script>

<style scoped>
.permission-loading,
.permission-error {
  padding: 20px;
  text-align: center;
}

.permission-error .el-button {
  margin-top: 16px;
}
</style>
```

## 避免的常见错误

### 1. 权限设计错误
```typescript
// ❌ 错误：权限过于宽泛
const badPermissions = [
  'admin',          // 过于宽泛
  'all',            // 没有具体含义
  'everything'      // 安全风险
];

// ✅ 正确：具体的权限设计
const goodPermissions = [
  'user:create',    // 具体操作
  'user:edit',      // 明确范围
  'admin:user'      // 明确模块
];
```

### 2. 性能问题
```vue
<!-- ❌ 错误：在循环中重复权限检查 -->
<template>
  <div v-for="item in largeList" :key="item.id">
    <FuniAuthButton auth="item:edit" @click="edit(item)">
      编辑
    </FuniAuthButton>
  </div>
</template>

<!-- ✅ 正确：预先检查权限 -->
<template>
  <div v-for="item in largeList" :key="item.id">
    <el-button 
      v-if="canEdit" 
      @click="edit(item)"
    >
      编辑
    </el-button>
  </div>
</template>

<script setup>
const canEdit = computed(() => 
  permissionStore.hasPermission('item:edit')
)
</script>
```

### 3. 用户体验问题
```vue
<!-- ❌ 错误：无提示的权限限制 -->
<FuniAuthButton auth="admin:panel">
  管理面板
</FuniAuthButton>

<!-- ✅ 正确：提供权限提示 -->
<FuniAuthButton 
  auth="admin:panel"
  :hide-when-no-permission="false"
  :disable-when-no-permission="true"
  no-permission-tooltip="需要管理员权限才能访问"
>
  管理面板
</FuniAuthButton>
```

## 总结

FuniAuthButton的最佳实践包括：

1. **权限设计** - 清晰的命名规范，合理的权限粒度
2. **用户体验** - 友好的权限反馈，直观的状态显示
3. **性能优化** - 权限缓存，批量检查，避免重复计算
4. **安全性** - 前后端一致性，敏感操作确认
5. **错误处理** - 优雅降级，重试机制
6. **可维护性** - 统一的权限管理，清晰的代码结构

遵循这些最佳实践可以确保权限系统的安全性、性能和用户体验。
