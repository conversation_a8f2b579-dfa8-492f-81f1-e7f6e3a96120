# FuniAuthButton 使用示例

## 基础权限控制示例

### 单个权限控制
```vue
<template>
  <div class="basic-auth-examples">
    <!-- 基础权限按钮 -->
    <FuniAuthButton auth="user:create" type="primary">
      新增用户
    </FuniAuthButton>
    
    <!-- 带图标的权限按钮 -->
    <FuniAuthButton auth="user:edit" type="warning" icon="Edit">
      编辑用户
    </FuniAuthButton>
    
    <!-- 危险操作权限按钮 -->
    <FuniAuthButton auth="user:delete" type="danger" icon="Delete">
      删除用户
    </FuniAuthButton>
    
    <!-- 文字按钮权限控制 -->
    <FuniAuthButton auth="user:view" type="primary" text>
      查看详情
    </FuniAuthButton>
  </div>
</template>

<style scoped>
.basic-auth-examples {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}
</style>
```

### 多权限控制（任意一个）
```vue
<template>
  <div class="multiple-auth-any">
    <!-- 编辑或更新权限（满足任意一个即可） -->
    <FuniAuthButton 
      :auth="['user:edit', 'user:update']" 
      type="warning"
      icon="Edit"
    >
      编辑用户
    </FuniAuthButton>
    
    <!-- 查看或管理权限 -->
    <FuniAuthButton 
      :auth="['user:view', 'user:manage']" 
      type="info"
      plain
    >
      用户管理
    </FuniAuthButton>
    
    <!-- 导出或下载权限 -->
    <FuniAuthButton 
      :auth="['data:export', 'data:download']" 
      type="success"
      icon="Download"
    >
      导出数据
    </FuniAuthButton>
  </div>
</template>
```

### 多权限控制（全部需要）
```vue
<template>
  <div class="multiple-auth-all">
    <!-- 需要删除和审批两个权限 -->
    <FuniAuthButton 
      :auth="['user:delete', 'admin:approve']" 
      permission-mode="all"
      type="danger"
      icon="Delete"
    >
      强制删除
    </FuniAuthButton>
    
    <!-- 需要系统配置和高级管理权限 -->
    <FuniAuthButton 
      :auth="['system:config', 'admin:advanced']" 
      permission-mode="all"
      type="warning"
      icon="Setting"
    >
      系统配置
    </FuniAuthButton>
  </div>
</template>
```

## 权限行为控制示例

### 隐藏模式（默认）
```vue
<template>
  <div class="hide-mode-examples">
    <h4>隐藏模式示例（无权限时按钮不显示）</h4>
    
    <!-- 默认隐藏模式 -->
    <FuniAuthButton auth="admin:panel" type="primary">
      管理面板
    </FuniAuthButton>
    
    <!-- 明确指定隐藏模式 -->
    <FuniAuthButton 
      auth="super:admin" 
      :hide-when-no-permission="true"
      type="danger"
    >
      超级管理
    </FuniAuthButton>
  </div>
</template>
```

### 禁用模式
```vue
<template>
  <div class="disable-mode-examples">
    <h4>禁用模式示例（无权限时按钮禁用但可见）</h4>
    
    <!-- 禁用模式，显示提示 -->
    <FuniAuthButton 
      auth="data:export" 
      :hide-when-no-permission="false"
      :disable-when-no-permission="true"
      :show-tooltip-when-disabled="true"
      no-permission-tooltip="您没有数据导出权限，请联系管理员"
      type="success"
      icon="Download"
    >
      导出数据
    </FuniAuthButton>
    
    <!-- 禁用模式，自定义提示 -->
    <FuniAuthButton 
      auth="system:backup" 
      :hide-when-no-permission="false"
      :disable-when-no-permission="true"
      no-permission-tooltip="系统备份需要高级权限"
      type="warning"
    >
      系统备份
    </FuniAuthButton>
  </div>
</template>
```

## 自定义权限检查示例

### 基于用户角色的权限检查
```vue
<template>
  <div class="custom-permission-examples">
    <!-- VIP用户或管理员可用 -->
    <FuniAuthButton 
      auth="vip:feature"
      :permission-check="vipOrAdminCheck"
      type="primary"
      icon="Crown"
    >
      VIP功能
    </FuniAuthButton>
    
    <!-- 工作时间限制 -->
    <FuniAuthButton 
      auth="time:sensitive"
      :permission-check="workingHoursCheck"
      type="warning"
    >
      时间敏感操作
    </FuniAuthButton>
    
    <!-- 复合条件检查 -->
    <FuniAuthButton 
      auth="complex:operation"
      :permission-check="complexPermissionCheck"
      type="danger"
    >
      复合权限操作
    </FuniAuthButton>
  </div>
</template>

<script setup>
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// VIP用户或管理员权限检查
const vipOrAdminCheck = (permission, userPermissions) => {
  const user = userStore.currentUser
  
  // VIP用户直接通过
  if (user.isVip) return true
  
  // 管理员直接通过
  if (user.role === 'admin') return true
  
  // 普通用户检查具体权限
  return userPermissions.includes(permission)
}

// 工作时间权限检查
const workingHoursCheck = (permission, userPermissions) => {
  const now = new Date()
  const hour = now.getHours()
  
  // 工作时间：9:00-18:00
  const isWorkingHours = hour >= 9 && hour <= 18
  
  if (!isWorkingHours) {
    return false
  }
  
  return userPermissions.includes(permission)
}

// 复合权限检查
const complexPermissionCheck = (permission, userPermissions) => {
  const user = userStore.currentUser
  
  // 必须是正式员工
  if (user.status !== 'active') return false
  
  // 必须有基础权限
  if (!userPermissions.includes(permission)) return false
  
  // 必须有部门主管权限或以上
  return user.level >= 3
}
</script>
```

## 事件处理示例

### 权限验证事件
```vue
<template>
  <div class="event-examples">
    <!-- 带权限验证的点击事件 -->
    <FuniAuthButton 
      auth="user:create"
      type="primary"
      @click="handleCreateUser"
      @permission-denied="handlePermissionDenied"
    >
      新增用户
    </FuniAuthButton>
    
    <!-- 复杂业务逻辑 -->
    <FuniAuthButton 
      auth="data:sensitive"
      type="danger"
      @click="handleSensitiveOperation"
      @permission-denied="handleSensitivePermissionDenied"
    >
      敏感操作
    </FuniAuthButton>
  </div>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus'

const handleCreateUser = () => {
  console.log('执行创建用户操作')
  ElMessage.success('开始创建用户')
}

const handlePermissionDenied = (permission) => {
  console.log('权限被拒绝:', permission)
  ElMessage.warning('您没有执行此操作的权限')
}

const handleSensitiveOperation = async () => {
  try {
    await ElMessageBox.confirm(
      '此操作不可逆，是否确认执行？',
      '敏感操作确认',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    console.log('执行敏感操作')
    ElMessage.success('敏感操作执行成功')
  } catch {
    ElMessage.info('已取消操作')
  }
}

const handleSensitivePermissionDenied = (permission) => {
  ElMessage.error('您没有执行敏感操作的权限，请联系系统管理员')
  
  // 记录权限拒绝日志
  console.warn('敏感操作权限被拒绝:', {
    permission,
    user: userStore.currentUser.id,
    timestamp: new Date().toISOString()
  })
}
</script>
```

## 动态权限示例

### 权限状态动态变化
```vue
<template>
  <div class="dynamic-permission-examples">
    <div class="permission-status">
      <h4>当前用户权限状态</h4>
      <p>权限列表: {{ userPermissions.join(', ') }}</p>
      <el-button @click="togglePermission('user:edit')">
        切换编辑权限
      </el-button>
      <el-button @click="togglePermission('admin:all')">
        切换管理员权限
      </el-button>
    </div>
    
    <div class="dynamic-buttons">
      <!-- 动态权限按钮 -->
      <FuniAuthButton 
        auth="user:edit"
        type="warning"
        @click="handleEdit"
      >
        编辑用户
      </FuniAuthButton>
      
      <FuniAuthButton 
        auth="admin:all"
        type="danger"
        @click="handleAdminAction"
      >
        管理员操作
      </FuniAuthButton>
      
      <!-- 组合权限动态检查 -->
      <FuniAuthButton 
        :auth="['user:edit', 'admin:all']"
        permission-mode="any"
        type="primary"
      >
        编辑或管理
      </FuniAuthButton>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { usePermissionStore } from '@/stores/permission'

const permissionStore = usePermissionStore()

const userPermissions = ref(['user:view', 'user:create'])

const togglePermission = (permission) => {
  const index = userPermissions.value.indexOf(permission)
  if (index > -1) {
    userPermissions.value.splice(index, 1)
  } else {
    userPermissions.value.push(permission)
  }
  
  // 更新权限存储
  permissionStore.updatePermissions(userPermissions.value)
}

const handleEdit = () => {
  console.log('执行编辑操作')
}

const handleAdminAction = () => {
  console.log('执行管理员操作')
}
</script>

<style scoped>
.dynamic-permission-examples {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.permission-status {
  padding: 16px;
  background: #f5f5f5;
  border-radius: 4px;
}

.dynamic-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}
</style>
```

## 表单中的权限控制示例

### 表单操作权限
```vue
<template>
  <div class="form-permission-examples">
    <el-form :model="formData" label-width="100px">
      <el-form-item label="用户名">
        <el-input v-model="formData.username" />
      </el-form-item>
      
      <el-form-item label="邮箱">
        <el-input v-model="formData.email" />
      </el-form-item>
      
      <el-form-item label="角色">
        <el-select v-model="formData.role">
          <el-option label="普通用户" value="user" />
          <el-option label="管理员" value="admin" />
        </el-select>
      </el-form-item>
      
      <el-form-item>
        <div class="form-actions">
          <!-- 保存权限 -->
          <FuniAuthButton 
            auth="user:save"
            type="primary"
            @click="handleSave"
          >
            保存
          </FuniAuthButton>
          
          <!-- 提交审核权限 -->
          <FuniAuthButton 
            auth="user:submit"
            type="success"
            @click="handleSubmit"
          >
            提交审核
          </FuniAuthButton>
          
          <!-- 直接发布权限（需要高级权限） -->
          <FuniAuthButton 
            :auth="['user:publish', 'admin:approve']"
            permission-mode="any"
            type="warning"
            @click="handlePublish"
          >
            直接发布
          </FuniAuthButton>
          
          <!-- 删除权限 -->
          <FuniAuthButton 
            auth="user:delete"
            type="danger"
            @click="handleDelete"
          >
            删除
          </FuniAuthButton>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const formData = ref({
  username: '',
  email: '',
  role: 'user'
})

const handleSave = () => {
  ElMessage.success('保存成功')
}

const handleSubmit = () => {
  ElMessage.success('提交审核成功')
}

const handlePublish = () => {
  ElMessage.success('发布成功')
}

const handleDelete = () => {
  ElMessage.success('删除成功')
}
</script>

<style scoped>
.form-actions {
  display: flex;
  gap: 12px;
}
</style>
```

## 列表操作权限示例

### 表格行操作权限
```vue
<template>
  <div class="table-permission-examples">
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="name" label="姓名" />
      <el-table-column prop="email" label="邮箱" />
      <el-table-column prop="role" label="角色" />
      <el-table-column label="操作" width="300">
        <template #default="{ row }">
          <div class="table-actions">
            <!-- 查看权限 -->
            <FuniAuthButton 
              auth="user:view"
              type="primary"
              text
              @click="handleView(row)"
            >
              查看
            </FuniAuthButton>
            
            <!-- 编辑权限 -->
            <FuniAuthButton 
              auth="user:edit"
              type="warning"
              text
              @click="handleEdit(row)"
            >
              编辑
            </FuniAuthButton>
            
            <!-- 删除权限（禁用模式） -->
            <FuniAuthButton 
              auth="user:delete"
              :hide-when-no-permission="false"
              :disable-when-no-permission="true"
              no-permission-tooltip="您没有删除权限"
              type="danger"
              text
              @click="handleDelete(row)"
            >
              删除
            </FuniAuthButton>
            
            <!-- 重置密码权限 -->
            <FuniAuthButton 
              :auth="['user:reset', 'admin:all']"
              permission-mode="any"
              type="info"
              text
              @click="handleResetPassword(row)"
            >
              重置密码
            </FuniAuthButton>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const tableData = ref([
  { id: 1, name: '张三', email: '<EMAIL>', role: 'user' },
  { id: 2, name: '李四', email: '<EMAIL>', role: 'admin' },
  { id: 3, name: '王五', email: '<EMAIL>', role: 'user' }
])

const handleView = (row) => {
  console.log('查看用户:', row)
}

const handleEdit = (row) => {
  console.log('编辑用户:', row)
}

const handleDelete = (row) => {
  console.log('删除用户:', row)
}

const handleResetPassword = (row) => {
  console.log('重置密码:', row)
}
</script>

<style scoped>
.table-actions {
  display: flex;
  gap: 8px;
}
</style>
```

这些示例展示了FuniAuthButton在各种场景下的使用方法，包括基础权限控制、多权限组合、自定义权限检查、事件处理、动态权限变化以及在表单和表格中的应用。
