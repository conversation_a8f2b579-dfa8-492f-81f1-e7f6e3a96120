# FuniDetail 配置结构

## 基础配置结构

```typescript
interface FuniDetailConfig {
  // 基础属性
  detailHeadOption: DetailHeadOption;
  steps: StepConfig[];
  bizName: '新建' | 'add' | '编辑' | 'edit' | '详情' | 'detail' | '审核' | 'audit';
  current?: number;
  showHead?: boolean;
  
  // 路由配置
  homeRouter?: RouterConfig;
  parentRouter?: RouterConfig;
  
  // 工作流配置
  businessId?: string;
  sysId?: string;
  showWorkflow?: boolean;
  auditButtons?: AuditButtonConfig[];
  
  // 功能配置
  isAuthFixedBtn?: boolean;
  isFormOpinion?: boolean;
  beforeLeave?: (activeName: string, oldActiveName: string) => boolean | Promise<boolean>;
  
  // 按钮配置
  tsKey?: string;
  tsType?: string;
}
```

## 详情头部配置结构

```typescript
interface DetailHeadOption {
  // 标题信息
  title?: string;                  // 页面主标题
  
  // 编号信息
  serialName?: string;             // 编号字段显示名称，默认'业务编号'
  no?: string;                     // 编号值
  
  // 状态信息
  statusName?: string;             // 状态字段显示名称，默认'状态'
  status?: string;                 // 状态值
  hideStatusBar?: boolean;         // 是否隐藏整个状态栏
  hideStatusName?: boolean;        // 是否隐藏状态名称
  
  // 链接配置
  links?: LinkConfig[];
  
  // 头部按钮配置
  btns?: HeaderButtonConfig[];
}

interface LinkConfig {
  title: string;                   // 链接前缀文本
  name: string;                    // 链接显示文本
  props?: {
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info';
    link?: boolean;                // 是否为链接样式
    // ... 更多el-button属性
  };
  on?: {
    click?: () => void;            // 点击事件处理
  };
}

interface HeaderButtonConfig {
  name: string;                    // 按钮显示文本
  type?: string;                   // 按钮类型，对应子组件方法名
  triggerEvent?: boolean;          // 是否触发clickBtnEvent事件
  props?: {
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info';
    size?: 'large' | 'default' | 'small';
    icon?: string;
    disabled?: boolean;
    loading?: boolean;
    // ... 更多el-button属性
  };
  on?: {
    click?: () => void;            // 点击事件处理
  };
}
```

## 步骤配置结构

```typescript
interface StepConfig {
  // 基础信息
  title: string;                   // 步骤标题
  icon?: string;                   // 步骤图标（可选）
  width?: number;                  // 步骤宽度，默认200px
  
  // 组件配置（二选一）
  type?: Component;                // Vue组件（推荐使用shallowRef包装）
  slot?: string;                   // 插槽名称
  
  // 组件属性
  props?: Record<string, any>;     // 传递给组件的props
  on?: Record<string, Function>;   // 组件事件处理器
  
  // 业务配置
  preservable?: boolean;           // 是否可保存（影响按钮显示逻辑）
}
```

## 路由配置结构

```typescript
interface RouterConfig {
  path: string;                    // 路由路径
  title?: string;                  // 路由标题（用于面包屑）
  query?: Record<string, any>;     // 路由查询参数
  params?: Record<string, any>;    // 路由路径参数
}
```

## 审核按钮配置结构

```typescript
interface AuditButtonConfig {
  key: string;                     // 按钮显示文本
  action: string;                  // 审核动作标识
  props?: {
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info';
    size?: 'large' | 'default' | 'small';
    disabled?: boolean;
    // ... 更多el-button属性
  };
}

// 默认审核按钮配置
const defaultAuditButtons: AuditButtonConfig[] = [
  { key: '回退', action: 'BACK' },
  { key: '退件', action: 'RETURN' },
  { key: '不予受理', action: 'NOT_ACCEPT' },
  { key: '同意', action: 'COMMIT' }
];
```

## 工作流配置结构

```typescript
interface WorkflowConfig {
  businessId: string;              // 工作流业务实例ID
  sysId: string;                   // 系统ID
  showWorkflow?: boolean;          // 是否显示工作流组件
  isAuthFixedBtn?: boolean;        // 是否启用权限固定按钮
  isFormOpinion?: boolean;         // 是否在表单中提供审核意见
}
```

## 常用配置组合

### 新建页面配置
```typescript
const createPageConfig: FuniDetailConfig = {
  bizName: '新建',
  showHead: false,                 // 新建页面通常不显示头部
  detailHeadOption: {
    title: '新建用户',
    btns: [
      {
        name: '保存',
        type: 'save',
        props: { type: 'primary' }
      },
      {
        name: '保存并继续',
        type: 'saveAndContinue',
        props: { type: 'success' }
      }
    ]
  },
  steps: [
    {
      title: '基本信息',
      type: shallowRef(BasicInfoForm),
      props: { editable: true }
    },
    {
      title: '详细信息',
      type: shallowRef(DetailInfoForm),
      props: { editable: true }
    }
  ]
};
```

### 编辑页面配置
```typescript
const editPageConfig: FuniDetailConfig = {
  bizName: '编辑',
  detailHeadOption: {
    title: '编辑用户',
    serialName: '用户ID',
    no: 'U001',
    status: '正常',
    btns: [
      {
        name: '保存',
        type: 'save',
        props: { type: 'primary' }
      },
      {
        name: '重置',
        type: 'reset',
        props: { type: 'warning' }
      }
    ]
  },
  steps: [
    {
      title: '基本信息',
      type: shallowRef(BasicInfoForm),
      props: { editable: true, data: userData }
    }
  ]
};
```

### 详情页面配置
```typescript
const detailPageConfig: FuniDetailConfig = {
  bizName: '详情',
  detailHeadOption: {
    title: '用户详情',
    serialName: '用户ID',
    no: 'U001',
    status: '正常',
    links: [
      {
        title: '关联订单:',
        name: 'O202301001',
        props: { type: 'primary', link: true },
        on: { click: () => router.push('/order/O202301001') }
      }
    ],
    btns: [
      {
        name: '编辑',
        triggerEvent: true,
        props: { type: 'primary' },
        on: { click: () => router.push('/user/edit/U001') }
      }
    ]
  },
  steps: [
    {
      title: '基本信息',
      type: shallowRef(BasicInfoView),
      props: { data: userData }
    },
    {
      title: '操作记录',
      type: shallowRef(OperationLog),
      props: { userId: 'U001' }
    }
  ]
};
```

### 审核页面配置
```typescript
const auditPageConfig: FuniDetailConfig = {
  bizName: '审核',
  businessId: 'BIZ202301001',
  sysId: 'SYS001',
  showWorkflow: true,
  detailHeadOption: {
    title: '用户审核',
    serialName: '申请编号',
    no: 'APP202301001',
    status: '待审核'
  },
  auditButtons: [
    { key: '同意', action: 'APPROVE' },
    { key: '拒绝', action: 'REJECT' },
    { key: '退回', action: 'RETURN' }
  ],
  steps: [
    {
      title: '申请信息',
      type: shallowRef(ApplicationView),
      props: { data: applicationData, readonly: true }
    },
    {
      title: '附件材料',
      slot: 'attachments'  // 使用插槽，FuniFileTable会自动渲染
    },
    {
      title: '审核记录',
      slot: 'auditHistory' // 审核历史会自动渲染
    }
  ]
};
```

### 工作流新建页面配置
```typescript
const workflowCreateConfig: FuniDetailConfig = {
  bizName: '新建',
  showWorkflow: true,
  sysId: 'SYS001',
  detailHeadOption: {
    title: '新建申请',
    btns: [
      {
        name: '暂存',
        type: 'tempSave',
        props: { type: 'default' }
      },
      {
        name: '提交',
        type: 'submit',
        props: { type: 'primary' }
      }
    ]
  },
  steps: [
    {
      title: '基本信息',
      type: shallowRef(ApplicationForm),
      props: { editable: true },
      preservable: true  // 第一步可保存，保存后获得businessId
    },
    {
      title: '附件上传',
      slot: 'attachments', // FuniFileTable需要businessId才能渲染
      preservable: false
    }
  ]
};
```

## 子组件接口规范

### 子组件必须暴露的方法
```typescript
interface StepComponent {
  // 对应按钮type的方法
  save?: () => Promise<any>;
  submit?: () => Promise<any>;
  reset?: () => Promise<any>;
  tempSave?: () => Promise<any>;
  
  // 步骤控制方法
  lastStep?: (params: StepParams) => Promise<any>;
  nextStep?: (params: StepParams) => Promise<any>;
  
  // 其他自定义方法...
}

interface StepParams {
  type: string;                    // 按钮类型
  current: number;                 // 当前步骤索引
}
```

### 子组件数据注入
```typescript
// 父组件提供的数据
interface ProvidedData {
  tempData: Reactive<any>;         // 临时数据存储
  updateTempData: (data: any) => void; // 更新临时数据
  loadingStatus: Ref<{             // 加载状态
    type?: string;
    status: boolean;
  }>;
}

// 子组件中使用
const tempData = inject('tempData');
const updateTempData = inject('updateTempData');
const loadingStatus = inject('loadingStatus');
```

## 最佳实践建议

### 1. 步骤设计原则
- 每个步骤应该是独立的业务单元
- 步骤之间通过tempData共享数据
- 复杂步骤可以进一步拆分子组件

### 2. 按钮配置原则
- 按钮type应该与子组件方法名一致
- 使用triggerEvent处理不需要子组件参与的操作
- 合理使用props配置按钮样式和状态

### 3. 工作流集成原则
- 工作流场景必须提供businessId和sysId
- 第一步保存后应该获得businessId
- 附件步骤应该在获得businessId后显示

### 4. 数据管理原则
- 使用tempData在步骤间共享数据
- 子组件负责自己的数据验证
- 父组件负责整体流程控制
