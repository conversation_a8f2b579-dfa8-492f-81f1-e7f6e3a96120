# FuniDetail ElementPlus API支持

## 基础组件

FuniDetail基于ElementPlus的以下组件进行封装：
- **el-form** - 主要表单容器
- **el-form-item** - 表单项容器
- **el-row/el-col** - 栅格布局系统
- **el-card** - 卡片容器
- **el-tabs** - 标签页容器
- **el-steps** - 步骤条组件
- **el-button** - 操作按钮

## 支持的ElementPlus API

### el-form API透传

| API名称 | 类型 | 说明 | 透传方式 |
|---------|------|------|---------|
| model | Object | 表单数据对象 | 自动处理 |
| rules | Object | 表单验证规则 | v-bind透传 |
| inline | Boolean | 行内表单模式 | v-bind透传 |
| label-position | String | 标签位置 | v-bind透传 |
| label-width | String | 标签宽度 | v-bind透传 |
| label-suffix | String | 标签后缀 | v-bind透传 |
| hide-required-asterisk | Boolean | 隐藏必填星号 | v-bind透传 |
| show-message | Boolean | 显示校验信息 | v-bind透传 |
| inline-message | Boolean | 行内显示校验信息 | v-bind透传 |
| status-icon | Boolean | 显示校验状态图标 | v-bind透传 |
| validate-on-rule-change | Boolean | 规则改变时触发校验 | v-bind透传 |
| size | String | 组件尺寸 | v-bind透传 |
| disabled | Boolean | 是否禁用 | v-bind透传 |
| scroll-to-error | Boolean | 校验失败时滚动到错误字段 | v-bind透传 |
| scroll-into-view-options | Object/Boolean | 滚动行为配置 | v-bind透传 |

### el-form Events透传

| 事件名 | 参数 | 说明 | 透传方式 |
|--------|------|------|---------|
| validate | prop, isValid, message | 表单项校验事件 | @validate |

### el-form Methods透传

| 方法名 | 参数 | 返回值 | 说明 | 透传方式 |
|--------|------|--------|------|---------|
| validate | callback | Promise | 验证整个表单 | ref调用 |
| validateField | props, callback | Promise | 验证指定字段 | ref调用 |
| resetFields | props | void | 重置表单字段 | ref调用 |
| scrollToField | prop | void | 滚动到指定字段 | ref调用 |
| clearValidate | props | void | 清除验证信息 | ref调用 |

### el-form-item API透传

| API名称 | 类型 | 说明 | 透传方式 |
|---------|------|------|---------|
| prop | String | 字段名 | 通过schema配置 |
| label | String | 标签文本 | 通过schema配置 |
| label-width | String | 标签宽度 | 通过schema配置 |
| required | Boolean | 是否必填 | 通过schema配置 |
| rules | Object/Array | 验证规则 | 通过schema配置 |
| error | String | 表单域验证错误信息 | 通过schema配置 |
| show-message | Boolean | 显示校验错误信息 | 通过schema配置 |
| inline-message | Boolean | 行内显示校验信息 | 通过schema配置 |
| size | String | 组件尺寸 | 通过schema配置 |

### el-row/el-col API透传

| API名称 | 类型 | 说明 | 透传方式 |
|---------|------|------|---------|
| gutter | Number | 栅格间隔 | 通过layout配置 |
| type | String | 布局模式 | 通过layout配置 |
| justify | String | 水平排列方式 | 通过layout配置 |
| align | String | 垂直排列方式 | 通过layout配置 |
| tag | String | 自定义元素标签 | 通过layout配置 |
| span | Number | 栅格占据列数 | 通过schema配置 |
| offset | Number | 栅格左侧间隔格数 | 通过schema配置 |
| push | Number | 栅格向右移动格数 | 通过schema配置 |
| pull | Number | 栅格向左移动格数 | 通过schema配置 |
| xs | Number/Object | 超小屏幕配置 | 通过schema配置 |
| sm | Number/Object | 小屏幕配置 | 通过schema配置 |
| md | Number/Object | 中等屏幕配置 | 通过schema配置 |
| lg | Number/Object | 大屏幕配置 | 通过schema配置 |
| xl | Number/Object | 超大屏幕配置 | 通过schema配置 |

### el-tabs API透传（分组模式）

| API名称 | 类型 | 说明 | 透传方式 |
|---------|------|------|---------|
| model-value | String | 选中标签页的name | 通过groupConfig配置 |
| type | String | 标签页类型 | 通过groupConfig配置 |
| closable | Boolean | 标签是否可关闭 | 通过groupConfig配置 |
| addable | Boolean | 标签是否可增加 | 通过groupConfig配置 |
| editable | Boolean | 标签是否可编辑 | 通过groupConfig配置 |
| tab-position | String | 标签页位置 | 通过groupConfig配置 |
| stretch | Boolean | 标签宽度是否自撑开 | 通过groupConfig配置 |
| before-leave | Function | 切换标签前的钩子 | 通过groupConfig配置 |

### el-tabs Events透传（分组模式）

| 事件名 | 参数 | 说明 | 透传方式 |
|--------|------|------|---------|
| tab-click | pane, event | 标签页点击事件 | @tab-click |
| tab-change | name | 标签页切换事件 | @tab-change |
| tab-remove | name | 标签页移除事件 | @tab-remove |
| tab-add | - | 标签页新增事件 | @tab-add |
| edit | targetName, action | 标签页编辑事件 | @edit |

### el-steps API透传（步骤模式）

| API名称 | 类型 | 说明 | 透传方式 |
|---------|------|------|---------|
| active | Number | 当前激活步骤 | 通过stepConfig配置 |
| process-status | String | 当前步骤状态 | 通过stepConfig配置 |
| finish-status | String | 结束步骤状态 | 通过stepConfig配置 |
| align-center | Boolean | 居中对齐 | 通过stepConfig配置 |
| direction | String | 显示方向 | 通过stepConfig配置 |
| space | String/Number | 每个step间距 | 通过stepConfig配置 |
| simple | Boolean | 简洁风格 | 通过stepConfig配置 |

## 使用方式

### 基础表单使用
```vue
<template>
  <FuniDetail
    v-model="formData"
    :schema="schema"
    
    <!-- ElementPlus el-form 属性透传 -->
    :rules="rules"
    label-width="120px"
    label-position="right"
    :show-message="true"
    :status-icon="true"
    
    <!-- ElementPlus el-form 事件透传 -->
    @validate="handleValidate"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const formData = ref({
  name: '',
  email: '',
  phone: ''
})

const rules = reactive({
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
})

const schema = reactive([
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    props: { placeholder: '请输入姓名' },
    span: 12
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: { placeholder: '请输入邮箱' },
    span: 12
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    props: { placeholder: '请输入手机号' },
    span: 24
  }
])

const handleValidate = (prop, isValid, message) => {
  console.log('字段验证:', { prop, isValid, message })
}
</script>
```

### 分组表单使用
```vue
<template>
  <FuniDetail
    v-model="formData"
    :schema="schema"
    :group-config="groupConfig"
    
    <!-- ElementPlus el-tabs 属性透传 -->
    @tab-click="handleTabClick"
    @tab-change="handleTabChange"
  />
</template>

<script setup>
const groupConfig = reactive({
  enabled: true,
  type: 'tabs',
  groups: [
    {
      key: 'basic',
      title: '基本信息',
      fields: ['name', 'email', 'phone']
    },
    {
      key: 'detail',
      title: '详细信息',
      fields: ['address', 'company', 'position']
    }
  ],
  // ElementPlus el-tabs 配置
  tabPosition: 'top',
  type: 'border-card',
  stretch: false
})

const handleTabClick = (pane, event) => {
  console.log('标签页点击:', pane.name)
}

const handleTabChange = (name) => {
  console.log('标签页切换:', name)
}
</script>
```

### 步骤表单使用
```vue
<template>
  <FuniDetail
    v-model="formData"
    :schema="schema"
    :step-config="stepConfig"
    
    <!-- ElementPlus el-steps 属性透传 -->
    @step-change="handleStepChange"
  />
</template>

<script setup>
const stepConfig = reactive({
  enabled: true,
  currentStep: 0,
  steps: [
    {
      key: 'basic',
      title: '基本信息',
      description: '填写基本个人信息',
      fields: ['name', 'email']
    },
    {
      key: 'detail',
      title: '详细信息',
      description: '填写详细信息',
      fields: ['address', 'company']
    },
    {
      key: 'confirm',
      title: '确认信息',
      description: '确认所有信息',
      fields: []
    }
  ],
  // ElementPlus el-steps 配置
  direction: 'horizontal',
  alignCenter: false,
  processStatus: 'process',
  finishStatus: 'success'
})

const handleStepChange = (currentStep, direction) => {
  console.log('步骤变化:', { currentStep, direction })
}
</script>
```

### 响应式布局使用
```vue
<template>
  <FuniDetail
    v-model="formData"
    :schema="schema"
    
    <!-- ElementPlus el-row 属性透传 -->
    :gutter="20"
    justify="start"
    align="top"
  />
</template>

<script setup>
const schema = reactive([
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    // ElementPlus el-col 配置
    span: 24,
    xs: 24,
    sm: 12,
    md: 8,
    lg: 6,
    xl: 6
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    span: 24,
    xs: 24,
    sm: 12,
    md: 8,
    lg: 6,
    xl: 6,
    offset: 0,
    push: 0,
    pull: 0
  }
])
</script>
```

### 表单方法调用
```vue
<template>
  <FuniDetail
    ref="detailRef"
    v-model="formData"
    :schema="schema"
  />
  
  <div class="form-actions">
    <el-button @click="validateForm">验证表单</el-button>
    <el-button @click="resetForm">重置表单</el-button>
    <el-button @click="validateField">验证单个字段</el-button>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const detailRef = ref()

// 调用ElementPlus el-form方法
const validateForm = async () => {
  try {
    const valid = await detailRef.value.validate()
    console.log('表单验证结果:', valid)
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

const resetForm = () => {
  detailRef.value.resetFields()
}

const validateField = async () => {
  try {
    const valid = await detailRef.value.validateField(['name', 'email'])
    console.log('字段验证结果:', valid)
  } catch (error) {
    console.log('字段验证失败:', error)
  }
}
</script>
```

## 注意事项

### 1. API透传机制
- 所有ElementPlus el-form的属性都通过v-bind自动透传
- 表单项相关API通过schema配置传递
- 布局相关API通过layout配置或直接透传
- 分组和步骤相关API通过对应的config配置传递

### 2. 方法调用
- 所有ElementPlus el-form的方法都可以通过组件ref调用
- 方法调用方式与原生el-form完全一致
- 支持Promise和callback两种调用方式

### 3. 事件处理
- 所有ElementPlus相关事件都可以正常监听
- 事件参数与原生ElementPlus组件一致
- 组件内部处理不会影响外部事件监听

### 4. 样式定制
- 可以通过ElementPlus的样式相关属性定制外观
- 支持通过CSS变量进行主题定制
- 可以通过深度选择器进一步定制样式

### 5. 响应式支持
- 完全支持ElementPlus的响应式栅格系统
- 可以为每个表单项单独配置响应式参数
- 支持断点配置和自适应布局

### 6. 兼容性
- 完全兼容ElementPlus el-form的所有功能
- 新增功能不会影响原有ElementPlus API的使用
- 可以无缝迁移现有的el-form代码

### 7. 性能考虑
- 大表单时建议使用分组或分步功能
- 合理设置表单项的响应式配置
- 避免在schema中使用复杂的响应式对象
- 使用防抖处理频繁的表单验证
