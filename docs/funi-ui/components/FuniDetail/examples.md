# FuniDetail 使用示例

## 基础使用

### 简单详情页面
```vue
<template>
  <FuniDetail
    v-model="formData"
    :schema="basicSchema"
    mode="view"
    title="用户详情"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const formData = ref({
  name: '张三',
  email: 'z<PERSON><EMAIL>',
  phone: '13800138000',
  department: '技术部',
  position: '前端工程师',
  joinDate: '2023-01-15'
})

const basicSchema = reactive([
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    span: 12
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    span: 12
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    span: 12
  },
  {
    prop: 'department',
    label: '部门',
    component: 'el-input',
    span: 12
  },
  {
    prop: 'position',
    label: '职位',
    component: 'el-input',
    span: 12
  },
  {
    prop: 'joinDate',
    label: '入职日期',
    component: 'el-date-picker',
    props: {
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD'
    },
    span: 12
  }
])
</script>
```

### 编辑模式表单
```vue
<template>
  <FuniDetail
    v-model="formData"
    :schema="editSchema"
    mode="edit"
    title="编辑用户"
    @submit="handleSubmit"
    @cancel="handleCancel"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const formData = ref({
  name: '',
  email: '',
  phone: '',
  department: '',
  position: '',
  status: 1
})

const editSchema = reactive([
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    props: { placeholder: '请输入姓名' },
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    span: 12
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: { placeholder: '请输入邮箱' },
    rules: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    span: 12
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    props: { placeholder: '请输入手机号' },
    rules: [
      { required: true, message: '请输入手机号', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ],
    span: 12
  },
  {
    prop: 'department',
    label: '部门',
    component: 'el-select',
    props: { placeholder: '请选择部门' },
    options: [
      { label: '技术部', value: '技术部' },
      { label: '产品部', value: '产品部' },
      { label: '运营部', value: '运营部' },
      { label: '市场部', value: '市场部' }
    ],
    rules: [{ required: true, message: '请选择部门', trigger: 'change' }],
    span: 12
  },
  {
    prop: 'position',
    label: '职位',
    component: 'el-input',
    props: { placeholder: '请输入职位' },
    span: 12
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-radio-group',
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ],
    span: 12
  }
])

const handleSubmit = async (data) => {
  try {
    console.log('提交数据:', data)
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const handleCancel = () => {
  console.log('取消操作')
}
</script>
```

## 高级配置

### 分组表单
```vue
<template>
  <FuniDetail
    v-model="formData"
    :schema="groupSchema"
    :group-config="groupConfig"
    mode="edit"
    title="用户信息管理"
    @submit="handleSubmit"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const formData = ref({
  // 基本信息
  name: '',
  email: '',
  phone: '',
  avatar: '',
  
  // 工作信息
  department: '',
  position: '',
  manager: '',
  joinDate: '',
  
  // 权限设置
  roles: [],
  permissions: [],
  status: 1
})

const groupSchema = reactive([
  // 基本信息组
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    group: 'basic',
    span: 12
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    group: 'basic',
    span: 12
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    group: 'basic',
    span: 12
  },
  {
    prop: 'avatar',
    label: '头像',
    component: 'el-upload',
    group: 'basic',
    span: 12
  },
  
  // 工作信息组
  {
    prop: 'department',
    label: '部门',
    component: 'el-select',
    group: 'work',
    options: [
      { label: '技术部', value: 'tech' },
      { label: '产品部', value: 'product' }
    ],
    span: 12
  },
  {
    prop: 'position',
    label: '职位',
    component: 'el-input',
    group: 'work',
    span: 12
  },
  {
    prop: 'manager',
    label: '直属领导',
    component: 'FuniSelect',
    group: 'work',
    props: {
      url: '/api/users',
      labelKey: 'name',
      valueKey: 'id'
    },
    span: 12
  },
  {
    prop: 'joinDate',
    label: '入职日期',
    component: 'el-date-picker',
    group: 'work',
    props: { type: 'date' },
    span: 12
  },
  
  // 权限设置组
  {
    prop: 'roles',
    label: '角色',
    component: 'el-select',
    group: 'permission',
    props: { multiple: true },
    options: [
      { label: '管理员', value: 'admin' },
      { label: '普通用户', value: 'user' }
    ],
    span: 24
  },
  {
    prop: 'permissions',
    label: '权限',
    component: 'el-checkbox-group',
    group: 'permission',
    options: [
      { label: '用户管理', value: 'user:manage' },
      { label: '系统设置', value: 'system:config' }
    ],
    span: 24
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-switch',
    group: 'permission',
    span: 12
  }
])

const groupConfig = reactive({
  enabled: true,
  type: 'tabs',
  groups: [
    {
      key: 'basic',
      title: '基本信息',
      icon: 'User',
      fields: ['name', 'email', 'phone', 'avatar']
    },
    {
      key: 'work',
      title: '工作信息',
      icon: 'Briefcase',
      fields: ['department', 'position', 'manager', 'joinDate']
    },
    {
      key: 'permission',
      title: '权限设置',
      icon: 'Lock',
      fields: ['roles', 'permissions', 'status']
    }
  ],
  tabPosition: 'top'
})

const handleSubmit = async (data) => {
  console.log('提交分组表单数据:', data)
}
</script>
```

### 分步表单
```vue
<template>
  <FuniDetail
    v-model="formData"
    :schema="stepSchema"
    :step-config="stepConfig"
    mode="create"
    title="新建项目"
    @submit="handleSubmit"
    @step-change="handleStepChange"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const formData = ref({
  // 第一步：基本信息
  projectName: '',
  projectType: '',
  description: '',
  
  // 第二步：团队配置
  leader: '',
  members: [],
  startDate: '',
  endDate: '',
  
  // 第三步：资源配置
  budget: '',
  resources: [],
  priority: 'medium'
})

const stepSchema = reactive([
  // 第一步字段
  {
    prop: 'projectName',
    label: '项目名称',
    component: 'el-input',
    step: 0,
    rules: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
    span: 24
  },
  {
    prop: 'projectType',
    label: '项目类型',
    component: 'el-select',
    step: 0,
    options: [
      { label: 'Web应用', value: 'web' },
      { label: '移动应用', value: 'mobile' },
      { label: '桌面应用', value: 'desktop' }
    ],
    rules: [{ required: true, message: '请选择项目类型', trigger: 'change' }],
    span: 24
  },
  {
    prop: 'description',
    label: '项目描述',
    component: 'el-input',
    step: 0,
    props: { type: 'textarea', rows: 4 },
    span: 24
  },
  
  // 第二步字段
  {
    prop: 'leader',
    label: '项目负责人',
    component: 'FuniSelect',
    step: 1,
    props: {
      url: '/api/users',
      labelKey: 'name',
      valueKey: 'id'
    },
    rules: [{ required: true, message: '请选择项目负责人', trigger: 'change' }],
    span: 12
  },
  {
    prop: 'members',
    label: '团队成员',
    component: 'FuniSelect',
    step: 1,
    props: {
      url: '/api/users',
      labelKey: 'name',
      valueKey: 'id',
      multiple: true
    },
    span: 12
  },
  {
    prop: 'startDate',
    label: '开始日期',
    component: 'el-date-picker',
    step: 1,
    props: { type: 'date' },
    rules: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
    span: 12
  },
  {
    prop: 'endDate',
    label: '结束日期',
    component: 'el-date-picker',
    step: 1,
    props: { type: 'date' },
    rules: [{ required: true, message: '请选择结束日期', trigger: 'change' }],
    span: 12
  },
  
  // 第三步字段
  {
    prop: 'budget',
    label: '项目预算',
    component: 'FuniMoneyInput',
    step: 2,
    span: 12
  },
  {
    prop: 'resources',
    label: '所需资源',
    component: 'el-checkbox-group',
    step: 2,
    options: [
      { label: '服务器', value: 'server' },
      { label: '数据库', value: 'database' },
      { label: '第三方服务', value: 'thirdparty' }
    ],
    span: 12
  },
  {
    prop: 'priority',
    label: '优先级',
    component: 'el-radio-group',
    step: 2,
    options: [
      { label: '高', value: 'high' },
      { label: '中', value: 'medium' },
      { label: '低', value: 'low' }
    ],
    span: 24
  }
])

const stepConfig = reactive({
  enabled: true,
  currentStep: 0,
  steps: [
    {
      key: 'basic',
      title: '基本信息',
      description: '填写项目的基本信息',
      icon: 'Document',
      fields: ['projectName', 'projectType', 'description']
    },
    {
      key: 'team',
      title: '团队配置',
      description: '配置项目团队和时间',
      icon: 'Users',
      fields: ['leader', 'members', 'startDate', 'endDate']
    },
    {
      key: 'resource',
      title: '资源配置',
      description: '配置项目资源和优先级',
      icon: 'Setting',
      fields: ['budget', 'resources', 'priority']
    }
  ],
  validateOnNext: true,
  showStepNumber: true
})

const handleSubmit = async (data) => {
  console.log('提交分步表单数据:', data)
}

const handleStepChange = (currentStep, direction) => {
  console.log('步骤变化:', { currentStep, direction })
}
</script>
```

## 业务场景示例

### 工作流审核页面
```vue
<template>
  <FuniDetail
    v-model="formData"
    :schema="auditSchema"
    mode="audit"
    title="请假申请审核"
    @submit="handleAuditSubmit"
  >
    <!-- 工作流附件 -->
    <template #after-form>
      <FuniFileTable
        v-if="formData.businessId"
        :business-id="formData.businessId"
        title="相关附件"
      />
    </template>
    
    <!-- 审核按钮 -->
    <template #footer>
      <FuniAuditButtomBtn
        v-if="formData.businessId"
        :business-id="formData.businessId"
        @audit="handleAudit"
      />
    </template>
  </FuniDetail>
  
  <!-- 审核意见抽屉 -->
  <FuniBusAuditDrawer
    v-model="showAuditDrawer"
    :business-id="formData.businessId"
    @submit="handleAuditOpinion"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const showAuditDrawer = ref(false)

const formData = ref({
  businessId: 'BIZ202312190001',
  applicant: '张三',
  leaveType: '年假',
  startDate: '2023-12-20',
  endDate: '2023-12-22',
  days: 3,
  reason: '家庭事务处理',
  applyDate: '2023-12-19',
  status: '待审核'
})

const auditSchema = reactive([
  {
    prop: 'applicant',
    label: '申请人',
    component: 'el-input',
    props: { readonly: true },
    span: 12
  },
  {
    prop: 'leaveType',
    label: '请假类型',
    component: 'el-input',
    props: { readonly: true },
    span: 12
  },
  {
    prop: 'startDate',
    label: '开始日期',
    component: 'el-date-picker',
    props: { readonly: true, type: 'date' },
    span: 12
  },
  {
    prop: 'endDate',
    label: '结束日期',
    component: 'el-date-picker',
    props: { readonly: true, type: 'date' },
    span: 12
  },
  {
    prop: 'days',
    label: '请假天数',
    component: 'el-input-number',
    props: { readonly: true },
    span: 12
  },
  {
    prop: 'applyDate',
    label: '申请日期',
    component: 'el-date-picker',
    props: { readonly: true, type: 'date' },
    span: 12
  },
  {
    prop: 'reason',
    label: '请假原因',
    component: 'el-input',
    props: { type: 'textarea', readonly: true, rows: 3 },
    span: 24
  },
  {
    prop: 'status',
    label: '当前状态',
    component: 'el-tag',
    span: 12
  }
])

const handleAuditSubmit = (data) => {
  console.log('审核提交:', data)
}

const handleAudit = (action) => {
  console.log('审核操作:', action)
  showAuditDrawer.value = true
}

const handleAuditOpinion = (opinion) => {
  console.log('审核意见:', opinion)
  showAuditDrawer.value = false
}
</script>
```

### 动态表单配置
```vue
<template>
  <FuniDetail
    v-model="formData"
    :schema="dynamicSchema"
    mode="edit"
    title="动态表单示例"
    @submit="handleSubmit"
  />
</template>

<script setup>
import { ref, reactive, watch } from 'vue'

const formData = ref({
  userType: 'individual',
  name: '',
  companyName: '',
  contactPerson: '',
  email: '',
  phone: '',
  address: ''
})

// 动态schema，根据用户类型显示不同字段
const dynamicSchema = reactive([
  {
    prop: 'userType',
    label: '用户类型',
    component: 'el-radio-group',
    options: [
      { label: '个人用户', value: 'individual' },
      { label: '企业用户', value: 'company' }
    ],
    span: 24
  },
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    show: () => formData.value.userType === 'individual',
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    span: 12
  },
  {
    prop: 'companyName',
    label: '公司名称',
    component: 'el-input',
    show: () => formData.value.userType === 'company',
    rules: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
    span: 12
  },
  {
    prop: 'contactPerson',
    label: '联系人',
    component: 'el-input',
    show: () => formData.value.userType === 'company',
    rules: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
    span: 12
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    rules: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    span: 12
  },
  {
    prop: 'phone',
    label: '联系电话',
    component: 'el-input',
    rules: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
    span: 12
  },
  {
    prop: 'address',
    label: '地址',
    component: 'el-input',
    props: { type: 'textarea', rows: 3 },
    span: 24
  }
])

// 监听用户类型变化，清空相关字段
watch(() => formData.value.userType, (newType, oldType) => {
  if (newType !== oldType) {
    if (newType === 'individual') {
      formData.value.companyName = ''
      formData.value.contactPerson = ''
    } else {
      formData.value.name = ''
    }
  }
})

const handleSubmit = async (data) => {
  console.log('提交动态表单数据:', data)
}
</script>
```

## 注意事项

### 1. 数据绑定
- 使用v-model进行双向数据绑定
- 确保formData的结构与schema中的prop对应
- 注意数据类型的一致性

### 2. 表单验证
- 在schema中配置rules进行表单验证
- 支持ElementPlus的所有验证规则
- 可以使用自定义验证函数

### 3. 组件配置
- 通过props传递组件属性
- 通过options配置选择器选项
- 支持所有ElementPlus表单组件

### 4. 工作流集成
- 工作流组件需要businessId
- 使用条件渲染控制组件显示
- 配合审核组件实现完整工作流

### 5. 性能优化
- 大表单时使用分组或分步功能
- 合理使用条件显示减少渲染
- 避免在schema中使用复杂的响应式对象
