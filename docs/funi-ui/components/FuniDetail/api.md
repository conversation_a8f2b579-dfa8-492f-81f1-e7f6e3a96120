# FuniDetail API文档

## 组件概述

FuniDetail是CLI框架中的核心详情页组件，**所有PC端详情/新建/编辑页面必须使用此组件**。支持多步骤流程、工作流集成、审核功能等复杂业务场景。

## ⚠️ 重要使用注意事项

### 模板标签使用
```vue
<!-- ✅ 正确：使用kebab-case -->
<funi-detail :steps="steps" :bizName="bizName" />

<!-- ❌ 错误：使用PascalCase -->
<FuniDetail :steps="steps" :bizName="bizName" />
```

**说明**：虽然需要手动导入 `import FuniDetail from '@/components/FuniDetail/index.vue'`，但在模板中必须使用小写的kebab-case标签名。

### Steps配置最佳实践
- **使用reactive定义**，确保响应式更新
- **Step组件必须实现nextStep/prevStep方法**
- **使用inject获取FuniDetail提供的updateTempData方法**

```javascript
// ✅ 正确：使用reactive
const steps = reactive([
  {
    title: '基本信息',
    type: shallowRef(BasicInfoForm),
    props: { formData: store.formData }
  }
])

// ❌ 错误：使用computed（会导致渲染问题）
const steps = computed(() => [
  {
    title: '基本信息',
    type: shallowRef(BasicInfoForm),
    props: { formData: store.formData }
  }
])
```

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| detailHeadOption | Object | {} | - | 详情头部配置选项 |
| homeRouter | Object | {path: '/'} | - | 主页路由配置 |
| parentRouter | Object | {title: '', path: ''} | - | 父级路由配置 |
| steps | Array | [] | ✅ | 步骤配置数组 |
| current | Number | 0 | - | 当前步骤索引 |
| bizName | String | '编辑' | ✅ | 业务操作类型：'新建'/'编辑'/'详情'/'审核' |
| showHead | Boolean | null | - | 是否显示头部，null时自动判断 |
| businessId | String | - | - | 工作流业务ID（工作流场景必需） |
| sysId | String | - | - | 系统ID（工作流场景必需） |
| showWorkflow | Boolean | false | - | 是否显示工作流相关组件 |
| auditButtons | Array | [] | - | 审核按钮配置（审核模式使用） |
| isAuthFixedBtn | Boolean | false | - | 是否启用权限固定按钮 |
| isFormOpinion | Boolean | false | - | 是否在表单中提供审核意见 |
| beforeLeave | Function | () => true | - | 切换标签前的钩子函数 |
| tsKey | String | '暂存' | - | 暂存按钮显示名称 |
| tsType | String | 'default' | - | 暂存按钮样式类型 |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| tabChange | tabIndex: number | 标签页切换事件 | 切换步骤标签页时 |
| clickBtnEvent | button: ButtonConfig | 按钮点击事件 | 点击头部按钮时 |
| auditClick | action: string | 审核按钮点击事件 | 点击审核按钮时 |
| auditEvent | event: AuditEvent | 审核事件 | 审核操作完成时 |

## Slots

| 插槽名 | 参数 | 说明 | 使用场景 |
|--------|------|------|---------|
| [stepSlot] | - | 步骤内容插槽 | 每个步骤的具体内容 |
| headTitle | { option, bizName } | 头部标题插槽 | 自定义页面标题 |
| headStatus | { option, bizName } | 头部状态插槽 | 自定义状态信息 |
| auditbtns | { steps, current, auditButtons } | 审核按钮插槽 | 自定义审核按钮区域 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| clickBtnEvent | button: ButtonConfig | void | 触发按钮点击事件 |

## detailHeadOption配置结构

```typescript
interface DetailHeadOption {
  // 基础信息
  title?: string;                  // 页面标题
  serialName?: string;             // 编号字段名称，默认'业务编号'
  no?: string;                     // 编号值
  statusName?: string;             // 状态字段名称，默认'状态'
  status?: string;                 // 状态值
  hideStatusBar?: boolean;         // 是否隐藏状态栏
  hideStatusName?: boolean;        // 是否隐藏状态名称
  
  // 链接配置
  links?: Array<{
    title: string;                 // 链接标题
    name: string;                  // 链接文本
    props?: Record<string, any>;   // ElementPlus按钮属性
    on?: Record<string, Function>; // 事件处理
  }>;
  
  // 头部按钮配置
  btns?: Array<{
    name: string;                  // 按钮文本
    type?: string;                 // 按钮类型，对应子组件方法名
    triggerEvent?: boolean;        // 是否触发clickBtnEvent事件
    props?: Record<string, any>;   // ElementPlus按钮属性
    on?: Record<string, Function>; // 事件处理
  }>;
}
```

## steps配置结构

```typescript
interface StepConfig {
  // 基础信息
  title: string;                   // 步骤标题
  icon?: string;                   // 步骤图标
  width?: number;                  // 步骤宽度，默认200
  
  // 组件配置
  type?: Component;                // Vue组件（使用shallowRef包装）
  props?: Record<string, any>;     // 传递给组件的props
  on?: Record<string, Function>;   // 组件事件处理
  
  // 业务配置
  preservable?: boolean;           // 是否可保存（影响按钮显示）
  slot?: string;                   // 插槽名称（不使用type时）
}
```

## 业务模式说明

### bizName模式对应关系
- **'新建' | 'add'**: 新建模式，显示保存/下一步按钮
- **'编辑' | 'edit'**: 编辑模式，显示保存/下一步按钮
- **'详情' | 'detail'**: 详情模式，只读展示，不显示操作按钮
- **'审核' | 'audit'**: 审核模式，显示审核按钮和审核抽屉

### 工作流集成条件
- **businessId**: 工作流业务ID，用于标识工作流实例
- **sysId**: 系统ID，用于工作流系统识别
- **showWorkflow**: 是否启用工作流功能

## 工作流组件集成

### 自动集成的工作流组件
1. **FuniFileTable**: 当businessId存在时自动显示
2. **FuniAuditButtomBtn**: 审核模式下自动显示
3. **FuniBusAuditDrawer**: 工作流场景下自动集成

### 工作流组件渲染条件
```javascript
// FuniFileTable渲染条件
const shouldShowFileTable = computed(() => {
  return !!businessId.value && 
         ['新建', 'add', '编辑', 'edit', '详情', 'detail', '审核', 'audit'].includes(bizName.value)
})

// FuniAuditButtomBtn渲染条件
const shouldShowAuditBtn = computed(() => {
  return !!businessId.value && 
         ['审核', 'audit'].includes(bizName.value)
})

// FuniBusAuditDrawer渲染条件
const shouldShowAuditDrawer = computed(() => {
  return !!businessId.value && 
         showWorkflow.value
})
```

## 步骤流程控制

### 按钮类型与方法映射
组件会自动查找子组件中与按钮type同名的方法：

```javascript
// 按钮配置
const buttons = [
  { name: '保存', type: 'save' },
  { name: '提交', type: 'submit' },
  { name: '上一步', type: 'lastStep' },
  { name: '下一步', type: 'nextStep' }
]

// 子组件需要暴露对应方法
defineExpose({
  save: () => Promise.resolve(),
  submit: () => Promise.resolve(),
  // 其他方法...
})
```

### 步骤切换逻辑
- **lastStep**: 当前步骤索引减1
- **nextStep**: 当前步骤索引加1
- **其他类型**: 不改变步骤索引

## 使用注意事项

### 1. 强制使用规则
- 所有PC端详情/新建/编辑页面必须使用FuniDetail
- steps配置至少包含一个步骤

### 2. 工作流场景配置
- 工作流场景必须提供businessId和sysId
- 审核模式需要配置auditButtons或使用默认按钮

### 3. 子组件要求
- 子组件必须暴露与按钮type对应的方法
- 方法必须返回Promise
- Promise resolve时继续流程，reject时中断流程

### 4. 权限控制
- 支持通过isAuthFixedBtn启用权限控制
- 审核按钮会根据工作流权限动态显示

## 常见问题

### Q: 如何实现多步骤表单？
A: 在steps中配置多个步骤，每个步骤对应一个组件，通过按钮控制步骤切换

### Q: 工作流组件什么时候显示？
A: 当businessId存在且bizName为工作流相关模式时自动显示

### Q: 如何自定义审核按钮？
A: 通过auditButtons属性配置，或使用auditbtns插槽自定义

### Q: 子组件如何与父组件通信？
A: 通过暴露方法、emit事件或provide/inject机制

### Q: 如何处理步骤切换前的验证？
A: 在子组件的对应方法中进行验证，返回rejected Promise阻止切换
