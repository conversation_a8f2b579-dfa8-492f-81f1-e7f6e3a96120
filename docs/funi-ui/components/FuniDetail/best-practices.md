# FuniDetail 最佳实践

## 推荐用法

### 1. 标准详情页配置
```vue
<template>
  <FuniDetail
    v-model="formData"
    :schema="schema"
    :mode="pageMode"
    :title="pageTitle"
    :loading="loading"
    @submit="handleSubmit"
    @cancel="handleCancel"
  />
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const loading = ref(false)

// 推荐：根据路由参数确定页面模式
const pageMode = computed(() => {
  if (route.path.includes('/create')) return 'create'
  if (route.path.includes('/edit')) return 'edit'
  if (route.path.includes('/audit')) return 'audit'
  return 'view'
})

// 推荐：动态页面标题
const pageTitle = computed(() => {
  const titles = {
    create: '新建用户',
    edit: '编辑用户',
    view: '用户详情',
    audit: '用户审核'
  }
  return titles[pageMode.value] || '用户信息'
})

// 推荐：使用响应式数据管理表单
const formData = ref({
  id: '',
  name: '',
  email: '',
  phone: '',
  department: '',
  status: 1
})

// 推荐：统一的schema配置
const schema = reactive([
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    props: { placeholder: '请输入姓名' },
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    span: 12
  },
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    props: { placeholder: '请输入邮箱' },
    rules: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    span: 12
  },
  {
    prop: 'phone',
    label: '手机号',
    component: 'el-input',
    props: { placeholder: '请输入手机号' },
    rules: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
    span: 12
  },
  {
    prop: 'department',
    label: '部门',
    component: 'FuniSelect',
    props: {
      url: '/api/departments',
      labelKey: 'name',
      valueKey: 'id',
      placeholder: '请选择部门'
    },
    rules: [{ required: true, message: '请选择部门', trigger: 'change' }],
    span: 12
  },
  {
    prop: 'status',
    label: '状态',
    component: 'el-switch',
    props: {
      activeText: '启用',
      inactiveText: '禁用'
    },
    span: 12
  }
])

// 推荐：统一的提交处理
const handleSubmit = async (data) => {
  loading.value = true
  try {
    if (pageMode.value === 'create') {
      await api.createUser(data)
      ElMessage.success('创建成功')
    } else if (pageMode.value === 'edit') {
      await api.updateUser(data.id, data)
      ElMessage.success('更新成功')
    }
    router.back()
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  router.back()
}
</script>
```

### 2. 工作流页面最佳配置
```vue
<template>
  <FuniDetail
    v-model="formData"
    :schema="workflowSchema"
    :mode="pageMode"
    :step-config="stepConfig"
    @submit="handleWorkflowSubmit"
  >
    <!-- 第二步：附件管理 -->
    <template #step-1>
      <FuniFileTable
        v-if="formData.businessId"
        :business-id="formData.businessId"
        title="申请附件"
      />
    </template>
  </FuniDetail>
  
  <!-- 审核按钮（仅审核模式显示） -->
  <FuniAuditButtomBtn
    v-if="showAuditButtons"
    :business-id="formData.businessId"
    @audit="handleAudit"
  />
</template>

<script setup>
import { ref, reactive, computed } from 'vue'

// 推荐：明确的工作流状态判断
const showAuditButtons = computed(() => {
  return pageMode.value === 'audit' && 
         formData.value.businessId && 
         formData.value.status === '待审核'
})

// 推荐：分步工作流配置
const stepConfig = reactive({
  enabled: true,
  currentStep: 0,
  steps: [
    {
      key: 'basic',
      title: '基本信息',
      description: '填写申请的基本信息',
      fields: ['title', 'type', 'reason', 'amount']
    },
    {
      key: 'attachment',
      title: '附件上传',
      description: '上传相关证明材料',
      fields: [] // 使用插槽处理
    },
    {
      key: 'confirm',
      title: '确认提交',
      description: '确认信息并提交申请',
      fields: []
    }
  ],
  validateOnNext: true
})

// 推荐：工作流提交处理
const handleWorkflowSubmit = async (data) => {
  try {
    if (pageMode.value === 'create') {
      // 第一步提交返回businessId
      const result = await api.createApplication(data)
      formData.value.businessId = result.businessId
      ElMessage.success('申请已提交，请上传相关附件')
    } else {
      await api.updateApplication(data.id, data)
      ElMessage.success('更新成功')
    }
  } catch (error) {
    ElMessage.error('操作失败')
  }
}
</script>
```

### 3. 分组表单最佳实践
```vue
<script setup>
// 推荐：合理的分组策略
const groupConfig = reactive({
  enabled: true,
  type: 'tabs', // 或 'collapse', 'card'
  groups: [
    {
      key: 'basic',
      title: '基本信息',
      icon: 'User',
      description: '用户的基本个人信息',
      fields: ['name', 'email', 'phone', 'avatar']
    },
    {
      key: 'work',
      title: '工作信息',
      icon: 'Briefcase',
      description: '用户的工作相关信息',
      fields: ['department', 'position', 'manager', 'joinDate']
    },
    {
      key: 'permission',
      title: '权限设置',
      icon: 'Lock',
      description: '用户的角色和权限配置',
      fields: ['roles', 'permissions', 'status'],
      // 推荐：权限控制分组显示
      show: () => hasPermission('user:permission')
    }
  ],
  // 推荐：合适的标签页配置
  tabPosition: 'top',
  stretch: false
})
</script>
```

## 避免的用法

### 1. 不推荐的配置方式
```vue
<!-- ❌ 避免：直接在模板中写复杂配置 -->
<FuniDetail
  :schema="[
    { prop: 'name', label: '姓名', component: 'el-input', rules: [{ required: true }] },
    { prop: 'email', label: '邮箱', component: 'el-input', rules: [{ type: 'email' }] }
  ]"
/>

<!-- ❌ 避免：在模板中写内联函数 -->
<FuniDetail
  @submit="(data) => api.save(data)"
  @cancel="() => router.back()"
/>

<!-- ❌ 避免：过度复杂的单个表单 -->
<FuniDetail
  :schema="[...50个字段的配置]" // 应该使用分组或分步
/>
```

### 2. 不推荐的数据处理
```vue
<script setup>
// ❌ 避免：在schema中使用复杂的响应式对象
const schema = reactive([
  {
    prop: 'department',
    label: '部门',
    component: 'el-select',
    options: computed(() => { // 不要在schema中使用computed
      return departments.value.map(d => ({ label: d.name, value: d.id }))
    })
  }
])

// ❌ 避免：直接修改props传入的数据
const handleSubmit = (data) => {
  data.id = Date.now() // 不要直接修改传入的数据
  api.save(data)
}

// ❌ 避免：在组件内部处理路由跳转
const handleSubmit = async (data) => {
  await api.save(data)
  this.$router.push('/list') // 应该通过事件通知父组件
}
</script>
```

### 3. 常见错误和解决方案

#### 错误1：表单验证不生效
```vue
<script setup>
// ❌ 错误：rules配置不正确
const schema = [
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    rules: { required: true } // 应该是数组
  }
]

// ✅ 正确：使用数组配置rules
const schema = [
  {
    prop: 'email',
    label: '邮箱',
    component: 'el-input',
    rules: [{ required: true, message: '请输入邮箱', trigger: 'blur' }]
  }
]
</script>
```

#### 错误2：工作流组件不显示
```vue
<script setup>
// ❌ 错误：缺少必要的条件判断
const showFileTable = true // 总是显示

// ✅ 正确：根据业务状态判断
const showFileTable = computed(() => {
  return formData.value.businessId && 
         ['create', 'edit', 'view', 'audit'].includes(pageMode.value)
})
</script>
```

#### 错误3：分步表单步骤控制不当
```vue
<script setup>
// ❌ 错误：手动控制步骤
const nextStep = () => {
  stepConfig.currentStep++ // 没有验证就跳转
}

// ✅ 正确：使用组件内置的步骤控制
const stepConfig = reactive({
  enabled: true,
  validateOnNext: true, // 启用验证
  steps: [...]
})
</script>
```

## 性能优化建议

### 1. 大表单优化
```vue
<script setup>
// 推荐：使用分组减少渲染压力
const groupConfig = reactive({
  enabled: true,
  type: 'tabs',
  groups: [
    { key: 'group1', title: '基本信息', fields: ['field1', 'field2'] },
    { key: 'group2', title: '详细信息', fields: ['field3', 'field4'] }
  ]
})

// 推荐：使用条件显示减少组件数量
const schema = reactive([
  {
    prop: 'advancedField',
    label: '高级选项',
    component: 'el-input',
    show: () => formData.value.showAdvanced // 条件显示
  }
])
</script>
```

### 2. 数据加载优化
```vue
<script setup>
// 推荐：使用防抖处理搜索
import { debounce } from 'lodash-es'

const debouncedSearch = debounce(async (keyword) => {
  const options = await api.search(keyword)
  // 更新选项
}, 300)

// 推荐：缓存常用数据
const departmentOptions = ref([])
const loadDepartments = async () => {
  if (departmentOptions.value.length === 0) {
    departmentOptions.value = await api.getDepartments()
  }
}
</script>
```

### 3. 组件复用优化
```vue
<script setup>
// 推荐：提取公共schema配置
const createUserSchema = () => [
  {
    prop: 'name',
    label: '姓名',
    component: 'el-input',
    rules: [{ required: true, message: '请输入姓名', trigger: 'blur' }]
  },
  // ... 其他字段
]

// 推荐：根据模式调整schema
const getSchemaByMode = (mode) => {
  const baseSchema = createUserSchema()
  if (mode === 'view') {
    return baseSchema.map(item => ({
      ...item,
      props: { ...item.props, readonly: true }
    }))
  }
  return baseSchema
}
</script>
```

## 业务场景最佳实践

### 1. 用户管理详情页
```vue
<template>
  <FuniDetail
    v-model="userForm"
    :schema="userSchema"
    :mode="pageMode"
    title="用户管理"
    :group-config="userGroupConfig"
    @submit="handleUserSubmit"
  />
</template>

<script setup>
// 用户管理的标准配置
const userSchema = reactive([
  // 基本信息
  { prop: 'name', label: '姓名', component: 'el-input', group: 'basic', span: 12 },
  { prop: 'email', label: '邮箱', component: 'el-input', group: 'basic', span: 12 },
  { prop: 'phone', label: '手机号', component: 'el-input', group: 'basic', span: 12 },
  { prop: 'avatar', label: '头像', component: 'el-upload', group: 'basic', span: 12 },
  
  // 工作信息
  { prop: 'department', label: '部门', component: 'FuniSelect', group: 'work', span: 12 },
  { prop: 'position', label: '职位', component: 'el-input', group: 'work', span: 12 },
  { prop: 'manager', label: '直属领导', component: 'FuniSelect', group: 'work', span: 12 },
  { prop: 'joinDate', label: '入职日期', component: 'el-date-picker', group: 'work', span: 12 },
  
  // 权限设置
  { prop: 'roles', label: '角色', component: 'FuniRUOC', group: 'permission', span: 24 },
  { prop: 'status', label: '状态', component: 'el-switch', group: 'permission', span: 12 }
])

const userGroupConfig = reactive({
  enabled: true,
  type: 'tabs',
  groups: [
    { key: 'basic', title: '基本信息', icon: 'User' },
    { key: 'work', title: '工作信息', icon: 'Briefcase' },
    { key: 'permission', title: '权限设置', icon: 'Lock' }
  ]
})
</script>
```

### 2. 订单详情页
```vue
<template>
  <FuniDetail
    v-model="orderForm"
    :schema="orderSchema"
    :mode="pageMode"
    title="订单详情"
    @submit="handleOrderSubmit"
  />
</template>

<script setup>
// 订单详情的标准配置
const orderSchema = reactive([
  { prop: 'orderNo', label: '订单号', component: 'el-input', props: { readonly: true }, span: 12 },
  { prop: 'customerName', label: '客户名称', component: 'FuniSelect', span: 12 },
  { prop: 'products', label: '商品列表', component: 'FuniCurd', span: 24 },
  { prop: 'amount', label: '订单金额', component: 'FuniMoneyInput', span: 12 },
  { prop: 'status', label: '订单状态', component: 'el-select', span: 12 },
  { prop: 'remark', label: '备注', component: 'el-input', props: { type: 'textarea' }, span: 24 }
])
</script>
```

### 3. 审批流程页
```vue
<template>
  <FuniDetail
    v-model="approvalForm"
    :schema="approvalSchema"
    :mode="pageMode"
    :step-config="approvalStepConfig"
    @submit="handleApprovalSubmit"
  >
    <template #step-1>
      <FuniFileTable
        v-if="approvalForm.businessId"
        :business-id="approvalForm.businessId"
      />
    </template>
  </FuniDetail>
  
  <FuniAuditButtomBtn
    v-if="showAuditButtons"
    :business-id="approvalForm.businessId"
    @audit="handleAudit"
  />
</template>

<script setup>
// 审批流程的标准配置
const approvalStepConfig = reactive({
  enabled: true,
  steps: [
    { key: 'basic', title: '申请信息', fields: ['title', 'type', 'reason'] },
    { key: 'attachment', title: '附件上传', fields: [] },
    { key: 'confirm', title: '确认提交', fields: [] }
  ],
  validateOnNext: true
})

const showAuditButtons = computed(() => {
  return pageMode.value === 'audit' && approvalForm.value.businessId
})
</script>
```

## 常见问题和解决方案

### 表单渲染空白问题

**问题描述**：FuniDetail组件渲染后，step内容显示空白，没有表单字段

**常见原因**：
1. 使用了错误的组件标签（PascalCase而不是kebab-case）
2. Steps使用computed而不是reactive定义
3. Step组件缺少必要的inject和方法暴露
4. 数据绑定使用props.formData而不是本地数据

**解决方案**：

#### 1. 正确的组件标签使用
```vue
<!-- ✅ 正确 -->
<funi-detail :steps="steps" :bizName="bizName" />

<!-- ❌ 错误 -->
<FuniDetail :steps="steps" :bizName="bizName" />
```

#### 2. 正确的Steps配置
```javascript
// ✅ 正确：使用reactive
const steps = reactive([
  {
    title: '基本信息',
    type: shallowRef(BasicInfoForm),
    props: { formData: store.formData }
  }
])

// ❌ 错误：使用computed
const steps = computed(() => [...])
```

#### 3. Step组件完整实现
```vue
<!-- Step组件模板 -->
<template>
  <div class="step-form">
    <el-form :model="localFormData" ref="formRef">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="localFormData.name" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, inject, watchEffect, reactive } from 'vue'

const props = defineProps({
  formData: { type: Object, required: true }
})

// 注入FuniDetail提供的方法
const tempData = inject('tempData', {})
const updateTempData = inject('updateTempData')

// 使用本地数据
const localFormData = reactive({ ...(props.formData || {}) })

// 同步props变化
watchEffect(() => {
  Object.assign(localFormData, { ...(props.formData || {}) })
})

const formRef = ref(null)

// 必须暴露的方法
const nextStep = ({ type, current }) => {
  return new Promise((resolve, reject) => {
    if (formRef.value) {
      formRef.value.validate((valid) => {
        if (valid) {
          // 更新临时数据
          if (updateTempData) {
            updateTempData({ ...localFormData })
          }
          resolve()
        } else {
          reject(new Error('表单验证失败'))
        }
      })
    } else {
      resolve()
    }
  })
}

const prevStep = ({ type, current }) => {
  return Promise.resolve()
}

defineExpose({
  nextStep,
  prevStep,
  validate: () => formRef.value?.validate(),
  validateField: (prop) => formRef.value?.validateField(prop),
  clearValidate: (props) => formRef.value?.clearValidate(props)
})
</script>

<style scoped>
.step-form {
  padding: 8px;
  min-height: 400px;
}
</style>
```

#### 4. 正确的按钮配置
```javascript
// ✅ 正确：使用on属性
const detailHeadOption = reactive({
  title: '用户信息',
  btns: [{
    name: '保存',
    props: { type: 'primary' },
    on: { click: () => handleSave() }
  }]
})

// ❌ 错误：使用事件监听
// <funi-detail @clickBtnEvent="handleButtonClick" />
```

### 数据同步问题

**问题描述**：表单数据修改后，其他步骤或父组件无法获取到最新数据

**解决方案**：
1. 在Step组件的nextStep方法中调用updateTempData
2. 使用watchEffect同步props变化到本地数据
3. 在父组件的onMounted中动态更新steps的props

```javascript
// 父组件中动态更新props
onMounted(async () => {
  await store.loadData()

  // 更新steps的props
  steps[0].props.formData = store.formData
  steps[0].props.options = store.options
})
```

## 总结

### 关键原则
1. **配置驱动**：通过schema配置而非硬编码实现表单
2. **模式区分**：根据页面模式（create/edit/view/audit）调整行为
3. **工作流集成**：合理使用businessId和工作流组件
4. **性能优先**：使用分组、分步、条件显示优化性能
5. **用户体验**：提供清晰的反馈和合理的交互流程

### 开发建议
1. 优先使用响应式配置对象
2. 合理设计表单结构和分组
3. 统一处理数据提交和错误处理
4. 遵循工作流组件的使用规范
5. 保持代码的可维护性和一致性
