# FuniBusAuditDrawer 使用示例

## 基础示例

### 1. 最简单的使用

```vue
<template>
  <FuniBusAuditDrawer
    :businessId="businessId"
    :sysId="sysId"
    @audit-event="handleAuditEvent"
  />
</template>

<script setup>
import { ref } from 'vue';

const businessId = ref('BIZ202401001');
const sysId = ref('LEAVE_SYSTEM');

const handleAuditEvent = (event) => {
  console.log('审核事件:', event);
  // 处理审核完成后的逻辑
};
</script>
```

### 2. 只读模式

```vue
<template>
  <FuniBusAuditDrawer
    :businessId="businessId"
    :sysId="sysId"
    :onlyShow="true"
    :isHideUnfold="true"
  />
</template>

<script setup>
import { ref } from 'vue';

const businessId = ref('BIZ202401001');
const sysId = ref('LEAVE_SYSTEM');
</script>
```

## 工作流集成示例

### 1. 与FuniDetail和FuniAuditButtomBtn集成

```vue
<template>
  <div class="audit-page">
    <FuniDetail 
      :steps="steps" 
      bizName="审核"
      :businessId="businessId"
      :sysId="sysId"
    >
      <!-- 申请信息 -->
      <template #applicationInfo>
        <FuniForm 
          :schema="applicationSchema" 
          v-model="applicationData" 
          :readonly="true" 
        />
      </template>
      
      <!-- 附件材料 -->
      <template #attachments>
        <FuniFileTable 
          :params="fileTableParams" 
          :onlyShow="true" 
        />
      </template>
      
      <!-- 审核按钮 -->
      <template #auditbtns="{ auditButtons }">
        <FuniAuditButtomBtn
          :auditButtons="auditButtons || mockAuditButtons"
          @audit-click="handleAuditClick"
        />
      </template>
    </FuniDetail>
    
    <!-- 审核抽屉 -->
    <FuniBusAuditDrawer 
      :businessId="businessId"
      :sysId="sysId"
      :onlyShow="false"
      @audit-event="handleAuditEvent"
      @drawer-change="handleDrawerChange"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();
const businessId = ref(route.query.businessId);
const sysId = ref(route.query.sysId);

const steps = ref([
  { title: '申请信息', slot: 'applicationInfo' },
  { title: '附件材料', slot: 'attachments' }
]);

const mockAuditButtons = ref([
  {
    key: '同意',
    action: 'APPROVE',
    props: { type: 'primary' }
  },
  {
    key: '拒绝',
    action: 'REJECT',
    props: { type: 'danger' }
  }
]);

const fileTableParams = computed(() => ({
  businessId: businessId.value,
  sysId: sysId.value
}));

const handleAuditClick = (action) => {
  console.log('审核按钮点击:', action);
  // 审核按钮点击后，抽屉会自动打开
};

const handleAuditEvent = (event) => {
  console.log('审核完成:', event);
  ElMessage.success('审核操作完成');
  // 跳转到列表页
  router.push('/audit/list');
};

const handleDrawerChange = (visible) => {
  console.log('抽屉状态变化:', visible);
};
</script>
```

### 2. 请假审批示例

```vue
<template>
  <div class="leave-audit-page">
    <FuniDetail 
      :steps="leaveSteps" 
      bizName="审核"
      :businessId="leaveId"
      sysId="LEAVE_SYSTEM"
    >
      <!-- 请假信息 -->
      <template #leaveInfo>
        <FuniForm 
          :schema="leaveSchema" 
          v-model="leaveData" 
          :readonly="true" 
        />
      </template>
      
      <!-- 请假附件 -->
      <template #leaveAttachments>
        <FuniFileTable 
          :params="{ businessId: leaveId, sysId: 'LEAVE_SYSTEM' }" 
          :onlyShow="true" 
        />
      </template>
      
      <!-- 审核按钮 -->
      <template #auditbtns="{ auditButtons }">
        <FuniAuditButtomBtn
          :auditButtons="auditButtons || leaveAuditButtons"
          @audit-click="handleLeaveAudit"
        />
      </template>
    </FuniDetail>
    
    <!-- 请假审核抽屉 -->
    <FuniBusAuditDrawer 
      :businessId="leaveId"
      sysId="LEAVE_SYSTEM"
      businessConfigCode="LEAVE_APPLICATION"
      :onlyShow="false"
      :beforeAuditFn="beforeLeaveAudit"
      @audit-event="handleLeaveAuditEvent"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';

const leaveId = ref('LEAVE_202401001');

const leaveAuditButtons = ref([
  {
    key: '部门同意',
    action: 'DEPT_APPROVE',
    props: { type: 'primary' }
  },
  {
    key: '人事确认',
    action: 'HR_CONFIRM',
    props: { type: 'success' }
  },
  {
    key: '需要补假条',
    action: 'NEED_LEAVE_NOTE',
    props: { type: 'warning' }
  }
]);

const leaveSteps = ref([
  { title: '请假信息', slot: 'leaveInfo' },
  { title: '请假附件', slot: 'leaveAttachments' }
]);

const beforeLeaveAudit = async (event) => {
  // 审核前的业务逻辑
  console.log('请假审核前处理:', event);
  
  // 可以返回额外的审核数据
  return {
    opinion: '部门审核意见',
    customAssigneeList: ['hr_manager'],
    businessVariableMap: {
      leaveType: 'annual',
      leaveDays: 3
    }
  };
};

const handleLeaveAudit = (action) => {
  console.log('请假审核动作:', action);
};

const handleLeaveAuditEvent = (event) => {
  console.log('请假审核完成:', event);
  ElMessage.success('请假审核完成');
  // 刷新页面或跳转
  location.reload();
};
</script>
```

## 高级示例

### 1. 表单集成审核意见

```vue
<template>
  <div class="form-audit-page">
    <FuniDetail 
      :steps="steps" 
      bizName="审核"
      :businessId="businessId"
      :sysId="sysId"
    >
      <!-- 业务信息 -->
      <template #businessInfo>
        <FuniForm 
          :schema="businessSchema" 
          v-model="businessData" 
          :readonly="true" 
        />
      </template>
      
      <!-- 审核意见表单 -->
      <template #auditOpinion>
        <FuniForm 
          :schema="auditOpinionSchema" 
          v-model="auditOpinionData" 
        />
      </template>
      
      <!-- 审核按钮 -->
      <template #auditbtns="{ auditButtons }">
        <FuniAuditButtomBtn
          :auditButtons="auditButtons || mockAuditButtons"
          @audit-click="handleAuditClick"
        />
      </template>
    </FuniDetail>
    
    <!-- 表单集成审核抽屉 -->
    <FuniBusAuditDrawer 
      :businessId="businessId"
      :sysId="sysId"
      :onlyShow="false"
      :isFormOpinion="true"
      :beforeAuditFn="beforeFormAudit"
      @audit-event="handleAuditEvent"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';

const businessId = ref('BIZ202401001');
const sysId = ref('EXPENSE_SYSTEM');

const auditOpinionData = ref({
  opinion: '',
  auditResult: '',
  nextAssignee: ''
});

const auditOpinionSchema = ref([
  {
    field: 'opinion',
    label: '审核意见',
    component: 'el-input',
    props: {
      type: 'textarea',
      rows: 4,
      placeholder: '请输入审核意见'
    },
    rules: [
      { required: true, message: '请输入审核意见' }
    ]
  },
  {
    field: 'auditResult',
    label: '审核结果',
    component: 'el-select',
    props: {
      placeholder: '请选择审核结果'
    },
    options: [
      { label: '同意', value: 'APPROVE' },
      { label: '拒绝', value: 'REJECT' }
    ]
  }
]);

const beforeFormAudit = async (event) => {
  // 从表单获取审核意见
  return {
    opinion: auditOpinionData.value.opinion,
    businessJson: {
      auditResult: auditOpinionData.value.auditResult,
      nextAssignee: auditOpinionData.value.nextAssignee
    }
  };
};

const handleAuditClick = (action) => {
  console.log('表单审核动作:', action);
};

const handleAuditEvent = (event) => {
  console.log('表单审核完成:', event);
  ElMessage.success('审核提交成功');
};
</script>
```

### 2. 批量审核示例

```vue
<template>
  <div class="batch-audit-page">
    <el-table 
      :data="auditList" 
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="title" label="标题" />
      <el-table-column prop="applicant" label="申请人" />
      <el-table-column prop="createTime" label="申请时间" />
      <el-table-column label="操作">
        <template #default="{ row }">
          <el-button 
            size="small" 
            @click="openSingleAudit(row)"
          >
            审核
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 批量操作按钮 -->
    <div class="batch-actions" v-if="selectedItems.length > 0">
      <el-button 
        type="primary" 
        @click="openBatchAudit"
      >
        批量审核 ({{ selectedItems.length }})
      </el-button>
    </div>
    
    <!-- 单个审核抽屉 -->
    <FuniBusAuditDrawer 
      v-if="currentAuditItem"
      :businessId="currentAuditItem.businessId"
      :sysId="currentAuditItem.sysId"
      :onlyShow="false"
      @audit-event="handleSingleAuditEvent"
      @drawer-change="handleDrawerChange"
    />
    
    <!-- 批量审核对话框 -->
    <el-dialog 
      v-model="batchAuditVisible" 
      title="批量审核"
      width="600px"
    >
      <div class="batch-audit-content">
        <div class="selected-items">
          <h4>选中的申请 ({{ selectedItems.length }}个)</h4>
          <ul>
            <li v-for="item in selectedItems" :key="item.id">
              {{ item.title }} - {{ item.applicant }}
            </li>
          </ul>
        </div>
        
        <el-form :model="batchAuditForm" label-width="100px">
          <el-form-item label="审核结果">
            <el-radio-group v-model="batchAuditForm.result">
              <el-radio label="APPROVE">批量同意</el-radio>
              <el-radio label="REJECT">批量拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="审核意见">
            <el-input 
              v-model="batchAuditForm.opinion"
              type="textarea"
              rows="4"
              placeholder="请输入批量审核意见"
            />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="batchAuditVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          :loading="batchSubmitting"
          @click="submitBatchAudit"
        >
          提交
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const auditList = ref([]);
const selectedItems = ref([]);
const currentAuditItem = ref(null);
const batchAuditVisible = ref(false);
const batchSubmitting = ref(false);

const batchAuditForm = ref({
  result: 'APPROVE',
  opinion: ''
});

const handleSelectionChange = (selection) => {
  selectedItems.value = selection;
};

const openSingleAudit = (item) => {
  currentAuditItem.value = item;
  // 抽屉会自动打开
};

const openBatchAudit = () => {
  batchAuditVisible.value = true;
};

const submitBatchAudit = async () => {
  batchSubmitting.value = true;
  
  try {
    const promises = selectedItems.value.map(item => 
      auditApi.submitAudit({
        businessId: item.businessId,
        sysId: item.sysId,
        result: batchAuditForm.value.result,
        opinion: batchAuditForm.value.opinion
      })
    );
    
    await Promise.all(promises);
    
    ElMessage.success(`批量审核完成，共处理 ${selectedItems.value.length} 个申请`);
    batchAuditVisible.value = false;
    selectedItems.value = [];
    
    // 刷新列表
    await loadAuditList();
    
  } catch (error) {
    ElMessage.error('批量审核失败：' + error.message);
  } finally {
    batchSubmitting.value = false;
  }
};

const handleSingleAuditEvent = (event) => {
  console.log('单个审核完成:', event);
  currentAuditItem.value = null;
  // 刷新列表
  loadAuditList();
};

const handleDrawerChange = (visible) => {
  if (!visible) {
    currentAuditItem.value = null;
  }
};
</script>

<style scoped>
.batch-actions {
  margin-top: 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
}

.batch-audit-content {
  .selected-items {
    margin-bottom: 20px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 4px;
    
    h4 {
      margin: 0 0 10px 0;
      color: #333;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 5px;
        color: #666;
      }
    }
  }
}
</style>
```

## 自定义配置示例

### 1. 自定义审核前处理

```vue
<template>
  <FuniBusAuditDrawer 
    :businessId="businessId"
    :sysId="sysId"
    :beforeAuditFn="customBeforeAudit"
    @audit-event="handleAuditEvent"
  />
</template>

<script setup>
import { ref } from 'vue';

const businessId = ref('BIZ202401001');
const sysId = ref('CUSTOM_SYSTEM');

const customBeforeAudit = async (event) => {
  console.log('自定义审核前处理:', event);
  
  // 可以进行权限检查
  const hasPermission = await checkAuditPermission(event.businessExecutionType);
  if (!hasPermission) {
    throw new Error('您没有执行此操作的权限');
  }
  
  // 可以获取额外的业务数据
  const businessData = await getBusinessData(businessId.value);
  
  // 可以进行业务规则验证
  if (event.businessExecutionType === 'APPROVE' && businessData.amount > 10000) {
    const confirmed = await ElMessageBox.confirm(
      '金额超过1万元，确认要审核通过吗？',
      '确认审核',
      { type: 'warning' }
    );
    
    if (!confirmed) {
      throw new Error('用户取消操作');
    }
  }
  
  // 返回额外的审核数据
  return {
    opinion: `系统自动生成：${event.businessExecutionName}`,
    customAssigneeList: businessData.nextAssignees,
    businessVariableMap: {
      auditTime: new Date().toISOString(),
      auditAmount: businessData.amount
    },
    businessJson: {
      originalData: businessData,
      auditContext: {
        userAgent: navigator.userAgent,
        timestamp: Date.now()
      }
    }
  };
};

const handleAuditEvent = (event) => {
  console.log('自定义审核完成:', event);
  ElMessage.success('审核操作完成');
};
</script>
```

### 2. 多系统集成示例

```vue
<template>
  <div class="multi-system-audit">
    <el-tabs v-model="activeSystem" @tab-change="handleSystemChange">
      <el-tab-pane 
        v-for="system in systems" 
        :key="system.id"
        :label="system.name" 
        :name="system.id"
      >
        <FuniBusAuditDrawer 
          :businessId="businessId"
          :sysId="system.id"
          :businessConfigCode="system.configCode"
          :onlyShow="system.readonly"
          :beforeAuditFn="getBeforeAuditFn(system)"
          @audit-event="handleSystemAuditEvent"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const businessId = ref('BIZ202401001');
const activeSystem = ref('LEAVE_SYSTEM');

const systems = ref([
  {
    id: 'LEAVE_SYSTEM',
    name: '请假系统',
    configCode: 'LEAVE_APPLICATION',
    readonly: false
  },
  {
    id: 'EXPENSE_SYSTEM',
    name: '报销系统',
    configCode: 'EXPENSE_APPLICATION',
    readonly: false
  },
  {
    id: 'APPROVAL_SYSTEM',
    name: '审批系统',
    configCode: 'GENERAL_APPROVAL',
    readonly: true
  }
]);

const getBeforeAuditFn = (system) => {
  return async (event) => {
    switch (system.id) {
      case 'LEAVE_SYSTEM':
        return await handleLeaveAudit(event);
      case 'EXPENSE_SYSTEM':
        return await handleExpenseAudit(event);
      case 'APPROVAL_SYSTEM':
        return await handleGeneralAudit(event);
      default:
        return {};
    }
  };
};

const handleLeaveAudit = async (event) => {
  // 请假系统特定逻辑
  return {
    opinion: '请假审核意见',
    businessVariableMap: { leaveType: 'annual' }
  };
};

const handleExpenseAudit = async (event) => {
  // 报销系统特定逻辑
  return {
    opinion: '报销审核意见',
    businessVariableMap: { expenseType: 'travel' }
  };
};

const handleGeneralAudit = async (event) => {
  // 通用审批逻辑
  return {
    opinion: '通用审核意见'
  };
};

const handleSystemChange = (systemId) => {
  console.log('切换到系统:', systemId);
};

const handleSystemAuditEvent = (event) => {
  console.log('系统审核完成:', activeSystem.value, event);
};
</script>
```
