# FuniBusAuditDrawer 配置规范

## 组件配置概述

FuniBusAuditDrawer是工作流审核意见抽屉组件，基于ElementPlus的el-drawer封装，通过iframe集成审核系统。组件配置包括抽屉属性、业务参数、显示控制和交互行为等多个方面。

## 核心配置结构

### 1. 组件Props配置

```typescript
interface FuniBusAuditDrawerProps {
  // 业务配置
  businessId: string;                    // 业务ID（必填）
  sysId?: string;                        // 系统ID
  businessConfigCode?: string;           // 业务配置编码
  
  // 显示控制
  onlyShow?: boolean;                    // 是否仅显示模式
  isHideUnfold?: boolean;               // 是否隐藏展开功能
  isDefaultOperate?: boolean;           // 是否执行默认操作
  isAuthFixedBtn?: boolean;             // 是否鉴权固定按钮
  isFormOpinion?: boolean;              // 是否在表单中提供审核意见
  
  // 回调函数
  beforeAuditFn?: Function;             // 审核前执行方法
}
```

### 2. 抽屉配置结构

```typescript
interface DrawerConfig {
  size: number | string;                // 抽屉尺寸
  withHeader: boolean;                  // 是否显示头部
  modal: boolean;                       // 是否显示遮罩
  direction: 'ltr' | 'rtl' | 'ttb' | 'btt'; // 打开方向
  closeOnClickModal: boolean;           // 点击遮罩是否关闭
  closeOnPressEscape: boolean;          // ESC键是否关闭
  destroyOnClose: boolean;              // 关闭时是否销毁
}
```

### 3. iframe配置结构

```typescript
interface IframeConfig {
  src: string;                          // iframe源地址
  frameborder: string;                  // 边框设置
  scrolling: 'auto' | 'yes' | 'no';    // 滚动设置
  width: string;                        // 宽度
  height: string;                       // 高度
}
```

## 配置示例

### 1. 基础配置

```javascript
const basicConfig = {
  businessId: 'BIZ202401001',
  sysId: 'LEAVE_SYSTEM',
  onlyShow: true,
  isHideUnfold: false
};
```

### 2. 完整配置

```javascript
const fullConfig = {
  // 业务配置
  businessId: 'BIZ202401001',
  sysId: 'LEAVE_SYSTEM',
  businessConfigCode: 'LEAVE_APPLICATION',
  
  // 显示控制
  onlyShow: false,                      // 允许操作
  isHideUnfold: false,                  // 显示展开功能
  isDefaultOperate: true,               // 执行默认操作
  isAuthFixedBtn: true,                 // 鉴权固定按钮
  isFormOpinion: false,                 // 不在表单中提供意见
  
  // 回调函数
  beforeAuditFn: async (event) => {
    // 审核前的业务逻辑
    return {
      opinion: '预设审核意见',
      customAssigneeList: ['user1', 'user2'],
      businessVariableMap: { key: 'value' }
    };
  }
};
```

### 3. 只读模式配置

```javascript
const readOnlyConfig = {
  businessId: 'BIZ202401001',
  sysId: 'LEAVE_SYSTEM',
  onlyShow: true,                       // 仅显示模式
  isHideUnfold: true,                   // 隐藏展开功能
  isDefaultOperate: false,              // 不执行默认操作
  isAuthFixedBtn: false                 // 不鉴权固定按钮
};
```

### 4. 表单集成配置

```javascript
const formIntegratedConfig = {
  businessId: 'BIZ202401001',
  sysId: 'LEAVE_SYSTEM',
  onlyShow: false,
  isFormOpinion: true,                  // 在表单中提供审核意见
  beforeAuditFn: async (event) => {
    // 从表单获取审核意见
    const formData = getFormData();
    return {
      opinion: formData.auditOpinion,
      businessJson: formData
    };
  }
};
```

## 默认配置

### 1. 系统默认配置

```javascript
const defaultConfig = {
  // 业务配置
  businessId: '',                       // 必须提供
  sysId: '',                           // 可选，默认使用当前系统ID
  businessConfigCode: '',              // 可选
  
  // 显示控制
  onlyShow: true,                      // 默认仅显示
  isHideUnfold: false,                 // 默认显示展开功能
  isDefaultOperate: true,              // 默认执行默认操作
  isAuthFixedBtn: false,               // 默认不鉴权固定按钮
  isFormOpinion: false,                // 默认不在表单中提供意见
  
  // 回调函数
  beforeAuditFn: undefined             // 默认无回调
};
```

### 2. 抽屉默认配置

```javascript
const defaultDrawerConfig = {
  size: 400,                           // 默认宽度400px
  withHeader: false,                   // 不显示头部
  modal: true,                         // 显示遮罩
  direction: 'rtl',                    // 从右侧打开
  closeOnClickModal: true,             // 点击遮罩关闭
  closeOnPressEscape: true,            // ESC键关闭
  destroyOnClose: false                // 关闭时不销毁
};
```

## 配置验证规则

### 1. 必填字段验证

```typescript
const configValidationRules = {
  businessId: {
    required: true,
    type: 'string',
    message: 'businessId是必填字段'
  },
  sysId: {
    required: false,
    type: 'string',
    message: 'sysId必须为字符串类型'
  },
  onlyShow: {
    required: false,
    type: 'boolean',
    message: 'onlyShow必须为布尔类型'
  },
  beforeAuditFn: {
    required: false,
    type: 'function',
    message: 'beforeAuditFn必须为函数类型'
  }
};
```

### 2. 配置完整性检查

```javascript
function validateConfig(config) {
  const errors = [];
  
  // 检查必填字段
  if (!config.businessId) {
    errors.push('businessId不能为空');
  }
  
  // 检查字段类型
  if (config.onlyShow !== undefined && typeof config.onlyShow !== 'boolean') {
    errors.push('onlyShow必须为布尔类型');
  }
  
  if (config.beforeAuditFn !== undefined && typeof config.beforeAuditFn !== 'function') {
    errors.push('beforeAuditFn必须为函数类型');
  }
  
  // 检查业务逻辑
  if (config.isFormOpinion && config.onlyShow) {
    errors.push('表单意见模式下不能设置为仅显示模式');
  }
  
  return errors;
}
```

## URL配置规范

### 1. iframe URL构建

```javascript
const buildIframeUrl = (config) => {
  const baseUrl = `${getServerBaseApi()}/sdkapp/`;
  const params = new URLSearchParams({
    cookie: sessionStorage.getItem('token'),
    businessId: config.businessId,
    sysId: config.sysId || appStore.system.id,
    onlyShow: config.onlyShow || !config.isHideUnfold,
    isDefaultOperate: config.isDefaultOperate,
    isLoading: '2'
  });
  
  return `${baseUrl}#/newAudit?${params.toString()}`;
};
```

### 2. URL参数说明

| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| cookie | string | 用户认证token | 'eyJhbGciOiJIUzI1NiJ9...' |
| businessId | string | 业务ID | 'BIZ202401001' |
| sysId | string | 系统ID | 'LEAVE_SYSTEM' |
| onlyShow | boolean | 是否仅显示 | true/false |
| isDefaultOperate | boolean | 是否默认操作 | true/false |
| isLoading | string | 加载状态 | '2' |

## API配置规范

### 1. 默认API端点

```javascript
const defaultApiEndpoints = {
  // 获取业务流程实例信息
  getBusinessInfo: '/bpmn/businessManage/getBusinessProcessInstanceInfoByBusinessId',
  
  // 执行业务操作
  executeBusiness: '/bpmn/businessManage/executeBusiness',
  
  // 获取审核历史
  getAuditHistory: '/bpmn/audit/getAuditHistory',
  
  // 提交审核意见
  submitAuditOpinion: '/bpmn/audit/submitOpinion'
};
```

### 2. API请求配置

```javascript
const apiRequestConfig = {
  // 获取业务信息请求
  getBusinessInfoRequest: {
    method: 'POST',
    url: '/bpmn/businessManage/getBusinessProcessInstanceInfoByBusinessId',
    data: {
      sysId: 'SYSTEM_ID',
      businessId: 'BUSINESS_ID'
    }
  },
  
  // 执行业务请求
  executeBusinessRequest: {
    method: 'POST',
    url: '/bpmn/businessManage/executeBusiness',
    data: {
      opinion: '',                      // 审核意见
      sysId: '',                       // 系统ID
      businessId: '',                  // 业务ID
      businessExecutionType: '',       // 执行类型
      businessExecutionName: '',       // 执行名称
      customAssigneeList: [],          // 自定义指派人列表
      customCopyToOtherIds: [],        // 抄送人列表
      operateRemark: '',               // 操作备注
      businessVariableMap: {},         // 业务变量映射
      businessJson: {}                 // 业务JSON数据
    }
  }
};
```

## 事件配置规范

### 1. 组件事件配置

```javascript
const eventConfig = {
  // 抽屉状态变化事件
  drawerChange: {
    type: 'boolean',
    description: '抽屉显示/隐藏状态变化'
  },
  
  // 审核事件
  auditEvent: {
    type: 'object',
    description: '审核操作完成事件',
    payload: {
      businessExecutionType: 'string',
      businessExecutionName: 'string',
      opinion: 'string'
    }
  },
  
  // 工作变化事件
  workChange: {
    type: 'object',
    description: '工作流状态变化事件'
  },
  
  // 通用变化事件
  change: {
    type: 'boolean',
    description: '组件状态变化事件'
  }
};
```

### 2. iframe消息配置

```javascript
const iframeMessageConfig = {
  // 关闭抽屉消息
  closeModal: {
    cmd: 'closeModal',
    description: '关闭审核抽屉'
  },
  
  // 请求开始消息
  requestStart: {
    cmd: 'requestStart',
    description: '审核请求开始'
  },
  
  // 请求结束消息
  requestEnd: {
    cmd: 'requestEnd',
    description: '审核请求结束'
  }
};
```

## 样式配置规范

### 1. 抽屉样式配置

```scss
$drawer-config: (
  // 抽屉主体
  drawer-body-padding: 0,
  drawer-overlay-bg: rgba(0, 0, 0, 0),
  drawer-overlay-z-index: 2009,
  drawer-box-shadow: -2px 0px 4px rgba(0, 0, 0, 0.07),
  
  // 展开按钮
  fold-width: 20px,
  fold-height: 80px,
  fold-bg: rgba(254, 254, 255, 1),
  fold-border: 1px solid #eeeeee,
  fold-border-radius: 4px 0px 0px 4px,
  
  // 信息面板
  info-width: 40px,
  info-bg: rgba(254, 254, 255, 1),
  info-border: 1px solid #eeeeee
);
```

### 2. 响应式配置

```javascript
const responsiveConfig = {
  // 桌面端
  desktop: {
    drawerSize: 400,
    foldSize: 20,
    infoWidth: 40
  },
  
  // 平板端
  tablet: {
    drawerSize: 350,
    foldSize: 18,
    infoWidth: 35
  },
  
  // 移动端
  mobile: {
    drawerSize: '100%',
    foldSize: 16,
    infoWidth: 30
  }
};
```

## 配置最佳实践

### 1. 配置组织建议

```javascript
// 按业务模块组织配置
const configByModule = {
  // 请假模块配置
  leave: {
    businessConfigCode: 'LEAVE_APPLICATION',
    sysId: 'LEAVE_SYSTEM',
    isFormOpinion: true,
    beforeAuditFn: async (event) => {
      return await getLeaveAuditData(event);
    }
  },
  
  // 报销模块配置
  expense: {
    businessConfigCode: 'EXPENSE_APPLICATION',
    sysId: 'EXPENSE_SYSTEM',
    isFormOpinion: false,
    isAuthFixedBtn: true
  },
  
  // 通用配置
  common: {
    onlyShow: true,
    isHideUnfold: false,
    isDefaultOperate: true
  }
};
```

### 2. 环境配置

```javascript
// 开发环境配置
const developmentConfig = {
  apiBaseUrl: 'http://localhost:8080',
  iframeBaseUrl: 'http://localhost:3000/sdkapp',
  enableDebug: true,
  mockData: true
};

// 生产环境配置
const productionConfig = {
  apiBaseUrl: 'https://api.example.com',
  iframeBaseUrl: 'https://app.example.com/sdkapp',
  enableDebug: false,
  mockData: false
};
```
