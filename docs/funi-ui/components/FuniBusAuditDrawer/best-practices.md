# FuniBusAuditDrawer 最佳实践

## 1. 组件使用原则

### 1.1 使用场景限制

**✅ 正确使用场景**
- 工作流审核页面
- 需要查看审核历史的场景
- 需要填写审核意见的场景
- 与FuniAuditButtomBtn配合使用
- 需要iframe集成审核系统的场景

**❌ 错误使用场景**
- 普通对话框或抽屉需求
- 非工作流业务场景
- 静态内容展示
- 没有businessId的页面

```vue
<!-- ✅ 正确：工作流审核场景 -->
<FuniDetail bizName="审核" :businessId="businessId">
  <template #auditbtns>
    <FuniAuditButtomBtn @audit-click="handleAuditClick" />
  </template>
</FuniDetail>

<FuniBusAuditDrawer
  :businessId="businessId"
  :sysId="sysId"
  @audit-event="handleAuditEvent"
/>

<!-- ❌ 错误：普通对话框场景 -->
<FuniBusAuditDrawer>
  <div>普通内容展示</div>
</FuniBusAuditDrawer>
```

### 1.2 组件集成原则

**必须配合使用的组件**
- `FuniDetail`: 提供工作流页面结构
- `FuniAuditButtomBtn`: 提供审核按钮触发
- `FuniFileTable`: 提供工作流附件管理

```vue
<!-- ✅ 完整的工作流组件集成 -->
<template>
  <FuniDetail bizName="审核" :businessId="businessId">
    <!-- 业务内容 -->
    <template #content>
      <FuniForm :schema="schema" v-model="data" :readonly="true" />
      <FuniFileTable :params="fileParams" :onlyShow="true" />
    </template>
    
    <!-- 审核按钮 -->
    <template #auditbtns>
      <FuniAuditButtomBtn @audit-click="handleAuditClick" />
    </template>
  </FuniDetail>
  
  <!-- 审核抽屉 -->
  <FuniBusAuditDrawer
    :businessId="businessId"
    :sysId="sysId"
    @audit-event="handleAuditEvent"
  />
</template>
```

## 2. 配置最佳实践

### 2.1 业务参数配置

```javascript
// ✅ 推荐：完整的业务参数配置
const auditConfig = {
  businessId: 'BIZ202401001',          // 必填：业务ID
  sysId: 'LEAVE_SYSTEM',               // 推荐：明确指定系统ID
  businessConfigCode: 'LEAVE_APPLICATION', // 推荐：业务配置编码
  onlyShow: false,                     // 明确指定是否仅显示
  isDefaultOperate: true               // 明确指定是否默认操作
};

// ❌ 避免：不完整的配置
const badConfig = {
  businessId: 'BIZ202401001'
  // 缺少其他重要配置
};
```

### 2.2 显示模式配置

```javascript
// 根据页面类型配置显示模式
const getDisplayConfig = (pageType, userRole) => {
  switch (pageType) {
    case 'audit':
      return {
        onlyShow: false,              // 审核模式：允许操作
        isHideUnfold: false,          // 显示展开功能
        isFormOpinion: false          // 使用抽屉意见
      };
    
    case 'detail':
      return {
        onlyShow: true,               // 详情模式：仅显示
        isHideUnfold: true,           // 隐藏展开功能
        isFormOpinion: false
      };
    
    case 'form-audit':
      return {
        onlyShow: false,
        isHideUnfold: false,
        isFormOpinion: true           // 表单模式：在表单中填写意见
      };
    
    default:
      return {
        onlyShow: true,
        isHideUnfold: true,
        isFormOpinion: false
      };
  }
};
```

### 2.3 权限控制配置

```javascript
// 基于用户权限的配置
const getPermissionConfig = (userRole, businessStatus) => {
  const config = {
    onlyShow: true,                   // 默认只读
    isAuthFixedBtn: false,            // 默认不鉴权固定按钮
    beforeAuditFn: undefined          // 默认无前置处理
  };
  
  // 根据用户角色调整权限
  if (['admin', 'auditor', 'manager'].includes(userRole)) {
    config.onlyShow = false;          // 允许操作
    config.isAuthFixedBtn = true;     // 启用按钮鉴权
  }
  
  // 根据业务状态调整权限
  if (businessStatus === 'locked' || businessStatus === 'completed') {
    config.onlyShow = true;           // 强制只读
  }
  
  return config;
};
```

## 3. 事件处理最佳实践

### 3.1 审核事件处理结构

```vue
<script setup>
const handleAuditEvent = async (event) => {
  console.log('审核事件:', event);
  
  try {
    // 1. 显示成功提示
    ElMessage.success('审核操作完成');
    
    // 2. 更新本地状态
    await updateLocalState(event);
    
    // 3. 刷新相关数据
    await refreshRelatedData();
    
    // 4. 页面跳转或状态更新
    handlePostAuditAction(event);
    
  } catch (error) {
    console.error('审核后处理失败:', error);
    ElMessage.error('操作完成，但页面更新失败');
  }
};

const updateLocalState = async (event) => {
  // 更新本地业务状态
  businessStatus.value = event.newStatus;
  auditHistory.value.unshift(event.auditRecord);
};

const refreshRelatedData = async () => {
  // 刷新相关组件数据
  await Promise.all([
    refreshFileTable(),
    refreshAuditButtons(),
    refreshBusinessData()
  ]);
};

const handlePostAuditAction = (event) => {
  // 根据审核结果决定后续动作
  switch (event.businessExecutionType) {
    case 'AGREE':
      router.push('/audit/list');
      break;
    case 'REJECT':
      // 保持在当前页面，显示拒绝结果
      break;
    case 'RETURN':
      ElMessage.info('已退回，等待重新提交');
      break;
  }
};
</script>
```

### 3.2 抽屉状态管理

```vue
<script setup>
import { ref, watch } from 'vue';

const drawerVisible = ref(false);
const drawerLoading = ref(false);

// 监听抽屉状态变化
const handleDrawerChange = (visible) => {
  drawerVisible.value = visible;
  
  if (visible) {
    // 抽屉打开时的处理
    onDrawerOpen();
  } else {
    // 抽屉关闭时的处理
    onDrawerClose();
  }
};

const onDrawerOpen = () => {
  console.log('审核抽屉打开');
  // 可以在这里加载额外数据
  loadAuditContext();
};

const onDrawerClose = () => {
  console.log('审核抽屉关闭');
  // 清理临时状态
  clearTempState();
};

const loadAuditContext = async () => {
  drawerLoading.value = true;
  try {
    // 加载审核上下文数据
    const context = await auditApi.getAuditContext(businessId.value);
    auditContext.value = context;
  } catch (error) {
    console.error('加载审核上下文失败:', error);
  } finally {
    drawerLoading.value = false;
  }
};

const clearTempState = () => {
  // 清理临时状态
  tempAuditData.value = {};
  validationErrors.value = [];
};
</script>
```

## 4. 性能优化实践

### 4.1 iframe优化

```javascript
// iframe加载优化
const optimizeIframeLoading = () => {
  // 1. 预加载iframe内容
  const preloadIframe = () => {
    if (!drawerVisible.value) {
      // 在抽屉打开前预加载
      const iframe = document.createElement('iframe');
      iframe.src = buildIframeUrl();
      iframe.style.display = 'none';
      document.body.appendChild(iframe);
      
      // 预加载完成后移除
      iframe.onload = () => {
        document.body.removeChild(iframe);
      };
    }
  };
  
  // 2. 缓存iframe URL
  const cachedUrls = new Map();
  const getCachedUrl = (businessId, sysId) => {
    const key = `${businessId}-${sysId}`;
    if (!cachedUrls.has(key)) {
      cachedUrls.set(key, buildIframeUrl(businessId, sysId));
    }
    return cachedUrls.get(key);
  };
  
  // 3. 延迟加载非关键内容
  const lazyLoadContent = () => {
    nextTick(() => {
      // 在下一个tick中加载非关键内容
      loadNonCriticalData();
    });
  };
};
```

### 4.2 数据加载优化

```javascript
// 数据加载优化策略
const optimizeDataLoading = () => {
  // 1. 并行加载数据
  const loadDataParallel = async () => {
    const [businessInfo, auditHistory, userPermissions] = await Promise.all([
      auditApi.getBusinessInfo(businessId.value),
      auditApi.getAuditHistory(businessId.value),
      auditApi.getUserPermissions(businessId.value)
    ]);
    
    return { businessInfo, auditHistory, userPermissions };
  };
  
  // 2. 分页加载历史记录
  const loadHistoryPaginated = async (page = 1, pageSize = 10) => {
    const history = await auditApi.getAuditHistory({
      businessId: businessId.value,
      page,
      pageSize
    });
    
    if (page === 1) {
      auditHistory.value = history.data;
    } else {
      auditHistory.value.push(...history.data);
    }
    
    return history;
  };
  
  // 3. 缓存常用数据
  const cacheManager = {
    cache: new Map(),
    
    get(key) {
      const item = this.cache.get(key);
      if (item && Date.now() - item.timestamp < 5 * 60 * 1000) { // 5分钟缓存
        return item.data;
      }
      return null;
    },
    
    set(key, data) {
      this.cache.set(key, {
        data,
        timestamp: Date.now()
      });
    }
  };
};
```

### 4.3 内存管理

```vue
<script setup>
import { onUnmounted } from 'vue';

// 内存泄漏防护
const memoryManagement = () => {
  const eventListeners = [];
  const timers = [];
  const observers = [];
  
  // 添加事件监听器
  const addEventListener = (element, event, handler) => {
    element.addEventListener(event, handler);
    eventListeners.push({ element, event, handler });
  };
  
  // 添加定时器
  const addTimer = (timer) => {
    timers.push(timer);
  };
  
  // 添加观察器
  const addObserver = (observer) => {
    observers.push(observer);
  };
  
  // 清理所有资源
  const cleanup = () => {
    // 清理事件监听器
    eventListeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler);
    });
    
    // 清理定时器
    timers.forEach(timer => {
      clearTimeout(timer);
      clearInterval(timer);
    });
    
    // 清理观察器
    observers.forEach(observer => {
      observer.disconnect();
    });
    
    // 清理缓存
    cacheManager.cache.clear();
  };
  
  // 组件卸载时清理
  onUnmounted(cleanup);
  
  return { addEventListener, addTimer, addObserver, cleanup };
};

const { addEventListener, addTimer, addObserver } = memoryManagement();
</script>
```

## 5. 错误处理最佳实践

### 5.1 iframe通信错误处理

```javascript
// iframe消息通信错误处理
const handleIframeErrors = () => {
  const messageHandler = (event) => {
    try {
      // 验证消息来源
      if (!isValidOrigin(event.origin)) {
        console.warn('收到来自未知源的消息:', event.origin);
        return;
      }
      
      // 验证消息格式
      if (!event.data || !event.data.cmd) {
        console.warn('收到格式错误的消息:', event.data);
        return;
      }
      
      // 处理具体消息
      handleIframeMessage(event.data);
      
    } catch (error) {
      console.error('处理iframe消息时发生错误:', error);
      ElMessage.error('系统通信异常，请刷新页面重试');
    }
  };
  
  const isValidOrigin = (origin) => {
    const allowedOrigins = [
      window.location.origin,
      process.env.VUE_APP_IFRAME_ORIGIN
    ];
    return allowedOrigins.includes(origin);
  };
  
  const handleIframeMessage = (data) => {
    switch (data.cmd) {
      case 'closeModal':
        handleCloseModal();
        break;
      case 'requestStart':
        handleRequestStart();
        break;
      case 'requestEnd':
        handleRequestEnd();
        break;
      case 'error':
        handleIframeError(data.error);
        break;
      default:
        console.warn('未知的iframe消息类型:', data.cmd);
    }
  };
  
  const handleIframeError = (error) => {
    console.error('iframe内部错误:', error);
    ElMessage.error('审核系统发生错误：' + error.message);
  };
  
  window.addEventListener('message', messageHandler);
  
  return () => {
    window.removeEventListener('message', messageHandler);
  };
};
```

### 5.2 API错误处理

```javascript
// API调用错误处理
const handleApiErrors = () => {
  const apiErrorHandler = async (apiCall, fallbackAction) => {
    try {
      return await apiCall();
    } catch (error) {
      console.error('API调用失败:', error);
      
      // 根据错误类型进行不同处理
      switch (error.code) {
        case 'NETWORK_ERROR':
          ElMessage.error('网络连接异常，请检查网络后重试');
          break;
        case 'PERMISSION_DENIED':
          ElMessage.error('权限不足，无法执行此操作');
          break;
        case 'BUSINESS_LOCKED':
          ElMessage.error('业务已被锁定，无法操作');
          break;
        case 'TIMEOUT':
          ElMessage.error('请求超时，请重试');
          break;
        default:
          ElMessage.error('操作失败：' + (error.message || '未知错误'));
      }
      
      // 执行回退操作
      if (fallbackAction) {
        await fallbackAction(error);
      }
      
      throw error;
    }
  };
  
  return { apiErrorHandler };
};
```

## 6. 安全最佳实践

### 6.1 iframe安全

```javascript
// iframe安全配置
const secureIframeConfig = {
  // 1. 设置安全的iframe属性
  sandbox: 'allow-scripts allow-same-origin allow-forms',
  
  // 2. 验证iframe源
  validateSource: (src) => {
    const allowedDomains = [
      process.env.VUE_APP_AUDIT_DOMAIN,
      window.location.hostname
    ];
    
    try {
      const url = new URL(src);
      return allowedDomains.includes(url.hostname);
    } catch {
      return false;
    }
  },
  
  // 3. 消息验证
  validateMessage: (event) => {
    // 验证来源
    if (!secureIframeConfig.validateSource(event.origin)) {
      return false;
    }
    
    // 验证消息结构
    if (!event.data || typeof event.data !== 'object') {
      return false;
    }
    
    // 验证必要字段
    if (!event.data.cmd || typeof event.data.cmd !== 'string') {
      return false;
    }
    
    return true;
  }
};
```

### 6.2 数据安全

```javascript
// 数据安全处理
const secureDataHandling = {
  // 1. 敏感数据脱敏
  maskSensitiveData: (data) => {
    const sensitiveFields = ['phone', 'idCard', 'bankAccount'];
    const masked = { ...data };
    
    sensitiveFields.forEach(field => {
      if (masked[field]) {
        masked[field] = maskString(masked[field]);
      }
    });
    
    return masked;
  },
  
  // 2. 输入验证
  validateInput: (input) => {
    // XSS防护
    const cleanInput = DOMPurify.sanitize(input);
    
    // SQL注入防护
    const sqlPattern = /('|(\\')|(;)|(\\;)|(--)|(\s*(union|select|insert|delete|update|drop|create|alter|exec|execute)\s*)/i;
    if (sqlPattern.test(cleanInput)) {
      throw new Error('输入包含非法字符');
    }
    
    return cleanInput;
  },
  
  // 3. 权限验证
  checkPermission: (action, resource) => {
    const userPermissions = getCurrentUserPermissions();
    return userPermissions.some(permission => 
      permission.action === action && permission.resource === resource
    );
  }
};
```

## 7. 测试最佳实践

### 7.1 单元测试

```javascript
// 组件单元测试
describe('FuniBusAuditDrawer', () => {
  test('应该正确渲染基础结构', () => {
    const wrapper = mount(FuniBusAuditDrawer, {
      props: {
        businessId: 'TEST_001',
        sysId: 'TEST_SYSTEM'
      }
    });
    
    expect(wrapper.find('#auditDrawer')).toBeTruthy();
    expect(wrapper.find('.el-drawer')).toBeTruthy();
  });
  
  test('应该正确处理审核事件', async () => {
    const mockAuditEvent = jest.fn();
    const wrapper = mount(FuniBusAuditDrawer, {
      props: {
        businessId: 'TEST_001',
        sysId: 'TEST_SYSTEM'
      },
      emits: {
        'audit-event': mockAuditEvent
      }
    });
    
    // 模拟审核操作
    await wrapper.vm.auditEvent({
      businessExecutionType: 'AGREE',
      opinion: '测试审核意见'
    });
    
    expect(mockAuditEvent).toHaveBeenCalledWith({
      businessExecutionType: 'AGREE',
      opinion: '测试审核意见'
    });
  });
});
```

### 7.2 集成测试

```javascript
// 工作流集成测试
describe('工作流审核集成测试', () => {
  test('完整的审核流程', async () => {
    // 1. 渲染审核页面
    const wrapper = mount(AuditPage, {
      props: { businessId: 'TEST_001' }
    });
    
    // 2. 点击审核按钮
    await wrapper.find('[data-testid="audit-btn"]').trigger('click');
    
    // 3. 验证抽屉打开
    expect(wrapper.find('.el-drawer').isVisible()).toBe(true);
    
    // 4. 填写审核意见
    await wrapper.find('textarea').setValue('测试审核意见');
    
    // 5. 提交审核
    await wrapper.find('[data-testid="submit-btn"]').trigger('click');
    
    // 6. 验证API调用
    expect(mockAuditApi.submit).toHaveBeenCalledWith({
      businessId: 'TEST_001',
      opinion: '测试审核意见'
    });
  });
});
```
