# FuniBusAuditDrawer ElementPlus API支持

## API集成机制

FuniBusAuditDrawer基于ElementPlus的`el-drawer`组件封装，同时集成了`el-button`、`el-input`等多个ElementPlus组件，通过props透传和事件代理机制实现完整的ElementPlus API支持。

## 支持的ElementPlus Drawer属性

### 1. 基础抽屉属性

| 属性名 | 类型 | 默认值 | 说明 | 透传方式 |
|--------|------|--------|------|----------|
| size | string/number | 400 | 抽屉尺寸 | 直接透传 |
| direction | string | 'rtl' | 打开方向 | 直接透传 |
| modal | boolean | true | 是否显示遮罩 | 直接透传 |
| withHeader | boolean | false | 是否显示头部 | 直接透传 |
| title | string | '' | 抽屉标题 | 直接透传 |
| closeOnClickModal | boolean | true | 点击遮罩是否关闭 | 直接透传 |
| closeOnPressEscape | boolean | true | ESC键是否关闭 | 直接透传 |
| destroyOnClose | boolean | false | 关闭时是否销毁 | 直接透传 |

### 2. 抽屉事件支持

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| open | - | 打开事件 | 抽屉打开时 |
| opened | - | 打开完成事件 | 抽屉打开动画完成时 |
| close | - | 关闭事件 | 抽屉关闭时 |
| closed | - | 关闭完成事件 | 抽屉关闭动画完成时 |

## 支持的ElementPlus Button属性

### 1. 动态按钮配置

```javascript
// 组件内部动态按钮支持ElementPlus Button的所有属性
const buttonConfig = {
  size: 'default',                      // 按钮尺寸
  type: 'primary',                      // 按钮类型
  plain: false,                         // 是否朴素按钮
  text: false,                          // 是否文字按钮
  link: false,                          // 是否链接按钮
  loading: false,                       // 是否加载中
  disabled: false,                      // 是否禁用
  icon: '',                            // 图标
  round: false,                        // 是否圆角
  circle: false                        // 是否圆形
};
```

### 2. 按钮样式映射

```javascript
// 组件内部按钮样式映射逻辑
const getSize = (itemBtn) => {
  return itemBtn.size || 'default';
};

const getPrimary = (itemBtn) => {
  // 根据按钮配置返回对应的type
  switch (itemBtn.type) {
    case '1': return 'primary';
    case '2': return 'success';
    case '3': return 'warning';
    case '4': return 'danger';
    case '5': return 'info';
    default: return 'default';
  }
};
```

## 支持的ElementPlus Input属性

### 1. 审核意见输入框

```javascript
// 内部el-input组件支持的属性
const inputConfig = {
  type: 'textarea',                     // 输入框类型
  placeholder: '请输入审核意见',         // 占位符
  resize: 'none',                       // 是否可调整大小
  autosize: { minRows: 4, maxRows: 4 }, // 自动调整高度
  showWordLimit: true,                  // 是否显示字数统计
  maxlength: 1000,                      // 最大长度
  disabled: false,                      // 是否禁用
  readonly: false,                      // 是否只读
  clearable: true,                      // 是否可清空
  showPassword: false,                  // 是否显示密码切换
  prefixIcon: '',                       // 前缀图标
  suffixIcon: ''                        // 后缀图标
};
```

### 2. 输入框事件支持

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| input | value | 输入事件 | 输入内容变化时 |
| change | value | 变化事件 | 输入完成时 |
| focus | event | 聚焦事件 | 获得焦点时 |
| blur | event | 失焦事件 | 失去焦点时 |
| clear | - | 清空事件 | 点击清空按钮时 |

## 使用示例

### 1. 基础抽屉配置

```vue
<template>
  <FuniBusAuditDrawer
    :businessId="businessId"
    :sysId="sysId"
    :size="drawerSize"
    :direction="drawerDirection"
    :modal="showModal"
    :closeOnClickModal="closeOnModal"
    @open="handleDrawerOpen"
    @close="handleDrawerClose"
  />
</template>

<script setup>
import { ref } from 'vue';

const businessId = ref('BIZ202401001');
const sysId = ref('LEAVE_SYSTEM');

// ElementPlus Drawer属性配置
const drawerSize = ref(500);              // 自定义抽屉宽度
const drawerDirection = ref('rtl');       // 从右侧打开
const showModal = ref(true);              // 显示遮罩
const closeOnModal = ref(false);          // 点击遮罩不关闭

const handleDrawerOpen = () => {
  console.log('抽屉打开');
};

const handleDrawerClose = () => {
  console.log('抽屉关闭');
};
</script>
```

### 2. 响应式抽屉配置

```vue
<template>
  <FuniBusAuditDrawer
    :businessId="businessId"
    :sysId="sysId"
    :size="responsiveSize"
    :direction="responsiveDirection"
    :modal="responsiveModal"
  />
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

const businessId = ref('BIZ202401001');
const sysId = ref('LEAVE_SYSTEM');
const screenWidth = ref(window.innerWidth);

// 响应式抽屉配置
const responsiveSize = computed(() => {
  if (screenWidth.value < 768) {
    return '100%';                      // 移动端全屏
  } else if (screenWidth.value < 1200) {
    return 400;                         // 平板端固定宽度
  } else {
    return 500;                         // 桌面端较大宽度
  }
});

const responsiveDirection = computed(() => {
  return screenWidth.value < 768 ? 'btt' : 'rtl';  // 移动端从底部打开
});

const responsiveModal = computed(() => {
  return screenWidth.value < 768;       // 移动端显示遮罩
});

const handleResize = () => {
  screenWidth.value = window.innerWidth;
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>
```

### 3. 自定义样式配置

```vue
<template>
  <FuniBusAuditDrawer
    :businessId="businessId"
    :sysId="sysId"
    class="custom-audit-drawer"
  />
</template>

<script setup>
const businessId = ref('BIZ202401001');
const sysId = ref('LEAVE_SYSTEM');
</script>

<style>
/* 自定义抽屉样式 */
.custom-audit-drawer :deep(.el-drawer) {
  border-radius: 8px 0 0 8px;          /* 自定义圆角 */
  box-shadow: -4px 0 8px rgba(0, 0, 0, 0.15); /* 自定义阴影 */
}

.custom-audit-drawer :deep(.el-drawer__body) {
  padding: 0;                           /* 移除默认内边距 */
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); /* 渐变背景 */
}

.custom-audit-drawer :deep(.el-overlay) {
  background-color: rgba(0, 0, 0, 0.3); /* 自定义遮罩透明度 */
}

/* 自定义按钮样式 */
.custom-audit-drawer :deep(.el-button) {
  border-radius: 20px;                  /* 圆角按钮 */
  font-weight: 500;                     /* 字体粗细 */
  transition: all 0.3s ease;           /* 过渡动画 */
}

.custom-audit-drawer :deep(.el-button:hover) {
  transform: translateY(-2px);          /* 悬停效果 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 自定义输入框样式 */
.custom-audit-drawer :deep(.el-textarea__inner) {
  border-radius: 8px;                   /* 圆角输入框 */
  border: 2px solid #e4e7ed;           /* 自定义边框 */
  font-size: 14px;                     /* 字体大小 */
  line-height: 1.6;                    /* 行高 */
}

.custom-audit-drawer :deep(.el-textarea__inner:focus) {
  border-color: #409eff;                /* 聚焦边框颜色 */
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2); /* 聚焦阴影 */
}
</style>
```

## 动态配置示例

### 1. 运行时配置更新

```vue
<template>
  <div>
    <!-- 配置控制面板 -->
    <div class="config-panel">
      <el-form :model="drawerConfig" label-width="120px">
        <el-form-item label="抽屉尺寸">
          <el-slider 
            v-model="drawerConfig.size" 
            :min="200" 
            :max="800" 
            show-input
          />
        </el-form-item>
        
        <el-form-item label="打开方向">
          <el-radio-group v-model="drawerConfig.direction">
            <el-radio label="rtl">从右侧</el-radio>
            <el-radio label="ltr">从左侧</el-radio>
            <el-radio label="ttb">从顶部</el-radio>
            <el-radio label="btt">从底部</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="显示遮罩">
          <el-switch v-model="drawerConfig.modal" />
        </el-form-item>
        
        <el-form-item label="点击遮罩关闭">
          <el-switch v-model="drawerConfig.closeOnClickModal" />
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 动态配置的抽屉 -->
    <FuniBusAuditDrawer
      :businessId="businessId"
      :sysId="sysId"
      :size="drawerConfig.size"
      :direction="drawerConfig.direction"
      :modal="drawerConfig.modal"
      :closeOnClickModal="drawerConfig.closeOnClickModal"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';

const businessId = ref('BIZ202401001');
const sysId = ref('LEAVE_SYSTEM');

const drawerConfig = reactive({
  size: 400,
  direction: 'rtl',
  modal: true,
  closeOnClickModal: true
});
</script>

<style scoped>
.config-panel {
  margin-bottom: 20px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #f8f9fa;
}
</style>
```

### 2. 主题适配配置

```vue
<template>
  <FuniBusAuditDrawer
    :businessId="businessId"
    :sysId="sysId"
    :class="themeClass"
  />
</template>

<script setup>
import { ref, computed } from 'vue';

const businessId = ref('BIZ202401001');
const sysId = ref('LEAVE_SYSTEM');
const currentTheme = ref('light'); // 'light' | 'dark' | 'blue'

const themeClass = computed(() => {
  return `audit-drawer-theme-${currentTheme.value}`;
});
</script>

<style>
/* 浅色主题 */
.audit-drawer-theme-light :deep(.el-drawer) {
  background: #ffffff;
  color: #303133;
}

.audit-drawer-theme-light :deep(.el-button--primary) {
  background-color: #409eff;
  border-color: #409eff;
}

/* 深色主题 */
.audit-drawer-theme-dark :deep(.el-drawer) {
  background: #2d3748;
  color: #e2e8f0;
}

.audit-drawer-theme-dark :deep(.el-button--primary) {
  background-color: #4299e1;
  border-color: #4299e1;
}

.audit-drawer-theme-dark :deep(.el-textarea__inner) {
  background-color: #4a5568;
  border-color: #718096;
  color: #e2e8f0;
}

/* 蓝色主题 */
.audit-drawer-theme-blue :deep(.el-drawer) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
}

.audit-drawer-theme-blue :deep(.el-button--primary) {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: #ffffff;
}

.audit-drawer-theme-blue :deep(.el-button--primary:hover) {
  background-color: rgba(255, 255, 255, 0.3);
}
</style>
```

## 兼容性说明

### 1. ElementPlus版本兼容

- **支持版本**: ElementPlus 2.0+
- **推荐版本**: ElementPlus 2.3+
- **测试版本**: ElementPlus 2.4.x

### 2. API兼容性矩阵

| 功能 | ElementPlus 2.0 | ElementPlus 2.1+ | ElementPlus 2.3+ |
|------|-----------------|------------------|------------------|
| el-drawer基础功能 | ✅ | ✅ | ✅ |
| el-button动态配置 | ✅ | ✅ | ✅ |
| el-input表单功能 | ✅ | ✅ | ✅ |
| 响应式尺寸 | ✅ | ✅ | ✅ |
| 自定义主题 | ⚠️ | ✅ | ✅ |
| 深色模式 | ❌ | ⚠️ | ✅ |

### 3. 浏览器兼容性

| 浏览器 | 最低版本 | 推荐版本 | 说明 |
|--------|----------|----------|------|
| Chrome | 70+ | 90+ | 完全支持 |
| Firefox | 65+ | 85+ | 完全支持 |
| Safari | 12+ | 14+ | 完全支持 |
| Edge | 79+ | 90+ | 完全支持 |
| IE | ❌ | ❌ | 不支持 |

## 最佳实践

### 1. 性能优化

```javascript
// 推荐：使用computed计算动态属性
const drawerConfig = computed(() => ({
  size: screenWidth.value < 768 ? '100%' : 400,
  direction: screenWidth.value < 768 ? 'btt' : 'rtl',
  modal: screenWidth.value < 768
}));

// 避免：在模板中直接计算
// <FuniBusAuditDrawer :size="screenWidth < 768 ? '100%' : 400" />
```

### 2. 样式隔离

```vue
<style scoped>
/* 推荐：使用scoped样式 */
.audit-drawer :deep(.el-drawer) {
  /* 自定义样式 */
}
</style>
```

### 3. 事件处理

```javascript
// 推荐：统一的事件处理
const handleDrawerEvents = {
  open: () => console.log('抽屉打开'),
  close: () => console.log('抽屉关闭'),
  auditEvent: (event) => console.log('审核完成', event)
};
```
