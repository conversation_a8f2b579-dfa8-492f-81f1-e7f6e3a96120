# FuniBusAuditDrawer API文档

## 组件概述

FuniBusAuditDrawer是工作流审核意见抽屉组件，基于ElementPlus的el-drawer封装，用于显示和提交审核意见、查看审核历史，是工作流系统的核心组件之一。

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| modelValue | Boolean | false | - | 是否显示抽屉 |
| businessId | String | '' | ✅ | 业务ID |
| businessConfigCode | String | '' | - | 业务配置编码 |
| sysId | String | '' | - | 系统ID |
| title | String | '审核意见' | - | 抽屉标题 |
| width | String/Number | '600px' | - | 抽屉宽度 |
| direction | String | 'rtl' | - | 抽屉打开方向 |
| size | String | 'default' | - | 组件尺寸 |
| showClose | Boolean | true | - | 是否显示关闭按钮 |
| modal | Boolean | true | - | 是否需要遮罩层 |
| appendToBody | Boolean | false | - | 是否插入至body元素 |
| lockScroll | Boolean | true | - | 是否锁定滚动 |
| closeOnClickModal | Boolean | true | - | 是否可通过点击modal关闭 |
| closeOnPressEscape | Boolean | true | - | 是否可通过ESC关闭 |
| destroyOnClose | Boolean | false | - | 关闭时销毁子元素 |
| showHistory | Boolean | true | - | 是否显示审核历史 |
| showOpinion | Boolean | true | - | 是否显示意见输入 |
| opinionRequired | Boolean | false | - | 意见是否必填 |
| opinionMaxLength | Number | 500 | - | 意见最大长度 |
| opinionPlaceholder | String | '请输入审核意见...' | - | 意见输入框占位符 |
| historyTitle | String | '审核历史' | - | 历史记录标题 |
| submitText | String | '提交' | - | 提交按钮文本 |
| cancelText | String | '取消' | - | 取消按钮文本 |
| submitLoading | Boolean | false | - | 提交按钮加载状态 |
| autoLoadHistory | Boolean | true | - | 是否自动加载历史记录 |
| historyPageSize | Number | 20 | - | 历史记录分页大小 |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| update:modelValue | value: Boolean | 显示状态更新事件 | 抽屉显示/隐藏时 |
| open | - | 打开事件 | 抽屉打开时 |
| opened | - | 打开完成事件 | 抽屉打开动画完成时 |
| close | - | 关闭事件 | 抽屉关闭时 |
| closed | - | 关闭完成事件 | 抽屉关闭动画完成时 |
| submit | opinion: String | 提交审核意见事件 | 点击提交按钮时 |
| cancel | - | 取消事件 | 点击取消按钮时 |
| history-load | history: Array | 历史记录加载事件 | 历史记录加载完成时 |
| opinion-change | opinion: String | 意见变化事件 | 审核意见输入变化时 |

## Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| open | - | void | 打开抽屉 |
| close | - | void | 关闭抽屉 |
| submit | - | Promise | 提交审核意见 |
| loadHistory | - | Promise | 加载审核历史 |
| clearOpinion | - | void | 清空审核意见 |
| validate | - | Boolean | 验证表单 |

## Slots

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| default | - | 自定义内容区域 |
| header | { close, titleId, titleClass } | 自定义头部内容 |
| title | - | 自定义标题内容 |
| footer | - | 自定义底部内容 |
| opinion | { opinion, onChange } | 自定义意见输入区域 |
| history | { history, loading } | 自定义历史记录区域 |
| history-item | { item, index } | 自定义历史记录项 |

## 数据结构

### 审核历史数据结构
```typescript
interface AuditHistory {
  id: string;                      // 审核记录ID
  businessId: string;              // 业务ID
  action: string;                  // 审核动作
  actionName: string;              // 审核动作名称
  operator: string;                // 操作人
  operatorName: string;            // 操作人姓名
  operateTime: string;             // 操作时间
  opinion: string;                 // 审核意见
  result: string;                  // 审核结果
  resultName: string;              // 审核结果名称
  nodeName: string;                // 节点名称
  nodeCode: string;                // 节点编码
  duration?: number;               // 处理时长（秒）
  attachments?: AttachmentInfo[];  // 附件信息
  [key: string]: any;              // 其他扩展字段
}

interface AttachmentInfo {
  id: string;                      // 附件ID
  name: string;                    // 附件名称
  url: string;                     // 附件URL
  size: number;                    // 附件大小
  type: string;                    // 附件类型
}
```

### 提交参数结构
```typescript
interface SubmitParams {
  businessId: string;              // 业务ID
  opinion: string;                 // 审核意见
  action?: string;                 // 审核动作
  attachments?: string[];          // 附件ID列表
  [key: string]: any;              // 其他参数
}
```

## 使用示例

### 基础审核抽屉
```vue
<template>
  <div>
    <el-button @click="showAuditDrawer">查看审核意见</el-button>
    
    <FuniBusAuditDrawer
      v-model="auditDrawerVisible"
      :business-id="businessId"
      business-config-code="LEAVE_APPLICATION"
      @submit="handleSubmitOpinion"
      @history-load="handleHistoryLoad"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const auditDrawerVisible = ref(false)
const businessId = ref('BIZ202301001')

const showAuditDrawer = () => {
  auditDrawerVisible.value = true
}

const handleSubmitOpinion = async (opinion) => {
  try {
    await auditApi.submitOpinion({
      businessId: businessId.value,
      opinion
    })
    
    ElMessage.success('审核意见提交成功')
    auditDrawerVisible.value = false
  } catch (error) {
    ElMessage.error('提交失败：' + error.message)
  }
}

const handleHistoryLoad = (history) => {
  console.log('审核历史:', history)
}
</script>
```

### 必填意见的审核抽屉
```vue
<template>
  <FuniBusAuditDrawer
    v-model="auditDrawerVisible"
    :business-id="businessId"
    opinion-required
    :opinion-max-length="200"
    opinion-placeholder="请输入详细的审核意见，不少于10个字符"
    :submit-loading="submitLoading"
    @submit="handleSubmitOpinion"
  />
</template>

<script setup>
import { ref } from 'vue'

const auditDrawerVisible = ref(false)
const businessId = ref('BIZ202301001')
const submitLoading = ref(false)

const handleSubmitOpinion = async (opinion) => {
  if (opinion.length < 10) {
    ElMessage.warning('审核意见不能少于10个字符')
    return
  }
  
  submitLoading.value = true
  
  try {
    await auditApi.submitOpinion({
      businessId: businessId.value,
      opinion,
      action: 'APPROVE'
    })
    
    ElMessage.success('审核意见提交成功')
    auditDrawerVisible.value = false
  } catch (error) {
    ElMessage.error('提交失败：' + error.message)
  } finally {
    submitLoading.value = false
  }
}
</script>
```

### 自定义历史记录显示
```vue
<template>
  <FuniBusAuditDrawer
    v-model="auditDrawerVisible"
    :business-id="businessId"
    title="项目审核记录"
    width="800px"
  >
    <template #history="{ history, loading }">
      <div class="custom-history">
        <h3>审核流程</h3>
        <el-timeline v-if="!loading">
          <el-timeline-item
            v-for="(item, index) in history"
            :key="item.id"
            :timestamp="item.operateTime"
            :type="getTimelineType(item.result)"
            :icon="getTimelineIcon(item.result)"
          >
            <div class="timeline-content">
              <div class="timeline-header">
                <span class="node-name">{{ item.nodeName }}</span>
                <el-tag :type="getResultTagType(item.result)" size="small">
                  {{ item.resultName }}
                </el-tag>
              </div>
              <div class="timeline-operator">
                操作人：{{ item.operatorName }}
                <span v-if="item.duration" class="duration">
                  （耗时：{{ formatDuration(item.duration) }}）
                </span>
              </div>
              <div v-if="item.opinion" class="timeline-opinion">
                {{ item.opinion }}
              </div>
              <div v-if="item.attachments && item.attachments.length" class="timeline-attachments">
                <el-tag
                  v-for="attachment in item.attachments"
                  :key="attachment.id"
                  size="small"
                  @click="downloadAttachment(attachment)"
                >
                  <el-icon><Paperclip /></el-icon>
                  {{ attachment.name }}
                </el-tag>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
        <el-skeleton v-else :rows="3" animated />
      </div>
    </template>
  </FuniBusAuditDrawer>
</template>

<script setup>
import { ref } from 'vue'
import { Paperclip } from '@element-plus/icons-vue'

const auditDrawerVisible = ref(false)
const businessId = ref('BIZ202301001')

const getTimelineType = (result) => {
  const typeMap = {
    'APPROVED': 'success',
    'REJECTED': 'danger',
    'RETURNED': 'warning',
    'PENDING': 'info'
  }
  return typeMap[result] || 'info'
}

const getTimelineIcon = (result) => {
  const iconMap = {
    'APPROVED': 'Check',
    'REJECTED': 'Close',
    'RETURNED': 'Back',
    'PENDING': 'Clock'
  }
  return iconMap[result] || 'Clock'
}

const getResultTagType = (result) => {
  const tagTypeMap = {
    'APPROVED': 'success',
    'REJECTED': 'danger',
    'RETURNED': 'warning',
    'PENDING': 'info'
  }
  return tagTypeMap[result] || 'info'
}

const formatDuration = (seconds) => {
  if (seconds < 60) {
    return `${seconds}秒`
  } else if (seconds < 3600) {
    return `${Math.floor(seconds / 60)}分钟`
  } else if (seconds < 86400) {
    return `${Math.floor(seconds / 3600)}小时`
  } else {
    return `${Math.floor(seconds / 86400)}天`
  }
}

const downloadAttachment = (attachment) => {
  window.open(attachment.url, '_blank')
}
</script>

<style scoped>
.custom-history {
  padding: 20px;
}

.timeline-content {
  padding-left: 10px;
}

.timeline-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.node-name {
  font-weight: 500;
  font-size: 14px;
}

.timeline-operator {
  color: #666;
  font-size: 12px;
  margin-bottom: 8px;
}

.duration {
  color: #999;
}

.timeline-opinion {
  background: #f5f7fa;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 13px;
  line-height: 1.5;
  margin-bottom: 8px;
}

.timeline-attachments {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.timeline-attachments .el-tag {
  cursor: pointer;
}
</style>
```

### 自定义意见输入区域
```vue
<template>
  <FuniBusAuditDrawer
    v-model="auditDrawerVisible"
    :business-id="businessId"
    :show-opinion="false"
  >
    <template #opinion="{ opinion, onChange }">
      <div class="custom-opinion">
        <h4>审核意见</h4>
        <el-form :model="opinionForm" :rules="opinionRules" ref="opinionFormRef">
          <el-form-item label="审核结果" prop="result" required>
            <el-radio-group v-model="opinionForm.result">
              <el-radio label="APPROVE">同意</el-radio>
              <el-radio label="REJECT">拒绝</el-radio>
              <el-radio label="RETURN">退回</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item label="审核意见" prop="opinion">
            <el-input
              v-model="opinionForm.opinion"
              type="textarea"
              :rows="4"
              placeholder="请输入审核意见..."
              maxlength="500"
              show-word-limit
              @input="onChange"
            />
          </el-form-item>
          
          <el-form-item label="附件上传">
            <el-upload
              v-model:file-list="opinionForm.attachments"
              action="/api/upload"
              multiple
              :limit="3"
              :on-success="handleUploadSuccess"
            >
              <el-button size="small" type="primary">点击上传</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  只能上传jpg/png/pdf文件，且不超过2MB
                </div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
      </div>
    </template>
    
    <template #footer>
      <div class="custom-footer">
        <el-button @click="auditDrawerVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          :loading="submitLoading"
          @click="handleCustomSubmit"
        >
          提交审核
        </el-button>
      </div>
    </template>
  </FuniBusAuditDrawer>
</template>

<script setup>
import { ref, reactive } from 'vue'

const auditDrawerVisible = ref(false)
const businessId = ref('BIZ202301001')
const submitLoading = ref(false)
const opinionFormRef = ref()

const opinionForm = reactive({
  result: '',
  opinion: '',
  attachments: []
})

const opinionRules = {
  result: [
    { required: true, message: '请选择审核结果', trigger: 'change' }
  ],
  opinion: [
    { required: true, message: '请输入审核意见', trigger: 'blur' },
    { min: 5, message: '审核意见不能少于5个字符', trigger: 'blur' }
  ]
}

const handleUploadSuccess = (response, file) => {
  console.log('上传成功:', response, file)
}

const handleCustomSubmit = async () => {
  try {
    await opinionFormRef.value.validate()
    
    submitLoading.value = true
    
    await auditApi.submitOpinion({
      businessId: businessId.value,
      result: opinionForm.result,
      opinion: opinionForm.opinion,
      attachments: opinionForm.attachments.map(file => file.response?.id).filter(Boolean)
    })
    
    ElMessage.success('审核提交成功')
    auditDrawerVisible.value = false
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}
</script>

<style scoped>
.custom-opinion {
  padding: 20px;
}

.custom-footer {
  padding: 16px 20px;
  border-top: 1px solid #ebeef5;
  text-align: right;
}
</style>
```

### 只读模式（仅查看历史）
```vue
<template>
  <FuniBusAuditDrawer
    v-model="auditDrawerVisible"
    :business-id="businessId"
    title="审核记录查看"
    :show-opinion="false"
    :show-close="true"
  >
    <template #footer>
      <div style="text-align: center; padding: 16px;">
        <el-button @click="auditDrawerVisible = false">关闭</el-button>
      </div>
    </template>
  </FuniBusAuditDrawer>
</template>

<script setup>
import { ref } from 'vue'

const auditDrawerVisible = ref(false)
const businessId = ref('BIZ202301001')
</script>
```

## API集成

### 默认API接口
```typescript
// 获取审核历史
GET /api/workflow/audit/history?businessId={businessId}&pageNum=1&pageSize=20

// 提交审核意见
POST /api/workflow/audit/submit
{
  "businessId": "BIZ202301001",
  "opinion": "审核意见内容",
  "action": "APPROVE",
  "attachments": ["file1", "file2"]
}
```

### 自定义API配置
```vue
<script setup>
// 可以通过全局配置或props传入自定义API
const apiConfig = {
  historyApi: '/custom/api/audit/history',
  submitApi: '/custom/api/audit/submit'
}
</script>
```

## 注意事项

### 1. 工作流集成
- 必须提供businessId参数
- 需要配合工作流系统使用
- 支持多种审核动作和结果

### 2. 权限控制
- 根据用户权限显示不同的操作
- 支持只读模式
- 可以控制意见是否必填

### 3. 数据格式
- 统一审核历史数据格式
- 支持附件信息显示
- 时间格式标准化

### 4. 用户体验
- 提供加载状态反馈
- 支持键盘操作
- 合理的错误提示

## 常见问题

### Q: 如何自定义审核历史的显示格式？
A: 使用history插槽自定义历史记录的显示内容和样式

### Q: 如何实现审核意见的模板功能？
A: 在opinion插槽中添加模板选择功能，或者提供快捷意见按钮

### Q: 如何处理大量审核历史的性能问题？
A: 使用分页加载，设置合适的pageSize，支持虚拟滚动

### Q: 如何集成文件上传功能？
A: 在自定义opinion插槽中集成el-upload组件，处理文件上传逻辑
