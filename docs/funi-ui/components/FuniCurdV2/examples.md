# FuniCurdV2 使用示例

## 基础示例

### 简单数据表格

⚠️ **注意**：根据项目配置规则，除操作列外，其他列默认不设置 width 值。

```vue
<template>
  <funi-curd-v2 :columns="columns" :data="tableData" stripe border />
</template>

<script setup>
import { ref } from 'vue';

const columns = ref([
  { label: 'ID', prop: 'id' }, // 不设置width，自动撑开
  { label: '姓名', prop: 'name' },
  { label: '年龄', prop: 'age' },
  { label: '地址', prop: 'address' } // 不设置width，自动撑开
]);

const tableData = ref([
  { id: 1, name: '张三', age: 25, address: '北京市朝阳区' },
  { id: 2, name: '李四', age: 30, address: '上海市浦东新区' },
  { id: 3, name: '王五', age: 28, address: '广州市天河区' }
]);
</script>
```

### 带分页的远程数据表格

**重要**：使用 `lodaData` 属性（注意拼写）进行远程数据加载。

```vue
<template>
  <FuniCurdV2 :columns="columns" :lodaData="loadData" :loading="loading" :pagination="true" />
</template>

<script setup>
import { ref } from 'vue';

const columns = ref([
  { label: 'ID', prop: 'id', sortable: true }, // 不设置width
  { label: '用户名', prop: 'username' },
  { label: '邮箱', prop: 'email' },
  { label: '状态', prop: 'status' },
  { label: '创建时间', prop: 'createTime', sortable: true }
]);

const loading = ref(false);

// lodaData 函数接收两个参数：pageParams 和 queryParams
const loadData = async (pageParams, queryParams) => {
  loading.value = true;
  try {
    // pageParams: { pageNo, pageSize, pageIndex }
    // queryParams: { groups: [...], sorts: [...] }
    const response = await userApi.getList({
      ...pageParams,
      ...queryParams
    });

    return {
      list: response.data.list,
      total: response.data.total
    };
  } catch (error) {
    console.error('加载数据失败:', error);
    return { list: [], total: 0 };
  } finally {
    loading.value = false;
  }
};
</script>
```

## 高级功能示例

### 带搜索功能的表格

**重要**：FuniCurdV2 内置了读取远程高级搜索配置的逻辑，通常无需配置 `searchConfig`，只需配置 `queryFields`。

```vue
<template>
  <FuniCurdV2 :columns="columns" :lodaData="loadData" :isShowSearch="true" :queryFields="queryFields" @search="handleSearch" />
</template>

<script setup>
import { ref } from 'vue';

const columns = ref([
  { label: 'ID', prop: 'id' },
  { label: '用户名', prop: 'username' },
  { label: '邮箱', prop: 'email' },
  { label: '状态', prop: 'status' }
]);

const queryFields = [
  {
    field: 'username',
    label: '用户名',
    component: 'input',
    props: { placeholder: '请输入用户名' }
  },
  {
    field: 'email',
    label: '邮箱',
    component: 'input',
    props: { placeholder: '请输入邮箱' }
  },
  {
    field: 'status',
    label: '状态',
    component: 'select',
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ]
  }
];

const loadData = async (pageParams, queryParams) => {
  const response = await userApi.getList({ ...pageParams, ...queryParams });
  return {
    list: response.data.list,
    total: response.data.total
  };
};

const handleSearch = searchParams => {
  console.log('搜索参数:', searchParams);
};
</script>
```

### 带工具栏功能的表格

```vue
<template>
  <FuniCurdV2 :columns="columns" :lodaData="loadData" :useTools="true">
    <template #header>
      <h3>用户管理</h3>
    </template>

    <template #buttonGroup>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        新增用户
      </el-button>
      <el-button @click="handleBatchDelete"> 批量删除 </el-button>
    </template>
  </FuniCurdV2>
</template>

<script setup>
import { ref } from 'vue';
import { Plus } from '@element-plus/icons-vue';

const columns = ref([
  { type: 'selection', width: 40 },
  { label: 'ID', prop: 'id' },
  { label: '用户名', prop: 'username' },
  { label: '状态', prop: 'status' },
  { label: '操作', prop: 'actions', width: 200, fixed: 'right' }
]);

const loadData = async (pageParams, queryParams) => {
  const response = await userApi.getList({ ...pageParams, ...queryParams });
  return {
    list: response.data.list,
    total: response.data.total
  };
};

const handleAdd = () => {
  console.log('新增用户');
};

const handleBatchDelete = () => {
  console.log('批量删除');
};
</script>
```

### 虚拟滚动大数据表格

```vue
<template>
  <FuniCurdV2 :columns="columns" :data="bigData" :virtual-scroll="virtualScrollConfig" row-key="id" height="500px" />
</template>

<script setup>
import { ref, onMounted } from 'vue';

const columns = ref([
  { label: 'ID', prop: 'id', width: 80 },
  { label: '姓名', prop: 'name', width: 120 },
  { label: '部门', prop: 'department', width: 120 },
  { label: '职位', prop: 'position', width: 120 },
  { label: '薪资', prop: 'salary', width: 100 }
]);

const bigData = ref([]);

const virtualScrollConfig = ref({
  enabled: true,
  height: 500,
  itemHeight: 50,
  buffer: 10,
  threshold: 100
});

onMounted(() => {
  // 生成大量数据
  const data = [];
  for (let i = 1; i <= 10000; i++) {
    data.push({
      id: i,
      name: `员工${i}`,
      department: ['技术部', '产品部', '运营部', '市场部'][Math.floor(Math.random() * 4)],
      position: ['工程师', '经理', '专员', '主管'][Math.floor(Math.random() * 4)],
      salary: Math.floor(Math.random() * 20000) + 5000
    });
  }
  bigData.value = data;
});
</script>
```

### 带搜索功能的表格

```vue
<template>
  <FuniCurdV2 :columns="columns" :loadData="loadData" :search-config="searchConfig" @search="handleSearch" />
</template>

<script setup>
import { ref } from 'vue';

const columns = ref([
  { label: 'ID', prop: 'id', width: 80 },
  { label: '用户名', prop: 'username', width: 120 },
  { label: '邮箱', prop: 'email', minWidth: 180 },
  { label: '部门', prop: 'department', width: 120 },
  { label: '状态', prop: 'status', width: 100 }
]);

const searchConfig = ref({
  enabled: true,
  mode: 'advanced',
  advancedSearch: {
    fields: [
      {
        prop: 'username',
        label: '用户名',
        component: 'el-input',
        props: { placeholder: '请输入用户名' },
        span: 8
      },
      {
        prop: 'email',
        label: '邮箱',
        component: 'el-input',
        props: { placeholder: '请输入邮箱' },
        span: 8
      },
      {
        prop: 'department',
        label: '部门',
        component: 'el-select',
        props: { placeholder: '请选择部门' },
        options: [
          { label: '技术部', value: 'tech' },
          { label: '产品部', value: 'product' },
          { label: '运营部', value: 'operation' }
        ],
        span: 8
      },
      {
        prop: 'status',
        label: '状态',
        component: 'el-select',
        props: { placeholder: '请选择状态' },
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 }
        ],
        span: 8
      }
    ],
    layout: 'block',
    collapsible: true,
    defaultCollapsed: false
  }
});

const loadData = async params => {
  const response = await userApi.getList(params);
  return {
    list: response.data.list,
    total: response.data.total
  };
};

const handleSearch = searchParams => {
  console.log('搜索参数:', searchParams);
};
</script>
```

### 带工具栏和操作按钮的表格

```vue
<template>
  <FuniCurdV2 :columns="columns" :loadData="loadData" :actions="actions" :toolbar-config="toolbarConfig" selection @toolbar-action="handleToolbarAction" @selection-change="handleSelectionChange" />
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const columns = ref([
  { label: 'ID', prop: 'id', width: 80 },
  { label: '用户名', prop: 'username', width: 120 },
  { label: '邮箱', prop: 'email', minWidth: 180 },
  { label: '状态', prop: 'status', width: 100 }
]);

const actions = ref([
  {
    name: '编辑',
    key: 'edit',
    props: { type: 'primary', size: 'small' },
    on: { click: handleEdit },
    permission: 'user:edit'
  },
  {
    name: '删除',
    key: 'delete',
    props: { type: 'danger', size: 'small' },
    on: { click: handleDelete },
    permission: 'user:delete',
    confirm: {
      title: '确认删除',
      content: '确定要删除这个用户吗？',
      type: 'warning'
    }
  }
]);

const toolbarConfig = ref({
  enabled: true,
  title: '用户管理',
  actions: [
    {
      key: 'add',
      name: '新增用户',
      icon: 'Plus',
      type: 'button',
      props: { type: 'primary' },
      permission: 'user:add'
    },
    {
      key: 'batch',
      name: '批量操作',
      icon: 'Operation',
      type: 'dropdown',
      children: [
        { key: 'batchDelete', name: '批量删除', icon: 'Delete' },
        { key: 'batchExport', name: '批量导出', icon: 'Download' }
      ]
    }
  ],
  settings: {
    refresh: true,
    density: true,
    columnConfig: true,
    fullscreen: true,
    export: true
  }
});

const selectedRows = ref([]);

const loadData = async params => {
  const response = await userApi.getList(params);
  return {
    list: response.data.list,
    total: response.data.total
  };
};

const handleEdit = row => {
  console.log('编辑用户:', row);
  // 打开编辑对话框
};

const handleDelete = async row => {
  try {
    await userApi.delete(row.id);
    ElMessage.success('删除成功');
    // 刷新表格
  } catch (error) {
    ElMessage.error('删除失败');
  }
};

const handleToolbarAction = (action, selectedRows) => {
  switch (action.key) {
    case 'add':
      console.log('新增用户');
      break;
    case 'batchDelete':
      handleBatchDelete(selectedRows);
      break;
    case 'batchExport':
      handleBatchExport(selectedRows);
      break;
  }
};

const handleSelectionChange = selection => {
  selectedRows.value = selection;
};

const handleBatchDelete = async rows => {
  if (rows.length === 0) {
    ElMessage.warning('请选择要删除的用户');
    return;
  }

  try {
    await ElMessageBox.confirm('确定要删除选中的用户吗？', '批量删除', {
      type: 'warning'
    });

    const ids = rows.map(row => row.id);
    await userApi.batchDelete(ids);
    ElMessage.success('批量删除成功');
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败');
    }
  }
};

const handleBatchExport = rows => {
  console.log('批量导出:', rows);
};
</script>
```

### 导出功能示例

```vue
<template>
  <FuniCurdV2 :columns="columns" :loadData="loadData" :export-config="exportConfig" @export="handleExport" />
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';

const columns = ref([
  { label: 'ID', prop: 'id', width: 80 },
  { label: '用户名', prop: 'username', width: 120 },
  { label: '邮箱', prop: 'email', minWidth: 180 },
  { label: '部门', prop: 'department', width: 120 },
  { label: '状态', prop: 'status', width: 100 }
]);

const exportConfig = ref({
  enabled: true,
  formats: ['xlsx', 'csv', 'pdf'],
  exportAll: true,
  exportSelected: true,
  exportVisible: true,
  filename: '用户数据',
  maxRows: 10000,
  beforeExport: data => {
    // 数据处理：将状态数字转换为文本
    return data.map(item => ({
      ...item,
      statusText: item.status === 1 ? '启用' : '禁用'
    }));
  },
  afterExport: result => {
    ElMessage.success('导出成功');
  }
});

const loadData = async params => {
  const response = await userApi.getList(params);
  return {
    list: response.data.list,
    total: response.data.total
  };
};

const handleExport = (format, data) => {
  console.log('导出格式:', format);
  console.log('导出数据:', data);
};
</script>
```

### 列配置功能示例

```vue
<template>
  <FuniCurdV2 :columns="columns" :loadData="loadData" :column-config="columnConfigOptions" @column-config-change="handleColumnConfigChange" />
</template>

<script setup>
import { ref } from 'vue';

const columns = ref([
  {
    label: 'ID',
    prop: 'id',
    width: 80,
    configurable: true,
    resizable: true
  },
  {
    label: '用户名',
    prop: 'username',
    width: 120,
    configurable: true,
    resizable: true,
    fixable: true
  },
  {
    label: '邮箱',
    prop: 'email',
    minWidth: 180,
    configurable: true,
    resizable: true
  },
  {
    label: '部门',
    prop: 'department',
    width: 120,
    configurable: true
  },
  {
    label: '状态',
    prop: 'status',
    width: 100,
    configurable: false // 不可配置
  }
]);

const columnConfigOptions = ref({
  enabled: true,
  configurable: true,
  resizable: true,
  sortable: true,
  fixable: true,
  storage: true,
  storageKey: 'user-table-columns',
  resetable: true
});

const loadData = async params => {
  const response = await userApi.getList(params);
  return {
    list: response.data.list,
    total: response.data.total
  };
};

const handleColumnConfigChange = newColumns => {
  console.log('列配置变化:', newColumns);
  // 可以在这里保存到服务端
};
</script>
```

### 密度配置示例

```vue
<template>
  <FuniCurdV2 :columns="columns" :loadData="loadData" :density-config="densityConfig" @density-change="handleDensityChange" />
</template>

<script setup>
import { ref } from 'vue';

const columns = ref([
  { label: 'ID', prop: 'id', width: 80 },
  { label: '用户名', prop: 'username', width: 120 },
  { label: '邮箱', prop: 'email', minWidth: 180 },
  { label: '状态', prop: 'status', width: 100 }
]);

const densityConfig = ref({
  enabled: true,
  options: [
    { key: 'large', name: '宽松', size: 'large', rowHeight: 60 },
    { key: 'default', name: '默认', size: 'default', rowHeight: 50 },
    { key: 'small', name: '紧凑', size: 'small', rowHeight: 40 }
  ],
  defaultDensity: 'default',
  storage: true,
  storageKey: 'table-density'
});

const loadData = async params => {
  const response = await userApi.getList(params);
  return {
    list: response.data.list,
    total: response.data.total
  };
};

const handleDensityChange = density => {
  console.log('密度变化:', density);
};
</script>
```

### 全屏功能示例

```vue
<template>
  <FuniCurdV2 :columns="columns" :loadData="loadData" :fullscreen-config="fullscreenConfig" @fullscreen-change="handleFullscreenChange" />
</template>

<script setup>
import { ref } from 'vue';

const columns = ref([
  { label: 'ID', prop: 'id', width: 80 },
  { label: '用户名', prop: 'username', width: 120 },
  { label: '邮箱', prop: 'email', minWidth: 180 },
  { label: '状态', prop: 'status', width: 100 }
]);

const fullscreenConfig = ref({
  enabled: true,
  zIndex: 3000,
  background: '#fff',
  escToExit: true,
  showToolbar: true
});

const loadData = async params => {
  const response = await userApi.getList(params);
  return {
    list: response.data.list,
    total: response.data.total
  };
};

const handleFullscreenChange = isFullscreen => {
  console.log('全屏状态:', isFullscreen);
};
</script>
```

## 复杂业务场景示例

### 订单管理表格

```vue
<template>
  <FuniCurdV2 :columns="orderColumns" :loadData="loadOrderData" :actions="orderActions" :toolbar-config="orderToolbarConfig" :search-config="orderSearchConfig" :export-config="orderExportConfig" selection @selection-change="handleOrderSelectionChange" />
</template>

<script setup>
import { ref } from 'vue';

const orderColumns = ref([
  { label: '订单号', prop: 'orderNo', width: 150, fixed: 'left' },
  { label: '客户名称', prop: 'customerName', width: 120 },
  { label: '订单金额', prop: 'amount', width: 120, valueType: 'money' },
  { label: '订单状态', prop: 'status', width: 100, valueType: 'select' },
  { label: '创建时间', prop: 'createTime', width: 180, valueType: 'dateTime' },
  { label: '备注', prop: 'remark', minWidth: 200, ellipsis: true }
]);

const orderActions = ref([
  {
    name: '查看',
    key: 'view',
    props: { type: 'primary', size: 'small' },
    on: { click: handleViewOrder }
  },
  {
    name: '编辑',
    key: 'edit',
    props: { type: 'warning', size: 'small' },
    on: { click: handleEditOrder },
    showCondition: row => row.status === 'draft'
  },
  {
    name: '取消',
    key: 'cancel',
    props: { type: 'danger', size: 'small' },
    on: { click: handleCancelOrder },
    showCondition: row => ['draft', 'pending'].includes(row.status),
    confirm: {
      title: '确认取消',
      content: '确定要取消这个订单吗？',
      type: 'warning'
    }
  }
]);

const orderToolbarConfig = ref({
  enabled: true,
  title: '订单管理',
  actions: [
    {
      key: 'add',
      name: '新建订单',
      icon: 'Plus',
      type: 'button',
      props: { type: 'primary' }
    },
    {
      key: 'batchExport',
      name: '批量导出',
      icon: 'Download',
      type: 'button'
    }
  ]
});

const orderSearchConfig = ref({
  enabled: true,
  mode: 'advanced',
  advancedSearch: {
    fields: [
      {
        prop: 'orderNo',
        label: '订单号',
        component: 'el-input',
        props: { placeholder: '请输入订单号' }
      },
      {
        prop: 'customerName',
        label: '客户名称',
        component: 'el-input',
        props: { placeholder: '请输入客户名称' }
      },
      {
        prop: 'status',
        label: '订单状态',
        component: 'el-select',
        options: [
          { label: '草稿', value: 'draft' },
          { label: '待付款', value: 'pending' },
          { label: '已付款', value: 'paid' },
          { label: '已发货', value: 'shipped' },
          { label: '已完成', value: 'completed' },
          { label: '已取消', value: 'cancelled' }
        ]
      },
      {
        prop: 'dateRange',
        label: '创建时间',
        component: 'el-date-picker',
        props: {
          type: 'daterange',
          rangeSeparator: '至',
          startPlaceholder: '开始日期',
          endPlaceholder: '结束日期'
        }
      }
    ],
    layout: 'block',
    collapsible: true
  }
});

const orderExportConfig = ref({
  enabled: true,
  formats: ['xlsx', 'csv'],
  exportAll: true,
  exportSelected: true,
  filename: '订单数据',
  beforeExport: data => {
    return data.map(item => ({
      ...item,
      statusText: getStatusText(item.status),
      amountText: `¥${item.amount.toFixed(2)}`
    }));
  }
});

const loadOrderData = async params => {
  const response = await orderApi.getList(params);
  return {
    list: response.data.list,
    total: response.data.total
  };
};

const handleViewOrder = row => {
  console.log('查看订单:', row);
};

const handleEditOrder = row => {
  console.log('编辑订单:', row);
};

const handleCancelOrder = async row => {
  try {
    await orderApi.cancel(row.id);
    ElMessage.success('订单取消成功');
  } catch (error) {
    ElMessage.error('订单取消失败');
  }
};

const handleOrderSelectionChange = selection => {
  console.log('选中的订单:', selection);
};

const getStatusText = status => {
  const statusMap = {
    draft: '草稿',
    pending: '待付款',
    paid: '已付款',
    shipped: '已发货',
    completed: '已完成',
    cancelled: '已取消'
  };
  return statusMap[status] || status;
};
</script>
```

## 性能优化示例

### 大数据量优化

```vue
<template>
  <FuniCurdV2 :columns="columns" :loadData="loadData" :virtual-scroll="virtualScrollConfig" :pagination="false" row-key="id" height="600px" />
</template>

<script setup>
import { ref } from 'vue';

const columns = ref([
  { label: 'ID', prop: 'id', width: 80 },
  { label: '姓名', prop: 'name', width: 120 },
  { label: '部门', prop: 'department', width: 120 },
  { label: '职位', prop: 'position', width: 120 }
]);

const virtualScrollConfig = ref({
  enabled: true,
  height: 600,
  itemHeight: 50,
  buffer: 20,
  threshold: 500,
  overscan: 10
});

const loadData = async params => {
  // 模拟大数据量加载
  const { pageNum = 1, pageSize = 1000 } = params;
  const start = (pageNum - 1) * pageSize;
  const end = start + pageSize;

  const data = [];
  for (let i = start; i < end; i++) {
    data.push({
      id: i + 1,
      name: `员工${i + 1}`,
      department: `部门${Math.floor(i / 100) + 1}`,
      position: `职位${Math.floor(i / 50) + 1}`
    });
  }

  return {
    list: data,
    total: 100000 // 总共10万条数据
  };
};
</script>
```

这些示例展示了FuniCurdV2的各种功能和使用场景，从基础的数据展示到复杂的业务应用，都有相应的配置和实现方式。
