# FuniCurdV2 API 文档

## 组件概述

FuniCurdV2 是基于 Element Plus 的 el-table 和 el-pagination 封装的增强型数据表格组件，专为复杂业务场景和大数据量展示而设计。

## Props 参数

### 数据相关

| 参数名         | 类型            | 默认值  | 说明                                                  |
| -------------- | --------------- | ------- | ----------------------------------------------------- |
| data           | Array           | `[]`    | 静态数据数组                                          |
| lodaData       | Function        | -       | 远程数据加载函数，接收 (pageParams, queryParams) 参数 |
| loading        | Boolean         | `false` | 加载状态                                              |
| rowKey         | String/Function | `'id'`  | 行数据的唯一标识                                      |
| reloadOnActive | Boolean         | `true`  | 页面激活时是否重新加载数据                            |

### 分页相关

| 参数名      | 类型    | 默认值                        | 说明                     |
| ----------- | ------- | ----------------------------- | ------------------------ |
| pagination  | Boolean | `true`                        | 是否显示分页             |
| pageSizes   | Array   | -                             | 每页显示个数选择器的选项 |
| defaultPage | Object  | `{ pageSize: 10, pageNo: 1 }` | 默认分页参数             |

### 表格配置

| 参数名            | 类型    | 默认值  | 说明                   |
| ----------------- | ------- | ------- | ---------------------- |
| columns           | Array   | `[]`    | 表格列配置数组（必填） |
| settings          | Object  | `{}`    | 列设置配置             |
| teleported        | Boolean | `false` | 是否传送到布局容器     |
| scrollbarAlwaysOn | Boolean | `true`  | 滚动条是否始终显示     |
| draggable         | Boolean | `false` | 是否启用行拖拽排序     |
| useTools          | Boolean | `false` | 是否显示工具栏         |

### 搜索相关

| 参数名             | 类型    | 默认值  | 说明                         |
| ------------------ | ------- | ------- | ---------------------------- |
| isShowSearch       | Boolean | `false` | 是否显示搜索区域             |
| searchConfig       | Object  | `{}`    | 搜索配置对象（通常无需配置） |
| queryFields        | Object  | -       | 查询字段配置                 |
| sortFields         | Object  | `[]`    | 排序字段配置                 |
| columnFilters      | Object  | `[]`    | 列筛选配置                   |
| colNumber          | Number  | `4`     | 搜索表单列数                 |
| searchOnFormChange | Boolean | `true`  | 表单变化时是否自动搜索       |
| useSearchV2        | Boolean | `true`  | 是否使用4.0模式搜索          |

### 行选择相关

| 参数名          | 类型           | 默认值    | 说明                                     |
| --------------- | -------------- | --------- | ---------------------------------------- |
| rowSelection    | String/Boolean | `'click'` | 行选择模式：'click'、'dblclick' 或 false |
| checkOnRowClick | Boolean        | `false`   | 点击行时是否选中复选框                   |

### 其他配置

| 参数名       | 类型    | 默认值  | 说明         |
| ------------ | ------- | ------- | ------------ |
| fixedButtons | Boolean | `false` | 是否固定按钮 |
| actionsProps | Object  | -       | 操作按钮属性 |

## Events 事件

### 组件生命周期事件

| 事件名        | 参数    | 说明               |
| ------------- | ------- | ------------------ |
| get-curd      | curdRef | 获取表格实例时触发 |
| beforeRequest | -       | 数据请求前触发     |
| afterRequest  | list    | 数据请求完成后触发 |
| requestError  | error   | 数据请求错误时触发 |

### 行交互事件

| 事件名       | 参数                                   | 说明       |
| ------------ | -------------------------------------- | ---------- |
| row-click    | { column, row, selection, currentRow } | 行点击事件 |
| row-dblclick | { column, row, selection, currentRow } | 行双击事件 |

### 表格操作事件

| 事件名             | 参数                    | 说明             |
| ------------------ | ----------------------- | ---------------- |
| draggableEnd       | -                       | 拖拽排序结束事件 |
| sort-change        | { column, order, prop } | 排序变化事件     |
| col-setting-change | newColumnSettings       | 列设置变化事件   |

### ElementPlus 透传事件

FuniCurdV2 通过 `v-bind="$attrs"` 透传所有 ElementPlus el-table 的事件：

| 事件名           | 参数                      | 说明           |
| ---------------- | ------------------------- | -------------- |
| selection-change | selection                 | 选择项变化事件 |
| current-change   | currentRow, oldCurrentRow | 当前行变化事件 |
| cell-click       | row, column, cell, event  | 单元格点击事件 |
| cell-dblclick    | row, column, cell, event  | 单元格双击事件 |
| header-click     | column, event             | 表头点击事件   |
| filter-change    | filters                   | 过滤变化事件   |

更多 ElementPlus 事件请参考 [ElementPlus API 文档](./elementplus-api.md)。

## Methods 方法

### 组件实例方法

通过 `ref` 获取组件实例后可调用以下方法：

| 方法名             | 参数    | 返回值 | 说明                                           |
| ------------------ | ------- | ------ | ---------------------------------------------- |
| reload             | options | -      | 重新加载数据，options: { resetPage?: boolean } |
| doRequest          | page    | -      | 执行数据请求，page: { pageNo, pageSize }       |
| resetCurrentRow    | -       | -      | 重置当前选中行                                 |
| setCurrentRow      | row     | -      | 设置当前选中行                                 |
| setCurrentRowByKey | key     | -      | 通过 key 设置当前行                            |
| toggleSelection    | row     | -      | 切换行选择状态                                 |
| buildQueryParams   | -       | Object | 构建查询参数                                   |

### 搜索相关方法

| 方法名            | 参数   | 返回值 | 说明           |
| ----------------- | ------ | ------ | -------------- |
| getSearchValues   | -      | Object | 获取搜索表单值 |
| setSearchValues   | values | -      | 设置搜索表单值 |
| resetSearchFields | -      | -      | 重置搜索表单   |

### ElementPlus 透传方法

FuniCurdV2 透传所有 ElementPlus el-table 的方法，通过组件实例的 `ref` 属性访问：

```vue
<template>
  <funi-curd-v2 ref="curdRef" :columns="columns" />
</template>

<script setup>
const curdRef = ref();

// 调用 ElementPlus el-table 方法
const clearSelection = () => {
  curdRef.value.ref.clearSelection();
};

const toggleRowSelection = (row, selected) => {
  curdRef.value.ref.toggleRowSelection(row, selected);
};
</script>
```

常用的 ElementPlus 方法：

| 方法名             | 参数            | 说明                 |
| ------------------ | --------------- | -------------------- |
| clearSelection     | -               | 清空选择             |
| toggleRowSelection | row, selected   | 切换某一行的选中状态 |
| toggleAllSelection | -               | 切换全选状态         |
| getSelectionRows   | -               | 获取当前选中的行数据 |
| sort               | prop, order     | 手动排序             |
| clearSort          | -               | 清空排序             |
| clearFilter        | columnKeys      | 清空过滤器           |
| doLayout           | -               | 重新布局表格         |
| scrollTo           | options, yCoord | 滚动到指定位置       |

更多 ElementPlus 方法请参考 [ElementPlus 官方文档](https://element-plus.org/zh-CN/component/table.html#table-methods)。

## Slots 插槽

### 基础插槽

| 插槽名            | 说明             | 参数     |
| ----------------- | ---------------- | -------- |
| header-slot       | 表格顶部插槽     | -        |
| header            | 工具栏标题区域   | -        |
| buttonGroup       | 工具栏按钮组     | -        |
| header-append-row | 工具栏下方追加行 | -        |
| append            | 表格底部追加内容 | -        |
| empty             | 空数据时显示内容 | -        |
| pagination_extra  | 分页器额外内容   | pageInfo |

### 动态插槽

通过 columns 配置中的 slots 属性可以定义动态插槽：

```javascript
const columns = [
  {
    prop: 'status',
    label: '状态',
    slots: {
      default: 'status-slot', // 内容插槽
      header: 'status-header-slot' // 表头插槽
    }
  }
];
```

```vue
<template>
  <FuniCurdV2 :columns="columns">
    <!-- 状态列内容插槽 -->
    <template #status-slot="{ row }">
      <el-tag :type="row.status === 1 ? 'success' : 'danger'">
        {{ row.status === 1 ? '启用' : '禁用' }}
      </el-tag>
    </template>

    <!-- 状态列表头插槽 -->
    <template #status-header-slot>
      <span
        >状态 <el-icon><InfoFilled /></el-icon
      ></span>
    </template>
  </FuniCurdV2>
</template>
```

## 列配置详解

### 基础列配置

```javascript
const columns = [
  {
    prop: 'id', // 字段名
    label: 'ID', // 列标题
    width: 80, // 固定宽度（操作列建议设置,一般列默认不配置宽度）
    minWidth: 100, // 最小宽度（特殊字段可设置）
    maxWidth: 200, // 最大宽度（内容很长时设置）
    align: 'center', // 对齐方式
    sortable: true, // 是否可排序
    fixed: 'left', // 固定列：'left' | 'right'
    showOverflowTooltip: true // 溢出显示提示,默认为true
  }
];
```

### 特殊列类型

```javascript
const columns = [
  // 选择列
  { type: 'selection', width: 40 },

  // 单选列
  { type: 'radio', width: 40 },

  // 序号列
  { type: 'index', label: '序号', width: 60 },

  // 自定义渲染列，建议使用render(jsx)语法，尽量避免使用插槽
  {
    prop: 'status',
    label: '状态',
    render: ({ row }) => {
      return row.status === 1 ? <el-tag type="success">启用</el-tag> : <el-tag type="danger">禁用</el-tag>;
    }
  }
];
```

### 列宽配置规则

⚠️ **重要**：根据项目配置规则，除了操作列外，其他列默认不设置 width 值：

```javascript
// ✅ 推荐做法
const columns = [
  { label: 'ID', prop: 'id' }, // 不设置width，自动撑开
  { label: '姓名', prop: 'name' },
  { label: '操作', prop: 'actions', width: 200, fixed: 'right' } // 操作列设置固定宽度
];

// ✅ 特殊情况可以设置
const columns = [
  { label: '很长的内容', prop: 'content', maxWidth: 300 }, // 内容很长时设置最大宽度
  { label: '状态', prop: 'status', minWidth: 100 } // 特殊字段设置最小宽度
];
```

## 使用示例

### 基础用法

```vue
<template>
  <funi-curd-v2 :columns="columns" :lodaData="loadData" :loading="loading" />
</template>

<script setup>
import { ref } from 'vue';

const columns = ref([
  { label: 'ID', prop: 'id' },
  { label: '姓名', prop: 'name' },
  { label: '状态', prop: 'status' }
]);

const loading = ref(false);

const loadData = async (pageParams, queryParams) => {
  loading.value = true;
  try {
    const response = await api.getList({ ...pageParams, ...queryParams });
    return {
      list: response.data.list,
      total: response.data.total
    };
  } finally {
    loading.value = false;
  }
};
</script>
```

### 带搜索功能

```vue
<template>
  <FuniCurdV2 :columns="columns" :lodaData="loadData" :isShowSearch="true" :queryFields="queryFields" />
</template>

<script setup>
const queryFields = [
  {
    field: 'name',
    label: '姓名',
    component: 'input',
    props: { placeholder: '请输入姓名' }
  },
  {
    field: 'status',
    label: '状态',
    component: 'select',
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ]
  }
];
</script>
```

## 注意事项

### 1. 搜索配置简化

FuniCurdV2 内置了读取远程高级搜索配置的逻辑，**通常无需配置 searchConfig**：

```vue
<!-- ✅ 推荐：只配置 queryFields -->
<FuniCurdV2
  :isShowSearch="true"
  :queryFields="queryFields"
/>

<!-- ❌ 避免：手动配置 searchConfig -->
<FuniCurdV2
  :isShowSearch="true"
  :searchConfig="searchConfig"  <!-- 通常不需要 -->
/>
```

### 2. 数据加载函数

`lodaData` 函数接收两个参数：

- `pageParams`: `{ pageNo, pageSize, pageIndex }`
- `queryParams`: `{ groups: [...], sorts: [...] }`

### 3. 性能考虑

- 大数据量时建议使用服务端分页
- 避免在 render 函数中进行复杂计算
- 合理使用列配置功能减少不必要的列渲染

更多详细示例请参考 [使用示例文档](./examples.md)。interface ExportConfig { exportable: boolean; // 是否支持导出 exportFormats: string[]; // 支持的格式：['xlsx', 'csv', 'pdf'] exportAll: boolean; // 是否导出全部数据 exportSelected: boolean; // 是否支持导出选中数据 exportVisible: boolean; // 是否只导出可见列 filename: string; // 默认文件名 sheetName: string; // Excel工作表名称 }
