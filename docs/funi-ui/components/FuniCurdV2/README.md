# FuniCurdV2 组件文档

## 📖 文档导航

- [🚀 快速开始](#快速开始)
- [📋 API 文档](./api.md) - 完整的 Props、Events、Methods 参考
- [💡 使用示例](./examples.md) - 从基础到高级的完整示例
- [⚙️ 配置结构](./config-schema.md) - 详细的配置选项说明
- [🎯 最佳实践](./best-practices.md) - 推荐用法和性能优化
- [🔗 ElementPlus API](./elementplus-api.md) - ElementPlus 原生 API 透传说明

## 组件概述

FuniCurdV2 是基于 Element Plus 的 el-table 和 el-pagination 封装的增强型数据表格组件，专为复杂业务场景和大数据量展示而设计。

### 🎯 核心特性

- **数据管理**：支持静态数据和远程数据加载，内置分页和排序功能
- **高级搜索**：集成 FuniSearch 组件，支持复杂查询条件和实时搜索
- **列配置管理**：支持列显示/隐藏、宽度调整、拖拽排序等个性化配置
- **工具栏功能**：内置刷新、密度调整、全屏、列设置等常用工具
- **行选择交互**：支持单选、多选，可配置行点击和双击行为
- **拖拽排序**：支持行数据拖拽排序功能
- **响应式布局**：支持全屏模式和自适应布局

### 🎪 适用场景

- ✅ 管理后台的数据列表页面
- ✅ 需要复杂查询和筛选的数据展示
- ✅ 大数据量的表格展示（配合分页使用）
- ✅ 需要用户个性化配置的表格界面
- ✅ 需要丰富交互功能的数据管理界面

## 快速开始

### 安装和引入

```javascript
// 全局注册（推荐）
import FuniCurdV2 from '@/components/FuniCurdV2/index.vue'
app.component('FuniCurdV2', FuniCurdV2)

// 局部引入
import FuniCurdV2 from '@/components/FuniCurdV2/index.vue'
```

### 基础使用

```vue
<template>
  <FuniCurdV2
    :columns="columns"
    :lodaData="loadData"
    :loading="loading"
  />
</template>

<script setup>
import { ref } from 'vue'

const columns = ref([
  { label: 'ID', prop: 'id' },
  { label: '姓名', prop: 'name' },
  { label: '状态', prop: 'status' }
])

const loading = ref(false)

const loadData = async (pageParams, queryParams) => {
  loading.value = true
  try {
    const response = await api.getList({ ...pageParams, ...queryParams })
    return {
      list: response.data.list,
      total: response.data.total
    }
  } finally {
    loading.value = false
  }
}
</script>
```

### 带搜索功能

```vue
<template>
  <FuniCurdV2
    :columns="columns"
    :lodaData="loadData"
    :isShowSearch="true"
    :queryFields="queryFields"
  />
</template>

<script setup>
const queryFields = [
  {
    field: 'name',
    label: '姓名',
    component: 'input',
    props: { placeholder: '请输入姓名' }
  },
  {
    field: 'status',
    label: '状态',
    component: 'select',
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 }
    ]
  }
]
</script>
```

## 🔧 重要配置说明

### 列宽配置规则

根据项目配置规则，**除了操作列外，其他列默认不设置 width 值**：

```javascript
// ✅ 推荐做法
const columns = [
  { label: 'ID', prop: 'id' },  // 不设置width，自动撑开
  { label: '姓名', prop: 'name' },
  { label: '操作', prop: 'actions', width: 200, fixed: 'right' }  // 操作列设置固定宽度
]

// ✅ 特殊情况
const columns = [
  { label: '很长内容', prop: 'content', maxWidth: 300 },  // 内容很长时设置最大宽度
  { label: '状态', prop: 'status', minWidth: 100 }  // 特殊字段设置最小宽度
]
```

### 搜索配置简化

FuniCurdV2 内置了读取远程高级搜索配置的逻辑，**通常无需配置 searchConfig**：

```vue
<template>
  <!-- ✅ 推荐：只配置 queryFields -->
  <FuniCurdV2
    :isShowSearch="true"
    :queryFields="queryFields"
  />
  
  <!-- ❌ 避免：手动配置 searchConfig -->
  <FuniCurdV2
    :isShowSearch="true"
    :searchConfig="searchConfig"  <!-- 通常不需要 -->
  />
</template>
```

## 📚 更多文档

- **[API 文档](./api.md)** - 查看完整的 Props、Events、Methods 列表
- **[使用示例](./examples.md)** - 查看各种使用场景的完整代码示例
- **[配置结构](./config-schema.md)** - 了解详细的配置选项和类型定义
- **[最佳实践](./best-practices.md)** - 学习推荐用法和性能优化技巧
- **[ElementPlus API](./elementplus-api.md)** - 了解如何使用 ElementPlus 原生功能

## 🤝 常见问题

### Q: 如何设置列宽？
A: 除操作列外，其他列默认不设置 width，让表格自动撑开。特殊情况可设置 minWidth 或 maxWidth。

### Q: 搜索功能如何配置？
A: 只需配置 `queryFields`，组件会自动生成搜索配置，无需手动配置 `searchConfig`。

### Q: 如何处理大数据量？
A: 建议使用服务端分页，通过 `lodaData` 函数返回分页数据和总数。

### Q: 如何自定义列内容？
A: 可以使用 `render` 函数或通过 `slots` 配置使用插槽。

更多问题请查看 [最佳实践文档](./best-practices.md)。

## 🔄 版本说明

当前版本基于实际代码分析，确保所有 API 和配置准确无误。如有疑问，请参考具体的 API 文档或查看组件源码。
