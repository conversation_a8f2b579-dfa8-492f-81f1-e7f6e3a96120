# FuniCurdV2 ElementPlus API支持

## 基础组件

FuniCurdV2基于ElementPlus的以下组件进行封装：
- **el-table** - 主要数据表格
- **el-table-column** - 表格列定义
- **el-pagination** - 分页组件
- **el-button** - 操作按钮
- **el-form** - 搜索表单
- **el-dialog** - 弹窗对话框
- **el-drawer** - 抽屉组件

## 支持的ElementPlus API

### el-table API透传

| API名称 | 类型 | 说明 | 透传方式 |
|---------|------|------|---------|
| data | Array | 表格数据 | 自动处理 |
| height | String/Number | 表格高度 | v-bind透传 |
| max-height | String/Number | 表格最大高度 | v-bind透传 |
| stripe | Boolean | 斑马纹 | v-bind透传 |
| border | Boolean | 边框 | v-bind透传 |
| size | String | 尺寸 | v-bind透传 |
| fit | Boolean | 列宽自适应 | v-bind透传 |
| show-header | Boolean | 显示表头 | v-bind透传 |
| highlight-current-row | Boolean | 高亮当前行 | v-bind透传 |
| current-row-key | String/Number | 当前行key | v-bind透传 |
| row-class-name | String/Function | 行类名 | v-bind透传 |
| row-style | Object/Function | 行样式 | v-bind透传 |
| cell-class-name | String/Function | 单元格类名 | v-bind透传 |
| cell-style | Object/Function | 单元格样式 | v-bind透传 |
| header-row-class-name | String/Function | 表头行类名 | v-bind透传 |
| header-row-style | Object/Function | 表头行样式 | v-bind透传 |
| header-cell-class-name | String/Function | 表头单元格类名 | v-bind透传 |
| header-cell-style | Object/Function | 表头单元格样式 | v-bind透传 |
| row-key | String/Function | 行数据Key | v-bind透传 |
| empty-text | String | 空数据文本 | v-bind透传 |
| default-expand-all | Boolean | 默认展开所有行 | v-bind透传 |
| expand-row-keys | Array | 展开行的keys | v-bind透传 |
| default-sort | Object | 默认排序 | v-bind透传 |
| tooltip-effect | String | tooltip效果 | v-bind透传 |
| tooltip-options | Object | tooltip配置 | v-bind透传 |
| show-summary | Boolean | 显示合计行 | v-bind透传 |
| sum-text | String | 合计行首列文本 | v-bind透传 |
| summary-method | Function | 自定义合计方法 | v-bind透传 |
| span-method | Function | 合并行或列 | v-bind透传 |
| select-on-indeterminate | Boolean | 多选框行为 | v-bind透传 |
| indent | Number | 树形数据缩进 | v-bind透传 |
| lazy | Boolean | 懒加载子节点 | v-bind透传 |
| load | Function | 加载子节点函数 | v-bind透传 |
| tree-props | Object | 树形数据配置 | v-bind透传 |
| table-layout | String | 表格布局方式 | v-bind透传 |
| scrollbar-always-on | Boolean | 总是显示滚动条 | v-bind透传 |
| flexible | Boolean | 确保主轴最小尺寸 | v-bind透传 |

### el-table Events透传

| 事件名 | 参数 | 说明 | 透传方式 |
|--------|------|------|---------|
| select | selection, row | 选择某一行 | @select |
| select-all | selection | 选择全部 | @select-all |
| selection-change | selection | 选择项变化 | @selection-change |
| cell-mouse-enter | row, column, cell, event | 单元格鼠标进入 | @cell-mouse-enter |
| cell-mouse-leave | row, column, cell, event | 单元格鼠标离开 | @cell-mouse-leave |
| cell-click | row, column, cell, event | 单元格点击 | @cell-click |
| cell-dblclick | row, column, cell, event | 单元格双击 | @cell-dblclick |
| row-click | row, column, event | 行点击 | @row-click |
| row-contextmenu | row, column, event | 行右键 | @row-contextmenu |
| row-dblclick | row, column, event | 行双击 | @row-dblclick |
| header-click | column, event | 表头点击 | @header-click |
| header-contextmenu | column, event | 表头右键 | @header-contextmenu |
| sort-change | { column, prop, order } | 排序变化 | @sort-change |
| filter-change | filters | 筛选变化 | @filter-change |
| current-change | currentRow, oldCurrentRow | 当前行变化 | @current-change |
| header-dragend | newWidth, oldWidth, column, event | 表头拖拽结束 | @header-dragend |
| expand-change | row, expandedRows | 展开行变化 | @expand-change |

### el-table Methods透传

| 方法名 | 参数 | 返回值 | 说明 | 透传方式 |
|--------|------|--------|------|---------|
| clearSelection | - | void | 清空选择 | ref调用 |
| getSelectionRows | - | Array | 获取选中行 | ref调用 |
| toggleRowSelection | row, selected | void | 切换行选择状态 | ref调用 |
| toggleAllSelection | - | void | 切换全选状态 | ref调用 |
| toggleRowExpansion | row, expanded | void | 切换行展开状态 | ref调用 |
| setCurrentRow | row | void | 设置当前行 | ref调用 |
| clearSort | - | void | 清空排序 | ref调用 |
| clearFilter | columnKeys | void | 清空筛选 | ref调用 |
| doLayout | - | void | 重新布局 | ref调用 |
| sort | prop, order | void | 手动排序 | ref调用 |
| scrollTo | options, yCoord | void | 滚动到指定位置 | ref调用 |
| setScrollTop | top | void | 设置垂直滚动位置 | ref调用 |
| setScrollLeft | left | void | 设置水平滚动位置 | ref调用 |

### el-table-column API透传

| API名称 | 类型 | 说明 | 透传方式 |
|---------|------|------|---------|
| type | String | 列类型 | 通过columns配置 |
| index | Number/Function | 索引 | 通过columns配置 |
| label | String | 列标题 | 通过columns配置 |
| column-key | String | 列的key | 通过columns配置 |
| prop | String | 字段名 | 通过columns配置 |
| width | String/Number | 列宽度 | 通过columns配置 |
| min-width | String/Number | 最小列宽 | 通过columns配置 |
| fixed | String/Boolean | 固定列 | 通过columns配置 |
| render-header | Function | 自定义表头 | 通过columns配置 |
| sortable | Boolean/String | 排序 | 通过columns配置 |
| sort-method | Function | 排序方法 | 通过columns配置 |
| sort-by | String/Array/Function | 排序字段 | 通过columns配置 |
| sort-orders | Array | 排序顺序 | 通过columns配置 |
| resizable | Boolean | 可调整列宽 | 通过columns配置 |
| formatter | Function | 格式化函数 | 通过columns配置 |
| show-overflow-tooltip | Boolean/Object | 溢出提示 | 通过columns配置 |
| align | String | 对齐方式 | 通过columns配置 |
| header-align | String | 表头对齐方式 | 通过columns配置 |
| class-name | String | 列类名 | 通过columns配置 |
| label-class-name | String | 表头类名 | 通过columns配置 |
| selectable | Function | 可选择函数 | 通过columns配置 |
| reserve-selection | Boolean | 保留选择 | 通过columns配置 |
| filters | Array | 筛选选项 | 通过columns配置 |
| filter-placement | String | 筛选弹出位置 | 通过columns配置 |
| filter-multiple | Boolean | 多选筛选 | 通过columns配置 |
| filter-method | Function | 筛选方法 | 通过columns配置 |
| filtered-value | Array | 筛选值 | 通过columns配置 |

### el-pagination API透传

| API名称 | 类型 | 说明 | 透传方式 |
|---------|------|------|---------|
| small | Boolean | 小型分页 | 通过pagination配置 |
| background | Boolean | 背景色 | 通过pagination配置 |
| page-size | Number | 每页条数 | 自动处理 |
| total | Number | 总条目数 | 自动处理 |
| page-count | Number | 总页数 | 自动处理 |
| pager-count | Number | 页码按钮数量 | 通过pagination配置 |
| current-page | Number | 当前页数 | 自动处理 |
| layout | String | 组件布局 | 通过pagination配置 |
| page-sizes | Array | 每页显示个数选择器选项 | 通过pagination配置 |
| popper-class | String | 下拉框类名 | 通过pagination配置 |
| prev-text | String | 上一页文字 | 通过pagination配置 |
| next-text | String | 下一页文字 | 通过pagination配置 |
| disabled | Boolean | 是否禁用 | 通过pagination配置 |
| hide-on-single-page | Boolean | 只有一页时隐藏 | 通过pagination配置 |

### el-pagination Events透传

| 事件名 | 参数 | 说明 | 透传方式 |
|--------|------|------|---------|
| size-change | pageSize | 每页条数变化 | @size-change |
| current-change | currentPage | 当前页变化 | @current-change |
| prev-click | currentPage | 上一页点击 | @prev-click |
| next-click | currentPage | 下一页点击 | @next-click |

## 使用方式

### 基础表格使用
```vue
<template>
  <FuniCurdV2
    :data="tableData"
    :columns="columns"
    :pagination="paginationConfig"
    
    <!-- ElementPlus el-table 属性透传 -->
    stripe
    border
    :row-key="row => row.id"
    show-summary
    :summary-method="getSummaries"
    
    <!-- ElementPlus el-table 事件透传 -->
    @selection-change="handleSelectionChange"
    @sort-change="handleSortChange"
    @row-click="handleRowClick"
    @cell-click="handleCellClick"
  />
</template>

<script setup>
import { ref, reactive } from 'vue'

const tableData = ref([
  { id: 1, name: '张三', email: '<EMAIL>', amount: 1000 },
  { id: 2, name: '李四', email: '<EMAIL>', amount: 2000 }
])

const columns = reactive([
  { type: 'selection', width: 55, fixed: 'left' },
  { type: 'index', label: '序号', width: 60 },
  { label: '姓名', prop: 'name', width: 120, sortable: true },
  { label: '邮箱', prop: 'email', minWidth: 180, showOverflowTooltip: true },
  { label: '金额', prop: 'amount', width: 120, sortable: true, formatter: formatMoney }
])

const paginationConfig = reactive({
  pageSize: 20,
  pageSizes: [10, 20, 50, 100],
  layout: 'total, sizes, prev, pager, next, jumper',
  background: true
})

const formatMoney = (row, column, cellValue) => {
  return `¥${cellValue.toFixed(2)}`
}

const getSummaries = (param) => {
  const { columns, data } = param
  const sums = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }
    if (column.property === 'amount') {
      const values = data.map(item => Number(item.amount))
      sums[index] = `¥${values.reduce((prev, curr) => prev + curr, 0).toFixed(2)}`
    } else {
      sums[index] = ''
    }
  })
  return sums
}

const handleSelectionChange = (selection) => {
  console.log('选择变化:', selection)
}

const handleSortChange = ({ column, prop, order }) => {
  console.log('排序变化:', { column, prop, order })
}

const handleRowClick = (row, column, event) => {
  console.log('行点击:', row)
}

const handleCellClick = (row, column, cell, event) => {
  console.log('单元格点击:', { row, column })
}
</script>
```

### 高级表格配置
```vue
<template>
  <FuniCurdV2
    :data="advancedData"
    :columns="advancedColumns"
    :pagination="paginationConfig"
    
    <!-- 表格样式配置 -->
    :row-class-name="getRowClassName"
    :cell-style="getCellStyle"
    :header-cell-style="getHeaderCellStyle"
    
    <!-- 树形数据配置 -->
    :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    lazy
    :load="loadTreeNode"
    
    <!-- 筛选和排序 -->
    :default-sort="{ prop: 'date', order: 'descending' }"
    @filter-change="handleFilterChange"
    @sort-change="handleSortChange"
  />
</template>

<script setup>
const advancedColumns = reactive([
  {
    type: 'selection',
    width: 55,
    selectable: (row) => row.status !== 'disabled'
  },
  {
    label: '名称',
    prop: 'name',
    width: 180,
    sortable: true,
    filters: [
      { text: '张三', value: '张三' },
      { text: '李四', value: '李四' }
    ],
    filterMethod: (value, row) => row.name === value
  },
  {
    label: '状态',
    prop: 'status',
    width: 100,
    formatter: (row) => {
      const statusMap = {
        active: '启用',
        inactive: '禁用',
        disabled: '已删除'
      }
      return statusMap[row.status] || row.status
    },
    filters: [
      { text: '启用', value: 'active' },
      { text: '禁用', value: 'inactive' }
    ],
    filterMethod: (value, row) => row.status === value
  },
  {
    label: '创建时间',
    prop: 'date',
    width: 180,
    sortable: true,
    sortMethod: (a, b) => new Date(a.date) - new Date(b.date)
  }
])

const getRowClassName = ({ row, rowIndex }) => {
  if (row.status === 'disabled') {
    return 'disabled-row'
  }
  if (rowIndex % 2 === 1) {
    return 'odd-row'
  }
  return ''
}

const getCellStyle = ({ row, column, rowIndex, columnIndex }) => {
  if (column.property === 'status' && row.status === 'disabled') {
    return { color: '#f56c6c' }
  }
  return {}
}

const getHeaderCellStyle = ({ row, column, rowIndex, columnIndex }) => {
  if (column.property === 'status') {
    return { backgroundColor: '#f0f9ff' }
  }
  return {}
}

const loadTreeNode = (tree, treeNode, resolve) => {
  setTimeout(() => {
    resolve([
      { id: 31, name: '子节点1', leaf: true },
      { id: 32, name: '子节点2', leaf: true }
    ])
  }, 1000)
}

const handleFilterChange = (filters) => {
  console.log('筛选变化:', filters)
}

const handleSortChange = ({ column, prop, order }) => {
  console.log('排序变化:', { column, prop, order })
}
</script>

<style scoped>
:deep(.disabled-row) {
  background-color: #f5f7fa;
  color: #c0c4cc;
}

:deep(.odd-row) {
  background-color: #fafafa;
}
</style>
```

### 表格方法调用
```vue
<template>
  <FuniCurdV2
    ref="tableRef"
    :data="tableData"
    :columns="columns"
  />
  
  <div class="table-actions">
    <el-button @click="clearSelection">清空选择</el-button>
    <el-button @click="getSelection">获取选中</el-button>
    <el-button @click="toggleSelection">切换选择</el-button>
    <el-button @click="clearSort">清空排序</el-button>
    <el-button @click="clearFilter">清空筛选</el-button>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const tableRef = ref()

// 调用ElementPlus el-table方法
const clearSelection = () => {
  tableRef.value.clearSelection()
}

const getSelection = () => {
  const selection = tableRef.value.getSelectionRows()
  console.log('当前选中行:', selection)
}

const toggleSelection = () => {
  if (tableData.value.length > 0) {
    tableRef.value.toggleRowSelection(tableData.value[0])
  }
}

const clearSort = () => {
  tableRef.value.clearSort()
}

const clearFilter = () => {
  tableRef.value.clearFilter(['name', 'status'])
}
</script>
```

## 注意事项

### 1. API透传机制
- 所有ElementPlus el-table的属性都通过v-bind自动透传
- 列相关API通过columns配置传递
- 分页相关API通过pagination配置传递
- 事件通过@event-name的方式透传

### 2. 方法调用
- 所有ElementPlus el-table的方法都可以通过组件ref调用
- 方法调用方式与原生el-table完全一致
- 支持链式调用和异步操作

### 3. 事件处理
- 所有ElementPlus相关事件都可以正常监听
- 事件参数与原生ElementPlus组件一致
- 组件内部处理不会影响外部事件监听

### 4. 列配置
- 完全支持ElementPlus el-table-column的所有配置
- 通过columns数组统一管理列配置
- 支持动态列配置和条件显示

### 5. 样式定制
- 可以通过ElementPlus的样式相关属性定制表格外观
- 支持行、列、单元格级别的样式定制
- 可以通过CSS深度选择器进一步定制样式

### 6. 性能考虑
- 大数据量时建议启用虚拟滚动（如果支持）
- 合理使用懒加载和树形数据功能
- 避免在格式化函数中进行复杂计算

### 7. 兼容性
- 完全兼容ElementPlus el-table的所有功能
- 新增功能不会影响原有ElementPlus API的使用
- 可以无缝迁移现有的el-table代码
