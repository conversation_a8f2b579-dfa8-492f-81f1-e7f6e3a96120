# FuniCurdV2 最佳实践

## 配置规则

### 1. 列宽配置规则

⚠️ **重要**：根据项目配置规则，除了操作列外，其他列默认不设置 width 值。

```javascript
// ✅ 推荐做法
const columns = [
  { label: 'ID', prop: 'id' }, // 不设置width，自动撑开
  { label: '姓名', prop: 'name' },
  { label: '操作', prop: 'actions', width: 200, fixed: 'right' } // 操作列设置固定宽度
];

// ✅ 特殊情况可以设置
const columns = [
  { label: '很长的内容', prop: 'content', maxWidth: 300 }, // 内容很长时设置最大宽度
  { label: '状态', prop: 'status', minWidth: 100 } // 特殊字段设置最小宽度
];

// ❌ 避免的做法
const columns = [
  { label: 'ID', prop: 'id', width: 80 }, // 不推荐给普通列设置固定宽度
  { label: '姓名', prop: 'name', width: 120 }
];
```

### 2. 搜索配置简化

FuniCurdV2 内置了读取远程高级搜索配置的逻辑，**通常无需配置 searchConfig**：

```vue
<!-- ✅ 推荐：只配置 queryFields -->
<funi-curd-v2
  :isShowSearch="true"
  :queryFields="queryFields"
/>

<!-- ❌ 避免：手动配置 searchConfig -->
<FuniCurdV2
  :isShowSearch="true"
  :searchConfig="searchConfig"  <!-- 通常不需要 -->
/>
```

## 推荐用法

### 1. 标准数据表格配置

```vue
<template>
  <funi-curd-v2 :data="tableData" :columns="columns" :loading="loading" :pagination="paginationConfig" :toolbar="toolbarConfig" stripe border @selection-change="handleSelectionChange" @sort-change="handleSortChange" />
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';

const loading = ref(false);
const tableData = ref([]);

// 推荐：使用响应式数据管理列配置
const columns = reactive([
  {
    type: 'selection',
    width: 55,
    fixed: 'left',
    selectable: row => row.status !== 'deleted'
  },
  {
    type: 'index',
    label: '序号',
    width: 60
  },
  {
    label: 'ID',
    prop: 'id',
    width: 80,
    sortable: true,
    fixed: 'left'
  },
  {
    label: '用户名',
    prop: 'username',
    minWidth: 120,
    sortable: true,
    showOverflowTooltip: true
  },
  {
    label: '邮箱',
    prop: 'email',
    minWidth: 180,
    showOverflowTooltip: true
  },
  {
    label: '状态',
    prop: 'status',
    width: 100,
    formatter: formatStatus,
    filters: [
      { text: '启用', value: 'active' },
      { text: '禁用', value: 'inactive' }
    ],
    filterMethod: (value, row) => row.status === value
  },
  {
    label: '创建时间',
    prop: 'createTime',
    width: 180,
    sortable: true,
    formatter: formatDateTime
  },
  {
    label: '操作',
    prop: 'actions',
    width: 200,
    fixed: 'right'
  }
]);

// 推荐：合理的分页配置
const paginationConfig = reactive({
  pageSize: 20,
  pageSizes: [10, 20, 50, 100],
  layout: 'total, sizes, prev, pager, next, jumper',
  background: true
});

// 推荐：工具栏配置
const toolbarConfig = reactive({
  showRefresh: true,
  showDensity: true,
  showColumnSetting: true,
  showFullscreen: true,
  actions: [
    {
      key: 'add',
      text: '新增',
      type: 'primary',
      icon: 'Plus',
      permission: 'user:add'
    },
    {
      key: 'batchDelete',
      text: '批量删除',
      type: 'danger',
      icon: 'Delete',
      permission: 'user:delete',
      disabled: () => selectedRows.value.length === 0
    }
  ]
});

// 推荐：格式化函数
const formatStatus = (row, column, cellValue) => {
  const statusMap = {
    active: '启用',
    inactive: '禁用',
    deleted: '已删除'
  };
  return statusMap[cellValue] || cellValue;
};

const formatDateTime = (row, column, cellValue) => {
  return dayjs(cellValue).format('YYYY-MM-DD HH:mm:ss');
};

const selectedRows = ref([]);

// 推荐：统一的事件处理
const handleSelectionChange = selection => {
  selectedRows.value = selection;
};

const handleSortChange = ({ column, prop, order }) => {
  console.log('排序变化:', { prop, order });
  loadData({ sortBy: prop, sortOrder: order });
};

// 推荐：统一的数据加载函数
const loadData = async (params = {}) => {
  loading.value = true;
  try {
    const response = await api.getUserList(params);
    tableData.value = response.data.list;
    paginationConfig.total = response.data.total;
  } catch (error) {
    console.error('数据加载失败:', error);
    ElMessage.error('数据加载失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadData();
});
</script>
```

### 2. 高级表格功能配置

```vue
<template>
  <FuniCurdV2
    :data="advancedData"
    :columns="advancedColumns"
    :pagination="paginationConfig"

    <!-- 高级功能配置 -->
    :virtual-scroll="virtualScrollConfig"
    :export-config="exportConfig"
    :column-config="columnConfig"

    <!-- 样式配置 -->
    :row-class-name="getRowClassName"
    :cell-style="getCellStyle"

    <!-- 树形数据配置 -->
    :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    lazy
    :load="loadTreeNode"

    @row-click="handleRowClick"
    @cell-click="handleCellClick"
  />
</template>

<script setup>
// 推荐：虚拟滚动配置（大数据量）
const virtualScrollConfig = reactive({
  enabled: true,
  itemHeight: 50,
  threshold: 100
})

// 推荐：导出配置
const exportConfig = reactive({
  enabled: true,
  filename: '用户列表',
  formats: ['xlsx', 'csv'],
  columns: ['username', 'email', 'status', 'createTime']
})

// 推荐：列配置功能
const columnConfig = reactive({
  enabled: true,
  storage: true, // 保存到本地存储
  storageKey: 'user-table-columns'
})

// 推荐：高级列配置
const advancedColumns = reactive([
  {
    label: '用户信息',
    children: [
      { label: '用户名', prop: 'username', width: 120 },
      { label: '邮箱', prop: 'email', width: 180 }
    ]
  },
  {
    label: '状态信息',
    children: [
      {
        label: '状态',
        prop: 'status',
        width: 100,
        formatter: formatStatus,
        cellStyle: ({ row }) => {
          return row.status === 'active'
            ? { color: '#67c23a' }
            : { color: '#f56c6c' }
        }
      },
      { label: '最后登录', prop: 'lastLogin', width: 180 }
    ]
  }
])

// 推荐：行样式函数
const getRowClassName = ({ row, rowIndex }) => {
  if (row.status === 'deleted') {
    return 'deleted-row'
  }
  if (row.isVip) {
    return 'vip-row'
  }
  return ''
}

// 推荐：单元格样式函数
const getCellStyle = ({ row, column, rowIndex, columnIndex }) => {
  if (column.property === 'email' && !row.emailVerified) {
    return { backgroundColor: '#fef0f0', color: '#f56c6c' }
  }
  return {}
}

// 推荐：树形数据懒加载
const loadTreeNode = async (tree, treeNode, resolve) => {
  try {
    const children = await api.getTreeChildren(tree.id)
    resolve(children)
  } catch (error) {
    console.error('加载子节点失败:', error)
    resolve([])
  }
}
</script>

<style scoped>
:deep(.deleted-row) {
  background-color: #f5f7fa;
  color: #c0c4cc;
}

:deep(.vip-row) {
  background-color: #fff7e6;
}
</style>
```

### 3. 搜索和筛选最佳实践

```vue
<template>
  <FuniCurdV2 :data="tableData" :columns="columns" :search-config="searchConfig" :filter-config="filterConfig" @search="handleSearch" @filter="handleFilter" />
</template>

<script setup>
// 推荐：搜索配置
const searchConfig = reactive({
  enabled: true,
  fields: [
    {
      prop: 'keyword',
      label: '关键字',
      component: 'el-input',
      props: { placeholder: '请输入用户名或邮箱' }
    },
    {
      prop: 'status',
      label: '状态',
      component: 'el-select',
      options: [
        { label: '启用', value: 'active' },
        { label: '禁用', value: 'inactive' }
      ]
    },
    {
      prop: 'dateRange',
      label: '创建时间',
      component: 'el-date-picker',
      props: {
        type: 'daterange',
        rangeSeparator: '至',
        startPlaceholder: '开始日期',
        endPlaceholder: '结束日期'
      }
    }
  ],
  showReset: true,
  showExpand: true,
  expandCount: 3
});

// 推荐：筛选配置
const filterConfig = reactive({
  enabled: true,
  filters: [
    {
      key: 'department',
      label: '部门',
      type: 'select',
      options: departmentOptions,
      multiple: true
    },
    {
      key: 'role',
      label: '角色',
      type: 'checkbox',
      options: roleOptions
    }
  ]
});

// 推荐：搜索处理函数
const handleSearch = searchParams => {
  // 清理搜索参数
  const cleanParams = Object.keys(searchParams).reduce((acc, key) => {
    const value = searchParams[key];
    if (value !== '' && value !== null && value !== undefined) {
      if (key === 'dateRange' && Array.isArray(value)) {
        acc.startDate = value[0];
        acc.endDate = value[1];
      } else {
        acc[key] = value;
      }
    }
    return acc;
  }, {});

  loadData({ ...cleanParams, page: 1 });
};

const handleFilter = filterParams => {
  loadData({ ...filterParams, page: 1 });
};
</script>
```

## 避免的用法

### 1. 不推荐的配置方式

```vue
<!-- ❌ 避免：直接在模板中写复杂配置 -->
<FuniCurdV2
  :columns="[
    { label: '姓名', prop: 'name', formatter: (row) => row.name || '未知' },
    { label: '状态', prop: 'status', formatter: (row) => row.status === 1 ? '启用' : '禁用' }
  ]"
/>

<!-- ❌ 避免：在模板中写内联函数 -->
<FuniCurdV2
  @selection-change="(selection) => console.log(selection)"
  @sort-change="(sort) => loadData(sort)"
/>

<!-- ❌ 避免：过多的列配置 -->
<FuniCurdV2
  :columns="[...20个列的配置]" // 应该考虑列隐藏或分页
/>
```

### 2. 不推荐的数据处理

```vue
<script setup>
// ❌ 避免：在formatter中进行异步操作
const columns = [
  {
    label: '用户名',
    prop: 'username',
    formatter: async row => {
      // 不要在formatter中使用async
      const user = await getUserInfo(row.userId);
      return user.name;
    }
  }
];

// ❌ 避免：在事件处理中直接操作DOM
const handleRowClick = row => {
  document.querySelector('.selected-row')?.classList.remove('selected-row');
  event.target.classList.add('selected-row'); // 应该通过数据驱动
};

// ❌ 避免：不处理加载状态
const loadData = async () => {
  const data = await api.getData(); // 没有loading状态
  tableData.value = data;
};
</script>
```

### 3. 常见错误和解决方案

#### 错误1：列配置不当

```vue
<script setup>
// ❌ 错误：固定列配置不合理
const columns = [
  { label: '操作', prop: 'actions', fixed: 'left' }, // 操作列应该在右侧
  { label: '姓名', prop: 'name', width: 50 }, // 宽度太小
  { label: '描述', prop: 'description', width: 500 } // 宽度太大
];

// ✅ 正确：合理的列配置
const columns = [
  { type: 'selection', width: 55, fixed: 'left' },
  { label: 'ID', prop: 'id', width: 80, fixed: 'left' },
  { label: '姓名', prop: 'name', minWidth: 120 },
  { label: '描述', prop: 'description', minWidth: 200, showOverflowTooltip: true },
  { label: '操作', prop: 'actions', width: 200, fixed: 'right' }
];
</script>
```

#### 错误2：分页参数处理不当

```vue
<script setup>
// ❌ 错误：分页参数不正确
const handlePageChange = page => {
  loadData({ page }); // 缺少其他参数
};

// ✅ 正确：保持完整的查询参数
const currentQuery = ref({});

const handlePageChange = page => {
  loadData({ ...currentQuery.value, page });
};

const handleSearch = searchParams => {
  currentQuery.value = searchParams;
  loadData({ ...searchParams, page: 1 });
};
</script>
```

#### 错误3：性能问题

```vue
<script setup>
// ❌ 错误：频繁的数据更新
const handleCellEdit = (row, prop, value) => {
  row[prop] = value;
  saveData(row); // 每次编辑都保存
};

// ✅ 正确：批量保存或防抖处理
import { debounce } from 'lodash-es';

const debouncedSave = debounce(async row => {
  await saveData(row);
}, 1000);

const handleCellEdit = (row, prop, value) => {
  row[prop] = value;
  debouncedSave(row);
};
</script>
```

## 性能优化建议

### 1. 大数据量优化

```vue
<script setup>
// 推荐：启用虚拟滚动
const virtualScrollConfig = reactive({
  enabled: true,
  itemHeight: 50,
  threshold: 100, // 超过100条数据启用虚拟滚动
  bufferSize: 10 // 缓冲区大小
});

// 推荐：分页加载
const paginationConfig = reactive({
  pageSize: 50, // 合理的分页大小
  pageSizes: [20, 50, 100],
  serverPaging: true // 服务端分页
});
</script>
```

### 2. 渲染优化

```vue
<script setup>
// 推荐：使用条件渲染减少列数
const columns = computed(() => {
  const baseColumns = [
    { label: '姓名', prop: 'name' },
    { label: '邮箱', prop: 'email' }
  ];

  if (showAdvancedColumns.value) {
    baseColumns.push({ label: '部门', prop: 'department' }, { label: '角色', prop: 'role' });
  }

  return baseColumns;
});

// 推荐：使用防抖处理搜索
const debouncedSearch = debounce(params => {
  loadData(params);
}, 300);
</script>
```

### 3. 内存优化

```vue
<script setup>
// 推荐：及时清理大数据
onUnmounted(() => {
  tableData.value = [];
  selectedRows.value = [];
});

// 推荐：使用对象池复用
const objectPool = {
  pool: [],
  get() {
    return this.pool.pop() || {};
  },
  release(obj) {
    Object.keys(obj).forEach(key => delete obj[key]);
    this.pool.push(obj);
  }
};
</script>
```

## 业务场景最佳实践

### 1. 用户管理表格

```vue
<template>
  <FuniCurdV2 :data="users" :columns="userColumns" :toolbar="userToolbar" :search-config="userSearchConfig" selection @selection-change="handleUserSelection" />
</template>

<script setup>
// 用户管理的标准配置
const userColumns = reactive([
  { type: 'selection', width: 55 },
  { label: 'ID', prop: 'id', width: 80, sortable: true },
  { label: '用户名', prop: 'username', minWidth: 120, sortable: true },
  { label: '邮箱', prop: 'email', minWidth: 180 },
  { label: '角色', prop: 'roleName', width: 120 },
  { label: '状态', prop: 'status', width: 100, formatter: formatUserStatus },
  { label: '最后登录', prop: 'lastLoginTime', width: 180, sortable: true },
  { label: '操作', prop: 'actions', width: 200, fixed: 'right' }
]);

const userToolbar = reactive([
  { key: 'add', text: '新增用户', type: 'primary', permission: 'user:add' },
  { key: 'export', text: '导出', type: 'default', permission: 'user:export' },
  { key: 'batchDelete', text: '批量删除', type: 'danger', permission: 'user:delete' }
]);
</script>
```

### 2. 订单管理表格

```vue
<template>
  <FuniCurdV2 :data="orders" :columns="orderColumns" :summary-config="orderSummaryConfig" show-summary />
</template>

<script setup>
// 订单管理的标准配置
const orderColumns = reactive([
  { label: '订单号', prop: 'orderNo', width: 150, fixed: 'left' },
  { label: '客户名称', prop: 'customerName', minWidth: 120 },
  { label: '订单金额', prop: 'amount', width: 120, sortable: true, formatter: formatMoney },
  { label: '订单状态', prop: 'status', width: 100, formatter: formatOrderStatus },
  { label: '创建时间', prop: 'createTime', width: 180, sortable: true }
]);

const orderSummaryConfig = reactive({
  fields: ['amount'],
  formatter: {
    amount: sum => `总计：¥${sum.toFixed(2)}`
  }
});
</script>
```

### 3. 日志查看表格

```vue
<template>
  <FuniCurdV2 :data="logs" :columns="logColumns" :virtual-scroll="{ enabled: true, itemHeight: 40 }" :export-config="{ enabled: true, formats: ['txt', 'csv'] }" />
</template>

<script setup>
// 日志查看的标准配置
const logColumns = reactive([
  { label: '时间', prop: 'timestamp', width: 180, sortable: true },
  { label: '级别', prop: 'level', width: 80, formatter: formatLogLevel },
  { label: '模块', prop: 'module', width: 120 },
  { label: '消息', prop: 'message', minWidth: 300, showOverflowTooltip: true },
  { label: 'IP地址', prop: 'ip', width: 120 }
]);
</script>
```

## 总结

### 关键原则

1. **配置驱动**：通过配置而非硬编码实现表格功能
2. **性能优先**：合理使用虚拟滚动、分页、防抖等优化手段
3. **用户体验**：提供清晰的反馈和合理的交互
4. **数据安全**：合理处理权限控制和数据验证
5. **可维护性**：保持配置的清晰和一致性

### 开发建议

1. 优先使用响应式配置对象
2. 合理设置列宽和表格高度
3. 统一处理数据加载和错误处理
4. 遵循ElementPlus的API规范
5. 注意大数据量的性能优化
