# FuniAuditButtomBtn 最佳实践

## 1. 组件使用原则

### 1.1 使用场景限制

**✅ 正确使用场景**
- 工作流审核页面
- 有businessId的详情页面
- bizName为'审核'或'audit'的页面
- 需要动态审核按钮的场景

**❌ 错误使用场景**
- 普通表单提交页面
- 静态按钮展示
- 非工作流业务场景
- 没有businessId的页面

```vue
<!-- ✅ 正确：工作流审核场景 -->
<FuniDetail bizName="审核" :businessId="businessId">
  <template #auditbtns="{ auditButtons }">
    <FuniAuditButtomBtn
      :auditButtons="auditButtons"
      @audit-click="handleAuditClick"
    />
  </template>
</FuniDetail>

<!-- ❌ 错误：普通表单场景 -->
<FuniForm>
  <FuniAuditButtomBtn @audit-click="handleSubmit" />
</FuniForm>
```

### 1.2 组件集成原则

**必须配合使用的组件**
- `FuniDetail`: 提供工作流页面结构
- `FuniBusAuditDrawer`: 提供审核意见输入
- `FuniFileTable`: 提供工作流附件管理

```vue
<!-- ✅ 完整的工作流组件集成 -->
<template>
  <FuniDetail bizName="审核" :businessId="businessId">
    <!-- 业务内容 -->
    <template #content>
      <FuniForm :schema="schema" v-model="data" :readonly="true" />
      <FuniFileTable :params="fileParams" :onlyShow="true" />
    </template>
    
    <!-- 审核按钮 -->
    <template #auditbtns="{ auditButtons }">
      <FuniAuditButtomBtn
        :auditButtons="auditButtons || mockButtons"
        @audit-click="handleAuditClick"
      />
    </template>
  </FuniDetail>
  
  <!-- 审核抽屉 -->
  <FuniBusAuditDrawer
    :businessId="businessId"
    @audit-event="handleAuditEvent"
  />
</template>
```

## 2. 配置最佳实践

### 2.1 按钮配置规范

**按钮文本规范**
```javascript
// ✅ 推荐：简洁明确的动作描述
const goodButtonTexts = [
  { key: '同意', action: 'APPROVE' },
  { key: '拒绝', action: 'REJECT' },
  { key: '退回', action: 'RETURN' },
  { key: '转办', action: 'TRANSFER' }
];

// ❌ 避免：冗长或模糊的描述
const badButtonTexts = [
  { key: '我同意这个申请', action: 'APPROVE' },
  { key: '操作', action: 'UNKNOWN' },
  { key: '处理一下', action: 'HANDLE' }
];
```

**动作标识规范**
```javascript
// ✅ 推荐：大写字母+下划线，语义明确
const goodActions = [
  'APPROVE',           // 同意
  'REJECT',            // 拒绝
  'RETURN_MODIFY',     // 退回修改
  'DEPT_APPROVE',      // 部门审批
  'FINANCE_AUDIT'      // 财务审核
];

// ❌ 避免：小写、中文或无意义标识
const badActions = [
  'approve',           // 应该大写
  '同意',              // 不应该用中文
  'btn1',              // 无语义
  'doSomething'        // 不明确
];
```

### 2.2 样式配置规范

**按钮类型语义化**
```javascript
const semanticButtonStyles = {
  // 积极操作 - 绿色/蓝色系
  approve: { type: 'primary' },      // 同意、通过、确认
  success: { type: 'success' },      // 完成、成功
  
  // 消极操作 - 红色系
  reject: { type: 'danger' },        // 拒绝、删除、不予受理
  
  // 中性操作 - 橙色/灰色系
  return: { type: 'warning' },       // 退回、修改、暂停
  transfer: { type: 'info' },        // 转办、查看、其他
  cancel: { type: 'default' }        // 取消、关闭
};

// ✅ 推荐：语义化的按钮配置
const semanticButtons = [
  {
    key: '同意',
    action: 'APPROVE',
    props: { type: 'primary', icon: 'Check' }
  },
  {
    key: '拒绝',
    action: 'REJECT',
    props: { type: 'danger', icon: 'Close' }
  },
  {
    key: '退回修改',
    action: 'RETURN_MODIFY',
    props: { type: 'warning', icon: 'RefreshLeft' }
  }
];
```

### 2.3 Mock数据管理

**分模块组织Mock数据**
```javascript
// mock/audit-buttons.js
export const auditButtonsMock = {
  // 请假审批
  leave: [
    {
      key: '部门同意',
      action: 'DEPT_APPROVE',
      props: { type: 'primary', icon: 'Check' }
    },
    {
      key: '人事确认',
      action: 'HR_CONFIRM',
      props: { type: 'success', icon: 'CircleCheck' }
    },
    {
      key: '需要补假条',
      action: 'NEED_LEAVE_NOTE',
      props: { type: 'warning', icon: 'Warning' }
    }
  ],
  
  // 报销审批
  expense: [
    {
      key: '财务审核',
      action: 'FINANCE_AUDIT',
      props: { type: 'primary', icon: 'Money' }
    },
    {
      key: '金额核实',
      action: 'AMOUNT_VERIFY',
      props: { type: 'info', icon: 'View' }
    }
  ],
  
  // 通用审批
  common: [
    {
      key: '同意',
      action: 'APPROVE',
      props: { type: 'primary' }
    },
    {
      key: '拒绝',
      action: 'REJECT',
      props: { type: 'danger' }
    }
  ]
};

// 使用Mock数据
import { auditButtonsMock } from '@/mock/audit-buttons.js';

const getAuditButtons = (businessType) => {
  return auditButtonsMock[businessType] || auditButtonsMock.common;
};
```

## 3. 事件处理最佳实践

### 3.1 事件处理结构

```vue
<script setup>
const handleAuditClick = async (action) => {
  // 1. 参数验证
  if (!action) {
    ElMessage.error('审核动作不能为空');
    return;
  }
  
  // 2. 权限检查
  if (!hasAuditPermission(action)) {
    ElMessage.error('您没有执行此操作的权限');
    return;
  }
  
  // 3. 业务逻辑分发
  try {
    switch (action) {
      case 'APPROVE':
        await handleApprove();
        break;
      case 'REJECT':
        await handleReject();
        break;
      case 'RETURN_MODIFY':
        await handleReturnModify();
        break;
      default:
        await handleCustomAction(action);
    }
  } catch (error) {
    console.error('审核操作失败:', error);
    ElMessage.error('操作失败：' + error.message);
  }
};

// 具体业务处理函数
const handleApprove = async () => {
  // 直接同意逻辑
  const result = await auditApi.approve({
    businessId: businessId.value,
    opinion: '同意'
  });
  
  if (result.success) {
    ElMessage.success('审核通过');
    router.push('/audit/list');
  }
};

const handleReject = async () => {
  // 需要填写意见，打开审核抽屉
  auditDrawerVisible.value = true;
  currentAction.value = 'REJECT';
};
</script>
```

### 3.2 错误处理策略

```javascript
// ✅ 推荐：完整的错误处理
const handleAuditClick = async (action) => {
  const loading = ElLoading.service({ fullscreen: true });
  
  try {
    // 业务逻辑
    await performAudit(action);
    
    // 成功反馈
    ElMessage.success('操作成功');
    
    // 页面跳转或刷新
    await refreshData();
    
  } catch (error) {
    // 错误分类处理
    if (error.code === 'PERMISSION_DENIED') {
      ElMessage.error('权限不足');
    } else if (error.code === 'BUSINESS_LOCKED') {
      ElMessage.error('业务已被锁定，无法操作');
    } else {
      ElMessage.error('操作失败：' + error.message);
    }
    
    // 错误日志
    console.error('Audit operation failed:', {
      action,
      businessId: businessId.value,
      error
    });
    
  } finally {
    loading.close();
  }
};
```

## 4. 性能优化实践

### 4.1 响应式数据优化

```vue
<script setup>
import { ref, computed, shallowRef } from 'vue';

// ✅ 推荐：使用computed计算动态属性
const auditButtons = computed(() => {
  const baseButtons = getBaseButtons();
  
  return baseButtons.map(button => ({
    ...button,
    props: {
      ...button.props,
      loading: loadingStates.value[button.action] || false,
      disabled: !hasPermission(button.action)
    }
  }));
});

// ✅ 推荐：使用shallowRef优化大对象
const loadingStates = shallowRef({});

// ❌ 避免：在模板中直接计算
// <FuniAuditButtomBtn :auditButtons="buttons.map(b => ({...b, props: {...}}))" />
</script>
```

### 4.2 事件处理优化

```javascript
// ✅ 推荐：防抖处理避免重复点击
import { debounce } from 'lodash-es';

const handleAuditClick = debounce(async (action) => {
  // 审核逻辑
}, 300);

// ✅ 推荐：使用状态管理避免重复请求
const isProcessing = ref(false);

const handleAuditClick = async (action) => {
  if (isProcessing.value) {
    return; // 防止重复提交
  }
  
  isProcessing.value = true;
  try {
    await performAudit(action);
  } finally {
    isProcessing.value = false;
  }
};
```

## 5. 可访问性最佳实践

### 5.1 键盘导航支持

```javascript
// 为按钮添加键盘快捷键
const auditButtons = ref([
  {
    key: '同意 (Alt+A)',
    action: 'APPROVE',
    props: {
      type: 'primary',
      // 可以通过全局键盘事件监听实现快捷键
    }
  },
  {
    key: '拒绝 (Alt+R)',
    action: 'REJECT',
    props: {
      type: 'danger'
    }
  }
]);

// 键盘事件处理
onMounted(() => {
  const handleKeydown = (event) => {
    if (event.altKey) {
      switch (event.key.toLowerCase()) {
        case 'a':
          handleAuditClick('APPROVE');
          break;
        case 'r':
          handleAuditClick('REJECT');
          break;
      }
    }
  };
  
  document.addEventListener('keydown', handleKeydown);
  
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown);
  });
});
```

### 5.2 屏幕阅读器支持

```javascript
// 为按钮添加aria标签
const accessibleButtons = ref([
  {
    key: '同意',
    action: 'APPROVE',
    props: {
      type: 'primary',
      'aria-label': '同意当前审核申请',
      'aria-describedby': 'approve-help-text'
    }
  }
]);
```

## 6. 测试最佳实践

### 6.1 单元测试

```javascript
// tests/FuniAuditButtomBtn.test.js
import { mount } from '@vue/test-utils';
import FuniAuditButtomBtn from '@/components/FuniAuditButtomBtn/index.vue';

describe('FuniAuditButtomBtn', () => {
  test('应该渲染默认按钮', () => {
    const wrapper = mount(FuniAuditButtomBtn);
    expect(wrapper.findAll('.el-button')).toHaveLength(4);
  });
  
  test('应该触发audit-click事件', async () => {
    const wrapper = mount(FuniAuditButtomBtn);
    await wrapper.find('.el-button').trigger('click');
    expect(wrapper.emitted('auditClick')).toBeTruthy();
  });
  
  test('应该支持自定义按钮配置', () => {
    const customButtons = [
      { key: '测试', action: 'TEST', props: { type: 'primary' } }
    ];
    
    const wrapper = mount(FuniAuditButtomBtn, {
      props: { auditButtons: customButtons }
    });
    
    expect(wrapper.text()).toContain('测试');
  });
});
```

### 6.2 集成测试

```javascript
// tests/integration/audit-workflow.test.js
describe('审核工作流集成测试', () => {
  test('完整的审核流程', async () => {
    // 1. 渲染审核页面
    const wrapper = mount(AuditPage, {
      props: { businessId: 'TEST_001' }
    });
    
    // 2. 点击审核按钮
    await wrapper.find('[data-action="APPROVE"]').trigger('click');
    
    // 3. 验证API调用
    expect(mockAuditApi.approve).toHaveBeenCalledWith({
      businessId: 'TEST_001',
      action: 'APPROVE'
    });
    
    // 4. 验证页面跳转
    expect(mockRouter.push).toHaveBeenCalledWith('/audit/list');
  });
});
```

## 7. 常见问题解决

### 7.1 按钮不显示问题

```javascript
// 问题诊断清单
const debugAuditButtons = () => {
  console.log('Debug信息:');
  console.log('businessId:', businessId.value);
  console.log('bizName:', bizName.value);
  console.log('auditButtons:', auditButtons.value);
  console.log('渲染条件:', !!businessId.value && ['审核', 'audit'].includes(bizName.value));
};

// 解决方案
const ensureAuditButtonsVisible = () => {
  // 1. 确保businessId存在
  if (!businessId.value) {
    console.warn('businessId为空，审核按钮不会显示');
  }
  
  // 2. 确保bizName正确
  if (!['审核', 'audit'].includes(bizName.value)) {
    console.warn('bizName不是审核模式，审核按钮不会显示');
  }
  
  // 3. 提供Mock数据
  if (!auditButtons.value || auditButtons.value.length === 0) {
    auditButtons.value = mockAuditButtons;
  }
};
```

### 7.2 权限控制问题

```javascript
// 权限检查最佳实践
const checkAuditPermission = (action) => {
  // 1. 检查用户角色
  const userRole = getCurrentUserRole();
  
  // 2. 检查业务状态
  const businessStatus = getBusinessStatus();
  
  // 3. 检查节点权限
  const nodePermissions = getNodePermissions();
  
  return nodePermissions.includes(action) && 
         businessStatus === 'pending' &&
         hasRolePermission(userRole, action);
};
```
