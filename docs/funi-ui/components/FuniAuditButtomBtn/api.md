# FuniAuditButtomBtn API文档

## 组件概述

FuniAuditButtomBtn是工作流审核按钮组件，**仅用于工作流审核场景**，需要在详情/审核模式且有businessId时使用。按钮配置由工作流系统根据当前节点动态返回。

⚠️ **重要提醒**: 
- 此组件仅用于工作流审核场景
- 需要businessId和审核模式才能正常工作
- 按钮配置由工作流系统动态提供

## Props

| 参数名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| auditButtons | Array | 默认按钮配置 | - | 审核按钮配置数组 |

## Events

| 事件名 | 参数 | 说明 | 触发时机 |
|--------|------|------|---------|
| auditClick | action: string | 审核按钮点击事件 | 点击任意审核按钮时 |

## auditButtons配置结构

```typescript
interface AuditButtonConfig {
  key: string;                     // 按钮显示文本
  action: string;                  // 审核动作标识
  props?: {
    type?: 'primary' | 'success' | 'warning' | 'danger' | 'info';
    size?: 'large' | 'default' | 'small';
    disabled?: boolean;
    loading?: boolean;
    // ... 更多el-button属性
  };
}
```

## 默认按钮配置

```javascript
const defaultAuditButtons = [
  {
    key: '回退',
    action: 'BACK'
  },
  {
    key: '退件',
    action: 'RETURN'
  },
  {
    key: '不予受理',
    action: 'NOT_ACCEPT'
  },
  {
    key: '同意',
    action: 'COMMIT'
  }
];
```

## 渲染条件

### 自动渲染条件
FuniAuditButtomBtn会在以下条件下自动渲染：

```javascript
const shouldRender = computed(() => {
  return !!businessId.value && 
         ['审核', 'audit'].includes(bizName.value)
})
```

### 在FuniDetail中的集成
```vue
<!-- FuniDetail自动集成审核按钮 -->
<template>
  <FuniDetail bizName="审核" :businessId="businessId">
    <!-- 审核按钮插槽 -->
    <template #auditbtns="{ auditButtons }">
      <FuniAuditButtomBtn
        :auditButtons="auditButtons || mockAuditButtons"
        @audit-click="handleAuditClick"
      />
    </template>
  </FuniDetail>
</template>
```

## 动态按钮配置

### 工作流系统动态返回
工作流系统会根据当前节点和用户权限动态返回按钮配置：

```javascript
// 工作流系统返回的按钮配置示例
const dynamicButtons = [
  {
    key: '同意',
    action: 'APPROVE',
    props: { type: 'primary' }
  },
  {
    key: '拒绝',
    action: 'REJECT',
    props: { type: 'danger' }
  },
  {
    key: '退回修改',
    action: 'RETURN_MODIFY',
    props: { type: 'warning' }
  }
];
```

### Mock数据配置
开发阶段需要提供Mock数据：

```javascript
const mockAuditButtons = [
  {
    key: '同意',
    action: 'APPROVE',
    props: { type: 'primary' }
  },
  {
    key: '拒绝',
    action: 'REJECT',
    props: { type: 'danger' }
  },
  {
    key: '退回',
    action: 'RETURN',
    props: { type: 'warning' }
  },
  {
    key: '转办',
    action: 'TRANSFER',
    props: { type: 'info' }
  }
];
```

## 使用示例

### 基础使用
```vue
<template>
  <FuniAuditButtomBtn
    :auditButtons="auditButtons"
    @audit-click="handleAuditClick"
  />
</template>

<script setup>
import { ref } from 'vue'

const auditButtons = ref([
  {
    key: '同意',
    action: 'APPROVE',
    props: { type: 'primary' }
  },
  {
    key: '拒绝',
    action: 'REJECT',
    props: { type: 'danger' }
  }
])

const handleAuditClick = (action) => {
  console.log('审核动作:', action)
  
  switch (action) {
    case 'APPROVE':
      handleApprove()
      break
    case 'REJECT':
      handleReject()
      break
    default:
      console.log('未知审核动作:', action)
  }
}

const handleApprove = () => {
  // 同意逻辑
  console.log('执行同意操作')
}

const handleReject = () => {
  // 拒绝逻辑
  console.log('执行拒绝操作')
}
</script>
```

### 在FuniDetail中使用
```vue
<template>
  <FuniDetail 
    :steps="steps" 
    bizName="审核"
    :businessId="businessId"
    :sysId="sysId"
  >
    <!-- 申请信息 -->
    <template #applicationInfo>
      <FuniForm :schema="applicationSchema" v-model="applicationData" :readonly="true" />
    </template>
    
    <!-- 附件材料 -->
    <template #attachments>
      <FuniFileTable :params="fileTableParams" :onlyShow="true" />
    </template>
    
    <!-- 自定义审核按钮 -->
    <template #auditbtns="{ auditButtons }">
      <FuniAuditButtomBtn
        :auditButtons="auditButtons || mockAuditButtons"
        @audit-click="handleAuditClick"
      />
    </template>
  </FuniDetail>
  
  <!-- 审核抽屉 -->
  <FuniBusAuditDrawer 
    :businessId="businessId"
    :sysId="sysId"
    @audit-event="handleAuditEvent"
  />
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const businessId = ref(route.query.businessId)
const sysId = ref(route.query.sysId)

// Mock审核按钮数据
const mockAuditButtons = ref([
  {
    key: '同意',
    action: 'APPROVE',
    props: { type: 'primary' }
  },
  {
    key: '拒绝',
    action: 'REJECT',
    props: { type: 'danger' }
  },
  {
    key: '退回修改',
    action: 'RETURN_MODIFY',
    props: { type: 'warning' }
  }
])

const steps = ref([
  {
    title: '申请信息',
    slot: 'applicationInfo'
  },
  {
    title: '附件材料',
    slot: 'attachments'
  }
])

const fileTableParams = computed(() => ({
  businessId: businessId.value,
  sysId: sysId.value
}))

const handleAuditClick = (action) => {
  console.log('审核动作:', action)
  
  // 根据不同动作执行不同逻辑
  switch (action) {
    case 'APPROVE':
      handleApprove()
      break
    case 'REJECT':
      handleReject()
      break
    case 'RETURN_MODIFY':
      handleReturnModify()
      break
    default:
      console.log('未知审核动作:', action)
  }
}

const handleApprove = async () => {
  try {
    // 调用审核API
    await auditApi.approve({
      businessId: businessId.value,
      sysId: sysId.value,
      action: 'APPROVE',
      opinion: '同意申请'
    })
    
    ElMessage.success('审核成功')
    // 跳转到列表页或其他页面
  } catch (error) {
    ElMessage.error('审核失败')
  }
}

const handleReject = async () => {
  try {
    await auditApi.reject({
      businessId: businessId.value,
      sysId: sysId.value,
      action: 'REJECT',
      opinion: '不符合条件，拒绝申请'
    })
    
    ElMessage.success('审核成功')
  } catch (error) {
    ElMessage.error('审核失败')
  }
}

const handleReturnModify = async () => {
  try {
    await auditApi.returnModify({
      businessId: businessId.value,
      sysId: sysId.value,
      action: 'RETURN_MODIFY',
      opinion: '请补充相关材料'
    })
    
    ElMessage.success('退回成功')
  } catch (error) {
    ElMessage.error('退回失败')
  }
}

const handleAuditEvent = (event) => {
  console.log('审核事件:', event)
  // 处理审核抽屉的事件
}
</script>
```

## 常见审核动作

| 动作标识 | 中文名称 | 说明 | 推荐按钮样式 |
|----------|----------|------|-------------|
| APPROVE | 同意 | 审核通过 | type="primary" |
| REJECT | 拒绝 | 审核拒绝 | type="danger" |
| RETURN | 退回 | 退回上一节点 | type="warning" |
| RETURN_MODIFY | 退回修改 | 退回申请人修改 | type="warning" |
| TRANSFER | 转办 | 转给其他人处理 | type="info" |
| BACK | 回退 | 回退到指定节点 | type="default" |
| NOT_ACCEPT | 不予受理 | 不予受理申请 | type="danger" |
| SUSPEND | 挂起 | 暂停流程 | type="info" |

## 与FuniBusAuditDrawer的配合

FuniAuditButtomBtn通常与FuniBusAuditDrawer配合使用：

```vue
<template>
  <!-- 审核按钮 -->
  <FuniAuditButtomBtn
    :auditButtons="auditButtons"
    @audit-click="handleAuditClick"
  />
  
  <!-- 审核抽屉 -->
  <FuniBusAuditDrawer 
    :businessId="businessId"
    :sysId="sysId"
    @audit-event="handleAuditEvent"
  />
</template>
```

## 注意事项

### 1. 使用条件
- 必须在工作流审核场景中使用
- 需要businessId和审核模式
- 通常与FuniBusAuditDrawer配合使用

### 2. 按钮配置
- 优先使用工作流系统动态返回的按钮配置
- 开发阶段需要提供完整的Mock数据
- 按钮样式应该符合业务语义

### 3. 事件处理
- auditClick事件只传递action参数
- 具体的审核逻辑需要在事件处理函数中实现
- 建议根据action类型分别处理

### 4. 权限控制
- 按钮显示由工作流系统控制
- 不同用户可能看到不同的按钮
- 按钮可能根据节点状态动态变化

## 常见问题

### Q: 按钮不显示怎么办？
A: 检查businessId是否存在，bizName是否为'审核'或'audit'

### Q: 如何自定义按钮样式？
A: 通过auditButtons中的props属性配置ElementPlus按钮属性

### Q: 如何获取动态按钮配置？
A: 工作流系统会根据businessId和当前用户自动返回按钮配置

### Q: 如何处理审核意见？
A: 通常配合FuniBusAuditDrawer使用，在审核抽屉中填写意见

### Q: 如何实现按钮权限控制？
A: 工作流系统会根据用户权限和节点配置自动控制按钮显示
