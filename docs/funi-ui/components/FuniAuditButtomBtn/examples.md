# FuniAuditButtomBtn 使用示例

## 基础示例

### 1. 最简单的使用

```vue
<template>
  <FuniAuditButtomBtn @audit-click="handleAuditClick" />
</template>

<script setup>
const handleAuditClick = (action) => {
  console.log('审核动作:', action);
  // 处理审核逻辑
};
</script>
```

### 2. 自定义按钮配置

```vue
<template>
  <FuniAuditButtomBtn
    :auditButtons="customButtons"
    @audit-click="handleAuditClick"
  />
</template>

<script setup>
import { ref } from 'vue';

const customButtons = ref([
  {
    key: '同意',
    action: 'APPROVE',
    props: { type: 'primary' }
  },
  {
    key: '拒绝',
    action: 'REJECT',
    props: { type: 'danger' }
  }
]);

const handleAuditClick = (action) => {
  switch (action) {
    case 'APPROVE':
      console.log('审核通过');
      break;
    case 'REJECT':
      console.log('审核拒绝');
      break;
  }
};
</script>
```

## 工作流集成示例

### 1. 在FuniDetail中使用

```vue
<template>
  <FuniDetail 
    :steps="steps" 
    bizName="审核"
    :businessId="businessId"
    :sysId="sysId"
  >
    <!-- 申请信息 -->
    <template #applicationInfo>
      <FuniForm 
        :schema="applicationSchema" 
        v-model="applicationData" 
        :readonly="true" 
      />
    </template>
    
    <!-- 附件材料 -->
    <template #attachments>
      <FuniFileTable 
        :params="fileTableParams" 
        :onlyShow="true" 
      />
    </template>
    
    <!-- 审核按钮 -->
    <template #auditbtns="{ auditButtons }">
      <FuniAuditButtomBtn
        :auditButtons="auditButtons || mockAuditButtons"
        @audit-click="handleAuditClick"
      />
    </template>
  </FuniDetail>
  
  <!-- 审核抽屉 -->
  <FuniBusAuditDrawer 
    :businessId="businessId"
    :sysId="sysId"
    @audit-event="handleAuditEvent"
  />
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const businessId = ref(route.query.businessId);
const sysId = ref(route.query.sysId);

// Mock审核按钮数据
const mockAuditButtons = ref([
  {
    key: '同意',
    action: 'APPROVE',
    props: { type: 'primary' }
  },
  {
    key: '拒绝',
    action: 'REJECT',
    props: { type: 'danger' }
  },
  {
    key: '退回修改',
    action: 'RETURN_MODIFY',
    props: { type: 'warning' }
  }
]);

const steps = ref([
  {
    title: '申请信息',
    slot: 'applicationInfo'
  },
  {
    title: '附件材料',
    slot: 'attachments'
  }
]);

const fileTableParams = computed(() => ({
  businessId: businessId.value,
  sysId: sysId.value
}));

const handleAuditClick = (action) => {
  console.log('审核动作:', action);
  // 打开审核抽屉或直接处理
  if (action === 'APPROVE') {
    // 直接同意逻辑
    handleDirectApprove();
  } else {
    // 打开审核抽屉填写意见
    openAuditDrawer(action);
  }
};

const handleDirectApprove = async () => {
  try {
    // 调用审核API
    await auditApi.approve({
      businessId: businessId.value,
      action: 'APPROVE',
      opinion: '同意'
    });
    
    ElMessage.success('审核成功');
    // 跳转到列表页
    router.push('/audit/list');
  } catch (error) {
    ElMessage.error('审核失败：' + error.message);
  }
};

const openAuditDrawer = (action) => {
  // 打开审核抽屉的逻辑
  console.log('打开审核抽屉，动作:', action);
};

const handleAuditEvent = (event) => {
  console.log('审核事件:', event);
  // 处理审核完成后的逻辑
};
</script>
```

### 2. 请假审批示例

```vue
<template>
  <div class="leave-audit-page">
    <FuniDetail 
      :steps="leaveSteps" 
      bizName="审核"
      :businessId="leaveId"
      sysId="LEAVE_SYSTEM"
    >
      <!-- 请假信息 -->
      <template #leaveInfo>
        <FuniForm 
          :schema="leaveSchema" 
          v-model="leaveData" 
          :readonly="true" 
        />
      </template>
      
      <!-- 请假附件 -->
      <template #leaveAttachments>
        <FuniFileTable 
          :params="{ businessId: leaveId, sysId: 'LEAVE_SYSTEM' }" 
          :onlyShow="true" 
        />
      </template>
      
      <!-- 审核按钮 -->
      <template #auditbtns="{ auditButtons }">
        <FuniAuditButtomBtn
          :auditButtons="auditButtons || leaveAuditButtons"
          @audit-click="handleLeaveAudit"
        />
      </template>
    </FuniDetail>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const leaveId = ref('LEAVE_202401001');

const leaveAuditButtons = ref([
  {
    key: '部门同意',
    action: 'DEPT_APPROVE',
    props: { type: 'primary', icon: 'Check' }
  },
  {
    key: '人事确认',
    action: 'HR_CONFIRM',
    props: { type: 'success', icon: 'CircleCheck' }
  },
  {
    key: '需要补假条',
    action: 'NEED_LEAVE_NOTE',
    props: { type: 'warning', icon: 'Warning' }
  },
  {
    key: '拒绝请假',
    action: 'REJECT_LEAVE',
    props: { type: 'danger', icon: 'Close' }
  }
]);

const leaveSteps = ref([
  { title: '请假信息', slot: 'leaveInfo' },
  { title: '请假附件', slot: 'leaveAttachments' }
]);

const handleLeaveAudit = async (action) => {
  console.log('请假审核动作:', action);
  
  switch (action) {
    case 'DEPT_APPROVE':
      await handleDeptApprove();
      break;
    case 'HR_CONFIRM':
      await handleHrConfirm();
      break;
    case 'NEED_LEAVE_NOTE':
      await handleNeedLeaveNote();
      break;
    case 'REJECT_LEAVE':
      await handleRejectLeave();
      break;
  }
};

const handleDeptApprove = async () => {
  try {
    await leaveApi.deptApprove({
      leaveId: leaveId.value,
      opinion: '部门同意该请假申请'
    });
    ElMessage.success('部门审批成功');
  } catch (error) {
    ElMessage.error('审批失败：' + error.message);
  }
};
</script>
```

## 高级示例

### 1. 动态按钮配置

```vue
<template>
  <FuniAuditButtomBtn
    :auditButtons="dynamicButtons"
    @audit-click="handleAuditClick"
  />
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

const userRole = ref('');
const nodeType = ref('');
const businessStatus = ref('');

// 动态计算按钮配置
const dynamicButtons = computed(() => {
  const buttons = [];
  
  // 基础按钮
  buttons.push({
    key: '同意',
    action: 'APPROVE',
    props: { type: 'primary' }
  });
  
  // 根据用户角色添加按钮
  if (userRole.value === 'manager') {
    buttons.push({
      key: '直接通过',
      action: 'DIRECT_APPROVE',
      props: { type: 'success' }
    });
  }
  
  // 根据节点类型添加按钮
  if (nodeType.value === 'final') {
    buttons.push({
      key: '最终确认',
      action: 'FINAL_CONFIRM',
      props: { type: 'primary', size: 'large' }
    });
  }
  
  // 根据业务状态控制按钮状态
  if (businessStatus.value === 'locked') {
    buttons.forEach(btn => {
      btn.props = { ...btn.props, disabled: true };
    });
  }
  
  return buttons;
});

onMounted(async () => {
  // 获取用户角色和节点信息
  const userInfo = await getUserInfo();
  userRole.value = userInfo.role;
  nodeType.value = userInfo.nodeType;
  businessStatus.value = userInfo.businessStatus;
});

const handleAuditClick = (action) => {
  console.log('动态审核动作:', action);
};
</script>
```

### 2. 批量审核示例

```vue
<template>
  <div class="batch-audit">
    <el-table :data="auditList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="title" label="标题" />
      <el-table-column prop="applicant" label="申请人" />
      <el-table-column prop="createTime" label="申请时间" />
    </el-table>
    
    <div class="batch-audit-buttons" v-if="selectedItems.length > 0">
      <FuniAuditButtomBtn
        :auditButtons="batchAuditButtons"
        @audit-click="handleBatchAudit"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const auditList = ref([]);
const selectedItems = ref([]);

const batchAuditButtons = ref([
  {
    key: '批量同意',
    action: 'BATCH_APPROVE',
    props: { type: 'primary', icon: 'Check' }
  },
  {
    key: '批量拒绝',
    action: 'BATCH_REJECT',
    props: { type: 'danger', icon: 'Close' }
  }
]);

const handleSelectionChange = (selection) => {
  selectedItems.value = selection;
};

const handleBatchAudit = async (action) => {
  const businessIds = selectedItems.value.map(item => item.businessId);
  
  try {
    if (action === 'BATCH_APPROVE') {
      await auditApi.batchApprove({ businessIds });
      ElMessage.success(`批量同意 ${businessIds.length} 个申请`);
    } else if (action === 'BATCH_REJECT') {
      await auditApi.batchReject({ businessIds });
      ElMessage.success(`批量拒绝 ${businessIds.length} 个申请`);
    }
    
    // 刷新列表
    await loadAuditList();
    selectedItems.value = [];
  } catch (error) {
    ElMessage.error('批量操作失败：' + error.message);
  }
};
</script>

<style scoped>
.batch-audit-buttons {
  margin-top: 16px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 4px;
}
</style>
```

### 3. 条件渲染示例

```vue
<template>
  <div>
    <!-- 只在审核模式下显示 -->
    <FuniAuditButtomBtn
      v-if="isAuditMode"
      :auditButtons="auditButtons"
      @audit-click="handleAuditClick"
    />
    
    <!-- 普通模式下显示其他按钮 -->
    <div v-else class="normal-buttons">
      <el-button @click="handleEdit">编辑</el-button>
      <el-button @click="handleDelete">删除</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  bizName: String,
  businessId: String
});

// 判断是否为审核模式
const isAuditMode = computed(() => {
  return !!props.businessId && 
         ['审核', 'audit'].includes(props.bizName);
});

const auditButtons = ref([
  {
    key: '同意',
    action: 'APPROVE',
    props: { type: 'primary' }
  },
  {
    key: '拒绝',
    action: 'REJECT',
    props: { type: 'danger' }
  }
]);

const handleAuditClick = (action) => {
  console.log('审核动作:', action);
};

const handleEdit = () => {
  console.log('编辑');
};

const handleDelete = () => {
  console.log('删除');
};
</script>
```

## Mock数据示例

### 1. 完整Mock数据

```javascript
// mock/audit-buttons.js
export const mockAuditButtons = {
  // 请假审批
  leave: [
    {
      key: '部门同意',
      action: 'DEPT_APPROVE',
      props: { type: 'primary', icon: 'Check' }
    },
    {
      key: '人事确认',
      action: 'HR_CONFIRM',
      props: { type: 'success', icon: 'CircleCheck' }
    },
    {
      key: '需要补假条',
      action: 'NEED_LEAVE_NOTE',
      props: { type: 'warning', icon: 'Warning' }
    },
    {
      key: '拒绝请假',
      action: 'REJECT_LEAVE',
      props: { type: 'danger', icon: 'Close' }
    }
  ],
  
  // 报销审批
  expense: [
    {
      key: '财务审核',
      action: 'FINANCE_AUDIT',
      props: { type: 'primary', icon: 'Money' }
    },
    {
      key: '金额核实',
      action: 'AMOUNT_VERIFY',
      props: { type: 'info', icon: 'View' }
    },
    {
      key: '发票补充',
      action: 'INVOICE_SUPPLEMENT',
      props: { type: 'warning', icon: 'Document' }
    },
    {
      key: '拒绝报销',
      action: 'REJECT_EXPENSE',
      props: { type: 'danger', icon: 'Close' }
    }
  ],
  
  // 通用审批
  common: [
    {
      key: '同意',
      action: 'APPROVE',
      props: { type: 'primary' }
    },
    {
      key: '拒绝',
      action: 'REJECT',
      props: { type: 'danger' }
    },
    {
      key: '退回',
      action: 'RETURN',
      props: { type: 'warning' }
    }
  ]
};
```

### 2. 在组件中使用Mock数据

```vue
<script setup>
import { mockAuditButtons } from '@/mock/audit-buttons.js';

// 根据业务类型获取Mock数据
const getAuditButtons = (businessType) => {
  return mockAuditButtons[businessType] || mockAuditButtons.common;
};

const auditButtons = ref(getAuditButtons('leave'));
</script>
```
