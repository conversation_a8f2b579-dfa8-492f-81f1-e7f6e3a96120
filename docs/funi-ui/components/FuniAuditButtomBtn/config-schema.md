# FuniAuditButtomBtn 配置规范

## 组件配置概述

FuniAuditButtomBtn是工作流审核按钮组件，通过配置化的方式支持动态审核按钮渲染。组件配置主要包括按钮配置数组和按钮属性配置。

## 核心配置结构

### 1. 组件Props配置

```typescript
interface FuniAuditButtomBtnProps {
  auditButtons?: AuditButtonConfig[];  // 审核按钮配置数组
}
```

### 2. 审核按钮配置结构

```typescript
interface AuditButtonConfig {
  key: string;                         // 按钮显示文本
  action: string;                      // 审核动作标识
  props?: ElButtonProps;               // ElementPlus按钮属性
}

interface ElButtonProps {
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text';
  size?: 'large' | 'default' | 'small';
  disabled?: boolean;
  loading?: boolean;
  plain?: boolean;
  round?: boolean;
  circle?: boolean;
  icon?: string;
  autofocus?: boolean;
  nativeType?: 'button' | 'submit' | 'reset';
  autoInsertSpace?: boolean;
  color?: string;
  dark?: boolean;
  link?: boolean;
  bg?: boolean;
  text?: boolean;
}
```

## 配置示例

### 1. 基础配置

```javascript
const basicConfig = [
  {
    key: '同意',
    action: 'APPROVE'
  },
  {
    key: '拒绝',
    action: 'REJECT'
  }
];
```

### 2. 完整配置

```javascript
const fullConfig = [
  {
    key: '同意',
    action: 'APPROVE',
    props: {
      type: 'primary',
      size: 'default',
      icon: 'Check'
    }
  },
  {
    key: '拒绝',
    action: 'REJECT',
    props: {
      type: 'danger',
      size: 'default',
      icon: 'Close'
    }
  },
  {
    key: '退回修改',
    action: 'RETURN_MODIFY',
    props: {
      type: 'warning',
      size: 'default',
      icon: 'RefreshLeft'
    }
  },
  {
    key: '转办',
    action: 'TRANSFER',
    props: {
      type: 'info',
      size: 'default',
      icon: 'Share'
    }
  }
];
```

### 3. 工作流系统动态配置

```javascript
// 工作流系统返回的动态配置示例
const workflowConfig = [
  {
    key: '部门审批',
    action: 'DEPT_APPROVE',
    props: {
      type: 'primary',
      disabled: false
    }
  },
  {
    key: '人事审批',
    action: 'HR_APPROVE',
    props: {
      type: 'success',
      disabled: true  // 根据流程状态动态控制
    }
  },
  {
    key: '总经理审批',
    action: 'GM_APPROVE',
    props: {
      type: 'primary',
      disabled: true
    }
  }
];
```

## 默认配置

### 1. 系统默认按钮配置

```javascript
const defaultAuditButtons = [
  {
    key: '回退',
    action: 'BACK'
  },
  {
    key: '退件',
    action: 'RETURN'
  },
  {
    key: '不予受理',
    action: 'NOT_ACCEPT'
  },
  {
    key: '同意',
    action: 'COMMIT'
  }
];
```

### 2. 推荐配置模板

```javascript
// 通用审批流程配置
const commonApprovalConfig = [
  {
    key: '同意',
    action: 'APPROVE',
    props: { type: 'primary' }
  },
  {
    key: '拒绝',
    action: 'REJECT',
    props: { type: 'danger' }
  },
  {
    key: '退回',
    action: 'RETURN',
    props: { type: 'warning' }
  }
];

// 复杂审批流程配置
const complexApprovalConfig = [
  {
    key: '同意',
    action: 'APPROVE',
    props: { type: 'primary', icon: 'Check' }
  },
  {
    key: '拒绝',
    action: 'REJECT',
    props: { type: 'danger', icon: 'Close' }
  },
  {
    key: '退回修改',
    action: 'RETURN_MODIFY',
    props: { type: 'warning', icon: 'RefreshLeft' }
  },
  {
    key: '转办',
    action: 'TRANSFER',
    props: { type: 'info', icon: 'Share' }
  },
  {
    key: '挂起',
    action: 'SUSPEND',
    props: { type: 'info', icon: 'VideoPause' }
  }
];
```

## 配置验证规则

### 1. 必填字段验证

```typescript
// 按钮配置验证规则
const buttonConfigRules = {
  key: {
    required: true,
    type: 'string',
    message: '按钮显示文本不能为空'
  },
  action: {
    required: true,
    type: 'string',
    pattern: /^[A-Z_]+$/,
    message: '审核动作必须为大写字母和下划线组合'
  },
  props: {
    required: false,
    type: 'object',
    message: '按钮属性必须为对象类型'
  }
};
```

### 2. 配置完整性检查

```javascript
function validateAuditButtonConfig(config) {
  const errors = [];
  
  if (!Array.isArray(config)) {
    errors.push('auditButtons必须为数组类型');
    return errors;
  }
  
  config.forEach((button, index) => {
    if (!button.key) {
      errors.push(`第${index + 1}个按钮缺少key字段`);
    }
    
    if (!button.action) {
      errors.push(`第${index + 1}个按钮缺少action字段`);
    }
    
    if (button.props && typeof button.props !== 'object') {
      errors.push(`第${index + 1}个按钮的props字段必须为对象类型`);
    }
  });
  
  return errors;
}
```

## 常用审核动作配置

### 1. 标准审核动作

| 动作标识 | 中文名称 | 推荐配置 | 使用场景 |
|----------|----------|----------|----------|
| APPROVE | 同意 | `{ type: 'primary' }` | 审核通过 |
| REJECT | 拒绝 | `{ type: 'danger' }` | 审核拒绝 |
| RETURN | 退回 | `{ type: 'warning' }` | 退回上一节点 |
| RETURN_MODIFY | 退回修改 | `{ type: 'warning' }` | 退回申请人修改 |
| TRANSFER | 转办 | `{ type: 'info' }` | 转给其他人处理 |
| BACK | 回退 | `{ type: 'default' }` | 回退到指定节点 |
| NOT_ACCEPT | 不予受理 | `{ type: 'danger' }` | 不予受理申请 |
| SUSPEND | 挂起 | `{ type: 'info' }` | 暂停流程 |

### 2. 业务特定动作

```javascript
// 请假审批特定动作
const leaveApprovalActions = [
  {
    key: '部门同意',
    action: 'DEPT_APPROVE',
    props: { type: 'primary' }
  },
  {
    key: '人事确认',
    action: 'HR_CONFIRM',
    props: { type: 'success' }
  },
  {
    key: '需要补充材料',
    action: 'NEED_SUPPLEMENT',
    props: { type: 'warning' }
  }
];

// 报销审批特定动作
const expenseApprovalActions = [
  {
    key: '财务审核',
    action: 'FINANCE_AUDIT',
    props: { type: 'primary' }
  },
  {
    key: '金额有误',
    action: 'AMOUNT_ERROR',
    props: { type: 'danger' }
  },
  {
    key: '发票不符',
    action: 'INVOICE_INVALID',
    props: { type: 'warning' }
  }
];
```

## 配置最佳实践

### 1. 按钮样式规范

```javascript
// 推荐的按钮样式配置
const styleGuide = {
  // 积极操作：绿色/蓝色
  positive: { type: 'primary' },    // 同意、通过、确认
  success: { type: 'success' },     // 完成、成功
  
  // 消极操作：红色
  negative: { type: 'danger' },     // 拒绝、删除、不予受理
  
  // 中性操作：橙色/灰色
  neutral: { type: 'warning' },     // 退回、修改、暂停
  info: { type: 'info' },           // 转办、查看、其他
  default: { type: 'default' }      // 取消、关闭
};
```

### 2. 配置组织建议

```javascript
// 按业务模块组织配置
const configByModule = {
  // 通用审批配置
  common: [
    { key: '同意', action: 'APPROVE', props: { type: 'primary' } },
    { key: '拒绝', action: 'REJECT', props: { type: 'danger' } }
  ],
  
  // 请假模块配置
  leave: [
    { key: '部门审批', action: 'DEPT_APPROVE', props: { type: 'primary' } },
    { key: '人事审批', action: 'HR_APPROVE', props: { type: 'success' } },
    { key: '需要补假条', action: 'NEED_LEAVE_NOTE', props: { type: 'warning' } }
  ],
  
  // 报销模块配置
  expense: [
    { key: '财务审核', action: 'FINANCE_AUDIT', props: { type: 'primary' } },
    { key: '金额核实', action: 'AMOUNT_VERIFY', props: { type: 'info' } },
    { key: '发票补充', action: 'INVOICE_SUPPLEMENT', props: { type: 'warning' } }
  ]
};
```

## 配置扩展

### 1. 自定义按钮属性

```javascript
// 支持所有ElementPlus按钮属性
const extendedConfig = [
  {
    key: '快速审批',
    action: 'QUICK_APPROVE',
    props: {
      type: 'primary',
      size: 'large',
      round: true,
      icon: 'Lightning',
      loading: false,
      disabled: false
    }
  }
];
```

### 2. 条件配置

```javascript
// 根据条件动态配置按钮
function getConditionalConfig(userRole, nodeType) {
  const baseConfig = [
    { key: '同意', action: 'APPROVE', props: { type: 'primary' } }
  ];
  
  if (userRole === 'manager') {
    baseConfig.push({
      key: '直接通过',
      action: 'DIRECT_APPROVE',
      props: { type: 'success' }
    });
  }
  
  if (nodeType === 'final') {
    baseConfig.push({
      key: '最终确认',
      action: 'FINAL_CONFIRM',
      props: { type: 'primary', size: 'large' }
    });
  }
  
  return baseConfig;
}
```
