# FuniAuditButtomBtn ElementPlus API支持

## API透传机制

FuniAuditButtomBtn通过`v-bind="item.props || {}"`机制，将ElementPlus Button组件的所有API属性透传给内部的`el-button`组件，实现完整的ElementPlus Button API支持。

## 支持的ElementPlus Button属性

### 1. 基础属性

| 属性名 | 类型 | 默认值 | 说明 | 示例 |
|--------|------|--------|------|------|
| type | string | 'primary' | 按钮类型 | `primary` `success` `warning` `danger` `info` `text` |
| size | string | 'default' | 按钮尺寸 | `large` `default` `small` |
| disabled | boolean | false | 是否禁用 | `true` `false` |
| loading | boolean | false | 是否加载中 | `true` `false` |
| plain | boolean | false | 是否朴素按钮 | `true` `false` |
| round | boolean | false | 是否圆角按钮 | `true` `false` |
| circle | boolean | false | 是否圆形按钮 | `true` `false` |

### 2. 图标属性

| 属性名 | 类型 | 默认值 | 说明 | 示例 |
|--------|------|--------|------|------|
| icon | string/Component | - | 图标组件或图标名 | `'Edit'` `'Delete'` `'Check'` |

### 3. 原生属性

| 属性名 | 类型 | 默认值 | 说明 | 示例 |
|--------|------|--------|------|------|
| autofocus | boolean | false | 是否自动聚焦 | `true` `false` |
| nativeType | string | 'button' | 原生type属性 | `button` `submit` `reset` |

### 4. 样式属性

| 属性名 | 类型 | 默认值 | 说明 | 示例 |
|--------|------|--------|------|------|
| color | string | - | 自定义颜色 | `#409eff` `rgb(64, 158, 255)` |
| dark | boolean | false | 是否为暗色模式 | `true` `false` |
| link | boolean | false | 是否为链接按钮 | `true` `false` |
| bg | boolean | false | 是否显示背景色 | `true` `false` |
| text | boolean | false | 是否为文字按钮 | `true` `false` |

## 使用示例

### 1. 基础样式配置

```javascript
const basicStyleButtons = [
  {
    key: '同意',
    action: 'APPROVE',
    props: {
      type: 'primary',
      size: 'default'
    }
  },
  {
    key: '拒绝',
    action: 'REJECT',
    props: {
      type: 'danger',
      size: 'default'
    }
  },
  {
    key: '退回',
    action: 'RETURN',
    props: {
      type: 'warning',
      size: 'default',
      plain: true
    }
  }
];
```

### 2. 图标按钮配置

```javascript
const iconButtons = [
  {
    key: '同意',
    action: 'APPROVE',
    props: {
      type: 'primary',
      icon: 'Check'
    }
  },
  {
    key: '拒绝',
    action: 'REJECT',
    props: {
      type: 'danger',
      icon: 'Close'
    }
  },
  {
    key: '编辑',
    action: 'EDIT',
    props: {
      type: 'info',
      icon: 'Edit',
      circle: true
    }
  }
];
```

### 3. 加载状态配置

```javascript
const loadingButtons = [
  {
    key: '提交中',
    action: 'SUBMITTING',
    props: {
      type: 'primary',
      loading: true,
      disabled: true
    }
  },
  {
    key: '处理中',
    action: 'PROCESSING',
    props: {
      type: 'success',
      loading: true
    }
  }
];
```

### 4. 禁用状态配置

```javascript
const disabledButtons = [
  {
    key: '已审核',
    action: 'AUDITED',
    props: {
      type: 'success',
      disabled: true
    }
  },
  {
    key: '权限不足',
    action: 'NO_PERMISSION',
    props: {
      type: 'info',
      disabled: true,
      plain: true
    }
  }
];
```

### 5. 特殊样式配置

```javascript
const specialStyleButtons = [
  {
    key: '快速通过',
    action: 'QUICK_APPROVE',
    props: {
      type: 'primary',
      round: true,
      size: 'large'
    }
  },
  {
    key: '紧急处理',
    action: 'URGENT_HANDLE',
    props: {
      type: 'danger',
      color: '#ff4757',
      dark: true
    }
  },
  {
    key: '查看详情',
    action: 'VIEW_DETAIL',
    props: {
      type: 'primary',
      link: true,
      text: true
    }
  }
];
```

## 动态属性控制

### 1. 根据状态动态设置属性

```vue
<template>
  <FuniAuditButtomBtn
    :auditButtons="dynamicButtons"
    @audit-click="handleAuditClick"
  />
</template>

<script setup>
import { ref, computed } from 'vue';

const isLoading = ref(false);
const hasPermission = ref(true);
const auditStatus = ref('pending'); // pending, approved, rejected

const dynamicButtons = computed(() => [
  {
    key: '同意',
    action: 'APPROVE',
    props: {
      type: 'primary',
      loading: isLoading.value && auditStatus.value === 'approving',
      disabled: !hasPermission.value || auditStatus.value !== 'pending'
    }
  },
  {
    key: '拒绝',
    action: 'REJECT',
    props: {
      type: 'danger',
      loading: isLoading.value && auditStatus.value === 'rejecting',
      disabled: !hasPermission.value || auditStatus.value !== 'pending'
    }
  }
]);

const handleAuditClick = async (action) => {
  isLoading.value = true;
  auditStatus.value = action === 'APPROVE' ? 'approving' : 'rejecting';
  
  try {
    await performAudit(action);
    auditStatus.value = action === 'APPROVE' ? 'approved' : 'rejected';
  } catch (error) {
    auditStatus.value = 'pending';
  } finally {
    isLoading.value = false;
  }
};
</script>
```

### 2. 响应式尺寸控制

```vue
<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

const screenWidth = ref(window.innerWidth);

const responsiveButtons = computed(() => {
  const size = screenWidth.value < 768 ? 'small' : 
               screenWidth.value < 1200 ? 'default' : 'large';
  
  return [
    {
      key: '同意',
      action: 'APPROVE',
      props: {
        type: 'primary',
        size: size,
        // 小屏幕时只显示图标
        icon: screenWidth.value < 768 ? 'Check' : undefined
      }
    },
    {
      key: screenWidth.value < 768 ? '' : '拒绝',
      action: 'REJECT',
      props: {
        type: 'danger',
        size: size,
        icon: screenWidth.value < 768 ? 'Close' : undefined,
        circle: screenWidth.value < 768
      }
    }
  ];
});

const handleResize = () => {
  screenWidth.value = window.innerWidth;
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
});
</script>
```

## 样式定制

### 1. 自定义颜色主题

```javascript
const customColorButtons = [
  {
    key: '企业审批',
    action: 'COMPANY_APPROVE',
    props: {
      color: '#1890ff',
      dark: false
    }
  },
  {
    key: '财务审核',
    action: 'FINANCE_AUDIT',
    props: {
      color: '#52c41a',
      dark: true
    }
  },
  {
    key: '风险警告',
    action: 'RISK_WARNING',
    props: {
      color: '#ff4d4f',
      plain: true
    }
  }
];
```

### 2. 组合样式效果

```javascript
const combinedStyleButtons = [
  {
    key: '立即通过',
    action: 'IMMEDIATE_APPROVE',
    props: {
      type: 'primary',
      size: 'large',
      round: true,
      icon: 'Lightning'
    }
  },
  {
    key: '需要讨论',
    action: 'NEED_DISCUSSION',
    props: {
      type: 'warning',
      plain: true,
      round: true,
      icon: 'ChatDotRound'
    }
  },
  {
    key: '暂停处理',
    action: 'PAUSE_PROCESS',
    props: {
      type: 'info',
      text: true,
      bg: true,
      icon: 'VideoPause'
    }
  }
];
```

## 兼容性说明

### 1. ElementPlus版本兼容

- **支持版本**: ElementPlus 2.0+
- **推荐版本**: ElementPlus 2.3+
- **注意事项**: 某些新特性可能需要更高版本支持

### 2. 属性兼容性

| 属性 | ElementPlus 2.0 | ElementPlus 2.1+ | ElementPlus 2.3+ |
|------|-----------------|------------------|------------------|
| type | ✅ | ✅ | ✅ |
| size | ✅ | ✅ | ✅ |
| icon | ✅ | ✅ | ✅ |
| loading | ✅ | ✅ | ✅ |
| disabled | ✅ | ✅ | ✅ |
| color | ❌ | ✅ | ✅ |
| link | ❌ | ✅ | ✅ |
| text | ❌ | ❌ | ✅ |
| bg | ❌ | ❌ | ✅ |

### 3. 图标兼容性

```javascript
// ElementPlus 2.0 - 字符串图标
const iconButtonsV2_0 = [
  {
    key: '同意',
    action: 'APPROVE',
    props: {
      icon: 'el-icon-check'
    }
  }
];

// ElementPlus 2.1+ - 组件图标
const iconButtonsV2_1 = [
  {
    key: '同意',
    action: 'APPROVE',
    props: {
      icon: 'Check'  // 直接使用图标名
    }
  }
];
```

## 最佳实践

### 1. 属性优先级

```javascript
// 推荐：明确指定所有需要的属性
const recommendedConfig = [
  {
    key: '同意',
    action: 'APPROVE',
    props: {
      type: 'primary',
      size: 'default',
      icon: 'Check',
      loading: false,
      disabled: false
    }
  }
];

// 避免：依赖默认值可能导致不一致
const notRecommendedConfig = [
  {
    key: '同意',
    action: 'APPROVE',
    props: {
      type: 'primary'
      // 其他属性依赖默认值
    }
  }
];
```

### 2. 性能优化

```javascript
// 推荐：使用computed计算动态属性
const optimizedButtons = computed(() => [
  {
    key: '同意',
    action: 'APPROVE',
    props: {
      type: 'primary',
      loading: isApproving.value,
      disabled: !canApprove.value
    }
  }
]);

// 避免：在模板中直接计算
// <FuniAuditButtomBtn :auditButtons="[{props: {loading: isLoading}}]" />
```
