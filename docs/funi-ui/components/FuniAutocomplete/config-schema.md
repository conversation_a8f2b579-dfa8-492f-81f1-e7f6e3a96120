# FuniAutocomplete 配置结构定义

## 组件配置接口

### IFuniAutocompleteProps

```typescript
interface IFuniAutocompleteProps {
  /** 绑定值 */
  modelValue?: string
  /** 输入框占位文本 */
  placeholder?: string
  /** 远程搜索API地址 */
  url?: string
  /** 请求参数 */
  requestParams?: Record<string, any>
  /** 搜索关键词参数名 */
  keywordKey?: string
  /** 选项值字段名 */
  valueKey?: string
  /** 选项标签字段名 */
  labelKey?: string
  /** 主键字段名，优先级高于valueKey */
  primaryKey?: string
}
```

## 数据结构接口

### IAutocompleteOption

```typescript
interface IAutocompleteOption {
  /** 显示文本 */
  key: string
  /** 选项值 */
  value: any
  /** 原始数据 */
  [key: string]: any
}
```

### IAutocompleteRequest

```typescript
interface IAutocompleteRequest {
  /** 页码 */
  pageNo: number
  /** 每页大小 */
  pageSize: number
  /** 搜索关键词 */
  [keywordKey: string]: string
  /** 额外请求参数 */
  [key: string]: any
}
```

### IAutocompleteResponse

```typescript
interface IAutocompleteResponse {
  /** 数据列表 */
  list: any[]
  /** 总数量 */
  total: number
  /** 当前页码 */
  pageNo?: number
  /** 每页大小 */
  pageSize?: number
}
```

## 事件接口

### IAutocompleteEvents

```typescript
interface IAutocompleteEvents {
  /** 值更新事件 */
  'update:modelValue': (value: string) => void
  /** 选择事件 */
  'select': (item: IAutocompleteOption) => void
}
```

## 内部状态接口

### IAutocompleteState

```typescript
interface IAutocompleteState {
  /** 当前输入值 */
  state: string
  /** 选中的项目 */
  selectedItem: IAutocompleteOption
  /** 当前页码 */
  pageNo: number
  /** 建议列表 */
  suggestions: IAutocompleteOption[]
  /** 是否可以加载更多 */
  loadMore: boolean
  /** tooltip禁用状态映射 */
  tooltipDisabledMap: Record<string, boolean>
}
```

## 配置示例

### 基础配置

```typescript
const basicConfig: IFuniAutocompleteProps = {
  modelValue: '',
  placeholder: '请输入用户名',
  url: '/api/users/search',
  keywordKey: 'name',
  valueKey: 'id',
  labelKey: 'displayName'
}
```

### 高级配置

```typescript
const advancedConfig: IFuniAutocompleteProps = {
  modelValue: '',
  placeholder: '请输入产品名称搜索',
  url: '/api/products/search',
  requestParams: {
    category: 'electronics',
    status: 'active',
    includeDeleted: false
  },
  keywordKey: 'productName',
  primaryKey: 'productId',
  labelKey: 'productTitle',
  valueKey: 'productId'
}
```

### 用户搜索配置

```typescript
const userSearchConfig: IFuniAutocompleteProps = {
  modelValue: '',
  placeholder: '请输入用户名或邮箱',
  url: '/api/users/autocomplete',
  requestParams: {
    role: 'employee',
    department: 'IT',
    status: 'active'
  },
  keywordKey: 'keyword',
  primaryKey: 'userId',
  labelKey: 'displayName',
  valueKey: 'userId'
}
```

### 部门搜索配置

```typescript
const departmentSearchConfig: IFuniAutocompleteProps = {
  modelValue: '',
  placeholder: '请选择部门',
  url: '/api/departments/search',
  requestParams: {
    level: 2,
    includeSubDepartments: true
  },
  keywordKey: 'name',
  primaryKey: 'deptId',
  labelKey: 'deptName',
  valueKey: 'deptId'
}
```

## API请求配置

### 请求参数结构

```typescript
interface ISearchRequestParams {
  /** 页码，从1开始 */
  pageNo: number
  /** 每页大小，默认10 */
  pageSize: number
  /** 搜索关键词字段，字段名由keywordKey配置 */
  [keywordKey: string]: string
  /** 额外的过滤参数 */
  [key: string]: any
}

// 示例请求参数
const requestExample: ISearchRequestParams = {
  pageNo: 1,
  pageSize: 10,
  keyword: '张三',
  status: 'active',
  department: 'IT'
}
```

### 响应数据结构

```typescript
interface ISearchResponse {
  /** 数据列表 */
  list: Array<{
    /** 根据配置的字段名返回数据 */
    [labelKey: string]: string
    [valueKey: string]: any
    [primaryKey: string]: any
    /** 其他字段 */
    [key: string]: any
  }>
  /** 总记录数 */
  total: number
  /** 当前页码 */
  pageNo?: number
  /** 每页大小 */
  pageSize?: number
}

// 示例响应数据
const responseExample: ISearchResponse = {
  list: [
    {
      id: 1,
      name: '张三',
      email: '<EMAIL>',
      department: 'IT部门'
    },
    {
      id: 2,
      name: '李四',
      email: '<EMAIL>',
      department: '财务部门'
    }
  ],
  total: 25,
  pageNo: 1,
  pageSize: 10
}
```

## 数据映射配置

### 字段映射规则

```typescript
interface IFieldMapping {
  /** 显示字段映射 */
  labelKey: string
  /** 值字段映射 */
  valueKey: string
  /** 主键字段映射（可选，优先级高于valueKey） */
  primaryKey?: string
}

// 映射示例
const fieldMappingExamples = {
  // 用户数据映射
  user: {
    labelKey: 'name',        // 显示用户名
    valueKey: 'id',          // 值为用户ID
    primaryKey: 'userId'     // 主键为userId
  },
  
  // 产品数据映射
  product: {
    labelKey: 'title',       // 显示产品标题
    valueKey: 'productId',   // 值为产品ID
    primaryKey: 'id'         // 主键为id
  },
  
  // 部门数据映射
  department: {
    labelKey: 'deptName',    // 显示部门名称
    valueKey: 'deptId',      // 值为部门ID
  }
}
```

### 数据转换流程

```typescript
// 原始API数据
const rawData = {
  id: 1,
  name: '张三',
  email: '<EMAIL>',
  userId: 'user001'
}

// 配置
const config = {
  labelKey: 'name',
  valueKey: 'id',
  primaryKey: 'userId'
}

// 转换后的数据
const transformedData: IAutocompleteOption = {
  key: '张三',              // 来自 rawData[labelKey]
  value: 'user001',         // 来自 rawData[primaryKey] || rawData[valueKey]
  id: 1,                    // 保留原始字段
  name: '张三',             // 保留原始字段
  email: '<EMAIL>', // 保留原始字段
  userId: 'user001'         // 保留原始字段
}
```

## 分页配置

### 分页参数

```typescript
interface IPaginationConfig {
  /** 初始页码 */
  initialPageNo: number
  /** 每页大小 */
  pageSize: number
  /** 滚动加载阈值 */
  scrollThreshold: number
  /** 是否启用无限滚动 */
  infiniteScroll: boolean
}

const defaultPaginationConfig: IPaginationConfig = {
  initialPageNo: 1,
  pageSize: 10,
  scrollThreshold: 60,
  infiniteScroll: true
}
```

### 滚动加载配置

```typescript
interface IScrollLoadConfig {
  /** 滚动容器选择器 */
  containerSelector: string
  /** 触发加载的滚动位置阈值 */
  threshold: number
  /** 防抖延迟 */
  debounceDelay: number
}

const scrollLoadConfig: IScrollLoadConfig = {
  containerSelector: '.el-autocomplete-suggestion__wrap',
  threshold: 60,
  debounceDelay: 100
}
```

## 样式配置

### Tooltip配置

```typescript
interface ITooltipConfig {
  /** tooltip位置 */
  placement: 'top' | 'bottom' | 'left' | 'right'
  /** 最大宽度 */
  maxWidth: string
  /** 是否启用溢出检测 */
  enableOverflowDetection: boolean
}

const tooltipConfig: ITooltipConfig = {
  placement: 'left',
  maxWidth: '240px',
  enableOverflowDetection: true
}
```

### 下拉面板配置

```typescript
interface IPopperConfig {
  /** 自定义CSS类名 */
  popperClass: string
  /** 面板宽度是否适应输入框 */
  fitInputWidth: boolean
  /** 面板最大高度 */
  maxHeight: string
}

const popperConfig: IPopperConfig = {
  popperClass: 'custom-autocomplete-popper',
  fitInputWidth: true,
  maxHeight: '200px'
}
```

## 默认配置

```typescript
const defaultConfig: Required<IFuniAutocompleteProps> = {
  modelValue: '',
  placeholder: '请输入',
  url: '',
  requestParams: {},
  keywordKey: 'keyword',
  valueKey: 'value',
  labelKey: 'key',
  primaryKey: ''
}

const defaultInternalConfig = {
  pageSize: 10,
  debounceDelay: 400,
  scrollThreshold: 60,
  tooltipPlacement: 'left',
  tooltipMaxWidth: '240px'
}
```

## 配置验证

### 必需配置检查

```typescript
function validateConfig(config: IFuniAutocompleteProps): boolean {
  if (!config.url) {
    console.warn('FuniAutocomplete: url is required for remote search')
    return false
  }
  
  if (!config.labelKey) {
    console.warn('FuniAutocomplete: labelKey is required')
    return false
  }
  
  if (!config.valueKey && !config.primaryKey) {
    console.warn('FuniAutocomplete: valueKey or primaryKey is required')
    return false
  }
  
  return true
}
```

### 字段映射验证

```typescript
function validateFieldMapping(data: any[], config: IFuniAutocompleteProps): boolean {
  if (!Array.isArray(data) || data.length === 0) {
    return true
  }
  
  const sample = data[0]
  
  if (!(config.labelKey in sample)) {
    console.warn(`FuniAutocomplete: labelKey '${config.labelKey}' not found in data`)
    return false
  }
  
  const valueField = config.primaryKey || config.valueKey
  if (!(valueField in sample)) {
    console.warn(`FuniAutocomplete: valueKey '${valueField}' not found in data`)
    return false
  }
  
  return true
}
```

## 性能配置

### 防抖配置

```typescript
interface IDebounceConfig {
  /** 输入防抖延迟 */
  inputDelay: number
  /** 搜索防抖延迟 */
  searchDelay: number
  /** 滚动防抖延迟 */
  scrollDelay: number
}

const performanceConfig: IDebounceConfig = {
  inputDelay: 400,
  searchDelay: 300,
  scrollDelay: 100
}
```

### 缓存配置

```typescript
interface ICacheConfig {
  /** 是否启用缓存 */
  enabled: boolean
  /** 缓存过期时间（毫秒） */
  expireTime: number
  /** 最大缓存条目数 */
  maxSize: number
}

const cacheConfig: ICacheConfig = {
  enabled: true,
  expireTime: 5 * 60 * 1000, // 5分钟
  maxSize: 100
}
```
