# FuniAutocomplete API文档

## 组件概述

FuniAutocomplete是基于ElementPlus el-autocomplete封装的自动完成输入框组件。它支持远程数据搜索、分页加载、防抖处理等功能，并提供了溢出文本的tooltip提示。

## Props

### 基础属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| modelValue | String | - | 绑定值 |
| placeholder | String | `'请输入'` | 输入框占位文本 |
| url | String | - | 远程搜索API地址 |
| requestParams | Object | `{}` | 请求参数 |
| keywordKey | String | `'keyword'` | 搜索关键词参数名 |
| valueKey | String | `'value'` | 选项值字段名 |
| labelKey | String | `'key'` | 选项标签字段名 |
| primaryKey | String | - | 主键字段名，优先级高于valueKey |

### 详细说明

#### modelValue
- **类型**: `String`
- **说明**: 组件的绑定值，支持v-model双向绑定
- **示例**:
```vue
<FuniAutocomplete v-model="searchValue" />
```

#### url
- **类型**: `String`
- **说明**: 远程搜索的API地址，支持分页和关键词搜索
- **示例**:
```vue
<FuniAutocomplete url="/api/users/search" />
```

#### requestParams
- **类型**: `Object`
- **默认值**: `{}`
- **说明**: 额外的请求参数，会合并到搜索请求中
- **示例**:
```vue
<FuniAutocomplete 
  url="/api/users/search"
  :requestParams="{ status: 'active', department: 'IT' }"
/>
```

#### keywordKey
- **类型**: `String`
- **默认值**: `'keyword'`
- **说明**: 搜索关键词在请求参数中的字段名
- **示例**:
```vue
<FuniAutocomplete 
  url="/api/users/search"
  keywordKey="searchText"
/>
```

#### valueKey / labelKey / primaryKey
- **类型**: `String`
- **说明**: 用于映射API返回数据的字段名
- **示例**:
```vue
<FuniAutocomplete 
  url="/api/users/search"
  valueKey="id"
  labelKey="name"
  primaryKey="userId"
/>
```

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | value | 绑定值更新事件 |
| select | item | 选择选项时触发 |

### 事件详细说明

#### update:modelValue
- **参数**: `(value: string)`
- **说明**: 当组件值发生变化时触发，用于v-model双向绑定
- **示例**:
```vue
<FuniAutocomplete 
  :modelValue="searchValue"
  @update:modelValue="searchValue = $event"
/>
```

#### select
- **参数**: `(item: object)`
- **说明**: 当用户选择某个选项时触发
- **示例**:
```vue
<FuniAutocomplete 
  @select="handleSelect"
/>

<script setup>
const handleSelect = (item) => {
  console.log('选择了:', item)
  // item包含完整的选项数据
}
</script>
```

## Slots

### 默认插槽

FuniAutocomplete使用内置的选项模板，支持溢出文本的tooltip提示。暂不支持自定义插槽。

## Methods

FuniAutocomplete通过ref暴露内部el-autocomplete的方法：

### 常用方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| focus | - | 使输入框获取焦点 |
| blur | - | 使输入框失去焦点 |
| clear | - | 清空输入框内容 |

### 使用示例

```vue
<template>
  <div>
    <FuniAutocomplete ref="autocompleteRef" v-model="value" />
    <el-button @click="focusInput">聚焦</el-button>
    <el-button @click="clearInput">清空</el-button>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const autocompleteRef = ref()
const value = ref('')

const focusInput = () => {
  autocompleteRef.value.focus()
}

const clearInput = () => {
  autocompleteRef.value.clear()
}
</script>
```

## 功能特性

### 1. 远程搜索

支持通过API进行远程数据搜索：

```vue
<template>
  <FuniAutocomplete
    v-model="selectedUser"
    url="/api/users/search"
    :requestParams="{ status: 'active' }"
    keywordKey="name"
    valueKey="id"
    labelKey="displayName"
    placeholder="请输入用户名搜索"
    @select="handleUserSelect"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedUser = ref('')

const handleUserSelect = (user) => {
  console.log('选择用户:', user)
}
</script>
```

### 2. 分页加载

组件内置分页加载功能，支持滚动加载更多数据：

- 每页默认加载10条数据
- 滚动到底部时自动加载下一页
- 支持加载状态管理

### 3. 防抖处理

内置400ms的防抖处理，避免频繁的API请求：

```javascript
// 组件内部实现
:debounce="400"
```

### 4. 溢出提示

当选项文本过长时，自动显示tooltip提示：

```vue
<template #default="{ item }">
  <el-tooltip placement="left" :disabled="tooltipDisabledMap[item.key]">
    <template #content>
      <div class="max-w-[240px]">{{ item.key }}</div>
    </template>
    <template #default>
      <span :ref="el => handleToolContentRef(el, item)">{{ item.key }}</span>
    </template>
  </el-tooltip>
</template>
```

### 5. 数据映射

支持灵活的数据字段映射：

```javascript
// API返回数据
const apiResponse = {
  list: [
    { userId: 1, userName: '张三', email: '<EMAIL>' },
    { userId: 2, userName: '李四', email: '<EMAIL>' }
  ]
}

// 组件配置
<FuniAutocomplete
  primaryKey="userId"    // 主键字段
  labelKey="userName"    // 显示字段
  valueKey="userId"      // 值字段
/>

// 内部处理后的数据格式
const processedData = [
  { key: '张三', value: 1, userId: 1, userName: '张三', email: '<EMAIL>' },
  { key: '李四', value: 2, userId: 2, userName: '李四', email: '<EMAIL>' }
]
```

## API请求格式

### 请求参数

```javascript
const requestParams = {
  pageNo: 1,              // 页码
  pageSize: 10,           // 每页大小
  [keywordKey]: 'search', // 搜索关键词
  ...requestParams        // 额外参数
}
```

### 响应格式

```javascript
const response = {
  list: [                 // 数据列表
    {
      id: 1,
      name: '张三',
      email: '<EMAIL>'
    }
  ],
  total: 100,            // 总数量
  pageNo: 1,             // 当前页码
  pageSize: 10           // 每页大小
}
```

## 使用示例

### 基础用法

```vue
<template>
  <FuniAutocomplete
    v-model="searchValue"
    url="/api/search"
    placeholder="请输入搜索内容"
    @select="handleSelect"
  />
</template>

<script setup>
import { ref } from 'vue'

const searchValue = ref('')

const handleSelect = (item) => {
  console.log('选择项:', item)
}
</script>
```

### 自定义字段映射

```vue
<template>
  <FuniAutocomplete
    v-model="selectedProduct"
    url="/api/products/search"
    :requestParams="{ category: 'electronics' }"
    keywordKey="productName"
    primaryKey="productId"
    labelKey="productTitle"
    placeholder="请输入产品名称"
    @select="handleProductSelect"
  />
</template>

<script setup>
import { ref } from 'vue'

const selectedProduct = ref('')

const handleProductSelect = (product) => {
  console.log('选择产品:', product)
  // product包含完整的产品信息
}
</script>
```

### 结合表单使用

```vue
<template>
  <el-form :model="form" label-width="120px">
    <el-form-item label="负责人">
      <FuniAutocomplete
        v-model="form.assigneeId"
        url="/api/users/search"
        :requestParams="{ role: 'manager' }"
        keywordKey="name"
        primaryKey="id"
        labelKey="displayName"
        placeholder="请选择负责人"
        @select="handleAssigneeSelect"
      />
    </el-form-item>
    
    <el-form-item label="部门">
      <FuniAutocomplete
        v-model="form.departmentId"
        url="/api/departments/search"
        keywordKey="name"
        primaryKey="id"
        labelKey="name"
        placeholder="请选择部门"
      />
    </el-form-item>
  </el-form>
</template>

<script setup>
import { reactive } from 'vue'

const form = reactive({
  assigneeId: '',
  assigneeName: '',
  departmentId: ''
})

const handleAssigneeSelect = (user) => {
  form.assigneeId = user.value
  form.assigneeName = user.key
}
</script>
```

## 注意事项

1. **API地址**: 必须提供有效的API地址才能进行远程搜索
2. **数据格式**: API返回数据必须包含list数组和total总数
3. **字段映射**: 确保labelKey和valueKey对应的字段在API返回数据中存在
4. **性能优化**: 组件内置防抖和分页，适合大数据量场景
5. **样式定制**: 可以通过CSS深度选择器定制下拉面板样式

## 与ElementPlus的关系

FuniAutocomplete基于ElementPlus的el-autocomplete组件，继承其基础功能并扩展了远程搜索、分页加载等特性。支持el-autocomplete的大部分属性和事件。
