# 前后端协同开发工作流程图

## 概述

本文档定义了基于AI智能体的前后端协同开发标准工作流程。该流程以**业务技术规格设计阶段**为核心，建立了前后端统一的业务基础，实现了API设计前置和全流程验证机制，确保前后端基于统一的业务技术规格文档进行方案设计和编码，保证开发过程的一致性和高质量交付。

## 设计理念

### 核心价值
- **统一理解基础**：建立前后端共同的技术语言和规格标准，确保对需求理解的一致性
- **协同开发模式**：通过标准化的业务技术规格文档，实现前后端高效并行开发
- **质量保证体系**：建立多层次的验证机制，确保从设计到实现的全流程质量控制
- **可持续发展**：形成可复用的开发模式，支持团队规模化和标准化发展

### 设计原则
本工作流程基于**业务技术规格设计阶段**构建统一的开发基础，通过AI智能体和人工审核相结合的方式，确保高质量的协同开发交付。

## 标准工作流程图

```mermaid
flowchart TD
    %% 起始节点
    START([项目启动])
    
    %% 产品阶段
    PRODUCT["产品阶段<br/>📋 基于提示词+前端HTML模板代码<br/>输出PRD、HTML原型页面"]
    
    %% 业务技术规格设计阶段
    subgraph TECH_SPEC_FLOW["📐 业务技术规格设计阶段"]
        TECH_SPEC["业务技术规格设计<br/>🤖 基于PRD、HTML原型<br/>输出业务技术规格、数据规格、技术约束"]
        API_SPEC["API接口设计<br/>🤖 基于业务技术规格<br/>输出完整API接口规范"]
        QC_SPEC["人工审核节点0<br/>🔍 业务技术规格和API规范审核"]
        SPEC_OUTPUT["业务技术规格文档<br/>📄 包含API规范的完整业务技术规格"]
    end
    
    %% 前端开发路线
    subgraph FRONTEND_FLOW["💻 前端开发路线"]
        FRONTEND_DESIGN["前端方案设计<br/>🤖 基于业务技术规格文档、FuniUI组件库<br/>输出菜单结构、页面类型、功能清单"]
        QC1["人工审核节点1<br/>🔍 前端方案审核"]
        
        FRONTEND_TASK["前端编码任务清单<br/>🤖 基于前端方案、代码模板<br/>输出按模块研发的任务集合"]
        QC2["人工审核节点2<br/>🔍 前端任务清单审核"]
        
        FRONTEND_CODE["前端编码<br/>🤖 基于任务清单完成代码编写"]
        QC3["人工审核节点3<br/>🔍 前端编码审核"]
        
        FRONTEND_COMPLETE["前端开发完成<br/>✅ 前端路线交付"]
    end
    
    %% 后端开发路线
    subgraph BACKEND_FLOW["⚙️ 后端开发路线"]
        BACKEND_DESIGN["后端方案设计<br/>🤖 基于业务技术规格文档<br/>输出API设计、数据库设计、架构设计"]
        QC4["人工审核节点4<br/>🔍 后端方案审核"]

        BACKEND_TASK["后端编码任务清单<br/>🤖 基于后端方案、代码模板<br/>输出按模块研发的任务集合"]
        QC5["人工审核节点5<br/>🔍 后端任务清单审核"]

        BACKEND_CODE["后端编码<br/>🤖 基于任务清单完成代码编写"]
        QC6["人工审核节点6<br/>🔍 后端编码审核"]

        API_GENERATE["API文档生成<br/>🤖 基于后端代码生成OpenAPI文档"]
        API_VALIDATE["API一致性验证<br/>🤖 验证实现与规格的一致性"]
        QC7["人工审核节点7<br/>🔍 API文档和一致性审核"]

        BACKEND_COMPLETE["后端开发完成<br/>✅ 后端路线交付"]
    end
    
    %% 集成测试路线
    subgraph INTEGRATION_FLOW["🔗 集成测试路线"]
        API_INTEGRATION["API集成<br/>🤖 前端基于OpenAPI文档对接后端"]
        QC8["人工审核节点8<br/>🔍 API集成审核"]
        
        INTEGRATION_TEST["集成测试<br/>🤖 基于Playwright MCP执行端到端测试"]
        QC9["人工审核节点9<br/>🔍 集成测试审核"]
        
        BUG_FIX["BUG修复<br/>🤖 基于测试报告修复前后端问题"]
        
        INTEGRATION_COMPLETE["集成完成<br/>✅ 集成路线交付"]
    end
    
    %% 输入文档
    FUNIUI_INPUT["FuniUI组件库文档<br/>📚 前端技术栈"]
    FRONTEND_TEMPLATE["前端代码模板<br/>📄 前端现有"]
    BACKEND_TEMPLATE["后端代码模板<br/>📄 后端现有"]
    
    %% 结束节点
    END([项目交付])
    
    %% 主流程连接
    START --> PRODUCT

    %% 业务技术规格设计阶段连接
    PRODUCT --> TECH_SPEC
    TECH_SPEC --> API_SPEC
    API_SPEC --> QC_SPEC
    QC_SPEC -->|通过| SPEC_OUTPUT
    QC_SPEC -->|不通过| TECH_SPEC
    SPEC_OUTPUT --> FRONTEND_DESIGN
    SPEC_OUTPUT --> BACKEND_DESIGN
    
    %% 前端路线连接
    FRONTEND_DESIGN --> QC1
    FUNIUI_INPUT --> FRONTEND_DESIGN
    QC1 -->|通过| FRONTEND_TASK
    QC1 -->|不通过| FRONTEND_DESIGN
    
    FRONTEND_TASK --> QC2
    FRONTEND_TEMPLATE --> FRONTEND_TASK
    QC2 -->|通过| FRONTEND_CODE
    QC2 -->|不通过| FRONTEND_TASK
    
    FRONTEND_CODE --> QC3
    QC3 -->|通过| FRONTEND_COMPLETE
    QC3 -->|不通过| FRONTEND_CODE
    
    %% 后端路线连接
    BACKEND_DESIGN --> QC4
    QC4 -->|通过| BACKEND_TASK
    QC4 -->|不通过| BACKEND_DESIGN
    
    BACKEND_TASK --> QC5
    BACKEND_TEMPLATE --> BACKEND_TASK
    QC5 -->|通过| BACKEND_CODE
    QC5 -->|不通过| BACKEND_TASK
    
    BACKEND_CODE --> QC6
    QC6 -->|通过| API_GENERATE
    QC6 -->|不通过| BACKEND_CODE

    API_GENERATE --> API_VALIDATE
    API_VALIDATE --> QC7
    QC7 -->|通过| BACKEND_COMPLETE
    QC7 -->|不通过| API_GENERATE
    
    %% 集成路线连接
    FRONTEND_COMPLETE --> API_INTEGRATION
    BACKEND_COMPLETE --> API_INTEGRATION
    API_INTEGRATION --> QC8
    QC8 -->|通过| INTEGRATION_TEST
    QC8 -->|不通过| API_INTEGRATION
    
    INTEGRATION_TEST --> QC9
    QC9 -->|通过| INTEGRATION_COMPLETE
    QC9 -->|不通过| BUG_FIX
    
    %% BUG修复反馈
    BUG_FIX --> FRONTEND_CODE
    BUG_FIX --> BACKEND_CODE
    BUG_FIX --> INTEGRATION_TEST
    
    %% 最终交付
    INTEGRATION_COMPLETE --> END
    
    %% 样式定义
    classDef productNode fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,color:#000
    classDef techSpecNode fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px,color:#000
    classDef frontendNode fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef backendNode fill:#fff3e0,stroke:#ef6c00,stroke-width:2px,color:#000
    classDef integrationNode fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000
    classDef qcNode fill:#fff3e0,stroke:#f57c00,stroke-width:3px,color:#000
    classDef inputNode fill:#e1f5fe,stroke:#0277bd,stroke-width:2px,color:#000
    classDef completeNode fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px,color:#000
    classDef bugfixNode fill:#ffebee,stroke:#d32f2f,stroke-width:2px,color:#000
    
    %% 应用样式
    class PRODUCT productNode
    class TECH_SPEC,API_SPEC,SPEC_OUTPUT techSpecNode
    class FUNIUI_INPUT,FRONTEND_TEMPLATE,BACKEND_TEMPLATE inputNode
    class FRONTEND_DESIGN,FRONTEND_TASK,FRONTEND_CODE frontendNode
    class BACKEND_DESIGN,BACKEND_TASK,BACKEND_CODE,API_GENERATE,API_VALIDATE backendNode
    class API_INTEGRATION,INTEGRATION_TEST integrationNode
    class QC_SPEC,QC1,QC2,QC3,QC4,QC5,QC6,QC7,QC8,QC9 qcNode
    class FRONTEND_COMPLETE,BACKEND_COMPLETE,INTEGRATION_COMPLETE completeNode
    class BUG_FIX bugfixNode
```

## 业务技术规格设计阶段

### 阶段概述

业务技术规格设计阶段是整个工作流程的核心环节，负责将产品需求转换为标准化的技术实现方案，为前后端协同开发提供统一的技术基础。

### 核心特点

**1. API设计前置**
- 业务技术规格设计阶段包含完整的API接口设计
- 确保API规范在开发启动前完全明确
- 为前后端并行开发提供清晰的接口约定

**2. 双重API保障机制**
- 设计阶段：基于业务需求设计完整的API接口规范
- 实现阶段：基于后端代码自动生成API文档
- 验证阶段：通过一致性验证确保设计与实现的完全对应

**3. 全流程质量验证**
- 业务技术规格和API规范的统一审核机制
- 多层次的一致性验证环节
- 关键节点的严格质量把关

### 业务技术规格文档内容

#### 1. 业务功能规格
- **功能模块划分**：基于PRD梳理的完整功能模块清单
- **业务流程定义**：详细的业务流程图和状态转换
- **用户角色权限**：用户角色定义和权限矩阵
- **业务规则约束**：业务逻辑规则和数据验证规则

#### 2. 数据规格定义
- **数据模型设计**：实体关系图和数据字典
- **数据流转图**：数据在前后端之间的流转路径
- **数据格式标准**：统一的数据格式、字段命名规范
- **数据验证规则**：前后端一致的数据验证标准

#### 3. 接口规格标准
- **API接口清单**：完整的API接口列表和分类
- **接口参数规范**：请求参数、响应参数的详细定义
- **错误码标准**：统一的错误码定义和处理规范
- **接口调用时序**：接口调用的时序图和依赖关系

#### 4. 前端交互规格
- **页面结构定义**：页面层级结构和导航关系
- **组件交互规范**：UI组件的交互行为和状态管理
- **前端路由规划**：路由结构和权限控制
- **状态管理方案**：前端状态管理的统一方案

#### 5. 技术约束规范
- **技术栈选择**：前后端技术栈的统一选择
- **编码规范**：代码风格、命名规范、注释标准
- **性能要求**：响应时间、并发量等性能指标
- **安全规范**：数据安全、接口安全的统一标准

### 业务技术规格设计智能体

#### 执行者
- **业务技术规格设计智能体**：负责将产品需求转换为业务技术规格
- **API接口设计智能体**：负责基于业务技术规格设计API接口规范

#### 输入
- **PRD文档**：产品需求文档
- **HTML原型页面**：产品原型设计
- **技术栈文档**：前后端技术栈规范
- **编码规范文档**：代码标准和规范

#### 输出
- **业务技术规格文档**：包含业务技术规格、数据规格、技术约束的完整文档
- **API接口规范**：完整的OpenAPI规范文档
- **数据库设计草案**：初步的数据库结构设计
- **前端组件清单**：需要开发的前端组件列表

#### 处理逻辑
1. **需求分析**：深度解析PRD和HTML原型，提取核心业务需求
2. **技术映射**：将业务需求映射为技术实现方案
3. **API设计**：基于业务流程设计完整的API接口规范
4. **规格标准化**：按照统一的业务技术规格模板输出标准化文档
5. **一致性检查**：确保前后端业务技术规格和API规范的一致性和兼容性

## 流程阶段详细说明

### 0. 业务技术规格设计阶段
- **执行者**：业务技术规格设计智能体 + API接口设计智能体
- **输入**：产品阶段输出（PRD + HTML原型）
- **输出**：包含API规范的完整业务技术规格文档
- **内部流程**：业务技术规格设计 → API接口设计 → 统一审核
- **人工审核节点0**：业务技术规格和API规范统一审核
- **核心价值**：为前后端提供统一的技术基础和API规范，确保理解一致性

### 1. 前端开发路线

#### 阶段1.1：前端方案设计
- **执行者**：前端方案设计智能体
- **输入**：业务技术规格文档 + FuniUI组件库文档
- **输出**：菜单结构、页面类型、功能清单
- **核心特点**：基于统一业务技术规格进行方案设计

#### 阶段1.2：前端编码任务清单
- **执行者**：前端任务清单生成智能体
- **输入**：前端方案设计输出 + 前端代码模板
- **输出**：按模块的前端任务集合

#### 阶段1.3：前端编码
- **执行者**：前端编码智能体
- **输入**：前端编码任务清单
- **输出**：完整的前端代码实现

### 2. 后端开发路线

#### 阶段2.1：后端方案设计
- **执行者**：后端方案设计智能体
- **输入**：业务技术规格文档
- **输出**：API设计、数据库设计、架构设计
- **关键点**：与前端基于相同的业务技术规格文档

#### 阶段2.2：后端编码任务清单
- **执行者**：后端任务清单生成智能体
- **输入**：后端方案设计输出 + 后端代码模板
- **输出**：按模块的后端任务集合

#### 阶段2.3：后端编码
- **执行者**：后端编码智能体
- **输入**：后端编码任务清单
- **输出**：完整的后端代码实现

#### 阶段2.4：API文档生成和一致性验证
- **执行者**：API文档生成智能体 + API一致性验证智能体
- **输入**：后端代码实现 + 业务技术规格中的API规范
- **输出**：标准的OpenAPI文档 + 一致性验证报告
- **核心功能**：验证实现的API与设计阶段的API规范完全一致

### 3. 集成测试路线

#### 阶段3.1：API集成
- **执行者**：API集成智能体
- **输入**：前端代码 + OpenAPI文档
- **输出**：前后端集成的完整应用

#### 阶段3.2：集成测试
- **执行者**：集成测试智能体
- **输入**：集成应用 + Playwright MCP
- **输出**：端到端测试报告

#### 阶段3.3：BUG修复
- **执行者**：BUG修复智能体
- **输入**：测试报告 + Sentry MCP + BrowserTools MCP
- **输出**：修复后的前后端代码

## 人工审核节点说明

### 人工审核节点0：业务技术规格和API规范审核
- **检查内容**：业务技术规格完整性、API规范准确性、前后端一致性、技术可行性
- **决策**：通过/不通过
- **不通过处理**：返回业务技术规格设计阶段
- **核心价值**：确保后续开发的技术基础和API规范正确
- **审核重点**：API接口设计的合理性和完整性

### 人工审核节点1：前端方案审核
- **检查内容**：方案与业务技术规格的一致性、前端技术选型合理性
- **决策**：通过/不通过
- **不通过处理**：返回前端方案设计阶段

### 人工审核节点2：前端任务清单审核
- **检查内容**：任务分解合理性、与业务技术规格的对应关系
- **决策**：通过/不通过
- **不通过处理**：返回前端编码任务清单阶段

### 人工审核节点3：前端编码审核
- **检查内容**：代码质量、功能完整性、规格符合度
- **决策**：通过/不通过
- **不通过处理**：返回前端编码阶段

### 人工审核节点4：后端方案审核
- **检查内容**：方案与业务技术规格的一致性、后端架构合理性
- **决策**：通过/不通过
- **不通过处理**：返回后端方案设计阶段

### 人工审核节点5：后端任务清单审核
- **检查内容**：任务分解合理性、与业务技术规格的对应关系
- **决策**：通过/不通过
- **不通过处理**：返回后端编码任务清单阶段

### 人工审核节点6：后端编码审核
- **检查内容**：代码质量、功能完整性、规格符合度
- **决策**：通过/不通过
- **不通过处理**：返回后端编码阶段

### 人工审核节点7：API文档和一致性审核
- **检查内容**：API文档完整性、与业务技术规格的一致性、API一致性验证结果
- **决策**：通过/不通过
- **不通过处理**：返回API文档生成阶段
- **审核重点**：验证实现的API与设计阶段API规范的一致性

### 人工审核节点8：API集成审核
- **检查内容**：前后端集成正确性、接口调用准确性
- **决策**：通过/不通过
- **不通过处理**：返回API集成阶段

### 人工审核节点9：集成测试审核
- **检查内容**：测试覆盖度、测试结果准确性、BUG识别完整性
- **决策**：通过/不通过
- **不通过处理**：进入BUG修复阶段

## 并行开发说明

### 前端开发路线特点
- **规格驱动**：基于统一业务技术规格进行前端方案设计
- **组件化开发**：基于FuniUI组件库的标准化前端开发
- **模板化实现**：基于前端代码模板的快速开发
- **独立验收**：每个阶段都有独立的人工审核节点

### 后端开发路线特点
- **规格驱动**：基于统一业务技术规格进行后端方案设计
- **API优先**：重视API设计和文档生成
- **模板化实现**：基于后端代码模板的快速开发
- **标准化输出**：生成标准的OpenAPI文档

### 集成测试路线特点
- **端到端测试**：基于Playwright MCP的自动化集成测试
- **智能修复**：集成多种MCP工具的智能BUG修复
- **反馈机制**：BUG修复可精准反馈到前后端开发阶段

### 流程同步点
- **业务技术规格阶段**：前后端都基于相同的业务技术规格文档开始
- **集成阶段**：前后端开发完成后进入统一的集成测试
- **BUG修复阶段**：可同时反馈到前后端开发阶段
- **交付阶段**：集成测试完成后项目交付

## 工作流程优势

### 1. 协同开发保障
- **统一理解基础**：业务技术规格文档确保前后端对需求的统一理解
- **API设计前置**：开发前完成完整的API接口设计，确保接口规范明确
- **双重API保障**：设计阶段API规范 + 实现阶段API文档 + 一致性验证
- **一致的数据模型**：统一的数据规格确保前后端数据结构完全对应
- **规范化开发**：统一的技术规范确保代码风格和质量标准

### 2. 开发效率提升
- **并行开发模式**：前后端基于统一业务技术规格同步启动开发
- **标准化流程**：统一的业务技术规格确保开发过程的高效协同
- **模板化开发**：基于代码模板实现标准化快速开发
- **自动化集成**：完整的自动化API集成和测试流程

### 3. 质量保证体系
- **全流程质量控制**：10个人工审核节点确保全流程质量把关
- **分层验收机制**：业务技术规格、方案设计、编码实现的分层验收
- **智能化测试**：基于AI和MCP工具的智能化测试和修复
- **标准化交付**：基于统一标准的高质量项目交付

### 4. 规模化发展支持
- **标准化流程**：可复制的标准化流程支持团队规模化发展
- **模板化资产**：业务技术规格模板、代码模板形成可复用的开发资产
- **工具集成体系**：MCP工具集成支持自动化规模化开发
- **知识资产沉淀**：业务技术规格文档形成项目知识资产的有效沉淀

### 5. 风险控制机制
- **前置风险控制**：业务技术规格阶段确保需求理解的统一性
- **分阶段风险管控**：每个阶段的人工审核节点实现风险控制
- **智能化问题处理**：基于AI的智能问题识别和修复机制
- **全链路可追溯**：从业务技术规格到最终交付的完整链路追溯

---

## 总结

**本标准工作流程构建了完整的前后端协同开发体系，具备以下核心特征：**

1. **业务技术规格驱动**：以业务技术规格设计阶段为核心，建立前后端统一的开发基础
2. **API设计前置**：在开发启动前完成完整的API接口设计，确保接口规范明确
3. **双重保障机制**：设计阶段API规范 + 实现阶段API文档 + 一致性验证的完整保障体系
4. **全流程质量控制**：通过多层次验证机制，确保从设计到交付的全流程质量
5. **标准化可复制**：形成标准化的开发模式，支持团队规模化和可持续发展

**本工作流程为团队提供了成熟、完善的前后端协同开发标准，确保高效协同和高质量交付。**

---

## 实施指南

### 团队角色分工

**产品团队**
- 负责PRD文档和HTML原型的输出
- 参与业务技术规格设计阶段的需求澄清
- 参与人工审核节点的产品验收

**技术团队**
- 负责业务技术规格设计阶段的执行
- 负责前后端开发路线的实施
- 负责集成测试路线的执行

**质量团队**
- 负责各人工审核节点的质量把关
- 负责测试方案的设计和执行
- 负责最终交付质量的验收

### 关键成功要素

**1. 业务技术规格文档质量**
- 确保业务技术规格文档的完整性和准确性
- 重视API接口设计的前置和标准化
- 建立业务技术规格文档的版本管理机制

**2. 人工审核节点执行**
- 严格执行每个人工审核节点的质量标准
- 建立明确的审核标准和检查清单
- 确保审核结果的及时反馈和处理

**3. 团队协作机制**
- 建立前后端团队的定期沟通机制
- 确保业务技术规格变更的及时同步
- 建立问题反馈和解决的快速响应机制

### 培训要点

**新团队成员培训**
- 理解业务技术规格设计阶段的核心价值
- 掌握各开发路线的执行标准
- 熟悉人工审核节点的质量要求

**持续改进培训**
- 定期回顾工作流程的执行效果
- 收集团队反馈并持续优化流程
- 分享最佳实践和经验总结