<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公告管理 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f8fafc;
            margin: 0;
            padding: 0;
        }

        .main-container {
            min-height: 100vh;
            background-color: #f8fafc;
        }

        .content-wrapper {
            max-width: 1400px;
            margin: 0 auto;
            padding: 24px;
        }

        /* 页面内容 */
        .page-content {
            background: white;
            border-radius: 0 12px 12px 12px;
            border: 1px solid #f0f0f0;
            padding: 24px;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #374151;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .help-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #e5e7eb;
            color: #6b7280;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .help-icon:hover {
            background-color: #1890ff;
            color: white;
        }



        /* 主页签样式 */
        .main-tabs {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            margin-bottom: 16px;
        }
        
        .main-tabs .tab-item {
            padding: 12px 24px;
            cursor: pointer;
            font-size: 14px;
            position: relative;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .main-tabs .tab-item.active {
            color: #1890ff;
            font-weight: 500;
        }
        
        .main-tabs .tab-item.active:after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #1890ff;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 查询区域 */
        .search-area {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            margin-bottom: 24px;
        }

        .search-form {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }

        .form-label {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-control {
            padding: 12px 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s;
            background-color: white;
            line-height: 1.5;
        }

        .form-control:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
            background-color: #fafbfc;
        }

        .btn-group {
            display: flex;
            gap: 12px;
            align-items: end;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .btn-primary {
            background: #1890ff;
            color: white;
            border: 1px solid #1890ff;
        }

        .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }

        .btn-secondary {
            background: #1890ff;
            color: white;
            border: 1px solid #1890ff;
        }

        .btn-secondary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }

        .btn-outline {
            background: white;
            color: #1890ff;
            border: 1px solid #1890ff;
        }

        .btn-outline:hover {
            background: #1890ff;
            color: white;
        }

        /* 高级查询 */
        .advanced-search {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #f0f0f0;
            display: none;
        }

        .advanced-search.show {
            display: block;
        }

        .advanced-toggle {
            color: #1890ff;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 500;
        }

        /* 功能操作栏 */
        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            background: white;
            padding: 16px 24px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        }

        .action-buttons {
            display: flex;
            gap: 12px;
        }

        .btn-success {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .btn-success:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }

        .btn-danger {
            background-color: #ef4444;
            color: white;
            border-color: #ef4444;
        }

        .btn-danger:hover {
            background-color: #f87171;
            border-color: #f87171;
        }

        .btn-warning {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .btn-warning:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }

        /* 表格样式 */
        .table-container {
            border: 1px solid #f0f0f0;
            border-radius: 12px;
            overflow: hidden;
            background: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }

        .table-wrapper {
            overflow-x: auto;
            max-width: 100%;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            min-width: 1200px;
        }

        .data-table th,
        .data-table td {
            padding: 16px 20px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .data-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #374151;
        }

        .data-table tr:hover td {
            background-color: #f0f9ff;
        }

        /* 冻结列样式 */
        .frozen-column {
            position: sticky;
            background: white;
            z-index: 10;
        }

        .frozen-column.checkbox-col {
            left: 0;
            width: 50px;
            min-width: 50px;
        }

        .frozen-column.name-col {
            left: 50px;
            width: 200px;
            min-width: 200px;
        }

        .frozen-column.action-col {
            right: 0;
            width: 200px;
            min-width: 200px;
        }

        .data-table th.frozen-column {
            background-color: #f8f9fa;
        }

        .data-table tr:hover .frozen-column {
            background-color: #f0f9ff;
        }

        /* 冻结列阴影效果 */
        .frozen-column.name-col::after {
            content: '';
            position: absolute;
            top: 0;
            right: -1px;
            width: 1px;
            height: 100%;
            background: linear-gradient(to right, rgba(0,0,0,0.1), transparent);
        }

        .frozen-column.action-col::before {
            content: '';
            position: absolute;
            top: 0;
            left: -1px;
            width: 1px;
            height: 100%;
            background: linear-gradient(to left, rgba(0,0,0,0.1), transparent);
        }

        /* 表格链接 */
        .table-link {
            color: #1890ff;
            text-decoration: none;
            cursor: pointer;
        }

        .table-link:hover {
            text-decoration: underline;
        }

        /* 状态标签 */
        .status-tag {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background-color: #fef3c7;
            color: #d97706;
        }

        .status-processing {
            background-color: #dbeafe;
            color: #1d4ed8;
        }

        .status-approved {
            background-color: #dcfce7;
            color: #16a34a;
        }

        .status-rejected {
            background-color: #fee2e2;
            color: #dc2626;
        }

        .status-published {
            background-color: #dcfce7;
            color: #16a34a;
        }

        .status-draft {
            background-color: #f3f4f6;
            color: #6b7280;
        }

        /* 操作按钮 */
        .action-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            margin-right: 6px;
            transition: all 0.2s;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .action-btn:last-child {
            margin-right: 0;
        }

        .btn-submit {
            background-color: #1890ff;
            color: white;
        }

        .btn-submit:hover {
            background-color: #40a9ff;
            transform: translateY(-1px);
        }

        .btn-audit {
            background-color: #1890ff;
            color: white;
        }

        .btn-audit:hover {
            background-color: #40a9ff;
            transform: translateY(-1px);
        }

        .btn-publish {
            background-color: #1890ff;
            color: white;
        }

        .btn-publish:hover {
            background-color: #40a9ff;
            transform: translateY(-1px);
        }

        .btn-edit {
            background-color: #1890ff;
            color: white;
        }

        .btn-edit:hover {
            background-color: #40a9ff;
            transform: translateY(-1px);
        }

        .btn-delete {
            background-color: #ef4444;
            color: white;
        }

        .btn-delete:hover {
            background-color: #f87171;
            transform: translateY(-1px);
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            border-top: 1px solid #f0f0f0;
            background-color: #fafbfc;
        }

        .pagination-info {
            color: #6b7280;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .page-btn {
            min-width: 36px;
            height: 36px;
            padding: 0 12px;
            border: 1px solid #e5e7eb;
            background-color: white;
            color: #374151;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .page-btn:hover {
            background-color: #f9fafb;
            border-color: #1890ff;
        }

        .page-btn.active {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .page-btn:disabled {
            color: #d1d5db;
            background-color: #f9fafb;
            border-color: #e5e7eb;
            cursor: not-allowed;
        }

        .page-btn:disabled:hover {
            border-color: #e5e7eb;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            padding: 24px;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e6e8eb;
        }

        .help-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #6b7280;
        }

        .help-body {
            color: #374151;
            line-height: 1.6;
        }

        .help-section {
            margin-bottom: 16px;
        }

        .help-section h4 {
            color: #1f2937;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .help-section p {
            margin-bottom: 8px;
            font-size: 13px;
        }

        .help-list {
            list-style: none;
            padding-left: 16px;
        }

        .help-list li {
            margin-bottom: 4px;
            font-size: 13px;
            position: relative;
        }

        .help-list li::before {
            content: '•';
            color: #2563eb;
            position: absolute;
            left: -12px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .search-form {
                grid-template-columns: 1fr;
            }
            
            .action-bar {
                flex-direction: column;
                gap: 12px;
                align-items: stretch;
            }
            
            .action-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- 主体容器 -->
    <div class="main-container">
        <!-- 内容区域 -->
        <div class="content-wrapper">
            
            <!-- 页面内容 -->
            <div class="page-content">

                
                <!-- 主页签区域 -->
                <div class="main-tabs">
                    <div class="tab-item active" data-tab="pending" onclick="switchTab('pending')">
                        待办
                    </div>
                    <div class="tab-item" data-tab="completed" onclick="switchTab('completed')">
                        已办
                    </div>
                </div>

                <!-- 内容区域 -->
            <!-- 待办页签 -->
            <div id="pending-tab" class="tab-content active">
                    <!-- 查询区域 -->
                    <div class="search-area">
                    <div class="search-form">
                        <div class="form-group">
                            <label class="form-label">公告名称</label>
                            <input type="text" class="form-control" placeholder="请输入公告名称">
                        </div>
                        <div class="form-group">
                            <label class="form-label">审核状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="pending">待审核</option>
                                <option value="processing">审核中</option>
                                <option value="approved">已通过</option>
                                <option value="rejected">未通过</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">发布状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="pending">待发布</option>
                                <option value="published">已发布</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购方式</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="public">公开招标</option>
                                <option value="invite">邀请招标</option>
                                <option value="inquiry">询价采购</option>
                                <option value="single">单一来源</option>
                            </select>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-primary">查询</button>
                            <button class="btn btn-secondary">重置</button>
                            <button class="btn btn-outline" onclick="toggleAdvanced()">高级查询</button>
                        </div>
                    </div>
                    
                    <!-- 高级查询 -->
                    <div class="advanced-search" id="advanced-search">
                        <div class="search-form">
                            <div class="form-group">
                                <label class="form-label">采购类型</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="goods">货物采购</option>
                                    <option value="service">服务采购</option>
                                    <option value="engineering">工程采购</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">是否发布</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="yes">是</option>
                                    <option value="no">否</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">是否发布</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="yes">是</option>
                                    <option value="no">否</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">采购组织方式</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="centralized">集中采购</option>
                                    <option value="decentralized">分散采购</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">资金来源</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="fiscal">财政资金</option>
                                    <option value="self">自有资金</option>
                                    <option value="loan">贷款资金</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">招标金额</label>
                                <input type="text" class="form-control" placeholder="请输入金额范围">
                            </div>
                            <div class="form-group">
                                <label class="form-label">申请人</label>
                                <input type="text" class="form-control" placeholder="请输入申请人">
                            </div>
                            <div class="form-group">
                                <label class="form-label">招标时间</label>
                                <input type="date" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="form-label">计划时间</label>
                                <input type="date" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="form-label">创建时间</label>
                                <input type="date" class="form-control">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能操作栏 -->
                <div class="action-bar">
                    <div class="action-buttons">
                        <button class="btn btn-success" onclick="createAnnouncement()">新建公告</button>
                    </div>
                </div>

                <!-- 数据列表 -->
                <div class="table-container">
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th class="frozen-column checkbox-col">
                                        <input type="checkbox" onclick="toggleAll(this)">
                                    </th>
                                    <th class="frozen-column name-col">公告名称</th>
                                    <th style="width: 100px;">审核状态</th>
                                    <th style="width: 100px;">是否发布</th>
                                    <th style="width: 100px;">发布状态</th>
                                    <th style="width: 120px;">采购方式</th>
                                    <th style="width: 100px;">采购类型</th>
                                    <th style="width: 150px;">关联标段</th>
                                    <th style="width: 180px;">关联计划项目名称</th>
                                    <th style="width: 120px;">创建时间</th>
                                    <th class="frozen-column action-col">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('ANN-2024-001')">办公设备采购招标公告</a>
                                    </td>
                                    <td><span class="status-tag status-pending">待审核</span></td>
                                    <td><span class="status-tag status-draft">否</span></td>
                                    <td><span class="status-tag status-pending">待发布</span></td>
                                    <td>公开招标</td>
                                    <td>货物采购</td>
                                    <td>办公桌椅采购标段</td>
                                    <td>办公设备采购计划项目</td>
                                    <td>2024-03-15</td>
                                    <td class="frozen-column action-col">
                                        <button class="action-btn btn-submit" onclick="submitAnnouncement('ANN-2024-001')">提交</button>
                                        <button class="action-btn btn-edit" onclick="editAnnouncement('ANN-2024-001')">编辑</button>
                                        <button class="action-btn btn-delete" onclick="deleteAnnouncement('ANN-2024-001')">删除</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('ANN-2024-002')">IT系统建设招标公告</a>
                                    </td>
                                    <td><span class="status-tag status-processing">审核中</span></td>
                                    <td><span class="status-tag status-draft">否</span></td>
                                    <td><span class="status-tag status-pending">待发布</span></td>
                                    <td>邀请招标</td>
                                    <td>服务采购</td>
                                    <td>系统开发标段</td>
                                    <td>IT系统建设计划项目</td>
                                    <td>2024-03-16</td>
                                    <td class="frozen-column action-col">
                                        <button class="action-btn btn-audit" onclick="auditAnnouncement('ANN-2024-002')">审核</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('ANN-2024-003')">文具用品采购招标公告</a>
                                    </td>
                                    <td><span class="status-tag status-approved">已通过</span></td>
                                    <td><span class="status-tag status-draft">否</span></td>
                                    <td><span class="status-tag status-pending">待发布</span></td>
                                    <td>公开招标</td>
                                    <td>货物采购</td>
                                    <td>文具用品采购标段</td>
                                    <td>办公设备采购计划项目</td>
                                    <td>2024-03-17</td>
                                    <td class="frozen-column action-col">
                                        <button class="action-btn btn-publish" onclick="publishAnnouncement('ANN-2024-003')">发布</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('ANN-2024-004')">电脑设备采购招标公告</a>
                                    </td>
                                    <td><span class="status-tag status-rejected">未通过</span></td>
                                    <td><span class="status-tag status-draft">否</span></td>
                                    <td><span class="status-tag status-pending">待发布</span></td>
                                    <td>公开招标</td>
                                    <td>货物采购</td>
                                    <td>电脑设备采购标段</td>
                                    <td>办公设备采购计划项目</td>
                                    <td>2024-03-18</td>
                                    <td class="frozen-column action-col">
                                        <button class="action-btn btn-submit" onclick="submitAnnouncement('ANN-2024-004')">提交</button>
                                        <button class="action-btn btn-edit" onclick="editAnnouncement('ANN-2024-004')">编辑</button>
                                        <button class="action-btn btn-delete" onclick="deleteAnnouncement('ANN-2024-004')">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="pagination">
                        <div class="pagination-info">
                            共 4 条记录，当前第 1 页
                        </div>
                        <div class="pagination-controls">
                            <button class="page-btn" disabled>上一页</button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn" disabled>下一页</button>
                        </div>
                    </div>
                    </div>
                </div>
            </div>

            <!-- 已办页签 -->
            <div id="completed-tab" class="tab-content">
                <!-- 查询区域 -->
                <div class="search-area">
                    <div class="search-form">
                        <div class="form-group">
                            <label class="form-label">公告名称</label>
                            <input type="text" class="form-control" placeholder="请输入公告名称">
                        </div>
                        <div class="form-group">
                            <label class="form-label">审核状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="approved">已通过</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">发布状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="pending">待发布</option>
                                <option value="published">已发布</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购方式</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="public">公开招标</option>
                                <option value="invite">邀请招标</option>
                                <option value="inquiry">询价采购</option>
                                <option value="single">单一来源</option>
                            </select>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary">查询</button>
                            <button type="button" class="btn btn-secondary">重置</button>
                            <button type="button" class="btn btn-outline" onclick="toggleAdvancedCompleted()">高级查询</button>
                        </div>
                    </div>
                    
                    <!-- 高级查询 -->
                    <div class="advanced-search" id="advanced-search-completed" style="display: none;">
                        <div class="search-form">
                            <div class="form-group">
                                <label class="form-label">采购类型</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="goods">货物采购</option>
                                    <option value="service">服务采购</option>
                                    <option value="engineering">工程采购</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">是否发布</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="yes">是</option>
                                    <option value="no">否</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">采购组织方式</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="centralized">集中采购</option>
                                    <option value="decentralized">分散采购</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">资金来源</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="fiscal">财政资金</option>
                                    <option value="self">自有资金</option>
                                    <option value="loan">贷款资金</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">招标金额</label>
                                <input type="text" class="form-control" placeholder="请输入金额范围">
                            </div>
                            <div class="form-group">
                                <label class="form-label">申请人</label>
                                <input type="text" class="form-control" placeholder="请输入申请人">
                            </div>
                            <div class="form-group">
                                <label class="form-label">招标时间</label>
                                <input type="date" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="form-label">计划时间</label>
                                <input type="date" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="form-label">创建时间</label>
                                <input type="date" class="form-control">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能操作栏 -->
                <div class="action-bar">
                    <div class="action-buttons">
                        <!-- 已办页签无批量操作 -->
                    </div>
                </div>

                <!-- 数据列表 -->
                <div class="table-container">
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th class="frozen-column checkbox-col">
                                        <input type="checkbox" onclick="toggleAllCompleted(this)">
                                    </th>
                                    <th class="frozen-column name-col">公告名称</th>
                                    <th style="width: 100px;">审核状态</th>
                                    <th style="width: 100px;">是否发布</th>
                                    <th style="width: 100px;">发布状态</th>
                                    <th style="width: 120px;">采购方式</th>
                                    <th style="width: 100px;">采购类型</th>
                                    <th style="width: 150px;">关联标段</th>
                                    <th style="width: 180px;">关联计划项目名称</th>
                                    <th style="width: 120px;">创建时间</th>
                                    <th class="frozen-column action-col">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('ANN-2024-005')">办公家具采购招标公告</a>
                                    </td>
                                    <td><span class="status-tag status-approved">已通过</span></td>
                                    <td><span class="status-tag status-published">是</span></td>
                                    <td><span class="status-tag status-published">已发布</span></td>
                                    <td>公开招标</td>
                                    <td>货物采购</td>
                                    <td>办公家具采购标段</td>
                                    <td>办公设备采购计划项目</td>
                                    <td>2024-02-15</td>
                                    <td class="frozen-column action-col">
                                        <!-- 已办页签无删除操作 -->
                                    </td>
                                </tr>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('ANN-2024-006')">网络设备采购招标公告</a>
                                    </td>
                                    <td><span class="status-tag status-approved">已通过</span></td>
                                    <td><span class="status-tag status-published">是</span></td>
                                    <td><span class="status-tag status-published">已发布</span></td>
                                    <td>邀请招标</td>
                                    <td>货物采购</td>
                                    <td>网络设备采购标段</td>
                                    <td>IT系统建设计划项目</td>
                                    <td>2024-02-20</td>
                                    <td class="frozen-column action-col">
                                        <!-- 已办页签无删除操作 -->
                                    </td>
                                </tr>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('ANN-2024-007')">软件开发服务招标公告</a>
                                    </td>
                                    <td><span class="status-tag status-approved">已通过</span></td>
                                    <td><span class="status-tag status-published">是</span></td>
                                    <td><span class="status-tag status-published">已发布</span></td>
                                    <td>公开招标</td>
                                    <td>服务采购</td>
                                    <td>软件开发标段</td>
                                    <td>IT系统建设项目</td>
                                    <td>2024-02-25</td>
                                    <td class="frozen-column action-col">
                                        <!-- 已办页签无删除操作 -->
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="pagination">
                        <div class="pagination-info">
                            共 3 条记录，当前第 1 页
                        </div>
                        <div class="pagination-controls">
                            <button class="page-btn" disabled>上一页</button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn" disabled>下一页</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="help-modal" class="help-modal">
        <div class="help-content">
            <div class="help-header">
                <h3 class="help-title">公告管理功能说明</h3>
                <button class="close-btn" onclick="hideHelp()">&times;</button>
            </div>
            <div class="help-body">
                <div class="help-section">
                    <h4>功能概述</h4>
                    <p>公告管理模块用于管理招标采购过程中的各类公告信息，包括招标公告、中标公告、变更公告等。</p>
                </div>
                
                <div class="help-section">
                    <h4>主要功能</h4>
                    <ul class="help-list">
                        <li>公告信息的新建、编辑、删除</li>
                        <li>公告审核流程管理</li>
                        <li>公告发布状态控制</li>
                        <li>公告与项目、标段的关联管理</li>
                        <li>公告信息的查询和筛选</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>操作说明</h4>
                    <ul class="help-list">
                        <li><strong>待办页签：</strong>显示需要处理的公告，包括草稿、待审核、审核中等状态</li>
                        <li><strong>已办页签：</strong>显示已完成处理的公告，主要为已发布状态</li>
                        <li><strong>提交：</strong>将草稿状态的公告提交审核</li>
                        <li><strong>审核：</strong>对提交的公告进行审核，可通过或驳回</li>
                        <li><strong>发布：</strong>将审核通过的公告发布到公开平台</li>
                        <li><strong>编辑：</strong>修改公告内容，仅限草稿和驳回状态</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>状态说明</h4>
                    <ul class="help-list">
                        <li><strong>草稿：</strong>公告尚未提交，可以编辑</li>
                        <li><strong>待审核：</strong>公告已提交，等待审核</li>
                        <li><strong>审核中：</strong>公告正在审核过程中</li>
                        <li><strong>已通过：</strong>公告审核通过，可以发布</li>
                        <li><strong>未通过：</strong>公告审核未通过，需要修改</li>
                        <li><strong>已发布：</strong>公告已发布到公开平台</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>注意事项</h4>
                    <ul class="help-list">
                        <li>公告信息主要从关联的标段自动带出，减少重复录入</li>
                        <li>公告发布后不可修改，如需变更请创建变更公告</li>
                        <li>删除操作需要相应权限，已发布的公告建议撤回而非删除</li>
                        <li>批量操作时请确认选择的公告状态一致</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页签切换
        function switchTab(tabName) {
            // 隐藏所有页签内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
                tab.style.display = 'none';
            });
            
            // 移除所有页签的激活状态
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示选中的页签内容
            if (tabName === 'pending') {
                const pendingTab = document.getElementById('pending-tab');
                pendingTab.classList.add('active');
                pendingTab.style.display = 'block';
                document.querySelector('.tab-item:first-child').classList.add('active');
            } else if (tabName === 'completed') {
                const completedTab = document.getElementById('completed-tab');
                completedTab.classList.add('active');
                completedTab.style.display = 'block';
                document.querySelector('.tab-item:last-child').classList.add('active');
            }
        }

        // 高级查询切换
        function toggleAdvanced() {
            const advancedSearch = document.getElementById('advanced-search');
            const toggle = document.querySelector('.advanced-toggle');
            
            if (advancedSearch.classList.contains('show')) {
                advancedSearch.classList.remove('show');
                toggle.innerHTML = '高级查询 ▼';
            } else {
                advancedSearch.classList.add('show');
                toggle.innerHTML = '高级查询 ▲';
            }
        }

        function toggleAdvancedCompleted() {
            const advancedSearch = document.getElementById('advanced-search-completed');
            const toggle = document.querySelector('#completed-tab .advanced-toggle');
            
            if (advancedSearch.style.display === 'none' || advancedSearch.style.display === '') {
                advancedSearch.style.display = 'block';
                toggle.innerHTML = '高级查询 ▲';
            } else {
                advancedSearch.style.display = 'none';
                toggle.innerHTML = '高级查询 ▼';
            }
        }

        // 全选功能
        function toggleAll(checkbox) {
            const checkboxes = document.querySelectorAll('#pending-tab tbody input[type="checkbox"]');
            checkboxes.forEach(cb => {
                cb.checked = checkbox.checked;
            });
        }

        function toggleAllCompleted(checkbox) {
            const checkboxes = document.querySelectorAll('#completed-tab tbody input[type="checkbox"]');
            checkboxes.forEach(cb => {
                cb.checked = checkbox.checked;
            });
        }

        // 帮助弹窗
        function showHelp() {
            document.getElementById('help-modal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('help-modal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });

        // 业务操作函数
        function viewDetail(id) {
            console.log('查看公告详情:', id);
            // 在主页面页签中打开详情页面
            openPageInTab('公告详情-' + id, '公告管理-详情页.html?id=' + id);
        }

        function submitAnnouncement(id) {
            if (confirm('确定要提交这个公告吗？提交后将进入审核流程。')) {
                console.log('提交公告:', id);
                alert('公告提交成功！');
                // 这里可以调用提交接口，然后刷新页面
                location.reload();
            }
        }

        function auditAnnouncement(id) {
            console.log('审核公告:', id);
            // 在主页面页签中打开审核页面
            openPageInTab('公告审核-' + id, '公告管理-审核页.html?id=' + id);
        }

        function publishAnnouncement(id) {
            if (confirm('确定要发布这个公告吗？发布后将在公开平台展示。')) {
                console.log('发布公告:', id);
                alert('公告发布成功！');
                // 这里可以调用发布接口，然后刷新页面
                location.reload();
            }
        }

        function editAnnouncement(id) {
            console.log('编辑公告:', id);
            // 在主页面页签中打开编辑页面
            openPageInTab('编辑公告-' + id, '公告管理-新建编辑页.html?id=' + id + '&mode=edit');
        }

        function deleteAnnouncement(id) {
            if (confirm('确定要删除这个公告吗？删除后无法恢复。')) {
                console.log('删除公告:', id);
                alert('公告删除成功！');
                // 这里可以调用删除接口，然后刷新页面
                location.reload();
            }
        }

        // 新建公告
        function createAnnouncement() {
            openPageInTab('新建公告', '公告管理-新建编辑页.html?mode=create');
        }

        // 在主页面页签中打开页面的通用函数
        function openPageInTab(title, url) {
            // 向父窗口发送消息，在页签中打开页面
            if (window.parent && window.parent !== window) {
                window.parent.postMessage({
                    type: 'openPage',
                    title: title,
                    url: url
                }, '*');
            } else {
                // 如果不在iframe中，则在新窗口打开（兼容性处理）
                window.open(url, '_blank');
            }
        }

        // 批量删除
        function batchDelete() {
            const activeTab = document.querySelector('.tab-content.active');
            const checkedBoxes = activeTab.querySelectorAll('tbody input[type="checkbox"]:checked');
            
            if (checkedBoxes.length === 0) {
                alert('请选择要删除的公告');
                return;
            }
            
            if (confirm(`确定要删除选中的 ${checkedBoxes.length} 个公告吗？删除后无法恢复。`)) {
                console.log('批量删除公告');
                alert('批量删除成功！');
                location.reload();
            }
        }

        // 批量审核
        function batchAudit() {
            const checkedBoxes = document.querySelectorAll('#pending-tab tbody input[type="checkbox"]:checked');
            
            if (checkedBoxes.length === 0) {
                alert('请选择要审核的公告');
                return;
            }
            
            if (confirm(`确定要批量审核选中的 ${checkedBoxes.length} 个公告吗？`)) {
                console.log('批量审核公告');
                alert('批量审核操作已提交！');
                location.reload();
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示待办页签
            switchTab('pending');
        });
    </script>
</body>
</html>