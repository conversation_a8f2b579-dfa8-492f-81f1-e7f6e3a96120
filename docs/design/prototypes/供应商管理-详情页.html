<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>供应商管理 - 详情页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 页面头部 */
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
        }

        .page-title::before {
            content: '';
            width: 4px;
            height: 20px;
            background: #3498db;
            margin-right: 12px;
        }

        .help-icon {
            margin-left: 8px;
            color: #95a5a6;
            cursor: pointer;
            font-size: 16px;
        }

        .back-btn {
            padding: 8px 16px;
            background: #95a5a6;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .back-btn:hover {
            background: #7f8c8d;
        }

        /* 页签容器 */
        .tabs-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        /* 页签导航 */
        .tabs-nav {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .tab-item {
            padding: 15px 25px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
            font-size: 14px;
            font-weight: 500;
            color: #6c757d;
        }

        .tab-item:hover {
            background: #e9ecef;
            color: #495057;
        }

        .tab-item.active {
            background: white;
            color: #3498db;
            border-bottom-color: #3498db;
        }

        /* 页签内容 */
        .tab-content {
            display: none;
            padding: 20px;
        }

        .tab-content.active {
            display: block;
        }

        /* 详情布局 */
        .detail-section {
            margin-bottom: 30px;
        }

        .detail-section:last-child {
            margin-bottom: 0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
            display: flex;
            align-items: center;
        }

        .section-title::before {
            content: '';
            width: 3px;
            height: 16px;
            background: #3498db;
            margin-right: 10px;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .detail-item {
            display: flex;
            align-items: flex-start;
            padding: 12px 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 500;
            color: #2c3e50;
            min-width: 120px;
            margin-right: 15px;
            font-size: 14px;
        }

        .detail-value {
            color: #495057;
            font-size: 14px;
            flex: 1;
            word-break: break-all;
        }

        .detail-value.empty {
            color: #95a5a6;
            font-style: italic;
        }

        /* 文件链接 */
        .file-link {
            color: #3498db;
            text-decoration: none;
        }

        .file-link:hover {
            text-decoration: underline;
        }

        /* 项目总览统计 */
        .overview-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .stat-card:nth-child(2) {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .stat-card:nth-child(3) {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .stat-card:nth-child(4) {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .stat-card:nth-child(5) {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .stat-number {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }

        /* 查询区域 */
        .search-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .search-form {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .form-control {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .btn-group {
            display: flex;
            gap: 12px;
            margin-left: auto;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        /* 数据表格 */
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-wrapper {
            overflow-x: auto;
            max-width: 100%;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            min-width: 1000px;
        }

        /* 列宽设置 */
        .col-name { width: 200px; }
        .col-stage { width: 120px; }
        .col-status { width: 100px; }
        .col-project { width: 180px; }
        .col-type { width: 120px; }
        .col-method { width: 120px; }
        .col-time { width: 160px; }

        .data-table th,
        .data-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table td {
            font-size: 13px;
            color: #495057;
        }

        .data-table tr:hover {
            background: #f8f9fa;
        }

        /* 状态标签 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-badge.ongoing {
            background: #e3f2fd;
            color: #1976d2;
        }

        .status-badge.completed {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .status-badge.suspended {
            background: #fff3e0;
            color: #f57c00;
        }

        .status-badge.failed {
            background: #ffebee;
            color: #d32f2f;
        }

        /* 分页组件 */
        .pagination-container {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }

        .pagination-info {
            font-size: 14px;
            color: #6c757d;
        }

        .pagination {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .page-btn {
            padding: 6px 12px;
            border: 1px solid #dee2e6;
            background: white;
            color: #495057;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .page-btn:hover {
            background: #e9ecef;
        }

        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .page-btn:disabled {
            background: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 8px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .help-text {
            font-size: 14px;
            line-height: 1.8;
            color: #555;
            margin-bottom: 15px;
        }

        .close-help {
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            font-size: 24px;
            color: #95a5a6;
            cursor: pointer;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .tabs-nav {
                flex-wrap: wrap;
            }
            
            .tab-item {
                flex: 1;
                text-align: center;
                min-width: 120px;
            }
            
            .detail-grid {
                grid-template-columns: 1fr;
            }
            
            .overview-stats {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
            
            .search-form {
                flex-direction: column;
            }
            
            .form-group {
                min-width: 100%;
            }
            
            .btn-group {
                margin-left: 0;
                margin-top: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="page-title">
                供应商详情 - 北京建筑材料有限公司
                <span class="help-icon" onclick="showHelp()" title="帮助说明">ⓘ</span>
            </div>
            <button class="back-btn" onclick="goBack()">返回列表</button>
        </div>

        <!-- 页签容器 -->
        <div class="tabs-container">
            <!-- 页签导航 -->
            <div class="tabs-nav">
                <div class="tab-item active" onclick="switchTab('basic')">基本信息</div>
                <div class="tab-item" onclick="switchTab('account')">账户信息</div>
                <div class="tab-item" onclick="switchTab('extended')">扩展信息</div>
                <div class="tab-item" onclick="switchTab('tax')">税务信息</div>
                <div class="tab-item" onclick="switchTab('projects')">项目总览</div>
                <div class="tab-item" onclick="switchTab('operations')">操作记录</div>
            </div>

            <!-- 基本信息页签 -->
            <div class="tab-content active" id="basic">
                <div class="detail-section">
                    <div class="section-title">基本信息</div>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">境内外企业：</div>
                            <div class="detail-value">境内企业</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">企业名称：</div>
                            <div class="detail-value">北京建筑材料有限公司</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">是否分公司：</div>
                            <div class="detail-value">否</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">行业分类：</div>
                            <div class="detail-value">建筑材料</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">联系人：</div>
                            <div class="detail-value">张经理</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">联系电话：</div>
                            <div class="detail-value">13800138001</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">联系邮箱：</div>
                            <div class="detail-value"><EMAIL></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">法定代表人：</div>
                            <div class="detail-value">李建国</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">企业通讯地址：</div>
                            <div class="detail-value">北京市朝阳区建国路88号</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">注册地址：</div>
                            <div class="detail-value">北京市朝阳区建国路88号</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">营业执照附件：</div>
                            <div class="detail-value"><a href="#" class="file-link">营业执照.pdf</a></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">经营范围：</div>
                            <div class="detail-value">建筑材料销售、工程施工、技术咨询服务</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 账户信息页签 -->
            <div class="tab-content" id="account">
                <div class="detail-section">
                    <div class="section-title">账户信息</div>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">账户名称：</div>
                            <div class="detail-value">北京建筑材料有限公司</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">账号：</div>
                            <div class="detail-value">1234567890123456789</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">开户银行：</div>
                            <div class="detail-value">中国工商银行北京朝阳支行</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 扩展信息页签 -->
            <div class="tab-content" id="extended">
                <div class="detail-section">
                    <div class="section-title">扩展信息</div>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">企业性质：</div>
                            <div class="detail-value">民营企业</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">所属集团名称：</div>
                            <div class="detail-value empty">暂无</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">母公司名称：</div>
                            <div class="detail-value empty">暂无</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">供应商标签：</div>
                            <div class="detail-value">优质供应商</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">标签有效期：</div>
                            <div class="detail-value">2025-12-31</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">企业官网：</div>
                            <div class="detail-value"><a href="#" class="file-link">www.bjjc.com</a></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">企业logo：</div>
                            <div class="detail-value"><a href="#" class="file-link">logo.png</a></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">企业标签：</div>
                            <div class="detail-value">建材供应商,工程承包商</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 税务信息页签 -->
            <div class="tab-content" id="tax">
                <div class="detail-section">
                    <div class="section-title">税务信息</div>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">税务登记号：</div>
                            <div class="detail-value">110108123456789</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">税务类型：</div>
                            <div class="detail-value">一般纳税人</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">税务登记证失效日期：</div>
                            <div class="detail-value">2025-06-30</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">税务扫描件：</div>
                            <div class="detail-value"><a href="#" class="file-link">税务登记证.pdf</a></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 项目总览页签 -->
            <div class="tab-content" id="projects">
                <!-- 统计卡片 -->
                <div class="overview-stats">
                    <div class="stat-card">
                        <div class="stat-number">15</div>
                        <div class="stat-label">项目总数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">8</div>
                        <div class="stat-label">已完成项目数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">12</div>
                        <div class="stat-label">已成交标段数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">5</div>
                        <div class="stat-label">进行中标段数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">2</div>
                        <div class="stat-label">中止或流标标段数</div>
                    </div>
                </div>

                <!-- 查询区域 -->
                <div class="search-section">
                    <div class="search-form">
                        <div class="form-group">
                            <label class="form-label">标段名称</label>
                            <input type="text" class="form-control" placeholder="请输入标段名称">
                        </div>
                        <div class="form-group">
                            <label class="form-label">标段阶段</label>
                            <select class="form-control">
                                <option value="">全部阶段</option>
                                <option value="preparation">准备阶段</option>
                                <option value="bidding">招标阶段</option>
                                <option value="evaluation">评标阶段</option>
                                <option value="contract">合同阶段</option>
                                <option value="execution">执行阶段</option>
                                <option value="completion">完成阶段</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">所属项目</label>
                            <input type="text" class="form-control" placeholder="请输入项目名称">
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-primary">查询</button>
                            <button class="btn btn-secondary">重置</button>
                        </div>
                    </div>
                </div>

                <!-- 标段列表 -->
                <div class="table-container">
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th class="col-name">标段名称</th>
                                    <th class="col-stage">标段阶段</th>
                                    <th class="col-status">标段状态</th>
                                    <th class="col-project">所属项目</th>
                                    <th class="col-type">采购类型</th>
                                    <th class="col-method">采购方式</th>
                                    <th class="col-time">更新时间</th>
                                    <th class="col-time">创建时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>办公楼建设工程标段一</td>
                                    <td>执行阶段</td>
                                    <td><span class="status-badge ongoing">进行中</span></td>
                                    <td>某政府办公楼建设项目</td>
                                    <td>工程类</td>
                                    <td>公开招标</td>
                                    <td>2024-01-15 14:30</td>
                                    <td>2023-10-15 09:00</td>
                                </tr>
                                <tr>
                                    <td>设备采购标段</td>
                                    <td>完成阶段</td>
                                    <td><span class="status-badge completed">已完成</span></td>
                                    <td>医院设备采购项目</td>
                                    <td>货物类</td>
                                    <td>邀请招标</td>
                                    <td>2024-01-10 16:20</td>
                                    <td>2023-08-20 10:30</td>
                                </tr>
                                <tr>
                                    <td>物业服务标段</td>
                                    <td>评标阶段</td>
                                    <td><span class="status-badge suspended">暂停</span></td>
                                    <td>学校物业服务项目</td>
                                    <td>服务类</td>
                                    <td>竞争性谈判</td>
                                    <td>2024-01-08 11:45</td>
                                    <td>2023-12-01 14:15</td>
                                </tr>
                                <tr>
                                    <td>道路维修工程</td>
                                    <td>招标阶段</td>
                                    <td><span class="status-badge failed">流标</span></td>
                                    <td>市政道路维修项目</td>
                                    <td>工程类</td>
                                    <td>公开招标</td>
                                    <td>2024-01-05 09:30</td>
                                    <td>2023-11-15 16:00</td>
                                </tr>
                                <tr>
                                    <td>软件开发服务</td>
                                    <td>合同阶段</td>
                                    <td><span class="status-badge ongoing">进行中</span></td>
                                    <td>信息系统建设项目</td>
                                    <td>服务类</td>
                                    <td>单一来源</td>
                                    <td>2024-01-03 15:20</td>
                                    <td>2023-09-10 11:30</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页组件 -->
                    <div class="pagination-container">
                        <div class="pagination-info">
                            显示第 1-5 条，共 19 条记录
                        </div>
                        <div class="pagination">
                            <button class="page-btn" disabled>上一页</button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn">2</button>
                            <button class="page-btn">3</button>
                            <button class="page-btn">4</button>
                            <button class="page-btn">下一页</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作记录页签 -->
            <div class="tab-content" id="operations">
                <div class="table-container">
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th class="col-time">操作时间</th>
                                    <th class="col-type">操作类型</th>
                                    <th class="col-name">操作人员</th>
                                    <th class="col-project">操作内容</th>
                                    <th class="col-method">IP地址</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2024-01-15 14:30:25</td>
                                    <td>编辑</td>
                                    <td>张管理员</td>
                                    <td>修改联系电话</td>
                                    <td>*************</td>
                                </tr>
                                <tr>
                                    <td>2024-01-10 09:15:30</td>
                                    <td>查看</td>
                                    <td>李审核员</td>
                                    <td>查看供应商详情</td>
                                    <td>*************</td>
                                </tr>
                                <tr>
                                    <td>2024-01-05 16:45:12</td>
                                    <td>编辑</td>
                                    <td>王经理</td>
                                    <td>更新企业标签</td>
                                    <td>192.168.1.102</td>
                                </tr>
                                <tr>
                                    <td>2023-12-20 11:20:45</td>
                                    <td>创建</td>
                                    <td>系统管理员</td>
                                    <td>创建供应商档案</td>
                                    <td>192.168.1.1</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div class="help-modal" id="helpModal">
        <div class="help-content">
            <button class="close-help" onclick="hideHelp()">&times;</button>
            <div class="help-title">供应商管理 - 详情页说明</div>
            <div class="help-text">
                <strong>功能说明：</strong><br>
                本页面展示供应商的详细信息，包含基本信息、账户信息、扩展信息、税务信息、项目总览和操作记录六个页签。
            </div>
            <div class="help-text">
                <strong>基本信息：</strong><br>
                展示供应商的基础信息，包括企业名称、联系方式、经营范围等核心信息。
            </div>
            <div class="help-text">
                <strong>账户信息：</strong><br>
                展示供应商的银行账户信息，用于财务结算。
            </div>
            <div class="help-text">
                <strong>扩展信息：</strong><br>
                展示供应商的扩展属性，包括企业性质、标签信息、官网等。
            </div>
            <div class="help-text">
                <strong>税务信息：</strong><br>
                展示供应商的税务相关信息，包括税务登记号、税务类型等。
            </div>
            <div class="help-text">
                <strong>项目总览：</strong><br>
                展示供应商参与的项目统计信息和标段列表，支持查询和筛选。
            </div>
            <div class="help-text">
                <strong>操作记录：</strong><br>
                记录对该供应商的所有操作历史，包括创建、编辑、查看等操作。
            </div>
        </div>
    </div>

    <script>
        // 页签切换
        function switchTab(tabName) {
            // 隐藏所有页签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有页签的激活状态
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示选中的页签内容
            document.getElementById(tabName).classList.add('active');
            
            // 激活选中的页签
            event.target.classList.add('active');
        }

        // 返回列表页
        function goBack() {
            window.location.href = '供应商管理-列表页.html';
        }

        // 帮助弹窗
        function showHelp() {
            document.getElementById('helpModal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('helpModal').style.display = 'none';
        }

        // 点击弹窗外部关闭弹窗
        document.getElementById('helpModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });
    </script>
</body>
</html>