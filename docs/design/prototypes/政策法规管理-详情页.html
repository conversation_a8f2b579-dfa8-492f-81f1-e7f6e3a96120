<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>政策法规管理 - 详情 - 招采平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .page-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        .page-header {
            background: white;
            color: #333;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-bottom: 1px solid #e8eaec;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }

        .page-breadcrumb {
            margin-top: 8px;
            opacity: 0.7;
            font-size: 14px;
        }

        .page-content {
            padding: 24px;
        }

        /* 页签导航 */
        .tab-nav {
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 24px;
        }

        .tab-nav-list {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .tab-nav-item {
            margin-right: 32px;
        }

        .tab-nav-link {
            display: block;
            padding: 12px 0;
            color: #666;
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-nav-link:hover {
            color: #5cadff;
        }

        .tab-nav-link.active {
            color: #5cadff;
            border-bottom-color: #5cadff;
        }

        /* 页签内容 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 详情容器 */
        .detail-container {
            background: #fff;
            border: 1px solid #e8eaec;
            border-radius: 6px;
            overflow: hidden;
        }

        /* 详情分组 */
        .detail-section {
            border-bottom: 1px solid #e8eaec;
        }

        .detail-section:last-child {
            border-bottom: none;
        }

        .section-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e8eaec;
            font-weight: 600;
            font-size: 16px;
            color: #333;
            position: relative;
        }

        .section-header::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #5cadff;
        }

        .section-content {
            padding: 20px;
        }

        /* 详情行 */
        .detail-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }

        .detail-row:last-child {
            margin-bottom: 0;
        }

        .detail-group {
            flex: 1;
            min-width: 300px;
            display: flex;
            flex-direction: column;
        }

        .detail-group.full-width {
            flex: 1 1 100%;
            min-width: 100%;
        }

        .detail-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .detail-value {
            font-size: 14px;
            color: #333;
            padding: 10px 12px;
            background: #f8f9fa;
            border: 1px solid #e8eaec;
            border-radius: 4px;
            min-height: 40px;
            display: flex;
            align-items: center;
        }

        .detail-value.rich-content {
            align-items: flex-start;
            min-height: auto;
            padding: 15px;
            line-height: 1.6;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-draft {
            background: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }

        .status-published {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        /* 附件列表 */
        .attachment-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .attachment-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #e8eaec;
            border-radius: 4px;
            background: white;
        }

        .attachment-icon {
            width: 32px;
            height: 32px;
            background: #5cadff;
            color: white;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-size: 14px;
        }

        .attachment-info {
            flex: 1;
        }

        .attachment-name {
            font-size: 14px;
            color: #333;
            margin-bottom: 2px;
        }

        .attachment-size {
            font-size: 12px;
            color: #999;
        }

        .attachment-actions {
            display: flex;
            gap: 8px;
        }

        .attachment-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 3px;
            font-size: 12px;
            cursor: pointer;
            text-decoration: none;
            background: #e6f7ff;
            color: #1890ff;
        }

        .attachment-btn:hover {
            background: #bae7ff;
        }

        /* 时间轴 */
        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e8eaec;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 24px;
            padding-bottom: 24px;
        }

        .timeline-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -23px;
            top: 6px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #5cadff;
            border: 2px solid white;
            box-shadow: 0 0 0 2px #5cadff;
        }

        .timeline-content {
            background: #f8f9fa;
            border: 1px solid #e8eaec;
            border-radius: 6px;
            padding: 15px;
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .timeline-title {
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }

        .timeline-time {
            color: #999;
            font-size: 12px;
        }

        .timeline-desc {
            color: #666;
            font-size: 13px;
            line-height: 1.5;
        }

        .timeline-user {
            color: #5cadff;
            font-size: 13px;
            margin-top: 5px;
        }

        /* 操作按钮 */
        .detail-actions {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e8eaec;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #5cadff;
            color: white;
        }

        .btn-primary:hover {
            background: #4a9eff;
        }

        .btn-success {
            background: #52c41a;
            color: white;
        }

        .btn-success:hover {
            background: #45b018;
        }

        .btn-warning {
            background: #fa8c16;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        .btn-default {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #dcdee2;
        }

        .btn-default:hover {
            background: #e9ecef;
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .detail-row {
                flex-direction: column;
            }

            .detail-group {
                min-width: auto;
            }

            .detail-actions {
                flex-direction: column;
            }

            .btn {
                justify-content: center;
            }

            .tab-nav-item {
                margin-right: 16px;
            }

            .timeline {
                padding-left: 20px;
            }

            .timeline-item::before {
                left: -18px;
            }
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">政策法规详情</h1>
            <div class="page-breadcrumb">政策法规管理 > 详情</div>
        </div>

        <div class="page-content">
            <!-- 页签导航 -->
            <div class="tab-nav">
                <ul class="tab-nav-list">
                    <li class="tab-nav-item">
                        <a href="#basic-info" class="tab-nav-link active" onclick="switchTab(event, 'basic-info')">基本信息</a>
                    </li>
                    <li class="tab-nav-item">
                        <a href="#flow-records" class="tab-nav-link" onclick="switchTab(event, 'flow-records')">流程记录</a>
                    </li>
                </ul>
            </div>

            <!-- 基本信息页签 -->
            <div id="basic-info" class="tab-content active">
                <div class="detail-container">
                    <!-- 基本信息 -->
                    <div class="detail-section">
                        <div class="section-header">基本信息</div>
                        <div class="section-content">
                            <div class="detail-row">
                                <div class="detail-group">
                                    <div class="detail-label">标题名称</div>
                                    <div class="detail-value" id="titleName">政府采购法实施条例</div>
                                </div>
                                <div class="detail-group">
                                    <div class="detail-label">发布状态</div>
                                    <div class="detail-value">
                                        <span class="status-badge status-published" id="publishStatus">已发布</span>
                                    </div>
                                </div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-group">
                                    <div class="detail-label">更新人</div>
                                    <div class="detail-value" id="updateUser">张三</div>
                                </div>
                                <div class="detail-group">
                                    <div class="detail-label">更新时间</div>
                                    <div class="detail-value" id="updateTime">2024-01-15 14:30:25</div>
                                </div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-group">
                                    <div class="detail-label">创建人</div>
                                    <div class="detail-value" id="createUser">李四</div>
                                </div>
                                <div class="detail-group">
                                    <div class="detail-label">创建时间</div>
                                    <div class="detail-value" id="createTime">2024-01-10 09:15:30</div>
                                </div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-group full-width">
                                    <div class="detail-label">政策法规说明</div>
                                    <div class="detail-value rich-content" id="policyDescription">
                                        <p>为了规范政府采购行为，提高政府采购资金的使用效益，维护国家利益和社会公共利益，保护政府采购当事人的合法权益，促进廉政建设，制定本条例。</p>
                                        <p>本条例适用于在中华人民共和国境内进行的政府采购活动。</p>
                                        <p><strong>主要内容包括：</strong></p>
                                        <ul>
                                            <li>政府采购的基本原则和要求</li>
                                            <li>政府采购的方式和程序</li>
                                            <li>政府采购合同的管理</li>
                                            <li>政府采购的监督检查</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 附件信息 -->
                    <div class="detail-section">
                        <div class="section-header">附件信息</div>
                        <div class="section-content">
                            <div class="detail-row">
                                <div class="detail-group full-width">
                                    <div class="attachment-list" id="attachmentList">
                                        <div class="attachment-item">
                                            <div class="attachment-icon">📄</div>
                                            <div class="attachment-info">
                                                <div class="attachment-name">政府采购法实施条例.pdf</div>
                                                <div class="attachment-size">1.2 MB</div>
                                            </div>
                                            <div class="attachment-actions">
                                                <button class="attachment-btn" onclick="downloadAttachment('1')">下载</button>
                                                <button class="attachment-btn" onclick="previewAttachment('1')">预览</button>
                                            </div>
                                        </div>
                                        <div class="attachment-item">
                                            <div class="attachment-icon">📄</div>
                                            <div class="attachment-info">
                                                <div class="attachment-name">政策解读说明.docx</div>
                                                <div class="attachment-size">856 KB</div>
                                            </div>
                                            <div class="attachment-actions">
                                                <button class="attachment-btn" onclick="downloadAttachment('2')">下载</button>
                                                <button class="attachment-btn" onclick="previewAttachment('2')">预览</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="detail-actions">
                    <button type="button" class="btn btn-default" onclick="goBack()">返回</button>
                    <button type="button" class="btn btn-primary" onclick="editPolicy()" id="editBtn">编辑</button>
                    <button type="button" class="btn btn-success" onclick="publishPolicy()" id="publishBtn" style="display: none;">发布</button>
                    <button type="button" class="btn btn-warning" onclick="unpublishPolicy()" id="unpublishBtn">撤销</button>
                </div>
            </div>

            <!-- 流程记录页签 -->
            <div id="flow-records" class="tab-content">
                <div class="detail-container">
                    <div class="detail-section">
                        <div class="section-header">操作记录</div>
                        <div class="section-content">
                            <div class="timeline" id="timelineList">
                                <div class="timeline-item">
                                    <div class="timeline-content">
                                        <div class="timeline-header">
                                            <div class="timeline-title">发布政策法规</div>
                                            <div class="timeline-time">2024-01-15 14:30:25</div>
                                        </div>
                                        <div class="timeline-desc">将政策法规状态更新为"已发布"，正式对外公开</div>
                                        <div class="timeline-user">操作人：张三</div>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-content">
                                        <div class="timeline-header">
                                            <div class="timeline-title">编辑政策法规</div>
                                            <div class="timeline-time">2024-01-12 16:45:10</div>
                                        </div>
                                        <div class="timeline-desc">修改了政策法规说明内容，更新了相关附件</div>
                                        <div class="timeline-user">操作人：张三</div>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-content">
                                        <div class="timeline-header">
                                            <div class="timeline-title">创建政策法规</div>
                                            <div class="timeline-time">2024-01-10 09:15:30</div>
                                        </div>
                                        <div class="timeline-desc">创建了新的政策法规记录，状态为"待发布"</div>
                                        <div class="timeline-user">操作人：李四</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPolicy = {
            id: '1',
            titleName: '政府采购法实施条例',
            status: 'published',
            updateUser: '张三',
            updateTime: '2024-01-15 14:30:25',
            createUser: '李四',
            createTime: '2024-01-10 09:15:30',
            description: '政策法规详细说明内容...',
            attachments: [
                { id: '1', name: '政府采购法实施条例.pdf', size: '1.2 MB' },
                { id: '2', name: '政策解读说明.docx', size: '856 KB' }
            ]
        };

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 从URL参数获取政策法规ID
            const urlParams = new URLSearchParams(window.location.search);
            const policyId = urlParams.get('id');
            
            if (policyId) {
                loadPolicyDetail(policyId);
            }
            
            updateActionButtons();
        });

        // 页签切换
        function switchTab(event, tabId) {
            event.preventDefault();
            
            // 移除所有活动状态
            document.querySelectorAll('.tab-nav-link').forEach(link => {
                link.classList.remove('active');
            });
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 激活当前页签
            event.target.classList.add('active');
            document.getElementById(tabId).classList.add('active');
        }

        // 加载政策法规详情
        function loadPolicyDetail(id) {
            // 模拟加载数据
            console.log('加载政策法规详情:', id);
            
            // 更新页面数据
            document.getElementById('titleName').textContent = currentPolicy.titleName;
            document.getElementById('updateUser').textContent = currentPolicy.updateUser;
            document.getElementById('updateTime').textContent = currentPolicy.updateTime;
            document.getElementById('createUser').textContent = currentPolicy.createUser;
            document.getElementById('createTime').textContent = currentPolicy.createTime;
            
            // 更新状态
            const statusElement = document.getElementById('publishStatus');
            if (currentPolicy.status === 'published') {
                statusElement.textContent = '已发布';
                statusElement.className = 'status-badge status-published';
            } else {
                statusElement.textContent = '待发布';
                statusElement.className = 'status-badge status-draft';
            }
        }

        // 更新操作按钮
        function updateActionButtons() {
            const editBtn = document.getElementById('editBtn');
            const publishBtn = document.getElementById('publishBtn');
            const unpublishBtn = document.getElementById('unpublishBtn');
            
            if (currentPolicy.status === 'published') {
                editBtn.style.display = 'none';
                publishBtn.style.display = 'none';
                unpublishBtn.style.display = 'inline-flex';
            } else {
                editBtn.style.display = 'inline-flex';
                publishBtn.style.display = 'inline-flex';
                unpublishBtn.style.display = 'none';
            }
        }

        // 编辑政策法规
        function editPolicy() {
            const editUrl = `政策法规管理-新建编辑页.html?id=${currentPolicy.id}`;
            
            if (window.parent && window.parent.openTab) {
                window.parent.openTab(editUrl, '编辑政策法规');
            } else {
                window.location.href = editUrl;
            }
        }

        // 发布政策法规
        function publishPolicy() {
            if (confirm('确定要发布这条政策法规吗？发布后将对外公开。')) {
                console.log('发布政策法规:', currentPolicy.id);
                
                // 更新状态
                currentPolicy.status = 'published';
                currentPolicy.updateTime = new Date().toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                }).replace(/\//g, '-');
                
                // 更新页面显示
                loadPolicyDetail(currentPolicy.id);
                updateActionButtons();
                
                alert('发布成功');
                
                // 添加操作记录
                addTimelineRecord('发布政策法规', '将政策法规状态更新为"已发布"，正式对外公开');
            }
        }

        // 撤销政策法规
        function unpublishPolicy() {
            if (confirm('确定要撤销这条政策法规吗？撤销后将不再对外公开。')) {
                console.log('撤销政策法规:', currentPolicy.id);
                
                // 更新状态
                currentPolicy.status = 'draft';
                currentPolicy.updateTime = new Date().toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                }).replace(/\//g, '-');
                
                // 更新页面显示
                loadPolicyDetail(currentPolicy.id);
                updateActionButtons();
                
                alert('撤销成功');
                
                // 添加操作记录
                addTimelineRecord('撤销政策法规', '将政策法规状态更新为"待发布"，停止对外公开');
            }
        }

        // 添加时间轴记录
        function addTimelineRecord(title, description) {
            const timeline = document.getElementById('timelineList');
            const currentTime = new Date().toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            }).replace(/\//g, '-');
            
            const newRecord = document.createElement('div');
            newRecord.className = 'timeline-item';
            newRecord.innerHTML = `
                <div class="timeline-content">
                    <div class="timeline-header">
                        <div class="timeline-title">${title}</div>
                        <div class="timeline-time">${currentTime}</div>
                    </div>
                    <div class="timeline-desc">${description}</div>
                    <div class="timeline-user">操作人：当前用户</div>
                </div>
            `;
            
            timeline.insertBefore(newRecord, timeline.firstChild);
        }

        // 下载附件
        function downloadAttachment(attachmentId) {
            console.log('下载附件:', attachmentId);
            alert('开始下载附件');
        }

        // 预览附件
        function previewAttachment(attachmentId) {
            console.log('预览附件:', attachmentId);
            alert('打开附件预览');
        }

        // 返回
        function goBack() {
            if (window.parent && window.parent.closeTab) {
                window.parent.closeTab(null, '政策法规详情');
            } else {
                window.history.back();
            }
        }
    </script>
</body>
</html>