<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流标或中止管理 - 详情 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #fff;
            border-bottom: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
        }

        .help-icon {
            margin-left: 8px;
            width: 16px;
            height: 16px;
            background: #6b7280;
            border-radius: 50%;
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .breadcrumb {
            color: #6b7280;
            font-size: 14px;
        }

        .back-btn {
            background: #6b7280;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: background 0.3s;
        }

        .back-btn:hover {
            background: #4b5563;
        }

        /* 内容区域 */
        .content-area {
            padding: 24px;
        }

        /* 页签导航 */
        .tab-nav {
            border-bottom: 1px solid #e6e8eb;
            margin-bottom: 24px;
        }

        .tab-list {
            display: flex;
            list-style: none;
        }

        .tab-item {
            margin-right: 32px;
        }

        .tab-link {
            display: block;
            padding: 12px 0;
            color: #6b7280;
            text-decoration: none;
            font-size: 14px;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab-link.active {
            color: #2563eb;
            border-bottom-color: #2563eb;
        }

        .tab-link:hover {
            color: #2563eb;
        }

        /* 页签内容 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 详情区域 */
        .detail-section {
            background: white;
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .section-header {
            background: #f8fafc;
            padding: 16px 20px;
            border-bottom: 1px solid #e6e8eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 16px;
            background: #3b82f6;
            margin-right: 8px;
        }

        .section-content {
            padding: 20px;
        }

        /* 详情网格 */
        .detail-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-item.full-width {
            grid-column: 1 / -1;
        }

        .detail-label {
            font-size: 13px;
            color: #6b7280;
            margin-bottom: 6px;
            font-weight: 500;
        }

        .detail-value {
            font-size: 14px;
            color: #1f2937;
            word-wrap: break-word;
        }

        .detail-value.empty {
            color: #9ca3af;
            font-style: italic;
        }

        /* 富文本内容样式 */
        .rich-content {
            line-height: 1.8;
            color: #374151;
        }

        .rich-content h1,
        .rich-content h2,
        .rich-content h3,
        .rich-content h4,
        .rich-content h5,
        .rich-content h6 {
            color: #1f2937;
            margin: 20px 0 12px 0;
            font-weight: 600;
        }

        .rich-content h1 {
            font-size: 24px;
        }

        .rich-content h2 {
            font-size: 20px;
        }

        .rich-content h3 {
            font-size: 18px;
        }

        .rich-content h4 {
            font-size: 16px;
        }

        .rich-content p {
            margin: 12px 0;
        }

        .rich-content ul,
        .rich-content ol {
            margin: 12px 0;
            padding-left: 24px;
        }

        .rich-content li {
            margin: 6px 0;
        }

        .rich-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 16px 0;
        }

        .rich-content table th,
        .rich-content table td {
            border: 1px solid #d1d5db;
            padding: 8px 12px;
            text-align: left;
        }

        .rich-content table th {
            background: #f8fafc;
            font-weight: 600;
        }

        .rich-content .signature {
            text-align: right;
            margin-top: 40px;
            font-weight: 600;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-draft {
            background: #f3f4f6;
            color: #374151;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-approved {
            background: #d1fae5;
            color: #065f46;
        }

        .status-published {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-rejected {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-withdrawn {
            background: #f3f4f6;
            color: #6b7280;
        }

        /* 底部操作栏 */
        .action-bar {
            background: #f8fafc;
            border-top: 1px solid #e6e8eb;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            bottom: 0;
        }

        .action-left {
            display: flex;
            gap: 12px;
        }

        .action-right {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-outline {
            background: white;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        /* 时间线样式 */
        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e6e8eb;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 24px;
            background: white;
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            padding: 16px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -23px;
            top: 20px;
            width: 12px;
            height: 12px;
            background: #3b82f6;
            border: 2px solid white;
            border-radius: 50%;
            box-shadow: 0 0 0 2px #e6e8eb;
        }

        .timeline-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .timeline-info {
            flex: 1;
        }

        .timeline-action {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .timeline-details {
            font-size: 13px;
            color: #6b7280;
            line-height: 1.5;
        }

        .timeline-meta {
            text-align: right;
            font-size: 12px;
            color: #9ca3af;
        }

        .timeline-time {
            font-weight: 500;
            color: #6b7280;
        }

        .timeline-user {
            margin-top: 2px;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e6e8eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .help-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            color: #6b7280;
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            color: #374151;
        }

        .help-body {
            padding: 24px;
        }

        .help-section {
            margin-bottom: 24px;
        }

        .help-section h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
        }

        .help-section p {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 12px;
        }

        .help-list {
            list-style: none;
            padding-left: 0;
        }

        .help-list li {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 8px;
            padding-left: 16px;
            position: relative;
        }

        .help-list li::before {
            content: '•';
            color: #3b82f6;
            position: absolute;
            left: 0;
        }

        .help-list strong {
            color: #1f2937;
        }

        /* 响应式 */
        @media (max-width: 1200px) {
            .detail-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .detail-grid {
                grid-template-columns: 1fr;
            }
            
            .action-bar {
                flex-direction: column;
                gap: 12px;
            }
            
            .action-left,
            .action-right {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">
                    流标或中止管理 - 详情
                    <span class="help-icon" onclick="showHelp()" title="功能说明">?</span>
                </h1>
                <div class="breadcrumb">首页 > 流标或中止管理 > 详情</div>
            </div>
            <button class="back-btn" onclick="goBack()">返回列表</button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 页签导航 -->
            <div class="tab-nav">
                <ul class="tab-list">
                    <li class="tab-item">
                        <a href="#" class="tab-link active" onclick="switchTab('detail')">公告详情</a>
                    </li>
                    <li class="tab-item">
                        <a href="#" class="tab-link" onclick="switchTab('project')">项目信息</a>
                    </li>
                    <li class="tab-item">
                        <a href="#" class="tab-link" onclick="switchTab('history')">操作记录</a>
                    </li>
                </ul>
            </div>

            <!-- 公告详情页签 -->
            <div id="detail-tab" class="tab-content active">
                <!-- 基本信息 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">基本信息</h3>
                    </div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">公告标题</div>
                                <div class="detail-value">办公设备采购项目招标公告</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">公告编号</div>
                                <div class="detail-value">GG-2024-001</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">公告类型</div>
                                <div class="detail-value">招标公告</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">状态</div>
                                <div class="detail-value">
                                    <span class="status-badge status-published">已发布</span>
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">创建人</div>
                                <div class="detail-value">张三</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">创建时间</div>
                                <div class="detail-value">2024-01-10 14:30:25</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 公告内容 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">公告内容</h3>
                    </div>
                    <div class="section-content">
                        <div class="rich-content">
                            <h3>一、项目概况</h3>
                            <p>本项目为办公设备采购项目，项目编号：XM-2024-001，预算金额：50.00万元。采购内容包括台式电脑、笔记本电脑、打印机、复印机等办公设备。</p>
                            
                            <h3>二、申请人的资格要求</h3>
                            <p>1. 具有独立承担民事责任的能力；</p>
                            <p>2. 具有良好的商业信誉和健全的财务会计制度；</p>
                            <p>3. 具有履行合同所必需的设备和专业技术能力；</p>
                            <p>4. 有依法缴纳税收和社会保障资金的良好记录；</p>
                            <p>5. 参加政府采购活动前三年内，在经营活动中没有重大违法记录；</p>
                            <p>6. 具有计算机及办公设备销售、安装、维护的相关资质和经验。</p>
                            
                            <h3>三、获取招标文件</h3>
                            <p><strong>时间：</strong>2024年1月20日至2024年1月25日，每日上午8:30-11:30，下午14:30-17:30（北京时间，法定节假日除外）</p>
                            <p><strong>地点：</strong>市政府采购中心二楼服务大厅</p>
                            <p><strong>方式：</strong>现场获取或登录市政府采购网下载</p>
                            <p><strong>售价：</strong>免费</p>
                            
                            <h3>四、投标文件的递交</h3>
                            <p><strong>截止时间：</strong>2024年1月26日上午9:00（北京时间）</p>
                            <p><strong>地点：</strong>市政府采购中心三楼开标室</p>
                            <p><strong>注意事项：</strong>逾期送达或者未送达指定地点的投标文件，采购人不予受理。</p>
                            
                            <h3>五、开标时间及地点</h3>
                            <p><strong>时间：</strong>2024年1月26日上午9:00（北京时间）</p>
                            <p><strong>地点：</strong>市政府采购中心三楼开标室</p>
                            
                            <h3>六、联系方式</h3>
                            <p><strong>采购人：</strong>市政府办公室</p>
                            <p><strong>地址：</strong>市政府大楼1208室</p>
                            <p><strong>联系人：</strong>李主任</p>
                            <p><strong>电话：</strong>0755-12345678</p>
                            
                            <p><strong>采购代理机构：</strong>市政府采购中心</p>
                            <p><strong>地址：</strong>市民中心B区2楼</p>
                            <p><strong>联系人：</strong>王经理</p>
                            <p><strong>电话：</strong>0755-87654321</p>
                            
                            <div class="signature">
                                <p><strong>市政府办公室</strong></p>
                                <p>2024年1月20日</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 项目信息页签 -->
            <div id="project-tab" class="tab-content">
                <!-- 项目信息 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">项目信息</h3>
                    </div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">项目名称</div>
                                <div class="detail-value">办公设备采购项目</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">项目标题</div>
                                <div class="detail-value">办公设备采购项目招标</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">项目业主</div>
                                <div class="detail-value">市政府办公室</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">采购方式</div>
                                <div class="detail-value">公开招标</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购类别</div>
                                <div class="detail-value">货物采购</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">所属二级公司单位</div>
                                <div class="detail-value">市政府办公室</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">立项决策日期</div>
                                <div class="detail-value">2024-01-08</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">创建时间</div>
                                <div class="detail-value">2024-01-10 14:30:25</div>
                            </div>
                            <div class="detail-item">
                                <!-- 空位保持布局对齐 -->
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">项目基本情况</div>
                                <div class="detail-value">本项目为办公设备采购项目，采购内容包括台式电脑、笔记本电脑、打印机、复印机等办公设备，预算金额50.00万元。</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 关联标段信息 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">关联标段信息</h3>
                    </div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">标段编号</div>
                                <div class="detail-value">BD-2024-001</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">标段名称</div>
                                <div class="detail-value">办公设备采购</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">是否公示</div>
                                <div class="detail-value">
                                    <span class="status-badge status-published">是</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">标段阶段</div>
                                <div class="detail-value">流标或中止</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">标段状态</div>
                                <div class="detail-value">
                                    <span class="status-badge status-published">已发布</span>
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购金额（万元）</div>
                                <div class="detail-value">50.00</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">标段说明</div>
                                <div class="detail-value">采购台式电脑、笔记本电脑、打印机、复印机等办公设备</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 采购需求 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">采购需求</h3>
                    </div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item full-width">
                                <div class="detail-label">采购内容</div>
                                <div class="detail-value">台式电脑20台、笔记本电脑10台、激光打印机5台、多功能一体机3台、投影仪2台及相关配件</div>
                            </div>
                            <div class="detail-item full-width">
                                <div class="detail-label">技术要求</div>
                                <div class="detail-value">所有设备需符合国家相关标准，具有3C认证，提供3年质保服务</div>
                            </div>
                            <div class="detail-item full-width">
                                <div class="detail-label">交付要求</div>
                                <div class="detail-value">合同签订后30个工作日内完成设备交付、安装、调试及培训</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 时间安排 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">时间安排</h3>
                    </div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">公告发布时间</div>
                                <div class="detail-value">2024-01-20 09:00</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">文件获取时间</div>
                                <div class="detail-value">2024-01-20 至 2024-01-25</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">投标截止时间</div>
                                <div class="detail-value">2024-01-26 09:00</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">开标时间</div>
                                <div class="detail-value">2024-01-26 09:00</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">评标时间</div>
                                <div class="detail-value">2024-01-26 09:30</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">中标公示时间</div>
                                <div class="detail-value">2024-01-28 至 2024-01-30</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 参与方信息 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">参与方信息</h3>
                    </div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">采购人联系人</div>
                                <div class="detail-value">李主任</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购人电话</div>
                                <div class="detail-value">0755-12345678</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">代理机构联系人</div>
                                <div class="detail-value">王经理</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">代理机构电话</div>
                                <div class="detail-value">0755-87654321</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">监督部门</div>
                                <div class="detail-value">市财政局政府采购监督处</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">监督电话</div>
                                <div class="detail-value">0755-11111111</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作记录页签 -->
            <div id="history-tab" class="tab-content">
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">操作记录</h3>
                    </div>
                    <div class="section-content">
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-info">
                                        <div class="timeline-action">提交审批</div>
                                        <div class="timeline-details">
                                            公告内容已完善，提交给审核人员进行审核。<br>
                                            审核意见：内容完整，格式规范，同意发布。
                                        </div>
                                    </div>
                                    <div class="timeline-meta">
                                        <div class="timeline-time">2024-01-15 09:00:00</div>
                                        <div class="timeline-user">操作人：张三</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-info">
                                        <div class="timeline-action">编辑公告</div>
                                        <div class="timeline-details">
                                            修改了公告内容中的联系方式信息，更新了开标地点。<br>
                                            主要变更：开标地点从二楼会议室改为三楼开标室。
                                        </div>
                                    </div>
                                    <div class="timeline-meta">
                                        <div class="timeline-time">2024-01-12 16:30:00</div>
                                        <div class="timeline-user">操作人：张三</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-info">
                                        <div class="timeline-action">创建公告</div>
                                        <div class="timeline-details">
                                            创建了办公设备采购项目招标公告，填写了基本信息和公告内容。<br>
                                            状态：草稿，等待进一步完善。
                                        </div>
                                    </div>
                                    <div class="timeline-meta">
                                        <div class="timeline-time">2024-01-10 14:30:00</div>
                                        <div class="timeline-user">操作人：张三</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部操作栏 -->
        <div class="action-bar">
            <div class="action-left">
                <button class="btn btn-secondary" onclick="goBack()">返回列表</button>
            </div>
            <div class="action-right">
                <button class="btn btn-outline" onclick="printNotice()">打印公告</button>
                <button class="btn btn-success" onclick="editNotice()">编辑</button>
                <button class="btn btn-warning" onclick="withdrawNotice()">撤回</button>
                <button class="btn btn-danger" onclick="deleteNotice()">删除</button>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="help-modal" class="help-modal">
        <div class="help-content">
            <div class="help-header">
                <h3 class="help-title">公告详情功能说明</h3>
                <button class="close-btn" onclick="hideHelp()">&times;</button>
            </div>
            <div class="help-body">
                <div class="help-section">
                    <h4>功能概述</h4>
                    <p>公告详情页面用于查看招标采购公告的完整信息，包括公告内容、项目信息和操作记录。支持打印、编辑、撤回等操作。</p>
                </div>
                
                <div class="help-section">
                    <h4>页签说明</h4>
                    <ul class="help-list">
                        <li><strong>公告详情：</strong>显示公告的基本信息和完整内容</li>
                        <li><strong>项目信息：</strong>显示项目的详细信息，包括采购需求、时间安排等</li>
                        <li><strong>操作记录：</strong>显示公告的操作历史记录</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>操作说明</h4>
                    <ul class="help-list">
                        <li><strong>打印公告：</strong>打印公告内容，用于存档或发布</li>
                        <li><strong>编辑：</strong>修改公告内容（仅草稿和已驳回状态可编辑）</li>
                        <li><strong>撤回：</strong>撤回已发布的公告</li>
                        <li><strong>删除：</strong>删除公告（仅草稿状态可删除）</li>
                        <li><strong>返回列表：</strong>返回公告列表页面</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>状态说明</h4>
                    <ul class="help-list">
                        <li><strong>草稿：</strong>公告已创建但未提交审核</li>
                        <li><strong>待审核：</strong>公告已提交，等待审核</li>
                        <li><strong>已审核：</strong>公告审核通过，等待发布</li>
                        <li><strong>已发布：</strong>公告已正式发布</li>
                        <li><strong>已驳回：</strong>公告审核未通过，需要修改</li>
                        <li><strong>已撤回：</strong>公告已撤回，不再有效</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>注意事项</h4>
                    <ul class="help-list">
                        <li>已发布的公告撤回后需要重新审核才能再次发布</li>
                        <li>删除操作不可恢复，请谨慎操作</li>
                        <li>编辑功能仅在特定状态下可用</li>
                        <li>操作记录会详细记录每次操作的时间和操作人</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页签切换
        function switchTab(tabName) {
            // 隐藏所有页签内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有页签链接的激活状态
            const tabLinks = document.querySelectorAll('.tab-link');
            tabLinks.forEach(link => {
                link.classList.remove('active');
            });
            
            // 显示选中的页签内容
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 激活选中的页签链接
            event.target.classList.add('active');
        }

        // 帮助弹窗
        function showHelp() {
            document.getElementById('help-modal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('help-modal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });

        // 业务操作函数
        function goBack() {
            window.location.href = '流标或中止管理-列表页.html';
        }

        function editNotice() {
            // 获取当前公告ID（实际应用中从URL参数获取）
            const noticeId = new URLSearchParams(window.location.search).get('id') || '1';
            window.location.href = `流标或中止管理-新建编辑页.html?id=${noticeId}`;
        }

        function withdrawNotice() {
            if (confirm('确定要撤回这个公告吗？撤回后公告将不再有效。')) {
                console.log('撤回公告');
                alert('公告撤回成功！');
                // 这里应该调用撤回接口，并更新页面状态
            }
        }

        function deleteNotice() {
            if (confirm('确定要删除这个公告吗？删除后无法恢复。')) {
                console.log('删除公告');
                alert('公告删除成功！');
                window.location.href = '流标或中止管理-列表页.html';
            }
        }

        function printNotice() {
            // 打印公告内容
            window.print();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('公告详情页面加载完成');
            
            // 根据URL参数加载对应的公告数据
            const noticeId = new URLSearchParams(window.location.search).get('id');
            if (noticeId) {
                console.log('加载公告ID:', noticeId);
                // 这里应该调用接口加载具体的公告数据
            }
        });
    </script>
</body>
</html>