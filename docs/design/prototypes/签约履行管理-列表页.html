<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>签约履行管理 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e6e8eb;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .help-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            background-color: #3b82f6;
            color: white;
            border-radius: 50%;
            font-size: 12px;
            cursor: pointer;
            font-weight: bold;
        }

        .help-icon:hover {
            margin-top: 4px;
        }

        /* 页签样式 */
        .tab-nav {
            margin-bottom: 24px;
            border-bottom: 1px solid #e6e8eb;
        }

        .tab-list {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .tab-item {
            margin-right: 32px;
        }

        .tab-link {
            display: block;
            padding: 12px 0;
            color: #6b7280;
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .tab-link:hover {
            color: #3b82f6;
        }

        .tab-link.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }

        /* 内容区域 */
        .content-area {
            background-color: #fff;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 查询区域 */
        .search-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 13px;
            color: #555;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-control {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .btn-group {
            display: flex;
            gap: 10px;
            align-items: end;
        }

        /* 高级查询 */
        .advanced-search {
            display: none;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e6e8eb;
        }

        .advanced-search.show {
            display: block;
        }

        .advanced-search .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }



        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-outline {
            background: white;
            color: #3498db;
            border: 1px solid #3498db;
        }

        .btn-outline:hover {
            background: #3498db;
            color: white;
        }

        .btn-success {
            background-color: #52c41a;
            color: white;
            border-color: #52c41a;
        }

        .btn-success:hover {
            background-color: #73d13d;
            border-color: #73d13d;
        }

        .btn-danger {
            background-color: #ff4d4f;
            color: white;
            border-color: #ff4d4f;
        }

        .btn-danger:hover {
            background-color: #ff7875;
            border-color: #ff7875;
        }

        .btn-warning {
            background-color: #faad14;
            color: white;
            border-color: #faad14;
        }

        .btn-warning:hover {
            background-color: #ffc53d;
            border-color: #ffc53d;
        }

        .btn-link {
            background-color: transparent;
            color: #3b82f6;
            border: 1px solid #3b82f6;
        }

        .btn-link:hover {
            background-color: #3b82f6;
            color: white;
        }

        .btn-success {
            background-color: #10b981;
            color: white;
        }

        .btn-success:hover {
            background-color: #059669;
        }

        .btn-warning {
            background-color: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background-color: #d97706;
        }

        .btn-danger {
            background-color: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background-color: #dc2626;
        }

        /* 功能操作栏 */
        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
        }

        /* 表格样式 */
        .table-container {
            background-color: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .table-wrapper {
            overflow-x: auto;
            max-width: 100%;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            min-width: 1200px;
        }

        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
            font-size: 13px;
        }

        .data-table th {
            background-color: #f8fafc;
            font-weight: 600;
            color: #374151;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table tbody tr:hover {
            background-color: #f8fafc;
        }

        /* 冻结列样式 */
        .col-checkbox {
            width: 50px;
            position: sticky;
            left: 0;
            background-color: inherit;
            z-index: 5;
            box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
        }

        .col-title {
            width: 200px;
            position: sticky;
            left: 50px;
            background-color: inherit;
            z-index: 5;
            box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
        }

        .col-actions {
            width: 180px;
            position: sticky;
            right: 0;
            background-color: inherit;
            z-index: 5;
            box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
        }

        .data-table th.col-checkbox,
        .data-table th.col-title,
        .data-table th.col-actions {
            background-color: #f8fafc;
        }

        .data-table tbody tr:hover .col-checkbox,
        .data-table tbody tr:hover .col-title,
        .data-table tbody tr:hover .col-actions {
            background-color: #f8fafc;
        }

        /* 状态标识 */
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }

        .status-approved {
            background-color: #d1fae5;
            color: #065f46;
        }

        .status-rejected {
            background-color: #fee2e2;
            color: #991b1b;
        }

        .status-draft {
            background-color: #f3f4f6;
            color: #374151;
        }

        .status-published {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .status-signing {
            background-color: #fef3c7;
            color: #92400e;
        }

        .status-performing {
            background-color: #dbeafe;
            color: #1e40af;
        }

        .status-completed {
            background-color: #d1fae5;
            color: #065f46;
        }

        /* 操作按钮 */
        .action-btn {
            display: inline-block;
            padding: 4px 6px;
            margin: 0 1px;
            border-radius: 4px;
            font-size: 11px;
            text-decoration: none;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .action-btn-view {
            background-color: #3b82f6;
            color: white;
        }

        .action-btn-view:hover {
            background-color: #2563eb;
        }

        .action-btn-approve {
            background-color: #10b981;
            color: white;
        }

        .action-btn-approve:hover {
            background-color: #059669;
        }

        .action-btn-edit {
            background-color: #f59e0b;
            color: white;
        }

        .action-btn-edit:hover {
            background-color: #d97706;
        }

        .action-btn-delete {
            background-color: #ef4444;
            color: white;
        }

        .action-btn-delete:hover {
            background-color: #dc2626;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            background-color: #f8fafc;
            border-top: 1px solid #e5e7eb;
        }

        .pagination-info {
            color: #6b7280;
            font-size: 13px;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .pagination-btn {
            padding: 6px 12px;
            border: 1px solid #d1d5db;
            background-color: #fff;
            color: #374151;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s ease;
        }

        .pagination-btn:hover {
            background-color: #f3f4f6;
        }

        .pagination-btn.active {
            background-color: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .page-size-selector {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-left: 16px;
        }

        .page-size-selector select {
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 13px;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: #fff;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #e6e8eb;
        }

        .help-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .help-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #6b7280;
        }

        .help-body {
            color: #374151;
            line-height: 1.6;
        }

        .help-body h4 {
            margin: 16px 0 8px 0;
            color: #1f2937;
        }

        .help-body ul {
            margin: 8px 0;
            padding-left: 20px;
        }

        .help-body li {
            margin: 4px 0;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .container {
                max-width: 100%;
                margin: 0;
            }

            .search-form {
                grid-template-columns: repeat(3, 1fr);
            }

            .search-buttons {
                grid-column: span 3;
            }
        }

        @media (max-width: 768px) {
            .search-form {
                grid-template-columns: repeat(2, 1fr);
            }

            .search-buttons {
                grid-column: span 2;
            }

            .action-bar {
                flex-direction: column;
                gap: 12px;
                align-items: stretch;
            }

            .action-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">
                    签约履行管理
                    <span class="help-icon" onclick="showHelp()" title="功能说明">?</span>
                </h1>

            </div>
        </div>

        <!-- 页签导航 -->
        <div class="tab-nav">
            <ul class="tab-list">
                <li class="tab-item">
                    <a href="#" class="tab-link active" onclick="switchTab('pending')">待办</a>
                </li>
                <li class="tab-item">
                    <a href="#" class="tab-link" onclick="switchTab('completed')">已办</a>
                </li>
            </ul>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 待办页签内容 -->
            <div id="pending-tab" class="tab-content active">
                <!-- 查询区域 -->
                <div class="search-section">
                    <div class="search-form">
                        <div class="form-group">
                            <label class="form-label">标题名称</label>
                            <input type="text" class="form-control" placeholder="请输入标题名称">
                        </div>
                        <div class="form-group">
                            <label class="form-label">审核状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="pending">待审核</option>
                                <option value="approved">已通过</option>
                                <option value="rejected">已驳回</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">合同状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="signing">签订中</option>
                                <option value="performing">履行中</option>
                                <option value="completed">执行完毕</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购方式</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="public">公开招标</option>
                                <option value="invite">邀请招标</option>
                                <option value="competitive">竞争性谈判</option>
                                <option value="inquiry">询价采购</option>
                                <option value="single">单一来源</option>
                            </select>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-primary">查询</button>
                            <button class="btn btn-secondary">重置</button>
                            <button class="btn btn-outline" onclick="toggleAdvanced()">高级查询</button>
                        </div>
                    </div>

                        <!-- 高级查询区域 -->
                        <div id="advanced-search" class="advanced-search">
                            <div class="search-form">
                                <div class="form-group">
                                    <label class="form-label">采购类型</label>
                                    <select class="form-control">
                                        <option value="">全部</option>
                                        <option value="goods">货物</option>
                                        <option value="service">服务</option>
                                        <option value="engineering">工程</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">采购组织方式</label>
                                    <select class="form-control">
                                        <option value="">全部</option>
                                        <option value="self">自行采购</option>
                                        <option value="agent">委托代理</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">资金来源</label>
                                    <select class="form-control">
                                        <option value="">全部</option>
                                        <option value="fiscal">财政资金</option>
                                        <option value="self">自有资金</option>
                                        <option value="loan">银行贷款</option>
                                        <option value="other">其他</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">所属标段</label>
                                    <select class="form-control">
                                        <option value="">全部</option>
                                        <option value="section1">标段一</option>
                                        <option value="section2">标段二</option>
                                        <option value="section3">标段三</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">所属项目</label>
                                    <input type="text" class="form-control" placeholder="请输入项目名称">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">招标金额（万元）</label>
                                    <input type="number" class="form-control" placeholder="请输入招标金额">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">申请人</label>
                                    <input type="text" class="form-control" placeholder="请输入申请人">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">招标时间</label>
                                    <input type="date" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">计划时间</label>
                                    <input type="date" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">创建时间</label>
                                    <input type="date" class="form-control">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能操作栏 -->
                <div class="action-bar">
                    <div class="action-buttons">
                        <button class="btn btn-success" onclick="createAnnouncement()">新建签约履行</button>
                        <button class="btn btn-warning" onclick="batchApprove()">批量审批</button>
                        <button class="btn btn-danger" onclick="batchDelete()">批量删除</button>
                    </div>
                </div>

                <!-- 数据列表 -->
                <div class="table-container">
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th class="col-checkbox">
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th class="col-title">签约履行标题</th>
                                    <th style="width: 100px;">审核状态</th>
                                    <th style="width: 100px;">合同状态</th>
                                    <th style="width: 100px;">采购方式</th>
                                    <th style="width: 100px;">采购类型</th>
                                    <th style="width: 120px;">关联标段</th>
                                    <th style="width: 150px;">关联项目</th>
                                    <th style="width: 120px;">创建时间</th>
                                    <th class="col-actions">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="col-checkbox">
                                        <input type="checkbox" name="rowSelect" value="1">
                                    </td>
                                    <td class="col-title">
                                        <a href="#" onclick="viewDetail(1)" style="color: #3b82f6; text-decoration: none;">办公设备采购项目签约履行</a>
                                    </td>
                                    <td><span class="status-badge status-pending">待审核</span></td>
                                    <td><span class="status-badge status-signing">签订中</span></td>
                                    <td>公开招标</td>
                                    <td>货物</td>
                                    <td>标段一</td>
                                    <td>办公设备采购项目</td>
                                    <td>2024-01-15</td>
                                    <td class="col-actions">
                                        <a href="#" class="action-btn action-btn-approve" onclick="approveAnnouncement(1)">审批</a>
                                        <a href="#" class="action-btn action-btn-edit" onclick="editAnnouncement(1)">编辑</a>
                                        <a href="#" class="action-btn action-btn-delete" onclick="deleteAnnouncement(1)">删除</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-checkbox">
                                        <input type="checkbox" name="rowSelect" value="2">
                                    </td>
                                    <td class="col-title">
                                        <a href="#" onclick="viewDetail(2)" style="color: #3b82f6; text-decoration: none;">IT服务外包项目签约履行</a>
                                    </td>
                                    <td><span class="status-badge status-approved">已通过</span></td>
                                    <td><span class="status-badge status-performing">履行中</span></td>
                                    <td>邀请招标</td>
                                    <td>服务</td>
                                    <td>标段二</td>
                                    <td>IT服务外包项目</td>
                                    <td>2024-01-14</td>
                                    <td class="col-actions">
                                        <a href="#" class="action-btn action-btn-change" onclick="changeAnnouncement(2)">变更</a>
                                        <a href="#" class="action-btn action-btn-edit" onclick="editAnnouncement(2)">编辑</a>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-checkbox">
                                        <input type="checkbox" name="rowSelect" value="3">
                                    </td>
                                    <td class="col-title">
                                        <a href="#" onclick="viewDetail(3)" style="color: #3b82f6; text-decoration: none;">基础设施建设项目签约履行</a>
                                    </td>
                                    <td><span class="status-badge status-rejected">已驳回</span></td>
                                    <td><span class="status-badge status-completed">执行完毕</span></td>
                                    <td>公开招标</td>
                                    <td>工程</td>
                                    <td>标段三</td>
                                    <td>基础设施建设项目</td>
                                    <td>2024-01-13</td>
                                    <td class="col-actions">
                                        <a href="#" class="action-btn action-btn-edit" onclick="editAnnouncement(3)">编辑</a>
                                        <a href="#" class="action-btn action-btn-delete" onclick="deleteAnnouncement(3)">删除</a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="pagination">
                        <div class="pagination-info">
                            显示第 1-10 条，共 25 条记录
                        </div>
                        <div class="pagination-controls">
                            <button class="pagination-btn" disabled>上一页</button>
                            <button class="pagination-btn active">1</button>
                            <button class="pagination-btn">2</button>
                            <button class="pagination-btn">3</button>
                            <button class="pagination-btn">下一页</button>
                            <div class="page-size-selector">
                                <span>每页显示</span>
                                <select onchange="changePageSize(this.value)">
                                    <option value="10">10条</option>
                                    <option value="20">20条</option>
                                    <option value="50">50条</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 已办页签内容 -->
            <div id="completed-tab" class="tab-content">
                <div class="search-section">
                    <div class="search-form">
                        <div class="form-group">
                            <label class="form-label">签约履行标题</label>
                            <input type="text" class="form-control" placeholder="请输入签约履行标题">
                        </div>
                        <div class="form-group">
                            <label class="form-label">履行状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="completed">已完成</option>
                                <option value="terminated">已终止</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购方式</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="public">公开招标</option>
                                <option value="invite">邀请招标</option>
                                <option value="competitive">竞争性谈判</option>
                                <option value="inquiry">询价采购</option>
                                <option value="single">单一来源</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购类型</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="goods">货物</option>
                                <option value="service">服务</option>
                                <option value="engineering">工程</option>
                            </select>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-primary">查询</button>
                            <button class="btn btn-secondary">重置</button>
                        </div>
                    </div>
                </div>

                <!-- 数据列表 -->
                <div class="table-container">
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th class="col-checkbox">
                                        <input type="checkbox" id="selectAllCompleted" onchange="toggleSelectAll()">
                                    </th>
                                    <th class="col-title">签约履行标题</th>
                                    <th style="width: 120px;">项目编号</th>
                                    <th style="width: 100px;">采购方式</th>
                                    <th style="width: 100px;">采购类型</th>
                                    <th style="width: 120px;">中标供应商</th>
                                    <th style="width: 120px;">完成时间</th>
                                    <th style="width: 120px;">创建人</th>
                                    <th class="col-actions">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="col-checkbox">
                                        <input type="checkbox" name="rowSelect" value="4">
                                    </td>
                                    <td class="col-title">
                                        <a href="#" onclick="viewDetail(4)" style="color: #3b82f6; text-decoration: none;">网络设备采购项目签约履行</a>
                                    </td>
                                    <td>ZB2023015</td>
                                    <td>公开招标</td>
                                    <td>货物</td>
                                    <td>某某网络科技公司</td>
                                    <td>2024-01-10</td>
                                    <td>赵六</td>
                                    <td class="col-actions">
                                        <!-- 无可用操作 -->
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="pagination">
                        <div class="pagination-info">
                            显示第 1-1 条，共 1 条记录
                        </div>
                        <div class="pagination-controls">
                            <button class="pagination-btn" disabled>上一页</button>
                            <button class="pagination-btn active">1</button>
                            <button class="pagination-btn" disabled>下一页</button>
                            <div class="page-size-selector">
                                <span>每页显示</span>
                                <select onchange="changePageSize(this.value)">
                                    <option value="10">10条</option>
                                    <option value="20">20条</option>
                                    <option value="50">50条</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="helpModal" class="help-modal">
        <div class="help-content">
            <div class="help-header">
                <h3 class="help-title">签约履行管理功能说明</h3>
                <button class="help-close" onclick="hideHelp()">&times;</button>
            </div>
            <div class="help-body" style="padding: 20px;">
                <h4>功能概述</h4>
                <p>签约履行管理模块用于管理项目的合同签约和履行过程，包括合同签订、履行监督、验收确认等环节。</p>
                
                <h4>主要功能</h4>
                <ul>
                    <li><strong>签约履行创建：</strong>创建新的签约履行记录，录入合同基本信息</li>
                    <li><strong>履行监督：</strong>跟踪合同履行进度，记录履行过程中的关键节点</li>
                    <li><strong>状态管理：</strong>管理履行状态（履行中、已完成、暂停等）</li>
                    <li><strong>审核流程：</strong>支持多级审核，确保履行过程合规</li>
                    <li><strong>验收管理：</strong>记录验收结果，确认履行完成情况</li>
                </ul>
                
                <h4>操作说明</h4>
                <ul>
                    <li><strong>待办页签：</strong>显示需要处理的签约履行事项</li>
                    <li><strong>已办页签：</strong>显示已完成处理的签约履行事项</li>
                    <li><strong>查询功能：</strong>支持按标题、状态、采购方式等条件查询</li>
                    <li><strong>批量操作：</strong>支持批量审批、批量删除等操作</li>
                </ul>
                
                <h4>状态说明</h4>
                <ul>
                    <li><strong>履行中：</strong>合同正在履行过程中</li>
                    <li><strong>已完成：</strong>合同履行已完成并通过验收</li>
                    <li><strong>暂停：</strong>合同履行暂时停止</li>
                    <li><strong>已终止：</strong>合同履行已终止</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 页签切换
        function switchTab(tabName) {
            // 隐藏所有页签内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有页签的激活状态
            const tabLinks = document.querySelectorAll('.tab-link');
            tabLinks.forEach(link => {
                link.classList.remove('active');
            });
            
            // 显示选中的页签内容
            const targetTab = document.getElementById(tabName + '-tab');
            if (targetTab) {
                targetTab.classList.add('active');
            }
            
            // 激活选中的页签
            event.target.classList.add('active');
        }

        // 高级查询切换
        function toggleAdvanced() {
            const advancedSearch = document.getElementById('advanced-search');
            const button = event.target;
            
            if (advancedSearch.classList.contains('show')) {
                advancedSearch.classList.remove('show');
                button.textContent = '高级查询';
            } else {
                advancedSearch.classList.add('show');
                button.textContent = '收起查询';
            }
        }

        // 全选功能
        function toggleSelectAll() {
            const selectAll = event.target;
            const checkboxes = document.querySelectorAll('input[name="rowSelect"]');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }

        // 重置查询
        function resetSearch() {
            const form = event.target.closest('form');
            form.reset();
        }

        // 创建签约履行
        function createAnnouncement() {
            window.location.href = '签约履行管理-新建编辑页.html';
        }

        // 查看详情
        function viewDetail(id) {
            window.location.href = `签约履行管理-详情页.html?id=${id}`;
        }

        // 审批
        function approveAnnouncement(id) {
            window.location.href = `签约履行管理-审核页.html?id=${id}`;
        }

        // 编辑
        function editAnnouncement(id) {
            window.location.href = `签约履行管理-新建编辑页.html?id=${id}&mode=edit`;
        }

        // 删除
        function deleteAnnouncement(id) {
            if (confirm('确定要删除这条签约履行记录吗？')) {
                alert('删除签约履行：' + id);
            }
        }

        // 批量审批
        function batchApprove() {
            const selected = document.querySelectorAll('input[name="rowSelect"]:checked');
            if (selected.length === 0) {
                alert('请选择要审批的记录');
                return;
            }
            alert('批量审批 ' + selected.length + ' 条记录');
        }

        // 批量删除
        function batchDelete() {
            const selected = document.querySelectorAll('input[name="rowSelect"]:checked');
            if (selected.length === 0) {
                alert('请选择要删除的记录');
                return;
            }
            if (confirm('确定要删除选中的 ' + selected.length + ' 条记录吗？')) {
                alert('批量删除 ' + selected.length + ' 条记录');
            }
        }

        // 导出数据
        function exportData() {
            alert('导出数据功能');
        }

        // 改变每页显示数量
        function changePageSize(size) {
            alert('改变每页显示数量为：' + size);
        }

        // 查询数据
        function searchData() {
            alert('执行查询操作');
        }

        // 显示帮助
        function showHelp() {
            document.getElementById('helpModal').style.display = 'block';
        }

        // 隐藏帮助
        function hideHelp() {
            document.getElementById('helpModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('helpModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('签约履行管理页面加载完成');
            // 确保默认显示待办页签
            switchTab('pending');
        });
    </script>
</body>
</html>