<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理机构详情 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 页面头部 */
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .help-icon {
            cursor: pointer;
            color: #3498db;
            font-size: 18px;
            padding: 4px;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .help-icon:hover {
            background-color: #e3f2fd;
        }

        .breadcrumb {
            color: #7f8c8d;
            font-size: 14px;
        }

        .breadcrumb a {
            color: #3498db;
            text-decoration: none;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .back-btn {
            background: #95a5a6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .back-btn:hover {
            background: #7f8c8d;
        }

        /* 页签导航 */
        .tab-navigation {
            background: white;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 0;
        }

        .tab-nav {
            display: flex;
            border-bottom: 1px solid #dee2e6;
        }

        .tab-item {
            padding: 15px 25px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
            font-weight: 500;
            color: #6c757d;
        }

        .tab-item.active {
            color: #3498db;
            border-bottom-color: #3498db;
            background: #f8f9fa;
        }

        .tab-item:hover {
            color: #3498db;
            background: #f8f9fa;
        }

        /* 页签内容 */
        .tab-content {
            background: white;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            min-height: 500px;
        }

        .tab-pane {
            display: none;
            padding: 20px;
        }

        .tab-pane.active {
            display: block;
        }

        /* 详情区域 */
        .detail-section {
            margin-bottom: 30px;
        }

        .section-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-radius: 8px 8px 0 0;
            border-bottom: 1px solid #dee2e6;
            margin: -20px -20px 20px -20px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
            position: relative;
            padding-left: 15px;
        }

        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 16px;
            background: #3498db;
            border-radius: 2px;
        }

        /* 详情网格 */
        .detail-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-item.full-width {
            grid-column: 1 / -1;
        }

        .detail-label {
            font-size: 13px;
            color: #555;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .detail-value {
            font-size: 14px;
            color: #2c3e50;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            min-height: 32px;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
        }

        .stat-card:nth-child(2) {
            background: linear-gradient(135deg, #27ae60, #229954);
            box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
        }

        .stat-card:nth-child(3) {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
        }

        .stat-card:nth-child(4) {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            box-shadow: 0 4px 8px rgba(155, 89, 182, 0.3);
        }

        .stat-card:nth-child(5) {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }

        /* 标段列表 */
        .segment-list {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }

        .list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .list-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }

        .list-count {
            color: #7f8c8d;
            font-size: 14px;
        }

        /* 数据表格 */
        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }

        .data-table th,
        .data-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #eee;
            font-size: 13px;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .data-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        /* 列宽设置 */
        .col-name { width: 200px; }
        .col-stage { width: 120px; }
        .col-status { width: 100px; }
        .col-project { width: 180px; }
        .col-type { width: 100px; }
        .col-method { width: 100px; }
        .col-time { width: 140px; }

        /* 状态标签 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-planning {
            background: #e3f2fd;
            color: #1976d2;
        }

        .status-bidding {
            background: #fff3e0;
            color: #f57c00;
        }

        .status-evaluation {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .status-completed {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .status-terminated {
            background: #ffebee;
            color: #c62828;
        }

        /* 阶段标签 */
        .stage-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            background: #f0f0f0;
            color: #666;
        }

        /* 操作记录 */
        .operation-timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline-item {
            position: relative;
            padding-bottom: 20px;
            border-left: 2px solid #e9ecef;
            padding-left: 20px;
            margin-left: 10px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -6px;
            top: 0;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #3498db;
        }

        .timeline-item:last-child {
            border-left: none;
        }

        .timeline-content {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .timeline-action {
            font-weight: 600;
            color: #2c3e50;
        }

        .timeline-time {
            color: #7f8c8d;
            font-size: 12px;
        }

        .timeline-user {
            color: #3498db;
            font-size: 13px;
        }

        .timeline-desc {
            color: #666;
            font-size: 13px;
            line-height: 1.5;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .help-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .help-close {
            position: absolute;
            right: 15px;
            top: 15px;
            font-size: 24px;
            cursor: pointer;
            color: #aaa;
        }

        .help-close:hover {
            color: #333;
        }

        .help-title {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 20px;
        }

        .help-content h4 {
            color: #34495e;
            margin: 15px 0 10px 0;
            font-size: 16px;
        }

        .help-content ul {
            margin-left: 20px;
            margin-bottom: 15px;
        }

        .help-content li {
            margin-bottom: 8px;
            line-height: 1.6;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .detail-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .stats-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .detail-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .tab-nav {
                flex-wrap: wrap;
            }

            .tab-item {
                flex: 1;
                text-align: center;
                min-width: 120px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">
                    代理机构详情
                    <span class="help-icon" onclick="showHelp()" title="查看帮助">❓</span>
                </h1>
                <div class="breadcrumb">
                    <a href="#">首页</a> > <a href="#">系统管理</a> > <a href="代理机构管理-列表页.html">代理机构管理</a> > 代理机构详情
                </div>
            </div>
            <button class="back-btn" onclick="goBack()">
                ← 返回列表
            </button>
        </div>

        <!-- 页签导航 -->
        <div class="tab-navigation">
            <div class="tab-nav">
                <div class="tab-item active" onclick="switchTab('basic')">
                    基本信息
                </div>
                <div class="tab-item" onclick="switchTab('project')">
                    项目总览
                </div>
                <div class="tab-item" onclick="switchTab('operation')">
                    操作记录
                </div>
            </div>
        </div>

        <!-- 页签内容 -->
        <div class="tab-content">
            <!-- 基本信息页签 -->
            <div class="tab-pane active" id="basic">
                <div class="detail-section">
                    <div class="section-header">
                        <h2 class="section-title">基本信息</h2>
                    </div>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">代理机构名称</div>
                            <div class="detail-value">中建工程咨询有限公司</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">企业代码</div>
                            <div class="detail-value">91110000123456789X</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">联系人</div>
                            <div class="detail-value">张经理</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">联系方式</div>
                            <div class="detail-value">13800138001</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">更新时间</div>
                            <div class="detail-value">2024-01-15 14:30:25</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">创建时间</div>
                            <div class="detail-value">2023-06-20 09:15:30</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 项目总览页签 -->
            <div class="tab-pane" id="project">
                <!-- 统计概览 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h2 class="section-title">项目统计</h2>
                    </div>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">28</div>
                            <div class="stat-label">项目总数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">15</div>
                            <div class="stat-label">已完成项目数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">42</div>
                            <div class="stat-label">已成交标段数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">8</div>
                            <div class="stat-label">进行中标段数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">3</div>
                            <div class="stat-label">中止或流标标段数</div>
                        </div>
                    </div>
                </div>

                <!-- 标段列表 -->
                <div class="detail-section">
                    <div class="segment-list">
                        <div class="list-header">
                            <h3 class="list-title">标段列表</h3>
                            <span class="list-count">共 53 个标段</span>
                        </div>
                        
                        <!-- 查询区域 -->
                        <div class="search-section" style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px;">
                            <div class="search-form" style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
                                <div class="form-group" style="display: flex; flex-direction: column; min-width: 200px;">
                                    <label class="form-label" style="font-size: 13px; color: #555; margin-bottom: 5px;">标段名称</label>
                                    <input type="text" class="form-control" placeholder="请输入标段名称" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px;">
                                </div>
                                <div class="form-group" style="display: flex; flex-direction: column; min-width: 150px;">
                                    <label class="form-label" style="font-size: 13px; color: #555; margin-bottom: 5px;">标段阶段</label>
                                    <select class="form-control" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px;">
                                        <option value="">全部阶段</option>
                                        <option value="准备阶段">准备阶段</option>
                                        <option value="招标阶段">招标阶段</option>
                                        <option value="评标阶段">评标阶段</option>
                                        <option value="完成阶段">完成阶段</option>
                                        <option value="终止阶段">终止阶段</option>
                                    </select>
                                </div>
                                <div class="form-group" style="display: flex; flex-direction: column; min-width: 200px;">
                                    <label class="form-label" style="font-size: 13px; color: #555; margin-bottom: 5px;">所属项目</label>
                                    <input type="text" class="form-control" placeholder="请输入项目名称" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px;">
                                </div>
                                <div class="btn-group" style="display: flex; gap: 10px;">
                                    <button class="btn btn-primary" style="padding: 8px 16px; background: #3498db; color: white; border: none; border-radius: 4px; font-size: 13px; cursor: pointer;">查询</button>
                                    <button class="btn btn-secondary" style="padding: 8px 16px; background: #95a5a6; color: white; border: none; border-radius: 4px; font-size: 13px; cursor: pointer;">重置</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th class="col-name">标段名称</th>
                                        <th class="col-stage">标段阶段</th>
                                        <th class="col-status">标段状态</th>
                                        <th class="col-project">所属项目</th>
                                        <th class="col-type">采购类型</th>
                                        <th class="col-method">采购方式</th>
                                        <th class="col-time">更新时间</th>
                                        <th class="col-time">创建时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>XX市政道路改造工程-施工标段</td>
                                        <td><span class="stage-badge">评标阶段</span></td>
                                        <td><span class="status-badge status-evaluation">评标中</span></td>
                                        <td>XX市政道路改造工程</td>
                                        <td>工程</td>
                                        <td>公开招标</td>
                                        <td>2024-01-15 14:30</td>
                                        <td>2023-12-01 09:00</td>
                                    </tr>
                                    <tr>
                                        <td>教学楼建设项目-土建标段</td>
                                        <td><span class="stage-badge">招标阶段</span></td>
                                        <td><span class="status-badge status-bidding">招标中</span></td>
                                        <td>教学楼建设项目</td>
                                        <td>工程</td>
                                        <td>公开招标</td>
                                        <td>2024-01-14 16:45</td>
                                        <td>2023-11-20 10:30</td>
                                    </tr>
                                    <tr>
                                        <td>办公设备采购-计算机设备</td>
                                        <td><span class="stage-badge">完成阶段</span></td>
                                        <td><span class="status-badge status-completed">已完成</span></td>
                                        <td>办公设备采购</td>
                                        <td>货物</td>
                                        <td>询价</td>
                                        <td>2024-01-13 11:20</td>
                                        <td>2023-11-15 14:15</td>
                                    </tr>
                                    <tr>
                                        <td>物业管理服务-保洁服务</td>
                                        <td><span class="stage-badge">准备阶段</span></td>
                                        <td><span class="status-badge status-planning">准备中</span></td>
                                        <td>物业管理服务</td>
                                        <td>服务</td>
                                        <td>竞争性谈判</td>
                                        <td>2024-01-12 09:15</td>
                                        <td>2023-11-10 16:20</td>
                                    </tr>
                                    <tr>
                                        <td>绿化工程-景观设计</td>
                                        <td><span class="stage-badge">终止阶段</span></td>
                                        <td><span class="status-badge status-terminated">已终止</span></td>
                                        <td>绿化工程</td>
                                        <td>工程</td>
                                        <td>公开招标</td>
                                        <td>2024-01-11 15:40</td>
                                        <td>2023-11-05 11:45</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        <div class="pagination-wrapper" style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; padding: 15px 0; border-top: 1px solid #dee2e6;">
                            <div class="pagination-info" style="color: #6c757d; font-size: 13px;">
                                显示第 1-20 条，共 53 条记录
                            </div>
                            <div class="pagination" style="display: flex; gap: 5px;">
                                <button class="page-btn" style="padding: 6px 12px; border: 1px solid #dee2e6; background: white; color: #6c757d; border-radius: 4px; cursor: pointer; font-size: 13px;">上一页</button>
                                <button class="page-btn active" style="padding: 6px 12px; border: 1px solid #3498db; background: #3498db; color: white; border-radius: 4px; cursor: pointer; font-size: 13px;">1</button>
                                <button class="page-btn" style="padding: 6px 12px; border: 1px solid #dee2e6; background: white; color: #6c757d; border-radius: 4px; cursor: pointer; font-size: 13px;">2</button>
                                <button class="page-btn" style="padding: 6px 12px; border: 1px solid #dee2e6; background: white; color: #6c757d; border-radius: 4px; cursor: pointer; font-size: 13px;">3</button>
                                <button class="page-btn" style="padding: 6px 12px; border: 1px solid #dee2e6; background: white; color: #6c757d; border-radius: 4px; cursor: pointer; font-size: 13px;">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作记录页签 -->
            <div class="tab-pane" id="operation">
                <div class="detail-section">
                    <div class="section-header">
                        <h2 class="section-title">操作记录</h2>
                    </div>
                    <div class="operation-timeline">
                        <div class="timeline-item">
                            <div class="timeline-content">
                                <div class="timeline-header">
                                    <span class="timeline-action">修改代理机构信息</span>
                                    <span class="timeline-time">2024-01-15 14:30:25</span>
                                </div>
                                <div class="timeline-user">操作人：系统管理员</div>
                                <div class="timeline-desc">更新了联系方式信息，将联系电话从13800138000修改为13800138001</div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-content">
                                <div class="timeline-header">
                                    <span class="timeline-action">新增项目关联</span>
                                    <span class="timeline-time">2024-01-10 09:15:30</span>
                                </div>
                                <div class="timeline-user">操作人：张三</div>
                                <div class="timeline-desc">关联了新项目"XX市政道路改造工程"，作为招标代理机构</div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-content">
                                <div class="timeline-header">
                                    <span class="timeline-action">完成项目验收</span>
                                    <span class="timeline-time">2024-01-05 16:45:20</span>
                                </div>
                                <div class="timeline-user">操作人：李四</div>
                                <div class="timeline-desc">完成了"办公设备采购"项目的最终验收工作</div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-content">
                                <div class="timeline-header">
                                    <span class="timeline-action">修改联系人信息</span>
                                    <span class="timeline-time">2023-12-20 11:20:15</span>
                                </div>
                                <div class="timeline-user">操作人：王五</div>
                                <div class="timeline-desc">更新了主要联系人信息，联系人从"李经理"变更为"张经理"</div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-content">
                                <div class="timeline-header">
                                    <span class="timeline-action">创建代理机构</span>
                                    <span class="timeline-time">2023-06-20 09:15:30</span>
                                </div>
                                <div class="timeline-user">操作人：系统管理员</div>
                                <div class="timeline-desc">在系统中创建了"中建工程咨询有限公司"的代理机构档案</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="helpModal" class="help-modal">
        <div class="help-content">
            <span class="help-close" onclick="hideHelp()">&times;</span>
            <h3 class="help-title">代理机构详情说明</h3>
            <p>代理机构详情页面展示了招标代理机构的完整信息和项目统计。</p>
            
            <h4>基本信息</h4>
            <ul>
                <li><strong>代理机构名称：</strong>招标代理机构的完整名称</li>
                <li><strong>企业代码：</strong>统一社会信用代码或组织机构代码</li>
                <li><strong>联系人：</strong>机构主要联系人姓名</li>
                <li><strong>联系方式：</strong>联系电话或邮箱地址</li>
                <li><strong>时间信息：</strong>记录创建和最后更新时间</li>
            </ul>
            
            <h4>项目总览</h4>
            <ul>
                <li><strong>项目统计：</strong>展示该代理机构承接的项目数量统计</li>
                <li><strong>标段列表：</strong>显示所有相关标段的详细信息</li>
                <li><strong>状态分类：</strong>按照不同阶段和状态进行分类展示</li>
            </ul>
            
            <h4>操作记录</h4>
            <ul>
                <li><strong>时间线：</strong>按时间顺序展示所有操作记录</li>
                <li><strong>操作详情：</strong>记录具体的操作内容和操作人</li>
                <li><strong>变更追踪：</strong>跟踪重要信息的变更历史</li>
            </ul>
        </div>
    </div>

    <script>
        // 页签切换
        function switchTab(tabName) {
            // 移除所有活动状态
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.remove('active');
            });
            
            // 激活当前页签
            event.target.classList.add('active');
            document.getElementById(tabName).classList.add('active');
        }

        // 返回列表页
        function goBack() {
            window.location.href = '代理机构管理-列表页.html';
        }

        // 帮助弹窗
        function showHelp() {
            document.getElementById('helpModal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('helpModal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('helpModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 可以在这里加载具体的代理机构数据
            const urlParams = new URLSearchParams(window.location.search);
            const agencyId = urlParams.get('id');
            
            if (agencyId) {
                // 根据ID加载具体数据
                console.log('加载代理机构ID:', agencyId);
            }
        });
    </script>
</body>
</html>