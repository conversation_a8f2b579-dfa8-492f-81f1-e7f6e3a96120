<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公告管理 - 详情 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #fff;
            border-bottom: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
        }

        .help-icon {
            margin-left: 8px;
            width: 16px;
            height: 16px;
            background: #6b7280;
            border-radius: 50%;
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .breadcrumb {
            color: #6b7280;
            font-size: 14px;
        }

        .back-btn {
            background: #6b7280;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
        }

        .back-btn:hover {
            background: #4b5563;
        }

        /* 内容区域 */
        .content-area {
            padding: 24px;
        }

        /* 页签导航 */
        .tab-nav {
            border-bottom: 1px solid #e6e8eb;
            margin-bottom: 24px;
        }

        .tab-list {
            display: flex;
            list-style: none;
        }

        .tab-item {
            margin-right: 32px;
        }

        .tab-link {
            display: block;
            padding: 12px 0;
            color: #6b7280;
            text-decoration: none;
            font-size: 14px;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab-link.active {
            color: #2563eb;
            border-bottom-color: #2563eb;
        }

        .tab-link:hover {
            color: #2563eb;
        }

        /* 页签内容 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 详情区域 */
        .detail-section {
            background: white;
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .section-header {
            background: #f8fafc;
            border-bottom: 1px solid #e6e8eb;
            padding: 15px 20px;
            font-weight: 600;
            color: #1f2937;
            font-size: 14px;
            position: relative;
        }

        .section-header::after {
            content: '';
            position: absolute;
            left: 20px;
            bottom: 0;
            width: 60px;
            height: 2px;
            background: #2563eb;
        }

        .section-content {
            padding: 20px;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .detail-grid.full-width {
            grid-template-columns: 1fr;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-size: 13px;
            color: #6b7280;
            margin-bottom: 6px;
            font-weight: 500;
        }

        .detail-value {
            font-size: 13px;
            color: #1f2937;
            min-height: 20px;
            padding: 4px 0;
        }

        .detail-value.rich-content {
            border: 1px solid #e6e8eb;
            border-radius: 4px;
            padding: 12px;
            background: #f9fafb;
            min-height: 60px;
        }

        .detail-value.rich-content p {
            margin-bottom: 8px;
        }

        .detail-value.rich-content ul, .detail-value.rich-content ol {
            margin-left: 20px;
            margin-bottom: 8px;
        }

        .detail-value.rich-content table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 8px;
        }

        .detail-value.rich-content table td, .detail-value.rich-content table th {
            border: 1px solid #d1d5db;
            padding: 8px;
            text-align: left;
        }

        .detail-value.rich-content table th {
            background: #f3f4f6;
            font-weight: 600;
        }

        /* 状态标签 */
        .status-tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
        }

        .status-published {
            background: #dcfce7;
            color: #166534;
        }

        .status-approved {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-draft {
            background: #f3f4f6;
            color: #374151;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        /* 状态徽章 */
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
        }

        .status-badge.status-approved {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-badge.status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        /* 分组列表 */
        .section-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .section-item {
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            overflow: hidden;
        }

        .section-item-header {
            background: #f8fafc;
            padding: 12px 16px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e6e8eb;
        }

        .section-item-content {
            padding: 16px;
        }

        /* 文件列表 */
        .file-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: #f8fafc;
            border-radius: 4px;
            border: 1px solid #e6e8eb;
        }

        .file-name {
            color: #374151;
            font-size: 13px;
        }

        .file-download {
            color: #2563eb;
            text-decoration: none;
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 3px;
            transition: all 0.2s;
        }

        .file-download:hover {
            background: #dbeafe;
            text-decoration: none;
        }

        /* 链接文本样式 */
        .link-text {
            color: #007bff;
            text-decoration: none;
            cursor: pointer;
        }

        .link-text:hover {
            color: #0056b3;
            text-decoration: underline;
        }

        /* 操作记录时间线样式 */
        .operation-timeline {
            position: relative;
            padding-left: 20px;
        }

        .operation-timeline::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
            padding-left: 25px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -4px;
            top: 8px;
            width: 8px;
            height: 8px;
            background: #007bff;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #e9ecef;
        }

        .timeline-content {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px 15px;
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .timeline-action {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .timeline-time {
            color: #6c757d;
            font-size: 12px;
        }

        .timeline-user {
            color: #6c757d;
            font-size: 12px;
            margin-bottom: 6px;
        }

        .timeline-desc {
            color: #495057;
            font-size: 13px;
            line-height: 1.4;
        }

        /* 操作记录表格 */
        .record-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .record-table th {
            background: #f8fafc;
            border: 1px solid #e6e8eb;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            color: #374151;
        }

        .record-table td {
            border: 1px solid #e6e8eb;
            padding: 12px 8px;
            color: #1f2937;
        }

        .record-table tr:nth-child(even) {
            background: #f9fafb;
        }

        .record-table tr:hover {
            background: #f3f4f6;
        }

        /* 操作按钮 */
        .action-bar {
            background: #f8fafc;
            border-top: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            bottom: 0;
        }

        .btn-group {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #2563eb;
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
        }

        .btn-success {
            background: #059669;
            color: white;
        }

        .btn-success:hover {
            background: #047857;
        }

        .btn-warning {
            background: #d97706;
            color: white;
        }

        .btn-warning:hover {
            background: #b45309;
        }

        .btn-danger {
            background: #dc2626;
            color: white;
        }

        .btn-danger:hover {
            background: #b91c1c;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-outline {
            background: white;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            padding: 24px;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e6e8eb;
        }

        .help-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #6b7280;
        }

        .help-body {
            color: #374151;
            line-height: 1.6;
        }

        .help-section {
            margin-bottom: 16px;
        }

        .help-section h4 {
            color: #1f2937;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .help-section p {
            margin-bottom: 8px;
            font-size: 13px;
        }

        .help-list {
            list-style: none;
            padding-left: 16px;
        }

        .help-list li {
            margin-bottom: 4px;
            font-size: 13px;
            position: relative;
        }

        .help-list li::before {
            content: '•';
            color: #2563eb;
            position: absolute;
            left: -12px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .detail-grid {
                grid-template-columns: 1fr;
            }
            
            .action-bar {
                flex-direction: column;
                gap: 12px;
            }
            
            .btn-group {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="page-title">
                公告管理 - 详情
                <span class="help-icon" onclick="showHelp()" title="功能说明">?</span>
            </div>
            <div style="display: flex; align-items: center; gap: 16px;">
                <div class="breadcrumb">
                    首页 > 招标管理 > 公告管理 > 公告详情
                </div>
                <button class="back-btn" onclick="goBack()">返回列表</button>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 页签导航 -->
            <div class="tab-nav">
                <ul class="tab-list">
                    <li class="tab-item">
                        <a href="#" class="tab-link active" onclick="switchTab('announcement')">公告信息</a>
                    </li>
                    <li class="tab-item">
                        <a href="#" class="tab-link" onclick="switchTab('project')">项目信息</a>
                    </li>
                    <li class="tab-item">
                        <a href="#" class="tab-link" onclick="switchTab('records')">流程记录</a>
                    </li>
                </ul>
            </div>

            <!-- 公告信息页签 -->
            <div id="announcement-tab" class="tab-content active">
                <!-- 公告信息 -->
                <div class="detail-section">
                    <div class="section-header">公告信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">公告标题</div>
                                <div class="detail-value">XX有限公司办公桌椅采购标段招标公告</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">公告阶段</div>
                                <div class="detail-value">招标公告</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">公告状态</div>
                                <div class="detail-value">
                                    <span class="status-tag status-published">已发布</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">公告开始时间</div>
                                <div class="detail-value">2024-01-15 09:00:00</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">澄清截止时间</div>
                                <div class="detail-value">2024-01-20 17:00:00</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">开标时间</div>
                                <div class="detail-value">2024-01-25 14:30:00</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">评审办法</div>
                                <div class="detail-value">综合评估法</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">公告内容</div>
                                <div class="detail-value rich-content">
                                    <p><strong>一、项目基本情况</strong></p>
                                    <p>项目编号：XM-2024-003</p>
                                    <p>项目名称：办公桌椅采购项目</p>
                                    <p>预算金额：25万元</p>
                                    <p>采购需求：采购办公桌椅，包括办公桌、办公椅、会议桌椅等，满足办公需求。</p>
                                    
                                    <p><strong>二、申请人的资格要求</strong></p>
                                    <ul>
                                        <li>具有独立承担民事责任的能力</li>
                                        <li>具有良好的商业信誉和健全的财务会计制度</li>
                                        <li>具有履行合同所必需的设备和专业技术能力</li>
                                        <li>有依法缴纳税收和社会保障资金的良好记录</li>
                                        <li>参加政府采购活动前三年内，在经营活动中没有重大违法记录</li>
                                    </ul>
                                    
                                    <p><strong>三、获取招标文件</strong></p>
                                    <p>时间：2024年1月15日至2024年1月22日（法定公休日、法定节假日除外），每日上午9:00-12:00，下午14:00-17:00</p>
                                    <p>地点：北京市朝阳区XX路XX号XX大厦10层1001室</p>
                                    <p>方式：现场获取或邮寄获取</p>
                                    <p>售价：500元/套，售后不退</p>
                                    
                                    <p><strong>四、提交投标文件截止时间、开标时间和地点</strong></p>
                                    <p>提交投标文件截止时间：2024年1月25日14时00分</p>
                                    <p>开标时间：2024年1月25日14时30分</p>
                                    <p>地点：北京市朝阳区XX路XX号XX大厦会议室A</p>
                                    
                                    <p><strong>五、联系方式</strong></p>
                                    <p>采购人：XX有限公司</p>
                                    <p>地址：北京市朝阳区XX路XX号</p>
                                    <p>联系人：张三</p>
                                    <p>电话：010-12345678</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">备注</div>
                                <div class="detail-value">要求环保材质，符合国家相关标准。投标人需具备相应的生产或供应能力。</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">审核依据</div>
                                <div class="detail-value">
                                    <div class="file-list">
                                        <div class="file-item">
                                            <span class="file-name">立项决策文件.pdf</span>
                                            <a href="#" class="file-download">下载</a>
                                        </div>
                                        <div class="file-item">
                                            <span class="file-name">采购需求书.docx</span>
                                            <a href="#" class="file-download">下载</a>
                                        </div>
                                        <div class="file-item">
                                            <span class="file-name">预算批复文件.pdf</span>
                                            <a href="#" class="file-download">下载</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>

            <!-- 项目信息页签 -->
            <div id="project-tab" class="tab-content">
                <!-- 项目信息 -->
                <div class="detail-section">
                    <div class="section-header">项目信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">计划项目编号</div>
                                <div class="detail-value">JH-2024-003</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">计划项目名称</div>
                                <div class="detail-value">办公设备采购项目</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">项目类型</div>
                                <div class="detail-value">货物采购</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">项目业主</div>
                                <div class="detail-value">XX有限公司</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购类型</div>
                                <div class="detail-value">集中采购</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购方式</div>
                                <div class="detail-value">公开招标</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">招标类别</div>
                                <div class="detail-value">公开招标</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购组织方式</div>
                                <div class="detail-value">自行采购</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">所属二级公司单位</div>
                                <div class="detail-value">XX分公司</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">代理机构</div>
                                <div class="detail-value">中国招标有限公司</div>
                            </div>
                            <div class="detail-item full-width">
                                <div class="detail-label">备注</div>
                                <div class="detail-value">按季度分批采购，确保质量符合标准。</div>
                            </div>
                            <div class="detail-item full-width">
                                <div class="detail-label">附件</div>
                                <div class="detail-value">
                                    <div class="file-list">
                                        <div class="file-item">
                                            <span class="file-name">立项决策文件.pdf</span>
                                            <a href="#" class="file-download">下载</a>
                                        </div>
                                        <div class="file-item">
                                            <span class="file-name">采购需求说明书.docx</span>
                                            <a href="#" class="file-download">下载</a>
                                        </div>
                                        <div class="file-item">
                                            <span class="file-name">技术规格书.pdf</span>
                                            <a href="#" class="file-download">下载</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 标段信息 -->
                <div class="detail-section">
                    <div class="section-header">标段信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">标段编号</div>
                                <div class="detail-value">
                                    <a href="#" class="link-text" onclick="goToSegmentDetail('BD-2024-001')">BD-2024-001</a>
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">标段名称</div>
                                <div class="detail-value">
                                    <a href="#" class="link-text" onclick="goToSegmentDetail('BD-2024-001')">办公桌椅采购标段</a>
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">标段阶段</div>
                                <div class="detail-value">招标公告</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">审核状态</div>
                                <div class="detail-value"><span class="status-badge status-approved">已审核</span></div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购金额（万元）</div>
                                <div class="detail-value">25.00</div>
                            </div>
                            <div class="detail-item">
                                <!-- 空位保持布局对齐 -->
                            </div>
                            <div class="detail-item full-width">
                                <div class="detail-label">标段说明</div>
                                <div class="detail-value">采购办公桌椅，包括办公桌、办公椅、会议桌椅等，满足办公需求。要求环保材质，符合国家相关标准。</div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>

            <!-- 操作记录页签 -->
            <div id="records-tab" class="tab-content">
                <div class="section-content">
                    <div class="info-section">
                        <div class="section-title">流程记录</div>
                        <div class="operation-timeline">
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">公告发布</div>
                                        <div class="timeline-time">2024-01-12 11:00:15</div>
                                    </div>
                                    <div class="timeline-user">操作人：王五</div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">公告审核</div>
                                        <div class="timeline-time">2024-01-12 10:45:33</div>
                                    </div>
                                    <div class="timeline-user">操作人：李四</div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">部门审批</div>
                                        <div class="timeline-time">2024-01-12 09:30:25</div>
                                    </div>
                                    <div class="timeline-user">操作人：王经理</div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">提交审批</div>
                                        <div class="timeline-time">2024-01-11 16:20:18</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三</div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">公告编辑</div>
                                        <div class="timeline-time">2024-01-11 09:15:42</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三</div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">公告创建</div>
                                        <div class="timeline-time">2024-01-10 14:30:25</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- 帮助弹窗 -->
    <div id="help-modal" class="help-modal">
        <div class="help-content">
            <div class="help-header">
                <h3 class="help-title">公告详情功能说明</h3>
                <button class="close-btn" onclick="hideHelp()">&times;</button>
            </div>
            <div class="help-body">
                <div class="help-section">
                    <h4>功能概述</h4>
                    <p>公告详情页面用于查看招标采购公告的完整信息，包括公告内容和操作记录。</p>
                </div>
                
                <div class="help-section">
                    <h4>页签说明</h4>
                    <ul class="help-list">
                        <li><strong>公告详情：</strong>显示公告的完整内容，包括项目信息、资格要求、时间安排等</li>
                        <li><strong>项目信息：</strong>显示关联项目的详细信息，包括基本信息、采购需求、时间安排和参与方</li>
                        <li><strong>操作记录：</strong>显示公告从创建到发布的全部操作历史</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>操作说明</h4>
                    <ul class="help-list">
                        <li><strong>编辑：</strong>修改公告内容（仅草稿状态可编辑）</li>
                        <li><strong>撤回：</strong>撤回已发布的公告（需要相应权限）</li>
                        <li><strong>删除：</strong>删除公告（仅草稿状态可删除）</li>
                        <li><strong>打印：</strong>打印公告内容</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>状态说明</h4>
                    <ul class="help-list">
                        <li><strong>草稿：</strong>公告尚未提交，可以编辑和删除</li>
                        <li><strong>待审核：</strong>公告已提交，等待审核</li>
                        <li><strong>审核通过：</strong>公告审核通过，可以发布</li>
                        <li><strong>已发布：</strong>公告已正式发布，供应商可以查看</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>注意事项</h4>
                    <ul class="help-list">
                        <li>已发布的公告撤回需要相应权限</li>
                        <li>操作记录不可修改，系统自动记录</li>
                        <li>公告内容的修改会影响供应商的投标准备</li>
                        <li>重要操作建议先预览确认后再执行</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页签切换
        function switchTab(tabName) {
            // 隐藏所有页签内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有页签链接的激活状态
            const tabLinks = document.querySelectorAll('.tab-link');
            tabLinks.forEach(link => {
                link.classList.remove('active');
            });
            
            // 显示选中的页签内容
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 激活选中的页签链接
            event.target.classList.add('active');
        }

        // 帮助弹窗
        function showHelp() {
            document.getElementById('help-modal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('help-modal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });

        // 业务操作函数
        function goBack() {
            window.history.back();
        }

        // 跳转到标段详情页
        function goToSegmentDetail(segmentId) {
            // 这里可以跳转到标段详情页
            alert('跳转到标段详情页：' + segmentId);
            // 实际项目中可以使用：
            // window.location.href = 'segment-detail.html?id=' + segmentId;
        }

        function editAnnouncement() {
            // 检查状态是否允许编辑
            const status = '已发布'; // 这里应该从页面获取实际状态
            if (status === '已发布') {
                alert('已发布的公告不能直接编辑，请先撤回后再编辑。');
                return;
            }
            
            console.log('编辑公告');
            // 跳转到编辑页面
        }

        function withdrawAnnouncement() {
            if (confirm('确定要撤回此公告吗？撤回后公告将不再对外展示。')) {
                console.log('撤回公告');
                alert('公告撤回成功！');
            }
        }

        function deleteAnnouncement() {
            // 检查状态是否允许删除
            const status = '已发布'; // 这里应该从页面获取实际状态
            if (status !== '草稿') {
                alert('只有草稿状态的公告才能删除。');
                return;
            }
            
            if (confirm('确定要删除此公告吗？删除后无法恢复。')) {
                console.log('删除公告');
                alert('公告删除成功！');
            }
        }

        function printAnnouncement() {
            // 打印公告内容
            window.print();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('公告详情页面加载完成');
        });
    </script>
</body>
</html>