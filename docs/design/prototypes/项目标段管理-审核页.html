<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目标段审核</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            min-height: 100vh;
        }

        .page-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #1c4e80;
        }

        .back-btn {
            background: #f0f0f0;
            border: 1px solid #d9d9d9;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 15px;
            color: #666;
            text-decoration: none;
            font-size: 13px;
        }

        .back-btn:hover {
            background: #e6e6e6;
            border-color: #bfbfbf;
        }

        .page-title {
            font-size: 18px;
            font-weight: bold;
            color: #1c4e80;
        }

        .audit-actions {
            margin-left: auto;
            display: flex;
            gap: 12px;
        }

        .audit-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }

        .approve-btn {
            background: #52c41a;
            color: white;
        }

        .approve-btn:hover {
            background: #389e0d;
        }

        .reject-btn {
            background: #ff4d4f;
            color: white;
        }

        .reject-btn:hover {
            background: #d9363e;
        }

        .content-area {
            background: white;
        }

        .detail-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #1c4e80;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e8e8e8;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-item.full-width {
            grid-column: 1 / -1;
        }

        .detail-label {
            font-size: 13px;
            color: #666;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .detail-value {
            font-size: 14px;
            color: #333;
            padding: 8px 12px;
            background: #f9f9f9;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            min-height: 36px;
            display: flex;
            align-items: center;
        }

        .detail-value.textarea {
            min-height: 80px;
            align-items: flex-start;
            padding-top: 12px;
        }

        .status-tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }

        .status-approved {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .status-draft {
            background: #f0f0f0;
            color: #666;
            border: 1px solid #d9d9d9;
        }

        .audit-form {
            background: #fafafa;
            padding: 20px;
            border-radius: 6px;
            border: 1px solid #e8e8e8;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 13px;
            color: #666;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            background: white;
        }

        .form-control:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        textarea.form-control {
            min-height: 100px;
            resize: vertical;
        }

        .required {
            color: #ff4d4f;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 24px;
            border-radius: 8px;
            max-width: 500px;
            width: 90%;
        }

        .modal-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #1c4e80;
        }

        .modal-actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 20px;
        }

        .btn {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            background: white;
        }

        .btn-primary {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }

        .btn:hover {
            background: #f5f5f5;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <a href="项目标段管理-列表页.html" class="back-btn">← 返回列表</a>
            <div class="page-title">项目标段审核</div>
            <div class="audit-actions">
                <button class="audit-btn approve-btn" onclick="showApproveModal()">审核通过</button>
                <button class="audit-btn reject-btn" onclick="showRejectModal()">审核驳回</button>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 基本信息 -->
            <div class="detail-section">
                <div class="section-title">招标信息</div>
                <div class="detail-grid">
                    <div class="detail-item">
                        <div class="detail-label">计划项目编号</div>
                        <div class="detail-value">CG-2024-001</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">计划项目名称</div>
                        <div class="detail-value">办公楼装修改造项目</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">采购类型</div>
                        <div class="detail-value">工程</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">招标类别</div>
                        <div class="detail-value">施工招标</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">采购方式</div>
                        <div class="detail-value">公开招标</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">采购预算金额（万元）</div>
                        <div class="detail-value">500.00</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">资金来源</div>
                        <div class="detail-value">自有资金</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">招标时间</div>
                        <div class="detail-value">2024年第3季度</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">采购组织方式</div>
                        <div class="detail-value">委托招标</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">代理机构</div>
                        <div class="detail-value">某招标代理公司</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">年采购计划（万元）</div>
                        <div class="detail-value">500.00</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">项目经办人</div>
                        <div class="detail-value">张三</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">立项决策日期</div>
                        <div class="detail-value">2024-01-10</div>
                    </div>
                </div>
            </div>

            <!-- 项目信息 -->
            <div class="detail-section">
                <h3>项目信息</h3>
                <div class="detail-grid">
                    <div class="detail-item">
                        <div class="detail-label">项目类型</div>
                        <div class="detail-value">依法必须招标项目</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">项目业主</div>
                        <div class="detail-value">张三</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">所属二级公司单位</div>
                        <div class="detail-value">某某分公司</div>
                    </div>
                    <div class="detail-item full-width">
                        <div class="detail-label">项目基本情况（建设内容及规模）</div>
                        <div class="detail-value textarea">本项目主要包括办公楼主体装修工程，包括地面铺装、墙面装饰、吊顶安装、门窗更换等工作内容。要求使用环保材料，符合国家相关标准。</div>
                    </div>
                    <div class="detail-item full-width">
                        <div class="detail-label">备注</div>
                        <div class="detail-value textarea">需要说明的其他情况</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">立项决策文件</div>
                        <div class="detail-value">
                            <span class="file-icon">📄</span>
                            <span class="file-name">办公楼装修改造项目立项决策文件.pdf</span>
                            <a href="#" class="file-download" onclick="downloadDecisionFile('/files/decision/CG-2024-001-decision.pdf', '办公楼装修改造项目立项决策文件.pdf')">下载</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 审核状态 -->
            <div class="detail-section">
                <h3>审核状态</h3>
                <div class="detail-grid">
                    <div class="detail-item">
                        <div class="detail-label">审核状态</div>
                        <div class="detail-value"><span class="status-tag status-pending">审核中</span></div>
                    </div>
                </div>
            </div>

            <!-- 技术要求 -->
            <div class="detail-section">
                <div class="section-title">技术要求</div>
                <div class="detail-grid">
                    <div class="detail-item full-width">
                        <div class="detail-label">技术规格要求</div>
                        <div class="detail-value textarea">1. 地面材料：采用复合地板，厚度不低于12mm，环保等级E1级<br>
2. 墙面材料：乳胶漆，环保无甲醛，颜色按设计方案执行<br>
3. 吊顶材料：轻钢龙骨石膏板吊顶，防火等级A级<br>
4. 门窗：断桥铝合金门窗，玻璃采用中空玻璃</div>
                    </div>
                    <div class="detail-item full-width">
                        <div class="detail-label">质量标准</div>
                        <div class="detail-value textarea">严格按照国家建筑装饰装修工程质量验收规范执行，确保工程质量达到优良标准。</div>
                    </div>
                </div>
            </div>

            <!-- 商务要求 -->
            <div class="detail-section">
                <div class="section-title">商务要求</div>
                <div class="detail-grid">
                    <div class="detail-item">
                        <div class="detail-label">工期要求</div>
                        <div class="detail-value">90个工作日</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">质保期</div>
                        <div class="detail-value">2年</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">付款方式</div>
                        <div class="detail-value">分期付款</div>
                    </div>
                    <div class="detail-item full-width">
                        <div class="detail-label">其他要求</div>
                        <div class="detail-value textarea">投标人须具备建筑装修装饰工程专业承包二级及以上资质，近三年内无重大质量事故记录。</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 审核通过确认模态框 -->
    <div id="approve-modal" class="modal">
        <div class="modal-content">
            <div class="modal-title">审核通过确认</div>
            <div class="form-group">
                <label class="form-label">审核意见</label>
                <textarea class="form-control" placeholder="请输入审核意见（可选）"></textarea>
            </div>
            <div class="modal-actions">
                <button class="btn" onclick="hideModal('approve-modal')">取消</button>
                <button class="btn btn-primary" onclick="confirmApprove()">确认通过</button>
            </div>
        </div>
    </div>

    <!-- 审核驳回确认模态框 -->
    <div id="reject-modal" class="modal">
        <div class="modal-content">
            <div class="modal-title">审核驳回确认</div>
            <div class="form-group">
                <label class="form-label">驳回原因 <span class="required">*</span></label>
                <textarea class="form-control" placeholder="请输入驳回原因" required></textarea>
            </div>
            <div class="modal-actions">
                <button class="btn" onclick="hideModal('reject-modal')">取消</button>
                <button class="btn btn-primary" onclick="confirmReject()">确认驳回</button>
            </div>
        </div>
    </div>

    <script>
        // 显示审核通过模态框
        function showApproveModal() {
            document.getElementById('approve-modal').style.display = 'block';
        }

        // 显示审核驳回模态框
        function showRejectModal() {
            document.getElementById('reject-modal').style.display = 'block';
        }

        // 隐藏模态框
        function hideModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 确认审核通过
        function confirmApprove() {
            // 这里添加审核通过的逻辑
            alert('审核通过成功！');
            hideModal('approve-modal');
            // 返回列表页
            window.location.href = '项目标段管理-列表页.html';
        }

        // 确认审核驳回
        function confirmReject() {
            const reason = document.querySelector('#reject-modal textarea').value.trim();
            if (!reason) {
                alert('请输入驳回原因！');
                return;
            }
            // 这里添加审核驳回的逻辑
            alert('审核驳回成功！');
            hideModal('reject-modal');
            // 返回列表页
            window.location.href = '项目标段管理-列表页.html';
        }
        
        // 立项决策文件下载函数
        function downloadDecisionFile(fileUrl, fileName) {
            // 模拟文件下载
            const link = document.createElement('a');
            link.href = fileUrl;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // 显示下载提示
            alert('文件下载已开始，请查看浏览器下载目录。');
        }

        // 点击模态框外部关闭
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                hideModal(e.target.id);
            }
        });
    </script>
</body>
</html>