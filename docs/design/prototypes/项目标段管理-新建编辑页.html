<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目标段管理 - 新建/编辑</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        /* 主体布局 */
        .main-container {
            height: 100vh;
        }
        
        /* 内容区域 */
        .content-wrapper {
            overflow-y: auto;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        /* 页面内容 */
        .page-content {
            background: white;
            border-radius: 4px;
            border: 1px solid #e8eaec;
            padding: 20px;
            padding-bottom: 100px;
        }
        

        

        
        /* 表单分组 */
        .form-section {
            margin-bottom: 32px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #1890ff;
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            bottom: -2px;
            width: 40px;
            height: 2px;
            background: #1890ff;
        }
        

        
        /* 表单布局 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-grid.full-width {
            grid-template-columns: 1fr;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-group.required .form-label::after {
            content: '*';
            color: #ff4d4f;
            margin-left: 4px;
        }
        
        .form-label {
            font-size: 14px;
            color: #262626;
            font-weight: 500;
        }
        
        .form-control {
            height: 36px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 0 12px;
            font-size: 14px;
            width: 100%;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
        }
        
        .form-control.error {
            border-color: #ff4d4f;
        }
        
        .form-control.error:focus {
            box-shadow: 0 0 0 2px rgba(255,77,79,0.2);
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
            padding: 12px;
            line-height: 1.5;
        }
        
        .form-textarea.large {
            min-height: 120px;
        }
        
        .error-message {
            color: #ff4d4f;
            font-size: 12px;
            margin-top: 4px;
        }
        
        .help-text {
            color: #8c8c8c;
            font-size: 12px;
            margin-top: 4px;
        }
        
        /* 附件上传 */
        .upload-area {
            border: 2px dashed #d9d9d9;
            border-radius: 4px;
            padding: 40px 20px;
            text-align: center;
            background: #fafafa;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .upload-area:hover {
            border-color: #1890ff;
            background: #e6f7ff;
        }
        
        .upload-area.dragover {
            border-color: #1890ff;
            background: #e6f7ff;
        }
        
        .upload-icon {
            font-size: 48px;
            color: #d9d9d9;
            margin-bottom: 16px;
        }
        
        .upload-text {
            font-size: 16px;
            color: #262626;
            margin-bottom: 8px;
        }
        
        .upload-hint {
            font-size: 14px;
            color: #8c8c8c;
        }
        
        .file-list {
            margin-top: 16px;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            border: 1px solid #e8eaec;
            border-radius: 4px;
            margin-bottom: 8px;
            background: white;
        }
        
        .file-info {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }
        
        .file-icon {
            width: 32px;
            height: 32px;
            background: #1890ff;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }
        
        .file-details {
            flex: 1;
        }
        
        .file-name {
            font-size: 14px;
            color: #262626;
            margin-bottom: 4px;
        }
        
        .file-meta {
            font-size: 12px;
            color: #8c8c8c;
        }
        
        .file-actions {
            display: flex;
            gap: 8px;
        }
        
        /* 表单控件组合样式 */
        .form-control-with-btn {
            display: flex;
            gap: 8px;
        }
        
        .form-control-with-btn .form-control {
            flex: 1;
        }
        
        .btn-sm {
            height: 32px;
            padding: 0 12px;
            font-size: 12px;
        }
        
        .file-action {
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }
        
        .file-action:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .file-action.delete:hover {
            border-color: #ff4d4f;
            color: #ff4d4f;
        }
        
        /* 标段列表 */
        .section-list {
            margin-top: 20px;
        }
        
        .section-item {
            border: 1px solid #e8eaec;
            border-radius: 4px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        
        .section-item-header {
            padding: 12px 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #e8eaec;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .section-item-title {
            font-weight: 500;
            color: #333;
        }
        
        .section-item-actions {
            display: flex;
            gap: 10px;
        }
        
        .section-item-content {
            padding: 15px;
        }
        
        .btn {
            padding: 0 24px;
            height: 40px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            border: 1px solid transparent;
            transition: all 0.3s;
            min-width: 100px;
        }
        
        .btn-primary {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        
        .btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }
        
        .btn-success {
            background-color: #52c41a;
            color: white;
            border-color: #52c41a;
        }
        
        .btn-success:hover {
            background-color: #73d13d;
            border-color: #73d13d;
        }
        
        .btn-default {
            background-color: white;
            border-color: #d9d9d9;
            color: #262626;
        }
        
        .btn-default:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
            height: auto;
            min-width: auto;
        }
        
        /* 操作按钮 */
        .form-actions {
            background: #f8fafc;
            border-top: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            gap: 16px;
            z-index: 1000;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn-group {
            display: flex;
            gap: 12px;
        }
        
        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 2000;
        }
        
        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .help-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #1c4e80;
        }
        
        .help-text {
            line-height: 1.6;
            color: #666;
            margin-bottom: 10px;
        }
        
        .close-btn {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #999;
        }
    </style>
</head>
<body>


    <!-- 主体容器 -->
    <div class="main-container">
        <!-- 内容区域 -->
        <div class="content-wrapper">
            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 表单内容 -->
                <form id="projectForm">
                    <!-- 招标信息 -->
                    <div class="form-section">
                        <div class="section-title">招标信息</div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label required">选择计划</label>
                            <div class="form-control-with-btn">
                                <input type="text" class="form-control" id="planSelect" placeholder="请选择采购计划" readonly>
                                <button type="button" class="btn btn-primary btn-sm" onclick="showPlanSelectModal()">选择</button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">计划项目编号</label>
                            <input type="text" class="form-control" id="planCode" disabled>
                        </div>
                        <div class="form-group">
                            <label class="form-label">计划项目名称</label>
                            <input type="text" class="form-control" id="planName" disabled>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购类型</label>
                            <input type="text" class="form-control" id="purchaseType" disabled>
                        </div>
                        <div class="form-group">
                            <label class="form-label">招标类别</label>
                            <input type="text" class="form-control" id="bidCategory" disabled>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购方式</label>
                            <input type="text" class="form-control" id="purchaseMethod" disabled>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购预算金额（万元）</label>
                            <input type="number" class="form-control" id="bidAmount" disabled>
                        </div>
                        <div class="form-group">
                            <label class="form-label">资金来源</label>
                            <input type="text" class="form-control" id="fundSource" disabled>
                        </div>
                        <div class="form-group">
                            <label class="form-label">招标时间</label>
                            <input type="text" class="form-control" id="bidTime" disabled>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购组织方式</label>
                            <input type="text" class="form-control" id="organizationMethod" disabled>
                        </div>
                        <div class="form-group">
                            <label class="form-label">代理机构</label>
                            <input type="text" class="form-control" id="agency" disabled>
                        </div>
                        <div class="form-group">
                            <label class="form-label">年采购计划（万元）</label>
                            <input type="number" class="form-control" id="annualPlan" disabled>
                        </div>
                        <div class="form-group">
                            <label class="form-label">项目经办人</label>
                            <input type="text" class="form-control" id="applicant" disabled>
                        </div>
                        <div class="form-group">
                            <label class="form-label">立项决策日期</label>
                            <input type="date" class="form-control" id="decisionDate" disabled>
                        </div>
                    </div>
                    
                    <!-- 项目信息 -->
                    <div class="form-section">
                        <div class="section-title">项目信息</div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">项目类型</label>
                            <input type="text" class="form-control" id="projectType" disabled>
                        </div>
                        <div class="form-group">
                            <label class="form-label">项目业主</label>
                            <input type="text" class="form-control" id="projectOwner" disabled>
                        </div>
                        <div class="form-group">
                            <label class="form-label">所属二级公司单位</label>
                            <input type="text" class="form-control" id="subCompany" disabled>
                        </div>
                        <div class="form-group full-width">
                            <label class="form-label">项目基本情况（建设内容及规模）</label>
                            <textarea class="form-control form-textarea" id="projectDescription" disabled></textarea>
                        </div>
                        <div class="form-group full-width">
                            <label class="form-label">备注</label>
                            <textarea class="form-control form-textarea" id="projectRemark" disabled></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">立项决策文件</label>
                            <div class="file-display" id="decisionFileDisplay">
                                <div class="file-item">
                                    <span class="file-icon">📄</span>
                                    <span class="file-name" id="decisionFileName">暂无文件</span>
                                    <a href="#" class="file-download" id="decisionFileDownload" style="display: none;" onclick="downloadDecisionFile()">下载</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 标段信息 -->
                    <div class="form-section">
                        <div class="section-title">
                            标段信息
                            <button type="button" class="btn btn-success btn-small" onclick="addSection()">添加标段</button>
                        </div>
                    <div class="section-list" id="sectionList">
                        <!-- 标段项目将动态添加到这里 -->
                    </div>
                    </div>
                    

                    
                    <!-- 操作按钮 -->
                    <div class="form-actions">
                        <div class="btn-group">
                            <button type="button" class="btn btn-default" onclick="cancelEdit()">取消</button>
                            <button type="button" class="btn btn-default" onclick="saveProject()">保存草稿</button>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary" onclick="submitProject()">提交审批</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div class="help-modal" id="help-modal">
        <div class="help-content">
            <button class="close-btn" onclick="hideHelp()">×</button>
            <div class="help-title">项目标段管理 - 新建/编辑说明</div>
            <div class="help-text">
                <strong>操作流程：</strong><br>
                1. 首先选择已审批的采购计划，系统将自动带出相关信息<br>
                2. 项目编号由系统自动生成<br>
                3. 添加一个或多个标段信息<br>
                4. 上传相关文件<br>
                5. 保存或提交审批
            </div>
            <div class="help-text">
                <strong>字段说明：</strong><br>
                • <strong>招标信息：</strong>从选择的采购计划中自动带出，不可编辑<br>
                • <strong>项目信息：</strong>从采购计划中带出，项目编号自动生成<br>
                • <strong>标段信息：</strong>可添加多个标段，每个标段包含名称、说明、金额等<br>
                • <strong>文件信息：</strong>上传立项决策文件和采购文件
            </div>
            <div class="help-text">
                <strong>注意事项：</strong><br>
                • 必须先选择采购计划才能继续填写<br>
                • 至少需要添加一个标段<br>
                • 标段金额总和不能超过招标金额<br>
                • 提交审批后不可修改，请仔细检查
            </div>
        </div>
    </div>

    <!-- 选择计划弹窗 -->
    <div id="plan-select-modal" class="help-modal">
        <div class="help-content" style="max-width: 1200px; max-height: 95vh; width: 90vw;">
            <button class="close-btn" onclick="hidePlanSelectModal()">×</button>
            <div class="help-title">请选择计划</div>
            
            <!-- 查询条件 -->
            <div class="form-section" style="margin-bottom: 20px;">
                <div class="form-grid" style="grid-template-columns: repeat(3, 1fr); gap: 15px; margin-bottom: 15px;">
                    <div class="form-group">
                        <label class="form-label">计划项目名称</label>
                        <input type="text" class="form-control" id="searchPlanName" placeholder="请输入计划项目名称">
                    </div>
                    <div class="form-group">
                        <label class="form-label">采购组织方式</label>
                        <select class="form-control" id="searchOrgMethod">
                            <option value="">全部</option>
                            <option value="自行组织">自行组织</option>
                            <option value="委托代理">委托代理</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">采购方式</label>
                        <select class="form-control" id="searchPurchaseMethod">
                            <option value="">全部</option>
                            <option value="公开招标">公开招标</option>
                            <option value="邀请招标">邀请招标</option>
                            <option value="竞争性谈判">竞争性谈判</option>
                            <option value="单一来源">单一来源</option>
                        </select>
                    </div>
                </div>
                <div class="form-grid" style="grid-template-columns: repeat(4, 1fr); gap: 15px;">
                    <div class="form-group">
                        <label class="form-label">采购类型</label>
                        <select class="form-control" id="searchPurchaseType">
                            <option value="">全部</option>
                            <option value="货物">货物</option>
                            <option value="工程">工程</option>
                            <option value="服务">服务</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">创建时间（起）</label>
                        <input type="date" class="form-control" id="searchStartDate">
                    </div>
                    <div class="form-group">
                        <label class="form-label">创建时间（止）</label>
                        <input type="date" class="form-control" id="searchEndDate">
                    </div>
                    <div class="form-group" style="display: flex; align-items: end;">
                        <button type="button" class="btn btn-primary" onclick="searchPlans()" style="margin-right: 10px;">查询</button>
                        <button type="button" class="btn btn-default" onclick="resetSearch()">重置</button>
                    </div>
                </div>
            </div>
            
            <!-- 计划列表 -->
            <div class="plan-list-container">
                <table class="plan-table" style="width: 100%; border-collapse: collapse; margin-bottom: 20px; font-size: 14px;">
                    <thead>
                        <tr style="background: #f8f9fa; border-bottom: 2px solid #e8eaec;">
                            <th style="padding: 15px 12px; text-align: center; border: 1px solid #e8eaec; width: 60px;">选择</th>
                            <th style="padding: 15px 12px; text-align: left; border: 1px solid #e8eaec; min-width: 200px;">计划项目名称</th>
                            <th style="padding: 15px 12px; text-align: center; border: 1px solid #e8eaec; width: 120px;">采购组织方式</th>
                            <th style="padding: 15px 12px; text-align: center; border: 1px solid #e8eaec; width: 120px;">采购方式</th>
                            <th style="padding: 15px 12px; text-align: center; border: 1px solid #e8eaec; width: 100px;">采购类型</th>
                            <th style="padding: 15px 12px; text-align: center; border: 1px solid #e8eaec; width: 120px;">创建时间</th>
                        </tr>
                    </thead>
                    <tbody id="planTableBody">
                        <!-- 动态生成的计划列表 -->
                    </tbody>
                </table>
                
                <!-- 分页 -->
                <div class="pagination-container" style="display: flex; justify-content: space-between; align-items: center; padding: 15px 0;">
                    <div class="pagination-info" style="font-size: 14px; color: #666;">
                        共 <span id="totalPlans">0</span> 条记录，第 <span id="currentPage">1</span> / <span id="totalPages">1</span> 页
                    </div>
                    <div class="pagination-controls" style="display: flex; gap: 8px;">
                        <button type="button" class="btn btn-default" onclick="goToPage(1)" id="firstPageBtn" style="padding: 8px 16px; min-width: 70px;">首页</button>
                        <button type="button" class="btn btn-default" onclick="goToPage(currentPageNum - 1)" id="prevPageBtn" style="padding: 8px 16px; min-width: 70px;">上一页</button>
                        <button type="button" class="btn btn-default" onclick="goToPage(currentPageNum + 1)" id="nextPageBtn" style="padding: 8px 16px; min-width: 70px;">下一页</button>
                        <button type="button" class="btn btn-default" onclick="goToPage(totalPageNum)" id="lastPageBtn" style="padding: 8px 16px; min-width: 70px;">末页</button>
                    </div>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="modal-actions" style="display: flex; justify-content: flex-end; gap: 15px; margin-top: 25px; padding-top: 20px; border-top: 1px solid #e8eaec;">
                <button type="button" class="btn btn-default" onclick="hidePlanSelectModal()" style="padding: 10px 30px; font-size: 14px; min-width: 100px;">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmPlanSelection()" style="padding: 10px 30px; font-size: 14px; min-width: 100px;">确认</button>
            </div>
        </div>
    </div>

    <script>
        let sectionCount = 0;
        
        // 模拟采购计划数据
        const planData = {
            'CG-2024-001': {
                planCode: 'CG-2024-001',
                purchaseType: '货物',
                bidCategory: '公开招标',
                purchaseMethod: '公开招标',
                bidAmount: 50.00,
                fundSource: '自有资金',
                bidTime: '2024-03-15',
                planStartTime: '2024-04-01',
                planEndTime: '2024-06-30',
                organizationMethod: '自行组织',
                agency: '-',
                annualPlan: '2024年度采购计划',
                applicant: '张三',
                projectName: '办公用品采购项目',
                projectType: '货物类',
                projectOwner: 'XX集团有限公司',
                subCompany: 'XX分公司',
                projectDescription: '采购办公桌椅、文具用品、电脑设备等日常办公用品，满足公司日常办公需求。',
                projectRemark: '按季度分批采购，确保质量符合标准。',
                createTime: '2024-01-15',
                decisionFile: {
                    name: '办公用品采购立项决策文件.pdf',
                    url: '/files/decision/CG-2024-001-decision.pdf'
                }
            },
            'CG-2024-002': {
                planCode: 'CG-2024-002',
                purchaseType: '工程',
                bidCategory: '公开招标',
                purchaseMethod: '公开招标',
                bidAmount: 200.00,
                fundSource: '银行贷款',
                bidTime: '2024-04-01',
                planStartTime: '2024-05-01',
                planEndTime: '2024-08-31',
                organizationMethod: '委托代理',
                agency: 'XX建设咨询有限公司',
                annualPlan: '2024年度建设计划',
                applicant: '李四',
                projectName: '建筑材料采购项目',
                projectType: '工程类',
                projectOwner: 'XX集团有限公司',
                subCompany: 'XX建设分公司',
                projectDescription: '采购水泥、钢材、砂石等建筑材料，用于公司基础设施建设项目。',
                projectRemark: '材料需符合国家建筑标准，分批次供货。',
                createTime: '2024-02-01',
                decisionFile: {
                    name: '建筑材料采购立项决策文件.pdf',
                    url: '/files/decision/CG-2024-002-decision.pdf'
                }
            },
            'CG-2024-003': {
                planCode: 'CG-2024-003',
                purchaseType: '服务',
                bidCategory: '邀请招标',
                purchaseMethod: '邀请招标',
                bidAmount: 80.00,
                fundSource: '自有资金',
                bidTime: '2024-03-20',
                planStartTime: '2024-04-15',
                planEndTime: '2024-07-15',
                organizationMethod: '自行组织',
                agency: '-',
                annualPlan: '2024年度服务采购计划',
                applicant: '王五',
                projectName: 'IT运维服务采购项目',
                projectType: '服务类',
                projectOwner: 'XX集团有限公司',
                subCompany: 'XX信息分公司',
                projectDescription: '采购IT系统运维、网络维护、设备保养等技术服务。',
                projectRemark: '要求服务商具备相关资质和经验。',
                createTime: '2024-01-20',
                decisionFile: {
                    name: 'IT运维服务采购立项决策文件.pdf',
                    url: '/files/decision/CG-2024-003-decision.pdf'
                }
            },
            'CG-2024-004': {
                planCode: 'CG-2024-004',
                purchaseType: '货物',
                bidCategory: '竞争性谈判',
                purchaseMethod: '竞争性谈判',
                bidAmount: 120.00,
                fundSource: '专项资金',
                bidTime: '2024-05-01',
                planStartTime: '2024-06-01',
                planEndTime: '2024-09-30',
                organizationMethod: '委托代理',
                agency: 'XX采购代理有限公司',
                annualPlan: '2024年度设备采购计划',
                applicant: '赵六',
                projectName: '生产设备采购项目',
                projectType: '货物类',
                projectOwner: 'XX集团有限公司',
                subCompany: 'XX制造分公司',
                projectDescription: '采购生产线设备、检测仪器、安全防护设备等。',
                projectRemark: '设备需符合行业标准，提供质保服务。',
                createTime: '2024-02-15',
                decisionFile: {
                    name: '生产设备采购立项决策文件.pdf',
                    url: '/files/decision/CG-2024-004-decision.pdf'
                }
            },
            'CG-2024-005': {
                planCode: 'CG-2024-005',
                purchaseType: '工程',
                bidCategory: '公开招标',
                purchaseMethod: '公开招标',
                bidAmount: 300.00,
                fundSource: '银行贷款',
                bidTime: '2024-06-01',
                planStartTime: '2024-07-01',
                planEndTime: '2024-12-31',
                organizationMethod: '委托代理',
                agency: 'XX工程咨询有限公司',
                annualPlan: '2024年度基建计划',
                applicant: '孙七',
                projectName: '厂房改造工程项目',
                projectType: '工程类',
                projectOwner: 'XX集团有限公司',
                subCompany: 'XX建设分公司',
                projectDescription: '对现有厂房进行改造升级，包括结构加固、设备更新等。',
                projectRemark: '工程需符合安全规范，按期完工。',
                createTime: '2024-03-01',
                decisionFile: {
                    name: '厂房改造工程立项决策文件.pdf',
                    url: '/files/decision/CG-2024-005-decision.pdf'
                }
            }
        };
        
        // 选择计划弹窗相关变量
        let currentPageNum = 1;
        let totalPageNum = 1;
        let pageSize = 5;
        let selectedPlanCode = '';
        let filteredPlans = [];
        
        // 加载计划数据
        function loadPlanData(planCode) {
            if (planCode && planData[planCode]) {
                const data = planData[planCode];
                
                // 填充招标信息
                document.getElementById('planCode').value = data.planCode;
                document.getElementById('purchaseType').value = data.purchaseType;
                document.getElementById('bidCategory').value = data.bidCategory;
                document.getElementById('purchaseMethod').value = data.purchaseMethod;
                document.getElementById('bidAmount').value = data.bidAmount;
                document.getElementById('fundSource').value = data.fundSource;
                document.getElementById('bidTime').value = data.bidTime;
                document.getElementById('planStartTime').value = data.planStartTime;
                document.getElementById('planEndTime').value = data.planEndTime;
                document.getElementById('organizationMethod').value = data.organizationMethod;
                document.getElementById('agency').value = data.agency;
                document.getElementById('annualPlan').value = data.annualPlan;
                document.getElementById('applicant').value = data.applicant;
                
                // 填充项目信息
                document.getElementById('projectName').value = data.projectName;
                document.getElementById('projectType').value = data.projectType;
                document.getElementById('projectOwner').value = data.projectOwner;
                document.getElementById('subCompany').value = data.subCompany;
                document.getElementById('projectDescription').value = data.projectDescription;
                document.getElementById('projectRemark').value = data.projectRemark;
                
                // 填充立项决策文件
                if (data.decisionFile) {
                    document.getElementById('decisionFileName').textContent = data.decisionFile.name;
                    document.getElementById('decisionFileDownload').style.display = 'inline';
                    document.getElementById('decisionFileDownload').setAttribute('data-url', data.decisionFile.url);
                } else {
                    document.getElementById('decisionFileName').textContent = '暂无文件';
                    document.getElementById('decisionFileDownload').style.display = 'none';
                }
                
                // 清空现有标段并重新生成
                document.getElementById('sectionList').innerHTML = '';
                sectionCount = 0;
                
                // 自动添加一个默认标段
                addSection();
            } else {
                // 清空所有字段
                clearAllFields();
            }
        }
        
        // 清空所有字段
        function clearAllFields() {
            const fields = ['planCode', 'purchaseType', 'bidCategory', 'purchaseMethod', 'bidAmount', 
                          'fundSource', 'bidTime', 'planStartTime', 'planEndTime', 'organizationMethod', 
                          'agency', 'annualPlan', 'applicant', 'projectName', 'projectType', 
                          'projectOwner', 'subCompany', 'projectDescription', 'projectRemark'];
            
            fields.forEach(field => {
                document.getElementById(field).value = '';
            });
            
            // 清空标段列表
            document.getElementById('sectionList').innerHTML = '';
            sectionCount = 0;
        }
        
        // 添加标段
        function addSection() {
            sectionCount++;
            const sectionList = document.getElementById('sectionList');
            
            // 获取当前选择的计划编号
            const planSelect = document.getElementById('planSelect');
            const selectedPlan = planSelect.getAttribute('data-plan-code') || '';
            let sectionCode = '';
            
            if (selectedPlan) {
                // 使用计划编号+3位序号格式
                sectionCode = `${selectedPlan}-${String(sectionCount).padStart(3, '0')}`;
            } else {
                // 如果没有选择计划，使用默认格式
                sectionCode = `BD-2024-${String(sectionCount).padStart(3, '0')}`;
            }
            
            const sectionHtml = `
                <div class="section-item" id="section-${sectionCount}">
                    <div class="section-item-header">
                        <div class="section-item-title">标段 ${sectionCount}</div>
                        <div class="section-item-actions">
                            <button type="button" class="btn btn-danger btn-small" onclick="removeSection(${sectionCount})">删除</button>
                        </div>
                    </div>
                    <div class="section-item-content">
                        <div class="form-grid">
                                <div class="form-group">
                                    <label class="form-label required">标段名称</label>
                                    <input type="text" class="form-control" name="sectionName-${sectionCount}" placeholder="请输入标段名称">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">标段编号</label>
                                    <input type="text" class="form-control" name="sectionCode-${sectionCount}" value="${sectionCode}" disabled>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">标段状态</label>
                                    <input type="text" class="form-control" name="sectionStatus-${sectionCount}" value="待审核" disabled>
                                </div>
                                <div class="form-group">
                                    <label class="form-label required">采购金额（万元）</label>
                                    <input type="number" class="form-control" name="sectionAmount-${sectionCount}" placeholder="请输入采购金额" step="0.01">
                                </div>
                            </div>
                            <div class="form-grid full-width">
                                <div class="form-group">
                                    <label class="form-label">标段说明</label>
                                    <textarea class="form-control form-textarea" name="sectionDescription-${sectionCount}" placeholder="请输入标段说明"></textarea>
                                </div>
                            </div>
                    </div>
                </div>
            `;
            
            sectionList.insertAdjacentHTML('beforeend', sectionHtml);
        }
        
        // 删除标段
        function removeSection(sectionId) {
            const sectionElement = document.getElementById(`section-${sectionId}`);
            if (sectionElement) {
                sectionElement.remove();
            }
        }
        
        // 立项决策文件下载函数
        function downloadDecisionFile() {
            const downloadLink = document.getElementById('decisionFileDownload');
            const fileUrl = downloadLink.getAttribute('data-url');
            if (fileUrl) {
                // 模拟文件下载
                const link = document.createElement('a');
                link.href = fileUrl;
                link.download = document.getElementById('decisionFileName').textContent;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                // 显示下载提示
                alert('文件下载已开始，请查看浏览器下载目录。');
            }
        }
        
        // 保存项目
        function saveProject() {
            if (validateForm()) {
                alert('项目保存成功！');
                // 这里可以添加实际的保存逻辑
            }
        }
        
        // 提交审批
        function submitProject() {
            if (validateForm()) {
                if (confirm('确定要提交审批吗？提交后将不能修改。')) {
                    alert('项目已提交审批！');
                    // 这里可以添加实际的提交逻辑
                    window.location.href = '项目标段管理-列表页.html';
                }
            }
        }
        
        // 取消编辑
        function cancelEdit() {
            if (confirm('确定要取消吗？未保存的数据将丢失。')) {
                window.location.href = '项目标段管理-列表页.html';
            }
        }
        
        // 表单验证
        function validateForm() {
            const planSelect = document.getElementById('planSelect');
            if (!planSelect.value || !planSelect.getAttribute('data-plan-code')) {
                alert('请选择采购计划！');
                return false;
            }
            
            const sectionList = document.getElementById('sectionList');
            if (sectionList.children.length === 0) {
                alert('请至少添加一个标段！');
                return false;
            }
            
            // 验证标段信息
            const sections = sectionList.querySelectorAll('.section-item');
            for (let section of sections) {
                const nameInput = section.querySelector('input[name^="sectionName"]');
                const amountInput = section.querySelector('input[name^="sectionAmount"]');
                
                if (!nameInput.value.trim()) {
                    alert('请填写标段名称！');
                    nameInput.focus();
                    return false;
                }
                
                if (!amountInput.value || parseFloat(amountInput.value) <= 0) {
                    alert('请填写正确的采购金额！');
                    amountInput.focus();
                    return false;
                }
            }
            
            return true;
        }
        
        // 帮助功能
        function showHelp() {
            document.getElementById('help-modal').style.display = 'block';
        }
        
        function hideHelp() {
            document.getElementById('help-modal').style.display = 'none';
        }
        
        // 点击模态框外部关闭
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });
        
        // 选择计划弹窗相关函数
        
        // 显示选择计划弹窗
        function showPlanSelectModal() {
            document.getElementById('plan-select-modal').style.display = 'block';
            resetSearch();
            searchPlans();
        }
        
        // 隐藏选择计划弹窗
        function hidePlanSelectModal() {
            document.getElementById('plan-select-modal').style.display = 'none';
            selectedPlanCode = '';
        }
        
        // 重置搜索条件
        function resetSearch() {
            document.getElementById('searchPlanName').value = '';
            document.getElementById('searchOrgMethod').value = '';
            document.getElementById('searchPurchaseMethod').value = '';
            document.getElementById('searchPurchaseType').value = '';
            document.getElementById('searchStartDate').value = '';
            document.getElementById('searchEndDate').value = '';
            currentPageNum = 1;
        }
        
        // 搜索计划
        function searchPlans() {
            const searchName = document.getElementById('searchPlanName').value.toLowerCase();
            const searchOrgMethod = document.getElementById('searchOrgMethod').value;
            const searchPurchaseMethod = document.getElementById('searchPurchaseMethod').value;
            const searchPurchaseType = document.getElementById('searchPurchaseType').value;
            const searchStartDate = document.getElementById('searchStartDate').value;
            const searchEndDate = document.getElementById('searchEndDate').value;
            
            // 过滤计划数据
            filteredPlans = Object.values(planData).filter(plan => {
                let match = true;
                
                if (searchName && !plan.projectName.toLowerCase().includes(searchName)) {
                    match = false;
                }
                
                if (searchOrgMethod && plan.organizationMethod !== searchOrgMethod) {
                    match = false;
                }
                
                if (searchPurchaseMethod && plan.purchaseMethod !== searchPurchaseMethod) {
                    match = false;
                }
                
                if (searchPurchaseType && plan.purchaseType !== searchPurchaseType) {
                    match = false;
                }
                
                if (searchStartDate && plan.createTime < searchStartDate) {
                    match = false;
                }
                
                if (searchEndDate && plan.createTime > searchEndDate) {
                    match = false;
                }
                
                return match;
            });
            
            // 重置到第一页
            currentPageNum = 1;
            
            // 渲染计划列表
            renderPlanList();
        }
        
        // 渲染计划列表
        function renderPlanList() {
            const tbody = document.getElementById('planTableBody');
            const startIndex = (currentPageNum - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const currentPlans = filteredPlans.slice(startIndex, endIndex);
            
            // 计算总页数
            totalPageNum = Math.ceil(filteredPlans.length / pageSize);
            
            // 生成表格行
            let html = '';
            currentPlans.forEach(plan => {
                html += `
                    <tr style="border-bottom: 1px solid #e8eaec; cursor: pointer;" onmouseover="this.style.backgroundColor='#f8f9fa'" onmouseout="this.style.backgroundColor=''">
                        <td style="padding: 15px 12px; border: 1px solid #e8eaec; text-align: center;">
                            <input type="radio" name="selectedPlan" value="${plan.planCode}" onchange="selectPlan('${plan.planCode}')" style="transform: scale(1.2);">
                        </td>
                        <td style="padding: 15px 12px; border: 1px solid #e8eaec; text-align: left;">${plan.projectName}</td>
                        <td style="padding: 15px 12px; border: 1px solid #e8eaec; text-align: center;">${plan.organizationMethod}</td>
                        <td style="padding: 15px 12px; border: 1px solid #e8eaec; text-align: center;">${plan.purchaseMethod}</td>
                        <td style="padding: 15px 12px; border: 1px solid #e8eaec; text-align: center;">${plan.purchaseType}</td>
                        <td style="padding: 15px 12px; border: 1px solid #e8eaec; text-align: center;">${plan.createTime}</td>
                    </tr>
                `;
            });
            
            tbody.innerHTML = html;
            
            // 更新分页信息
            document.getElementById('totalPlans').textContent = filteredPlans.length;
            document.getElementById('currentPage').textContent = currentPageNum;
            document.getElementById('totalPages').textContent = totalPageNum;
            
            // 更新分页按钮状态
            updatePaginationButtons();
        }
        
        // 选择计划
        function selectPlan(planCode) {
            selectedPlanCode = planCode;
        }
        
        // 分页跳转
        function goToPage(page) {
            if (page < 1 || page > totalPageNum) {
                return;
            }
            currentPageNum = page;
            renderPlanList();
        }
        
        // 更新分页按钮状态
        function updatePaginationButtons() {
            document.getElementById('firstPageBtn').disabled = currentPageNum === 1;
            document.getElementById('prevPageBtn').disabled = currentPageNum === 1;
            document.getElementById('nextPageBtn').disabled = currentPageNum === totalPageNum;
            document.getElementById('lastPageBtn').disabled = currentPageNum === totalPageNum;
        }
        
        // 确认选择计划
        function confirmPlanSelection() {
            if (!selectedPlanCode) {
                alert('请选择一个计划！');
                return;
            }
            
            const selectedPlan = planData[selectedPlanCode];
            if (selectedPlan) {
                // 填充计划名称到输入框
                const planSelect = document.getElementById('planSelect');
                planSelect.value = selectedPlan.projectName;
                planSelect.setAttribute('data-plan-code', selectedPlanCode);
                
                // 加载计划数据
                loadPlanData(selectedPlanCode);
                
                // 关闭弹窗
                hidePlanSelectModal();
            }
        }
        
        // 点击弹窗外部关闭
        document.getElementById('plan-select-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hidePlanSelectModal();
            }
        });
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 根据URL参数判断是新建还是编辑
            const urlParams = new URLSearchParams(window.location.search);
            const type = urlParams.get('type');
            const id = urlParams.get('id');
            
            if (type === 'section') {
                document.querySelector('.page-title').textContent = '新建标段';
            } else if (id) {
                document.querySelector('.page-title').textContent = '编辑项目标段';
                // 这里可以加载现有数据
            }
            
            // 默认添加一个标段
            addSection();
        });
    </script>
</body>
</html>