<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目预警管理 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 页面头部 */
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .help-icon {
            width: 20px;
            height: 20px;
            background: #3498db;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .help-icon:hover {
            background: #2980b9;
        }

        /* 页签导航 */
        .tab-navigation {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-nav {
            display: flex;
            border-bottom: 1px solid #e9ecef;
        }

        .tab-nav-item {
            padding: 15px 25px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
            font-weight: 500;
            color: #666;
        }

        .tab-nav-item.active {
            color: #3498db;
            border-bottom-color: #3498db;
            background: #f8f9fa;
        }

        .tab-nav-item:hover {
            background: #f8f9fa;
            color: #3498db;
        }

        /* 页签内容 */
        .tab-content {
            display: none;
            padding: 20px;
        }

        .tab-content.active {
            display: block;
        }

        /* 查询区域 */
        .search-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 13px;
            color: #555;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-control {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .btn-group {
            display: flex;
            gap: 10px;
            align-items: end;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-outline {
            background: white;
            color: #3498db;
            border: 1px solid #3498db;
        }

        .btn-outline:hover {
            background: #3498db;
            color: white;
        }

        /* 高级查询 */
        .advanced-search {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e6e8eb;
            display: none;
        }

        .advanced-search.show {
            display: block;
        }

        .advanced-toggle {
            color: #1890ff;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        /* 功能操作栏 */
        .action-bar {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .action-left {
            display: flex;
            gap: 10px;
        }

        .action-right {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        /* 数据表格 */
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-wrapper {
            overflow-x: auto;
            max-height: 600px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
            table-layout: fixed;
            min-width: 1200px;
        }

        .data-table th,
        .data-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            word-wrap: break-word;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table tbody tr:hover {
            background: #f8f9fa;
        }

        /* 冻结列样式 */
        .data-table th:first-child,
        .data-table td:first-child {
            position: sticky;
            left: 0;
            background: white;
            z-index: 5;
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
            width: 50px;
        }

        .data-table th:first-child {
            background: #f8f9fa;
            z-index: 15;
        }

        .data-table th:nth-child(2),
        .data-table td:nth-child(2) {
            position: sticky;
            left: 50px;
            background: white;
            z-index: 5;
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
            width: 250px;
        }

        .data-table th:nth-child(2) {
            background: #f8f9fa;
            z-index: 15;
        }

        .data-table th:last-child,
        .data-table td:last-child {
            position: sticky;
            right: 0;
            background: white;
            z-index: 5;
            box-shadow: -2px 0 4px rgba(0,0,0,0.1);
            width: 160px;
        }

        /* 预警名称链接样式 */
        .warning-name-link {
            color: #3498db;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }

        .warning-name-link:hover {
            color: #2980b9;
            text-decoration: underline;
        }

        /* 操作列按钮样式优化 */
        .col-actions {
            white-space: nowrap;
        }

        .col-actions .btn {
            margin-right: 5px;
            margin-bottom: 0;
            min-width: auto;
            padding: 4px 8px;
            font-size: 12px;
            display: inline-block;
        }

        .col-actions .btn:last-child {
            margin-right: 0;
        }

        /* 确保操作栏按钮单行显示 */
        .data-table td.col-actions {
            white-space: nowrap;
            display: table-cell;
            vertical-align: middle;
        }

        /* 已办页签操作按钮样式 */
        .btn-action {
            background: none;
            border: none;
            font-size: 16px;
            cursor: pointer;
            margin-right: 8px;
            padding: 4px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }

        .btn-action:hover {
            background-color: #f8f9fa;
        }

        .btn-action:last-child {
            margin-right: 0;
        }

        .data-table th:last-child {
            background: #f8f9fa;
            z-index: 15;
        }

        /* 列宽设置 */
        .col-checkbox { width: 50px; }
        .col-warning-name { width: 250px; }
        .col-warning-type { width: 200px; }
        .col-section-name { width: 200px; }
        .col-warning-level { width: 100px; }
        .col-warning-time { width: 150px; }
        .col-warning-content { width: 300px; }
        .col-status { width: 100px; }
        .col-handler { width: 120px; }
        .col-handle-time { width: 150px; }
        .col-actions { width: 160px; }

        /* 状态标签 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-handled {
            background: #d4edda;
            color: #155724;
        }

        .status-ignored {
            background: #f8d7da;
            color: #721c24;
        }

        .warning-level {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .level-high {
            background: #f8d7da;
            color: #721c24;
        }

        .level-medium {
            background: #fff3cd;
            color: #856404;
        }

        .level-low {
            background: #cce5ff;
            color: #004085;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            gap: 5px;
            align-items: center;
        }

        .page-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .page-btn:hover {
            background: #f8f9fa;
            border-color: #3498db;
        }

        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 配置表单样式 */
        .config-form {
            display: grid;
            gap: 20px;
        }

        .config-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .config-section-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }

        .config-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }

        .config-label {
            font-weight: 500;
            color: #555;
            flex: 1;
        }

        .config-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #3498db;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .config-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .help-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 8px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .help-close {
            position: absolute;
            right: 20px;
            top: 20px;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #aaa;
        }

        .help-close:hover {
            color: #333;
        }

        .help-title {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 24px;
        }

        .help-content h4 {
            color: #3498db;
            margin: 20px 0 10px 0;
            font-size: 16px;
        }

        .help-content ul {
            margin-left: 20px;
            margin-bottom: 15px;
        }

        .help-content li {
            margin-bottom: 8px;
            line-height: 1.6;
        }

        .help-content p {
            margin-bottom: 15px;
            line-height: 1.6;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .search-form {
                grid-template-columns: 1fr;
            }

            .action-bar {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .action-left,
            .action-right {
                justify-content: center;
            }

            .data-table {
                font-size: 12px;
            }

            .data-table th,
            .data-table td {
                padding: 8px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="page-title">
                项目预警管理
                <span class="help-icon" onclick="showHelp()">?</span>
            </div>
        </div>

        <!-- 页签导航 -->
        <div class="tab-navigation">
            <div class="tab-nav">
                <div class="tab-nav-item active" onclick="switchTab('todo')">待办</div>
            <div class="tab-nav-item" onclick="switchTab('done')">已办</div>
            </div>

            <!-- 待办页签 -->
        <div class="tab-content active" id="todo">
                <!-- 查询区域 -->
                <div class="search-section">
                    <form class="search-form">
                        <!-- 默认查询栏 -->
                        <div class="form-group">
                            <label class="form-label">预警类型</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="招标公告变更预警">招标公告变更预警</option>
                                <option value="中标候选人公示">中标候选人公示(中标金额大于预算价)</option>
                                <option value="中标候选人公示日期">中标候选人公示(公示日期少于3日)</option>
                                <option value="中标候选人公示变更">中标候选人公示变更</option>
                                <option value="中标结果公告变更">中标结果公告变更</option>
                                <option value="中标结果通知书变更">中标结果通知书变更</option>
                                <option value="多次中标提醒">多次中标提醒</option>
                                <option value="多次投标提醒">多次投标提醒</option>
                                <option value="预算价异常接近">预算价异常接近</option>
                                <option value="标段流标">标段流标</option>
                                <option value="标段终止">标段终止</option>
                                <option value="邀请函变更">邀请函变更</option>
                                <option value="成交结果公告变更">成交结果公告变更</option>
                                <option value="成交结果通知书变更">成交结果通知书变更</option>
                                <option value="采购邀请函变更">采购邀请函变更</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">预警名称</label>
                            <input type="text" class="form-control" placeholder="请输入预警名称">
                        </div>
                        <div class="form-group">
                            <label class="form-label">所属标段</label>
                            <input type="text" class="form-control" placeholder="请输入标段名称">
                        </div>
                        <div class="form-group">
                            <label class="form-label">标段阶段</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="招标准备">招标准备</option>
                                <option value="招标公告">招标公告</option>
                                <option value="投标报名">投标报名</option>
                                <option value="开标评标">开标评标</option>
                                <option value="中标公示">中标公示</option>
                                <option value="合同签订">合同签订</option>
                                <option value="履约执行">履约执行</option>
                                <option value="项目完成">项目完成</option>
                            </select>
                        </div>
                        
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary">查询</button>
                            <button type="button" class="btn btn-secondary">重置</button>
                            <button type="button" class="btn btn-outline" onclick="toggleAdvanced()">高级查询</button>
                        </div>
                    </div>
                    
                    <!-- 高级查询 -->
                    <div class="advanced-search" id="advanced-search">
                        <div class="search-form">
                            <div class="form-group">
                                <label class="form-label">所属项目</label>
                                <input type="text" class="form-control" placeholder="请输入项目名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">发布状态</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="草稿">草稿</option>
                                    <option value="已发布">已发布</option>
                                    <option value="已暂停">已暂停</option>
                                    <option value="已终止">已终止</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">采购方式</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="公开招标">公开招标</option>
                                    <option value="邀请招标">邀请招标</option>
                                    <option value="竞争性谈判">竞争性谈判</option>
                                    <option value="竞争性磋商">竞争性磋商</option>
                                    <option value="单一来源">单一来源</option>
                                    <option value="询价采购">询价采购</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">采购类型</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="货物类">货物类</option>
                                    <option value="工程类">工程类</option>
                                    <option value="服务类">服务类</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">生成时间</label>
                                <input type="date" class="form-control">
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 功能操作栏 -->
                <div class="action-bar">
                    <div class="action-left">
                        <button class="btn btn-secondary">导出数据</button>
                    </div>
                    <div class="action-right">
                        <span class="pagination-info">共 156 条记录</span>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="table-container">
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th class="col-checkbox">
                                        <input type="checkbox" id="selectAll">
                                    </th>
                                    <th class="col-warning-name">预警名称</th>
                                    <th class="col-warning-type">预警类型</th>
                                    <th class="col-section-name">所属标段</th>
                                    <th class="col-warning-level">标段阶段</th>
                                    <th class="col-warning-time">所属项目</th>
                                    <th class="col-warning-content">发布状态</th>
                                    <th class="col-status">采购方式</th>
                                    <th class="col-handler">采购类型</th>
                                    <th class="col-handle-time">生成时间</th>
                                    <th class="col-actions">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><input type="checkbox" class="row-checkbox"></td>
                                    <td><a href="javascript:void(0)" onclick="viewDetail(1)" class="warning-name-link">中标金额超出预算价提醒</a></td>
                                    <td>中标候选人公示</td>
                                    <td>办公设备采购项目-标段一</td>
                                    <td>中标公示</td>
                                    <td>办公设备采购项目</td>
                                    <td>已发布</td>
                                    <td>公开招标</td>
                                    <td>货物类</td>
                                    <td>2024-01-25 14:30</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary">处理</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox" class="row-checkbox"></td>
                                    <td><a href="javascript:void(0)" onclick="viewDetail(2)" class="warning-name-link">招标公告发布后进行重要变更</a></td>
                                    <td>招标公告变更预警</td>
                                    <td>IT设备采购项目-标段二</td>
                                    <td>招标公告</td>
                                    <td>IT设备采购项目</td>
                                    <td>已发布</td>
                                    <td>公开招标</td>
                                    <td>货物类</td>
                                    <td>2024-01-24 16:20</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary">处理</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox" class="row-checkbox"></td>
                                    <td><a href="javascript:void(0)" onclick="viewDetail(3)" class="warning-name-link">公示期少于法定要求</a></td>
                                    <td>中标候选人公示日期</td>
                                    <td>车辆采购项目-标段一</td>
                                    <td>中标公示</td>
                                    <td>车辆采购项目</td>
                                    <td>已发布</td>
                                    <td>公开招标</td>
                                    <td>货物类</td>
                                    <td>2024-01-23 10:15</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary">处理</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox" class="row-checkbox"></td>
                                    <td>多次中标提醒</td>
                                    <td>供应商频繁中标提醒</td>
                                    <td>家具采购项目-标段三</td>
                                    <td>合同签订</td>
                                    <td>家具采购项目</td>
                                    <td>已发布</td>
                                    <td>竞争性谈判</td>
                                    <td>货物类</td>
                                    <td>2024-01-22 09:30</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary">处理</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox" class="row-checkbox"></td>
                                    <td>标段流标</td>
                                    <td>有效投标人不足导致流标</td>
                                    <td>清洁用品采购项目-标段一</td>
                                    <td>开标评标</td>
                                    <td>清洁用品采购项目</td>
                                    <td>已暂停</td>
                                    <td>公开招标</td>
                                    <td>货物类</td>
                                    <td>2024-01-21 15:45</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary">处理</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="pagination">
                        <div class="pagination-info">
                            显示第 1-10 条，共 156 条记录
                        </div>
                        <div class="pagination-controls">
                            <button class="page-btn" disabled>上一页</button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn">2</button>
                            <button class="page-btn">3</button>
                            <button class="page-btn">...</button>
                            <button class="page-btn">16</button>
                            <button class="page-btn">下一页</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 已办页签 -->
        <div class="tab-content" id="done">
                <!-- 查询区域 -->
                <div class="search-section">
                    <div class="search-form">
                        <div class="form-group">
                            <label class="form-label">项目名称</label>
                            <input type="text" class="form-control" placeholder="请输入项目名称">
                        </div>
                        <div class="form-group">
                            <label class="form-label">预警类型</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="公告变更">公告变更</option>
                                <option value="中标异常">中标异常</option>
                                <option value="时间异常">时间异常</option>
                                <option value="金额异常">金额异常</option>
                                <option value="流程异常">流程异常</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">处理时间</label>
                            <input type="date" class="form-control">
                        </div>
                        <div class="form-group">
                            <label class="form-label">处理人</label>
                            <input type="text" class="form-control" placeholder="请输入处理人">
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-primary">查询</button>
                            <button class="btn btn-secondary">重置</button>
                            <button class="btn btn-outline" onclick="toggleAdvancedSearch()">高级查询</button>
                        </div>
                    </div>
                    
                    <!-- 高级查询区域 -->
                    <div class="advanced-search" id="advanced-search-done" style="display: none;">
                        <div class="search-form">
                            <div class="form-group">
                                <label class="form-label">标段编号</label>
                                <input type="text" class="form-control" placeholder="请输入标段编号">
                            </div>
                            <div class="form-group">
                                <label class="form-label">处理结果</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="已处理">已处理</option>
                                    <option value="已忽略">已忽略</option>
                                    <option value="已关闭">已关闭</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">处理时间范围</label>
                                <input type="date" class="form-control" placeholder="开始时间">
                            </div>
                            <div class="form-group">
                                <label class="form-label">至</label>
                                <input type="date" class="form-control" placeholder="结束时间">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能操作栏 -->
                <div class="action-bar">
                    <div class="action-left">
                        <button class="btn btn-outline">导出数据</button>
                    </div>
                    <div class="action-right">
                        <span class="result-count">共找到 <strong>156</strong> 条已办事项</span>
                    </div>
                </div>

                <!-- 数据列表 -->
                <div class="data-section">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th class="col-checkbox">
                                        <input type="checkbox" id="select-all-done">
                                    </th>
                                    <th class="col-warning-name">预警名称</th>
                                    <th>项目名称</th>
                                    <th>标段编号</th>
                                    <th>预警类型</th>
                                    <th>预警级别</th>
                                    <th>预警内容</th>
                                    <th>生成时间</th>
                                    <th>处理时间</th>
                                    <th>处理人</th>
                                    <th>处理结果</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><input type="checkbox"></td>
                                    <td><a href="javascript:void(0)" onclick="viewDetail(101)" class="warning-name-link">招标公告发布后24小时内发生变更</a></td>
                                    <td>XX市政道路改造工程</td>
                                    <td>BD2024001-01</td>
                                    <td><span class="badge badge-warning">公告变更</span></td>
                                    <td><span class="level-medium">中等</span></td>
                                    <td>招标公告发布后24小时内发生变更</td>
                                    <td>2024-01-15 09:30</td>
                                    <td>2024-01-15 14:20</td>
                                    <td>张三</td>
                                    <td><span class="status-processed">已处理</span></td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox"></td>
                                    <td><a href="javascript:void(0)" onclick="viewDetail(102)" class="warning-name-link">中标金额超出预算价15%</a></td>
                                    <td>教学楼建设项目</td>
                                    <td>BD2024002-01</td>
                                    <td><span class="badge badge-danger">金额异常</span></td>
                                    <td><span class="level-high">高</span></td>
                                    <td>中标金额超出预算价15%</td>
                                    <td>2024-01-14 16:45</td>
                                    <td>2024-01-15 10:30</td>
                                    <td>李四</td>
                                    <td><span class="status-ignored">已忽略</span></td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox"></td>
                                    <td><a href="javascript:void(0)" onclick="viewDetail(103)" class="warning-name-link">公示期少于法定要求</a></td>
                                    <td>办公设备采购</td>
                                    <td>BD2024003-01</td>
                                    <td><span class="badge badge-info">时间异常</span></td>
                                    <td><span class="level-low">低</span></td>
                                    <td>公示期少于法定要求</td>
                                    <td>2024-01-13 11:20</td>
                                    <td>2024-01-14 09:15</td>
                                    <td>王五</td>
                                    <td><span class="status-processed">已处理</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="pagination">
                        <div class="pagination-info">
                            显示第 1-10 条，共 156 条记录
                        </div>
                        <div class="pagination-controls">
                            <button class="page-btn" disabled>上一页</button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn">2</button>
                            <button class="page-btn">3</button>
                            <button class="page-btn">...</button>
                            <button class="page-btn">16</button>
                            <button class="page-btn">下一页</button>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="helpModal" class="help-modal">
        <div class="help-content">
            <span class="help-close" onclick="hideHelp()">&times;</span>
            <h3 class="help-title">项目预警管理说明</h3>
            <p>项目预警管理用于监控招标采购过程中的异常情况，及时发现和处理潜在风险。</p>
            
            <h4>预警信息页签</h4>
            <ul>
                <li><strong>预警类型：</strong>系统根据配置规则自动生成的各类预警信息</li>
                <li><strong>预警级别：</strong>高、中、低三个级别，根据风险程度自动判定</li>
                <li><strong>处理状态：</strong>待处理、已处理、已忽略三种状态</li>
                <li><strong>批量操作：</strong>支持批量处理和批量忽略预警信息</li>
            </ul>
            
            <h4>预警配置页签</h4>
            <ul>
                <li><strong>公告变更预警：</strong>监控招标公告、邀请函等文件的变更情况</li>
                <li><strong>中标相关预警：</strong>监控中标候选人公示、中标结果等环节的异常</li>
                <li><strong>成交相关预警：</strong>监控成交结果公告和通知书的变更</li>
                <li><strong>频次提醒：</strong>监控供应商的中标和投标频次</li>
                <li><strong>异常情况预警：</strong>监控预算价异常、流标、终止等情况</li>
            </ul>
            
            <h4>预警规则说明</h4>
            <ul>
                <li><strong>中标金额大于预算价：</strong>当中标金额超出项目预算价时触发</li>
                <li><strong>公示日期少于3日：</strong>当中标候选人公示期少于法定3天时触发</li>
                <li><strong>多次中标/投标：</strong>当供应商在指定时间内频繁中标或投标时触发</li>
                <li><strong>预算价异常接近：</strong>当多个投标报价过于接近预算价时触发</li>
            </ul>
            
            <h4>操作说明</h4>
            <ul>
                <li><strong>处理：</strong>标记预警为已处理状态，需要填写处理说明</li>
                <li><strong>忽略：</strong>标记预警为已忽略状态，适用于误报或不需要处理的情况</li>
                <li><strong>查看：</strong>查看预警的详细信息和处理记录</li>
                <li><strong>导出：</strong>导出预警数据用于分析和报告</li>
            </ul>
            
            <h4>注意事项</h4>
            <ul>
                <li>预警配置修改后立即生效，影响后续的预警生成</li>
                <li>已生成的预警信息不会因配置修改而自动删除</li>
                <li>建议定期检查和处理预警信息，确保采购过程合规</li>
                <li>重要预警信息应及时处理，避免影响项目进度</li>
            </ul>
        </div>
    </div>

    <script>
        // 页签切换
        function switchTab(tabId) {
            // 移除所有活动状态
            document.querySelectorAll('.tab-nav-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 添加当前活动状态
            event.target.classList.add('active');
            document.getElementById(tabId).classList.add('active');
        }

        // 高级查询展开收起
        function toggleAdvanced() {
            const advancedSearch = document.getElementById('advanced-search');
            const button = event.target;
            
            if (advancedSearch.classList.contains('show')) {
                advancedSearch.classList.remove('show');
                button.textContent = '高级查询';
            } else {
                advancedSearch.classList.add('show');
                button.textContent = '收起查询';
            }
        }

        // 全选功能
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.row-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // 显示帮助
        function showHelp() {
            document.getElementById('helpModal').style.display = 'block';
        }

        // 隐藏帮助
        function hideHelp() {
            document.getElementById('helpModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('helpModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

        // 查看详情
        function viewDetail(id) {
            window.open('项目预警管理-详情页.html?id=' + id, '_blank');
        }

        // 模拟数据加载
        document.addEventListener('DOMContentLoaded', function() {
            console.log('项目预警管理页面加载完成');
        });
    </script>
</body>
</html>