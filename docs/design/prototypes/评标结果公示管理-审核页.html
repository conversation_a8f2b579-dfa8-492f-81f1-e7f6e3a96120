<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评标结果公示管理 - 审核 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #fff;
            border-bottom: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
        }

        .help-icon {
            margin-left: 8px;
            width: 16px;
            height: 16px;
            background: #6b7280;
            border-radius: 50%;
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .breadcrumb {
            color: #6b7280;
            font-size: 14px;
        }

        .back-btn {
            background: #6b7280;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
        }

        .back-btn:hover {
            background: #4b5563;
        }

        /* 内容区域 */
        .content-area {
            padding: 24px;
        }

        /* 页签导航 */
        .tab-nav {
            border-bottom: 1px solid #e6e8eb;
            margin-bottom: 24px;
        }

        .tab-list {
            display: flex;
            list-style: none;
        }

        .tab-item {
            margin-right: 32px;
        }

        .tab-link {
            display: block;
            padding: 12px 0;
            color: #6b7280;
            text-decoration: none;
            font-size: 14px;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab-link.active {
            color: #2563eb;
            border-bottom-color: #2563eb;
        }

        .tab-link:hover {
            color: #2563eb;
        }

        /* 页签内容 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 详情区域 */
        .detail-section {
            background: white;
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .section-header {
            background: #f8fafc;
            border-bottom: 1px solid #e6e8eb;
            padding: 15px 20px;
            font-weight: 600;
            color: #1f2937;
            font-size: 14px;
            position: relative;
        }

        .section-header::after {
            content: '';
            position: absolute;
            left: 20px;
            bottom: 0;
            width: 60px;
            height: 2px;
            background: #2563eb;
        }

        .section-content {
            padding: 20px;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .detail-grid.full-width {
            grid-template-columns: 1fr;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-size: 13px;
            color: #6b7280;
            margin-bottom: 6px;
            font-weight: 500;
        }

        .detail-value {
            font-size: 13px;
            color: #1f2937;
            min-height: 20px;
            padding: 4px 0;
        }

        .detail-value.rich-content {
            border: 1px solid #e6e8eb;
            border-radius: 4px;
            padding: 12px;
            background: #f9fafb;
            min-height: 60px;
        }

        .detail-value.rich-content p {
            margin-bottom: 8px;
        }

        .detail-value.rich-content h1 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            text-align: center;
        }

        .detail-value.rich-content h2 {
            font-size: 16px;
            font-weight: 600;
            margin: 16px 0 8px 0;
        }

        .detail-value.rich-content h3 {
            font-size: 14px;
            font-weight: 600;
            margin: 12px 0 6px 0;
        }

        .detail-value.rich-content ul {
            margin-left: 20px;
            margin-bottom: 8px;
        }

        .detail-value.rich-content ol {
            margin-left: 20px;
            margin-bottom: 8px;
        }

        .detail-value.rich-content .signature {
            text-align: right;
            margin-top: 20px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-draft {
            background: #f3f4f6;
            color: #374151;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-approved {
            background: #d1fae5;
            color: #065f46;
        }

        .status-published {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-rejected {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-withdrawn {
            background: #f3f4f6;
            color: #6b7280;
        }

        /* 操作按钮 */
        .action-bar {
            background: #fff;
            border-top: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .btn-group {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: 1px solid transparent;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-outline {
            background: #fff;
            border-color: #d1d5db;
            color: #374151;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        /* 时间线样式 */
        .timeline {
            position: relative;
            padding-left: 24px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e5e7eb;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 24px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -20px;
            top: 6px;
            width: 8px;
            height: 8px;
            background: #3b82f6;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #3b82f6;
        }

        .timeline-content {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 16px;
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .timeline-action {
            font-weight: 600;
            color: #1f2937;
            font-size: 14px;
        }

        .timeline-time {
            color: #6b7280;
            font-size: 12px;
        }

        .timeline-user {
            color: #6b7280;
            font-size: 13px;
            margin-bottom: 4px;
        }

        .timeline-desc {
            color: #374151;
            font-size: 13px;
            line-height: 1.6;
        }

        /* 中标候选人表格 */
        .candidate-table-container {
            width: 100%;
            overflow-x: auto;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }

        .candidate-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            table-layout: fixed;
        }

        .candidate-table th,
        .candidate-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
            word-wrap: break-word;
        }

        .candidate-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
            border-bottom: 2px solid #e8e8e8;
        }

        .candidate-table tbody tr:hover {
            background: #f8f9fa;
        }

        .candidate-table tbody tr:last-child td {
            border-bottom: none;
        }

        /* 文件列表 */
        .file-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e8e8e8;
        }

        .file-name {
            color: #333;
            font-size: 13px;
        }

        .file-download {
            color: #1890ff;
            text-decoration: none;
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 3px;
            transition: all 0.2s;
        }

        .file-download:hover {
            background: #e6f7ff;
            text-decoration: none;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e6e8eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .help-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            color: #6b7280;
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            color: #374151;
        }

        .help-body {
            padding: 24px;
        }

        .help-section {
            margin-bottom: 24px;
        }

        .help-section h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
        }

        .help-section p {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 12px;
        }

        .help-list {
            list-style: none;
            padding-left: 0;
        }

        .help-list li {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 8px;
            padding-left: 16px;
            position: relative;
        }

        .help-list li::before {
            content: '•';
            color: #3b82f6;
            position: absolute;
            left: 0;
        }

        .help-list strong {
            color: #1f2937;
        }

        /* 操作按钮 */
        .action-bar {
            background: #f8fafc;
            border-top: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            bottom: 0;
        }

        .btn-group {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #2563eb;
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
        }

        .btn-success {
            background: #059669;
            color: white;
        }

        .btn-success:hover {
            background: #047857;
        }

        .btn-warning {
            background: #d97706;
            color: white;
        }

        .btn-warning:hover {
            background: #b45309;
        }

        .btn-danger {
            background: #dc2626;
            color: white;
        }

        .btn-danger:hover {
            background: #b91c1c;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-outline {
            background: white;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">
                    评标结果公示管理 - 审核
                    <span class="help-icon" onclick="showHelp()" title="功能说明">?</span>
                </h1>
                <div class="breadcrumb">首页 > 评标结果公示管理 > 公示审核</div>
            </div>
            <button class="back-btn" onclick="goBack()">返回列表</button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 页签导航 -->
            <div class="tab-nav">
                <ul class="tab-list">
                    <li class="tab-item">
                        <a href="#" class="tab-link active" onclick="switchTab('announcement')">公示详情</a>
                    </li>
                    <li class="tab-item">
                        <a href="#" class="tab-link" onclick="switchTab('project')">项目信息</a>
                    </li>
                    <li class="tab-item">
                        <a href="#" class="tab-link" onclick="switchTab('record')">操作记录</a>
                    </li>
                </ul>
            </div>

            <!-- 公示详情页签 -->
            <div id="announcement-tab" class="tab-content active">
                <!-- 项目信息 -->
                <div class="detail-section">
                    <div class="section-header">项目信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">选择标段</div>
                                <div class="detail-value">办公桌椅采购标段</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">计划项目编号</div>
                                <div class="detail-value">JHXM-2024-001</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">所属计划项目名称</div>
                                <div class="detail-value">办公设备采购项目</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购方式</div>
                                <div class="detail-value">公开招标</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">项目业主</div>
                                <div class="detail-value">XX有限公司</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">所属二级公司单位</div>
                                <div class="detail-value">XX分公司</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">代理机构</div>
                                <div class="detail-value">XX招标代理有限公司</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">状态</div>
                                <div class="detail-value">
                                    <span class="status-badge status-pending">待审核</span>
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">提交时间</div>
                                <div class="detail-value">2024-01-25 16:30</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">项目基本情况（建设内容及规模）</div>
                                <div class="detail-value">本项目主要采购办公桌椅等办公家具，用于新办公区域装修配套。要求环保材质，符合国家相关标准。</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">备注</div>
                                <div class="detail-value">要求环保材质，符合国家相关标准。</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">立项决策文件</div>
                                <div class="detail-value">立项决策文件内容详情...</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 标段信息 -->
                <div class="detail-section">
                    <div class="section-header">标段信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">标段名称</div>
                                <div class="detail-value">办公桌椅采购标段</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">标段编号</div>
                                <div class="detail-value">BD-2024-001</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购金额（万元）</div>
                                <div class="detail-value">45.00</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">标段说明</div>
                                <div class="detail-value">采购办公桌椅，包括办公桌、办公椅、会议桌椅等，满足办公需求。本标段采用公开招标方式，预计开标时间为2024年1月25日14:30。</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 评标信息 -->
                <div class="detail-section">
                    <div class="section-header">评标信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">公告标题</div>
                                <div class="detail-value">XX有限公司办公桌椅采购标段评标结果公示</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">是否公示</div>
                                <div class="detail-value">是</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">业主代表姓名</div>
                                <div class="detail-value">张经理</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">投标情况说明</div>
                                <div class="detail-value rich-content">
                                    <p>本次招标共收到5家投标单位的投标文件，经资格审查，5家投标单位均符合投标资格要求。</p>
                                    <p>投标文件均在规定时间内递交，文件格式符合招标文件要求。</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">异常情况说明</div>
                                <div class="detail-value rich-content">
                                    <p>本次评标过程中未发现异常情况，评标过程公正透明。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 投标人信息 -->
                <div class="detail-section">
                    <div class="section-header">投标人信息</div>
                    <div class="section-content">
                        <div class="candidate-table-container">
                            <table class="candidate-table">
                                <thead>
                                    <tr>
                                        <th width="80">序号</th>
                                        <th width="200">投标单位全称</th>
                                        <th width="120">投标形式</th>
                                        <th width="150">投标报价</th>
                                        <th width="100">综合得分</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>1</td>
                                        <td>北京优质办公家具有限公司</td>
                                        <td>金额</td>
                                        <td>42.50万元</td>
                                        <td>92.5</td>
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>上海现代办公设备股份有限公司</td>
                                        <td>金额</td>
                                        <td>43.80万元</td>
                                        <td>89.2</td>
                                    </tr>
                                    <tr>
                                        <td>3</td>
                                        <td>广州精品家具制造有限公司</td>
                                        <td>金额</td>
                                        <td>44.20万元</td>
                                        <td>85.8</td>
                                    </tr>
                                    <tr>
                                        <td>4</td>
                                        <td>深圳现代办公用品有限公司</td>
                                        <td>金额</td>
                                        <td>45.00万元</td>
                                        <td>82.3</td>
                                    </tr>
                                    <tr>
                                        <td>5</td>
                                        <td>天津办公家具制造厂</td>
                                        <td>金额</td>
                                        <td>46.50万元</td>
                                        <td>78.9</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 候选人信息 -->
                <div class="detail-section">
                    <div class="section-header">候选人信息</div>
                    <div class="section-content">
                        <div class="candidate-table-container">
                            <table class="candidate-table">
                                <thead>
                                    <tr>
                                        <th width="80">序号</th>
                                        <th width="200">投标单位全称</th>
                                        <th width="120">投标形式</th>
                                        <th width="150">投标报价</th>
                                        <th width="100">综合得分</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>1</td>
                                        <td>北京优质办公家具有限公司</td>
                                        <td>金额</td>
                                        <td>42.50万元</td>
                                        <td>92.5</td>
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>上海现代办公设备股份有限公司</td>
                                        <td>金额</td>
                                        <td>43.80万元</td>
                                        <td>89.2</td>
                                    </tr>
                                    <tr>
                                        <td>3</td>
                                        <td>广州精品家具制造有限公司</td>
                                        <td>金额</td>
                                        <td>44.20万元</td>
                                        <td>85.8</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 评标说明 -->
                <div class="detail-section">
                    <div class="section-header">评标说明</div>
                    <div class="section-content">
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">评标说明</div>
                                <div class="detail-value rich-content">
                                    <p><strong>评标方法：</strong></p>
                                    <p>本次评标采用综合评分法，满分100分。</p>
                                    
                                    <p><strong>评标标准：</strong></p>
                                    <ul>
                                        <li>技术标准（60分）：产品质量、技术参数、环保认证等</li>
                                        <li>商务标准（40分）：价格合理性、交货期、售后服务等</li>
                                    </ul>
                                    
                                    <p><strong>评标结果：</strong></p>
                                    <p>经评标委员会评审，北京优质办公家具有限公司综合得分最高，推荐为第一中标候选人。该公司产品质量优良，价格合理，具备良好的履约能力和售后服务保障。</p>
                                    
                                    <p><strong>评标委员会意见：</strong></p>
                                    <p>评标过程公正透明，各投标单位均符合基本资格要求，评标结果客观公正。建议按排序确定中标单位。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 附件信息 -->
                <div class="detail-section">
                    <div class="section-header">附件信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">评标报告</div>
                                <div class="detail-value">
                                    <div class="file-list">
                                        <div class="file-item">
                                            <span class="file-name">评标报告.pdf</span>
                                            <a href="#" class="file-download">下载</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">评标记录</div>
                                <div class="detail-value">
                                    <div class="file-list">
                                        <div class="file-item">
                                            <span class="file-name">评标记录表.xlsx</span>
                                            <a href="#" class="file-download">下载</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">其他附件</div>
                                <div class="detail-value">
                                    <div class="file-list">
                                        <div class="file-item">
                                            <span class="file-name">投标文件汇总.zip</span>
                                            <a href="#" class="file-download">下载</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 项目信息页签 -->
            <div id="project-tab" class="tab-content">
                <div class="detail-section">
                    <div class="section-header">项目基本信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">项目名称</div>
                                <div class="detail-value">办公设备采购项目</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">项目编号</div>
                                <div class="detail-value">BGCG-2024-002</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购方式</div>
                                <div class="detail-value">公开招标</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">预算金额</div>
                                <div class="detail-value">500,000.00元</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">项目业主</div>
                                <div class="detail-value">某市政府采购中心</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购金额</div>
                                <div class="detail-value">480,000.00元</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <div class="section-header">评标信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">评标日期</div>
                                <div class="detail-value">2024-01-25</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">评标方法</div>
                                <div class="detail-value">综合评分法</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">投标人数量</div>
                                <div class="detail-value">5家</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">有效投标</div>
                                <div class="detail-value">4家</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">评标委员会</div>
                                <div class="detail-value">5人（技术专家3人，经济专家1人，采购人代表1人）</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <div class="section-header">中标候选人</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">第一中标候选人</div>
                                <div class="detail-value">北京优质办公家具有限公司</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">投标报价</div>
                                <div class="detail-value">42.50万元</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">综合得分</div>
                                <div class="detail-value">92.5分</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">第二中标候选人</div>
                                <div class="detail-value">上海现代办公设备股份有限公司</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">投标报价</div>
                                <div class="detail-value">43.80万元</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">综合得分</div>
                                <div class="detail-value">89.2分</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <div class="section-header">公示信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">公示开始时间</div>
                                <div class="detail-value">2024-01-26 09:00</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">公示结束时间</div>
                                <div class="detail-value">2024-01-29 17:00</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">公示期限</div>
                                <div class="detail-value">3个工作日</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">质疑受理部门</div>
                                <div class="detail-value">某市政府采购中心</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">联系人</div>
                                <div class="detail-value">赵先生</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">联系电话</div>
                                <div class="detail-value">010-12345678</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作记录页签 -->
            <div id="record-tab" class="tab-content">
                <div class="detail-section">
                    <div class="section-header">操作记录</div>
                    <div class="section-content">
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">提交审核</div>
                                        <div class="timeline-time">2024-02-20 16:45:30</div>
                                    </div>
                                    <div class="timeline-user">操作人：李四（评标专员）</div>
                                    <div class="timeline-desc">
                                        提交评标结果公示审核，等待审核人员审核。状态变更：草稿 → 待审核
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">编辑</div>
                                        <div class="timeline-time">2024-02-20 15:20:15</div>
                                    </div>
                                    <div class="timeline-user">操作人：李四</div>
                                    <div class="timeline-desc">
                                        完善评标结果详情和中标候选人信息。状态变更：草稿 → 草稿
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">创建</div>
                                        <div class="timeline-time">2024-02-20 14:30:25</div>
                                    </div>
                                    <div class="timeline-user">操作人：李四</div>
                                    <div class="timeline-desc">
                                        创建评标结果公示，关联项目：办公设备采购项目。状态变更：无 → 草稿
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-bar">
            <div class="btn-group">
                <button class="btn btn-outline" onclick="goBack()">返回列表</button>
                <button class="btn btn-secondary" onclick="printAnnouncement()">打印公示</button>
            </div>
            <div class="btn-group">
                <button class="btn btn-success" onclick="approveAnnouncement()">审核通过</button>
                <button class="btn btn-danger" onclick="rejectAnnouncement()">审核驳回</button>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="help-modal" class="help-modal">
        <div class="help-content">
            <div class="help-header">
                <h3 class="help-title">评标结果公示审核功能说明</h3>
                <button class="close-btn" onclick="hideHelp()">&times;</button>
            </div>
            <div class="help-body">
                <div class="help-section">
                    <h4>功能概述</h4>
                    <p>评标结果公示审核页面用于审核人员对提交的评标结果公示进行审核，确保公示内容的准确性和完整性。</p>
                </div>
                
                <div class="help-section">
                    <h4>页签说明</h4>
                    <ul class="help-list">
                        <li><strong>公示详情：</strong>显示待审核评标结果公示的完整内容</li>
                        <li><strong>项目信息：</strong>显示关联项目的详细信息，便于核对一致性</li>
                        <li><strong>操作记录：</strong>查看公示的操作历史</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>审核说明</h4>
                    <ul class="help-list">
                        <li><strong>审核通过：</strong>评标结果公示内容符合要求，可以发布</li>
                        <li><strong>审核驳回：</strong>公示内容存在问题，需要修改后重新提交</li>
                        <li><strong>审核意见：</strong>必须填写，说明审核的具体意见</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>注意事项</h4>
                    <ul class="help-list">
                        <li>审核前请仔细核对评标结果的准确性</li>
                        <li>确认与项目信息的一致性</li>
                        <li>审核意见将作为历史记录保存</li>
                        <li>审核通过后公示将自动发布</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页签切换
        function switchTab(tabName) {
            // 隐藏所有页签内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有页签链接的激活状态
            const tabLinks = document.querySelectorAll('.tab-link');
            tabLinks.forEach(link => {
                link.classList.remove('active');
            });
            
            // 显示选中的页签内容
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 激活选中的页签链接
            event.target.classList.add('active');
        }

        // 帮助弹窗
        function showHelp() {
            document.getElementById('help-modal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('help-modal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });

        // 业务操作函数
        function goBack() {
            window.location.href = '评标结果公示管理-列表页.html';
        }

        function approveAnnouncement() {
            const reason = prompt('请输入审核通过的意见：');
            if (reason && reason.trim()) {
                console.log('审核通过', reason);
                alert('审核通过成功！评标结果公示已发布。');
                // 跳转回列表页
                window.location.href = '评标结果公示管理-列表页.html';
            } else if (reason !== null) {
                alert('请填写审核意见');
            }
        }

        function rejectAnnouncement() {
             const reason = prompt('请输入驳回的具体原因：');
             if (reason && reason.trim()) {
                 console.log('审核驳回', reason);
                 alert('审核驳回成功！已通知创建人修改。');
                 // 跳转回列表页
                 window.location.href = '评标结果公示管理-列表页.html';
             } else if (reason !== null) {
                 alert('请填写驳回原因');
             }
         }

         function printAnnouncement() {
             console.log('打印评标结果公示');
             window.print();
         }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('评标结果公示审核页面加载完成');
        });
    </script>
</body>
</html>