<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机构人员管理 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 页面头部 */
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .help-icon {
            cursor: pointer;
            color: #3498db;
            font-size: 18px;
            padding: 4px;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .help-icon:hover {
            background-color: #e3f2fd;
        }



        /* 查询区域 */
        .search-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 13px;
            color: #555;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-control {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .btn-group {
            display: flex;
            gap: 10px;
            align-items: end;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-outline {
            background: white;
            color: #3498db;
            border: 1px solid #3498db;
        }

        .btn-outline:hover {
            background: #3498db;
            color: white;
        }

        /* 高级查询 */
        .advanced-search {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        /* 功能操作栏 */
        .action-bar {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .action-left {
            display: flex;
            gap: 10px;
        }

        .action-right {
            color: #7f8c8d;
            font-size: 13px;
        }

        .result-count {
            font-weight: 500;
        }

        .result-count strong {
            color: #2c3e50;
        }

        /* 数据表格 */
        .data-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-container {
            overflow-x: auto;
            max-width: 100%;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            min-width: 1400px;
        }

        .data-table th,
        .data-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #eee;
            font-size: 13px;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        /* 列宽设置 */
        .col-checkbox { width: 50px; }
        .col-fixed { width: 150px; }
        .col-contact { width: 150px; }
        .col-agency { width: 250px; }
        .col-code { width: 180px; }
        .col-time { width: 160px; }
        .col-action { width: 120px; }

        /* 冻结列 */
        .col-checkbox,
        .col-fixed {
            position: sticky;
            left: 0;
            background: white;
            z-index: 5;
        }

        .col-checkbox {
            left: 0;
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
        }

        .col-fixed {
            left: 50px;
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
        }

        .col-action {
            position: sticky;
            right: 0;
            background: white;
            z-index: 5;
            box-shadow: -2px 0 4px rgba(0,0,0,0.1);
        }

        .data-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        /* 人员姓名链接 */
        .staff-name-link {
            color: #3498db;
            text-decoration: none;
            font-weight: 500;
        }

        .staff-name-link:hover {
            text-decoration: underline;
        }

        /* 状态标签 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .status-active {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .status-inactive {
            background: #ffebee;
            color: #c62828;
        }

        .status-frozen {
            background: #fff3e0;
            color: #f57c00;
        }

        .status-pending {
            background: #e3f2fd;
            color: #1976d2;
        }

        /* 列宽调整 */
        .col-status { width: 120px; }

        /* 证书标签 */
        .cert-badge {
            padding: 3px 6px;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 500;
            margin: 1px;
            display: inline-block;
        }

        .cert-primary {
            background: #e3f2fd;
            color: #1976d2;
        }

        .cert-secondary {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .cert-success {
            background: #e8f5e8;
            color: #2e7d32;
        }

        /* 操作按钮 */
        .btn-action {
            background: none;
            border: none;
            cursor: pointer;
            padding: 4px 8px;
            margin: 0 2px;
            border-radius: 3px;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .btn-action:hover {
            background-color: #e3f2fd;
        }

        /* 分页 */
        .pagination-wrapper {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #eee;
        }

        .pagination-info {
            color: #7f8c8d;
            font-size: 13px;
        }

        .pagination {
            display: flex;
            gap: 5px;
        }

        .page-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            font-size: 13px;
            transition: all 0.3s;
        }

        .page-btn:hover {
            background: #f8f9fa;
        }

        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .help-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .help-close {
            position: absolute;
            right: 15px;
            top: 15px;
            font-size: 24px;
            cursor: pointer;
            color: #aaa;
        }

        .help-close:hover {
            color: #333;
        }

        .help-title {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 20px;
        }

        .help-content h4 {
            color: #34495e;
            margin: 15px 0 10px 0;
            font-size: 16px;
        }

        .help-content ul {
            margin-left: 20px;
            margin-bottom: 15px;
        }

        .help-content li {
            margin-bottom: 8px;
            line-height: 1.6;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .search-form {
                grid-template-columns: 1fr;
            }

            .action-bar {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .action-left {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">
                    机构人员管理
                    <span class="help-icon" onclick="showHelp()" title="查看帮助">❓</span>
                </h1>

            </div>
        </div>

        <!-- 查询区域 -->
        <div class="search-section">
            <div class="search-form">
                <div class="form-group">
                    <label class="form-label">人员名称</label>
                    <input type="text" class="form-control" placeholder="请输入人员名称">
                </div>
                <div class="form-group">
                    <label class="form-label">联系方式</label>
                    <input type="text" class="form-control" placeholder="请输入联系方式">
                </div>
                <div class="form-group">
                    <label class="form-label">所属机构</label>
                    <select class="form-control">
                        <option value="">全部机构</option>
                        <option value="1">中建工程咨询有限公司</option>
                        <option value="2">华信工程管理集团</option>
                        <option value="3">北京建设工程咨询公司</option>
                        <option value="4">上海工程项目管理有限公司</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">企业代码</label>
                    <input type="text" class="form-control" placeholder="请输入企业代码">
                </div>
                <div class="btn-group">
                    <button class="btn btn-primary">查询</button>
                    <button class="btn btn-secondary">重置</button>
                    <button class="btn btn-outline" onclick="toggleAdvancedSearch()">高级查询</button>
                </div>
            </div>
            
            <!-- 高级查询区域 -->
            <div class="advanced-search" id="advanced-search" style="display: none;">
                <div class="search-form">
                    <div class="form-group">
                        <label class="form-label">创建时间</label>
                        <input type="date" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label">至</label>
                        <input type="date" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label">更新时间</label>
                        <input type="date" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label">至</label>
                        <input type="date" class="form-control">
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能操作栏 -->
        <div class="action-bar">
            <div class="action-left">
                <button class="btn btn-primary" onclick="createStaff()">新增人员</button>
                <button class="btn btn-default">批量导入</button>
                <button class="btn btn-success">导出数据</button>
                <button class="btn btn-danger">批量删除</button>
                <button class="btn btn-info" onclick="batchAuthorize()">批量授权</button>
                <button class="btn btn-warning" onclick="batchFreeze()">批量冻结</button>
                <button class="btn btn-success" onclick="batchUnfreeze()">批量解冻</button>
            </div>
            <div class="action-right">
                <span class="result-count">共找到 <strong>256</strong> 条记录</span>
            </div>
        </div>

        <!-- 数据列表 -->
        <div class="data-section">
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th class="col-checkbox">
                                <input type="checkbox" id="select-all">
                            </th>
                            <th class="col-fixed">人员名称</th>
                            <th class="col-contact">联系方式</th>
                            <th class="col-agency">所属机构</th>
                            <th class="col-code">企业代码</th>
                            <th class="col-status">权限状态</th>
                            <th class="col-time">更新时间</th>
                            <th class="col-time">创建时间</th>
                            <th class="col-action">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="col-checkbox"><input type="checkbox"></td>
                            <td class="col-fixed"><a href="javascript:void(0)" onclick="viewDetail(1)" class="staff-name-link">张工程师</a></td>
                            <td>13800138001</td>
                            <td>中建工程咨询有限公司</td>
                            <td>91110000123456789X</td>
                            <td><span class="status-badge status-active">已授权</span></td>
                            <td>2024-01-15 14:30</td>
                            <td>2020-03-15 09:00</td>
                            <td class="col-action">
                                <div class="operation-buttons">
                                    <button class="op-btn op-btn-info" title="授权管理" onclick="manageAuth(1)">授权</button>
                                    <button class="op-btn op-btn-warning" title="冻结权限" onclick="freezeAuth(1)">冻结</button>
                                    <button class="op-btn op-btn-warning" title="编辑">编辑</button>
                                    <button class="op-btn op-btn-danger" title="删除">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-checkbox"><input type="checkbox"></td>
                            <td class="col-fixed"><a href="javascript:void(0)" onclick="viewDetail(2)" class="staff-name-link">李建筑师</a></td>
                            <td>13900139002</td>
                            <td>华信工程管理集团</td>
                            <td>91110000987654321Y</td>
                            <td><span class="status-badge status-frozen">权限冻结</span></td>
                            <td>2024-01-14 16:45</td>
                            <td>2019-07-20 10:30</td>
                            <td class="col-action">
                                <div class="operation-buttons">
                                    <button class="op-btn op-btn-info" title="授权管理" onclick="manageAuth(2)">授权</button>
                                    <button class="op-btn op-btn-success" title="解冻权限" onclick="unfreezeAuth(2)">解冻</button>
                                    <button class="op-btn op-btn-warning" title="编辑">编辑</button>
                                    <button class="op-btn op-btn-danger" title="删除">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-checkbox"><input type="checkbox"></td>
                            <td class="col-fixed"><a href="javascript:void(0)" onclick="viewDetail(3)" class="staff-name-link">王监理</a></td>
                            <td>13700137003</td>
                            <td>北京建设工程咨询公司</td>
                            <td>91110000456789123Z</td>
                            <td><span class="status-badge status-inactive">未授权</span></td>
                            <td>2024-01-13 10:20</td>
                            <td>2024-01-10 14:15</td>
                            <td class="col-action">
                                <div class="operation-buttons">
                                    <button class="op-btn op-btn-info" title="授权管理" onclick="manageAuth(3)">授权</button>
                                    <button class="op-btn op-btn-warning" title="冻结权限" onclick="freezeAuth(3)">冻结</button>
                                    <button class="op-btn op-btn-warning" title="编辑">编辑</button>
                                    <button class="op-btn op-btn-danger" title="删除">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-checkbox"><input type="checkbox"></td>
                            <td class="col-fixed"><a href="javascript:void(0)" onclick="viewDetail(4)" class="staff-name-link">陈造价师</a></td>
                            <td>13600136004</td>
                            <td>上海工程项目管理有限公司</td>
                            <td>91310000789123456A</td>
                            <td><span class="status-badge status-active">已授权</span></td>
                            <td>2024-01-12 09:15</td>
                            <td>2021-09-01 11:20</td>
                            <td class="col-action">
                                <div class="operation-buttons">
                                    <button class="op-btn op-btn-info" title="授权管理" onclick="manageAuth(4)">授权</button>
                                    <button class="op-btn op-btn-warning" title="冻结权限" onclick="freezeAuth(4)">冻结</button>
                                    <button class="op-btn op-btn-warning" title="编辑">编辑</button>
                                    <button class="op-btn op-btn-danger" title="删除">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-checkbox"><input type="checkbox"></td>
                            <td class="col-fixed"><a href="javascript:void(0)" onclick="viewDetail(5)" class="staff-name-link">刘助理</a></td>
                            <td>13500135005</td>
                            <td>中建工程咨询有限公司</td>
                            <td>91110000123456789X</td>
                            <td><span class="status-badge status-inactive">未授权</span></td>
                            <td>2024-01-11 15:40</td>
                            <td>2022-05-15 16:30</td>
                            <td class="col-action">
                                <div class="operation-buttons">
                                    <button class="op-btn op-btn-info" title="授权管理" onclick="manageAuth(5)">授权</button>
                                    <button class="op-btn op-btn-warning" title="冻结权限" onclick="freezeAuth(5)">冻结</button>
                                    <button class="op-btn op-btn-warning" title="编辑">编辑</button>
                                    <button class="op-btn op-btn-danger" title="删除">删除</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="pagination-wrapper">
                <div class="pagination-info">
                    显示第 1-20 条，共 256 条记录
                </div>
                <div class="pagination">
                    <button class="page-btn">上一页</button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">3</button>
                    <button class="page-btn">...</button>
                    <button class="page-btn">13</button>
                    <button class="page-btn">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="helpModal" class="help-modal">
        <div class="help-content">
            <span class="help-close" onclick="hideHelp()">&times;</span>
            <h3 class="help-title">机构人员管理说明</h3>
            <p>机构人员管理用于维护招标代理机构的人员信息，包括人员资质、证书等。</p>
            
            <h4>功能说明</h4>
            <ul>
                <li><strong>新增人员：</strong>添加新的机构人员信息</li>
                <li><strong>批量导入：</strong>通过Excel文件批量导入人员信息</li>
                <li><strong>导出数据：</strong>将人员列表导出为Excel文件</li>
                <li><strong>批量删除：</strong>选择多个人员进行批量删除操作</li>
            </ul>
            
            <h4>查询条件</h4>
            <ul>
                <li><strong>人员姓名：</strong>支持模糊查询人员姓名</li>
                <li><strong>所属机构：</strong>按代理机构筛选人员</li>
                <li><strong>职务：</strong>按职务类型筛选</li>
                <li><strong>联系方式：</strong>按联系电话或邮箱查询</li>
                <li><strong>人员状态：</strong>在职、离职、待审核状态筛选</li>
                <li><strong>证书类型：</strong>按持有的专业证书筛选</li>
            </ul>
            
            <h4>人员状态</h4>
            <ul>
                <li><strong>在职：</strong>正常在职的人员</li>
                <li><strong>离职：</strong>已离职的人员</li>
                <li><strong>待审核：</strong>新入职待审核的人员</li>
            </ul>
            
            <h4>证书管理</h4>
            <ul>
                <li><strong>注册工程师：</strong>各类注册工程师证书</li>
                <li><strong>注册建筑师：</strong>一级、二级注册建筑师</li>
                <li><strong>监理工程师：</strong>注册监理工程师证书</li>
                <li><strong>造价工程师：</strong>注册造价工程师证书</li>
            </ul>
        </div>
    </div>

    <script>
        // 高级查询切换
        function toggleAdvancedSearch() {
            const advancedSearch = document.getElementById('advanced-search');
            if (advancedSearch.style.display === 'none') {
                advancedSearch.style.display = 'block';
            } else {
                advancedSearch.style.display = 'none';
            }
        }

        // 全选功能
        document.getElementById('select-all').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // 查看详情
        function viewDetail(id) {
            // 跳转到详情页
            window.location.href = `机构人员管理-详情页.html?id=${id}`;
        }

        // 新增人员
        function createStaff() {
            window.location.href = '机构人员管理-新建编辑页.html';
        }

        // 帮助弹窗
        function showHelp() {
            document.getElementById('helpModal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('helpModal').style.display = 'none';
        }

        // 授权管理
        function manageAuth(id) {
            alert(`正在为人员ID: ${id} 进行授权管理`);
            // 这里可以打开授权管理弹窗或跳转到授权页面
        }

        // 冻结权限
        function freezeAuth(id) {
            if (confirm('确定要冻结该人员的权限吗？')) {
                alert(`人员ID: ${id} 的权限已冻结`);
                // 这里可以调用API更新权限状态
                location.reload(); // 刷新页面显示最新状态
            }
        }

        // 解冻权限
        function unfreezeAuth(id) {
            if (confirm('确定要解冻该人员的权限吗？')) {
                alert(`人员ID: ${id} 的权限已解冻`);
                // 这里可以调用API更新权限状态
                location.reload(); // 刷新页面显示最新状态
            }
        }

        // 批量授权
        function batchAuthorize() {
            const checkedBoxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');
            if (checkedBoxes.length === 0) {
                alert('请先选择要授权的人员');
                return;
            }
            if (confirm(`确定要对选中的 ${checkedBoxes.length} 个人员进行批量授权吗？`)) {
                alert(`已对 ${checkedBoxes.length} 个人员进行批量授权`);
                // 这里可以调用API进行批量授权
                location.reload();
            }
        }

        // 批量冻结
        function batchFreeze() {
            const checkedBoxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');
            if (checkedBoxes.length === 0) {
                alert('请先选择要冻结权限的人员');
                return;
            }
            if (confirm(`确定要冻结选中的 ${checkedBoxes.length} 个人员的权限吗？`)) {
                alert(`已冻结 ${checkedBoxes.length} 个人员的权限`);
                // 这里可以调用API进行批量冻结
                location.reload();
            }
        }

        // 批量解冻
        function batchUnfreeze() {
            const checkedBoxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');
            if (checkedBoxes.length === 0) {
                alert('请先选择要解冻权限的人员');
                return;
            }
            if (confirm(`确定要解冻选中的 ${checkedBoxes.length} 个人员的权限吗？`)) {
                alert(`已解冻 ${checkedBoxes.length} 个人员的权限`);
                // 这里可以调用API进行批量解冻
                location.reload();
            }
        }

        // 点击弹窗外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('helpModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>