<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中标结果公示管理 - 详情 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #fff;
            border-bottom: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
        }

        .help-icon {
            margin-left: 8px;
            width: 16px;
            height: 16px;
            background: #6b7280;
            border-radius: 50%;
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .breadcrumb {
            color: #6b7280;
            font-size: 14px;
        }

        .back-btn {
            background: #6b7280;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
        }

        .back-btn:hover {
            background: #4b5563;
        }

        /* 内容区域 */
        .content-area {
            padding: 24px;
        }

        /* 页签导航 */
        .tab-nav {
            border-bottom: 1px solid #e6e8eb;
            margin-bottom: 24px;
        }

        .tab-list {
            display: flex;
            list-style: none;
        }

        .tab-item {
            margin-right: 32px;
        }

        .tab-link {
            display: block;
            padding: 12px 0;
            color: #6b7280;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab-link:hover {
            color: #2563eb;
        }

        .tab-link.active {
            color: #2563eb;
            border-bottom-color: #2563eb;
        }

        /* 页签内容 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 详情区域 */
        .detail-section {
            background: white;
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .section-header {
            background: #f8fafc;
            border-bottom: 1px solid #e6e8eb;
            padding: 15px 20px;
            font-weight: 600;
            color: #1f2937;
            font-size: 14px;
            position: relative;
        }

        .section-header::after {
            content: '';
            position: absolute;
            left: 20px;
            bottom: 0;
            width: 60px;
            height: 2px;
            background: #2563eb;
        }

        .section-content {
            padding: 20px;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .detail-grid.full-width {
            grid-template-columns: 1fr;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-size: 13px;
            color: #6b7280;
            margin-bottom: 6px;
            font-weight: 500;
        }

        .detail-value {
            font-size: 14px;
            color: #1f2937;
            word-break: break-all;
        }

        .detail-value.empty {
            color: #9ca3af;
            font-style: italic;
        }

        /* 状态标签 */
        .status-tag {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-tag.draft {
            background: #f3f4f6;
            color: #6b7280;
        }

        .status-tag.pending {
            background: #fef3c7;
            color: #d97706;
        }

        .status-tag.approved {
            background: #d1fae5;
            color: #059669;
        }

        .status-tag.published {
            background: #dbeafe;
            color: #2563eb;
        }

        .status-tag.rejected {
            background: #fee2e2;
            color: #dc2626;
        }

        /* 富文本内容 */
        .rich-content {
            line-height: 1.8;
            color: #374151;
        }

        .rich-content h1, .rich-content h2, .rich-content h3, .rich-content h4 {
            margin: 16px 0 8px 0;
            color: #1f2937;
        }

        .rich-content p {
            margin-bottom: 12px;
        }

        .rich-content ul, .rich-content ol {
            margin: 12px 0;
            padding-left: 24px;
        }

        .rich-content li {
            margin-bottom: 6px;
        }

        .rich-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 16px 0;
        }

        .rich-content table th, .rich-content table td {
            border: 1px solid #e6e8eb;
            padding: 8px 12px;
            text-align: left;
        }

        .rich-content table th {
            background: #f8fafc;
            font-weight: 600;
        }

        /* 文件列表 */
        .file-list {
            list-style: none;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-name {
            flex: 1;
            color: #374151;
            font-size: 13px;
        }

        .file-download {
            color: #2563eb;
            text-decoration: none;
            font-size: 13px;
            padding: 4px 8px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .file-download:hover {
            background: #f3f4f6;
        }

        /* 操作记录时间线 */
        .operation-timeline {
            position: relative;
            padding-left: 24px;
        }

        .operation-timeline::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e6e8eb;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 24px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -20px;
            top: 6px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #2563eb;
        }

        .timeline-content {
            background: #f8fafc;
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            padding: 12px 16px;
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .timeline-action {
            font-weight: 600;
            color: #1f2937;
            font-size: 14px;
        }

        .timeline-time {
            color: #6b7280;
            font-size: 12px;
        }

        .timeline-user {
            color: #374151;
            font-size: 13px;
            margin-bottom: 4px;
        }

        .timeline-desc {
            color: #6b7280;
            font-size: 13px;
        }

        /* 操作记录表格 */
        .record-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .record-table th {
            background: #f8fafc;
            border: 1px solid #e6e8eb;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            color: #374151;
        }

        .record-table td {
            border: 1px solid #e6e8eb;
            padding: 12px 8px;
            color: #1f2937;
        }

        .record-table tr:nth-child(even) {
            background: #f9fafb;
        }

        /* 操作按钮 */
        .action-bar {
            background: #f8fafc;
            border-top: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            bottom: 0;
        }

        .btn-group {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #2563eb;
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
        }

        .btn-success {
            background: #059669;
            color: white;
        }

        .btn-success:hover {
            background: #047857;
        }

        .btn-warning {
            background: #d97706;
            color: white;
        }

        .btn-warning:hover {
            background: #b45309;
        }

        .btn-danger {
            background: #dc2626;
            color: white;
        }

        .btn-danger:hover {
            background: #b91c1c;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-outline {
            background: white;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            padding: 24px;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e6e8eb;
        }

        .help-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #6b7280;
        }

        .help-body {
            color: #374151;
            line-height: 1.6;
        }

        .help-section {
            margin-bottom: 16px;
        }

        .help-section h4 {
            color: #1f2937;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .help-section p {
            margin-bottom: 8px;
            font-size: 13px;
        }

        .help-list {
            list-style: none;
            padding-left: 16px;
        }

        .help-list li {
            margin-bottom: 4px;
            font-size: 13px;
            position: relative;
        }

        .help-list li::before {
            content: '•';
            color: #2563eb;
            position: absolute;
            left: -12px;
        }

        /* 候选人信息样式 */
        .candidate-info-section {
            margin-top: 10px;
        }
        
        .selected-winners {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 15px;
        }
        
        .winner-info {
            font-weight: 600;
            color: #0369a1;
        }
        
        .candidate-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            overflow: hidden;
        }
        
        .candidate-table th {
            background: #f8fafc;
            padding: 12px 8px;
            text-align: center;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .candidate-table td {
            padding: 12px 8px;
            text-align: center;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .candidate-table tbody tr:hover {
            background: #f8fafc;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .detail-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .detail-grid {
                grid-template-columns: 1fr;
            }
            
            .action-bar {
                flex-direction: column;
                gap: 12px;
            }
            
            .btn-group {
                width: 100%;
                justify-content: center;
            }
            
            .tab-list {
                flex-wrap: wrap;
            }
            
            .tab-item {
                margin-right: 16px;
                margin-bottom: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="page-title">
                中标结果公示管理 - 详情
                <span class="help-icon" onclick="showHelp()" title="功能说明">?</span>
            </div>
            <div style="display: flex; align-items: center; gap: 16px;">
                <div class="breadcrumb">
                    首页 > 招标管理 > 中标结果公示管理 > 详情
                </div>
                <button class="back-btn" onclick="goBack()">返回列表</button>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 页签导航 -->
            <nav class="tab-nav">
                <ul class="tab-list">
                    <li class="tab-item">
                        <a href="#" class="tab-link active" onclick="switchTab(event, 'announcement-detail')">公告详情</a>
                    </li>
                    <li class="tab-item">
                        <a href="#" class="tab-link" onclick="switchTab(event, 'project-info')">项目信息</a>
                    </li>
                    <li class="tab-item">
                        <a href="#" class="tab-link" onclick="switchTab(event, 'operation-records')">操作记录</a>
                    </li>
                </ul>
            </nav>

            <!-- 公告详情页签 -->
            <div id="announcement-detail" class="tab-content active">
                <!-- 项目信息 -->
                <div class="detail-section">
                    <div class="section-header">项目信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">计划项目编号</div>
                                <div class="detail-value">JHXM-2024-001</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">所属计划项目名称</div>
                                <div class="detail-value">办公用品采购项目</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购方式</div>
                                <div class="detail-value">公开招标</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">项目业主</div>
                                <div class="detail-value">XX有限公司</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">所属二级公司单位</div>
                                <div class="detail-value">XX分公司</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">代理机构</div>
                                <div class="detail-value">XX招标代理有限公司</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">立项决策文件</div>
                                <div class="detail-value">项目立项申请书.pdf</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">立项决策日期</div>
                                <div class="detail-value">2024-01-10</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">项目总预算（万元）</div>
                                <div class="detail-value">25.00</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 标段信息 -->
                <div class="detail-section">
                    <div class="section-header">标段信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">标段名称</div>
                                <div class="detail-value">办公桌椅采购</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">标段编号</div>
                                <div class="detail-value">BD-2024-001</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购金额（万元）</div>
                                <div class="detail-value">23.50</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">标段说明</div>
                                <div class="detail-value">采购办公桌椅，包括办公桌、办公椅、会议桌椅等，要求产品质量可靠，符合环保要求。</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 中标信息 -->
                <div class="detail-section">
                    <div class="section-header">中标信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">公告标题</div>
                                <div class="detail-value">XX有限公司办公桌椅采购中标结果公示</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">是否公示</div>
                                <div class="detail-value">
                                    <span class="status-tag published">是</span>
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">成交金额（万元）</div>
                                <div class="detail-value">23.50</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">中选结果公示开始时间</div>
                                <div class="detail-value">2024-01-26 09:00:00</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">中选结果公示结束时间</div>
                                <div class="detail-value">2024-01-31 17:00:00</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">业主代表姓名</div>
                                <div class="detail-value">张三</div>
                            </div>
                        </div>
                        
                        <!-- 候选人信息表格 -->
                        <div class="detail-item" style="margin-top: 20px;">
                            <div class="detail-label">候选人信息</div>
                            <div class="detail-value">
                                <div class="candidate-info-section">
                                    <div class="selected-winners">
                                        <div class="winner-info">中标人：XX家具有限公司</div>
                                    </div>
                                    <table class="candidate-table">
                                        <thead>
                                            <tr>
                                                <th width="60">排序</th>
                                                <th>投标单位全称</th>
                                                <th width="100">投标形式</th>
                                                <th width="120">投标报价</th>
                                                <th width="100">综合得分</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr style="background: #f0f9ff;">
                                                <td>1</td>
                                                <td>XX家具有限公司</td>
                                                <td>金额</td>
                                                <td>23.50万元</td>
                                                <td>95.8</td>
                                            </tr>
                                            <tr>
                                                <td>2</td>
                                                <td>YY办公用品有限公司</td>
                                                <td>金额</td>
                                                <td>24.20万元</td>
                                                <td>92.5</td>
                                            </tr>
                                            <tr>
                                                <td>3</td>
                                                <td>ZZ家具制造有限公司</td>
                                                <td>金额</td>
                                                <td>24.80万元</td>
                                                <td>89.2</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width" style="margin-top: 20px;">
                            <div class="detail-item">
                                <div class="detail-label">中标情况说明</div>
                                <div class="detail-value">经评标委员会评审，XX家具有限公司综合得分最高，符合中标条件，确定为中标人。中标产品质量可靠，价格合理，具有完善的售后服务体系。</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">异常情况说明</div>
                                <div class="detail-value empty">无</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 附件信息 -->
                <div class="detail-section">
                    <div class="section-header">附件信息</div>
                    <div class="section-content">
                        <div class="detail-item">
                            <div class="detail-label">中标结果公示文件</div>
                            <div class="detail-value">
                                <div class="file-list">
                                    <div class="file-item">
                                        <i class="file-icon">📄</i>
                                        <span class="file-name">中标结果公示.pdf</span>
                                        <a href="#" class="download-link">下载</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="detail-item">
                            <div class="detail-label">评标报告</div>
                            <div class="detail-value">
                                <div class="file-list">
                                    <div class="file-item">
                                        <i class="file-icon">📄</i>
                                        <span class="file-name">评标报告.pdf</span>
                                        <a href="#" class="download-link">下载</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 项目信息页签 -->
            <div id="project-info" class="tab-content">
                <!-- 项目信息 -->
                <div class="detail-section">
                    <div class="section-header">项目信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">计划项目编号</div>
                                <div class="detail-value">JHXM-2024-001</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">所属计划项目名称</div>
                                <div class="detail-value">办公用品采购项目</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购方式</div>
                                <div class="detail-value">公开招标</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">项目业主</div>
                                <div class="detail-value">XX有限公司</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">所属二级公司单位</div>
                                <div class="detail-value">XX分公司</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">代理机构</div>
                                <div class="detail-value">XX招标代理有限公司</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">立项决策文件</div>
                                <div class="detail-value">项目立项申请书.pdf</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">立项决策日期</div>
                                <div class="detail-value">2024-01-10</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">项目总预算（万元）</div>
                                <div class="detail-value">25.00</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 标段信息 -->
                <div class="detail-section">
                    <div class="section-header">标段信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">标段名称</div>
                                <div class="detail-value">办公桌椅采购</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">标段编号</div>
                                <div class="detail-value">BD-2024-001</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购金额（万元）</div>
                                <div class="detail-value">23.50</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">标段说明</div>
                                <div class="detail-value">采购办公桌椅，包括办公桌、办公椅、会议桌椅等，要求产品质量可靠，符合环保要求。</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 中标信息 -->
                <div class="detail-section">
                    <div class="section-header">中标信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">公告标题</div>
                                <div class="detail-value">XX有限公司办公桌椅采购中标结果公示</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">是否公示</div>
                                <div class="detail-value">
                                    <span class="status-tag published">是</span>
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">成交金额（万元）</div>
                                <div class="detail-value">23.50</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">中选结果公示开始时间</div>
                                <div class="detail-value">2024-01-26 09:00:00</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">中选结果公示结束时间</div>
                                <div class="detail-value">2024-01-31 17:00:00</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">业主代表姓名</div>
                                <div class="detail-value">张三</div>
                            </div>
                        </div>
                        
                        <!-- 候选人信息表格 -->
                        <div class="detail-item" style="margin-top: 20px;">
                            <div class="detail-label">候选人信息</div>
                            <div class="detail-value">
                                <div class="candidate-info-section">
                                    <div class="selected-winners">
                                        <div class="winner-info">中标人：XX家具有限公司</div>
                                    </div>
                                    <table class="candidate-table">
                                        <thead>
                                            <tr>
                                                <th width="60">排序</th>
                                                <th>投标单位全称</th>
                                                <th width="100">投标形式</th>
                                                <th width="120">投标报价</th>
                                                <th width="100">综合得分</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr style="background: #f0f9ff;">
                                                <td>1</td>
                                                <td>XX家具有限公司</td>
                                                <td>金额</td>
                                                <td>23.50万元</td>
                                                <td>95.8</td>
                                            </tr>
                                            <tr>
                                                <td>2</td>
                                                <td>YY办公用品有限公司</td>
                                                <td>金额</td>
                                                <td>24.20万元</td>
                                                <td>92.5</td>
                                            </tr>
                                            <tr>
                                                <td>3</td>
                                                <td>ZZ家具制造有限公司</td>
                                                <td>金额</td>
                                                <td>24.80万元</td>
                                                <td>89.2</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width" style="margin-top: 20px;">
                            <div class="detail-item">
                                <div class="detail-label">中标情况说明</div>
                                <div class="detail-value">经评标委员会评审，XX家具有限公司综合得分最高，符合中标条件，确定为中标人。中标产品质量可靠，价格合理，具有完善的售后服务体系。</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">异常情况说明</div>
                                <div class="detail-value empty">无</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 附件信息 -->
                <div class="detail-section">
                    <div class="section-header">附件信息</div>
                    <div class="section-content">
                        <div class="detail-item">
                            <div class="detail-label">中标结果公示文件</div>
                            <div class="detail-value">
                                <div class="file-list">
                                    <div class="file-item">
                                        <i class="file-icon">📄</i>
                                        <span class="file-name">中标结果公示.pdf</span>
                                        <a href="#" class="download-link">下载</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="detail-item">
                            <div class="detail-label">评标报告</div>
                            <div class="detail-value">
                                <div class="file-list">
                                    <div class="file-item">
                                        <i class="file-icon">📄</i>
                                        <span class="file-name">评标报告.pdf</span>
                                        <a href="#" class="download-link">下载</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作记录页签 -->
            <div id="operation-records" class="tab-content">
                <div class="detail-section">
                    <div class="section-header">操作时间线</div>
                    <div class="section-content">
                        <div class="operation-timeline">
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <span class="timeline-action">发布</span>
                                        <span class="timeline-time">2024-01-15 09:30:00</span>
                                    </div>
                                    <div class="timeline-user">操作人：系统管理员</div>
                                    <div class="timeline-desc">公告已成功发布，开始接受投标</div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <span class="timeline-action">审核通过</span>
                                        <span class="timeline-time">2024-01-15 09:15:00</span>
                                    </div>
                                    <div class="timeline-user">操作人：审核员 - 张三</div>
                                    <div class="timeline-desc">公告内容审核通过，符合发布要求</div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <span class="timeline-action">部门审批</span>
                                        <span class="timeline-time">2024-01-14 16:20:00</span>
                                    </div>
                                    <div class="timeline-user">操作人：部门经理 - 李四</div>
                                    <div class="timeline-desc">部门审批通过，同意发布招标公告</div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <span class="timeline-action">提交审批</span>
                                        <span class="timeline-time">2024-01-14 14:30:00</span>
                                    </div>
                                    <div class="timeline-user">操作人：采购员 - 王五</div>
                                    <div class="timeline-desc">公告编辑完成，提交审批流程</div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <span class="timeline-action">编辑</span>
                                        <span class="timeline-time">2024-01-14 10:15:00</span>
                                    </div>
                                    <div class="timeline-user">操作人：采购员 - 王五</div>
                                    <div class="timeline-desc">修改了招标人联系方式和开标地点</div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <span class="timeline-action">创建</span>
                                        <span class="timeline-time">2024-01-14 09:00:00</span>
                                    </div>
                                    <div class="timeline-user">操作人：采购员 - 王五</div>
                                    <div class="timeline-desc">创建招标公告，关联标段BD-2024-001</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-bar">
            <div class="btn-group">
                <button class="btn btn-outline" onclick="goBack()">返回列表</button>
                <button class="btn btn-secondary" onclick="printAnnouncement()">打印公告</button>
            </div>
            <div class="btn-group">
                <button class="btn btn-primary" onclick="editAnnouncement()">编辑</button>
                <button class="btn btn-warning" onclick="withdrawAnnouncement()">撤回</button>
                <button class="btn btn-danger" onclick="deleteAnnouncement()">删除</button>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="help-modal" class="help-modal">
        <div class="help-content">
            <div class="help-header">
                <h3 class="help-title">中标结果公示管理详情功能说明</h3>
                <button class="close-btn" onclick="hideHelp()">&times;</button>
            </div>
            <div class="help-body">
                <div class="help-section">
                    <h4>功能概述</h4>
                    <p>中标结果公示管理详情页面用于查看招标采购公告的详细信息，包括公告内容、项目信息和操作记录。</p>
                </div>
                
                <div class="help-section">
                    <h4>页签说明</h4>
                    <ul class="help-list">
                        <li><strong>公告详情：</strong>显示完整的招标公告内容，包括基本信息、资格要求、时间安排等</li>
                        <li><strong>项目信息：</strong>显示关联的项目和标段信息，以及相关文件</li>
                        <li><strong>操作记录：</strong>显示公告的完整操作历史和审批流程</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>操作说明</h4>
                    <ul class="help-list">
                        <li><strong>编辑：</strong>修改公告内容（需要相应权限）</li>
                        <li><strong>撤回：</strong>撤回已发布的公告（需要相应权限）</li>
                        <li><strong>删除：</strong>删除公告（需要相应权限）</li>
                        <li><strong>打印：</strong>打印公告内容</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>状态说明</h4>
                    <ul class="help-list">
                        <li><strong>草稿：</strong>公告尚未提交审核</li>
                        <li><strong>待审核：</strong>公告已提交，等待审核</li>
                        <li><strong>已通过：</strong>公告审核通过，等待发布</li>
                        <li><strong>已发布：</strong>公告已正式发布</li>
                        <li><strong>已驳回：</strong>公告审核未通过</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>注意事项</h4>
                    <ul class="help-list">
                        <li>已发布的公告修改需要重新审核</li>
                        <li>撤回操作会影响正在进行的招标流程</li>
                        <li>删除操作不可恢复，请谨慎操作</li>
                        <li>操作记录完整保存，便于审计追溯</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页签切换
        function switchTab(event, tabId) {
            event.preventDefault();
            
            // 隐藏所有页签内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有页签链接的激活状态
            const tabLinks = document.querySelectorAll('.tab-link');
            tabLinks.forEach(link => {
                link.classList.remove('active');
            });
            
            // 显示选中的页签内容
            document.getElementById(tabId).classList.add('active');
            
            // 激活选中的页签链接
            event.target.classList.add('active');
        }

        // 帮助弹窗
        function showHelp() {
            document.getElementById('help-modal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('help-modal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });

        // 业务操作函数
        function goBack() {
            window.history.back();
        }

        function editAnnouncement() {
            console.log('编辑公告');
            // 跳转到编辑页面
        }

        function withdrawAnnouncement() {
            if (confirm('确定要撤回这个公告吗？撤回后将停止接受投标。')) {
                console.log('撤回公告');
                alert('公告撤回成功！');
            }
        }

        function deleteAnnouncement() {
            if (confirm('确定要删除这个公告吗？删除后无法恢复。')) {
                console.log('删除公告');
                alert('公告删除成功！');
            }
        }

        function printAnnouncement() {
            console.log('打印公告');
            window.print();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('中标结果公示管理详情页面加载完成');
        });
    </script>
</body>
</html>