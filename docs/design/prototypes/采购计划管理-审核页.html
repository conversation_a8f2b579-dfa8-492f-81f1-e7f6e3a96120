<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>招采平台 - 采购计划审核</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        /* 顶部导航栏 */
        .top-nav {
            height: 60px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .logo {
            font-size: 20px;
            font-weight: bold;
            margin-right: 40px;
        }
        
        .nav-menu {
            display: flex;
            gap: 30px;
            flex: 1;
        }
        
        .nav-item {
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .nav-item:hover, .nav-item.active {
            background-color: rgba(255,255,255,0.2);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        /* 主体布局 */
        .main-container {
            height: calc(100vh - 60px);
        }
        
        /* 内容区域 */
        .content-wrapper {
            overflow-y: auto;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        /* 页签区域 */
        .tabs {
            display: flex;
            background: white;
            border-radius: 4px 4px 0 0;
            border: 1px solid #e8eaec;
            border-bottom: none;
            margin-bottom: -1px;
            position: relative;
            z-index: 1;
        }
        
        .tab {
            padding: 12px 20px;
            cursor: pointer;
            border-right: 1px solid #e8eaec;
            position: relative;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tab.active {
            color: #1890ff;
            background-color: #f5f7fa;
            border-bottom: 2px solid #1890ff;
            margin-bottom: -1px;
        }
        
        .tab-close {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .tab-close:hover {
            background-color: #e6e6e6;
        }
        
        /* 页面内容 */
        .page-content {
            background: white;
            border-radius: 0 4px 4px 4px;
            border: 1px solid #e8eaec;
            padding: 20px;
        }
        
        /* 页面标题 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e8eaec;
        }
        
        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #262626;
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            gap: 8px;
        }
        
        .title-status {
            display: flex;
            align-items: center;
        }
        
        .title-content {
            flex: 1;
        }
        
        .title-main {
            font-size: 20px;
            font-weight: 600;
            color: #262626;
            line-height: 1.4;
        }
        
        .title-sub {
            font-size: 14px;
            font-weight: 400;
            color: #8c8c8c;
            margin-top: 4px;
        }
        
        .help-icon {
            width: 16px;
            height: 16px;
            background: #1890ff;
            border-radius: 50%;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
        }
        
        .header-actions {
            background: #f8fafc;
            border-top: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            gap: 12px;
            z-index: 1000;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn-group {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            padding: 0 16px;
            height: 32px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            border: 1px solid transparent;
            transition: all 0.3s;
            text-decoration: none;
        }
        
        .btn-primary {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        
        .btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }
        
        .btn-success {
            background-color: #52c41a;
            color: white;
            border-color: #52c41a;
        }
        
        .btn-success:hover {
            background-color: #73d13d;
            border-color: #73d13d;
        }
        
        .btn-default {
            background-color: white;
            border-color: #d9d9d9;
            color: #262626;
        }
        
        .btn-default:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .btn-danger {
            background-color: #ff4d4f;
            color: white;
            border-color: #ff4d4f;
        }
        
        .btn-danger:hover {
            background-color: #ff7875;
            border-color: #ff7875;
        }
        
        /* 详情页签 */
        .detail-tabs {
            display: flex;
            border-bottom: 1px solid #e8eaec;
            margin-bottom: 20px;
        }
        
        .detail-tab {
            padding: 12px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .detail-tab.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
        }
        
        .detail-tab:hover {
            color: #1890ff;
        }
        
        /* 详情内容 */
        .detail-content {
            display: none;
        }
        
        .detail-content.active {
            display: block;
        }
        
        /* 信息展示 */
        .info-section {
            margin-bottom: 32px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #1890ff;
            position: relative;
        }
        
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            bottom: -2px;
            width: 40px;
            height: 2px;
            background: #1890ff;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .info-grid.full-width {
            grid-template-columns: 1fr;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .info-label {
            font-size: 14px;
            color: #8c8c8c;
            font-weight: 500;
        }
        
        .info-value {
            font-size: 14px;
            color: #262626;
            min-height: 20px;
        }
        
        .info-value.empty {
            color: #d9d9d9;
        }
        
        .info-value.description {
            line-height: 1.6;
            background: #fafafa;
            padding: 12px;
            border-radius: 4px;
            border: 1px solid #e8eaec;
        }
        
        /* 状态标签 */
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-draft {
            background-color: #f5f5f5;
            color: #8c8c8c;
        }
        
        .status-pending {
            background-color: #e6f7ff;
            color: #1890ff;
        }
        
        .status-approved {
            background-color: #f6ffed;
            color: #52c41a;
        }
        
        .status-rejected {
            background-color: #fff1f0;
            color: #ff4d4f;
        }
        
        /* 附件列表 */
        .attachment-list {
            display: grid;
            gap: 12px;
        }
        
        .attachment-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #e8eaec;
            border-radius: 4px;
            background: #fafafa;
        }
        
        .attachment-icon {
            width: 32px;
            height: 32px;
            background: #1890ff;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            margin-right: 12px;
        }
        
        .attachment-info {
            flex: 1;
        }
        
        .attachment-name {
            font-size: 14px;
            color: #262626;
            margin-bottom: 4px;
        }
        
        .attachment-size {
            font-size: 12px;
            color: #8c8c8c;
        }
        
        .attachment-actions {
            display: flex;
            gap: 8px;
        }
        
        .attachment-btn {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
            border: 1px solid #d9d9d9;
            background: white;
            color: #262626;
            cursor: pointer;
            text-decoration: none;
        }
        
        .attachment-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        /* 操作记录时间线 */
        .operation-timeline {
            position: relative;
            padding-left: 20px;
        }
        
        .operation-timeline::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e8eaec;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 24px;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -16px;
            top: 8px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #1890ff;
            border: 2px solid white;
            box-shadow: 0 0 0 2px #e8eaec;
        }
        
        .timeline-content {
            background: white;
            border: 1px solid #e8eaec;
            border-radius: 4px;
            padding: 16px;
        }
        
        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .timeline-action {
            font-size: 14px;
            font-weight: 500;
            color: #262626;
        }
        
        .timeline-time {
            font-size: 12px;
            color: #8c8c8c;
        }
        
        .timeline-user {
            font-size: 12px;
            color: #1890ff;
            margin-bottom: 8px;
        }
        
        .timeline-desc {
            font-size: 14px;
            color: #595959;
            line-height: 1.5;
        }
        
        /* 审批流程 */
        .approval-flow {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 20px;
            background: #fafafa;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .flow-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            flex: 1;
        }
        
        .flow-step::after {
            content: '';
            position: absolute;
            top: 20px;
            right: -10px;
            width: 20px;
            height: 2px;
            background: #e8eaec;
        }
        
        .flow-step:last-child::after {
            display: none;
        }
        
        .flow-step.completed::after {
            background: #52c41a;
        }
        
        .flow-step.current::after {
            background: #1890ff;
        }
        
        .step-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            margin-bottom: 8px;
            border: 2px solid #e8eaec;
            background: white;
        }
        
        .flow-step.completed .step-icon {
            background: #52c41a;
            border-color: #52c41a;
            color: white;
        }
        
        .flow-step.current .step-icon {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }
        
        .step-title {
            font-size: 12px;
            color: #262626;
            text-align: center;
            margin-bottom: 4px;
        }
        
        .step-time {
            font-size: 10px;
            color: #8c8c8c;
            text-align: center;
        }
        
        /* 驳回原因弹窗 */
        .reject-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .reject-modal.show {
            display: flex;
        }
        
        .reject-modal-content {
            background: white;
            border-radius: 8px;
            width: 500px;
            max-width: 90vw;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }
        
        .reject-modal-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e8eaec;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .reject-modal-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
        }
        
        .reject-modal-close {
            width: 24px;
            height: 24px;
            border: none;
            background: none;
            font-size: 18px;
            cursor: pointer;
            color: #8c8c8c;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .reject-modal-close:hover {
            color: #262626;
        }
        
        .reject-modal-body {
            padding: 24px;
        }
        
        .reject-form-group {
            margin-bottom: 20px;
        }
        
        .reject-form-label {
            display: block;
            font-size: 14px;
            color: #262626;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .reject-form-label .required {
            color: #ff4d4f;
        }
        
        .reject-textarea {
            width: 100%;
            min-height: 120px;
            padding: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            font-family: inherit;
            resize: vertical;
            transition: border-color 0.3s;
        }
        
        .reject-textarea:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .reject-modal-footer {
            padding: 16px 24px;
            border-top: 1px solid #e8eaec;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }
        
        /* 为固定底部按钮留出空间 */
        .page-content {
            padding-bottom: 100px;
        }
        
        /* 响应式 */
        @media (max-width: 1200px) {
            .info-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .content-wrapper {
                padding: 10px;
            }
            
            .approval-flow {
                flex-direction: column;
                gap: 10px;
            }
            
            .flow-step::after {
                display: none;
            }
        }
    </style>
</head>
<body>

    
    <!-- 主体容器 -->
    <div class="main-container">
        <!-- 内容区域 -->
        <div class="content-wrapper">

            
            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 页面标题 -->
                <div class="page-header">
                    <div class="page-title">
                        <div class="title-content">
                            <div class="title-main">办公楼装修工程采购计划</div>
                            <div class="title-sub">CG-2024-001</div>
                        </div>
                        <div class="title-status">
                            <span class="status-badge status-pending">审核中</span>
                        </div>
                    </div>
                </div>
                
                <!-- 详情页签 -->
                <div class="detail-tabs">
                    <div class="detail-tab active" data-tab="basic">基本信息</div>
                    <div class="detail-tab" data-tab="operation">流程记录</div>
                </div>
                
                <!-- 基本信息 -->
                <div class="detail-content active" id="basic">
                    <!-- 招标信息 -->
                    <div class="info-section">
                        <div class="section-title">招标信息</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">计划项目编号</div>
                                <div class="info-value">CG-2024-001</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">计划项目名称</div>
                                <div class="info-value">办公楼装修工程采购计划</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">采购类型</div>
                                <div class="info-value">施工</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">招标类别</div>
                                <div class="info-value">监理招标</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">采购方式</div>
                                <div class="info-value">公告比选</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">采购预算金额（万元）</div>
                                <div class="info-value">1,200.00</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">资金来源</div>
                                <div class="info-value">自有资金</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">招标时间</div>
                                <div class="info-value">2024年第3季度</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">采购组织方式</div>
                                <div class="info-value">委托招标</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">代理机构</div>
                                <div class="info-value">某招标代理公司</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 项目信息 -->
                    <div class="info-section">
                        <div class="section-title">项目信息</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">项目建设地点</div>
                                <div class="info-value">北京市朝阳区某办公楼</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">建设规模</div>
                                <div class="info-value">约5000平方米</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">计划工期</div>
                                <div class="info-value">90天</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">质量要求</div>
                                <div class="info-value">合格</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">技术标准</div>
                                <div class="info-value">国家现行相关标准</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">环保要求</div>
                                <div class="info-value">符合环保标准</div>
                            </div>
                        </div>
                        
                        <div class="info-grid full-width">
                            <div class="info-item">
                                <div class="info-label">项目概况</div>
                                <div class="info-value description">
                                    本项目为办公楼装修工程，主要包括室内装修、水电安装、消防系统、空调系统等。装修风格要求现代简约，注重环保和节能。具体包括：
                                    <br>1. 地面铺装：采用环保复合地板和瓷砖
                                    <br>2. 墙面装修：乳胶漆和部分墙纸装饰
                                    <br>3. 吊顶工程：石膏板吊顶，集成LED照明
                                    <br>4. 水电改造：重新布线，增加网络点位
                                    <br>5. 消防系统：烟感、喷淋系统安装
                                    <br>6. 空调系统：中央空调安装调试
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 立项决策文件 -->
                    <div class="info-section">
                        <div class="section-title">立项决策文件</div>
                        <div class="attachment-list">
                            <div class="attachment-item">
                                <div class="attachment-icon">📄</div>
                                <div class="attachment-info">
                                    <div class="attachment-name">办公楼装修工程立项申请书.pdf</div>
                                    <div class="attachment-size">2.3 MB</div>
                                </div>
                                <div class="attachment-actions">
                                    <a href="#" class="attachment-btn">预览</a>
                                    <a href="#" class="attachment-btn">下载</a>
                                </div>
                            </div>
                            
                            <div class="attachment-item">
                                <div class="attachment-icon">📊</div>
                                <div class="attachment-info">
                                    <div class="attachment-name">项目可行性研究报告.docx</div>
                                    <div class="attachment-size">1.8 MB</div>
                                </div>
                                <div class="attachment-actions">
                                    <a href="#" class="attachment-btn">预览</a>
                                    <a href="#" class="attachment-btn">下载</a>
                                </div>
                            </div>
                            
                            <div class="attachment-item">
                                <div class="attachment-icon">💰</div>
                                <div class="attachment-info">
                                    <div class="attachment-name">预算明细表.xlsx</div>
                                    <div class="attachment-size">856 KB</div>
                                </div>
                                <div class="attachment-actions">
                                    <a href="#" class="attachment-btn">预览</a>
                                    <a href="#" class="attachment-btn">下载</a>
                                </div>
                            </div>
                        </div>
                    </div>
                
                </div>
                
                <!-- 流程记录 -->
                <div class="detail-content" id="operation">
                    <!-- 操作记录 -->
                    <div class="info-section">
                        <div class="section-title">操作记录</div>
                        <div class="operation-timeline">
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">提交审批</div>
                                        <div class="timeline-time">2024-01-15 14:20:30</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三（采购经办人）</div>
                                    <div class="timeline-desc">
                                        采购计划已完善，现提交部门领导审核。请重点关注预算金额的合理性。
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">申请人公司审核通过</div>
                                        <div class="timeline-time">2024-01-15 16:45:20</div>
                                    </div>
                                    <div class="timeline-user">操作人：李四（行政部经理）</div>
                                    <div class="timeline-desc">
                                        经审核，该采购计划符合部门需求，预算合理。同意提交上级公司审核。
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">修改计划内容</div>
                                        <div class="timeline-time">2024-01-15 11:30:15</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三（采购经办人）</div>
                                    <div class="timeline-desc">
                                        根据部门意见，调整了技术规格要求，增加了环保材料的具体标准。
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">创建采购计划</div>
                                        <div class="timeline-time">2024-01-15 10:30:45</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三（采购经办人）</div>
                                    <div class="timeline-desc">
                                        创建了办公楼装修工程采购计划，填写了基本信息和供应商要求等内容。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 底部操作按钮 -->
            <div class="header-actions">
                <div class="btn-group">
                    <button class="btn btn-default" onclick="goBack()">返回列表</button>
                </div>
                <div class="btn-group">
                    <button class="btn btn-danger" onclick="showRejectModal()">审核驳回</button>
                    <button class="btn btn-primary" onclick="approveAudit()">审核通过</button>
                </div>
            </div>

        </div>
    </div>
    
    <!-- 驳回原因弹窗 -->
    <div class="reject-modal" id="rejectModal">
        <div class="reject-modal-content">
            <div class="reject-modal-header">
                <div class="reject-modal-title">审核驳回</div>
                <button class="reject-modal-close" onclick="closeRejectModal()">&times;</button>
            </div>
            <div class="reject-modal-body">
                <div class="reject-form-group">
                    <label class="reject-form-label" for="rejectReason">
                        驳回原因 <span class="required">*</span>
                    </label>
                    <textarea 
                        id="rejectReason" 
                        class="reject-textarea" 
                        placeholder="请输入驳回原因，说明具体问题和改进建议..."
                        maxlength="500"
                    ></textarea>
                </div>
            </div>
            <div class="reject-modal-footer">
                <button class="btn btn-default" onclick="closeRejectModal()">取消</button>
                <button class="btn btn-danger" onclick="submitReject()">确认驳回</button>
            </div>
        </div>
    </div>
    
    <script>
        // 页面跳转函数
        function openPage(url, title) {
            if (window.parent && window.parent !== window) {
                // 向父窗口发送消息，请求打开新页面
                window.parent.postMessage({
                    type: 'openPage',
                    title: title,
                    url: url
                }, '*');
            } else {
                window.open(url, '_blank');
            }
        }
        
        // 返回列表
        function goBack() {
            if (window.parent && window.parent !== window) {
                // 向父窗口发送消息，请求打开新页面
                window.parent.postMessage({
                    type: 'openPage',
                    title: '采购计划管理',
                    url: '采购计划管理-列表页.html'
                }, '*');
            } else {
                window.location.href = '采购计划管理-列表页.html';
            }
        }
        
        // 审核通过
        function approveAudit() {
            if (confirm('确定要审核通过该采购计划吗？')) {
                // 模拟审核通过操作
                alert('审核通过成功！');
                // 这里可以添加实际的审核通过逻辑
                // 例如：发送请求到后端，更新状态等
            }
        }
        
        // 显示驳回弹窗
        function showRejectModal() {
            document.getElementById('rejectModal').classList.add('show');
            document.getElementById('rejectReason').focus();
        }
        
        // 关闭驳回弹窗
        function closeRejectModal() {
            document.getElementById('rejectModal').classList.remove('show');
            document.getElementById('rejectReason').value = '';
        }
        
        // 提交驳回
        function submitReject() {
            const reason = document.getElementById('rejectReason').value.trim();
            
            if (!reason) {
                alert('请输入驳回原因');
                return;
            }
            
            if (confirm('确定要驳回该采购计划吗？')) {
                // 模拟驳回操作
                alert('驳回成功！原因：' + reason);
                closeRejectModal();
                // 这里可以添加实际的驳回逻辑
                // 例如：发送请求到后端，更新状态等
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            // 页签点击
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
                
                const closeBtn = tab.querySelector('.tab-close');
                if (closeBtn) {
                    closeBtn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        if (tab.classList.contains('active') && tabs.length > 1) {
                            const nextTab = tab.nextElementSibling || tab.previousElementSibling;
                            nextTab.classList.add('active');
                        }
                        tab.remove();
                    });
                }
            });
            
            // 详情页签切换
            const detailTabs = document.querySelectorAll('.detail-tab');
            const detailContents = document.querySelectorAll('.detail-content');
            
            detailTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const targetTab = this.dataset.tab;
                    
                    // 切换页签状态
                    detailTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 切换内容显示
                    detailContents.forEach(content => {
                        content.classList.remove('active');
                        if (content.id === targetTab) {
                            content.classList.add('active');
                        }
                    });
                });
            });
            
            // 附件操作
            const attachmentBtns = document.querySelectorAll('.attachment-btn');
            attachmentBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const action = this.textContent.trim();
                    const fileName = this.closest('.attachment-item').querySelector('.attachment-name').textContent;
                    
                    if (action === '预览') {
                        alert('预览文件：' + fileName);
                    } else if (action === '下载') {
                        alert('下载文件：' + fileName);
                    }
                });
            });
            
            // 点击弹窗外部关闭弹窗
            document.getElementById('rejectModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeRejectModal();
                }
            });
            
            // ESC键关闭弹窗
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeRejectModal();
                }
            });
        });
    </script>
</body>
</html>