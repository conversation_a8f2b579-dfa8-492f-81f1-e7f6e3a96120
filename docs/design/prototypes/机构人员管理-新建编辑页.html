<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机构人员管理 - 新建/编辑</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 页面头部 */
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
        }

        .page-title::before {
            content: '';
            width: 4px;
            height: 20px;
            background: #3498db;
            margin-right: 12px;
        }

        .help-icon {
            margin-left: 8px;
            color: #95a5a6;
            cursor: pointer;
            font-size: 16px;
        }

        .back-btn {
            background: #95a5a6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .back-btn:hover {
            background: #7f8c8d;
        }

        /* 表单容器 */
        .form-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        /* 表单区域 */
        .form-section {
            padding: 30px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ecf0f1;
            position: relative;
        }

        .section-title::before {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 60px;
            height: 2px;
            background: #3498db;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .form-label.required::after {
            content: '*';
            color: #e74c3c;
            margin-left: 4px;
        }

        .form-control {
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s, box-shadow 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .form-control:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
        }

        /* 选择机构按钮 */
        .select-agency-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
            width: 100%;
        }

        .select-agency-btn:hover {
            background: #2980b9;
        }

        /* 操作按钮区域 */
        .form-actions {
            padding: 20px 30px;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .btn {
            padding: 10px 24px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 8px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .help-text {
            font-size: 14px;
            line-height: 1.8;
            color: #555;
            margin-bottom: 15px;
        }

        .close-help {
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            font-size: 24px;
            color: #95a5a6;
            cursor: pointer;
        }

        /* 机构选择弹窗 */
        .agency-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .agency-modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 8px;
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .agency-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .agency-item {
            padding: 12px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .agency-item:hover {
            background: #f8f9fa;
        }

        .agency-item:last-child {
            border-bottom: none;
        }

        .agency-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .agency-code {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 4px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 10px;
            }
            
            .form-section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="page-title">
                机构人员管理 - 新建人员
                <span class="help-icon" onclick="showHelp()" title="帮助说明">ⓘ</span>
            </div>
            <button class="back-btn" onclick="goBack()">返回列表</button>
        </div>

        <!-- 表单容器 -->
        <div class="form-container">
            <form id="staffForm">
                <!-- 基本信息 -->
                <div class="form-section">
                    <div class="section-title">基本信息</div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label required">人员名称</label>
                            <input type="text" class="form-control" name="staffName" placeholder="请输入人员名称" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">联系方式</label>
                            <input type="text" class="form-control" name="contact" placeholder="请输入联系方式" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">所属机构</label>
                            <button type="button" class="select-agency-btn" onclick="showAgencyModal()">选择机构</button>
                            <input type="hidden" name="agencyId" id="agencyId">
                            <input type="text" class="form-control" name="agencyName" id="agencyName" placeholder="请选择所属机构" readonly style="margin-top: 8px;">
                        </div>
                        <div class="form-group">
                            <label class="form-label required">企业代码</label>
                            <input type="text" class="form-control" name="enterpriseCode" placeholder="请输入企业代码" required>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="goBack()">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div class="help-modal" id="helpModal">
        <div class="help-content">
            <button class="close-help" onclick="hideHelp()">&times;</button>
            <div class="help-title">机构人员管理 - 操作说明</div>
            <div class="help-text">
                <strong>功能说明：</strong><br>
                本页面用于新建或编辑机构人员信息，支持录入人员基本信息。
            </div>
            <div class="help-text">
                <strong>字段说明：</strong><br>
                • <strong>人员名称：</strong>必填，输入人员的真实姓名<br>
                • <strong>联系方式：</strong>必填，输入人员的联系电话或其他联系方式<br>
                • <strong>所属机构：</strong>必填，通过弹窗选择人员所属的代理机构<br>
                • <strong>企业代码：</strong>必填，输入机构的统一社会信用代码
            </div>
            <div class="help-text">
                <strong>操作说明：</strong><br>
                1. 填写所有必填字段（标有红色*号）<br>
                2. 点击"选择机构"按钮，在弹窗中选择所属机构<br>
                3. 确认信息无误后，点击"保存"按钮提交<br>
                4. 点击"取消"或"返回列表"可退出当前页面
            </div>
        </div>
    </div>

    <!-- 机构选择弹窗 -->
    <div class="agency-modal" id="agencyModal">
        <div class="agency-modal-content">
            <button class="close-help" onclick="hideAgencyModal()">&times;</button>
            <div class="help-title">选择所属机构</div>
            <div class="agency-list">
                <div class="agency-item" onclick="selectAgency('1', '中建工程咨询有限公司', '91110000123456789X')">
                    <div class="agency-name">中建工程咨询有限公司</div>
                    <div class="agency-code">企业代码：91110000123456789X</div>
                </div>
                <div class="agency-item" onclick="selectAgency('2', '华信工程管理集团', '91110000987654321Y')">
                    <div class="agency-name">华信工程管理集团</div>
                    <div class="agency-code">企业代码：91110000987654321Y</div>
                </div>
                <div class="agency-item" onclick="selectAgency('3', '北京建设工程咨询公司', '91110000456789123Z')">
                    <div class="agency-name">北京建设工程咨询公司</div>
                    <div class="agency-code">企业代码：91110000456789123Z</div>
                </div>
                <div class="agency-item" onclick="selectAgency('4', '上海工程项目管理有限公司', '91310000789123456A')">
                    <div class="agency-name">上海工程项目管理有限公司</div>
                    <div class="agency-code">企业代码：91310000789123456A</div>
                </div>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="hideAgencyModal()">取消</button>
            </div>
        </div>
    </div>

    <script>
        // 显示帮助弹窗
        function showHelp() {
            document.getElementById('helpModal').style.display = 'block';
        }

        // 隐藏帮助弹窗
        function hideHelp() {
            document.getElementById('helpModal').style.display = 'none';
        }

        // 显示机构选择弹窗
        function showAgencyModal() {
            document.getElementById('agencyModal').style.display = 'block';
        }

        // 隐藏机构选择弹窗
        function hideAgencyModal() {
            document.getElementById('agencyModal').style.display = 'none';
        }

        // 选择机构
        function selectAgency(id, name, code) {
            document.getElementById('agencyId').value = id;
            document.getElementById('agencyName').value = name;
            document.querySelector('input[name="enterpriseCode"]').value = code;
            hideAgencyModal();
        }

        // 返回列表页
        function goBack() {
            window.location.href = '机构人员管理-列表页.html';
        }

        // 表单提交
        document.getElementById('staffForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 获取表单数据
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            // 验证必填字段
            if (!data.staffName || !data.contact || !data.agencyId || !data.enterpriseCode) {
                alert('请填写所有必填字段');
                return;
            }
            
            // 这里应该调用后端API保存数据
            console.log('保存数据:', data);
            alert('保存成功！');
            
            // 保存成功后返回列表页
            goBack();
        });

        // 点击弹窗外部关闭弹窗
        document.getElementById('helpModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });

        document.getElementById('agencyModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideAgencyModal();
            }
        });
    </script>
</body>
</html>