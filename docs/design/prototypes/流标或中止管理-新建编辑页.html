<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流标或中止管理 - 新建/编辑 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #fff;
            border-bottom: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
        }

        .help-icon {
            margin-left: 8px;
            width: 16px;
            height: 16px;
            background: #6b7280;
            border-radius: 50%;
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .breadcrumb {
            color: #6b7280;
            font-size: 14px;
        }

        .back-btn {
            background: #6b7280;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: background 0.3s;
        }

        .back-btn:hover {
            background: #4b5563;
        }

        /* 内容区域 */
        .content-area {
            padding: 24px;
        }

        /* 表单区域 */
        .form-container {
            background: white;
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            overflow: hidden;
        }

        .form-section {
            border-bottom: 1px solid #e6e8eb;
        }

        .form-section:last-child {
            border-bottom: none;
        }

        .section-header {
            background: #f8fafc;
            padding: 16px 20px;
            border-bottom: 1px solid #e6e8eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 16px;
            background: #3b82f6;
            margin-right: 8px;
        }

        .section-content {
            padding: 20px;
        }

        /* 表单网格 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-grid.full-width {
            grid-template-columns: 1fr;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            font-size: 13px;
            color: #374151;
            margin-bottom: 6px;
            font-weight: 500;
        }

        .form-label.required::after {
            content: '*';
            color: #ef4444;
            margin-left: 4px;
        }

        .form-input {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 13px;
            transition: border-color 0.3s;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 13px;
            background: white;
            cursor: pointer;
        }

        .form-textarea {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 13px;
            resize: vertical;
            min-height: 100px;
            font-family: inherit;
        }

        .form-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 富文本编辑器 */
        .editor-container {
            border: 1px solid #d1d5db;
            border-radius: 4px;
            overflow: hidden;
        }

        .editor-toolbar {
            background: #f8fafc;
            border-bottom: 1px solid #e6e8eb;
            padding: 8px 12px;
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .editor-btn {
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .editor-btn:hover {
            background: #f3f4f6;
        }

        .editor-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .editor-content {
            min-height: 300px;
            padding: 12px;
            outline: none;
            line-height: 1.6;
        }

        .editor-content:focus {
            box-shadow: inset 0 0 0 2px rgba(59, 130, 246, 0.1);
        }

        /* 标段管理 */
        .segment-container {
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            overflow: hidden;
        }

        .segment-header {
            background: #f8fafc;
            padding: 12px 16px;
            border-bottom: 1px solid #e6e8eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .segment-title {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
        }

        .segment-actions {
            display: flex;
            gap: 8px;
        }

        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-outline {
            background: white;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        /* 标段选择容器 */
        .section-select-container {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .section-select-container .form-input {
            flex: 1;
        }

        /* 标段选择弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: #fff;
            margin: 5% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 1200px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .modal-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e6e8eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8fafc;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .close {
            color: #6b7280;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            color: #374151;
        }

        .search-section {
            padding: 20px 24px;
            border-bottom: 1px solid #e6e8eb;
            background: #fff;
        }

        .search-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 16px;
        }

        .search-group {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .search-group label {
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
        }

        .search-group input,
        .search-group select {
            padding: 6px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 13px;
        }

        .date-range {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .date-range span {
            font-size: 12px;
            color: #6b7280;
        }

        .search-buttons {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }

        .table-section {
            padding: 0 24px;
            max-height: 400px;
            overflow-y: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .table th,
        .table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e6e8eb;
        }

        .table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tbody tr:hover {
            background: #f9fafb;
        }

        .pagination-section {
            padding: 16px 24px;
            border-top: 1px solid #e6e8eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8fafc;
        }

        .pagination-info {
            font-size: 12px;
            color: #6b7280;
        }

        .pagination-controls {
            display: flex;
            gap: 8px;
        }

        .pagination-btn {
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .pagination-btn:hover {
            background: #f3f4f6;
        }

        .pagination-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 标段列表 */
        .segment-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .segment-item {
            padding: 16px;
            border-bottom: 1px solid #e6e8eb;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .segment-item:last-child {
            border-bottom: none;
        }

        .segment-info {
            flex: 1;
        }

        .segment-name {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .segment-details {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.4;
        }

        .segment-controls {
            display: flex;
            gap: 8px;
            margin-left: 16px;
        }

        /* 附件上传 */
        .upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 6px;
            padding: 40px 20px;
            text-align: center;
            background: #f9fafb;
            transition: all 0.3s;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #3b82f6;
            background: #f0f9ff;
        }

        .upload-area.dragover {
            border-color: #3b82f6;
            background: #dbeafe;
        }

        .upload-icon {
            font-size: 48px;
            color: #9ca3af;
            margin-bottom: 16px;
        }

        .upload-text {
            color: #6b7280;
            margin-bottom: 8px;
        }

        .upload-hint {
            font-size: 12px;
            color: #9ca3af;
        }

        .file-list {
            margin-top: 16px;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: #f8fafc;
            border: 1px solid #e6e8eb;
            border-radius: 4px;
            margin-bottom: 8px;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .file-icon {
            width: 16px;
            height: 16px;
            background: #3b82f6;
            border-radius: 2px;
        }

        .file-name {
            font-size: 13px;
            color: #1f2937;
        }

        .file-size {
            font-size: 12px;
            color: #6b7280;
        }

        .file-actions {
            display: flex;
            gap: 8px;
        }

        .file-btn {
            padding: 2px 6px;
            border: none;
            border-radius: 2px;
            font-size: 11px;
            cursor: pointer;
        }

        .file-btn.download {
            background: #3b82f6;
            color: white;
        }

        .file-btn.delete {
            background: #ef4444;
            color: white;
        }

        /* 底部操作栏 */
        .action-bar {
            background: #f8fafc;
            border-top: 1px solid #e6e8eb;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            bottom: 0;
        }

        .action-left {
            display: flex;
            gap: 12px;
        }

        .action-right {
            display: flex;
            gap: 12px;
        }

        /* 模态弹窗 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e6e8eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            color: #6b7280;
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            color: #374151;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid #e6e8eb;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin-top: 20px;
        }

        .page-btn {
            padding: 6px 12px;
            border: 1px solid #d1d5db;
            background: white;
            color: #374151;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
        }

        .page-btn:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .page-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e6e8eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .help-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .help-body {
            padding: 24px;
        }

        .help-section {
            margin-bottom: 24px;
        }

        .help-section h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
        }

        .help-section p {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 12px;
        }

        .help-list {
            list-style: none;
            padding-left: 0;
        }

        .help-list li {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 8px;
            padding-left: 16px;
            position: relative;
        }

        .help-list li::before {
            content: '•';
            color: #3b82f6;
            position: absolute;
            left: 0;
        }

        .help-list strong {
            color: #1f2937;
        }

        /* 响应式 */
        @media (max-width: 1200px) {
            .form-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .action-bar {
                flex-direction: column;
                gap: 12px;
            }
            
            .action-left,
            .action-right {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">
                    流标或中止管理 - 新建/编辑
                    <span class="help-icon" onclick="showHelp()" title="功能说明">?</span>
                </h1>
                <div class="breadcrumb">首页 > 流标或中止管理 > 新建/编辑</div>
            </div>
            <button class="back-btn" onclick="goBack()">返回列表</button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <form class="form-container">
                <!-- 基本信息 -->
                <div class="form-section">
                    <div class="section-header">
                        <h3 class="section-title">基本信息</h3>
                    </div>
                    <div class="section-content">
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label required">关联标段</label>
                                <div class="section-select-container">
                                    <input type="text" class="form-input" id="sectionName" placeholder="请选择标段" readonly>
                                    <input type="hidden" id="sectionId" value="">
                                    <button type="button" class="btn btn-outline" onclick="openSectionSelectModal()">选择标段</button>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label required">公告标题</label>
                                <input type="text" class="form-input" id="announcementTitle" placeholder="选择标段后自动生成，可手动修改" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label required">公告编号</label>
                                <input type="text" class="form-input" id="announcementNumber" placeholder="系统自动生成" readonly>
                            </div>
                            <div class="form-group">
                                <label class="form-label required">流标类型</label>
                                <select class="form-select" id="failureType" required>
                                    <option value="">请选择流标类型</option>
                                    <option value="流标">流标</option>
                                    <option value="废标">废标</option>
                                    <option value="中止">中止</option>
                                    <option value="终止">终止</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label required">项目名称</label>
                                <input type="text" class="form-input" id="projectName" placeholder="选择标段后自动填充" readonly required>
                            </div>
                            <div class="form-group">
                                <label class="form-label required">项目编号</label>
                                <input type="text" class="form-input" id="projectNumber" placeholder="选择标段后自动填充" readonly required>
                            </div>
                            <div class="form-group">
                                <label class="form-label required">预算金额（万元）</label>
                                <input type="number" class="form-input" id="budgetAmount" placeholder="选择标段后自动填充" readonly step="0.01" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">采购方式</label>
                                <select class="form-select" id="procurementMethod">
                                    <option value="">选择标段后自动填充</option>
                                    <option value="公开招标">公开招标</option>
                                    <option value="邀请招标">邀请招标</option>
                                    <option value="竞争性谈判">竞争性谈判</option>
                                    <option value="单一来源">单一来源</option>
                                    <option value="询价采购">询价采购</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">采购人</label>
                                <input type="text" class="form-input" id="purchaser" placeholder="选择标段后自动填充" readonly>
                            </div>
                            <div class="form-group">
                                <label class="form-label">代理机构</label>
                                <input type="text" class="form-input" id="agencyOrg" placeholder="选择标段后自动填充" readonly>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 公告内容 -->
                <div class="form-section">
                    <div class="section-header">
                        <h3 class="section-title">公告内容</h3>
                    </div>
                    <div class="section-content">
                        <div class="form-group full-width">
                            <label class="form-label required">公告正文</label>
                            <div class="editor-container">
                                <div class="editor-toolbar">
                                    <button type="button" class="editor-btn" onclick="formatText('bold')"><strong>B</strong></button>
                                    <button type="button" class="editor-btn" onclick="formatText('italic')"><em>I</em></button>
                                    <button type="button" class="editor-btn" onclick="formatText('underline')"><u>U</u></button>
                                    <button type="button" class="editor-btn" onclick="formatText('fontSize', '14px')">字号</button>
                                    <button type="button" class="editor-btn" onclick="formatText('foreColor', '#000000')">字色</button>
                                    <button type="button" class="editor-btn" onclick="formatText('justifyLeft')">左对齐</button>
                                    <button type="button" class="editor-btn" onclick="formatText('justifyCenter')">居中</button>
                                    <button type="button" class="editor-btn" onclick="formatText('justifyRight')">右对齐</button>
                                    <button type="button" class="editor-btn" onclick="formatText('insertUnorderedList')">项目符号</button>
                                    <button type="button" class="editor-btn" onclick="formatText('insertOrderedList')">编号</button>
                                    <button type="button" class="editor-btn" onclick="insertTable()">表格</button>
                                    <button type="button" class="editor-btn" onclick="insertImage()">图片</button>
                                </div>
                                <div class="editor-content" contenteditable="true" id="editor">
                                    <h3>一、项目概况</h3>
                                    <p>本项目为[项目名称]，项目编号：[项目编号]，预算金额：[预算金额]万元。</p>
                                    
                                    <h3>二、申请人的资格要求</h3>
                                    <p>1. 具有独立承担民事责任的能力；</p>
                                    <p>2. 具有良好的商业信誉和健全的财务会计制度；</p>
                                    <p>3. 具有履行合同所必需的设备和专业技术能力；</p>
                                    <p>4. 有依法缴纳税收和社会保障资金的良好记录；</p>
                                    <p>5. 参加政府采购活动前三年内，在经营活动中没有重大违法记录。</p>
                                    
                                    <h3>三、获取招标文件</h3>
                                    <p><strong>时间：</strong>2024年1月20日至2024年1月25日，每日上午8:30-11:30，下午14:30-17:30（北京时间，法定节假日除外）</p>
                                    <p><strong>地点：</strong>[获取地点]</p>
                                    <p><strong>方式：</strong>现场获取或网上下载</p>
                                    <p><strong>售价：</strong>免费</p>
                                    
                                    <h3>四、投标文件的递交</h3>
                                    <p><strong>截止时间：</strong>2024年1月26日上午9:00（北京时间）</p>
                                    <p><strong>地点：</strong>[递交地点]</p>
                                    
                                    <h3>五、开标时间及地点</h3>
                                    <p><strong>时间：</strong>2024年1月26日上午9:00（北京时间）</p>
                                    <p><strong>地点：</strong>[开标地点]</p>
                                    
                                    <h3>六、联系方式</h3>
                                    <p><strong>采购人：</strong>[采购人名称]</p>
                                    <p><strong>地址：</strong>[采购人地址]</p>
                                    <p><strong>联系人：</strong>[联系人姓名]</p>
                                    <p><strong>电话：</strong>[联系电话]</p>
                                    
                                    <p><strong>采购代理机构：</strong>[代理机构名称]</p>
                                    <p><strong>地址：</strong>[代理机构地址]</p>
                                    <p><strong>联系人：</strong>[联系人姓名]</p>
                                    <p><strong>电话：</strong>[联系电话]</p>
                                    
                                    <div style="text-align: right; margin-top: 40px;">
                                        <p><strong>[采购人名称]</strong></p>
                                        <p>2024年1月20日</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- 附件上传 -->
                <div class="form-section">
                    <div class="section-header">
                        <h3 class="section-title">附件上传</h3>
                    </div>
                    <div class="section-content">
                        <div class="upload-area" onclick="selectFiles()" ondrop="handleDrop(event)" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
                            <div class="upload-icon">📁</div>
                            <div class="upload-text">点击上传文件或拖拽文件到此区域</div>
                            <div class="upload-hint">支持 PDF、DOC、DOCX、XLS、XLSX、JPG、PNG 格式，单个文件不超过 10MB</div>
                        </div>
                        <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png" style="display: none;" onchange="handleFileSelect(event)">
                        <div class="file-list" id="fileList">
                            <!-- 文件列表将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 底部操作栏 -->
        <div class="action-bar">
            <div class="action-left">
                <button type="button" class="btn btn-secondary" onclick="goBack()">返回</button>
            </div>
            <div class="action-right">
                <button type="button" class="btn btn-outline" onclick="saveDraft()">保存草稿</button>
                <button type="button" class="btn btn-warning" onclick="preview()">预览</button>
                <button type="button" class="btn btn-primary" onclick="submitForReview()">提交审核</button>
            </div>
        </div>
    </div>

    <!-- 标段编辑弹窗 -->
    <div id="segmentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">编辑标段</h3>
                <button class="close-btn" onclick="closeSegmentModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label required">标段名称</label>
                        <input type="text" id="segmentName" class="form-input" placeholder="请输入标段名称">
                    </div>
                    <div class="form-group">
                        <label class="form-label required">标段编号</label>
                        <input type="text" id="segmentCode" class="form-input" placeholder="请输入标段编号">
                    </div>
                    <div class="form-group">
                        <label class="form-label required">预算金额（万元）</label>
                        <input type="number" id="segmentBudget" class="form-input" placeholder="请输入预算金额" step="0.01">
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">标段描述</label>
                        <textarea id="segmentDescription" class="form-textarea" placeholder="请输入标段描述"></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeSegmentModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveSegment()">保存</button>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="help-modal" class="help-modal">
        <div class="help-content">
            <div class="help-header">
                <h3 class="help-title">公告管理功能说明</h3>
                <button class="close-btn" onclick="hideHelp()">&times;</button>
            </div>
            <div class="help-body">
                <div class="help-section">
                    <h4>功能概述</h4>
                    <p>公告新建/编辑页面用于创建和修改招标采购公告。支持富文本编辑、标段管理、附件上传等功能。</p>
                </div>
                
                <div class="help-section">
                    <h4>基本信息</h4>
                    <ul class="help-list">
                        <li><strong>公告标题：</strong>必填项，简明扼要地描述公告内容</li>
                        <li><strong>公告编号：</strong>系统自动生成，唯一标识</li>
                        <li><strong>公告类型：</strong>选择合适的公告类型</li>
                        <li><strong>项目信息：</strong>填写项目名称、编号、预算等基本信息</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>公告内容编辑</h4>
                    <ul class="help-list">
                        <li><strong>富文本编辑器：</strong>支持文字格式化、表格、图片插入</li>
                        <li><strong>模板内容：</strong>系统提供标准模板，可根据需要修改</li>
                        <li><strong>格式要求：</strong>遵循政府采购公告格式规范</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>附件上传</h4>
                    <ul class="help-list">
                        <li><strong>支持格式：</strong>PDF、DOC、DOCX、XLS、XLSX、JPG、PNG</li>
                        <li><strong>文件大小：</strong>单个文件不超过10MB</li>
                        <li><strong>上传方式：</strong>支持点击选择和拖拽上传</li>
                        <li><strong>文件管理：</strong>可预览、下载、删除已上传的文件</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>操作说明</h4>
                    <ul class="help-list">
                        <li><strong>保存草稿：</strong>保存当前编辑内容，可稍后继续编辑</li>
                        <li><strong>预览：</strong>预览公告发布后的效果</li>
                        <li><strong>提交审核：</strong>提交给审核人员进行审核</li>
                        <li><strong>返回：</strong>返回公告列表页面</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 标段数据
        let segments = [
            {
                id: 1,
                name: '第一标段：办公设备采购',
                code: 'BD-001',
                budget: 30.00,
                description: '包括台式电脑、笔记本电脑、打印机等办公设备的采购'
            },
            {
                id: 2,
                name: '第二标段：办公家具采购',
                code: 'BD-002',
                budget: 20.00,
                description: '包括办公桌、办公椅、文件柜等办公家具的采购'
            }
        ];

        let currentSegmentId = null;
        let uploadedFiles = [];

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderSegments();
            initEditor();
        });

        // 渲染标段列表
        function renderSegments() {
            const segmentList = document.getElementById('segmentList');
            if (segments.length === 0) {
                segmentList.innerHTML = '<div style="padding: 40px; text-align: center; color: #6b7280;">暂无标段，请点击"添加标段"按钮添加</div>';
                return;
            }

            segmentList.innerHTML = segments.map(segment => `
                <div class="segment-item">
                    <div class="segment-info">
                        <div class="segment-name">${segment.name}</div>
                        <div class="segment-details">
                            标段编号：${segment.code} | 预算金额：${segment.budget}万元<br>
                            ${segment.description}
                        </div>
                    </div>
                    <div class="segment-controls">
                        <button type="button" class="btn btn-primary" onclick="editSegment(${segment.id})">编辑</button>
                        <button type="button" class="btn btn-danger" onclick="deleteSegment(${segment.id})">删除</button>
                    </div>
                </div>
            `).join('');
        }

        // 添加标段
        function addSegment() {
            currentSegmentId = null;
            document.getElementById('segmentName').value = '';
            document.getElementById('segmentCode').value = '';
            document.getElementById('segmentBudget').value = '';
            document.getElementById('segmentDescription').value = '';
            document.getElementById('segmentModal').style.display = 'block';
        }

        // 编辑标段
        function editSegment(id) {
            const segment = segments.find(s => s.id === id);
            if (segment) {
                currentSegmentId = id;
                document.getElementById('segmentName').value = segment.name;
                document.getElementById('segmentCode').value = segment.code;
                document.getElementById('segmentBudget').value = segment.budget;
                document.getElementById('segmentDescription').value = segment.description;
                document.getElementById('segmentModal').style.display = 'block';
            }
        }

        // 保存标段
        function saveSegment() {
            const name = document.getElementById('segmentName').value.trim();
            const code = document.getElementById('segmentCode').value.trim();
            const budget = parseFloat(document.getElementById('segmentBudget').value);
            const description = document.getElementById('segmentDescription').value.trim();

            if (!name || !code || !budget) {
                alert('请填写必填项');
                return;
            }

            if (currentSegmentId) {
                // 编辑现有标段
                const index = segments.findIndex(s => s.id === currentSegmentId);
                if (index !== -1) {
                    segments[index] = { id: currentSegmentId, name, code, budget, description };
                }
            } else {
                // 添加新标段
                const newId = Math.max(...segments.map(s => s.id), 0) + 1;
                segments.push({ id: newId, name, code, budget, description });
            }

            renderSegments();
            closeSegmentModal();
        }

        // 删除标段
        function deleteSegment(id) {
            if (confirm('确定要删除这个标段吗？')) {
                segments = segments.filter(s => s.id !== id);
                renderSegments();
            }
        }

        // 关闭标段弹窗
        function closeSegmentModal() {
            document.getElementById('segmentModal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        document.getElementById('segmentModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeSegmentModal();
            }
        });

        // 初始化富文本编辑器
        function initEditor() {
            const editor = document.getElementById('editor');
            editor.addEventListener('paste', function(e) {
                // 处理粘贴事件，清理格式
                e.preventDefault();
                const text = (e.originalEvent || e).clipboardData.getData('text/plain');
                document.execCommand('insertText', false, text);
            });
        }

        // 富文本编辑器格式化
        function formatText(command, value = null) {
            document.execCommand(command, false, value);
            document.getElementById('editor').focus();
        }

        // 插入表格
        function insertTable() {
            const rows = prompt('请输入行数:', '3');
            const cols = prompt('请输入列数:', '3');
            
            if (rows && cols) {
                let tableHTML = '<table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;">';
                for (let i = 0; i < parseInt(rows); i++) {
                    tableHTML += '<tr>';
                    for (let j = 0; j < parseInt(cols); j++) {
                        tableHTML += '<td style="padding: 8px; border: 1px solid #ccc;">单元格</td>';
                    }
                    tableHTML += '</tr>';
                }
                tableHTML += '</table>';
                
                document.execCommand('insertHTML', false, tableHTML);
            }
        }

        // 插入图片
        function insertImage() {
            const url = prompt('请输入图片URL:');
            if (url) {
                document.execCommand('insertImage', false, url);
            }
        }

        // 文件上传相关
        function selectFiles() {
            document.getElementById('fileInput').click();
        }

        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            uploadFiles(files);
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.currentTarget.classList.remove('dragover');
        }

        function handleDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');
            const files = Array.from(event.dataTransfer.files);
            uploadFiles(files);
        }

        function uploadFiles(files) {
            files.forEach(file => {
                // 验证文件类型和大小
                const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 
                                     'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                     'image/jpeg', 'image/jpg', 'image/png'];
                
                if (!allowedTypes.includes(file.type)) {
                    alert(`文件 ${file.name} 格式不支持`);
                    return;
                }
                
                if (file.size > 10 * 1024 * 1024) {
                    alert(`文件 ${file.name} 大小超过10MB`);
                    return;
                }
                
                // 添加到文件列表
                const fileInfo = {
                    id: Date.now() + Math.random(),
                    name: file.name,
                    size: formatFileSize(file.size),
                    file: file
                };
                
                uploadedFiles.push(fileInfo);
                renderFileList();
            });
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function renderFileList() {
            const fileList = document.getElementById('fileList');
            if (uploadedFiles.length === 0) {
                fileList.innerHTML = '';
                return;
            }

            fileList.innerHTML = uploadedFiles.map(file => `
                <div class="file-item">
                    <div class="file-info">
                        <div class="file-icon"></div>
                        <span class="file-name">${file.name}</span>
                        <span class="file-size">(${file.size})</span>
                    </div>
                    <div class="file-actions">
                        <button type="button" class="file-btn download" onclick="downloadFile(${file.id})">下载</button>
                        <button type="button" class="file-btn delete" onclick="deleteFile(${file.id})">删除</button>
                    </div>
                </div>
            `).join('');
        }

        function downloadFile(fileId) {
            const file = uploadedFiles.find(f => f.id === fileId);
            if (file) {
                const url = URL.createObjectURL(file.file);
                const a = document.createElement('a');
                a.href = url;
                a.download = file.name;
                a.click();
                URL.revokeObjectURL(url);
            }
        }

        function deleteFile(fileId) {
            uploadedFiles = uploadedFiles.filter(f => f.id !== fileId);
            renderFileList();
        }

        // 标段选择弹窗相关函数
        let availableSections = [
            {
                id: 'BD001',
                name: '办公设备采购标段',
                projectName: '2024年度办公设备采购项目',
                procurementMethod: '公开招标',
                procurementType: '货物类',
                budgetAmount: 150.00,
                purchaser: '某政府机关',
                agencyOrg: '某招标代理公司',
                createTime: '2024-01-15'
            },
            {
                id: 'BD002',
                name: '办公家具采购标段',
                projectName: '2024年度办公家具采购项目',
                procurementMethod: '邀请招标',
                procurementType: '货物类',
                budgetAmount: 80.00,
                purchaser: '某事业单位',
                agencyOrg: '某招标代理公司',
                createTime: '2024-01-20'
            },
            {
                id: 'BD003',
                name: '信息系统建设标段',
                projectName: '智慧办公系统建设项目',
                procurementMethod: '竞争性谈判',
                procurementType: '服务类',
                budgetAmount: 200.00,
                purchaser: '某国有企业',
                agencyOrg: '某咨询公司',
                createTime: '2024-01-25'
            }
        ];

        // 打开标段选择弹窗
        function openSectionSelectModal() {
            document.getElementById('sectionSelectModal').style.display = 'block';
            loadSectionData();
        }

        // 关闭标段选择弹窗
        function closeSectionSelectModal() {
            document.getElementById('sectionSelectModal').style.display = 'none';
        }

        // 加载标段数据
        function loadSectionData() {
            const tbody = document.querySelector('#sectionSelectModal .table tbody');
            tbody.innerHTML = availableSections.map(section => `
                <tr>
                    <td><input type="radio" name="selectedSection" value="${section.id}" onchange="selectSection('${section.id}')"></td>
                    <td>${section.name}</td>
                    <td>${section.projectName}</td>
                    <td>${section.procurementMethod}</td>
                    <td>${section.procurementType}</td>
                    <td>${section.createTime}</td>
                </tr>
            `).join('');
        }

        // 选择标段
        function selectSection(sectionId) {
            const section = availableSections.find(s => s.id === sectionId);
            if (section) {
                // 填充表单数据
                document.getElementById('sectionId').value = section.id;
                document.getElementById('sectionName').value = section.name;
                document.getElementById('projectName').value = section.projectName;
                document.getElementById('projectNumber').value = section.id;
                document.getElementById('budgetAmount').value = section.budgetAmount;
                document.getElementById('procurementMethod').value = section.procurementMethod;
                document.getElementById('purchaser').value = section.purchaser;
                document.getElementById('agencyOrg').value = section.agencyOrg;
                
                // 自动生成公告标题
                const failureType = document.getElementById('failureType').value;
                if (failureType) {
                    document.getElementById('announcementTitle').value = `${section.projectName} - ${failureType}公告`;
                }
            }
        }

        // 确认选择标段
        function confirmSectionSelection() {
            const selectedRadio = document.querySelector('input[name="selectedSection"]:checked');
            if (!selectedRadio) {
                alert('请选择一个标段');
                return;
            }
            closeSectionSelectModal();
        }

        // 搜索标段
        function searchSections() {
            const searchName = document.getElementById('searchSectionName').value.toLowerCase();
            const searchProject = document.getElementById('searchProjectName').value.toLowerCase();
            const searchMethod = document.getElementById('searchProcurementMethod').value;
            const searchType = document.getElementById('searchProcurementType').value;
            
            const filteredSections = availableSections.filter(section => {
                return (!searchName || section.name.toLowerCase().includes(searchName)) &&
                       (!searchProject || section.projectName.toLowerCase().includes(searchProject)) &&
                       (!searchMethod || section.procurementMethod === searchMethod) &&
                       (!searchType || section.procurementType === searchType);
            });
            
            const tbody = document.querySelector('#sectionSelectModal .table tbody');
            tbody.innerHTML = filteredSections.map(section => `
                <tr>
                    <td><input type="radio" name="selectedSection" value="${section.id}" onchange="selectSection('${section.id}')"></td>
                    <td>${section.name}</td>
                    <td>${section.projectName}</td>
                    <td>${section.procurementMethod}</td>
                    <td>${section.procurementType}</td>
                    <td>${section.createTime}</td>
                </tr>
            `).join('');
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchSectionName').value = '';
            document.getElementById('searchProjectName').value = '';
            document.getElementById('searchProcurementMethod').value = '';
            document.getElementById('searchProcurementType').value = '';
            loadSectionData();
        }

        // 流标类型变化时自动更新公告标题
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('failureType').addEventListener('change', function() {
                const sectionName = document.getElementById('sectionName').value;
                const projectName = document.getElementById('projectName').value;
                if (sectionName && projectName && this.value) {
                    document.getElementById('announcementTitle').value = `${projectName} - ${this.value}公告`;
                }
            });
        });

        // 帮助弹窗
        function showHelp() {
            document.getElementById('help-modal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('help-modal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });

        // 标段选择弹窗点击外部关闭
        document.addEventListener('DOMContentLoaded', function() {
            const modal = document.getElementById('sectionSelectModal');
            if (modal) {
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        closeSectionSelectModal();
                    }
                });
            }
        });

        // 业务操作函数
        function goBack() {
            if (confirm('确定要返回吗？未保存的内容将丢失。')) {
                window.location.href = '流标或中止管理-列表页.html';
            }
        }

        function saveDraft() {
            console.log('保存草稿');
            alert('草稿保存成功！');
        }

        function preview() {
            console.log('预览公告');
            alert('预览功能待实现');
        }

        function submitForReview() {
            // 验证必填项
            const requiredFields = document.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.style.borderColor = '#ef4444';
                } else {
                    field.style.borderColor = '#d1d5db';
                }
            });
            
            if (!isValid) {
                alert('请填写所有必填项');
                return;
            }
            
            if (confirm('确定要提交审核吗？提交后将无法修改。')) {
                console.log('提交审核');
                alert('提交成功！');
                window.location.href = '流标或中止管理-列表页.html';
            }
        }
    </script>

    <!-- 标段选择弹窗 -->
    <div id="sectionSelectModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>请选择标段</h3>
                <span class="close" onclick="closeSectionSelectModal()">&times;</span>
            </div>
            
            <!-- 查询区域 -->
            <div class="search-section">
                <div class="search-grid">
                    <div class="search-group">
                        <label>标段名称：</label>
                        <input type="text" id="searchSectionName" placeholder="请输入标段名称">
                    </div>
                    <div class="search-group">
                        <label>项目名称：</label>
                        <input type="text" id="searchProjectName" placeholder="请输入项目名称">
                    </div>
                    <div class="search-group">
                        <label>采购方式：</label>
                        <select id="searchProcurementMethod">
                            <option value="">请选择</option>
                            <option value="公开招标">公开招标</option>
                            <option value="邀请招标">邀请招标</option>
                            <option value="竞争性谈判">竞争性谈判</option>
                            <option value="单一来源">单一来源</option>
                        </select>
                    </div>
                    <div class="search-group">
                        <label>采购类型：</label>
                        <select id="searchProcurementType">
                            <option value="">请选择</option>
                            <option value="货物类">货物类</option>
                            <option value="服务类">服务类</option>
                            <option value="工程类">工程类</option>
                        </select>
                    </div>
                    <div class="search-group">
                        <label>创建时间：</label>
                        <div class="date-range">
                            <input type="date" id="searchStartTime">
                            <span>至</span>
                            <input type="date" id="searchEndTime">
                        </div>
                    </div>
                </div>
                <div class="search-buttons">
                    <button type="button" class="btn btn-primary" onclick="searchSections()">查询</button>
                    <button type="button" class="btn btn-secondary" onclick="resetSearch()">重置</button>
                </div>
            </div>
            
            <!-- 列表区域 -->
            <div class="table-section">
                <table class="table">
                    <thead>
                        <tr>
                            <th width="50">选择</th>
                            <th width="200">标段名称</th>
                            <th width="200">所属项目</th>
                            <th width="120">采购方式</th>
                            <th width="120">采购类型</th>
                            <th width="120">创建时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 分页区域 -->
            <div class="pagination-section">
                <div class="pagination-info">
                    共 3 条记录，第 1/1 页
                </div>
                <div class="pagination-controls">
                    <button class="pagination-btn" disabled>上一页</button>
                    <button class="pagination-btn active">1</button>
                    <button class="pagination-btn" disabled>下一页</button>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="search-buttons" style="padding: 16px 24px; border-top: 1px solid #e6e8eb; background: #f8fafc;">
                <button type="button" class="btn btn-primary" onclick="confirmSectionSelection()">确认选择</button>
                <button type="button" class="btn btn-secondary" onclick="closeSectionSelectModal()">取消</button>
            </div>
        </div>
    </div>
</body>
</html>