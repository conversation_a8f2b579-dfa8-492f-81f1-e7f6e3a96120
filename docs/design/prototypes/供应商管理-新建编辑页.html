<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>供应商管理 - 新建/编辑</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 页面头部 */
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
        }

        .page-title::before {
            content: '';
            width: 4px;
            height: 20px;
            background: #3498db;
            margin-right: 12px;
        }

        .help-icon {
            margin-left: 8px;
            color: #95a5a6;
            cursor: pointer;
            font-size: 16px;
        }

        .back-btn {
            padding: 8px 16px;
            background: #95a5a6;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .back-btn:hover {
            background: #7f8c8d;
        }

        /* 表单容器 */
        .form-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        /* 表单分组 */
        .form-section {
            border-bottom: 1px solid #eee;
        }

        .form-section:last-child {
            border-bottom: none;
        }

        .section-header {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }

        .section-header::before {
            content: '';
            width: 3px;
            height: 16px;
            background: #3498db;
            margin-right: 10px;
        }

        .section-content {
            padding: 20px;
        }

        /* 表单布局 */
        .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row:last-child {
            margin-bottom: 0;
        }

        .form-group {
            flex: 1;
            min-width: 300px;
            max-width: 400px;
        }

        .form-group.full-width {
            flex: 1 1 100%;
            max-width: 100%;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .form-label .required {
            color: #e74c3c;
            margin-left: 2px;
        }

        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .form-control[readonly] {
            background-color: #f8f9fa;
            color: #6c757d;
        }

        .form-control.error {
            border-color: #e74c3c;
        }

        .error-message {
            color: #e74c3c;
            font-size: 12px;
            margin-top: 4px;
        }

        /* 文本域 */
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }

        /* 文件上传 */
        .file-upload {
            position: relative;
            display: inline-block;
        }

        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-button {
            display: inline-block;
            padding: 8px 16px;
            background: #3498db;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .file-button:hover {
            background: #2980b9;
        }

        .file-name {
            margin-left: 10px;
            color: #666;
            font-size: 14px;
        }

        /* 单选按钮组 */
        .radio-group {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .radio-item {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .radio-item input[type="radio"] {
            margin: 0;
        }

        .radio-item label {
            margin: 0;
            font-weight: normal;
            cursor: pointer;
        }

        /* 操作按钮区域 */
        .action-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-top: 20px;
            text-align: center;
        }

        .btn {
            padding: 10px 24px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            margin: 0 8px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 8px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .help-text {
            font-size: 14px;
            line-height: 1.8;
            color: #555;
            margin-bottom: 15px;
        }

        .close-help {
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            font-size: 24px;
            color: #95a5a6;
            cursor: pointer;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }
            
            .form-group {
                min-width: 100%;
                max-width: 100%;
            }
            
            .radio-group {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="page-title">
                新增供应商
                <span class="help-icon" onclick="showHelp()" title="帮助说明">ⓘ</span>
            </div>
            <button class="back-btn" onclick="goBack()">返回列表</button>
        </div>

        <!-- 表单容器 -->
        <div class="form-container">
            <form id="supplierForm">
                <!-- 基本信息 -->
                <div class="form-section">
                    <div class="section-header">基本信息</div>
                    <div class="section-content">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">境内外企业<span class="required">*</span></label>
                                <div class="radio-group">
                                    <div class="radio-item">
                                        <input type="radio" id="domestic" name="enterprise_type" value="domestic" checked>
                                        <label for="domestic">境内企业</label>
                                    </div>
                                    <div class="radio-item">
                                        <input type="radio" id="overseas" name="enterprise_type" value="overseas">
                                        <label for="overseas">境外企业</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">企业名称<span class="required">*</span></label>
                                <input type="text" class="form-control" name="company_name" placeholder="请输入企业名称" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">是否分公司<span class="required">*</span></label>
                                <div class="radio-group">
                                    <div class="radio-item">
                                        <input type="radio" id="branch_yes" name="is_branch" value="yes">
                                        <label for="branch_yes">是</label>
                                    </div>
                                    <div class="radio-item">
                                        <input type="radio" id="branch_no" name="is_branch" value="no" checked>
                                        <label for="branch_no">否</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">行业分类<span class="required">*</span></label>
                                <select class="form-control" name="industry" required>
                                    <option value="">请选择行业分类</option>
                                    <option value="construction">建筑业</option>
                                    <option value="manufacturing">制造业</option>
                                    <option value="technology">科技服务业</option>
                                    <option value="logistics">物流运输业</option>
                                    <option value="consulting">咨询服务业</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">联系人<span class="required">*</span></label>
                                <input type="text" class="form-control" name="contact_person" placeholder="请输入联系人" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">联系电话<span class="required">*</span></label>
                                <input type="tel" class="form-control" name="contact_phone" placeholder="请输入联系电话" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">联系邮箱</label>
                                <input type="email" class="form-control" name="contact_email" placeholder="请输入联系邮箱">
                            </div>
                            <div class="form-group">
                                <label class="form-label">法定代表人<span class="required">*</span></label>
                                <input type="text" class="form-control" name="legal_representative" placeholder="请输入法定代表人" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">营业执照附件</label>
                                <div class="file-upload">
                                    <input type="file" class="file-input" name="business_license" accept=".pdf,.jpg,.jpeg,.png">
                                    <span class="file-button">选择文件</span>
                                    <span class="file-name">未选择文件</span>
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group full-width">
                                <label class="form-label">企业通讯地址</label>
                                <input type="text" class="form-control" name="communication_address" placeholder="请输入企业通讯地址">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group full-width">
                                <label class="form-label">注册地址</label>
                                <input type="text" class="form-control" name="registered_address" placeholder="请输入注册地址">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group full-width">
                                <label class="form-label">经营范围</label>
                                <textarea class="form-control form-textarea" name="business_scope" placeholder="请输入经营范围"></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 账户信息 -->
                <div class="form-section">
                    <div class="section-header">账户信息</div>
                    <div class="section-content">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">账户名称</label>
                                <input type="text" class="form-control" name="account_name" placeholder="请输入账户名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">账号</label>
                                <input type="text" class="form-control" name="account_number" placeholder="请输入账号">
                            </div>
                            <div class="form-group">
                                <label class="form-label">开户银行</label>
                                <input type="text" class="form-control" name="bank_name" placeholder="请输入开户银行">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 扩展信息 -->
                <div class="form-section">
                    <div class="section-header">扩展信息</div>
                    <div class="section-content">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">企业性质</label>
                                <select class="form-control" name="enterprise_nature">
                                    <option value="">请选择企业性质</option>
                                    <option value="state_owned">国有企业</option>
                                    <option value="private">民营企业</option>
                                    <option value="foreign">外资企业</option>
                                    <option value="joint_venture">合资企业</option>
                                    <option value="other">其他</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">所属集团名称</label>
                                <input type="text" class="form-control" name="group_name" placeholder="请输入所属集团名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">母公司名称</label>
                                <input type="text" class="form-control" name="parent_company" placeholder="请输入母公司名称">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">供应商标签</label>
                                <input type="text" class="form-control" name="supplier_tags" placeholder="请输入供应商标签">
                            </div>
                            <div class="form-group">
                                <label class="form-label">标签有效期</label>
                                <input type="date" class="form-control" name="tag_expiry_date">
                            </div>
                            <div class="form-group">
                                <label class="form-label">企业官网</label>
                                <input type="url" class="form-control" name="company_website" placeholder="请输入企业官网">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">企业logo</label>
                                <div class="file-upload">
                                    <input type="file" class="file-input" name="company_logo" accept=".jpg,.jpeg,.png,.gif">
                                    <span class="file-button">选择文件</span>
                                    <span class="file-name">未选择文件</span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">企业标签</label>
                                <input type="text" class="form-control" name="enterprise_tags" placeholder="请输入企业标签">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 税务信息 -->
                <div class="form-section">
                    <div class="section-header">税务信息</div>
                    <div class="section-content">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">税务登记号</label>
                                <input type="text" class="form-control" name="tax_registration_number" placeholder="请输入税务登记号">
                            </div>
                            <div class="form-group">
                                <label class="form-label">税务类型</label>
                                <select class="form-control" name="tax_type">
                                    <option value="">请选择税务类型</option>
                                    <option value="general">一般纳税人</option>
                                    <option value="small">小规模纳税人</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">税务登记证失效日期</label>
                                <input type="date" class="form-control" name="tax_cert_expiry_date">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">税务扫描件</label>
                                <div class="file-upload">
                                    <input type="file" class="file-input" name="tax_certificate" accept=".pdf,.jpg,.jpeg,.png">
                                    <span class="file-button">选择文件</span>
                                    <span class="file-name">未选择文件</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 操作按钮 -->
        <div class="action-section">
            <button type="button" class="btn btn-primary" onclick="saveSupplier()">保存</button>
            <button type="button" class="btn btn-success" onclick="saveAndSubmit()">保存并提交</button>
            <button type="button" class="btn btn-secondary" onclick="goBack()">取消</button>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div class="help-modal" id="helpModal">
        <div class="help-content">
            <button class="close-help" onclick="hideHelp()">&times;</button>
            <div class="help-title">供应商管理 - 新建/编辑说明</div>
            <div class="help-text">
                <strong>功能说明：</strong><br>
                本页面用于新建或编辑供应商信息，包含基本信息、账户信息、扩展信息和税务信息四个模块。
            </div>
            <div class="help-text">
                <strong>基本信息：</strong><br>
                • <strong>境内外企业：</strong>选择企业类型（境内/境外）<br>
                • <strong>企业名称：</strong>必填，供应商企业全称<br>
                • <strong>是否分公司：</strong>选择是否为分公司<br>
                • <strong>行业分类：</strong>必填，选择所属行业<br>
                • <strong>联系人：</strong>必填，主要联系人姓名<br>
                • <strong>联系电话：</strong>必填，联系电话号码<br>
                • <strong>法定代表人：</strong>必填，企业法定代表人
            </div>
            <div class="help-text">
                <strong>账户信息：</strong><br>
                用于记录供应商的银行账户信息，包括账户名称、账号和开户银行。
            </div>
            <div class="help-text">
                <strong>扩展信息：</strong><br>
                包含企业性质、集团信息、标签信息、官网和logo等扩展属性。
            </div>
            <div class="help-text">
                <strong>税务信息：</strong><br>
                记录供应商的税务相关信息，包括税务登记号、税务类型等。
            </div>
            <div class="help-text">
                <strong>操作说明：</strong><br>
                1. 填写必填字段（标有红色*号）<br>
                2. 点击"保存"暂存信息<br>
                3. 点击"保存并提交"完成创建<br>
                4. 点击"取消"返回列表页
            </div>
        </div>
    </div>

    <script>
        // 文件上传处理
        document.querySelectorAll('.file-input').forEach(input => {
            input.addEventListener('change', function() {
                const fileName = this.files[0] ? this.files[0].name : '未选择文件';
                this.parentNode.querySelector('.file-name').textContent = fileName;
            });
        });

        // 表单验证
        function validateForm() {
            const form = document.getElementById('supplierForm');
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('error');
                    isValid = false;
                } else {
                    field.classList.remove('error');
                }
            });

            return isValid;
        }

        // 保存供应商
        function saveSupplier() {
            if (validateForm()) {
                alert('供应商信息已保存！');
                // 这里可以添加实际的保存逻辑
            } else {
                alert('请填写所有必填字段！');
            }
        }

        // 保存并提交
        function saveAndSubmit() {
            if (validateForm()) {
                alert('供应商信息已保存并提交！');
                // 这里可以添加实际的保存和提交逻辑
                setTimeout(() => {
                    goBack();
                }, 1000);
            } else {
                alert('请填写所有必填字段！');
            }
        }

        // 返回列表页
        function goBack() {
            window.location.href = '供应商管理-列表页.html';
        }

        // 帮助弹窗
        function showHelp() {
            document.getElementById('helpModal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('helpModal').style.display = 'none';
        }

        // 点击弹窗外部关闭弹窗
        document.getElementById('helpModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });

        // 实时验证
        document.querySelectorAll('.form-control[required]').forEach(field => {
            field.addEventListener('blur', function() {
                if (!this.value.trim()) {
                    this.classList.add('error');
                } else {
                    this.classList.remove('error');
                }
            });
        });
    </script>
</body>
</html>