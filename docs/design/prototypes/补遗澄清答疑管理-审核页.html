<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>补遗/澄清/答疑管理 - 审核 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #fff;
            border-bottom: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
        }

        .help-icon {
            margin-left: 8px;
            width: 16px;
            height: 16px;
            background: #6b7280;
            border-radius: 50%;
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .breadcrumb {
            color: #6b7280;
            font-size: 14px;
        }

        .back-btn {
            background: #6b7280;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
        }

        .back-btn:hover {
            background: #4b5563;
        }

        /* 内容区域 */
        .content-area {
            padding: 24px;
        }

        /* 详情页签样式 */
        .detail-tabs {
            display: flex;
            border-bottom: 1px solid #e6e8eb;
            margin-bottom: 24px;
        }

        .detail-tab {
            padding: 12px 24px;
            cursor: pointer;
            color: #6b7280;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .detail-tab.active {
            color: #2563eb;
            border-bottom-color: #2563eb;
        }

        .detail-tab:hover {
            color: #2563eb;
        }

        /* 详情内容 */
        .detail-content {
            display: none;
        }

        .detail-content.active {
            display: block;
        }

        /* 详情区域 */
        .detail-section {
            background: white;
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .section-header {
            background: #f8fafc;
            border-bottom: 1px solid #e6e8eb;
            padding: 15px 20px;
            font-weight: 600;
            color: #1f2937;
            font-size: 14px;
            position: relative;
        }

        .section-header::after {
            content: '';
            position: absolute;
            left: 20px;
            bottom: 0;
            width: 60px;
            height: 2px;
            background: #2563eb;
        }

        .section-content {
            padding: 20px;
        }

        /* 信息展示 */
        .info-section {
            margin-bottom: 32px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #1890ff;
            position: relative;
        }
        
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            bottom: -2px;
            width: 40px;
            height: 2px;
            background: #1890ff;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .info-grid.full-width {
            grid-template-columns: 1fr;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .info-label {
            font-size: 14px;
            color: #8c8c8c;
            font-weight: 500;
        }
        
        .info-value {
            font-size: 14px;
            color: #262626;
            word-break: break-all;
        }
        
        .info-value.rich-content {
            line-height: 1.8;
        }
        
        .info-value.rich-content p {
            margin-bottom: 12px;
        }
        
        .info-value.rich-content ul {
            margin-left: 20px;
            margin-bottom: 12px;
        }
        
        .info-value.rich-content li {
            margin-bottom: 6px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending {
            background: #fff7e6;
            color: #d46b08;
            border: 1px solid #ffd591;
        }
        
        .status-approved {
            background: #f6ffed;
            color: #389e0d;
            border: 1px solid #b7eb8f;
        }
        
        .status-rejected {
            background: #fff2f0;
            color: #cf1322;
            border: 1px solid #ffccc7;
        }
        
        .status-published {
            background: #e6f7ff;
            color: #0958d9;
            border: 1px solid #91d5ff;
        }
        
        .link-text {
            color: #1890ff;
            text-decoration: none;
            cursor: pointer;
        }
        
        .link-text:hover {
            color: #40a9ff;
            text-decoration: underline;
        }
        
        /* 文件列表 */
        .file-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
        }
        
        .file-name {
            font-size: 14px;
            color: #262626;
        }
        
        .file-download {
            color: #1890ff;
            text-decoration: none;
            font-size: 13px;
            padding: 4px 8px;
            border: 1px solid #1890ff;
            border-radius: 3px;
            transition: all 0.3s;
        }
        
        .file-download:hover {
            background: #1890ff;
            color: white;
        }
        
        /* 详情项 */
        .detail-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .detail-label {
            font-size: 14px;
            color: #8c8c8c;
            font-weight: 500;
        }
        
        .detail-value {
            font-size: 14px;
            color: #262626;
            word-break: break-all;
        }
        
        .detail-value.rich-content {
            line-height: 1.8;
        }
        
        .detail-value.rich-content p {
            margin-bottom: 12px;
        }
        
        .detail-value.rich-content ul {
            margin-left: 20px;
            margin-bottom: 12px;
        }
        
        .detail-value.rich-content li {
            margin-bottom: 6px;
        }
        
        /* 操作记录时间线 */
        .operation-timeline {
            position: relative;
            padding-left: 20px;
        }
        
        .operation-timeline::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e6e8eb;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 24px;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -16px;
            top: 6px;
            width: 8px;
            height: 8px;
            background: #2563eb;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 0 0 2px #2563eb;
        }
        
        .timeline-content {
            background: #f8fafc;
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            padding: 16px;
        }
        
        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .timeline-action {
            font-weight: 600;
            color: #1f2937;
            font-size: 14px;
        }
        
        .timeline-time {
            color: #6b7280;
            font-size: 13px;
        }
        
        .timeline-user {
            color: #6b7280;
            font-size: 13px;
            margin-bottom: 8px;
        }
        
        .timeline-desc {
            color: #374151;
            font-size: 14px;
            line-height: 1.5;
        }
        
        /* 审核区域 */
        .approval-section {
            background: #f8fafc;
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            padding: 20px;
            margin-top: 24px;
        }
        
        .approval-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 16px;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
        }
        
        .required {
            color: #ef4444;
        }
        
        .form-textarea {
            width: 100%;
            min-height: 100px;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 14px;
            font-family: inherit;
            resize: vertical;
            transition: border-color 0.3s;
        }
        
        .form-textarea:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        /* 操作按钮 */
        .action-bar {
            background: #f8fafc;
            border-top: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .btn-group {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        .btn-outline {
            background: white;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }
        
        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background: #dc2626;
        }
        
        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .help-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e6e8eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .help-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            color: #6b7280;
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
        }
        
        .close-btn:hover {
            background: #f3f4f6;
            color: #374151;
        }
        
        .help-body {
            padding: 24px;
        }
        
        .help-section {
            margin-bottom: 24px;
        }
        
        .help-section h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
        }
        
        .help-section p {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 12px;
        }
        
        .help-list {
            list-style: none;
            padding: 0;
        }
        
        .help-list li {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 8px;
            padding-left: 16px;
            position: relative;
        }
        
        .help-list li::before {
            content: '•';
            color: #2563eb;
            position: absolute;
            left: 0;
        }
        
        .help-list li strong {
            color: #1f2937;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                margin: 0;
            }
            
            .page-header {
                padding: 16px;
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }
            
            .content-area {
                padding: 16px;
            }
            
            .detail-tabs {
                flex-wrap: wrap;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .detail-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .action-bar {
                padding: 16px;
                flex-direction: column;
                gap: 16px;
            }
            
            .btn-group {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">


        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 页面标题和状态 -->
            <div class="page-title-section">
                <div class="title-content">
                    <div class="title-main">关于办公设备采购项目技术规格的补遗说明</div>
                    <div class="title-sub">CLA-2024-001</div>
                    <div class="title-status">
                        <span class="status-badge status-pending">待审核</span>
                    </div>
                </div>
            </div>
            
            <!-- 详情页签 -->
            <div class="detail-tabs">
                <div class="detail-tab active" data-tab="basic-info">基本信息</div>
                <div class="detail-tab" data-tab="project-info">项目信息</div>
                <div class="detail-tab" data-tab="operation-record">操作记录</div>
            </div>

            <!-- 基本信息 -->
            <div class="detail-content active" id="basic-info">
                <!-- 公告信息 -->
                <div class="info-section">
                    <div class="section-title">公告信息</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">公告标题</div>
                            <div class="info-value">关于办公设备采购项目技术规格的补遗说明</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">公告阶段</div>
                            <div class="info-value">招标阶段</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">公告状态</div>
                            <div class="info-value"><span class="status-badge status-pending">待审核</span></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">是否公示</div>
                            <div class="info-value">否</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">类型</div>
                            <div class="info-value">补遗</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">审核依据</div>
                            <div class="info-value">《招标投标法》第二十三条</div>
                        </div>
                    </div>
                </div>
                
                <!-- 补遗/澄清/答疑信息 -->
                <div class="info-section">
                    <div class="section-title">补遗/澄清/答疑信息</div>
                    <div class="info-grid full-width">
                        <div class="info-item">
                            <div class="info-label">补遗/澄清/答疑说明</div>
                            <div class="info-value rich-content">
                                根据投标人反馈，现对招标文件中的技术规格进行补遗说明：<br>
                                1. 办公桌椅的材质要求调整为环保E1级板材<br>
                                2. 电脑配置要求增加固态硬盘256GB<br>
                                3. 打印机要求支持双面打印功能<br>
                                请各投标人据此调整投标方案。
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 文件信息 -->
                <div class="info-section">
                    <div class="section-title">文件信息</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">补遗文件</div>
                            <div class="info-value">
                                <div class="file-list">
                                    <div class="file-item">
                                        <span class="file-name">技术规格补遗说明.pdf</span>
                                        <a href="#" class="file-download">下载</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">技术文件</div>
                            <div class="info-value">
                                <div class="file-list">
                                    <div class="file-item">
                                        <span class="file-name">技术规格书修订版.docx</span>
                                        <a href="#" class="file-download">下载</a>
                                    </div>
                                    <div class="file-item">
                                        <span class="file-name">产品参数对比表.xlsx</span>
                                        <a href="#" class="file-download">下载</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
            
            <!-- 项目信息 -->
            <div class="detail-content" id="project-info">
                <!-- 项目信息 -->
                <div class="info-section">
                    <div class="section-title">项目信息</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">计划项目编号</div>
                            <div class="info-value">CG-2024-001</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">计划项目名称</div>
                            <div class="info-value">办公设备采购项目</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">项目类型</div>
                            <div class="info-value">货物采购</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">项目业主</div>
                            <div class="info-value">某某集团有限公司</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">采购类型</div>
                            <div class="info-value">集中采购</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">采购方式</div>
                            <div class="info-value">公开招标</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">招标类别</div>
                            <div class="info-value">货物招标</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">采购组织方式</div>
                            <div class="info-value">委托代理</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">所属二级公司单位</div>
                            <div class="info-value">某某分公司</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">代理机构</div>
                            <div class="info-value">某某招标代理有限公司</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">备注</div>
                            <div class="info-value">办公设备统一采购，包含桌椅、电脑、打印机等</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">附件</div>
                            <div class="info-value">
                                <div class="file-list">
                                    <div class="file-item">
                                        <span class="file-name">项目立项申请书.pdf</span>
                                        <a href="#" class="file-download">下载</a>
                                    </div>
                                    <div class="file-item">
                                        <span class="file-name">预算明细表.xlsx</span>
                                        <a href="#" class="file-download">下载</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 标段信息 -->
                <div class="info-section">
                    <div class="section-title">标段信息</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">标段编号</div>
                            <div class="info-value"><a href="项目标段管理-标段详情页.html" class="link-text">BD-001</a></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">标段名称</div>
                            <div class="info-value"><a href="项目标段管理-标段详情页.html" class="link-text">办公桌椅采购标段</a></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">标段阶段</div>
                            <div class="info-value">招标阶段</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">审核状态</div>
                            <div class="info-value"><span class="status-badge status-pending">待审核</span></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">标段说明</div>
                            <div class="info-value">办公桌椅采购，包含办公桌、办公椅等办公家具</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 操作记录 -->
            <div class="detail-content" id="operation-record">
                <div class="info-section">
                    <div class="section-title">操作记录</div>
                    <div class="operation-timeline">
                        <div class="timeline-item">
                            <div class="timeline-content">
                                <div class="timeline-header">
                                    <div class="timeline-action">提交审核</div>
                                    <div class="timeline-time">2024-03-15 17:45:30</div>
                                </div>
                                <div class="timeline-user">操作人：张三（采购专员）</div>
                                <div class="timeline-desc">
                                    提交补遗内容审核，等待审核人员审核。状态变更：草稿 → 待审核
                                </div>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-content">
                                <div class="timeline-header">
                                    <div class="timeline-action">编辑</div>
                                    <div class="timeline-time">2024-03-15 14:15:42</div>
                                </div>
                                <div class="timeline-user">操作人：张三</div>
                                <div class="timeline-desc">
                                    完善补遗内容和相关文件。状态变更：草稿 → 草稿
                                </div>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-content">
                                <div class="timeline-header">
                                    <div class="timeline-action">创建</div>
                                    <div class="timeline-time">2024-03-15 10:30:25</div>
                                </div>
                                <div class="timeline-user">操作人：张三</div>
                                <div class="timeline-desc">
                                    创建补遗/澄清/答疑，关联标段：办公桌椅采购标段。状态变更：无 → 草稿
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-bar">
            <div class="btn-group">
                <button class="btn btn-outline" onclick="goBack()">返回列表</button>
                <button class="btn btn-secondary" onclick="printClarification()">打印</button>
                <button class="btn btn-secondary" onclick="exportClarification()">导出</button>
            </div>
            <div class="btn-group">
                <button class="btn btn-success" onclick="approveClarification()">审核通过</button>
                <button class="btn btn-danger" onclick="rejectClarification()">审核驳回</button>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="help-modal" class="help-modal">
        <div class="help-content">
            <div class="help-header">
                <h3 class="help-title">补遗/澄清/答疑审核功能说明</h3>
                <button class="close-btn" onclick="hideHelp()">&times;</button>
            </div>
            <div class="help-body">
                <div class="help-section">
                    <h4>功能概述</h4>
                    <p>补遗/澄清/答疑审核页面用于审核补遗、澄清或答疑的内容，确保信息准确性和合规性。</p>
                </div>
                
                <div class="help-section">
                    <h4>页签说明</h4>
                    <ul class="help-list">
                        <li><strong>基本信息：</strong>显示补遗/澄清/答疑的详细内容，包括标题、类型、内容、影响范围等</li>
                        <li><strong>项目信息：</strong>显示关联项目和标段的详细信息</li>
                        <li><strong>操作记录：</strong>显示从创建到当前的全部操作历史</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>审核操作</h4>
                    <ul class="help-list">
                        <li><strong>审核通过：</strong>确认内容无误，允许发布</li>
                        <li><strong>审核驳回：</strong>发现问题，退回修改</li>
                        <li><strong>打印：</strong>打印补遗/澄清/答疑内容</li>
                        <li><strong>导出：</strong>导出为PDF或Word文档</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>审核要点</h4>
                    <ul class="help-list">
                        <li>检查内容是否准确、完整</li>
                        <li>确认是否符合招标文件要求</li>
                        <li>验证影响范围是否合理</li>
                        <li>检查相关文件是否齐全</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>注意事项</h4>
                    <ul class="help-list">
                        <li>审核通过后内容将自动发布给相关投标人</li>
                        <li>审核驳回时请详细说明驳回原因</li>
                        <li>审核操作不可撤销，请谨慎操作</li>
                        <li>重要内容建议多人审核确认</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页签切换
        document.querySelectorAll('.detail-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                const targetTab = this.getAttribute('data-tab');
                
                // 移除所有激活状态
                document.querySelectorAll('.detail-tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.detail-content').forEach(c => c.classList.remove('active'));
                
                // 激活当前页签和内容
                this.classList.add('active');
                document.getElementById(targetTab).classList.add('active');
            });
        });

        // 帮助弹窗
        function showHelp() {
            document.getElementById('help-modal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('help-modal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });

        // 业务操作函数
        function goBack() {
            window.history.back();
        }

        function printClarification() {
            // 打印补遗/澄清/答疑内容
            window.print();
        }

        function exportClarification() {
            console.log('导出补遗/澄清/答疑');
            alert('导出功能开发中...');
        }

        function approveClarification() {
            const comment = document.getElementById('approval-comment').value.trim();
            if (!comment) {
                alert('请输入审核意见');
                return;
            }
            
            if (confirm('确定审核通过吗？')) {
                console.log('审核通过，意见：', comment);
                alert('审核通过成功！补遗/澄清/答疑将自动发布给相关投标人。');
                // 这里应该调用后端API提交审核结果
                // 审核成功后跳转回列表页
                setTimeout(() => {
                    window.history.back();
                }, 1500);
            }
        }

        function rejectClarification() {
            const comment = document.getElementById('approval-comment').value.trim();
            if (!comment) {
                alert('请输入审核驳回原因');
                return;
            }
            
            if (confirm('确定审核驳回吗？')) {
                console.log('审核驳回，原因：', comment);
                alert('审核驳回成功！补遗/澄清/答疑已退回给创建人修改。');
                // 这里应该调用后端API提交审核结果
                // 审核成功后跳转回列表页
                setTimeout(() => {
                    window.history.back();
                }, 1500);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('补遗/澄清/答疑审核页面加载完成');
        });
    </script>
</body>
</html>