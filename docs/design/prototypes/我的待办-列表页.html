<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的待办 - 招采平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 页面头部 */
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .back-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .back-btn:hover {
            background: #5a6268;
        }

        /* 查询区域 */
        .search-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-size: 13px;
            color: #666;
            margin-bottom: 5px;
        }

        .form-group input,
        .form-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
        }

        .search-buttons {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        /* 功能操作栏 */
        .action-bar {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        /* 页签区域 */
        .tab-container {
            background: white;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .tab-header {
            display: flex;
            border-bottom: 1px solid #e9ecef;
        }

        .tab-item {
            padding: 15px 25px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            color: #666;
            font-size: 14px;
            transition: all 0.3s;
        }

        .tab-item.active {
            color: #3498db;
            border-bottom-color: #3498db;
            background: #f8f9fa;
        }

        .tab-item:hover {
            background: #f8f9fa;
        }

        /* 数据列表区 */
        .table-container {
            background: white;
            border-radius: 0 0 8px 8px;
            overflow: hidden;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }

        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            font-size: 13px;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table tbody tr:hover {
            background: #f8f9fa;
        }

        /* 冻结列样式 */
        .data-table th:first-child,
        .data-table td:first-child {
            position: sticky;
            left: 0;
            background: white;
            z-index: 5;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }

        .data-table th:first-child {
            z-index: 15;
            background: #f8f9fa;
        }

        .data-table th:last-child,
        .data-table td:last-child {
            position: sticky;
            right: 0;
            background: white;
            z-index: 5;
            box-shadow: -2px 0 5px rgba(0,0,0,0.1);
        }

        .data-table th:last-child {
            z-index: 15;
            background: #f8f9fa;
        }

        /* 状态标签 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-urgent {
            background: #f8d7da;
            color: #721c24;
        }

        .status-normal {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-processed {
            background: #d4edda;
            color: #155724;
        }

        /* 操作按钮 */
        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 5px;
        }

        .btn-handle {
            background: #3498db;
            color: white;
        }

        .btn-handle:hover {
            background: #2980b9;
        }

        .btn-view {
            background: #95a5a6;
            color: white;
        }

        .btn-view:hover {
            background: #7f8c8d;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .pagination-info {
            color: #666;
            font-size: 13px;
        }

        .pagination-controls {
            display: flex;
            gap: 5px;
        }

        .page-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            font-size: 13px;
        }

        .page-btn:hover {
            background: #f8f9fa;
        }

        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .search-form {
                grid-template-columns: 1fr;
            }
            
            .action-bar {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="page-title">
                📝 我的待办
                <span style="font-size: 12px; color: #666; margin-left: 10px;">ℹ️</span>
            </div>
            <button class="back-btn" onclick="goBack()">返回</button>
        </div>

        <!-- 查询区域 -->
        <div class="search-section">
            <div class="search-form">
                <div class="form-group">
                    <label>标题名称</label>
                    <input type="text" placeholder="请输入待办标题名称">
                </div>
                <div class="form-group">
                    <label>创建时间</label>
                    <input type="date">
                </div>
                <div class="search-buttons">
                    <button class="btn btn-primary">查询</button>
                    <button class="btn btn-secondary">重置</button>
                </div>
            </div>
        </div>

        <!-- 数据列表 -->
        <div class="table-container" style="border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th style="width: 400px;">标题名称</th>
                            <th style="width: 150px;">所属模块</th>
                            <th style="width: 180px;">创建时间</th>
                            <th style="width: 120px;">操作</th>
                        </tr>
                    </thead>
                    <tbody id="todoTableBody">
                        <tr>
                            <td><a href="#" onclick="viewTodoDetail('todo-001')" style="color: #3498db; text-decoration: none;">办公设备采购项目-标段001需要审核</a></td>
                            <td>公告管理</td>
                            <td>2023-12-10 14:30</td>
                            <td>
                                <button class="action-btn btn-handle" onclick="handleTodo('公告管理-审核页.html')">去处理</button>
                            </td>
                        </tr>
                        <tr>
                            <td><a href="#" onclick="viewTodoDetail('todo-002')" style="color: #3498db; text-decoration: none;">IT基础设施建设项目-标段002需要审核</a></td>
                            <td>项目标段管理</td>
                            <td>2023-12-11 09:15</td>
                            <td>
                                <button class="action-btn btn-handle" onclick="handleTodo('项目标段管理-审核页.html')">去处理</button>
                            </td>
                        </tr>
                        <tr>
                            <td><a href="#" onclick="viewTodoDetail('todo-003')" style="color: #3498db; text-decoration: none;">数据中心设备采购项目-标段003需要审核</a></td>
                            <td>签约履行管理</td>
                            <td>2023-12-12 16:45</td>
                            <td>
                                <button class="action-btn btn-handle" onclick="handleTodo('签约履行管理-审核页.html')">去处理</button>
                            </td>
                        </tr>
                        <tr>
                            <td><a href="#" onclick="viewTodoDetail('todo-004')" style="color: #3498db; text-decoration: none;">网络安全设备采购项目-标段004需要审核</a></td>
                            <td>供应商管理</td>
                            <td>2023-12-13 11:20</td>
                            <td>
                                <button class="action-btn btn-handle" onclick="handleTodo('供应商管理-详情页.html')">去处理</button>
                            </td>
                        </tr>
                        <tr>
                            <td><a href="#" onclick="viewTodoDetail('todo-005')" style="color: #3498db; text-decoration: none;">软件系统开发项目-标段005需要审核</a></td>
                            <td>采购计划管理</td>
                            <td>2023-12-14 08:30</td>
                            <td>
                                <button class="action-btn btn-handle" onclick="handleTodo('采购计划管理-详情页.html')">去处理</button>
                            </td>
                        </tr>
                        <tr>
                            <td><a href="#" onclick="viewTodoDetail('todo-006')" style="color: #3498db; text-decoration: none;">机房建设工程项目-标段006需要审核</a></td>
                            <td>评标结果公示管理</td>
                            <td>2023-12-15 10:20</td>
                            <td>
                                <button class="action-btn btn-handle" onclick="handleTodo('评标结果公示管理-审核页.html')">去处理</button>
                            </td>
                        </tr>
                        <tr>
                            <td><a href="#" onclick="viewTodoDetail('todo-007')" style="color: #3498db; text-decoration: none;">监控系统升级项目-标段007需要审核</a></td>
                            <td>中标结果公示管理</td>
                            <td>2023-12-16 09:45</td>
                            <td>
                                <button class="action-btn btn-handle" onclick="handleTodo('中标结果公示管理-审核页.html')">去处理</button>
                            </td>
                        </tr>
                        <tr>
                            <td><a href="#" onclick="viewTodoDetail('todo-008')" style="color: #3498db; text-decoration: none;">云平台服务采购项目-标段008需要审核</a></td>
                            <td>补遗澄清答疑管理</td>
                            <td>2023-12-17 14:15</td>
                            <td>
                                <button class="action-btn btn-handle" onclick="handleTodo('补遗澄清答疑管理-审核页.html')">去处理</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- 分页 -->
                <div class="pagination">
                    <div class="pagination-info">
                        显示第 1-5 条，共 23 条记录
                    </div>
                    <div class="pagination-controls">
                        <button class="page-btn">上一页</button>
                        <button class="page-btn active">1</button>
                        <button class="page-btn">2</button>
                        <button class="page-btn">3</button>
                        <button class="page-btn">4</button>
                        <button class="page-btn">5</button>
                        <button class="page-btn">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 返回上一页
        function goBack() {
            window.history.back();
        }

        // 页签切换
        function switchTab(type) {
            // 移除所有活动状态
            document.querySelectorAll('.tab-item').forEach(tab => tab.classList.remove('active'));
            // 激活当前页签
            event.target.classList.add('active');
            
            // 根据类型加载不同数据
            loadTodoData(type);
        }

        // 加载待办数据
        function loadTodoData(type) {
            console.log('加载待办数据:', type);
            // 这里可以通过AJAX加载不同类型的待办数据
        }

        // 全选/取消全选
        function selectAll(checkbox) {
            const checkboxes = document.querySelectorAll('#todoTableBody input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = checkbox.checked);
        }

        // 处理待办事项
        function handleTodo(pageUrl) {
            // 直接跳转到指定的处理页面
            window.location.href = pageUrl;
        }

        // 查看待办详情
        function viewTodoDetail(todoId) {
            console.log('查看待办详情:', todoId);
            alert('跳转到待办详情页面: ' + todoId);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('我的待办列表页加载完成');
        });
    </script>
</body>
</html>