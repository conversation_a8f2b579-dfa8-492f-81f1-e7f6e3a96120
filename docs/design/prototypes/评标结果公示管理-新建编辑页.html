<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评标结果公示管理 - 新建/编辑 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            min-height: 100vh;
        }

        /* 模态弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: #fff;
            margin: 2% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 1200px;
            max-height: 90vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #e0e6ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f8f9fa;
        }

        .modal-header h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 18px;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            color: #333;
        }

        .modal-body {
            flex: 1;
            overflow-y: auto;
            padding: 0;
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid #e0e6ed;
            background-color: #f8f9fa;
            text-align: right;
        }

        .modal-footer .btn {
            margin-left: 10px;
        }

        /* 分页样式 */
        .pagination-section {
            padding: 20px;
            border-top: 1px solid #e0e6ed;
            background-color: #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pagination-info {
            color: #666;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .page-numbers {
            display: flex;
            gap: 5px;
        }

        .page-number {
            min-width: 32px;
            height: 32px;
            border: 1px solid #ddd;
            background: #fff;
            color: #333;
        }

        .page-number.active {
            background: #007bff;
            color: #fff;
            border-color: #007bff;
        }

        .page-size-selector {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .page-size-selector select {
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        /* 页面头部 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid #e0e6ed;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .help-icon {
            width: 20px;
            height: 20px;
            background: #007bff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .help-icon:hover {
            background: #0056b3;
        }

        .breadcrumb {
            color: #6c757d;
            font-size: 14px;
        }

        .breadcrumb a {
            color: #007bff;
            text-decoration: none;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .back-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .back-btn:hover {
            background: #545b62;
        }

        /* 表单区域 */
        .form-section {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 2px solid #007bff;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 60px;
            height: 2px;
            background: #007bff;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            align-items: start;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-group label {
            font-weight: 500;
            margin-bottom: 8px;
            color: #2c3e50;
            font-size: 14px;
        }

        .form-group label.required::after {
            content: ' *';
            color: #e74c3c;
        }

        .form-input {
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s, box-shadow 0.3s;
        }

        .form-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .form-input:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
        }

        .input-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .input-group .form-input {
            flex: 1;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        /* 富文本编辑器 */
        .rich-editor {
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }

        .editor-toolbar {
            background: #f8f9fa;
            padding: 8px 12px;
            border-bottom: 1px solid #ddd;
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .editor-btn {
            padding: 4px 8px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.3s;
        }

        .editor-btn:hover {
            background: #e9ecef;
        }

        .editor-content {
            min-height: 200px;
            padding: 12px;
            outline: none;
            line-height: 1.6;
        }

        .editor-content:focus {
            box-shadow: inset 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        /* 底部操作栏 */
        .action-bar {
            position: sticky;
            bottom: 0;
            background: #fff;
            padding: 20px 0;
            border-top: 1px solid #e0e6ed;
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 100;
        }

        .btn-group {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #1e7e34;
            transform: translateY(-1px);
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-info:hover {
            background: #117a8b;
            transform: translateY(-1px);
        }

        /* 搜索区域样式 */
        .search-section {
            padding: 20px;
            border-bottom: 1px solid #e0e6ed;
        }

        .search-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .search-group {
            display: flex;
            flex-direction: column;
        }

        .search-group label {
            font-weight: 500;
            margin-bottom: 5px;
            color: #2c3e50;
            font-size: 14px;
        }

        .search-group input,
        .search-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .date-range {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .date-range input {
            flex: 1;
        }

        .search-buttons {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        /* 表格区域样式 */
        .table-section {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
            background: #fff;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e6ed;
            vertical-align: middle;
        }

        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .help-content {
            background-color: #fff;
            margin: 5% auto;
            padding: 0;
            border-radius: 8px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .help-header {
            padding: 20px;
            border-bottom: 1px solid #e0e6ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f8f9fa;
        }

        .help-header h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 18px;
        }

        .help-body {
            padding: 20px;
            overflow-y: auto;
            flex: 1;
        }

        .help-body h4 {
            color: #007bff;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .help-body h4:first-child {
            margin-top: 0;
        }

        .help-body p {
            margin-bottom: 10px;
            line-height: 1.6;
        }

        .help-body ul {
            margin-bottom: 15px;
            padding-left: 20px;
        }

        .help-body li {
            margin-bottom: 5px;
        }

        .help-body .note {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 10px 15px;
            margin: 15px 0;
        }

        .help-body .warning {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 10px 15px;
            margin: 15px 0;
        }

        /* 候选人表格样式 */
        .candidate-table-container {
            margin-top: 15px;
        }

        .table-toolbar {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            align-items: center;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 3px;
        }

        .table-wrapper {
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow-x: auto;
            background: #fff;
        }

        .candidate-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
            min-width: 950px;
        }

        .candidate-table th {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 12px 8px;
            text-align: center;
            font-weight: 600;
            color: #333;
            white-space: nowrap;
        }

        .candidate-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
        }

        .candidate-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .table-input {
            width: 100%;
            border: none;
            background: transparent;
            padding: 6px 8px;
            font-size: 13px;
            text-align: center;
            outline: none;
        }

        .table-input:focus {
            background-color: #fff;
            border: 1px solid #4a90e2;
            border-radius: 3px;
        }

        .table-input[readonly] {
            background-color: #f8f9fa;
            color: #666;
        }

        .btn-delete {
            background: #dc3545;
            color: white;
            border: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 14px;
            line-height: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
        }

        .btn-delete:hover {
            background: #c82333;
        }

        .table-note {
            margin-top: 15px;
            padding: 12px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #4a90e2;
        }

        .table-note p {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 13px;
        }

        .table-note ul {
            margin: 0;
            padding-left: 20px;
        }

        .table-note li {
            color: #666;
            font-size: 12px;
            line-height: 1.5;
            margin-bottom: 4px;
        }

        /* 附件上传样式 */
        .upload-area {
            width: 100%;
        }

        .upload-box {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            background-color: #f9fafb;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .upload-box:hover {
            border-color: #4a90e2;
            background-color: #f0f7ff;
        }

        .upload-box.dragover {
            border-color: #4a90e2;
            background-color: #e6f3ff;
        }

        .upload-icon {
            font-size: 48px;
            margin-bottom: 15px;
            color: #6b7280;
        }

        .upload-text p {
            margin: 0 0 8px 0;
            color: #374151;
            font-size: 14px;
        }

        .upload-hint {
            color: #6b7280 !important;
            font-size: 12px !important;
        }

        .file-list {
            margin-top: 15px;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 15px;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            margin-bottom: 8px;
        }

        .file-info {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .file-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .file-details {
            flex: 1;
        }

        .file-name {
            font-size: 13px;
            color: #333;
            margin-bottom: 2px;
        }

        .file-size {
            font-size: 11px;
            color: #666;
        }

        .file-actions {
            display: flex;
            gap: 8px;
        }

        .file-remove {
            background: #dc3545;
            color: white;
            border: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .file-remove:hover {
            background: #c82333;
        }

        .file-download {
            background: #28a745;
            color: white;
            border: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .file-download:hover {
            background: #218838;
        }

        /* 字段提示样式 */
        .field-hint {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
            line-height: 1.4;
        }

        /* 候选人信息弹窗 */
        .candidate-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .candidate-modal-content {
            background-color: #fff;
            margin: 2% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 1000px;
            max-height: 90vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .candidate-modal-header {
            padding: 20px;
            border-bottom: 1px solid #e0e6ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f8f9fa;
        }

        .candidate-modal-body {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        .candidate-modal-footer {
            padding: 20px;
            border-top: 1px solid #e0e6ed;
            background-color: #f8f9fa;
            text-align: right;
        }

        .candidate-selection-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .candidate-selection-table th,
        .candidate-selection-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .candidate-selection-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }

        .candidate-selection-table tbody tr:hover {
            background-color: #f5f5f5;
        }

        .candidate-checkbox {
            margin-right: 8px;
        }

        /* 空状态样式 */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
            background-color: #f8f9fa;
            border: 1px dashed #ddd;
            border-radius: 8px;
            margin-top: 10px;
        }

        .empty-state p {
            margin: 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">
                    评标结果公示管理 - 新建/编辑
                    <span class="help-icon" onclick="showHelp()" title="查看帮助">?</span>
                </h1>
                <div class="breadcrumb">
                    <a href="#">首页</a> > 
                    <a href="#">评标结果公示管理</a> > 
                    新建/编辑
                </div>
            </div>
            <button class="back-btn" onclick="goBack()">返回列表</button>
        </div>

        <!-- 项目信息 -->
        <div class="form-section">
            <h2 class="section-title">项目信息</h2>
            <div class="form-grid">
                <div class="form-group">
                    <label class="required">选择标段</label>
                    <div class="input-group">
                        <input type="text" id="sectionName" class="form-input" placeholder="请选择标段" readonly>
                        <input type="hidden" id="sectionId">
                        <button type="button" class="btn btn-primary btn-sm" onclick="openSectionSelectModal()">选择</button>
                    </div>
                    <div class="field-hint">选择标段为审核通过、阶段为公告阶段</div>
                </div>
                <div class="form-group">
                    <label>计划项目编号</label>
                    <input type="text" id="projectCode" class="form-input" placeholder="由标段自动带出" readonly>
                </div>
                <div class="form-group">
                    <label>所属计划项目名称</label>
                    <input type="text" id="projectName" class="form-input" placeholder="由标段自动带出" readonly>
                </div>
                <div class="form-group">
                    <label>采购方式</label>
                    <input type="text" id="procurementMethod" class="form-input" placeholder="由标段自动带出" readonly>
                </div>
                <div class="form-group">
                    <label>项目业主</label>
                    <input type="text" id="projectOwner" class="form-input" placeholder="由标段自动带出" readonly>
                </div>
                <div class="form-group">
                    <label>所属二级公司单位</label>
                    <input type="text" id="secondaryCompany" class="form-input" placeholder="由标段自动带出" readonly>
                </div>
                <div class="form-group">
                    <label>代理机构</label>
                    <input type="text" id="agencyName" class="form-input" placeholder="由标段自动带出" readonly>
                </div>
                <div class="form-group full-width">
                    <label>项目基本情况（建设内容及规模）</label>
                    <textarea id="projectDescription" class="form-input" rows="3" placeholder="由标段自动带出" readonly></textarea>
                </div>
                <div class="form-group full-width">
                    <label>备注</label>
                    <textarea id="remarks" class="form-input" rows="2" placeholder="由标段自动带出" readonly></textarea>
                </div>
                <div class="form-group full-width">
                    <label>立项决策文件</label>
                    <textarea id="decisionDocument" class="form-input" rows="2" placeholder="由标段自动带出" readonly></textarea>
                </div>
            </div>
        </div>

        <!-- 标段信息 -->
        <div class="form-section">
            <h2 class="section-title">标段信息</h2>
            <div class="form-grid">
                <div class="form-group">
                    <label>标段名称</label>
                    <input type="text" id="sectionNameInfo" class="form-input" placeholder="由标段自动带出" readonly>
                </div>
                <div class="form-group">
                    <label>标段编号</label>
                    <input type="text" id="sectionCode" class="form-input" placeholder="由标段自动带出" readonly>
                </div>
                <div class="form-group">
                    <label>采购金额（万元）</label>
                    <input type="number" id="procurementAmount" class="form-input" placeholder="由标段自动带出" readonly>
                </div>
                <div class="form-group full-width">
                    <label>标段说明</label>
                    <textarea id="sectionDescription" class="form-input" rows="3" placeholder="由标段自动带出" readonly></textarea>
                </div>
            </div>
        </div>

        <!-- 评标信息 -->
        <div class="form-section">
            <h2 class="section-title">评标信息</h2>
            <div class="form-grid">
                <div class="form-group">
                    <label class="required">公告标题</label>
                    <input type="text" id="announcementTitle" class="form-input" placeholder="请输入公告标题" maxlength="255">
                    <div class="field-hint">项目业主全称+标段名称+公告类型</div>
                </div>
                <div class="form-group">
                    <label>是否公示</label>
                    <select id="isPublicity" class="form-input">
                        <option value="是">是</option>
                        <option value="否">否</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>业主代表姓名</label>
                    <input type="text" id="ownerRepresentative" class="form-input" placeholder="请输入业主代表姓名">
                </div>
                <div class="form-group full-width">
                    <label>投标情况说明</label>
                    <div class="rich-editor">
                        <div class="editor-toolbar">
                            <button type="button" class="editor-btn" onclick="formatText('bold')" title="粗体"><b>B</b></button>
                            <button type="button" class="editor-btn" onclick="formatText('italic')" title="斜体"><i>I</i></button>
                            <button type="button" class="editor-btn" onclick="formatText('underline')" title="下划线"><u>U</u></button>
                            <button type="button" class="editor-btn" onclick="insertList()" title="项目符号">•</button>
                            <button type="button" class="editor-btn" onclick="insertTable()" title="插入表格">表格</button>
                        </div>
                        <div id="bidSituation" class="editor-content" contenteditable="true">
                            请输入投标情况说明...
                        </div>
                    </div>
                </div>
                <div class="form-group full-width">
                    <label>异常情况说明</label>
                    <div class="rich-editor">
                        <div class="editor-toolbar">
                            <button type="button" class="editor-btn" onclick="formatText('bold')" title="粗体"><b>B</b></button>
                            <button type="button" class="editor-btn" onclick="formatText('italic')" title="斜体"><i>I</i></button>
                            <button type="button" class="editor-btn" onclick="formatText('underline')" title="下划线"><u>U</u></button>
                            <button type="button" class="editor-btn" onclick="insertList()" title="项目符号">•</button>
                            <button type="button" class="editor-btn" onclick="insertTable()" title="插入表格">表格</button>
                        </div>
                        <div id="abnormalSituation" class="editor-content" contenteditable="true">
                            请输入异常情况说明...
                        </div>
                    </div>
                    <div class="field-hint">如有异常填写异常说明，无异常不填</div>
                </div>
            </div>
        </div>

        <!-- 投标人信息 -->
        <div class="form-section">
            <h2 class="section-title">投标人信息</h2>
            <div class="candidate-table-container">
                <div class="table-toolbar">
                    <button type="button" class="btn btn-primary btn-sm" onclick="addBidderRow()">+ 添加投标人</button>
                    <button type="button" class="btn btn-info btn-sm" onclick="sortBiddersByScore()">按得分排序</button>
                    <button type="button" class="btn btn-secondary btn-sm" onclick="clearBidderTable()">清空表格</button>
                </div>
                <div class="table-wrapper">
                    <table class="candidate-table" id="bidderTable">
                        <thead>
                            <tr>
                                <th width="80">序号</th>
                                <th width="200">投标单位全称</th>
                                <th width="120">投标形式</th>
                                <th width="150">投标报价</th>
                                <th width="100">综合得分</th>
                                <th width="80">操作</th>
                            </tr>
                        </thead>
                        <tbody id="bidderTableBody">
                            <tr>
                                <td><input type="number" class="table-input" value="1" min="1" readonly></td>
                                <td><input type="text" class="table-input" placeholder="请输入投标单位全称"></td>
                                <td>
                                    <select class="table-input">
                                        <option value="费率">费率</option>
                                        <option value="金额">金额</option>
                                    </select>
                                </td>
                                <td><input type="number" class="table-input" placeholder="0.00" step="0.01" min="0"></td>
                                <td><input type="number" class="table-input" placeholder="0" step="0.1" min="0" max="100"></td>
                                <td><button type="button" class="btn-delete" onclick="deleteBidderRow(this)" title="删除">×</button></td>
                            </tr>
                            <tr>
                                <td><input type="number" class="table-input" value="2" min="1" readonly></td>
                                <td><input type="text" class="table-input" placeholder="请输入投标单位全称"></td>
                                <td>
                                    <select class="table-input">
                                        <option value="费率">费率</option>
                                        <option value="金额">金额</option>
                                    </select>
                                </td>
                                <td><input type="number" class="table-input" placeholder="0.00" step="0.01" min="0"></td>
                                <td><input type="number" class="table-input" placeholder="0" step="0.1" min="0" max="100"></td>
                                <td><button type="button" class="btn-delete" onclick="deleteBidderRow(this)" title="删除">×</button></td>
                            </tr>
                            <tr>
                                <td><input type="number" class="table-input" value="3" min="1" readonly></td>
                                <td><input type="text" class="table-input" placeholder="请输入投标单位全称"></td>
                                <td>
                                    <select class="table-input">
                                        <option value="费率">费率</option>
                                        <option value="金额">金额</option>
                                    </select>
                                </td>
                                <td><input type="number" class="table-input" placeholder="0.00" step="0.01" min="0"></td>
                                <td><input type="number" class="table-input" placeholder="0" step="0.1" min="0" max="100"></td>
                                <td><button type="button" class="btn-delete" onclick="deleteBidderRow(this)" title="删除">×</button></td>
                            </tr>
                            <tr>
                                <td><input type="number" class="table-input" value="4" min="1" readonly></td>
                                <td><input type="text" class="table-input" placeholder="请输入投标单位全称"></td>
                                <td>
                                    <select class="table-input">
                                        <option value="费率">费率</option>
                                        <option value="金额">金额</option>
                                    </select>
                                </td>
                                <td><input type="number" class="table-input" placeholder="0.00" step="0.01" min="0"></td>
                                <td><input type="number" class="table-input" placeholder="0" step="0.1" min="0" max="100"></td>
                                <td><button type="button" class="btn-delete" onclick="deleteBidderRow(this)" title="删除">×</button></td>
                            </tr>
                            <tr>
                                <td><input type="number" class="table-input" value="5" min="1" readonly></td>
                                <td><input type="text" class="table-input" placeholder="请输入投标单位全称"></td>
                                <td>
                                    <select class="table-input">
                                        <option value="费率">费率</option>
                                        <option value="金额">金额</option>
                                    </select>
                                </td>
                                <td><input type="number" class="table-input" placeholder="0.00" step="0.01" min="0"></td>
                                <td><input type="number" class="table-input" placeholder="0" step="0.1" min="0" max="100"></td>
                                <td><button type="button" class="btn-delete" onclick="deleteBidderRow(this)" title="删除">×</button></td>
                            </tr>
                            <tr>
                                <td><input type="number" class="table-input" value="6" min="1" readonly></td>
                                <td><input type="text" class="table-input" placeholder="请输入投标单位全称"></td>
                                <td>
                                    <select class="table-input">
                                        <option value="费率">费率</option>
                                        <option value="金额">金额</option>
                                    </select>
                                </td>
                                <td><input type="number" class="table-input" placeholder="0.00" step="0.01" min="0"></td>
                                <td><input type="number" class="table-input" placeholder="0" step="0.1" min="0" max="100"></td>
                                <td><button type="button" class="btn-delete" onclick="deleteBidderRow(this)" title="删除">×</button></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 候选人信息 -->
        <div class="form-section">
            <h2 class="section-title">候选人信息</h2>
            <div class="candidate-table-container">
                <div class="table-toolbar">
                    <button type="button" class="btn btn-warning btn-sm" onclick="openCandidateModal()">选择候选人</button>
                </div>
                <div class="table-wrapper" id="candidateDisplayTable" style="display: none;">
                    <table class="candidate-table">
                        <thead>
                            <tr>
                                <th width="80">序号</th>
                                <th width="200">投标单位全称</th>
                                <th width="120">投标形式</th>
                                <th width="150">投标报价</th>
                                <th width="100">综合得分</th>
                            </tr>
                        </thead>
                        <tbody id="candidateDisplayTableBody">
                            <!-- 候选人数据将在选择后显示 -->
                        </tbody>
                    </table>
                </div>
                <div id="candidateEmptyState" class="empty-state">
                    <p>暂无候选人信息，请点击"选择候选人"按钮进行选择</p>
                </div>
            </div>
        </div>

        <!-- 附件上传 -->
        <div class="form-section">
            <h2 class="section-title">附件上传</h2>
            <div class="form-grid">
                <div class="form-group full-width">
                    <label>评标记录资料</label>
                    <div class="upload-area">
                        <div class="upload-box" onclick="triggerEvaluationFileUpload()">
                            <div class="upload-icon">📎</div>
                            <div class="upload-text">
                                <p>点击上传文件或拖拽文件到此区域</p>
                                <p class="upload-hint">支持 PDF、DOC、DOCX、XLS、XLSX、JPG、PNG、MP4 格式，单个文件不超过 100MB</p>
                            </div>
                        </div>
                        <input type="file" id="evaluationFileInput" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.mp4" style="display: none;" onchange="handleEvaluationFileSelect(event)">
                        <div class="file-list" id="evaluationFileList"></div>
                    </div>
                    <div class="field-hint">包括开标记录、资格评审、符合性评审、评分记录、评标视频（市属国企仅需提供自主开展的招标采购项目的评标视频）。</div>
                </div>
                <div class="form-group full-width">
                    <label>审核依据</label>
                    <div class="upload-area">
                        <div class="upload-box" onclick="triggerAuditFileUpload()">
                            <div class="upload-icon">📎</div>
                            <div class="upload-text">
                                <p>点击上传文件或拖拽文件到此区域</p>
                                <p class="upload-hint">支持 PDF、DOC、DOCX、XLS、XLSX、JPG、PNG 格式，单个文件不超过 50MB</p>
                            </div>
                        </div>
                        <input type="file" id="auditFileInput" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png" style="display: none;" onchange="handleAuditFileSelect(event)">
                        <div class="file-list" id="auditFileList"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部操作栏 -->
        <div class="action-bar">
            <div class="btn-group">
                <button type="button" class="btn btn-secondary" onclick="goBack()">取消</button>
            </div>
            <div class="btn-group">
                <button type="button" class="btn btn-secondary" onclick="saveDraft()">保存草稿</button>
                <button type="button" class="btn btn-info" onclick="preview()">预览</button>
                <button type="button" class="btn btn-primary" onclick="submit()">提交审核</button>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="help-modal" class="help-modal">
        <div class="help-content">
            <div class="help-header">
                <h3>功能说明</h3>
                <span class="close" onclick="hideHelp()">&times;</span>
            </div>
            <div class="help-body">
                <h4>页面功能</h4>
                <p>本页面用于新建或编辑评标结果公示管理信息，支持以下功能：</p>
                <ul>
                    <li>选择关联标段，自动带出相关项目信息</li>
                    <li>填写公告基本信息和详细内容</li>
                    <li>设置申请人资格要求</li>
                    <li>配置采购文件获取方式和时间</li>
                    <li>设定响应文件递交要求</li>
                    <li>安排开标时间和地点</li>
                    <li>添加其他补充事项</li>
                    <li>确认招标人和代理机构信息</li>
                </ul>
                
                <h4>操作说明</h4>
                <p><strong>关联标段：</strong>点击"选择"按钮，在弹出的标段列表中选择要关联的标段。选择后系统会自动填入相关的项目信息。</p>
                <p><strong>富文本编辑：</strong>在资格要求、获取地点、递交地点等文本区域，可以使用工具栏进行文本格式化，包括粗体、斜体、下划线、项目符号和表格插入。</p>
                <p><strong>时间设置：</strong>所有时间字段都支持日期时间选择器，请确保时间逻辑的合理性（如获取结束时间应晚于开始时间）。</p>
                
                <h4>保存选项</h4>
                <ul>
                    <li><strong>保存草稿：</strong>保存当前填写内容，状态为草稿，可以继续编辑</li>
                    <li><strong>预览：</strong>预览公告的最终显示效果</li>
                    <li><strong>提交审核：</strong>提交给上级审核，提交后无法编辑</li>
                </ul>
                
                <div class="note">
                    <strong>注意：</strong>带有红色星号(*)的字段为必填项，提交前请确保所有必填项都已正确填写。
                </div>
                
                <div class="warning">
                    <strong>提醒：</strong>提交审核后将无法修改，请在提交前仔细检查所有信息的准确性。
                </div>
            </div>
        </div>
    </div>

    <script>
        // 标段详细数据（模拟数据）
        const sectionDetailData = {
            'SEC-2024-001-01': {
                projectCode: 'XM-2024-001',
                procurementMethod: '公开招标',
                budgetAmount: '500000',
                procurementAmount: '480000',
                projectOwner: '某市政府采购中心',
                relatedProject: '办公设备采购项目',
                projectDescription: '本项目主要采购办公家具，包括办公桌椅、文件柜等，用于新办公楼装修配套。项目预算充足，要求产品质量优良，符合环保标准。',
                remarks: '投标人需具备相关资质，产品需通过质量认证。',
                tenderName: '某市政府采购中心',
                tenderContact: '张主任',
                tenderPhone: '010-12345678',
                tenderAddress: '北京市朝阳区政府大楼3楼',
                agencyName: '北京招标代理有限公司',
                agencyContact: '李经理',
                agencyPhone: '010-87654321',
                agencyAddress: '北京市海淀区中关村大街1号'
            },
            'SEC-2024-001-02': {
                projectCode: 'XM-2024-001',
                procurementMethod: '公开招标',
                budgetAmount: '800000',
                procurementAmount: '750000',
                projectOwner: '某市政府采购中心',
                relatedProject: '办公设备采购项目',
                projectDescription: '本项目主要采购办公设备，包括电脑、打印机、复印机等，用于新办公楼信息化建设。要求设备性能稳定，售后服务完善。',
                remarks: '投标人需提供三年质保服务，设备需符合国家节能标准。',
                tenderName: '某市政府采购中心',
                tenderContact: '王主任',
                tenderPhone: '010-12345679',
                tenderAddress: '北京市朝阳区政府大楼3楼',
                agencyName: '北京招标代理有限公司',
                agencyContact: '刘经理',
                agencyPhone: '010-87654322',
                agencyAddress: '北京市海淀区中关村大街1号'
            },
            'SEC-2024-002-01': {
                projectCode: 'XM-2024-002',
                procurementMethod: '邀请招标',
                budgetAmount: '1200000',
                procurementAmount: '1150000',
                projectOwner: '某科技发展有限公司',
                relatedProject: 'IT系统建设项目',
                projectDescription: '本项目为企业信息化系统开发，包括ERP系统、OA系统等模块开发，要求技术先进，功能完善，用户体验良好。',
                remarks: '投标人需具备软件开发资质，有同类项目成功案例。',
                tenderName: '某科技发展有限公司',
                tenderContact: '陈总经理',
                tenderPhone: '010-23456789',
                tenderAddress: '北京市海淀区科技园区A座',
                agencyName: '中科招标咨询有限公司',
                agencyContact: '赵经理',
                agencyPhone: '010-98765432',
                agencyAddress: '北京市西城区金融街5号'
            }
        };

        // 关闭标段选择弹窗
        function closeSectionSelectModal() {
            document.getElementById('sectionSelectModal').style.display = 'none';
        }

        // 加载标段列表
        function loadSectionList() {
            // 模拟标段数据
            const sections = [
                {
                    id: 'SEC-2024-001-01',
                    name: '办公家具采购标段',
                    project: '办公设备采购项目',
                    procurementMethod: '公开招标',
                    procurementType: '货物类',
                    createTime: '2024-01-15'
                },
                {
                    id: 'SEC-2024-001-02',
                    name: '办公设备采购标段',
                    project: '办公设备采购项目',
                    procurementMethod: '公开招标',
                    procurementType: '货物类',
                    createTime: '2024-01-16'
                },
                {
                    id: 'SEC-2024-002-01',
                    name: 'IT系统开发标段',
                    project: 'IT系统建设项目',
                    procurementMethod: '邀请招标',
                    procurementType: '服务类',
                    createTime: '2024-01-20'
                }
            ];
            
            renderSectionList(sections);
        }

        function renderSectionList(sections) {
            const tbody = document.querySelector('#sectionSelectModal .table tbody');
            tbody.innerHTML = '';
            
            sections.forEach(section => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><input type="radio" name="sectionRadio" value="${section.id}" 
                        data-name="${section.name}" 
                        data-project="${section.project || section.projectName}" 
                        data-method="${section.procurementMethod}" 
                        data-type="${section.procurementType}"></td>
                    <td>${section.name}</td>
                    <td>${section.project || section.projectName}</td>
                    <td>${section.procurementMethod}</td>
                    <td>${section.procurementType}</td>
                    <td>${section.createTime}</td>
                `;
                tbody.appendChild(row);
            });
        }

        function searchSections() {
            // 获取查询条件
            const sectionName = document.getElementById('searchSectionName').value;
            const projectName = document.getElementById('searchProjectName').value;
            const procurementMethod = document.getElementById('searchProcurementMethod').value;
            const procurementType = document.getElementById('searchProcurementType').value;
            const startTime = document.getElementById('searchStartTime').value;
            const endTime = document.getElementById('searchEndTime').value;
            
            // 这里应该调用后端API进行查询，现在模拟过滤
            console.log('查询条件:', { sectionName, projectName, procurementMethod, procurementType, startTime, endTime });
            
            // 重新加载列表（实际应该根据查询条件过滤）
            loadSectionList();
        }

        function resetSearch() {
            // 清空查询条件
            document.getElementById('searchSectionName').value = '';
            document.getElementById('searchProjectName').value = '';
            document.getElementById('searchProcurementMethod').value = '';
            document.getElementById('searchProcurementType').value = '';
            document.getElementById('searchStartTime').value = '';
            document.getElementById('searchEndTime').value = '';
            
            // 重新加载列表
            loadSectionList();
        }

        function confirmSectionSelect() {
            const selectedRadio = document.querySelector('input[name="sectionRadio"]:checked');
            if (!selectedRadio) {
                alert('请选择一个标段');
                return;
            }
            
            const sectionId = selectedRadio.value;
            const sectionName = selectedRadio.getAttribute('data-name');
            
            // 填充标段信息
            document.getElementById('sectionName').value = sectionName;
            document.getElementById('sectionId').value = sectionId;
            
            // 加载标段相关数据
            loadSectionData(sectionId);
            
            // 关闭弹窗
            closeSectionSelectModal();
        }

        // 加载标段数据
        function loadSectionData(sectionId) {
            const selectedSection = sectionId || document.getElementById('sectionId').value;
            
            if (selectedSection && sectionData[selectedSection]) {
                const data = sectionData[selectedSection];
                
                // 填充自动带出的字段
                document.getElementById('projectCode').value = data.projectCode;
                document.getElementById('procurementMethod').value = data.procurementMethod;
                document.getElementById('budgetAmount').value = data.budgetAmount;
                document.getElementById('procurementAmount').value = data.procurementAmount;
                document.getElementById('projectOwner').value = data.projectOwner;
                document.getElementById('relatedProject').value = data.relatedProject;
                document.getElementById('projectDescription').value = data.projectDescription;
                document.getElementById('remarks').value = data.remarks;
                document.getElementById('tenderName').value = data.tenderName;
                document.getElementById('tenderContact').value = data.tenderContact;
                document.getElementById('tenderPhone').value = data.tenderPhone;
                document.getElementById('tenderAddress').value = data.tenderAddress;
                document.getElementById('agencyName').value = data.agencyName;
                document.getElementById('agencyContact').value = data.agencyContact;
                document.getElementById('agencyPhone').value = data.agencyPhone;
                document.getElementById('agencyAddress').value = data.agencyAddress;
                
                // 自动生成公告名称
                const announcementName = data.relatedProject + '招标公告';
                document.getElementById('announcementName').value = announcementName;
            } else {
                // 清空所有字段
                const fields = ['projectCode', 'procurementMethod', 'budgetAmount', 'procurementAmount', 
                              'projectOwner', 'relatedProject', 'projectDescription', 'remarks',
                              'tenderName', 'tenderContact', 'tenderPhone', 'tenderAddress',
                              'agencyName', 'agencyContact', 'agencyPhone', 'agencyAddress', 'announcementName'];
                
                fields.forEach(field => {
                    document.getElementById(field).value = '';
                });
            }
        }

        // 富文本编辑器功能
        function formatText(command) {
            document.execCommand(command, false, null);
        }

        function insertList() {
            document.execCommand('insertUnorderedList', false, null);
        }

        function insertTable() {
            const table = '<table border="1" style="border-collapse: collapse; width: 100%;"><tr><td>单元格1</td><td>单元格2</td></tr><tr><td>单元格3</td><td>单元格4</td></tr></table>';
            document.execCommand('insertHTML', false, table);
        }

        // 帮助弹窗
        function showHelp() {
            document.getElementById('help-modal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('help-modal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });

        // 业务操作函数
        function goBack() {
            if (confirm('确定要返回吗？未保存的内容将丢失。')) {
                window.history.back();
            }
        }

        function saveDraft() {
            // 验证必填字段
            if (!validateRequired()) {
                return;
            }
            
            console.log('保存草稿');
            alert('草稿保存成功！');
        }

        function preview() {
            // 验证必填字段
            if (!validateRequired()) {
                return;
            }
            
            console.log('预览公告');
            // 这里可以打开预览窗口
        }

        function submit() {
            // 验证必填字段
            if (!validateRequired()) {
                return;
            }
            
            if (confirm('确定要提交审核吗？提交后将无法编辑。')) {
                console.log('提交审核');
                alert('提交成功！');
            }
        }

        function validateRequired() {
            const requiredFields = [
                { id: 'sectionId', name: '关联标段' },
                { id: 'announcementName', name: '公告名称' },
                { id: 'evaluationDate', name: '评标日期' },
                { id: 'publicityStartTime', name: '公示开始时间' },
                { id: 'publicityEndTime', name: '公示结束时间' }
            ];
            
            for (let field of requiredFields) {
                const element = document.getElementById(field.id);
                if (!element.value.trim()) {
                    alert(`请填写${field.name}`);
                    element.focus();
                    return false;
                }
            }
            
            // 验证评标过程说明
            const evaluationProcessContent = document.getElementById('evaluationProcess').textContent.trim();
            if (!evaluationProcessContent || evaluationProcessContent === '请输入评标过程说明...') {
                alert('请填写评标过程说明');
                document.getElementById('evaluationProcess').focus();
                return false;
            }
            
            // 验证评标说明
            const evaluationDescriptionContent = document.getElementById('evaluationDescription').textContent.trim();
            if (!evaluationDescriptionContent || evaluationDescriptionContent === '请输入评标说明（包括评标方法、评标标准、评标结果等详细说明）...') {
                alert('请填写评标说明');
                document.getElementById('evaluationDescription').focus();
                return false;
            }
            
            // 验证候选人表格
            if (!validateCandidateTable()) {
                return false;
            }
            
            // 验证时间逻辑
            const publicityStart = new Date(document.getElementById('publicityStartTime').value);
            const publicityEnd = new Date(document.getElementById('publicityEndTime').value);
            if (publicityEnd <= publicityStart) {
                alert('公示结束时间必须晚于公示开始时间');
                document.getElementById('publicityEndTime').focus();
                return false;
            }
            
            return true;
        }
        
        // 附件上传相关功能
        let uploadedFiles = [];
        
        function triggerFileUpload() {
            document.getElementById('fileInput').click();
        }
        
        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            
            files.forEach(file => {
                // 验证文件大小（50MB限制）
                if (file.size > 50 * 1024 * 1024) {
                    alert(`文件 "${file.name}" 超过50MB大小限制`);
                    return;
                }
                
                // 验证文件类型
                const allowedTypes = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.jpg', '.jpeg', '.png'];
                const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
                if (!allowedTypes.includes(fileExtension)) {
                    alert(`文件 "${file.name}" 格式不支持`);
                    return;
                }
                
                // 检查是否已存在同名文件
                if (uploadedFiles.some(f => f.name === file.name)) {
                    alert(`文件 "${file.name}" 已存在`);
                    return;
                }
                
                // 添加到文件列表
                uploadedFiles.push({
                    id: Date.now() + Math.random(),
                    name: file.name,
                    size: file.size,
                    type: fileExtension,
                    file: file
                });
            });
            
            renderFileList();
            // 清空input以便重复选择同一文件
            event.target.value = '';
        }
        
        function renderFileList() {
            const fileList = document.getElementById('fileList');
            
            if (uploadedFiles.length === 0) {
                fileList.innerHTML = '';
                return;
            }
            
            fileList.innerHTML = uploadedFiles.map(file => `
                <div class="file-item">
                    <div class="file-info">
                        <div class="file-icon">${getFileIcon(file.type)}</div>
                        <div class="file-details">
                            <div class="file-name">${file.name}</div>
                            <div class="file-size">${formatFileSize(file.size)}</div>
                        </div>
                    </div>
                    <div class="file-actions">
                        <button type="button" class="file-download" onclick="downloadFile('${file.id}')" title="下载">↓</button>
                        <button type="button" class="file-remove" onclick="removeFile('${file.id}')" title="删除">×</button>
                    </div>
                </div>
            `).join('');
        }
        
        function getFileIcon(type) {
            const iconMap = {
                '.pdf': '📄',
                '.doc': '📝',
                '.docx': '📝',
                '.xls': '📊',
                '.xlsx': '📊',
                '.jpg': '🖼️',
                '.jpeg': '🖼️',
                '.png': '🖼️'
            };
            return iconMap[type] || '📎';
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function removeFile(fileId) {
            uploadedFiles = uploadedFiles.filter(file => file.id != fileId);
            renderFileList();
        }
        
        function downloadFile(fileId) {
            const file = uploadedFiles.find(f => f.id == fileId);
            if (file) {
                const url = URL.createObjectURL(file.file);
                const a = document.createElement('a');
                a.href = url;
                a.download = file.name;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }
        }
        
        // 拖拽上传功能
        document.addEventListener('DOMContentLoaded', function() {
            const uploadBox = document.querySelector('.upload-box');
            
            if (uploadBox) {
                uploadBox.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('dragover');
                });
                
                uploadBox.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                });
                
                uploadBox.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                    
                    const files = Array.from(e.dataTransfer.files);
                    const fileInput = document.getElementById('fileInput');
                    
                    // 模拟文件选择事件
                    const event = { target: { files: files, value: '' } };
                    handleFileSelect(event);
                });
            }
        });
        
        // 添加候选人行
        function addCandidateRow() {
            const tbody = document.getElementById('candidateTableBody');
            const rowCount = tbody.rows.length;
            const newRow = document.createElement('tr');
            
            newRow.innerHTML = `
                <td><input type="number" class="table-input" value="${rowCount + 1}" min="1" readonly></td>
                <td><input type="text" class="table-input" placeholder="请输入投标单位全称"></td>
                <td><input type="text" class="table-input" placeholder="请输入业务代表姓名"></td>
                <td><input type="number" class="table-input" placeholder="0.00" step="0.01" min="0"></td>
                <td><input type="number" class="table-input" placeholder="0" step="0.1" min="0" max="100"></td>
                <td><button type="button" class="btn-delete" onclick="deleteCandidateRow(this)" title="删除">×</button></td>
            `;
            
            tbody.appendChild(newRow);
            updateRowNumbers();
        }
        
        // 删除候选人行
        function deleteCandidateRow(button) {
            const tbody = document.getElementById('candidateTableBody');
            if (tbody.rows.length <= 1) {
                alert('至少需要保留一行候选人信息');
                return;
            }
            
            const row = button.closest('tr');
            row.remove();
            updateRowNumbers();
        }
        
        // 清空候选人表格
        function clearCandidateTable() {
            if (confirm('确定要清空所有候选人信息吗？')) {
                const tbody = document.getElementById('candidateTableBody');
                tbody.innerHTML = `
                    <tr>
                        <td><input type="number" class="table-input" value="1" min="1" readonly></td>
                        <td><input type="text" class="table-input" placeholder="请输入投标单位全称"></td>
                        <td><input type="text" class="table-input" placeholder="请输入业务代表姓名"></td>
                        <td><input type="number" class="table-input" placeholder="0.00" step="0.01" min="0"></td>
                        <td><input type="number" class="table-input" placeholder="0" step="0.1" min="0" max="100"></td>
                        <td><button type="button" class="btn-delete" onclick="deleteCandidateRow(this)" title="删除">×</button></td>
                    </tr>
                `;
            }
        }
        
        // 更新行号
        function updateRowNumbers() {
            const tbody = document.getElementById('candidateTableBody');
            const rows = tbody.querySelectorAll('tr');
            
            rows.forEach((row, index) => {
                const numberInput = row.querySelector('input[type="number"][readonly]');
                if (numberInput) {
                    numberInput.value = index + 1;
                }
            });
        }
        
        // 根据综合得分自动排序
        function sortCandidatesByScore() {
            const tbody = document.getElementById('candidateTableBody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            // 获取每行的数据
            const rowsData = rows.map(row => {
                const inputs = row.querySelectorAll('input');
                return {
                    row: row,
                    company: inputs[1].value,
                    representative: inputs[2].value,
                    bidPrice: parseFloat(inputs[3].value) || 0,
                    totalScore: parseFloat(inputs[4].value) || 0
                };
            });
            
            // 按综合得分降序排序
            rowsData.sort((a, b) => b.totalScore - a.totalScore);
            
            // 清空tbody并重新添加排序后的行
            tbody.innerHTML = '';
            rowsData.forEach((data, index) => {
                const inputs = data.row.querySelectorAll('input');
                inputs[0].value = index + 1; // 更新排序号
                tbody.appendChild(data.row);
            });
        }
        
        // 验证候选人表格数据
        function validateCandidateTable() {
            const tbody = document.getElementById('candidateTableBody');
            const rows = tbody.querySelectorAll('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const inputs = rows[i].querySelectorAll('input');
                const company = inputs[1].value.trim();
                const representative = inputs[2].value.trim();
                const bidPrice = inputs[3].value;
                
                if (!company) {
                    alert(`第${i + 1}行：请填写投标单位全称`);
                    inputs[1].focus();
                    return false;
                }
                
                if (!representative) {
                    alert(`第${i + 1}行：请填写业务代表姓名`);
                    inputs[2].focus();
                    return false;
                }
                
                if (!bidPrice || parseFloat(bidPrice) <= 0) {
                    alert(`第${i + 1}行：请填写有效的投标报价`);
                    inputs[3].focus();
                    return false;
                }
            }
            
            return true;
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认时间
            const now = new Date();
            const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
            const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
            
            // 格式化时间为 datetime-local 格式
            function formatDateTime(date) {
                return date.getFullYear() + '-' + 
                       String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                       String(date.getDate()).padStart(2, '0') + 'T' + 
                       String(date.getHours()).padStart(2, '0') + ':' + 
                       String(date.getMinutes()).padStart(2, '0');
            }
            
            document.getElementById('docStartTime').value = formatDateTime(tomorrow);
            document.getElementById('docEndTime').value = formatDateTime(nextWeek);
            document.getElementById('responseDeadline').value = formatDateTime(nextWeek);
            document.getElementById('openingTime').value = formatDateTime(nextWeek);
        });

        // 分页相关变量
        let currentPageNum = 1;
        let pageSize = 5;
        let totalRecords = 0;
        let totalPages = 1;
        let sectionData = [];

        // 模拟标段数据
        function generateSectionData() {
            const data = [];
            // 添加与sectionDetailData匹配的标段
            data.push({
                id: 'SEC-2024-001-01',
                name: '办公家具采购标段',
                projectName: '办公设备采购项目',
                procurementMethod: '公开招标',
                procurementType: '货物类',
                createTime: '2024-01-15'
            });
            data.push({
                id: 'SEC-2024-001-02',
                name: '办公设备采购标段',
                projectName: '办公设备采购项目',
                procurementMethod: '公开招标',
                procurementType: '货物类',
                createTime: '2024-01-16'
            });
            data.push({
                id: 'SEC-2024-002-01',
                name: 'IT系统开发标段',
                projectName: 'IT系统建设项目',
                procurementMethod: '邀请招标',
                procurementType: '服务类',
                createTime: '2024-01-20'
            });
            
            // 生成其他模拟数据
            for (let i = 4; i <= 156; i++) {
                data.push({
                    id: `SECTION_${String(i).padStart(3, '0')}`,
                    name: `标段${i}`,
                    projectName: `项目${Math.ceil(i/3)}`,
                    procurementMethod: ['公开招标', '邀请招标', '竞争性谈判', '单一来源'][i % 4],
                    procurementType: ['货物类', '服务类', '工程类'][i % 3],
                    createTime: new Date(2024, 0, i % 30 + 1).toLocaleDateString()
                });
            }
            return data;
        }

        // 初始化数据
        sectionData = generateSectionData();
        totalRecords = sectionData.length;
        totalPages = Math.ceil(totalRecords / pageSize);

        // 搜索标段
        function searchSections() {
            const searchName = document.getElementById('searchSectionName').value.trim();
            const searchProject = document.getElementById('searchProjectName').value.trim();
            const searchMethod = document.getElementById('searchProcurementMethod').value;
            const searchType = document.getElementById('searchProcurementType').value;
            
            // 重置到第一页
            currentPageNum = 1;
            
            // 模拟搜索结果
            let filteredData = generateSectionData();
            
            if (searchName) {
                filteredData = filteredData.filter(item => item.name.includes(searchName));
            }
            if (searchProject) {
                filteredData = filteredData.filter(item => item.projectName.includes(searchProject));
            }
            if (searchMethod) {
                filteredData = filteredData.filter(item => item.procurementMethod === searchMethod);
            }
            if (searchType) {
                filteredData = filteredData.filter(item => item.procurementType === searchType);
            }
            
            sectionData = filteredData;
            totalRecords = sectionData.length;
            totalPages = Math.ceil(totalRecords / pageSize);
            
            loadSectionList();
            updatePagination();
        }

        // 重置搜索
        function resetSearch() {
            document.getElementById('searchSectionName').value = '';
            document.getElementById('searchProjectName').value = '';
            document.getElementById('searchProcurementMethod').value = '';
            document.getElementById('searchProcurementType').value = '';
            document.getElementById('searchStartTime').value = '';
            document.getElementById('searchEndTime').value = '';
            
            currentPageNum = 1;
            sectionData = generateSectionData();
            totalRecords = sectionData.length;
            totalPages = Math.ceil(totalRecords / pageSize);
            
            loadSectionList();
            updatePagination();
        }

        // 加载标段列表
        function loadSectionList() {
            const tbody = document.querySelector('#sectionSelectModal tbody');
            const startIndex = (currentPageNum - 1) * pageSize;
            const endIndex = Math.min(startIndex + pageSize, sectionData.length);
            const currentPageData = sectionData.slice(startIndex, endIndex);
            
            let html = '';
            currentPageData.forEach(item => {
                html += `
                    <tr>
                        <td><input type="radio" name="sectionRadio" value="${item.id}" 
                            data-name="${item.name}" 
                            data-project="${item.projectName}" 
                            data-method="${item.procurementMethod}" 
                            data-type="${item.procurementType}"></td>
                        <td>${item.name}</td>
                        <td>${item.projectName}</td>
                        <td>${item.procurementMethod}</td>
                        <td>${item.procurementType}</td>
                        <td>${item.createTime}</td>
                    </tr>
                `;
            });
            
            if (html === '') {
                html = '<tr><td colspan="6" style="text-align: center; color: #999;">暂无数据</td></tr>';
            }
            
            tbody.innerHTML = html;
        }

        // 更新分页信息
        function updatePagination() {
            document.getElementById('totalRecords').textContent = totalRecords;
            document.getElementById('currentPage').textContent = currentPageNum;
            document.getElementById('totalPages').textContent = totalPages;
            
            // 更新按钮状态
            document.getElementById('firstPageBtn').disabled = currentPageNum === 1;
            document.getElementById('prevPageBtn').disabled = currentPageNum === 1;
            document.getElementById('nextPageBtn').disabled = currentPageNum === totalPages;
            document.getElementById('lastPageBtn').disabled = currentPageNum === totalPages;
            
            // 生成页码按钮
            generatePageNumbers();
        }

        // 生成页码按钮
        function generatePageNumbers() {
            const pageNumbers = document.getElementById('pageNumbers');
            let html = '';
            
            // 计算显示的页码范围
            let startPage = Math.max(1, currentPageNum - 2);
            let endPage = Math.min(totalPages, currentPageNum + 2);
            
            // 确保显示5个页码（如果总页数足够）
            if (endPage - startPage < 4) {
                if (startPage === 1) {
                    endPage = Math.min(totalPages, startPage + 4);
                } else {
                    startPage = Math.max(1, endPage - 4);
                }
            }
            
            for (let i = startPage; i <= endPage; i++) {
                const activeClass = i === currentPageNum ? ' active' : '';
                html += `<button type="button" class="btn btn-sm page-number${activeClass}" onclick="goToPage(${i})">${i}</button>`;
            }
            
            pageNumbers.innerHTML = html;
        }

        // 跳转到指定页
        function goToPage(page) {
            if (page >= 1 && page <= totalPages && page !== currentPageNum) {
                currentPageNum = page;
                loadSectionList();
                updatePagination();
            }
        }

        // 首页
        function firstPage() {
            goToPage(1);
        }

        // 上一页
        function prevPage() {
            goToPage(currentPageNum - 1);
        }

        // 下一页
        function nextPage() {
            goToPage(currentPageNum + 1);
        }

        // 末页
        function lastPage() {
            goToPage(totalPages);
        }

        // 改变每页显示数量
        function changePageSize() {
            const newPageSize = parseInt(document.getElementById('pageSizeSelect').value);
            pageSize = newPageSize;
            currentPageNum = 1;
            totalPages = Math.ceil(totalRecords / pageSize);
            
            loadSectionList();
            updatePagination();
        }

        // 打开标段选择弹窗
        function openSectionSelectModal() {
            document.getElementById('sectionSelectModal').style.display = 'block';
            // 初始化数据
            currentPageNum = 1;
            sectionData = generateSectionData();
            totalRecords = sectionData.length;
            totalPages = Math.ceil(totalRecords / pageSize);
            loadSectionList();
            updatePagination();
        }

        // 确认选择标段
        function confirmSectionSelect() {
            const selectedRadio = document.querySelector('input[name="sectionRadio"]:checked');
            if (!selectedRadio) {
                alert('请选择一个标段');
                return;
            }
            
            const sectionId = selectedRadio.value;
            const sectionName = selectedRadio.getAttribute('data-name');
            const sectionProject = selectedRadio.getAttribute('data-project');
            const sectionMethod = selectedRadio.getAttribute('data-method');
            const sectionType = selectedRadio.getAttribute('data-type');
            
            // 更新页面显示
            document.getElementById('sectionId').value = sectionId;
            document.getElementById('sectionName').value = sectionName;
            document.getElementById('sectionDisplay').textContent = sectionName;
            
            // 模拟标段详细数据（实际应从后端获取）
            const sectionDetailData = {
                'SEC-2024-001-01': {
                    projectCode: 'XM-2024-001',
                    procurementMethod: '公开招标',
                    budgetAmount: '500000',
                    procurementAmount: '480000',
                    projectOwner: '某市政府采购中心',
                    relatedProject: '办公设备采购项目',
                    projectDescription: '本项目主要采购办公家具，包括办公桌椅、文件柜等，用于新办公楼装修配套。项目预算充足，要求产品质量优良，符合环保标准。',
                    remarks: '投标人需具备相关资质，产品需通过质量认证。',
                    tenderName: '某市政府采购中心',
                    tenderContact: '张主任',
                    tenderPhone: '010-12345678',
                    tenderAddress: '北京市朝阳区政府大楼3楼',
                    agencyName: '北京招标代理有限公司',
                    agencyContact: '李经理',
                    agencyPhone: '010-87654321',
                    agencyAddress: '北京市海淀区中关村大街1号'
                },
                'SEC-2024-001-02': {
                    projectCode: 'XM-2024-001',
                    procurementMethod: '公开招标',
                    budgetAmount: '800000',
                    procurementAmount: '750000',
                    projectOwner: '某市政府采购中心',
                    relatedProject: '办公设备采购项目',
                    projectDescription: '本项目主要采购办公设备，包括电脑、打印机、复印机等，用于新办公楼信息化建设。要求设备性能稳定，售后服务完善。',
                    remarks: '投标人需提供三年质保服务，设备需符合国家节能标准。',
                    tenderName: '某市政府采购中心',
                    tenderContact: '王主任',
                    tenderPhone: '010-12345679',
                    tenderAddress: '北京市朝阳区政府大楼3楼',
                    agencyName: '北京招标代理有限公司',
                    agencyContact: '刘经理',
                    agencyPhone: '010-87654322',
                    agencyAddress: '北京市海淀区中关村大街1号'
                },
                'SEC-2024-002-01': {
                    projectCode: 'XM-2024-002',
                    procurementMethod: '邀请招标',
                    budgetAmount: '1200000',
                    procurementAmount: '1150000',
                    projectOwner: '某科技发展有限公司',
                    relatedProject: 'IT系统建设项目',
                    projectDescription: '本项目为企业信息化系统开发，包括ERP系统、OA系统等模块开发，要求技术先进，功能完善，用户体验良好。',
                    remarks: '投标人需具备软件开发资质，有同类项目成功案例。',
                    tenderName: '某科技发展有限公司',
                    tenderContact: '陈总经理',
                    tenderPhone: '010-23456789',
                    tenderAddress: '北京市海淀区科技园区A座',
                    agencyName: '中科招标咨询有限公司',
                    agencyContact: '赵经理',
                    agencyPhone: '010-98765432',
                    agencyAddress: '北京市西城区金融街5号'
                }
            };
            
            // 自动填入标段相关信息
            if (sectionDetailData[sectionId]) {
                const data = sectionDetailData[sectionId];
                
                // 填充自动带出的字段
                document.getElementById('projectCode').value = data.projectCode;
                document.getElementById('procurementMethod').value = data.procurementMethod;
                document.getElementById('budgetAmount').value = data.budgetAmount;
                document.getElementById('procurementAmount').value = data.procurementAmount;
                document.getElementById('projectOwner').value = data.projectOwner;
                document.getElementById('relatedProject').value = data.relatedProject;
                document.getElementById('projectDescription').value = data.projectDescription;
                document.getElementById('remarks').value = data.remarks;
                document.getElementById('tenderName').value = data.tenderName;
                document.getElementById('tenderContact').value = data.tenderContact;
                document.getElementById('tenderPhone').value = data.tenderPhone;
                document.getElementById('tenderAddress').value = data.tenderAddress;
                document.getElementById('agencyName').value = data.agencyName;
                document.getElementById('agencyContact').value = data.agencyContact;
                document.getElementById('agencyPhone').value = data.agencyPhone;
                document.getElementById('agencyAddress').value = data.agencyAddress;
                
                // 自动生成公告名称
                const announcementName = data.relatedProject + '招标公告';
                document.getElementById('announcementName').value = announcementName;
                
                // 显示成功提示
                alert('标段信息已自动填入相关字段');
            }
            
            // 关闭弹窗
            closeSectionSelectModal();
        }

        // 点击弹窗外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('sectionSelectModal');
            const candidateModal = document.getElementById('candidateModal');
            if (event.target === modal) {
                closeSectionSelectModal();
            }
            if (event.target === candidateModal) {
                closeCandidateModal();
            }
        };

        // 候选人信息弹窗相关函数
        function openCandidateModal() {
            document.getElementById('candidateModal').style.display = 'block';
        }

        function closeCandidateModal() {
            document.getElementById('candidateModal').style.display = 'none';
        }

        function confirmCandidateSelection() {
            const checkboxes = document.querySelectorAll('.candidate-checkbox:checked');
            
            if (checkboxes.length < 3) {
                alert('请至少选择三家候选人！');
                return;
            }

            // 显示候选人信息表格，隐藏空状态
            const candidateDisplayTable = document.getElementById('candidateDisplayTable');
            const candidateEmptyState = document.getElementById('candidateEmptyState');
            const candidateDisplayTableBody = document.getElementById('candidateDisplayTableBody');
            
            candidateDisplayTable.style.display = 'block';
            candidateEmptyState.style.display = 'none';
            
            // 清空现有的候选人显示表格
            candidateDisplayTableBody.innerHTML = '';

            // 根据选中的候选人填充候选人信息表格
            checkboxes.forEach((checkbox, index) => {
                const row = checkbox.closest('tr');
                const cells = row.querySelectorAll('td');
                
                const newRow = document.createElement('tr');
                newRow.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${cells[2].textContent}</td>
                    <td>${cells[3].textContent}</td>
                    <td>${cells[4].textContent}</td>
                    <td>${cells[5].textContent}</td>
                `;
                candidateDisplayTableBody.appendChild(newRow);
            });

            alert(`已成功选择 ${checkboxes.length} 家候选人！`);
            closeCandidateModal();
        }
    </script>
    
    <!-- 标段选择弹窗 -->
    <div id="sectionSelectModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>请选择标段</h3>
                <span class="close" onclick="closeSectionSelectModal()">&times;</span>
            </div>
            
            <!-- 查询区域 -->
            <div class="search-section">
                <div class="search-grid">
                    <div class="search-group">
                        <label>标段名称：</label>
                        <input type="text" id="searchSectionName" placeholder="请输入标段名称">
                    </div>
                    <div class="search-group">
                        <label>项目名称：</label>
                        <input type="text" id="searchProjectName" placeholder="请输入项目名称">
                    </div>
                    <div class="search-group">
                        <label>采购方式：</label>
                        <select id="searchProcurementMethod">
                            <option value="">请选择</option>
                            <option value="公开招标">公开招标</option>
                            <option value="邀请招标">邀请招标</option>
                            <option value="竞争性谈判">竞争性谈判</option>
                            <option value="单一来源">单一来源</option>
                        </select>
                    </div>
                    <div class="search-group">
                        <label>采购类型：</label>
                        <select id="searchProcurementType">
                            <option value="">请选择</option>
                            <option value="货物类">货物类</option>
                            <option value="服务类">服务类</option>
                            <option value="工程类">工程类</option>
                        </select>
                    </div>
                    <div class="search-group">
                        <label>创建时间：</label>
                        <div class="date-range">
                            <input type="date" id="searchStartTime">
                            <span>至</span>
                            <input type="date" id="searchEndTime">
                        </div>
                    </div>
                </div>
                <div class="search-buttons">
                    <button type="button" class="btn btn-primary" onclick="searchSections()">查询</button>
                    <button type="button" class="btn btn-secondary" onclick="resetSearch()">重置</button>
                </div>
            </div>
            
            <!-- 列表区域 -->
            <div class="table-section">
                <table class="table">
                    <thead>
                        <tr>
                            <th width="50">选择</th>
                            <th width="200">标段名称</th>
                            <th width="200">所属项目</th>
                            <th width="120">采购方式</th>
                            <th width="120">采购类型</th>
                            <th width="120">创建时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 分页区域 -->
            <div class="pagination-section">
                <div class="pagination-info">
                    <span>共 <span id="totalRecords">0</span> 条记录，当前第 <span id="currentPage">1</span>/<span id="totalPages">1</span> 页</span>
                </div>
                <div class="pagination-controls">
                    <button type="button" class="btn btn-sm" id="firstPageBtn" onclick="firstPage()">首页</button>
                    <button type="button" class="btn btn-sm" id="prevPageBtn" onclick="prevPage()">上一页</button>
                    <div class="page-numbers" id="pageNumbers">
                        <!-- 页码按钮将动态生成 -->
                    </div>
                    <button type="button" class="btn btn-sm" id="nextPageBtn" onclick="nextPage()">下一页</button>
                    <button type="button" class="btn btn-sm" id="lastPageBtn" onclick="lastPage()">末页</button>
                    <div class="page-size-selector">
                        <label>每页显示：</label>
                        <select id="pageSizeSelect" onchange="changePageSize()">
                            <option value="5" selected>5条</option>
                            <option value="10">10条</option>
                            <option value="20">20条</option>
                            <option value="50">50条</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="confirmSectionSelect()">确认</button>
                <button type="button" class="btn btn-secondary" onclick="closeSectionSelectModal()">取消</button>
            </div>
        </div>
    </div>

    <!-- 候选人信息弹窗 -->
    <div id="candidateModal" class="candidate-modal">
        <div class="candidate-modal-content">
            <div class="candidate-modal-header">
                <h3>候选人信息</h3>
                <span class="close" onclick="closeCandidateModal()">&times;</span>
            </div>
            <div class="candidate-modal-body">
                <p>请至少勾选三家候选人：</p>
                <table class="candidate-selection-table">
                    <thead>
                        <tr>
                            <th width="50">选择</th>
                            <th width="80">序号</th>
                            <th width="200">投标单位全称</th>
                            <th width="120">投标形式</th>
                            <th width="150">投标报价</th>
                            <th width="100">综合得分</th>
                        </tr>
                    </thead>
                    <tbody id="candidateTableBody">
                        <tr>
                            <td><input type="checkbox" class="candidate-checkbox" value="1"></td>
                            <td>1</td>
                            <td>北京科技发展有限公司</td>
                            <td>费率</td>
                            <td>2.5%</td>
                            <td>95.8</td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="candidate-checkbox" value="2"></td>
                            <td>2</td>
                            <td>上海建设工程有限公司</td>
                            <td>金额</td>
                            <td>480000.00</td>
                            <td>92.3</td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="candidate-checkbox" value="3"></td>
                            <td>3</td>
                            <td>广州智能科技股份有限公司</td>
                            <td>费率</td>
                            <td>2.8%</td>
                            <td>89.7</td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="candidate-checkbox" value="4"></td>
                            <td>4</td>
                            <td>深圳创新技术有限公司</td>
                            <td>金额</td>
                            <td>465000.00</td>
                            <td>87.2</td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="candidate-checkbox" value="5"></td>
                            <td>5</td>
                            <td>天津现代工程有限公司</td>
                            <td>费率</td>
                            <td>3.0%</td>
                            <td>85.6</td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="candidate-checkbox" value="6"></td>
                            <td>6</td>
                            <td>重庆建筑设计院</td>
                            <td>金额</td>
                            <td>520000.00</td>
                            <td>83.4</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="candidate-modal-footer">
                <button type="button" class="btn btn-primary" onclick="confirmCandidateSelection()">确认选择</button>
                <button type="button" class="btn btn-secondary" onclick="closeCandidateModal()">取消</button>
            </div>
        </div>
    </div>
</body>
</html>