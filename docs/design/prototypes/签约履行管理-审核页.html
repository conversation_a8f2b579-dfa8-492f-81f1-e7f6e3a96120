<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>签约履行管理 - 审核 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #fff;
            border-bottom: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
        }

        .help-icon {
            margin-left: 8px;
            width: 16px;
            height: 16px;
            background: #6b7280;
            border-radius: 50%;
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .breadcrumb {
            color: #6b7280;
            font-size: 14px;
        }

        .back-btn {
            padding: 8px 16px;
            background: #6b7280;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: background 0.3s;
        }

        .back-btn:hover {
            background: #4b5563;
        }

        /* 内容区域 */
        .content-area {
            padding: 24px;
        }

        /* 页签导航 */
        .tab-nav {
            border-bottom: 1px solid #e6e8eb;
            margin-bottom: 24px;
        }

        .tab-list {
            display: flex;
            list-style: none;
        }

        .tab-item {
            margin-right: 32px;
        }

        .tab-link {
            display: block;
            padding: 12px 0;
            color: #6b7280;
            text-decoration: none;
            font-size: 14px;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab-link.active {
            color: #2563eb;
            border-bottom-color: #2563eb;
        }

        .tab-link:hover {
            color: #2563eb;
        }

        /* 页签内容 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 详情区域 */
        .detail-section {
            background: white;
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .section-header {
            padding: 16px 20px;
            border-bottom: 1px solid #e6e8eb;
            background: #f8fafc;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 16px;
            background: #3b82f6;
            margin-right: 8px;
        }

        .section-content {
            padding: 20px;
        }

        /* 详情网格 */
        .detail-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-item.full-width {
            grid-column: 1 / -1;
        }

        .detail-label {
            font-size: 13px;
            color: #6b7280;
            margin-bottom: 6px;
            font-weight: 500;
        }

        .detail-value {
            font-size: 14px;
            color: #1f2937;
            word-break: break-all;
        }

        .detail-value.rich-content {
            line-height: 1.8;
        }

        .detail-value.rich-content h1,
        .detail-value.rich-content h2,
        .detail-value.rich-content h3 {
            margin: 16px 0 8px 0;
            color: #1f2937;
        }

        .detail-value.rich-content h1 {
            font-size: 18px;
        }

        .detail-value.rich-content h2 {
            font-size: 16px;
        }

        .detail-value.rich-content h3 {
            font-size: 14px;
        }

        .detail-value.rich-content p {
            margin: 8px 0;
        }

        .detail-value.rich-content ul,
        .detail-value.rich-content ol {
            margin: 8px 0;
            padding-left: 24px;
        }

        .detail-value.rich-content li {
            margin: 4px 0;
        }

        .detail-value.rich-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 12px 0;
        }

        .detail-value.rich-content table th,
        .detail-value.rich-content table td {
            border: 1px solid #e6e8eb;
            padding: 8px 12px;
            text-align: left;
        }

        .detail-value.rich-content table th {
            background: #f8fafc;
            font-weight: 600;
        }

        .detail-value.rich-content .signature {
            text-align: right;
            margin-top: 20px;
            font-style: italic;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-draft {
            background: #f3f4f6;
            color: #374151;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-approved {
            background: #d1fae5;
            color: #065f46;
        }

        .status-published {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-rejected {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-withdrawn {
            background: #f3f4f6;
            color: #6b7280;
        }

        /* 操作按钮 */
        .action-bar {
            background: #f8fafc;
            border-top: 1px solid #e6e8eb;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            bottom: 0;
        }

        .btn-group {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-outline {
            background: white;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        /* 时间线样式 */
        .timeline {
            position: relative;
            padding-left: 24px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e6e8eb;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 24px;
            padding-left: 24px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -8px;
            top: 6px;
            width: 12px;
            height: 12px;
            background: #3b82f6;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 0 0 2px #e6e8eb;
        }

        .timeline-item.completed::before {
            background: #10b981;
        }

        .timeline-item.rejected::before {
            background: #ef4444;
        }

        .timeline-content {
            background: #f8fafc;
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            padding: 16px;
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .timeline-title {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
        }

        .timeline-time {
            font-size: 12px;
            color: #6b7280;
        }

        .timeline-user {
            font-size: 13px;
            color: #374151;
            margin-bottom: 4px;
        }

        .timeline-desc {
            font-size: 13px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e6e8eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .help-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            color: #6b7280;
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            color: #374151;
        }

        .help-body {
            padding: 24px;
        }

        .help-section {
            margin-bottom: 24px;
        }

        .help-section h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
        }

        .help-section p {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 12px;
        }

        .help-list {
            list-style: none;
            padding-left: 0;
        }

        .help-list li {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 8px;
            padding-left: 16px;
            position: relative;
        }

        .help-list li::before {
            content: '•';
            color: #3b82f6;
            position: absolute;
            left: 0;
        }

        .help-list strong {
            color: #1f2937;
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .detail-grid {
                grid-template-columns: 1fr;
            }
            
            .btn-group {
                flex-direction: column;
            }
            
            .action-bar {
                flex-direction: column;
                gap: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">
                    签约履行管理 - 审核
                    <span class="help-icon" onclick="showHelp()" title="功能说明">?</span>
                </h1>
                <div class="breadcrumb">首页 > 签约履行管理 > 审核</div>
            </div>
            <button class="back-btn" onclick="goBack()">返回列表</button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 页签导航 -->
            <div class="tab-nav">
                <ul class="tab-list">
                    <li class="tab-item">
                        <a href="#" class="tab-link active" onclick="switchTab('announcement')">履行详情</a>
                    </li>
                    <li class="tab-item">
                        <a href="#" class="tab-link" onclick="switchTab('project')">关联标段</a>
                    </li>
                    <li class="tab-item">
                        <a href="#" class="tab-link" onclick="switchTab('operation')">操作记录</a>
                    </li>
                </ul>
            </div>

            <!-- 履行详情页签 -->
            <div id="announcement-tab" class="tab-content active">
                <!-- 基本信息 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">基本信息</h3>
                    </div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">签约履行标题</div>
                                <div class="detail-value">办公设备采购项目签约履行</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">项目编号</div>
                                <div class="detail-value">XM-2024-001</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购方式</div>
                                <div class="detail-value">公开招标</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购类型</div>
                                <div class="detail-value">货物类</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">合同金额</div>
                                <div class="detail-value">45.80万元</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">中标供应商</div>
                                <div class="detail-value">深圳市科技设备有限公司</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">履行状态</div>
                                <div class="detail-value">
                                    <span class="status-badge status-pending">履行中</span>
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">审核状态</div>
                                <div class="detail-value">
                                    <span class="status-badge status-pending">待审核</span>
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">签约时间</div>
                                <div class="detail-value">2024-02-15 14:30:00</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">计划完成时间</div>
                                <div class="detail-value">2024-03-15 18:00:00</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">创建人</div>
                                <div class="detail-value">张三</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">创建时间</div>
                                <div class="detail-value">2024-02-16 09:30:25</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 履行详情 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">履行详情</h3>
                    </div>
                    <div class="section-content">
                        <div class="detail-item full-width">
                            <div class="detail-value rich-content">
                                <h2>一、合同履行概况</h2>
                                <p>本项目为办公设备采购项目签约履行，合同已于2024年2月15日正式签署。中标供应商深圳市科技设备有限公司按照合同约定，正在有序推进设备采购、配置和交付工作。</p>
                                
                                <h2>二、履行进度安排</h2>
                                <ol>
                                    <li><strong>设备采购阶段（2024年2月16日-2月28日）：</strong>完成设备订购和生产安排</li>
                                    <li><strong>设备配置阶段（2024年3月1日-3月10日）：</strong>完成设备组装、测试和质检</li>
                                    <li><strong>设备交付阶段（2024年3月11日-3月15日）：</strong>完成设备运输、安装和调试</li>
                                    <li><strong>验收阶段（2024年3月16日-3月20日）：</strong>完成设备验收和培训</li>
                                </ol>
                                
                                <h2>三、验收安排</h2>
                                <p><strong>验收时间：</strong>2024年3月16日至3月20日</p>
                                <p><strong>验收地点：</strong>市政府办公大楼各楼层</p>
                                <p><strong>验收标准：</strong>按照合同技术规格和国家相关标准执行</p>
                                <p><strong>验收人员：</strong>采购人代表、技术专家、监督人员</p>
                                
                                <h2>四、履行要求</h2>
                                <p><strong>质量要求：</strong>所有设备必须符合合同约定的技术规格，通过质量检测</p>
                                <p><strong>时间要求：</strong>严格按照履行计划执行，不得无故延期</p>
                                <p><strong>服务要求：</strong>提供设备安装、调试、培训等全套服务</p>
                                
                                <h2>五、采购方信息</h2>
                                <p><strong>采购人：</strong>市政府办公室</p>
                                <p><strong>联系人：</strong>张先生</p>
                                <p><strong>电话：</strong>0755-12345678</p>
                                
                                <h2>六、供应商信息</h2>
                                <p><strong>供应商：</strong>深圳市科技设备有限公司</p>
                                <p><strong>联系人：</strong>李经理</p>
                                <p><strong>电话：</strong>0755-87654321</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 关联标段页签 -->
            <div id="project-tab" class="tab-content">
                <!-- 关联标段 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">关联标段</h3>
                    </div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">标段编号</div>
                                <div class="detail-value">BD-2024-001-01</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">标段名称</div>
                                <div class="detail-value">办公设备采购项目第一标段</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">标段金额</div>
                                <div class="detail-value">45.80万元</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">中标供应商</div>
                                <div class="detail-value">深圳市科技设备有限公司</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">中标金额</div>
                                <div class="detail-value">45.80万元</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">中标时间</div>
                                <div class="detail-value">2024-02-10 15:30:00</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购需求</div>
                                <div class="detail-value">台式电脑50台、打印机10台、复印机5台</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">技术要求</div>
                                <div class="detail-value">Intel i5处理器，8GB内存，256GB SSD</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">交付时间</div>
                                <div class="detail-value">合同签订后30个工作日内</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">交付地点</div>
                                <div class="detail-value">市政府办公大楼</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">质保期</div>
                                <div class="detail-value">3年</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">标段状态</div>
                                <div class="detail-value">
                                    <span class="status-badge status-success">已中标</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 时间安排 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">时间安排</h3>
                    </div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">公告发布时间</div>
                                <div class="detail-value">2024年1月15日</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">文件获取时间</div>
                                <div class="detail-value">2024年1月15日至1月25日</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">投标截止时间</div>
                                <div class="detail-value">2024年2月1日 09:30</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">开标时间</div>
                                <div class="detail-value">2024年2月1日 09:30</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">评标时间</div>
                                <div class="detail-value">2024年2月1日至2月3日</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">中标公示时间</div>
                                <div class="detail-value">2024年2月5日至2月7日</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 参与方信息 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">参与方信息</h3>
                    </div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">采购人</div>
                                <div class="detail-value">市政府办公室</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购代理机构</div>
                                <div class="detail-value">市政府采购中心</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">监督部门</div>
                                <div class="detail-value">市财政局</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作记录页签 -->
            <div id="operation-tab" class="tab-content">
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">操作记录</h3>
                    </div>
                    <div class="section-content">
                        <div class="timeline">
                            <div class="timeline-item completed">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-title">提交审核</div>
                                        <div class="timeline-time">2024-02-17 10:15:00</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三</div>
                                    <div class="timeline-desc">签约履行记录编辑完成，已提交部门负责人审批。</div>
                                </div>
                            </div>
                            
                            <div class="timeline-item completed">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-title">关联标段</div>
                                        <div class="timeline-time">2024-02-16 16:45:00</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三</div>
                                    <div class="timeline-desc">关联了办公设备采购项目第一标段，确认中标供应商和合同信息。</div>
                                </div>
                            </div>
                            
                            <div class="timeline-item completed">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-title">创建履行记录</div>
                                        <div class="timeline-time">2024-02-16 09:30:00</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三</div>
                                    <div class="timeline-desc">创建了办公设备采购项目签约履行记录，填写了基本信息和履行详情。</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-bar">
            <div class="btn-group">
                <button class="btn btn-secondary" onclick="goBack()">返回列表</button>
            </div>
            <div class="btn-group">
                <button class="btn btn-outline" onclick="printRecord()">打印履行记录</button>
                <button class="btn btn-success" onclick="approveRecord()">审核通过</button>
                <button class="btn btn-danger" onclick="rejectRecord()">审核驳回</button>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="help-modal" class="help-modal">
        <div class="help-content">
            <div class="help-header">
                <h3 class="help-title">签约履行审核功能说明</h3>
                <button class="close-btn" onclick="hideHelp()">&times;</button>
            </div>
            <div class="help-body">
                <div class="help-section">
                    <h4>功能概述</h4>
                    <p>签约履行审核页面用于审核签约履行记录的内容，确保履行信息准确、完整、符合合同约定。审核人员可以查看履行详情、关联标段信息和操作记录，并进行审核通过或驳回操作。</p>
                </div>
                
                <div class="help-section">
                    <h4>页签说明</h4>
                    <ul class="help-list">
                        <li><strong>履行详情：</strong>显示签约履行的基本信息和详细内容，供审核人员查看</li>
                        <li><strong>关联标段：</strong>显示关联的标段信息、中标供应商、合同金额等</li>
                        <li><strong>操作记录：</strong>显示履行记录的创建、编辑、提交等操作历史</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>审核说明</h4>
                    <ul class="help-list">
                        <li><strong>审核通过：</strong>确认履行记录内容无误，符合合同要求，履行记录将被确认</li>
                        <li><strong>审核驳回：</strong>发现履行记录有问题，需要修改，记录将退回给创建人</li>
                        <li><strong>审核意见：</strong>审核时需要填写审核意见，说明通过原因或驳回理由</li>
                        <li><strong>打印履行记录：</strong>可以打印履行记录内容进行线下审核</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>注意事项</h4>
                    <ul class="help-list">
                        <li>审核前请仔细核对履行记录内容的准确性和完整性</li>
                        <li>确认关联标段信息与实际合同一致</li>
                        <li>审核意见将被保存并显示在操作记录中</li>
                        <li>审核通过后履行记录状态将更新，请谨慎操作</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页签切换
        function switchTab(tabName) {
            // 隐藏所有页签内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有页签链接的激活状态
            const tabLinks = document.querySelectorAll('.tab-link');
            tabLinks.forEach(link => {
                link.classList.remove('active');
            });
            
            // 显示选中的页签内容
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 激活选中的页签链接
            event.target.classList.add('active');
        }

        // 帮助弹窗
        function showHelp() {
            document.getElementById('help-modal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('help-modal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });

        // 业务操作函数
        function goBack() {
            window.location.href = '签约履行管理-列表页.html';
        }

        function approveRecord() {
            const opinion = prompt('请输入审核意见：');
            if (opinion !== null && opinion.trim() !== '') {
                console.log('审核通过，意见：', opinion);
                alert('审核通过！履行记录已确认。');
                // 这里应该调用审核通过接口
                window.location.href = '签约履行管理-列表页.html';
            }
        }

        function rejectRecord() {
            const reason = prompt('请输入驳回原因：');
            if (reason !== null && reason.trim() !== '') {
                console.log('审核驳回，原因：', reason);
                alert('审核驳回！履行记录已退回给创建人修改。');
                // 这里应该调用审核驳回接口
                window.location.href = '签约履行管理-列表页.html';
            }
        }

        function printRecord() {
            console.log('打印履行记录');
            window.print();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('签约履行审核页面加载完成');
        });
    </script>
</body>
</html>