<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>招采平台 - 新建补遗/澄清/答疑</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        

        
        /* 主体布局 */
        .main-container {
            height: 100vh;
        }
        
        /* 内容区域 */
        .content-wrapper {
            padding: 20px;
            background-color: #f5f7fa;
            min-height: 100vh;
        }
        

        
        /* 页面内容 */
        .page-content {
            background: white;
            border-radius: 0 4px 4px 4px;
            border: 1px solid #e8eaec;
            padding: 20px;
        }
        

        
        /* 表单样式 */
        .form-section {
            margin-bottom: 32px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #1890ff;
            position: relative;
        }
        
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            bottom: -2px;
            width: 40px;
            height: 2px;
            background: #1890ff;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-grid.full-width {
            grid-template-columns: 1fr;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-group.required .form-label::after {
            content: '*';
            color: #ff4d4f;
            margin-left: 4px;
        }
        
        .form-label {
            font-size: 14px;
            color: #262626;
            font-weight: 500;
        }
        
        .form-control {
            height: 36px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 0 12px;
            font-size: 14px;
            width: 100%;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
        }
        
        .form-control.error {
            border-color: #ff4d4f;
        }
        
        .form-control.error:focus {
            box-shadow: 0 0 0 2px rgba(255,77,79,0.2);
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
            padding: 12px;
            line-height: 1.5;
        }
        
        .form-textarea.large {
            min-height: 120px;
        }
        
        .error-message {
            color: #ff4d4f;
            font-size: 12px;
            margin-top: 4px;
        }
        
        .help-text {
            color: #8c8c8c;
            font-size: 12px;
            margin-top: 4px;
        }
        
        /* 附件上传 */
        .upload-area {
            border: 2px dashed #d9d9d9;
            border-radius: 4px;
            padding: 40px 20px;
            text-align: center;
            background: #fafafa;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .upload-area:hover {
            border-color: #1890ff;
            background: #e6f7ff;
        }
        
        .upload-area.dragover {
            border-color: #1890ff;
            background: #e6f7ff;
        }
        
        .upload-icon {
            font-size: 48px;
            color: #d9d9d9;
            margin-bottom: 16px;
        }
        
        .upload-text {
            font-size: 16px;
            color: #262626;
            margin-bottom: 8px;
        }
        
        .upload-hint {
            font-size: 14px;
            color: #8c8c8c;
        }
        
        .file-list {
            margin-top: 16px;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            border: 1px solid #e8eaec;
            border-radius: 4px;
            margin-bottom: 8px;
            background: white;
        }
        
        .file-info {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }
        
        .file-icon {
            width: 32px;
            height: 32px;
            background: #1890ff;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }
        
        .file-details {
            flex: 1;
        }
        
        .file-name {
            font-size: 14px;
            color: #262626;
            margin-bottom: 4px;
        }
        
        .file-meta {
            font-size: 12px;
            color: #8c8c8c;
        }
        
        .file-actions {
            display: flex;
            gap: 8px;
        }
        
        /* 表单控件组合样式 */
        .form-control-with-btn {
            display: flex;
            gap: 8px;
        }
        
        .form-control-with-btn .form-control {
            flex: 1;
        }
        
        .btn-sm {
            height: 32px;
            padding: 0 12px;
            font-size: 12px;
        }
        
        .file-action {
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }
        
        .file-action:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .file-action.delete:hover {
            border-color: #ff4d4f;
            color: #ff4d4f;
        }
        
        /* 操作按钮 */
        .form-actions {
            background: #f8fafc;
            border-top: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            gap: 16px;
            z-index: 1000;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn-group {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            padding: 0 24px;
            height: 40px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            border: 1px solid transparent;
            transition: all 0.3s;
            min-width: 100px;
        }
        
        .btn-primary {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        
        .btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }
        
        .btn-success {
            background-color: #52c41a;
            color: white;
            border-color: #52c41a;
        }
        
        .btn-success:hover {
            background-color: #73d13d;
            border-color: #73d13d;
        }
        
        .btn-default {
            background-color: white;
            border-color: #d9d9d9;
            color: #262626;
        }
        
        .btn-default:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        /* 单选框样式 */
        .radio-group {
            display: flex;
            gap: 20px;
            align-items: center;
        }
        
        .radio-item {
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        
        .radio-item input[type="radio"] {
            margin-right: 6px;
            width: 16px;
            height: 16px;
        }
        
        .radio-label {
            font-size: 14px;
            color: #333;
            user-select: none;
        }
        
        /* 富文本编辑器 */
        .rich-editor {
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .editor-toolbar {
            background-color: #f8f8f9;
            border-bottom: 1px solid #d9d9d9;
            padding: 8px 12px;
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .editor-btn {
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            background-color: #fff;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            color: #666;
        }
        
        .editor-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .editor-content {
            min-height: 120px;
            padding: 12px;
            outline: none;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .editor-content:empty::before {
            content: attr(data-placeholder);
            color: #999;
        }
        
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .form-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .content-wrapper {
                padding: 16px;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .form-actions {
                padding: 12px 16px;
            }
            
            .btn {
                margin-left: 0;
                margin-top: 8px;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- 主体容器 -->
    <div class="main-container">
        <!-- 内容区域 -->
        <div class="content-wrapper">
            
            <!-- 页面内容 -->
            <div class="page-content">
                <form id="clarificationForm">
                    <!-- 标段信息 -->
                    <div class="form-section">
                        <div class="section-title">标段信息</div>
                        <div class="form-grid">
                            <div class="form-group required">
                                <label class="form-label">选择标段</label>
                                <div class="form-control-with-btn">
                                    <input type="text" class="form-control" name="sectionName" placeholder="请选择标段" readonly>
                                    <button type="button" class="btn btn-default btn-sm" id="selectSectionBtn">选择标段</button>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">标段名称</label>
                                <input type="text" class="form-control" name="sectionTitle" readonly>
                                <div class="help-text">由标段自动带出</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">标段编号</label>
                                <input type="text" class="form-control" name="sectionCode" readonly>
                                <div class="help-text">由标段自动带出</div>
                            </div>
                        </div>
                        
                        <div class="form-grid full-width">
                            <div class="form-group">
                                <label class="form-label">标段说明</label>
                                <textarea class="form-control form-textarea" name="sectionDescription" readonly placeholder="由标段自动带出"></textarea>
                                <div class="help-text">由标段自动带出</div>
                            </div>
                        </div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">采购金额（万元）</label>
                                <input type="number" class="form-control" name="sectionAmount" readonly>
                                <div class="help-text">由标段自动带出</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 项目信息 -->
                    <div class="form-section">
                        <div class="section-title">项目信息</div>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">计划项目编号</label>
                                <input type="text" class="form-control" name="planCode" readonly>
                                <div class="help-text">由标段自动带出</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">所属计划项目名称</label>
                                <input type="text" class="form-control" name="planName" readonly>
                                <div class="help-text">由标段自动带出</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">采购方式</label>
                                <input type="text" class="form-control" name="procurementMethod" readonly>
                                <div class="help-text">由标段自动带出</div>
                            </div>
                        </div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">项目业主</label>
                                <input type="text" class="form-control" name="projectOwner" readonly>
                                <div class="help-text">由标段自动带出</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">所属二级公司单位</label>
                                <input type="text" class="form-control" name="secondaryCompany" readonly>
                                <div class="help-text">由标段自动带出</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">代理机构</label>
                                <input type="text" class="form-control" name="agency" readonly>
                                <div class="help-text">由标段自动带出</div>
                            </div>
                        </div>
                        
                        <div class="form-grid full-width">
                            <div class="form-group">
                                <label class="form-label">项目基本情况（建设内容及规模）</label>
                                <textarea class="form-control form-textarea" name="projectDescription" readonly placeholder="由标段自动带出"></textarea>
                                <div class="help-text">由标段自动带出</div>
                            </div>
                        </div>
                        
                        <div class="form-grid full-width">
                            <div class="form-group">
                                <label class="form-label">备注</label>
                                <textarea class="form-control form-textarea" name="remarks" readonly placeholder="由标段自动带出"></textarea>
                                <div class="help-text">由标段自动带出</div>
                            </div>
                        </div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">立项决策文件</label>
                                <input type="text" class="form-control" name="decisionFile" readonly>
                                <div class="help-text">由标段自动带出</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 补遗/澄清/答疑说明 -->
                    <div class="form-section">
                        <div class="section-title">补遗/澄清/答疑说明</div>
                        <div class="form-grid">
                            <div class="form-group required">
                                <label class="form-label">类型</label>
                                <div class="radio-group">
                                    <div class="radio-item">
                                        <input type="radio" name="clarificationType" value="supplement" id="supplement">
                                        <label class="radio-label" for="supplement">补遗</label>
                                    </div>
                                    <div class="radio-item">
                                        <input type="radio" name="clarificationType" value="clarification" id="clarification">
                                        <label class="radio-label" for="clarification">澄清</label>
                                    </div>
                                    <div class="radio-item">
                                        <input type="radio" name="clarificationType" value="qa" id="qa">
                                        <label class="radio-label" for="qa">答疑</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group required">
                                <label class="form-label">是否公示</label>
                                <select class="form-control" name="isPublic">
                                    <option value="">请选择</option>
                                    <option value="yes">是</option>
                                    <option value="no">否</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-grid full-width">
                            <div class="form-group required">
                                <label class="form-label">补遗/澄清/答疑内容</label>
                                <div class="rich-editor">
                                    <div class="editor-toolbar">
                                        <button type="button" class="editor-btn" onclick="formatText('bold')">B</button>
                                        <button type="button" class="editor-btn" onclick="formatText('italic')">I</button>
                                        <button type="button" class="editor-btn" onclick="formatText('underline')">U</button>
                                        <button type="button" class="editor-btn" onclick="formatText('insertOrderedList')">1.</button>
                                        <button type="button" class="editor-btn" onclick="formatText('insertUnorderedList')">•</button>
                                        <button type="button" class="editor-btn" onclick="insertTable()">表格</button>
                                    </div>
                                    <div class="editor-content" contenteditable="true" data-placeholder="请输入补遗/澄清/答疑的详细内容..."></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 审核依据文件 -->
                    <div class="form-section">
                        <div class="section-title">审核依据文件</div>
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-icon">📁</div>
                            <div class="upload-text">点击或拖拽文件到此处上传</div>
                            <div class="upload-hint">支持 PDF、DOC、DOCX、XLS、XLSX 格式，单个文件不超过 10MB</div>
                            <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.xls,.xlsx" style="display: none;">
                        </div>
                        <div class="file-list" id="fileList"></div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="form-actions">
                        <div class="btn-group">
                            <button type="button" class="btn btn-default" id="cancelBtn">取消</button>
                            <button type="button" class="btn btn-default" id="saveBtn">保存草稿</button>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-default" id="previewBtn">预览</button>
                            <button type="button" class="btn btn-primary" id="submitBtn">提交审核</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('clarificationForm');
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const fileList = document.getElementById('fileList');
            
            let uploadedFiles = [];
            
            // 文件上传功能
            uploadArea.addEventListener('click', () => fileInput.click());
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                handleFiles(e.dataTransfer.files);
            });
            
            fileInput.addEventListener('change', (e) => {
                handleFiles(e.target.files);
            });
            
            function handleFiles(files) {
                Array.from(files).forEach(file => {
                    if (file.size > 10 * 1024 * 1024) {
                        alert('文件大小不能超过10MB');
                        return;
                    }
                    
                    const fileObj = {
                        id: Date.now() + Math.random(),
                        name: file.name,
                        size: formatFileSize(file.size),
                        type: file.type
                    };
                    
                    uploadedFiles.push(fileObj);
                    renderFileList();
                });
            }
            
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
            
            function renderFileList() {
                fileList.innerHTML = uploadedFiles.map(file => `
                    <div class="file-item">
                        <div class="file-info">
                            <div class="file-icon">📄</div>
                            <div class="file-details">
                                <div class="file-name">${file.name}</div>
                                <div class="file-meta">${file.size}</div>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button type="button" class="file-action" onclick="previewFile('${file.id}')">预览</button>
                            <button type="button" class="file-action delete" onclick="removeFile('${file.id}')">删除</button>
                        </div>
                    </div>
                `).join('');
            }
            
            // 全局函数
            window.removeFile = function(fileId) {
                uploadedFiles = uploadedFiles.filter(file => file.id != fileId);
                renderFileList();
            };
            
            window.previewFile = function(fileId) {
                alert('预览功能开发中...');
            };
            
            window.formatText = function(command) {
                document.execCommand(command, false, null);
            };
            
            window.insertTable = function() {
                const table = '<table border="1" style="border-collapse: collapse; width: 100%;"><tr><td>单元格1</td><td>单元格2</td></tr><tr><td>单元格3</td><td>单元格4</td></tr></table>';
                document.execCommand('insertHTML', false, table);
            };
            
            window.showHelp = function() {
                alert('帮助功能开发中...');
            };
            
            // 表单提交
            document.getElementById('submitBtn').addEventListener('click', function() {
                if (validateForm()) {
                    alert('提交成功！');
                }
            });
            
            document.getElementById('saveBtn').addEventListener('click', function() {
                alert('保存草稿成功！');
            });
            
            document.getElementById('previewBtn').addEventListener('click', function() {
                alert('预览功能开发中...');
            });
            
            document.getElementById('cancelBtn').addEventListener('click', function() {
                if (confirm('确定要取消吗？未保存的数据将丢失。')) {
                    window.history.back();
                }
            });
            
            function validateForm() {
                const requiredFields = form.querySelectorAll('[required], .form-group.required input, .form-group.required select');
                let isValid = true;
                
                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        field.classList.add('error');
                        isValid = false;
                    } else {
                        field.classList.remove('error');
                    }
                });
                
                if (!isValid) {
                    alert('请填写所有必填字段');
                }
                
                return isValid;
            }
        });
    </script>
</body>
</html>