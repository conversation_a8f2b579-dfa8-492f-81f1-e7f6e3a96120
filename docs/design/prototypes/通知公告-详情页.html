<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知公告详情 - 招采平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            padding: 0;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* 页面标题 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 2px solid #e8eaec;
        }
        
        .page-title {
            font-size: 20px;
            font-weight: bold;
            color: #17233d;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .back-btn {
            color: #1890ff;
            text-decoration: none;
            font-size: 14px;
            margin-right: 12px;
        }
        
        .back-btn:hover {
            color: #40a9ff;
        }
        
        .page-breadcrumb {
            font-size: 13px;
            color: #808695;
        }
        
        /* 详情卡片 */
        .detail-card {
            background: white;
            border-radius: 4px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.05);
            margin-bottom: 20px;
        }
        
        .detail-header {
            padding: 20px;
            border-bottom: 1px solid #e8eaec;
        }
        
        .detail-title {
            font-size: 18px;
            font-weight: bold;
            color: #17233d;
            margin-bottom: 12px;
            line-height: 1.4;
        }
        
        .detail-meta {
            display: flex;
            gap: 24px;
            font-size: 13px;
            color: #808695;
        }
        
        .detail-body {
            padding: 20px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #17233d;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e8eaec;
        }
        
        .content-text {
            font-size: 14px;
            line-height: 1.8;
            color: #515a6e;
            margin-bottom: 20px;
            text-align: justify;
        }
        
        .content-text p {
            margin-bottom: 12px;
        }
        
        .content-text ul {
            margin-left: 20px;
            margin-bottom: 12px;
        }
        
        .content-text li {
            margin-bottom: 6px;
        }
        
        /* 附件区域 */
        .attachment-section {
            margin-top: 20px;
        }
        
        .attachment-list {
            list-style: none;
        }
        
        .attachment-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px;
            background-color: #f8f9fa;
            border-radius: 4px;
            margin-bottom: 8px;
            transition: background-color 0.3s;
        }
        
        .attachment-item:hover {
            background-color: #e6f7ff;
        }
        
        .attachment-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        
        .attachment-name {
            flex: 1;
            font-size: 14px;
            color: #515a6e;
        }
        
        .attachment-size {
            font-size: 12px;
            color: #808695;
        }
        
        .attachment-download {
            color: #1890ff;
            text-decoration: none;
            font-size: 13px;
            padding: 4px 8px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .attachment-download:hover {
            background-color: #e6f7ff;
            text-decoration: none;
        }
        
        /* 操作按钮 */
        .action-buttons {
            padding: 20px;
            border-top: 1px solid #e8eaec;
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }
        
        .btn {
            height: 36px;
            padding: 0 16px;
            border: none;
            border-radius: 4px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }
        
        .btn-primary {
            background-color: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #40a9ff;
        }
        
        .btn-default {
            background-color: #f5f5f5;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .btn-default:hover {
            background-color: #e6e6e6;
        }
        
        /* 页签样式 */
        .tabs {
            display: flex;
            background: white;
            border-radius: 4px 4px 0 0;
            border: 1px solid #e8eaec;
            border-bottom: none;
            margin-bottom: -1px;
        }
        
        .tab {
            padding: 12px 20px;
            cursor: pointer;
            border-right: 1px solid #e8eaec;
            background: #f8f9fa;
            transition: all 0.3s;
        }
        
        .tab.active {
            color: #1890ff;
            background-color: white;
            border-bottom: 2px solid #1890ff;
            margin-bottom: -1px;
        }
        
        .tab-content {
            background: white;
            border: 1px solid #e8eaec;
            border-radius: 0 4px 4px 4px;
            min-height: 200px;
        }
        
        .tab-pane {
            display: none;
            padding: 20px;
        }
        
        .tab-pane.active {
            display: block;
        }
        
        /* 操作记录样式 */
        .operation-list {
            list-style: none;
        }
        
        .operation-item {
            padding: 16px 0;
            border-bottom: 1px solid #e8eaec;
            display: flex;
            gap: 16px;
        }
        
        .operation-item:last-child {
            border-bottom: none;
        }
        
        .operation-time {
            width: 140px;
            font-size: 13px;
            color: #808695;
            flex-shrink: 0;
        }
        
        .operation-content {
            flex: 1;
        }
        
        .operation-action {
            font-size: 14px;
            font-weight: 500;
            color: #17233d;
            margin-bottom: 4px;
        }
        
        .operation-user {
            font-size: 13px;
            color: #1890ff;
            margin-bottom: 4px;
        }
        
        .operation-desc {
            font-size: 13px;
            color: #808695;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div>
                <div class="page-title">
                    <a href="javascript:history.back()" class="back-btn">← 返回</a>
                    通知公告详情
                </div>
                <div class="page-breadcrumb">首页 > 通知公告 > 详情</div>
            </div>
        </div>
        
        <!-- 详情内容 -->
        <div class="detail-card">
            <div class="detail-header">
                <div class="detail-title" id="noticeTitle">关于2024年度采购计划编制工作的通知</div>
                <div class="detail-meta">
                    <span>发布人：系统管理员</span>
                    <span>发布时间：2023-12-10 09:30:00</span>
                    <span>浏览次数：156</span>
                </div>
            </div>
        </div>
        
        <!-- 页签区域 -->
        <div class="tabs">
            <div class="tab active" onclick="switchTab('detail')">通知详情</div>
            <div class="tab" onclick="switchTab('operation')">操作记录</div>
        </div>
        
        <div class="tab-content">
            <!-- 通知详情页签 -->
            <div class="tab-pane active" id="detail-pane">
                <div class="section-title">公告内容</div>
                <div class="content-text" id="noticeContent">
                    <p>各相关部门：</p>
                    <p>为进一步规范和完善我单位2024年度采购计划编制工作，确保采购活动的科学性、合理性和有效性，现就相关事项通知如下：</p>
                    
                    <p><strong>一、编制原则</strong></p>
                    <ul>
                        <li>坚持统筹规划、合理安排的原则，确保采购计划与单位发展规划和年度预算相衔接；</li>
                        <li>坚持公开透明、公平竞争的原则，严格按照政府采购法律法规执行；</li>
                        <li>坚持节约高效、绿色环保的原则，优先采购节能环保产品。</li>
                    </ul>
                    
                    <p><strong>二、时间安排</strong></p>
                    <ul>
                        <li>2023年12月15日前：各部门提交初步采购需求；</li>
                        <li>2023年12月25日前：完成采购计划汇总和初审；</li>
                        <li>2024年1月5日前：完成采购计划审批和发布。</li>
                    </ul>
                    
                    <p><strong>三、工作要求</strong></p>
                    <p>1. 各部门要高度重视采购计划编制工作，指定专人负责，确保按时完成；</p>
                    <p>2. 采购需求要真实准确，避免重复采购和浪费；</p>
                    <p>3. 严格执行采购预算，不得超预算采购。</p>
                    
                    <p>特此通知。</p>
                    
                    <p style="text-align: right; margin-top: 30px;">招采管理部<br>2023年12月10日</p>
                </div>
                
                <!-- 附件区域 -->
                <div class="attachment-section">
                    <div class="section-title">附件</div>
                    <ul class="attachment-list">
                        <li class="attachment-item">
                            <div class="attachment-icon">📄</div>
                            <div class="attachment-name">2024年度采购计划编制指南.pdf</div>
                            <div class="attachment-size">2.5MB</div>
                            <a href="#" class="attachment-download" onclick="downloadAttachment('guide.pdf')">下载</a>
                        </li>
                        <li class="attachment-item">
                            <div class="attachment-icon">📊</div>
                            <div class="attachment-name">采购计划申报表模板.xlsx</div>
                            <div class="attachment-size">156KB</div>
                            <a href="#" class="attachment-download" onclick="downloadAttachment('template.xlsx')">下载</a>
                        </li>
                        <li class="attachment-item">
                            <div class="attachment-icon">📋</div>
                            <div class="attachment-name">采购需求确认单.docx</div>
                            <div class="attachment-size">89KB</div>
                            <a href="#" class="attachment-download" onclick="downloadAttachment('confirm.docx')">下载</a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- 操作记录页签 -->
            <div class="tab-pane" id="operation-pane">
                <ul class="operation-list">
                    <li class="operation-item">
                        <div class="operation-time">2023-12-10 09:30</div>
                        <div class="operation-content">
                            <div class="operation-action">发布通知公告</div>
                            <div class="operation-user">操作人：系统管理员</div>
                            <div class="operation-desc">将通知公告状态从"待发布"更改为"已发布"，通知公告正式生效并对外展示。</div>
                        </div>
                    </li>
                    <li class="operation-item">
                        <div class="operation-time">2023-12-10 09:15</div>
                        <div class="operation-content">
                            <div class="operation-action">审核通过</div>
                            <div class="operation-user">操作人：部门领导</div>
                            <div class="operation-desc">审核通过该通知公告，内容符合相关规定，可以发布。</div>
                        </div>
                    </li>
                    <li class="operation-item">
                        <div class="operation-time">2023-12-09 16:45</div>
                        <div class="operation-content">
                            <div class="operation-action">提交审核</div>
                            <div class="operation-user">操作人：采购专员</div>
                            <div class="operation-desc">完成通知公告内容编写，提交给上级领导审核。</div>
                        </div>
                    </li>
                    <li class="operation-item">
                        <div class="operation-time">2023-12-09 14:20</div>
                        <div class="operation-content">
                            <div class="operation-action">上传附件</div>
                            <div class="operation-user">操作人：采购专员</div>
                            <div class="operation-desc">上传了3个附件文件：采购计划编制指南、申报表模板、需求确认单。</div>
                        </div>
                    </li>
                    <li class="operation-item">
                        <div class="operation-time">2023-12-09 10:30</div>
                        <div class="operation-content">
                            <div class="operation-action">创建通知公告</div>
                            <div class="operation-user">操作人：采购专员</div>
                            <div class="operation-desc">创建新的通知公告，标题为"关于2024年度采购计划编制工作的通知"，状态为"草稿"。</div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="detail-card">
            <div class="action-buttons">
                <button class="btn btn-default" onclick="printNotice()">打印</button>
                <button class="btn btn-primary" onclick="shareNotice()">分享</button>
            </div>
        </div>
    </div>
    
    <script>
        // 页签切换
        function switchTab(tabName) {
            // 移除所有活动状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.remove('active');
            });
            
            // 激活当前页签
            event.target.classList.add('active');
            document.getElementById(tabName + '-pane').classList.add('active');
        }
        
        // 下载附件
        function downloadAttachment(filename) {
            console.log('下载附件:', filename);
            alert('下载功能已触发：' + filename);
        }
        
        // 打印通知
        function printNotice() {
            window.print();
        }
        
        // 分享通知
        function shareNotice() {
            const url = window.location.href;
            if (navigator.share) {
                navigator.share({
                    title: document.getElementById('noticeTitle').textContent,
                    url: url
                });
            } else {
                // 复制链接到剪贴板
                navigator.clipboard.writeText(url).then(() => {
                    alert('链接已复制到剪贴板');
                });
            }
        }
        
        // 根据URL参数加载对应的通知详情
        function loadNoticeDetail() {
            const urlParams = new URLSearchParams(window.location.search);
            const noticeId = urlParams.get('id');
            
            if (noticeId) {
                console.log('加载通知详情，ID:', noticeId);
                
                // 根据不同的ID加载不同的内容
                const noticeData = {
                    'notice-001': {
                        title: '关于2024年度采购计划编制工作的通知',
                        content: document.getElementById('noticeContent').innerHTML
                    },
                    'notice-002': {
                        title: '招采平台系统升级维护通知',
                        content: '<p>各位用户：</p><p>为提升系统性能和用户体验，招采平台将于2023年12月15日22:00-24:00进行系统升级维护。</p><p>维护期间系统将暂停服务，请各位用户提前做好相关准备工作。</p><p>给您带来的不便，敬请谅解。</p>'
                    },
                    'notice-003': {
                        title: '关于规范采购流程管理的通知',
                        content: '<p>各相关部门：</p><p>为进一步规范采购流程管理，提高采购效率和质量，现就相关事项通知如下：</p><p>一、严格执行采购程序...</p>'
                    }
                };
                
                if (noticeData[noticeId]) {
                    document.getElementById('noticeTitle').textContent = noticeData[noticeId].title;
                    document.getElementById('noticeContent').innerHTML = noticeData[noticeId].content;
                }
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadNoticeDetail();
            console.log('通知公告详情页加载完成');
        });
    </script>
</body>
</html>