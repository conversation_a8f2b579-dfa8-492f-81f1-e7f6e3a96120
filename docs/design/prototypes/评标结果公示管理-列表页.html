<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评标结果公示管理 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #fff;
            border-bottom: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #262626;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .help-icon {
            width: 16px;
            height: 16px;
            background: #1890ff;
            border-radius: 50%;
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }



        /* 页签样式 */
        .tab-container {
            background: #fff;
            border-bottom: 1px solid #e6e8eb;
        }

        .tab-nav {
            display: flex;
            padding: 0 24px;
        }

        .tab-item {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            color: #6b7280;
            font-weight: 500;
            transition: all 0.3s;
        }

        .tab-item.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
            background: #f5f7fa;
        }

        .tab-item:hover {
            color: #1890ff;
            background: #f5f7fa;
        }

        /* 内容区域 */
        .content-area {
            padding: 24px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 查询区域 */
        .search-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 13px;
            color: #555;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-control {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .btn-group {
            display: flex;
            gap: 10px;
            align-items: end;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-outline {
            background: white;
            color: #3498db;
            border: 1px solid #3498db;
        }

        .btn-outline:hover {
            background: #3498db;
            color: white;
        }

        /* 高级查询 */
        .advanced-search {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            display: none;
        }

        /* 功能操作栏 */
        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
        }

        .btn-success {
            background: #52c41a;
            color: white;
        }

        .btn-success:hover {
            background: #73d13d;
        }

        .btn-danger {
            background: #ff4d4f;
            color: white;
        }

        .btn-danger:hover {
            background: #ff7875;
        }

        /* 表格样式 */
        .table-container {
            background: white;
            border: 1px solid #e6e8eb;
            border-radius: 8px;
            overflow: hidden;
        }

        .table-wrapper {
            overflow-x: auto;
            max-width: 100%;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            min-width: 1200px;
        }

        .data-table th,
        .data-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
            font-size: 13px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .data-table th {
            background: #fafafa;
            font-weight: 600;
            color: #262626;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table tbody tr:hover {
            background: #f5f7fa;
        }

        /* 冻结列样式 */
        .frozen-column {
            position: sticky;
            background: inherit;
            z-index: 5;
        }

        .checkbox-col {
            width: 50px;
            left: 0;
        }

        .name-col {
            width: 200px;
            left: 50px;
        }

        .action-col {
            width: 180px;
            right: 0;
        }

        .frozen-column::after {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            width: 1px;
            background: #e6e8eb;
        }

        .checkbox-col::after,
        .name-col::after {
            right: -1px;
        }

        .action-col::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: -1px;
            width: 1px;
            background: #e6e8eb;
        }

        /* 表格链接 */
        .table-link {
            color: #1890ff;
            text-decoration: none;
            cursor: pointer;
        }

        .table-link:hover {
            color: #40a9ff;
            text-decoration: underline;
        }

        /* 状态标签 */
        .status-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }

        .status-processing {
            background: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }

        .status-approved {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        .status-rejected {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }

        .status-published {
            background: #f0f5ff;
            color: #722ed1;
            border: 1px solid #d3adf7;
        }

        /* 操作按钮 */
        .action-btn {
            padding: 4px 8px;
            margin: 0 2px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-submit {
            background: #1890ff;
            color: white;
        }

        .btn-submit:hover {
            background: #40a9ff;
        }

        .btn-approve {
            background: #52c41a;
            color: white;
        }

        .btn-approve:hover {
            background: #73d13d;
        }

        .btn-publish {
            background: #722ed1;
            color: white;
        }

        .btn-publish:hover {
            background: #9254de;
        }

        .btn-edit {
            background: #fa8c16;
            color: white;
        }

        .btn-edit:hover {
            background: #ffa940;
        }

        .btn-delete {
            background: #ff4d4f;
            color: white;
        }

        .btn-delete:hover {
            background: #ff7875;
        }

        .btn-view {
            background: #13c2c2;
            color: white;
        }

        .btn-view:hover {
            background: #36cfc9;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            background: #fafafa;
            border-top: 1px solid #f0f0f0;
        }

        .pagination-info {
            color: #666;
            font-size: 13px;
        }

        .pagination-controls {
            display: flex;
            gap: 8px;
        }

        .page-btn {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            background: white;
            color: #666;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s;
        }

        .page-btn:hover:not(:disabled) {
            border-color: #1890ff;
            color: #1890ff;
        }

        .page-btn.active {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }

        .page-btn:disabled {
            background: #f5f5f5;
            color: #bbb;
            cursor: not-allowed;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 24px;
            border-radius: 8px;
            width: 500px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .help-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e6e8eb;
        }

        .help-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #999;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            color: #666;
        }

        .help-body {
            color: #666;
            line-height: 1.6;
        }

        .help-section {
            margin-bottom: 16px;
        }

        .help-section h4 {
            color: #262626;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .help-section p {
            margin-bottom: 8px;
            font-size: 13px;
        }

        .help-section ul {
            margin-left: 16px;
            margin-bottom: 8px;
        }

        .help-section li {
            margin-bottom: 4px;
            font-size: 13px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                margin: 0;
                border-radius: 0;
            }

            .page-header {
                padding: 16px;
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .content-area {
                padding: 16px;
            }

            .search-form {
                grid-template-columns: 1fr;
            }

            .action-bar {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }

            .table-wrapper {
                font-size: 12px;
            }

            .data-table th,
            .data-table td {
                padding: 8px 4px;
            }

            .help-content {
                width: 90%;
                margin: 20px;
                max-height: 80vh;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="page-title">
                评标结果公示管理
                <span class="help-icon" onclick="showHelp()">?</span>
            </div>

        </div>

        <!-- 页签导航 -->
        <div class="tab-container">
            <div class="tab-nav">
                <div class="tab-item active" onclick="switchTab('pending')">
                    待办
                </div>
                <div class="tab-item" onclick="switchTab('completed')">
                    已办
                </div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 待办页签 -->
            <div id="pending-tab" class="tab-content active">
                <!-- 查询区域 -->
                <div class="search-section">
                    <div class="search-form">
                        <div class="form-group">
                            <label class="form-label">评标结果公示标题</label>
                            <input type="text" class="form-control" placeholder="请输入评标结果公示标题">
                        </div>
                        <div class="form-group">
                            <label class="form-label">审核状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="pending">待审核</option>
                                <option value="processing">审核中</option>
                                <option value="approved">已通过</option>
                                <option value="rejected">未通过</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">发布状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="pending">待发布</option>
                                <option value="published">已发布</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购方式</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="public">公开招标</option>
                                <option value="invite">邀请招标</option>
                                <option value="inquiry">询价采购</option>
                                <option value="single">单一来源</option>
                            </select>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary" onclick="searchData()">查询</button>
                            <button type="button" class="btn btn-secondary" onclick="resetSearch()">重置</button>
                            <button type="button" class="btn btn-outline" onclick="toggleAdvanced()">高级查询</button>
                        </div>
                    </div>
                    
                    <!-- 高级查询 -->
                    <div class="advanced-search" id="advanced-search" style="display: none;">
                        <div class="search-form">
                            <div class="form-group">
                                <label class="form-label">采购类型</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="goods">货物采购</option>
                                    <option value="service">服务采购</option>
                                    <option value="engineering">工程采购</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">采购组织方式</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="centralized">集中采购</option>
                                    <option value="decentralized">分散采购</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">资金来源</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="fiscal">财政资金</option>
                                    <option value="self">自有资金</option>
                                    <option value="loan">贷款资金</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">招标金额</label>
                                <input type="text" class="form-control" placeholder="请输入金额范围">
                            </div>
                            <div class="form-group">
                                <label class="form-label">申请人</label>
                                <input type="text" class="form-control" placeholder="请输入申请人">
                            </div>
                            <div class="form-group">
                                <label class="form-label">招标时间</label>
                                <input type="date" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="form-label">计划时间</label>
                                <input type="date" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="form-label">创建时间</label>
                                <input type="date" class="form-control">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能操作栏 -->
                <div class="action-bar">
                    <div class="action-buttons">
                        <button class="btn btn-success" onclick="createAnnouncement()">新建公告</button>
                        <button class="btn btn-primary" onclick="batchApprove()">批量审批</button>
                        <button class="btn btn-danger" onclick="batchDelete()">批量删除</button>
                    </div>
                </div>

                <!-- 数据列表 -->
                <div class="table-container">
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th class="frozen-column checkbox-col">
                                        <input type="checkbox" onclick="toggleSelectAll(this)">
                                    </th>
                                    <th class="frozen-column name-col">评标结果公示标题</th>
                                    <th style="width: 100px;">审核状态</th>
                                    <th style="width: 100px;">发布状态</th>
                                    <th style="width: 120px;">采购方式</th>
                                    <th style="width: 100px;">采购类型</th>
                                    <th style="width: 150px;">关联标段</th>
                                    <th style="width: 150px;">关联项目</th>
                                    <th style="width: 120px;">创建时间</th>
                                    <th class="frozen-column action-col">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('EVA-2024-001')">办公设备采购项目评标结果公示</a>
                                    </td>
                                    <td><span class="status-tag status-pending">待审核</span></td>
                                    <td><span class="status-tag status-pending">待发布</span></td>
                                    <td>公开招标</td>
                                    <td>货物采购</td>
                                    <td>办公桌椅采购标段</td>
                                    <td>办公设备采购项目</td>
                                    <td>2024-03-15</td>
                                    <td class="frozen-column action-col">
                                        <button class="action-btn btn-submit" onclick="submitAnnouncement('EVA-2024-001')">提交</button>
                                        <button class="action-btn btn-edit" onclick="editAnnouncement('EVA-2024-001')">编辑</button>
                                        <button class="action-btn btn-delete" onclick="deleteAnnouncement('EVA-2024-001')">删除</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('EVA-2024-002')">网络设备采购项目评标结果公示</a>
                                    </td>
                                    <td><span class="status-tag status-processing">审核中</span></td>
                                    <td><span class="status-tag status-pending">待发布</span></td>
                                    <td>邀请招标</td>
                                    <td>货物采购</td>
                                    <td>网络设备采购标段</td>
                                    <td>IT系统建设项目</td>
                                    <td>2024-03-18</td>
                                    <td class="frozen-column action-col">
                                        <button class="action-btn btn-approve" onclick="approveAnnouncement('EVA-2024-002')">审核</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('EVA-2024-003')">软件开发服务项目评标结果公示</a>
                                    </td>
                                    <td><span class="status-tag status-approved">已通过</span></td>
                                    <td><span class="status-tag status-pending">待发布</span></td>
                                    <td>公开招标</td>
                                    <td>服务采购</td>
                                    <td>软件开发标段</td>
                                    <td>IT系统建设项目</td>
                                    <td>2024-03-20</td>
                                    <td class="frozen-column action-col">
                                        <button class="action-btn btn-publish" onclick="publishAnnouncement('EVA-2024-003')">发布</button>
                                        <button class="action-btn btn-edit" onclick="editAnnouncement('EVA-2024-003')">编辑</button>
                                        <button class="action-btn btn-delete" onclick="deleteAnnouncement('EVA-2024-003')">删除</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('EVA-2024-004')">清洁服务采购项目评标结果公示</a>
                                    </td>
                                    <td><span class="status-tag status-rejected">未通过</span></td>
                                    <td><span class="status-tag status-pending">待发布</span></td>
                                    <td>询价采购</td>
                                    <td>服务采购</td>
                                    <td>清洁服务标段</td>
                                    <td>后勤服务项目</td>
                                    <td>2024-03-22</td>
                                    <td class="frozen-column action-col">
                                        <button class="action-btn btn-submit" onclick="submitAnnouncement('EVA-2024-004')">提交</button>
                                        <button class="action-btn btn-edit" onclick="editAnnouncement('EVA-2024-004')">编辑</button>
                                        <button class="action-btn btn-delete" onclick="deleteAnnouncement('EVA-2024-004')">删除</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('EVA-2024-005')">建筑工程项目评标结果公示</a>
                                    </td>
                                    <td><span class="status-tag status-approved">已通过</span></td>
                                    <td><span class="status-tag status-published">已发布</span></td>
                                    <td>公开招标</td>
                                    <td>工程采购</td>
                                    <td>主体建筑标段</td>
                                    <td>办公楼建设项目</td>
                                    <td>2024-03-25</td>
                                    <td class="frozen-column action-col">
                                        <button class="action-btn btn-change" onclick="changeAnnouncement('EVA-2024-005')">变更</button>
                                        <button class="action-btn btn-edit" onclick="editAnnouncement('EVA-2024-005')">编辑</button>
                                        <button class="action-btn btn-delete" onclick="deleteAnnouncement('EVA-2024-005')">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="pagination">
                        <div class="pagination-info">
                            共 5 条记录，当前第 1 页
                        </div>
                        <div class="pagination-controls">
                            <button class="page-btn" onclick="goToPage(0)" disabled>上一页</button>
                            <button class="page-btn active" onclick="goToPage(1)">1</button>
                            <button class="page-btn" onclick="goToPage(2)" disabled>下一页</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 已办页签 -->
            <div id="completed-tab" class="tab-content">
                <!-- 查询区域 -->
                <div class="search-section">
                    <div class="search-form">
                        <div class="form-group">
                            <label class="form-label">评标结果公示标题</label>
                            <input type="text" class="form-control" placeholder="请输入评标结果公示标题">
                        </div>
                        <div class="form-group">
                            <label class="form-label">审核状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="approved">已通过</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">发布状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="pending">待发布</option>
                                <option value="published">已发布</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购方式</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="public">公开招标</option>
                                <option value="invite">邀请招标</option>
                                <option value="inquiry">询价采购</option>
                                <option value="single">单一来源</option>
                            </select>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary" onclick="searchData()">查询</button>
                            <button type="button" class="btn btn-secondary" onclick="resetSearch()">重置</button>
                            <button type="button" class="btn btn-outline" onclick="toggleAdvancedCompleted()">高级查询</button>
                        </div>
                    </div>
                    
                    <!-- 高级查询 -->
                    <div class="advanced-search" id="advanced-search-completed" style="display: none;">
                        <div class="search-form">
                            <div class="form-group">
                                <label class="form-label">采购类型</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="goods">货物采购</option>
                                    <option value="service">服务采购</option>
                                    <option value="engineering">工程采购</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">采购组织方式</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="centralized">集中采购</option>
                                    <option value="decentralized">分散采购</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">资金来源</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="fiscal">财政资金</option>
                                    <option value="self">自有资金</option>
                                    <option value="loan">贷款资金</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">招标金额</label>
                                <input type="text" class="form-control" placeholder="请输入金额范围">
                            </div>
                            <div class="form-group">
                                <label class="form-label">申请人</label>
                                <input type="text" class="form-control" placeholder="请输入申请人">
                            </div>
                            <div class="form-group">
                                <label class="form-label">招标时间</label>
                                <input type="date" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="form-label">计划时间</label>
                                <input type="date" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="form-label">创建时间</label>
                                <input type="date" class="form-control">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能操作栏 -->
                <div class="action-bar">
                    <div class="action-buttons">
                        <!-- 已办页签无批量操作 -->
                    </div>
                </div>

                <!-- 数据列表 -->
                <div class="table-container">
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th class="frozen-column checkbox-col">
                                        <input type="checkbox" onclick="toggleAllCompleted(this)">
                                    </th>
                                    <th class="frozen-column name-col">评标结果公示标题</th>
                                    <th style="width: 100px;">审核状态</th>
                                    <th style="width: 100px;">发布状态</th>
                                    <th style="width: 120px;">采购方式</th>
                                    <th style="width: 100px;">采购类型</th>
                                    <th style="width: 150px;">关联标段</th>
                                    <th style="width: 150px;">关联项目</th>
                                    <th style="width: 120px;">创建时间</th>
                                    <th class="frozen-column action-col">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('EVA-2024-006')">医疗设备采购项目评标结果公示</a>
                                    </td>
                                    <td><span class="status-tag status-approved">已通过</span></td>
                                    <td><span class="status-tag status-published">已发布</span></td>
                                    <td>公开招标</td>
                                    <td>货物采购</td>
                                    <td>医疗设备采购标段</td>
                                    <td>医院设备更新项目</td>
                                    <td>2024-02-15</td>
                                    <td class="frozen-column action-col">
                                        <!-- 已办页签无删除操作 -->
                                    </td>
                                </tr>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('EVA-2024-007')">教学设备采购项目评标结果公示</a>
                                    </td>
                                    <td><span class="status-tag status-approved">已通过</span></td>
                                    <td><span class="status-tag status-published">已发布</span></td>
                                    <td>邀请招标</td>
                                    <td>货物采购</td>
                                    <td>教学设备采购标段</td>
                                    <td>教育信息化项目</td>
                                    <td>2024-02-20</td>
                                    <td class="frozen-column action-col">
                                        <!-- 已办页签无删除操作 -->
                                    </td>
                                </tr>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('EVA-2024-008')">安保服务项目评标结果公示</a>
                                    </td>
                                    <td><span class="status-tag status-approved">已通过</span></td>
                                    <td><span class="status-tag status-published">已发布</span></td>
                                    <td>公开招标</td>
                                    <td>服务采购</td>
                                    <td>安保服务标段</td>
                                    <td>综合安保项目</td>
                                    <td>2024-02-25</td>
                                    <td class="frozen-column action-col">
                                        <!-- 已办页签无删除操作 -->
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="pagination">
                        <div class="pagination-info">
                            共 3 条记录，当前第 1 页
                        </div>
                        <div class="pagination-controls">
                            <button class="page-btn" onclick="goToPage(0)" disabled>上一页</button>
                            <button class="page-btn active" onclick="goToPage(1)">1</button>
                            <button class="page-btn" onclick="goToPage(2)" disabled>下一页</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div class="help-modal" id="helpModal">
        <div class="help-content">
            <div class="help-header">
                <h3 class="help-title">评标结果公示管理功能说明</h3>
                <button class="close-btn" onclick="hideHelp()">×</button>
            </div>
            <div class="help-body">
                <div class="help-section">
                    <h4>功能概述</h4>
                    <p>评标结果公示管理模块用于管理招标项目的评标结果公示信息，包括公示内容的创建、审核、发布和维护等功能。</p>
                </div>
                
                <div class="help-section">
                    <h4>主要功能</h4>
                    <ul>
                        <li><strong>新建公告：</strong>创建新的评标结果公示</li>
                        <li><strong>审核管理：</strong>对公示内容进行审核</li>
                        <li><strong>发布管理：</strong>控制公示的发布状态</li>
                        <li><strong>批量操作：</strong>支持批量审核和删除</li>
                        <li><strong>查询筛选：</strong>支持多条件查询和高级筛选</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>状态说明</h4>
                    <ul>
                        <li><strong>待审核：</strong>公示已创建，等待审核</li>
                        <li><strong>审核中：</strong>公示正在审核过程中</li>
                        <li><strong>已通过：</strong>公示审核通过，可以发布</li>
                        <li><strong>未通过：</strong>公示审核未通过，需要修改</li>
                        <li><strong>已发布：</strong>公示已对外发布</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>操作说明</h4>
                    <ul>
                        <li><strong>待办页签：</strong>显示需要处理的公示</li>
                        <li><strong>已办页签：</strong>显示已完成处理的公示</li>
                        <li><strong>点击标题：</strong>可直接查看公示详情</li>
                        <li><strong>高级查询：</strong>支持更多筛选条件</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 高级查询切换
        function toggleAdvanced() {
            const advancedSearch = document.getElementById('advanced-search');
            const toggle = document.querySelector('#pending-tab .btn-outline[onclick="toggleAdvanced()"]');
            
            if (advancedSearch.style.display === 'none' || advancedSearch.style.display === '') {
                advancedSearch.style.display = 'block';
                if (toggle) toggle.textContent = '收起查询';
            } else {
                advancedSearch.style.display = 'none';
                if (toggle) toggle.textContent = '高级查询';
            }
        }

        // 已办页签高级查询展开/收起
        function toggleAdvancedCompleted() {
            const advancedSearch = document.getElementById('advanced-search-completed');
            const toggle = document.querySelector('#completed-tab .btn-outline[onclick="toggleAdvancedCompleted()"]');
            
            if (advancedSearch.style.display === 'none' || advancedSearch.style.display === '') {
                advancedSearch.style.display = 'block';
                if (toggle) toggle.textContent = '收起查询';
            } else {
                advancedSearch.style.display = 'none';
                if (toggle) toggle.textContent = '高级查询';
            }
        }

        // 页签切换
        function switchTab(tabName) {
            // 隐藏所有页签内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有页签的激活状态
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示对应的页签内容
            const targetTab = document.getElementById(tabName + '-tab');
            if (targetTab) {
                targetTab.classList.add('active');
            }
            
            // 激活对应的页签
            const activeTabItem = document.querySelector(`[onclick="switchTab('${tabName}')"]`);
            if (activeTabItem) {
                activeTabItem.classList.add('active');
            }
        }

        // 全选功能
        function toggleSelectAll(checkbox) {
            const checkboxes = document.querySelectorAll('#pending-tab input[type="checkbox"]:not(.select-all)');
            checkboxes.forEach(cb => {
                cb.checked = checkbox.checked;
            });
        }

        // 已办页签全选功能
        function toggleAllCompleted(checkbox) {
            const checkboxes = document.querySelectorAll('#completed-tab input[type="checkbox"]:not(.select-all)');
            checkboxes.forEach(cb => {
                cb.checked = checkbox.checked;
            });
        }

        // 显示帮助
        function showHelp() {
            document.getElementById('helpModal').style.display = 'block';
        }

        // 隐藏帮助
        function hideHelp() {
            document.getElementById('helpModal').style.display = 'none';
        }

        // 查询数据
        function searchData() {
            console.log('执行查询操作');
            // 这里应该调用查询接口
        }

        // 重置查询
        function resetSearch() {
            const inputs = document.querySelectorAll('.form-input, .form-select');
            inputs.forEach(input => {
                if (input.type === 'checkbox') {
                    input.checked = false;
                } else {
                    input.value = '';
                }
            });
            console.log('重置查询条件');
        }

        function createAnnouncement() {
            window.location.href = '评标结果公示管理-新建编辑页.html';
        }

        function viewDetail(id) {
            window.location.href = `评标结果公示管理-详情页.html?id=${id}`;
        }

        function editAnnouncement(id) {
            window.location.href = `评标结果公示管理-新建编辑页.html?id=${id}`;
        }

        function submitAnnouncement(id) {
            if (confirm('确定要提交这个公告吗？')) {
                console.log('提交公告ID:', id);
                // 这里应该调用提交接口
                alert('提交成功！');
            }
        }

        function approveAnnouncement(id) {
            window.location.href = `评标结果公示管理-审核页.html?id=${id}`;
        }

        function publishAnnouncement(id) {
            if (confirm('确定要发布这个公告吗？')) {
                console.log('发布公告ID:', id);
                // 这里应该调用发布接口
                alert('发布成功！');
            }
        }

        function deleteAnnouncement(id) {
            if (confirm('确定要删除这个公告吗？删除后无法恢复。')) {
                console.log('删除公告ID:', id);
                // 这里应该调用删除接口
                alert('删除成功！');
            }
        }

        function batchApprove() {
            const checkedBoxes = document.querySelectorAll('#pending-tab input[type="checkbox"]:checked:not(.select-all)');
            if (checkedBoxes.length === 0) {
                alert('请选择要审批的公告！');
                return;
            }
            
            if (confirm(`确定要批量审批选中的 ${checkedBoxes.length} 个公告吗？`)) {
                console.log('批量审批');
                // 这里应该调用批量审批接口
                alert('批量审批成功！');
            }
        }

        function batchDelete() {
            const checkedBoxes = document.querySelectorAll('#pending-tab input[type="checkbox"]:checked:not(.select-all)');
            if (checkedBoxes.length === 0) {
                alert('请选择要删除的公告！');
                return;
            }
            
            if (confirm(`确定要批量删除选中的 ${checkedBoxes.length} 个公告吗？删除后无法恢复。`)) {
                console.log('批量删除');
                // 这里应该调用批量删除接口
                alert('批量删除成功！');
            }
        }

        function getSelectedIds() {
            const checkedBoxes = document.querySelectorAll('input[type="checkbox"]:checked:not(.select-all)');
            const ids = [];
            checkedBoxes.forEach(checkbox => {
                const row = checkbox.closest('tr');
                if (row) {
                    const link = row.querySelector('.table-link');
                    if (link) {
                        const onclick = link.getAttribute('onclick');
                        const match = onclick.match(/'([^']+)'/);
                        if (match) {
                            ids.push(match[1]);
                        }
                    }
                }
            });
            return ids;
        }

        function goToPage(page) {
            console.log('跳转到第', page, '页');
            // 这里应该调用分页接口
        }

        function changePageSize(size) {
            console.log('改变每页显示数量:', size);
            // 这里应该重新加载数据
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('评标结果公示管理页面加载完成');
            // 默认显示待办页签
            switchTab('pending');
        });
    </script>
</body>
</html>