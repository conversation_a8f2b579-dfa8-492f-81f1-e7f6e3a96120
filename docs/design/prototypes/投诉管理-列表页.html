<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>投诉管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* 页面标题区域 */
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .page-title {
            font-size: 18px;
            font-weight: bold;
            color: #1c4e80;
        }
        
        .help-icon {
            width: 16px;
            height: 16px;
            background: #1890ff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
            position: relative;
        }
        
        .help-tooltip {
            position: absolute;
            top: 25px;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
            display: none;
        }
        
        .help-tooltip::before {
            content: '';
            position: absolute;
            top: -5px;
            left: 50%;
            transform: translateX(-50%);
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-bottom: 5px solid #333;
        }
        
        .help-icon:hover .help-tooltip {
            display: block;
        }
        
        /* 查询区域 */
        .search-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .search-form {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: nowrap;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            font-size: 13px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .form-group input,
        .form-group select {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 13px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .search-buttons {
            display: flex;
            gap: 10px;
            align-items: end;
            margin-left: auto;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
        }
        
        .btn-default {
            background: #f5f5f5;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .btn-default:hover {
            background: #e6f7ff;
            border-color: #1890ff;
            color: #1890ff;
        }
        
        /* 高级查询 */
        .advanced-search {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #f0f0f0;
            display: none;
        }
        
        .advanced-search.show {
            display: block;
        }
        
        /* 日期范围选择器 */
        .date-range {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .date-separator {
            color: #666;
            font-size: 13px;
        }
        
        .btn-link {
            background: none;
            color: #5cadff;
            padding: 8px 0;
        }
        
        .btn-link:hover {
            color: #4a9eff;
            text-decoration: underline;
        }
        
        /* 功能操作栏 */
        .action-bar {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
        }
        
        .btn-success {
            background: #52c41a;
            color: white;
        }
        
        .btn-success:hover {
            background: #73d13d;
        }
        
        .btn-danger {
            background: #ff4d4f;
            color: white;
        }
        
        .btn-danger:hover {
            background: #ff7875;
        }
        
        .btn:disabled {
            background: #f5f5f5;
            color: #bfbfbf;
            cursor: not-allowed;
        }
        
        /* 数据表格 */
        .table-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .table-container {
            overflow-x: auto;
            max-width: 100%;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
            table-layout: fixed;
            min-width: 1200px;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
            word-wrap: break-word;
        }
        
        .data-table th {
            background: #fafafa;
            font-weight: 600;
            color: #262626;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        /* 冻结列样式 */
        .data-table th:first-child,
        .data-table td:first-child {
            position: sticky;
            left: 0;
            background: white;
            z-index: 11;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            width: 50px;
        }
        
        .data-table th:first-child {
            background: #fafafa;
        }
        
        .data-table th:last-child,
        .data-table td:last-child {
            position: sticky;
            right: 0;
            background: white;
            z-index: 11;
            box-shadow: -2px 0 5px rgba(0,0,0,0.1);
            width: 150px;
        }
        
        .data-table th:last-child {
            background: #fafafa;
        }
        
        /* 列宽设置 */
        .col-checkbox { width: 50px; }
        .col-source { width: 120px; }
        .col-segment { width: 200px; }
        .col-company { width: 180px; }
        .col-handled { width: 100px; }
        .col-time { width: 150px; }
        .col-create-time { width: 150px; }
        .col-actions { width: 150px; }
        
        .data-table tbody tr:hover {
            background-color: #f5f5f5;
        }
        
        .checkbox {
            width: 16px;
            height: 16px;
        }
        
        /* 状态标签 */
        .status-tag {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-handled {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .status-unhandled {
            background: #fff2e8;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }
        
        /* 操作按钮 */
        .action-btn {
            padding: 4px 8px;
            margin: 0 2px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }
        
        .btn-edit {
            background: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }
        
        .btn-edit:hover {
            background: #bae7ff;
        }
        
        .btn-process {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .btn-process:hover {
            background: #d9f7be;
        }
        
        .btn-delete {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
        
        .btn-delete:hover {
            background: #ffebe9;
        }
        
        /* 分页 */
        .pagination {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #f0f0f0;
        }
        
        .pagination-info {
            color: #666;
            font-size: 13px;
        }
        
        .pagination-controls {
            display: flex;
            gap: 5px;
            align-items: center;
        }
        
        .pagination-btn {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            font-size: 13px;
        }
        
        .pagination-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .pagination-btn.active {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        
        .pagination-btn:disabled {
            background: #f5f5f5;
            color: #bfbfbf;
            cursor: not-allowed;
        }
        
        /* 链接样式 */
        .link {
            color: #1890ff;
            cursor: pointer;
            text-decoration: none;
        }
        
        .link:hover {
            color: #40a9ff;
            text-decoration: underline;
        }
        
        /* 响应式 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .search-form {
                grid-template-columns: 1fr;
            }
            
            .action-bar {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">投诉管理</h1>
            <div class="help-icon">
                ?
                <div class="help-tooltip">
                    支持对投诉信息进行新建、编辑、删除、查询、查看详情等业务操作。
                </div>
            </div>
        </div>
        
        <!-- 查询区域 -->
        <div class="search-section">
            <div class="search-form">
                <div class="form-group">
                    <label for="complaintSource">投诉来源</label>
                    <select id="complaintSource" name="complaintSource" class="form-control">
                        <option value="">请选择</option>
                        <option value="书面异议">书面异议</option>
                        <option value="书面投诉">书面投诉</option>
                        <option value="网络理政">网络理政</option>
                        <option value="纪委转办">纪委转办</option>
                        <option value="其他">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="complaintSegment">投诉标段</label>
                    <input type="text" id="complaintSegment" name="complaintSegment" class="form-control" placeholder="请输入标段名称">
                </div>
                <div class="form-group">
                    <label for="complaintCompany">投诉企业</label>
                    <input type="text" id="complaintCompany" name="complaintCompany" class="form-control" placeholder="请输入企业名称">
                </div>
                <div class="form-group">
                    <label for="isHandled">是否处理</label>
                    <select id="isHandled" name="isHandled" class="form-control">
                        <option value="">请选择</option>
                        <option value="是">是</option>
                        <option value="否">否</option>
                    </select>
                </div>
                <div class="search-buttons">
                    <button type="button" class="btn btn-primary" onclick="searchData()">查询</button>
                    <button type="button" class="btn btn-default" onclick="resetSearch()">重置</button>
                    <button type="button" class="btn btn-link" onclick="toggleAdvancedSearch()">高级查询</button>
                </div>
            </div>
            
            <!-- 高级查询 -->
            <div class="advanced-search" id="advancedSearch">
                <div class="search-form">
                    <div class="form-group">
                        <label for="complaintTimeStart">投诉时间</label>
                        <div class="date-range">
                            <input type="date" id="complaintTimeStart" name="complaintTimeStart" class="form-control">
                            <span class="date-separator">至</span>
                            <input type="date" id="complaintTimeEnd" name="complaintTimeEnd" class="form-control">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="createTimeStart">创建时间</label>
                        <div class="date-range">
                            <input type="date" id="createTimeStart" name="createTimeStart" class="form-control">
                            <span class="date-separator">至</span>
                            <input type="date" id="createTimeEnd" name="createTimeEnd" class="form-control">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 功能操作栏 -->
        <div class="action-bar">
            <div class="action-buttons">
                <button class="btn btn-success" onclick="createComplaint()">新建投诉</button>
                <button class="btn btn-danger" id="batchDeleteBtn" onclick="batchDelete()" disabled>批量删除</button>
            </div>
            <div class="pagination-info">
                共 <span id="totalCount">0</span> 条记录
            </div>
        </div>
        
        <!-- 数据表格 -->
        <div class="table-section">
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th class="col-checkbox">
                                <input type="checkbox" class="checkbox" id="selectAll" onchange="toggleSelectAll()">
                            </th>
                            <th class="col-source">投诉来源</th>
                            <th class="col-segment">投诉标段</th>
                            <th class="col-company">投诉企业</th>
                            <th class="col-handled">是否处理</th>
                            <th class="col-time">投诉时间</th>
                            <th class="col-create-time">创建时间</th>
                            <th class="col-actions">操作</th>
                        </tr>
                    </thead>
                    <tbody id="dataTableBody">
                        <!-- 示例数据 -->
                        <tr>
                            <td><input type="checkbox" class="checkbox row-checkbox" value="1"></td>
                            <td>书面异议</td>
                            <td><a href="javascript:void(0)" class="link" onclick="viewDetail(1)">XX项目第一标段</a></td>
                            <td>XX建设集团有限公司</td>
                            <td><span class="status-tag status-unhandled">否</span></td>
                            <td>2024-01-15</td>
                            <td>2024-01-15 10:30:25</td>
                            <td>
                                <button class="action-btn btn-edit" onclick="editComplaint(1)">编辑</button>
                                <button class="action-btn btn-process" onclick="processComplaint(1)">处理</button>
                                <button class="action-btn btn-delete" onclick="deleteComplaint(1)">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="checkbox row-checkbox" value="2"></td>
                            <td>网络理政</td>
                            <td><a href="javascript:void(0)" class="link" onclick="viewDetail(2)">YY工程第二标段</a></td>
                            <td>YY工程有限责任公司</td>
                            <td><span class="status-tag status-handled">是</span></td>
                            <td>2024-01-14</td>
                            <td>2024-01-14 14:20:15</td>
                            <td>
                                <button class="action-btn btn-delete" onclick="deleteComplaint(2)">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="checkbox row-checkbox" value="3"></td>
                            <td>书面投诉</td>
                            <td><a href="javascript:void(0)" class="link" onclick="viewDetail(3)">ZZ项目第三标段</a></td>
                            <td>ZZ建筑工程有限公司</td>
                            <td><span class="status-tag status-unhandled">否</span></td>
                            <td>2024-01-13</td>
                            <td>2024-01-13 09:15:30</td>
                            <td>
                                <button class="action-btn btn-edit" onclick="editComplaint(3)">编辑</button>
                                <button class="action-btn btn-process" onclick="processComplaint(3)">处理</button>
                                <button class="action-btn btn-delete" onclick="deleteComplaint(3)">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="pagination">
                <div class="pagination-info">
                    显示第 1-3 条，共 3 条记录
                </div>
                <div class="pagination-controls">
                    <button class="pagination-btn" disabled>上一页</button>
                    <button class="pagination-btn active">1</button>
                    <button class="pagination-btn" disabled>下一页</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 切换高级查询
        function toggleAdvancedSearch() {
            const advancedSearch = document.getElementById('advancedSearch');
            const toggleBtn = event.target;
            
            if (advancedSearch.classList.contains('show')) {
                advancedSearch.classList.remove('show');
                toggleBtn.textContent = '高级查询';
            } else {
                advancedSearch.classList.add('show');
                toggleBtn.textContent = '收起';
            }
        }
        
        // 查询数据
        function searchData() {
            // 获取查询条件
            const formData = new FormData();
            const form = document.querySelector('.search-form');
            const inputs = form.querySelectorAll('input, select');
            
            inputs.forEach(input => {
                if (input.value) {
                    formData.append(input.name, input.value);
                }
            });
            
            // 这里应该调用后端API进行查询
            console.log('查询条件:', Object.fromEntries(formData));
            alert('查询功能待实现');
        }
        
        // 重置查询
        function resetSearch() {
            const form = document.querySelector('.search-form');
            const advancedForm = document.querySelector('.advanced-search .search-form');
            
            form.reset();
            if (advancedForm) {
                advancedForm.reset();
            }
        }
        
        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.row-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
            
            updateBatchButtons();
        }
        
        // 更新批量操作按钮状态
        function updateBatchButtons() {
            const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
            const batchDeleteBtn = document.getElementById('batchDeleteBtn');
            
            batchDeleteBtn.disabled = checkedBoxes.length === 0;
        }
        
        // 监听单个复选框变化
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('row-checkbox')) {
                updateBatchButtons();
                
                // 更新全选状态
                const allCheckboxes = document.querySelectorAll('.row-checkbox');
                const checkedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
                const selectAll = document.getElementById('selectAll');
                
                selectAll.checked = allCheckboxes.length === checkedCheckboxes.length;
                selectAll.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < allCheckboxes.length;
            }
        });
        
        // 页签跳转函数
        function openPage(url, title) {
            try {
                // 优先使用 postMessage 方式
                window.parent.postMessage({
                    type: 'openPage',
                    url: url,
                    title: title
                }, '*');
            } catch (e) {
                // 降级方案：直接打开新窗口
                window.open(url, '_blank');
            }
        }
        
        // 新建投诉
        function createComplaint() {
            openPage('投诉管理-新建编辑页.html', '新建投诉');
        }
        
        // 编辑投诉
        function editComplaint(id) {
            openPage('投诉管理-新建编辑页.html?id=' + id, '编辑投诉');
        }
        
        // 查看详情
        function viewDetail(id) {
            openPage('投诉管理-详情页.html?id=' + id, '投诉详情');
        }
        
        // 处理投诉
        function processComplaint(id) {
            if (confirm('确认要处理这条投诉吗？')) {
                // 这里应该调用后端API
                console.log('处理投诉:', id);
                alert('处理成功');
                // 刷新页面数据
                location.reload();
            }
        }
        
        // 删除投诉
        function deleteComplaint(id) {
            if (confirm('确认要删除这条投诉吗？删除后无法恢复。')) {
                // 这里应该调用后端API
                console.log('删除投诉:', id);
                alert('删除成功');
                // 刷新页面数据
                location.reload();
            }
        }
        
        // 批量删除
        function batchDelete() {
            const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
            if (checkedBoxes.length === 0) {
                alert('请选择要删除的记录');
                return;
            }
            
            if (confirm(`确认要删除选中的 ${checkedBoxes.length} 条记录吗？删除后无法恢复。`)) {
                const ids = Array.from(checkedBoxes).map(cb => cb.value);
                // 这里应该调用后端API
                console.log('批量删除:', ids);
                alert('删除成功');
                // 刷新页面数据
                location.reload();
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 更新总记录数
            const totalCount = document.querySelectorAll('.row-checkbox').length;
            document.getElementById('totalCount').textContent = totalCount;
        });
    </script>
</body>
</html>