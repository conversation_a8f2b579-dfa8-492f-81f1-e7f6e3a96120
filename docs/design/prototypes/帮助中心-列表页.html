<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>帮助中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            min-height: 100vh;
        }

        /* 页首区域 */
        .page-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #1890ff;
        }

        .page-title {
            font-size: 18px;
            font-weight: bold;
            color: #1890ff;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .help-icon {
            width: 16px;
            height: 16px;
            cursor: pointer;
            color: #666;
        }

        /* 内容区域 */
        .content-area {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 30px;
        }

        .help-section {
            background: #fff;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f0f0f0;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #1890ff;
            margin-right: 8px;
        }

        .section-icon {
            width: 20px;
            height: 20px;
            margin-right: 8px;
        }

        /* 文件列表 */
        .file-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: #fafafa;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .file-item:hover {
            background: #e6f7ff;
            border-color: #1890ff;
        }

        .file-info {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .file-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            color: #1890ff;
        }

        .file-details {
            flex: 1;
        }

        .file-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }

        .file-meta {
            font-size: 12px;
            color: #666;
        }

        .file-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            background: #fff;
            color: #666;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }

        .action-btn.primary {
            background: #1890ff;
            border-color: #1890ff;
            color: #fff;
        }

        .action-btn.primary:hover {
            background: #40a9ff;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #999;
        }

        .empty-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            opacity: 0.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .content-area {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .file-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }
            
            .file-actions {
                width: 100%;
                justify-content: flex-end;
            }
        }

        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #fff;
            border-radius: 6px;
            padding: 24px;
            max-width: 90%;
            max-height: 90%;
            overflow: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f0f0f0;
        }

        .modal-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #999;
        }

        .close-btn:hover {
            color: #333;
        }

        .preview-content {
            text-align: center;
        }

        .preview-content iframe {
            width: 100%;
            height: 500px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
        }

        .preview-content video {
            width: 100%;
            max-height: 500px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页首区域 -->
        <div class="page-header">
            <div class="page-title">
                <span>帮助中心</span>
                <svg class="help-icon" onclick="showHelp()" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
                </svg>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 操作手册区域 -->
            <div class="help-section">
                <div class="section-header">
                    <svg class="section-icon" viewBox="0 0 24 24" fill="#1890ff">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                    </svg>
                    <span class="section-title">操作手册</span>
                </div>
                <div class="file-list" id="manualList">
                    <div class="file-item">
                        <div class="file-info">
                            <svg class="file-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                            </svg>
                            <div class="file-details">
                                <div class="file-name">招采平台用户操作手册</div>
                                <div class="file-meta">PDF文档 • 2.5MB • 更新时间：2024-01-15</div>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="action-btn" onclick="previewFile('manual', '招采平台用户操作手册.pdf')">预览</button>
                            <button class="action-btn primary" onclick="downloadFile('招采平台用户操作手册.pdf')">下载</button>
                        </div>
                    </div>
                    
                    <div class="file-item">
                        <div class="file-info">
                            <svg class="file-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                            </svg>
                            <div class="file-details">
                                <div class="file-name">系统管理员操作手册</div>
                                <div class="file-meta">PDF文档 • 3.2MB • 更新时间：2024-01-15</div>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="action-btn" onclick="previewFile('manual', '系统管理员操作手册.pdf')">预览</button>
                            <button class="action-btn primary" onclick="downloadFile('系统管理员操作手册.pdf')">下载</button>
                        </div>
                    </div>
                    
                    <div class="file-item">
                        <div class="file-info">
                            <svg class="file-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                            </svg>
                            <div class="file-details">
                                <div class="file-name">供应商操作指南</div>
                                <div class="file-meta">PDF文档 • 1.8MB • 更新时间：2024-01-10</div>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="action-btn" onclick="previewFile('manual', '供应商操作指南.pdf')">预览</button>
                            <button class="action-btn primary" onclick="downloadFile('供应商操作指南.pdf')">下载</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作视频区域 -->
            <div class="help-section">
                <div class="section-header">
                    <svg class="section-icon" viewBox="0 0 24 24" fill="#1890ff">
                        <path d="M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z"/>
                    </svg>
                    <span class="section-title">操作视频</span>
                </div>
                <div class="file-list" id="videoList">
                    <div class="file-item">
                        <div class="file-info">
                            <svg class="file-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z"/>
                            </svg>
                            <div class="file-details">
                                <div class="file-name">系统登录与基础操作</div>
                                <div class="file-meta">MP4视频 • 15.2MB • 时长：05:30 • 更新时间：2024-01-15</div>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="action-btn" onclick="previewFile('video', '系统登录与基础操作.mp4')">预览</button>
                            <button class="action-btn primary" onclick="downloadFile('系统登录与基础操作.mp4')">下载</button>
                        </div>
                    </div>
                    
                    <div class="file-item">
                        <div class="file-info">
                            <svg class="file-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z"/>
                            </svg>
                            <div class="file-details">
                                <div class="file-name">采购计划管理操作演示</div>
                                <div class="file-meta">MP4视频 • 28.5MB • 时长：12:15 • 更新时间：2024-01-12</div>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="action-btn" onclick="previewFile('video', '采购计划管理操作演示.mp4')">预览</button>
                            <button class="action-btn primary" onclick="downloadFile('采购计划管理操作演示.mp4')">下载</button>
                        </div>
                    </div>
                    
                    <div class="file-item">
                        <div class="file-info">
                            <svg class="file-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z"/>
                            </svg>
                            <div class="file-details">
                                <div class="file-name">项目标段管理流程</div>
                                <div class="file-meta">MP4视频 • 35.8MB • 时长：18:45 • 更新时间：2024-01-10</div>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="action-btn" onclick="previewFile('video', '项目标段管理流程.mp4')">预览</button>
                            <button class="action-btn primary" onclick="downloadFile('项目标段管理流程.mp4')">下载</button>
                        </div>
                    </div>
                    
                    <div class="file-item">
                        <div class="file-info">
                            <svg class="file-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z"/>
                            </svg>
                            <div class="file-details">
                                <div class="file-name">供应商注册与认证</div>
                                <div class="file-meta">MP4视频 • 22.1MB • 时长：09:20 • 更新时间：2024-01-08</div>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="action-btn" onclick="previewFile('video', '供应商注册与认证.mp4')">预览</button>
                            <button class="action-btn primary" onclick="downloadFile('供应商注册与认证.mp4')">下载</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 预览弹窗 -->
    <div class="modal" id="previewModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="previewTitle">文件预览</div>
                <button class="close-btn" onclick="closePreview()">&times;</button>
            </div>
            <div class="preview-content" id="previewContent">
                <!-- 预览内容将在这里动态加载 -->
            </div>
        </div>
    </div>

    <script>
        // 显示帮助说明
        function showHelp() {
            alert('帮助中心提供系统操作手册和视频教程，支持在线预览和下载。\n\n操作手册：包含详细的文字说明和截图指导\n操作视频：提供直观的操作演示和讲解');
        }

        // 预览文件
        function previewFile(type, filename) {
            const modal = document.getElementById('previewModal');
            const title = document.getElementById('previewTitle');
            const content = document.getElementById('previewContent');
            
            title.textContent = `预览 - ${filename}`;
            
            if (type === 'manual') {
                // PDF预览
                content.innerHTML = `
                    <iframe src="#" title="${filename}">
                        您的浏览器不支持PDF预览，请下载文件查看。
                    </iframe>
                    <p style="margin-top: 10px; color: #666; font-size: 12px;">注：实际使用时需要配置PDF文件路径</p>
                `;
            } else if (type === 'video') {
                // 视频预览
                content.innerHTML = `
                    <video controls>
                        <source src="#" type="video/mp4">
                        您的浏览器不支持视频播放。
                    </video>
                    <p style="margin-top: 10px; color: #666; font-size: 12px;">注：实际使用时需要配置视频文件路径</p>
                `;
            }
            
            modal.style.display = 'block';
        }

        // 关闭预览
        function closePreview() {
            document.getElementById('previewModal').style.display = 'none';
        }

        // 下载文件
        function downloadFile(filename) {
            // 实际项目中需要配置真实的文件下载路径
            alert(`正在下载：${filename}\n\n注：实际使用时需要配置文件下载接口`);
            
            // 模拟下载过程
            console.log(`下载文件：${filename}`);
            
            // 实际下载代码示例：
            // const link = document.createElement('a');
            // link.href = `/api/download/${filename}`;
            // link.download = filename;
            // document.body.appendChild(link);
            // link.click();
            // document.body.removeChild(link);
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('previewModal');
            if (event.target === modal) {
                closePreview();
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('帮助中心页面加载完成');
        });
    </script>
</body>
</html>