<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>招采平台 - 采购计划管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            margin: 0;
            padding: 0;
        }
        
        /* 主体布局 */
        .main-container {
            height: 100vh;
        }
        
        /* 内容区域 */
        .content-wrapper {
            overflow-y: auto;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        
        /* 页签区域 */
        .tabs {
            display: flex;
            background: white;
            border-radius: 12px 12px 0 0;
            border: 1px solid #f0f0f0;
            border-bottom: none;
            margin-bottom: -1px;
            position: relative;
            z-index: 1;
        }
        
        .tab {
            padding: 16px 24px;
            cursor: pointer;
            border-right: 1px solid #f0f0f0;
            position: relative;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }
        
        .tab.active {
            color: #1890ff;
            background-color: #f8f9fa;
            border-bottom: 2px solid #1890ff;
            margin-bottom: -1px;
        }
        
        .tab-close {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .tab-close:hover {
            background-color: #e6e6e6;
        }
        
        /* 页面内容 */
        .page-content {
            background: white;
            border-radius: 0 12px 12px 12px;
            border: 1px solid #f0f0f0;
            padding: 24px;
        }
        
        /* 页面标题 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        /* 计划页签 */
        /* 主页签样式 */
        .main-tabs {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            margin-bottom: 16px;
        }
        
        .main-tabs .tab-item {
            padding: 12px 24px;
            cursor: pointer;
            font-size: 14px;
            position: relative;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .main-tabs .tab-item.active {
            color: #1890ff;
            font-weight: 500;
        }
        
        .main-tabs .tab-item.active:after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #1890ff;
        }
        
        /* 视图按钮样式 */
        .view-buttons {
            display: flex;
            gap: 8px;
            margin-bottom: 24px;
        }
        
        .view-btn {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            background: white;
            color: #666;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 400;
            transition: all 0.3s;
        }
        
        .view-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .view-btn.active {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }
        
        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #374151;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .help-icon {
            width: 16px;
            height: 16px;
            background: #1890ff;
            border-radius: 50%;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
        }
        
        /* 查询区域 */
        .search-area {
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            margin-bottom: 24px;
        }

        .search-form {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }

        .form-label {
            font-size: 14px;
            color: #374151;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-control {
            padding: 12px 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background-color: #ffffff;
            line-height: 1.5;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
        }

        .form-control:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
            background-color: #ffffff;
        }
        
        /* 下拉框样式 */
        .form-control.select-control {
            background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
            background-repeat: no-repeat;
            background-position: right 8px center;
            background-size: 12px;
            padding-right: 30px;
        }
        
        .form-control.select-control:focus {
            background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%233498db" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
        }
        
        /* 多选框特殊样式 */
        .form-control.select-control[multiple] {
            height: auto;
            min-height: 44px;
            padding: 8px 16px;
            background-image: none;
            padding-right: 16px;
        }
        
        .form-control.select-control[multiple] option {
            padding: 4px 8px;
            margin: 2px 0;
        }
        
        .form-control.select-control[multiple] option:checked {
            background-color: #1890ff;
            color: white;
        }
        
        .btn-group {
            display: flex;
            gap: 12px;
            align-items: end;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .btn-primary {
            background: #1890ff;
            color: white;
            border: 1px solid #1890ff;
        }

        .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }

        .btn-secondary {
            background: #1890ff;
            color: white;
            border: 1px solid #1890ff;
        }

        .btn-secondary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }

        .btn-outline {
            background: white;
            color: #1890ff;
            border: 1px solid #1890ff;
        }

        .btn-outline:hover {
            background: #1890ff;
            color: white;
        }
        
        /* 高级查询 */
        .advanced-search {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #f0f0f0;
        }
        
        /* 功能操作栏 */
        .action-bar {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            background: white;
            padding: 20px 24px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }

        .action-buttons {
            display: flex;
            gap: 12px;
        }

        .btn-success {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .btn-success:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }

        .btn-warning {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .btn-warning:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }
        
        .btn-danger {
            background-color: #ff4d4f;
            color: white;
            border-color: #ff4d4f;
        }
        
        .btn-danger:hover {
            background-color: #ff7875;
            border-color: #ff7875;
        }
        
        /* 表格样式 */
        .table-container {
            overflow-x: auto;
            border: 1px solid #f0f0f0;
            border-radius: 12px;
            max-width: 100%;
            background: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }

        .data-table {
            min-width: 1800px; /* 确保表格有足够宽度支持横向滚动 */
            border-collapse: collapse;
            table-layout: auto; /* 改为auto以支持动态列宽 */
            width: 100%;
        }

        .data-table th, .data-table td {
            padding: 16px 20px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .data-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #374151;
        }

        .data-table tr:hover td {
            background-color: #f0f9ff;
        }
        
        /* 冻结列样式 */
        .data-table .checkbox-column {
            width: 50px;
            text-align: center;
            position: sticky;
            left: 0;
            z-index: 2;
            background-color: white;
        }
        
        .data-table .plan-code-column {
            width: 140px;
            position: sticky;
            left: 50px; /* 紧接在复选框列后面 */
            z-index: 2;
            background-color: white;
            box-shadow: 2px 0 5px rgba(0,0,0,0.05);
        }
        
        .data-table .operation-column {
            width: 180px;
            text-align: center;
            position: sticky;
            right: 0;
            z-index: 2;
            background-color: white;
            box-shadow: -2px 0 5px rgba(0,0,0,0.05);
        }
        
        /* 冻结列表头样式 */
        .data-table th.checkbox-column,
        .data-table th.plan-code-column {
            background-color: #f8f9fa;
            position: sticky;
            z-index: 3;
        }

        .data-table th.checkbox-column {
            left: 0;
        }

        .data-table th.plan-code-column {
            left: 50px;
            box-shadow: 2px 0 5px rgba(0,0,0,0.05);
        }
        
        .data-table th.operation-column {
            background-color: #f8f9fa;
            position: sticky;
            right: 0;
            z-index: 3;
            box-shadow: -2px 0 5px rgba(0,0,0,0.05);
        }

        /* 悬停时冻结列的背景色 */
        .data-table tr:hover td.checkbox-column,
        .data-table tr:hover td.plan-code-column,
        .data-table tr:hover td.operation-column {
            background-color: #f0f9ff;
        }
        
        .table-link {
            color: #1890ff;
            text-decoration: none;
            cursor: pointer;
        }
        
        .table-link:hover {
            text-decoration: underline;
        }
        
        .operation-buttons {
            display: flex;
            justify-content: center;
            gap: 6px;
        }

        .op-btn {
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            border: 1px solid #d9d9d9;
            background-color: #f8f9fa;
            color: #374151;
            transition: all 0.2s;
            font-weight: 500;
        }

        .op-btn:hover {
            color: #1890ff;
            border-color: #1890ff;
            background-color: #e5e7eb;
        }

        .op-btn-primary {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .op-btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }

        .op-btn-danger {
            background-color: #ef4444;
            color: white;
            border-color: #ef4444;
        }

        .op-btn-danger:hover {
            background-color: #f87171;
            border-color: #f87171;
        }

        .op-btn-warning {
            background-color: #f59e0b;
            color: white;
            border-color: #f59e0b;
        }

        .op-btn-warning:hover {
            background-color: #fbbf24;
            border-color: #fbbf24;
        }
        
        /* 状态标签 */
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        /* 计划状态样式 */
        .status-draft {
            background-color: #f3f4f6;
            color: #6b7280;
        }
        
        .status-pending-start {
            background-color: #fef3c7;
            color: #d97706;
        }

        .status-in-progress {
            background-color: #dbeafe;
            color: #2563eb;
        }

        .status-completed {
            background-color: #dcfce7;
            color: #16a34a;
        }

        /* 审核状态样式 */
        .status-pending-review {
            background-color: #fef3c7;
            color: #d97706;
        }

        .status-reviewing {
            background-color: #dbeafe;
            color: #2563eb;
        }

        .status-approved {
            background-color: #dcfce7;
            color: #16a34a;
        }

        .status-rejected {
            background-color: #fee2e2;
            color: #dc2626;
        }

        .status-pending {
            background-color: #dbeafe;
            color: #2563eb;
        }

        .status-normal {
            background-color: #dcfce7;
            color: #16a34a;
        }
        
        /* 导入弹窗样式 */
        .import-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .import-modal.show {
            display: flex;
        }
        
        .import-modal-content {
            background: white;
            border-radius: 12px;
            padding: 32px;
            width: 600px;
            max-width: 90vw;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            position: relative;
        }
        
        .import-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .import-modal-title {
            font-size: 20px;
            font-weight: 600;
            color: #374151;
        }
        
        .import-modal-close {
            width: 32px;
            height: 32px;
            border: none;
            background: #f5f5f5;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #666;
            transition: all 0.3s;
        }
        
        .import-modal-close:hover {
            background: #e5e5e5;
            color: #333;
        }
        
        .import-section {
            margin-bottom: 32px;
        }
        
        .import-section:last-child {
            margin-bottom: 0;
        }
        
        .import-section-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .import-section-desc {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 16px;
            line-height: 1.5;
        }
        
        .template-download-area {
            background: #f8f9fa;
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 24px;
            text-align: center;
        }
        
        .template-icon {
            width: 48px;
            height: 48px;
            background: #1890ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            color: white;
            font-size: 20px;
        }
        
        .file-upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 32px;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .file-upload-area:hover {
            border-color: #1890ff;
            background-color: #f0f9ff;
        }
        
        .file-upload-area.dragover {
            border-color: #1890ff;
            background-color: #e6f7ff;
        }
        
        .upload-icon {
            width: 64px;
            height: 64px;
            background: #e6f7ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            color: #1890ff;
            font-size: 24px;
        }
        
        .upload-text {
            font-size: 16px;
            color: #374151;
            margin-bottom: 8px;
        }
        
        .upload-hint {
            font-size: 14px;
            color: #6b7280;
        }
        
        .file-info {
            background: #f0f9ff;
            border: 1px solid #bae7ff;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            display: none;
        }
        
        .file-info.show {
            display: block;
        }
        
        .file-name {
            font-weight: 500;
            color: #1890ff;
            margin-bottom: 4px;
        }
        
        .file-size {
            font-size: 12px;
            color: #6b7280;
        }
        
        .import-actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #f0f0f0;
        }
        
        .btn-upload {
            background: #1890ff;
            color: white;
            border: 1px solid #1890ff;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            display: none;
        }
        
        .btn-upload.show {
            display: inline-block;
        }
        
        .btn-upload:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }
        
        /* 分页 */
        .pagination {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-top: 24px;
            gap: 8px;
            padding: 16px 0;
        }

        .page-info {
            font-size: 14px;
            color: #6b7280;
        }

        .page-size-selector {
            margin-left: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .page-size-selector select {
            height: 36px;
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            padding: 0 12px;
            background-color: white;
            color: #374151;
            font-size: 14px;
        }

        .page-btn {
            width: 36px;
            height: 36px;
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            background-color: white;
            transition: all 0.2s;
            color: #374151;
            font-weight: 500;
        }

        .page-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
            background-color: #f0f9ff;
        }

        .page-btn.active {
            background-color: #1890ff;
            border-color: #1890ff;
            color: white;
        }
        
        .page-btn.disabled {
            color: #9ca3af;
            cursor: not-allowed;
            background-color: #f9fafb;
        }

        .page-btn.disabled:hover {
            border-color: #d9d9d9;
            color: #9ca3af;
            background-color: #f9fafb;
        }
    </style>
</head>
<body>
    <!-- 主体容器 -->
    <div class="main-container">
        <!-- 内容区域 -->
        <div class="content-wrapper">

            
            <!-- 页面内容 -->
            <div class="page-content">

                
                <!-- 主页签区域 -->
                <div class="main-tabs">
                    <div class="tab-item active" data-tab="todo" onclick="switchMainTab('todo')">
                        待办
                    </div>
                    <div class="tab-item" data-tab="done" onclick="switchMainTab('done')">
                        已办
                    </div>
                    <div class="tab-item" data-tab="all" onclick="switchMainTab('all')">
                        全部
                    </div>
                </div>
                
                <!-- 视图按钮区域 -->
                <div class="view-buttons">
                    <button class="view-btn active" data-view="my-plans" onclick="switchView('my-plans')">
                        我的计划
                    </button>
                    <button class="view-btn" data-view="subordinate-plans" onclick="switchView('subordinate-plans')">
                        下级单位计划
                    </button>
                </div>
                
                <!-- 查询区域 -->
                <div class="search-area">
                    <div class="search-form">
                        <div class="form-group">
                            <label class="form-label">计划项目名称</label>
                            <input type="text" class="form-control" placeholder="请输入计划项目名称（2位以上进行模糊查询）">
                        </div>
                        <div class="form-group">
                            <label class="form-label">审核状态</label>
                            <select class="form-control select-control">
                                <option value="">全部</option>
                                <option value="pending_review">待审核</option>
                                <option value="reviewing">审核中</option>
                                <option value="approved">审核通过</option>
                                <option value="rejected">审核未过</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购类型</label>
                            <select class="form-control select-control">
                                <option value="">全部</option>
                                <option value="goods">货物</option>
                                <option value="construction">施工</option>
                                <option value="service">服务</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购方式</label>
                            <select class="form-control select-control">
                                <option value="">全部</option>
                                <option value="public_bidding">公告比选</option>
                                <option value="invite_bidding">邀请比选</option>
                                <option value="competitive_consultation">竞争性磋商</option>
                                <option value="competitive_negotiation">竞争性谈判</option>
                                <option value="inquiry">询价择优</option>
                                <option value="single_source">单一来源</option>
                            </select>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-primary">查询</button>
                            <button class="btn btn-secondary">重置</button>
                            <button class="btn btn-outline" onclick="toggleAdvancedSearch()">高级查询</button>
                        </div>
                    </div>
                    
                    <div class="advanced-search" id="advanced-search" style="display: none;">
                        <div class="search-form">

                            <div class="form-group">
                                <label class="form-label">采购组织方式</label>
                                <select class="form-control select-control">
                                    <option value="">全部</option>
                                    <option value="self">自主招标</option>
                                    <option value="delegate">委托招标</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">资金来源</label>
                                <select class="form-control select-control">
                                    <option value="">全部</option>
                                    <option value="own">自有资金</option>
                                    <option value="government">政府资本</option>
                                    <option value="social">其他社会资本</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">采购预算金额（万元）</label>
                                <div style="display: flex; gap: 8px; align-items: center;">
                                    <input type="number" class="form-control" placeholder="最小金额" step="0.01">
                                    <span>-</span>
                                    <input type="number" class="form-control" placeholder="最大金额" step="0.01">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">项目经办人</label>
                                <input type="text" class="form-control" placeholder="请输入项目经办人（2位以上进行模糊查询）">
                            </div>
                            <div class="form-group">
                                <label class="form-label">立项决策日期</label>
                                <div style="display: flex; gap: 8px; align-items: center;">
                                    <input type="date" class="form-control" placeholder="开始日期">
                                    <span>-</span>
                                    <input type="date" class="form-control" placeholder="结束日期">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">创建时间</label>
                                <div style="display: flex; gap: 8px; align-items: center;">
                                    <input type="date" class="form-control">
                                    <span>-</span>
                                    <input type="date" class="form-control">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 功能操作栏 -->
                <div class="action-bar">
                    <div class="action-buttons">
                        <button class="btn btn-success">新建采购计划</button>
                        <button class="btn btn-primary" onclick="importData()">导入</button>
                        <button class="btn btn-default">导出</button>
                    </div>
                </div>
                
                <!-- 数据表格 -->
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th class="checkbox-column">
                                    <input type="checkbox">
                                </th>
                                <th class="plan-code-column">计划编号</th>
                                <th style="min-width: 180px;">计划项目名称</th>
                                <th style="min-width: 100px;">采购类型</th>
                                <th style="min-width: 120px;">采购方式</th>
                                <th style="min-width: 100px;">审核状态</th>
                                <th style="min-width: 120px;">采购组织方式</th>
                                <th style="min-width: 120px;">代理机构</th>
                                <th style="min-width: 100px;">项目经办人</th>
                                <th style="min-width: 120px;">项目业主</th>
                                <th style="min-width: 120px;">招标时间</th>
                                <th style="min-width: 150px;">创建时间</th>
                                <th class="operation-column">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 待审核状态 -->
                            <tr data-status="pending_review" data-tab="todo">
                                <td class="checkbox-column">
                                    <input type="checkbox">
                                </td>
                                <td class="plan-code-column">
                                    <a href="#" class="table-link" onclick="viewDetail('CG-20240115-0001')">CG-20240115-0001</a>
                                </td>
                                <td>办公设备采购计划</td>
                                <td>货物</td>
                                <td>公告比选</td>
                                <td>
                                    <span class="status-badge status-pending-review">待审核</span>
                                </td>
                                <td>自主招标</td>
                                <td>无</td>
                                <td>李四</td>
                                <td>行政部</td>
                                <td>2024年第1季度</td>
                                <td>2024-01-15 09:30:25</td>
                                <td class="operation-column">
                                    <div class="operation-buttons">
                                        <button class="op-btn" onclick="editPlan('CG-20240115-0001')">编辑</button>
                                        <button class="op-btn op-btn-danger" onclick="deletePlan('CG-20240115-0001')">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <!-- 审核中状态 -->
                            <tr data-status="reviewing" data-tab="todo">
                                <td class="checkbox-column">
                                    <input type="checkbox">
                                </td>
                                <td class="plan-code-column">
                                    <a href="#" class="table-link" onclick="viewDetail('CG-20240114-0002')">CG-20240114-0002</a>
                                </td>
                                <td>建筑材料采购计划</td>
                                <td>施工</td>
                                <td>邀请比选</td>
                                <td>
                                    <span class="status-badge status-reviewing">审核中</span>
                                </td>
                                <td>委托招标</td>
                                <td>某建筑招标代理公司</td>
                                <td>张三</td>
                                <td>工程部</td>
                                <td>2024年第1季度</td>
                                <td>2024-01-14 16:20:15</td>
                                <td class="operation-column">
                                    <div class="operation-buttons">
                                        <button class="op-btn op-btn-primary" onclick="approvePlan('CG-20240114-0002')">审核</button>
                                    </div>
                                </td>
                            </tr>
                            <!-- 审核未过状态 -->
                            <tr data-status="rejected" data-tab="todo">
                                <td class="checkbox-column">
                                    <input type="checkbox">
                                </td>
                                <td class="plan-code-column">
                                    <a href="#" class="table-link" onclick="viewDetail('CG-20240113-0003')">CG-20240113-0003</a>
                                </td>
                                <td>年度办公用品采购计划</td>
                                <td>货物</td>
                                <td>竞争性磋商</td>
                                <td>
                                    <span class="status-badge status-rejected">审核未过</span>
                                </td>
                                <td>自主招标</td>
                                <td>无</td>
                                <td>王五</td>
                                <td>行政部</td>
                                <td>2024年第1季度</td>
                                <td>2024-01-13 14:30:10</td>
                                <td class="operation-column">
                                    <div class="operation-buttons">
                                        <button class="op-btn" onclick="editPlan('CG-20240113-0003')">编辑</button>
                                        <button class="op-btn op-btn-danger" onclick="deletePlan('CG-20240113-0003')">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <!-- 审核通过状态 - 已从待办列表移除 -->
                            <tr data-status="approved" data-tab="completed" style="display: none;">
                                <td class="checkbox-column">
                                    <input type="checkbox">
                                </td>
                                <td class="plan-code-column">
                                    <a href="#" class="table-link" onclick="viewDetail('CG-20240112-0004')">CG-20240112-0004</a>
                                </td>
                                <td>IT设备采购计划</td>
                                <td>货物</td>
                                <td>公告比选</td>
                                <td>
                                    <span class="status-badge status-approved">审核通过</span>
                                </td>
                                <td>委托招标</td>
                                <td>某IT招标代理公司</td>
                                <td>赵六</td>
                                <td>信息部</td>
                                <td>2024年第2季度</td>
                                <td>2024-01-12 11:15:30</td>
                                <td class="operation-column">
                                    <div class="operation-buttons">
                                        <!-- 已审核状态，无操作按钮 -->
                                        -
                                    </div>
                                </td>
                            </tr>

                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div class="pagination">
                    <div class="page-info">共 42 条记录，当前 1/9 页</div>
                    <div class="page-size-selector">
                        <span>每页显示：</span>
                        <select>
                            <option>5</option>
                            <option>10</option>
                            <option>20</option>
                            <option>50</option>
                        </select>
                    </div>
                    <button class="page-btn disabled">«</button>
                    <button class="page-btn disabled">‹</button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">3</button>
                    <button class="page-btn">4</button>
                    <button class="page-btn">5</button>
                    <button class="page-btn">›</button>
                    <button class="page-btn">»</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 导入弹窗 -->
    <div class="import-modal" id="importModal">
        <div class="import-modal-content">
            <div class="import-modal-header">
                <h3 class="import-modal-title">数据导入</h3>
                <button class="import-modal-close" onclick="closeImportModal()">&times;</button>
            </div>
            
            <!-- 模版下载区域 -->
            <div class="import-section">
                <div class="import-section-title">
                    <span>📋</span>
                    第一步：下载导入模版
                </div>
                <div style="text-align: center; padding: 20px 0;">
                    <button class="btn btn-outline" onclick="downloadTemplate()">下载模版</button>
                </div>
            </div>
            
            <!-- 文件上传区域 -->
            <div class="import-section">
                <div class="import-section-title">
                    <span>📤</span>
                    第二步：选择文件导入
                </div>
                <div class="import-section-desc">
                    支持 Excel 格式文件（.xlsx, .xls），文件大小不超过 10MB。
                </div>
                <div class="file-upload-area" id="fileUploadArea" onclick="selectFile()">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">点击选择文件或拖拽文件到此处</div>
                    <div class="upload-hint">支持 .xlsx, .xls 格式，最大 10MB</div>
                </div>
                
                <!-- 文件信息显示 -->
                <div class="file-info" id="fileInfo">
                    <div class="file-name" id="fileName"></div>
                    <div class="file-size" id="fileSize"></div>
                </div>
                
                <!-- 隐藏的文件输入框 -->
                <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;">
            </div>
            
            <!-- 操作按钮 -->
            <div class="import-actions">
                <button class="btn btn-outline" onclick="closeImportModal()">取消</button>
                <button class="btn btn-upload" id="uploadBtn" onclick="uploadFile()">开始导入</button>
            </div>
        </div>
    </div>
    
    <script>
        // 高级查询切换功能
        function toggleAdvancedSearch() {
            const advancedSearch = document.getElementById('advanced-search');
            if (advancedSearch.style.display === 'none') {
                advancedSearch.style.display = 'block';
            } else {
                advancedSearch.style.display = 'none';
            }
        }
        
        // 主页签切换功能（待办/已办）
        function switchMainTab(tabName) {
            // 移除所有主页签活跃状态
            document.querySelectorAll('.main-tabs .tab-item').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 添加当前活跃状态
            document.querySelector(`.main-tabs [data-tab="${tabName}"]`).classList.add('active');
            
            // 根据页签过滤数据行
            const tableRows = document.querySelectorAll('tbody tr');
            tableRows.forEach(row => {
                const rowStatus = row.getAttribute('data-status');
                
                if (tabName === 'all') {
                    // 全部页签显示所有数据
                    row.style.display = '';
                } else if (tabName === 'todo') {
                    // 待办页签只显示待审核、审核中、审核未过状态
                    if (rowStatus === 'pending_review' || rowStatus === 'reviewing' || rowStatus === 'rejected') {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                } else if (tabName === 'done') {
                    // 已办页签显示审核通过状态
                    if (rowStatus === 'approved') {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                }
            });
            
            console.log('切换到主页签:', tabName);
        }
        
        // 视图按钮切换功能（我的计划/下级单位计划/全部计划）
        function switchView(viewName) {
            // 移除所有视图按钮活跃状态
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 添加当前活跃状态
            event.target.classList.add('active');
            
            // 根据视图控制审核按钮显示
            const approveBtn = document.querySelector('.btn-approve');
            if (viewName === 'my-plans') {
                if (approveBtn) approveBtn.style.display = 'none';
            } else {
                if (approveBtn) approveBtn.style.display = 'inline-block';
            }
            
            // 这里可以添加数据过滤逻辑
            console.log('切换到视图:', viewName);
        }
        
        // 页面跳转函数
        function openPage(url, title) {
            if (window.parent && window.parent !== window) {
                // 向父窗口发送消息，请求打开新页面
                window.parent.postMessage({
                    type: 'openPage',
                    title: title,
                    url: url
                }, '*');
            } else {
                window.open(url, '_blank');
            }
        }
        
        // 全局操作函数
        function viewDetail(planId) {
            openPage('采购计划管理-详情页.html', '采购计划详情-' + planId);
        }
        
        function editPlan(planId) {
            openPage('采购计划管理-新建编辑页.html', '编辑采购计划-' + planId);
        }
        
        function deletePlan(planId) {
            if (confirm('确定要删除采购计划 ' + planId + ' 吗？')) {
                alert('删除成功');
                // 这里可以添加实际的删除逻辑
            }
        }
        
        function submitPlan(planCode) {
            if (confirm('确定要提交计划 ' + planCode + ' 吗？')) {
                alert('提交计划: ' + planCode);
            }
        }
        
        function withdrawPlan(planCode) {
            if (confirm('确定要撤回计划 ' + planCode + ' 吗？')) {
                alert('撤回计划: ' + planCode);
            }
        }
        
        function approvePlan(planCode) {
            openPage('采购计划管理-审核页.html', '审核采购计划-' + planCode);
        }
        
        // 导入数据功能 - 显示弹窗
        function importData() {
            document.getElementById('importModal').style.display = 'flex';
        }
        
        // 关闭导入弹窗
        function closeImportModal() {
            document.getElementById('importModal').style.display = 'none';
            // 重置文件选择
            document.getElementById('fileInput').value = '';
            document.getElementById('fileInfo').style.display = 'none';
            document.getElementById('uploadBtn').disabled = true;
        }
        
        // 选择文件
        function selectFile() {
            document.getElementById('fileInput').click();
        }
        
        // 上传文件
        function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('请先选择要导入的文件');
                return;
            }
            
            // 模拟上传过程
            const uploadBtn = document.getElementById('uploadBtn');
            uploadBtn.textContent = '导入中...';
            uploadBtn.disabled = true;
            
            setTimeout(() => {
                alert(`文件 "${file.name}" 导入成功！\n\n注意：这是演示版本，实际导入功能需要后端支持。`);
                closeImportModal();
                uploadBtn.textContent = '开始导入';
                uploadBtn.disabled = false;
            }, 2000);
        }
        
        // 下载导入模版
        function downloadTemplate() {
            // 创建模版数据
            const templateData = [
                ['计划项目名称', '采购类型', '采购方式', '采购组织方式', '代理机构', '项目经办人', '项目业主', '招标时间', '备注'],
                ['示例：办公设备采购计划', '货物', '公告比选', '自主招标', '', '张三', '行政部', '2024年第1季度', ''],
                ['示例：建筑材料采购计划', '施工', '邀请比选', '委托招标', '某建筑招标代理公司', '李四', '工程部', '2024年第2季度', '']
            ];
            
            // 创建CSV内容
            const csvContent = templateData.map(row => row.join(',')).join('\n');
            
            // 创建下载链接
            const blob = new Blob(["\uFEFF" + csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            
            link.setAttribute('href', url);
            link.setAttribute('download', '采购计划导入模版.csv');
            link.style.visibility = 'hidden';
            
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            alert('导入模版下载成功！\n\n请按照模版格式填写数据后重新导入。');
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化状态 - 默认在"待办"主页签和"我的计划"视图，隐藏审核按钮
            const approveBtn = document.querySelector('.btn-approve');
            if (approveBtn) {
                approveBtn.style.display = 'none';
            }
            
            // 文件选择处理
            const fileInput = document.getElementById('fileInput');
            const fileInfo = document.getElementById('fileInfo');
            const fileName = document.getElementById('fileName');
            const fileSize = document.getElementById('fileSize');
            const uploadBtn = document.getElementById('uploadBtn');
            
            if (fileInput) {
                fileInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        // 检查文件类型
                        const allowedTypes = ['.xlsx', '.xls'];
                        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
                        
                        if (!allowedTypes.includes(fileExtension)) {
                            alert('请选择 Excel 格式文件（.xlsx 或 .xls）');
                            fileInput.value = '';
                            return;
                        }
                        
                        // 检查文件大小（10MB限制）
                        if (file.size > 10 * 1024 * 1024) {
                            alert('文件大小不能超过 10MB');
                            fileInput.value = '';
                            return;
                        }
                        
                        // 显示文件信息
                        fileName.textContent = file.name;
                        fileSize.textContent = `文件大小：${(file.size / 1024 / 1024).toFixed(2)}MB`;
                        fileInfo.style.display = 'block';
                        uploadBtn.disabled = false;
                    } else {
                        fileInfo.style.display = 'none';
                        uploadBtn.disabled = true;
                    }
                });
            }
            
            // 页签点击
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
                
                const closeBtn = tab.querySelector('.tab-close');
                if (closeBtn) {
                    closeBtn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        if (tab.classList.contains('active') && tabs.length > 1) {
                            const nextTab = tab.nextElementSibling || tab.previousElementSibling;
                            nextTab.classList.add('active');
                        }
                        tab.remove();
                    });
                }
            });
            
            // 主页签项点击
            document.querySelectorAll('.main-tabs .tab-item').forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有主页签活跃状态
                    document.querySelectorAll('.main-tabs .tab-item').forEach(t => t.classList.remove('active'));
                    // 添加当前活跃状态
                    this.classList.add('active');
                });
            });
            
            // 视图按钮点击
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有视图按钮活跃状态
                    document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                    // 添加当前活跃状态
                    this.classList.add('active');
                });
            });
            
            // 高级查询切换功能已移到全局函数toggleAdvancedSearch()
            
            // 表格行点击
            const tableLinks = document.querySelectorAll('.table-link');
            tableLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const planId = this.textContent;
                    openPage('采购计划管理-详情页.html', '采购计划详情-' + planId);
                });
            });
            
            // 操作按钮点击
            const opBtns = document.querySelectorAll('.op-btn');
            opBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const action = this.textContent.trim();
                    const planId = this.closest('tr').querySelector('.table-link').textContent;
                    
                    if (action === '查看') {
                        openPage('采购计划管理-详情页.html', '采购计划详情-' + planId);
                    } else if (action === '编辑' || action === '复制') {
                        openPage('采购计划管理-新建编辑页.html', (action === '编辑' ? '编辑' : '复制') + '采购计划-' + planId);
                    } else {
                        alert(`执行${action}操作：${planId}`);
                    }
                });
            });
            
            // 新建按钮点击
            const createBtn = document.querySelector('.btn-success');
            createBtn.addEventListener('click', function() {
                openPage('采购计划管理-新建编辑页.html', '新建采购计划');
            });
            
            // 批量操作按钮点击
            const batchBtns = document.querySelectorAll('.action-buttons .btn');
            batchBtns.forEach(btn => {
                if (btn !== createBtn) {
                    btn.addEventListener('click', function() {
                        const action = this.textContent.trim();
                        alert(`执行${action}操作`);
                    });
                }
            });
            
            // 分页按钮点击
            const pageBtns = document.querySelectorAll('.page-btn');
            pageBtns.forEach(btn => {
                if (!btn.classList.contains('disabled')) {
                    btn.addEventListener('click', function() {
                        pageBtns.forEach(b => b.classList.remove('active'));
                        if (!['«', '‹', '›', '»'].includes(this.textContent)) {
                            this.classList.add('active');
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>