<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>补遗/澄清/答疑管理 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            margin: 0;
            padding: 0;
        }
        
        /* 主体布局 */
        .main-container {
            height: 100vh;
        }
        
        /* 内容区域 */
        .content-wrapper {
            overflow-y: auto;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        

        
        /* 页面内容 */
        .page-content {
            background: white;
            border-radius: 12px;
            border: 1px solid #f0f0f0;
            padding: 24px;
        }
        
        /* 页面标题 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        /* 主页签样式 */
        .main-tabs {
            display: flex;
            border-bottom: 1px solid #f0f0f0;
            margin-bottom: 16px;
        }
        
        .main-tabs .tab-item {
            padding: 12px 24px;
            cursor: pointer;
            font-size: 14px;
            position: relative;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .main-tabs .tab-item.active {
            color: #1890ff;
            font-weight: 500;
        }
        
        .main-tabs .tab-item.active:after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #1890ff;
        }
        

        
        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #374151;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .help-icon {
            width: 16px;
            height: 16px;
            background: #1890ff;
            border-radius: 50%;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
        }

        /* 查询区域 */
        .search-area {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-label {
            font-size: 13px;
            color: #555;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-control {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .btn-group {
            display: flex;
            gap: 10px;
            align-items: end;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-outline {
            background: white;
            color: #3498db;
            border: 1px solid #3498db;
        }

        .btn-outline:hover {
            background: #3498db;
            color: white;
        }

        /* 高级查询 */
        .advanced-search {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            display: none;
        }

        .advanced-search.show {
            display: block;
        }

        .advanced-toggle {
            color: #1890ff;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        /* 功能操作栏 */
        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
        }

        .btn-success {
            background-color: #52c41a;
            color: white;
            border-color: #52c41a;
        }

        .btn-success:hover {
            background-color: #73d13d;
            border-color: #73d13d;
        }

        .btn-danger {
            background-color: #ff4d4f;
            color: white;
            border-color: #ff4d4f;
        }

        .btn-danger:hover {
            background-color: #ff7875;
            border-color: #ff7875;
        }

        .btn-warning {
            background-color: #faad14;
            color: white;
            border-color: #faad14;
        }

        .btn-warning:hover {
            background-color: #ffc53d;
            border-color: #ffc53d;
        }

        /* 表格样式 */
        .table-container {
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            overflow: hidden;
            background: white;
        }

        .table-wrapper {
            overflow-x: auto;
            max-width: 100%;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            min-width: 1200px;
        }

        .data-table th,
        .data-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e8eaec;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .data-table th {
            background-color: #fafafa;
            font-weight: 600;
            color: #262626;
        }

        .data-table tr:hover td {
            background-color: #e6f7ff;
        }

        /* 冻结列样式 */
        .frozen-column {
            position: sticky;
            background: white;
            z-index: 10;
        }

        .frozen-column.checkbox-col {
            left: 0;
            width: 50px;
            min-width: 50px;
        }

        .frozen-column.name-col {
            left: 50px;
            width: 200px;
            min-width: 200px;
        }

        .frozen-column.action-col {
            right: 0;
            width: 200px;
            min-width: 200px;
        }

        .data-table th.frozen-column {
            background-color: #fafafa;
        }

        .data-table tr:hover .frozen-column {
            background-color: #e6f7ff;
        }

        /* 冻结列阴影效果 */
        .frozen-column.name-col::after {
            content: '';
            position: absolute;
            top: 0;
            right: -1px;
            width: 1px;
            height: 100%;
            background: linear-gradient(to right, rgba(0,0,0,0.1), transparent);
        }

        .frozen-column.action-col::before {
            content: '';
            position: absolute;
            top: 0;
            left: -1px;
            width: 1px;
            height: 100%;
            background: linear-gradient(to left, rgba(0,0,0,0.1), transparent);
        }

        /* 表格链接 */
        .table-link {
            color: #1890ff;
            text-decoration: none;
            cursor: pointer;
        }

        .table-link:hover {
            text-decoration: underline;
        }

        /* 状态标签 */
        .status-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }

        .status-pending {
            background-color: #fff7e6;
            color: #fa8c16;
        }

        .status-processing {
            background-color: #e6f7ff;
            color: #1890ff;
        }

        .status-approved {
            background-color: #e8f5e8;
            color: #52c41a;
        }

        .status-rejected {
            background-color: #fff2f0;
            color: #ff4d4f;
        }

        .status-published {
            background-color: #e8f5e8;
            color: #52c41a;
        }

        .status-draft {
            background-color: #f6f6f6;
            color: #8c8c8c;
        }

        /* 操作按钮 */
        .operation-buttons {
            display: flex;
            justify-content: center;
            gap: 6px;
        }

        .op-btn {
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            border: 1px solid #d9d9d9;
            background-color: #f8f9fa;
            color: #374151;
            transition: all 0.2s;
            font-weight: 500;
        }

        .op-btn:hover {
            color: #1890ff;
            border-color: #1890ff;
            background-color: #e5e7eb;
        }

        .op-btn-primary {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .op-btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }

        .op-btn-danger {
            background-color: #ef4444;
            color: white;
            border-color: #ef4444;
        }

        .op-btn-danger:hover {
            background-color: #f87171;
            border-color: #f87171;
        }

        .op-btn-warning {
            background-color: #f59e0b;
            color: white;
            border-color: #f59e0b;
        }

        .op-btn-warning:hover {
            background-color: #fbbf24;
            border-color: #fbbf24;
        }

        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 2px;
            font-size: 12px;
            cursor: pointer;
            margin-right: 4px;
            transition: all 0.3s;
        }

        .action-btn:last-child {
            margin-right: 0;
        }

        .btn-submit {
            background-color: #52c41a;
            color: white;
        }

        .btn-submit:hover {
            background-color: #73d13d;
        }

        .btn-audit {
            background-color: #1890ff;
            color: white;
        }

        .btn-audit:hover {
            background-color: #40a9ff;
        }

        .btn-publish {
            background-color: #722ed1;
            color: white;
        }

        .btn-publish:hover {
            background-color: #9254de;
        }

        .btn-edit {
            background-color: #faad14;
            color: white;
        }

        .btn-edit:hover {
            background-color: #ffc53d;
        }

        .btn-delete {
            background-color: #ff4d4f;
            color: white;
        }

        .btn-delete:hover {
            background-color: #ff7875;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 0;
            border-top: 1px solid #e8eaec;
            background-color: #fafafa;
        }

        .pagination-info {
            color: #8c8c8c;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .page-btn {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            background-color: white;
            color: #262626;
            border-radius: 2px;
            cursor: pointer;
            font-size: 14px;
        }

        .page-btn:hover {
            background-color: #f5f5f5;
        }

        .page-btn.active {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            padding: 24px;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e6e8eb;
        }

        .help-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #6b7280;
        }

        .help-body {
            color: #374151;
            line-height: 1.6;
        }

        .help-section {
            margin-bottom: 16px;
        }

        .help-section h4 {
            color: #1f2937;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .help-section p {
            margin-bottom: 8px;
            font-size: 13px;
        }

        .help-list {
            list-style: none;
            padding-left: 16px;
        }

        .help-list li {
            margin-bottom: 4px;
            font-size: 13px;
            position: relative;
        }

        .help-list li::before {
            content: '•';
            color: #2563eb;
            position: absolute;
            left: -12px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .search-form {
                grid-template-columns: 1fr;
            }
            
            .action-bar {
                flex-direction: column;
                gap: 12px;
                align-items: stretch;
            }
            
            .action-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="content-wrapper">
            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 页面标题 -->
                <div class="page-header">
                    <div class="page-title">
                        补遗澄清答疑管理
                        <span class="help-icon" onclick="showHelp()">?</span>
                    </div>
                </div>
                
                <!-- 主页签 -->
                 <div class="main-tabs">
                     <div class="tab-item active" onclick="switchMainTab('pending')" id="tab-pending">待办</div>
                     <div class="tab-item" onclick="switchMainTab('completed')" id="tab-completed">已办</div>
                 </div>
                

            <!-- 待办页签 -->
            <div id="pending-tab" class="tab-content active">
                <!-- 查询区域 -->
                 <div class="search-area">
                    <div class="search-form">
                        <div class="form-group">
                            <label class="form-label">补遗/澄清/答疑标题</label>
                            <input type="text" class="form-control" placeholder="请输入标题">
                        </div>
                        <div class="form-group">
                            <label class="form-label">类型</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="supplement">补遗</option>
                                <option value="clarification">澄清</option>
                                <option value="qa">答疑</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">审核状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="pending">待审核</option>
                                <option value="processing">审核中</option>
                                <option value="approved">已通过</option>
                                <option value="rejected">已驳回</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">发布状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="draft">草稿</option>
                                <option value="published">已发布</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <div class="btn-group">
                                <button type="button" class="btn btn-primary" onclick="searchData()">查询</button>
                                <button type="button" class="btn btn-secondary" onclick="resetSearch()">重置</button>
                                <button type="button" class="btn btn-outline" onclick="toggleAdvanced()">高级查询</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 高级查询 -->
                    <div class="advanced-search" id="advanced-search">
                        <div class="search-form">
                            <div class="form-group">
                                <label class="form-label">关联项目</label>
                                <input type="text" class="form-control" placeholder="请输入项目名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">关联标段</label>
                                <input type="text" class="form-control" placeholder="请输入标段名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">申请人</label>
                                <input type="text" class="form-control" placeholder="请输入申请人">
                            </div>
                            <div class="form-group">
                                <label class="form-label">紧急程度</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="urgent">紧急</option>
                                    <option value="normal">普通</option>
                                    <option value="low">低</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">创建时间</label>
                                <input type="date" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="form-label">发布时间</label>
                                <input type="date" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="form-label">截止时间</label>
                                <input type="date" class="form-control">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能操作栏 -->
                <div class="action-bar">
                    <div class="action-buttons">
                        <button class="btn btn-success" onclick="createClarification()">新建补遗/澄清/答疑</button>
                    </div>
                </div>

                <!-- 数据列表 -->
                <div class="table-container">
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th class="frozen-column checkbox-col">
                                        <input type="checkbox" onclick="toggleAll(this)">
                                    </th>
                                    <th class="frozen-column name-col">补遗澄清答疑标题</th>
                                    <th style="width: 100px;">审核状态</th>
                                    <th style="width: 100px;">发布状态</th>
                                    <th style="width: 100px;">采购类型</th>
                                    <th style="width: 150px;">关联标段</th>
                                    <th style="width: 150px;">关联项目</th>
                                    <th style="width: 120px;">创建时间</th>
                                    <th class="frozen-column action-col">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('CLA-2024-001')">办公设备采购技术规格补遗</a>
                                    </td>
                                    <td><span class="status-tag status-pending">待审核</span></td>
                                    <td><span class="status-tag status-pending">待发布</span></td>
                                    <td>货物采购</td>
                                    <td>办公桌椅采购标段</td>
                                    <td>办公设备采购项目</td>
                                    <td>2024-03-15</td>
                                    <td class="frozen-column action-col">
                                        <div class="operation-buttons">
                                            <button class="op-btn op-btn-primary" onclick="submitClarification('CLA-2024-001')">提交</button>
                                            <button class="op-btn op-btn-warning" onclick="editClarification('CLA-2024-001')">编辑</button>
                                            <button class="op-btn op-btn-danger" onclick="deleteClarification('CLA-2024-001')">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('CLA-2024-002')">IT系统建设投标文件格式澄清</a>
                                    </td>
                                    <td><span class="status-tag status-processing">审核中</span></td>
                                    <td><span class="status-tag status-pending">待发布</span></td>
                                    <td>服务采购</td>
                                    <td>系统开发标段</td>
                                    <td>IT系统建设项目</td>
                                    <td>2024-03-16</td>
                                    <td class="frozen-column action-col">
                                        <button class="action-btn btn-audit" onclick="auditClarification('CLA-2024-002')">审核</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('CLA-2024-003')">文具用品采购数量变更答疑</a>
                                    </td>
                                    <td><span class="status-tag status-approved">已通过</span></td>
                                    <td><span class="status-tag status-pending">待发布</span></td>
                                    <td>货物采购</td>
                                    <td>文具用品采购标段</td>
                                    <td>办公设备采购项目</td>
                                    <td>2024-03-17</td>
                                    <td class="frozen-column action-col">
                                        <button class="action-btn btn-publish" onclick="publishClarification('CLA-2024-003')">发布</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('CLA-2024-004')">电脑设备采购配置要求补遗</a>
                                    </td>
                                    <td><span class="status-tag status-rejected">未通过</span></td>
                                    <td><span class="status-tag status-pending">待发布</span></td>
                                    <td>货物采购</td>
                                    <td>电脑设备采购标段</td>
                                    <td>办公设备采购项目</td>
                                    <td>2024-03-18</td>
                                    <td class="frozen-column action-col">
                                        <button class="action-btn btn-submit" onclick="submitClarification('CLA-2024-004')">提交</button>
                                        <button class="action-btn btn-edit" onclick="editClarification('CLA-2024-004')">编辑</button>
                                        <button class="action-btn btn-delete" onclick="deleteClarification('CLA-2024-004')">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="pagination">
                        <div class="pagination-info">
                            共 4 条记录，当前第 1 页
                        </div>
                        <div class="pagination-controls">
                            <button class="page-btn" disabled>上一页</button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn" disabled>下一页</button>
                        </div>
                    </div>
                </div>
            </div>

                <!-- 已办页签 -->
                 <div id="completed-tab" class="tab-content">
                 </div>
             </div>
         </div>
     </div>

    <!-- 帮助弹窗 -->
    <div id="help-modal" class="help-modal">
        <div class="help-content">
            <div class="help-header">
                <h3 class="help-title">补遗/澄清/答疑管理功能说明</h3>
                <button class="close-btn" onclick="hideHelp()">&times;</button>
            </div>
            <div class="help-body">
                <div class="help-section">
                    <h4>功能概述</h4>
                    <p>补遗/澄清/答疑管理模块用于管理招标采购过程中的补遗文件、澄清说明和答疑回复等信息。</p>
                </div>
                
                <div class="help-section">
                    <h4>主要功能</h4>
                    <ul class="help-list">
                        <li>补遗/澄清/答疑信息的新建、编辑、删除</li>
                        <li>补遗/澄清/答疑审核流程管理</li>
                        <li>补遗/澄清/答疑发布状态控制</li>
                        <li>与项目、标段的关联管理</li>
                        <li>信息的查询和筛选</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>操作说明</h4>
                    <ul class="help-list">
                        <li><strong>待办页签：</strong>显示需要处理的补遗/澄清/答疑，包括草稿、待审核、审核中等状态</li>
                        <li><strong>已办页签：</strong>显示已完成处理的补遗/澄清/答疑，主要为已发布状态</li>
                        <li><strong>提交：</strong>将草稿状态的内容提交审核</li>
                        <li><strong>审核：</strong>对提交的内容进行审核，可通过或驳回</li>
                        <li><strong>发布：</strong>将审核通过的内容发布到公开平台</li>
                        <li><strong>编辑：</strong>修改内容，仅限草稿和驳回状态</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>类型说明</h4>
                    <ul class="help-list">
                        <li><strong>补遗：</strong>对招标文件的补充说明或修正</li>
                        <li><strong>澄清：</strong>对招标文件中不明确内容的澄清说明</li>
                        <li><strong>答疑：</strong>对投标人疑问的回复和解答</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>状态说明</h4>
                    <ul class="help-list">
                        <li><strong>草稿：</strong>内容尚未提交，可以编辑</li>
                        <li><strong>待审核：</strong>内容已提交，等待审核</li>
                        <li><strong>审核中：</strong>内容正在审核过程中</li>
                        <li><strong>已通过：</strong>内容审核通过，可以发布</li>
                        <li><strong>未通过：</strong>内容审核未通过，需要修改</li>
                        <li><strong>已发布：</strong>内容已发布到公开平台</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>注意事项</h4>
                    <ul class="help-list">
                        <li>补遗/澄清/答疑信息主要从关联的标段自动带出，减少重复录入</li>
                        <li>发布后不可修改，如需变更请创建新的补遗/澄清/答疑</li>
                        <li>删除操作需要相应权限，已发布的内容建议撤回而非删除</li>
                        <li>紧急程度影响处理优先级，请合理设置</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页签切换
        function switchTab(tabName) {
            // 隐藏所有页签内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 移除所有页签的激活状态
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示选中的页签内容
            if (tabName === 'pending') {
                document.getElementById('pending-tab').classList.add('active');
                document.querySelector('.tab-item:first-child').classList.add('active');
            } else if (tabName === 'completed') {
                document.getElementById('completed-tab').classList.add('active');
                document.querySelector('.tab-item:last-child').classList.add('active');
            }
        }

        // 高级查询切换
        function toggleAdvanced() {
            const advancedSearch = document.getElementById('advanced-search');
            const button = event.target;
            
            if (advancedSearch.style.display === 'none' || advancedSearch.style.display === '') {
                advancedSearch.style.display = 'block';
                button.textContent = '收起查询';
            } else {
                advancedSearch.style.display = 'none';
                button.textContent = '高级查询';
            }
        }

        function toggleAdvancedCompleted() {
            const advancedSearch = document.getElementById('advanced-search-completed');
            const button = event.target;
            
            if (advancedSearch.style.display === 'none' || advancedSearch.style.display === '') {
                advancedSearch.style.display = 'block';
                button.textContent = '收起查询';
            } else {
                advancedSearch.style.display = 'none';
                button.textContent = '高级查询';
            }
        }

        // 全选功能
        function toggleAll(checkbox) {
            const checkboxes = document.querySelectorAll('#pending-tab tbody input[type="checkbox"]');
            checkboxes.forEach(cb => {
                cb.checked = checkbox.checked;
            });
        }

        function toggleAllCompleted(checkbox) {
            const checkboxes = document.querySelectorAll('#completed-tab tbody input[type="checkbox"]');
            checkboxes.forEach(cb => {
                cb.checked = checkbox.checked;
            });
        }

        // 帮助弹窗
        function showHelp() {
            document.getElementById('help-modal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('help-modal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });

        // 业务操作函数
        function viewDetail(id) {
            console.log('查看补遗/澄清/答疑详情:', id);
            // 在主页面页签中打开详情页面
            openPageInTab('补遗/澄清/答疑详情-' + id, '补遗澄清答疑管理-详情页.html?id=' + id);
        }

        function submitClarification(id) {
            if (confirm('确定要提交这个补遗/澄清/答疑吗？提交后将进入审核流程。')) {
                console.log('提交补遗/澄清/答疑:', id);
                alert('补遗/澄清/答疑提交成功！');
                // 这里可以调用提交接口，然后刷新页面
                location.reload();
            }
        }

        function auditClarification(id) {
            console.log('审核补遗/澄清/答疑:', id);
            // 在主页面页签中打开审核页面
            openPageInTab('补遗/澄清/答疑审核-' + id, '补遗澄清答疑管理-审核页.html?id=' + id);
        }

        function publishClarification(id) {
            if (confirm('确定要发布这个补遗/澄清/答疑吗？发布后将在公开平台展示。')) {
                console.log('发布补遗/澄清/答疑:', id);
                alert('补遗/澄清/答疑发布成功！');
                // 这里可以调用发布接口，然后刷新页面
                location.reload();
            }
        }

        function editClarification(id) {
            console.log('编辑补遗/澄清/答疑:', id);
            // 在主页面页签中打开编辑页面
            openPageInTab('编辑补遗/澄清/答疑-' + id, '补遗澄清答疑管理-新建编辑页.html?id=' + id + '&mode=edit');
        }

        function deleteClarification(id) {
            if (confirm('确定要删除这个补遗/澄清/答疑吗？删除后无法恢复。')) {
                console.log('删除补遗/澄清/答疑:', id);
                alert('补遗/澄清/答疑删除成功！');
                // 这里可以调用删除接口，然后刷新页面
                location.reload();
            }
        }

        // 新建补遗/澄清/答疑
        function createClarification() {
            openPageInTab('新建补遗/澄清/答疑', '补遗澄清答疑管理-新建编辑页.html?mode=create');
        }

        // 在主页面页签中打开页面的通用函数
        function openPageInTab(title, url) {
            // 向父窗口发送消息，在页签中打开页面
            if (window.parent && window.parent !== window) {
                window.parent.postMessage({
                    type: 'openPage',
                    title: title,
                    url: url
                }, '*');
            } else {
                // 如果不在iframe中，则在新窗口打开（兼容性处理）
                window.open(url, '_blank');
            }
        }

        // 批量删除
        function batchDelete() {
            const activeTab = document.querySelector('.tab-content.active');
            const checkedBoxes = activeTab.querySelectorAll('tbody input[type="checkbox"]:checked');
            
            if (checkedBoxes.length === 0) {
                alert('请选择要删除的补遗/澄清/答疑');
                return;
            }
            
            if (confirm(`确定要删除选中的 ${checkedBoxes.length} 个补遗/澄清/答疑吗？删除后无法恢复。`)) {
                console.log('批量删除补遗/澄清/答疑');
                alert('批量删除成功！');
                location.reload();
            }
        }

        // 批量审核
        function batchAudit() {
            const checkedBoxes = document.querySelectorAll('#pending-tab tbody input[type="checkbox"]:checked');
            
            if (checkedBoxes.length === 0) {
                alert('请选择要审核的补遗/澄清/答疑');
                return;
            }
            
            if (confirm(`确定要批量审核选中的 ${checkedBoxes.length} 个补遗/澄清/答疑吗？`)) {
                console.log('批量审核补遗/澄清/答疑');
                alert('批量审核操作已提交！');
                location.reload();
            }
        }
    </script>
</body>
</html>