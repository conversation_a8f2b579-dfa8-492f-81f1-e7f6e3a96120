<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>招采平台 - 采购计划详情</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        /* 顶部导航栏 */
        .top-nav {
            height: 60px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .logo {
            font-size: 20px;
            font-weight: bold;
            margin-right: 40px;
        }
        
        .nav-menu {
            display: flex;
            gap: 30px;
            flex: 1;
        }
        
        .nav-item {
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .nav-item:hover, .nav-item.active {
            background-color: rgba(255,255,255,0.2);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        /* 主体布局 */
        .main-container {
            height: calc(100vh - 60px);
        }
        
        /* 内容区域 */
        .content-wrapper {
            overflow-y: auto;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        /* 页签区域 */
        .tabs {
            display: flex;
            background: white;
            border-radius: 4px 4px 0 0;
            border: 1px solid #e8eaec;
            border-bottom: none;
            margin-bottom: -1px;
            position: relative;
            z-index: 1;
        }
        
        .tab {
            padding: 12px 20px;
            cursor: pointer;
            border-right: 1px solid #e8eaec;
            position: relative;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tab.active {
            color: #1890ff;
            background-color: #f5f7fa;
            border-bottom: 2px solid #1890ff;
            margin-bottom: -1px;
        }
        
        .tab-close {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .tab-close:hover {
            background-color: #e6e6e6;
        }
        
        /* 页面内容 */
        .page-content {
            background: white;
            border-radius: 0 4px 4px 4px;
            border: 1px solid #e8eaec;
            padding: 20px;
        }
        
        /* 页面标题 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e8eaec;
        }
        
        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #262626;
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            gap: 8px;
        }
        
        .title-status {
            display: flex;
            align-items: center;
        }
        
        .title-content {
            flex: 1;
        }
        
        .title-main {
            font-size: 20px;
            font-weight: 600;
            color: #262626;
            line-height: 1.4;
        }
        
        .title-sub {
            font-size: 14px;
            font-weight: 400;
            color: #8c8c8c;
            margin-top: 4px;
        }
        
        .help-icon {
            width: 16px;
            height: 16px;
            background: #1890ff;
            border-radius: 50%;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
        }
        
        .header-actions {
            background: #f8fafc;
            border-top: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            bottom: 0;
            gap: 12px;
        }
        
        .btn-group {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            padding: 0 16px;
            height: 32px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            border: 1px solid transparent;
            transition: all 0.3s;
            text-decoration: none;
        }
        
        .btn-primary {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        
        .btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }
        
        .btn-success {
            background-color: #52c41a;
            color: white;
            border-color: #52c41a;
        }
        
        .btn-success:hover {
            background-color: #73d13d;
            border-color: #73d13d;
        }
        
        .btn-default {
            background-color: white;
            border-color: #d9d9d9;
            color: #262626;
        }
        
        .btn-default:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .btn-danger {
            background-color: #ff4d4f;
            color: white;
            border-color: #ff4d4f;
        }
        
        .btn-danger:hover {
            background-color: #ff7875;
            border-color: #ff7875;
        }
        
        /* 详情页签 */
        .detail-tabs {
            display: flex;
            border-bottom: 1px solid #e8eaec;
            margin-bottom: 20px;
        }
        
        .detail-tab {
            padding: 12px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .detail-tab.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
        }
        
        .detail-tab:hover {
            color: #1890ff;
        }
        
        /* 详情内容 */
        .detail-content {
            display: none;
        }
        
        .detail-content.active {
            display: block;
        }
        
        /* 信息展示 */
        .info-section {
            margin-bottom: 32px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #1890ff;
            position: relative;
        }
        
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            bottom: -2px;
            width: 40px;
            height: 2px;
            background: #1890ff;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .info-grid.full-width {
            grid-template-columns: 1fr;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .info-label {
            font-size: 14px;
            color: #8c8c8c;
            font-weight: 500;
        }
        
        .info-value {
            font-size: 14px;
            color: #262626;
            min-height: 20px;
        }
        
        .info-value.empty {
            color: #d9d9d9;
        }
        
        .info-value.description {
            line-height: 1.6;
            background: #fafafa;
            padding: 12px;
            border-radius: 4px;
            border: 1px solid #e8eaec;
        }
        
        /* 状态标签 */
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-draft {
            background-color: #f5f5f5;
            color: #8c8c8c;
        }
        
        .status-pending {
            background-color: #e6f7ff;
            color: #1890ff;
        }
        
        .status-approved {
            background-color: #f6ffed;
            color: #52c41a;
        }
        
        .status-rejected {
            background-color: #fff1f0;
            color: #ff4d4f;
        }
        
        /* 附件列表 */
        .attachment-list {
            display: grid;
            gap: 12px;
        }
        
        .attachment-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #e8eaec;
            border-radius: 4px;
            background: #fafafa;
        }
        
        .attachment-icon {
            width: 32px;
            height: 32px;
            background: #1890ff;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            margin-right: 12px;
        }
        
        .attachment-info {
            flex: 1;
        }
        
        .attachment-name {
            font-size: 14px;
            color: #262626;
            margin-bottom: 4px;
        }
        
        .attachment-meta {
            font-size: 12px;
            color: #8c8c8c;
        }
        
        .attachment-actions {
            display: flex;
            gap: 8px;
        }
        
        .attachment-btn {
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            color: #1890ff;
            text-decoration: none;
        }
        
        .attachment-btn:hover {
            border-color: #1890ff;
            background: #e6f7ff;
        }
        
        /* 操作记录 */
        .operation-timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline-item {
            position: relative;
            padding-bottom: 24px;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -22px;
            top: 8px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #1890ff;
        }
        
        .timeline-item::after {
            content: '';
            position: absolute;
            left: -18px;
            top: 16px;
            width: 1px;
            height: calc(100% - 8px);
            background: #e8eaec;
        }
        
        .timeline-item:last-child::after {
            display: none;
        }
        
        .timeline-content {
            background: white;
            border: 1px solid #e8eaec;
            border-radius: 4px;
            padding: 16px;
        }
        
        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .timeline-action {
            font-size: 14px;
            font-weight: 500;
            color: #262626;
        }
        
        .timeline-time {
            font-size: 12px;
            color: #8c8c8c;
        }
        
        .timeline-user {
            font-size: 12px;
            color: #1890ff;
            margin-bottom: 8px;
        }
        
        .timeline-desc {
            font-size: 14px;
            color: #595959;
            line-height: 1.5;
        }
        
        /* 审批流程 */
        .approval-flow {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 20px;
            background: #fafafa;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        
        .flow-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            flex: 1;
        }
        
        .flow-step::after {
            content: '';
            position: absolute;
            top: 20px;
            right: -10px;
            width: 20px;
            height: 2px;
            background: #e8eaec;
        }
        
        .flow-step:last-child::after {
            display: none;
        }
        
        .flow-step.completed::after {
            background: #52c41a;
        }
        
        .flow-step.current::after {
            background: #1890ff;
        }
        
        .step-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            margin-bottom: 8px;
            border: 2px solid #e8eaec;
            background: white;
        }
        
        .flow-step.completed .step-icon {
            background: #52c41a;
            border-color: #52c41a;
            color: white;
        }
        
        .flow-step.current .step-icon {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }
        
        .step-title {
            font-size: 12px;
            color: #262626;
            text-align: center;
            margin-bottom: 4px;
        }
        
        .step-time {
            font-size: 10px;
            color: #8c8c8c;
            text-align: center;
        }
        
        /* 响应式 */
        @media (max-width: 1200px) {
            .info-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .content-wrapper {
                padding: 10px;
            }
            
            .approval-flow {
                flex-direction: column;
                gap: 10px;
            }
            
            .flow-step::after {
                display: none;
            }
        }
    </style>
</head>
<body>

    
    <!-- 主体容器 -->
    <div class="main-container">
        <!-- 内容区域 -->
        <div class="content-wrapper">

            
            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 页面标题 -->
                <div class="page-header">
                    <div class="page-title">
                        <div class="title-content">
                            <div class="title-main">办公楼装修工程采购计划</div>
                            <div class="title-sub">CG-2024-001</div>
                        </div>
                        <div class="title-status">
                            <span class="status-badge status-approved">审核通过</span>
                        </div>
                    </div>
                </div>
                
                <!-- 详情页签 -->
                <div class="detail-tabs">
                    <div class="detail-tab active" data-tab="basic">基本信息</div>
                    <div class="detail-tab" data-tab="operation">流程记录</div>
                </div>
                
                <!-- 基本信息 -->
                <div class="detail-content active" id="basic">
                    <!-- 招标信息 -->
                    <div class="info-section">
                        <div class="section-title">招标信息</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">计划项目编号</div>
                                <div class="info-value">CG-2024-001</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">计划项目名称</div>
                                <div class="info-value">办公楼装修工程采购计划</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">采购类型</div>
                                <div class="info-value">施工</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">招标类别</div>
                                <div class="info-value">监理招标</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">采购方式</div>
                                <div class="info-value">公告比选</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">采购预算金额（万元）</div>
                                <div class="info-value">1,200.00</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">资金来源</div>
                                <div class="info-value">自有资金</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">招标时间</div>
                                <div class="info-value">2024年第3季度</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">采购组织方式</div>
                                <div class="info-value">委托招标</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">代理机构</div>
                                <div class="info-value">某招标代理公司</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">年采购计划（万元）</div>
                                <div class="info-value">5,000.00</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">项目经办人</div>
                                <div class="info-value">张三</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">立项决策日期</div>
                                <div class="info-value">2024-01-10</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">驳回原因</div>
                                <div class="info-value empty">-</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 项目信息 -->
                    <div class="info-section">
                        <div class="section-title">项目信息</div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">项目类型</div>
                                <div class="info-value">依法必须招标项目</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">项目业主</div>
                                <div class="info-value">某建设集团有限公司</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">所属二级公司单位</div>
                                <div class="info-value">建设工程分公司</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">备注</div>
                                <div class="info-value">项目需在2024年4月底前完成，确保不影响正常办公。施工期间需做好安全防护措施。</div>
                            </div>
                        </div>
                        
                        <div class="info-grid full-width">
                            <div class="info-item">
                                <div class="info-label">项目基本情况（建设内容及规模）</div>
                                <div class="info-value description">
                                    本次采购为公司办公楼装修工程，包括但不限于：<br>
                                    1. 办公区域装修：包括地面铺装、墙面装饰、吊顶安装等<br>
                                    2. 会议室装修：会议桌椅、投影设备、音响系统等<br>
                                    3. 公共区域装修：大厅、走廊、卫生间等区域的装修改造<br>
                                    4. 配套设施：消防系统、安防系统、网络布线等
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 立项决策文件 -->
                    <div class="info-section">
                        <div class="section-title">立项决策文件</div>
                        <div class="attachment-list">
                            <div class="attachment-item">
                                <div class="attachment-icon">PDF</div>
                                <div class="attachment-info">
                                    <div class="attachment-name">办公楼装修工程立项批复.pdf</div>
                                    <div class="attachment-meta">1.2MB · 上传于 2024-01-10 · 张三</div>
                                </div>
                                <div class="attachment-actions">
                                    <a href="javascript:void(0)" class="attachment-btn">预览</a>
                                    <a href="javascript:void(0)" class="attachment-btn">下载</a>
                                </div>
                            </div>
                            <div class="attachment-item">
                                <div class="attachment-icon">DOC</div>
                                <div class="attachment-info">
                                    <div class="attachment-name">装修工程可行性研究报告.docx</div>
                                    <div class="attachment-meta">2.5MB · 上传于 2024-01-10 · 张三</div>
                                </div>
                                <div class="attachment-actions">
                                    <a href="javascript:void(0)" class="attachment-btn">预览</a>
                                    <a href="javascript:void(0)" class="attachment-btn">下载</a>
                                </div>
                            </div>
                        </div>
                    </div>
                        

                    </div>
                    

                </div>
                
                <!-- 流程记录 -->
                <div class="detail-content" id="operation">
                    <!-- 操作记录 -->
                    <div class="info-section">
                        <div class="section-title">操作记录</div>
                        <div class="operation-timeline">
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">提交审批</div>
                                        <div class="timeline-time">2024-01-15 14:20:30</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三（采购经办人）</div>
                                    <div class="timeline-desc">
                                        采购计划已完善，现提交部门领导审核。请重点关注预算金额的合理性。
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">申请人公司审核通过</div>
                                        <div class="timeline-time">2024-01-15 16:45:20</div>
                                    </div>
                                    <div class="timeline-user">操作人：李四（行政部经理）</div>
                                    <div class="timeline-desc">
                                        经审核，该采购计划符合部门需求，预算合理。同意提交上级公司审核。
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">修改计划内容</div>
                                        <div class="timeline-time">2024-01-15 11:30:15</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三（采购经办人）</div>
                                    <div class="timeline-desc">
                                        根据部门意见，调整了技术规格要求，增加了环保材料的具体标准。
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">创建采购计划</div>
                                        <div class="timeline-time">2024-01-15 10:30:45</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三（采购经办人）</div>
                                    <div class="timeline-desc">
                                        创建了办公楼装修工程采购计划，填写了基本信息和供应商要求等内容。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            

        </div>
    </div>
    
    <script>
        // 页面跳转函数
        function openPage(url, title) {
            if (window.parent && window.parent !== window) {
                // 向父窗口发送消息，请求打开新页面
                window.parent.postMessage({
                    type: 'openPage',
                    title: title,
                    url: url
                }, '*');
            } else {
                window.open(url, '_blank');
            }
        }
        
        // 返回列表
        function goBack() {
            if (window.parent && window.parent !== window) {
                // 向父窗口发送消息，请求打开新页面
                window.parent.postMessage({
                    type: 'openPage',
                    title: '采购计划管理',
                    url: '采购计划管理-列表页.html'
                }, '*');
            } else {
                window.location.href = '采购计划管理-列表页.html';
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            // 页签点击
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
                
                const closeBtn = tab.querySelector('.tab-close');
                closeBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    if (tab.classList.contains('active') && tabs.length > 1) {
                        const nextTab = tab.nextElementSibling || tab.previousElementSibling;
                        nextTab.classList.add('active');
                    }
                    tab.remove();
                });
            });
            
            // 详情页签切换
            const detailTabs = document.querySelectorAll('.detail-tab');
            const detailContents = document.querySelectorAll('.detail-content');
            
            detailTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const targetTab = this.dataset.tab;
                    
                    // 切换页签状态
                    detailTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 切换内容显示
                    detailContents.forEach(content => {
                        content.classList.remove('active');
                        if (content.id === targetTab) {
                            content.classList.add('active');
                        }
                    });
                });
            });
            
            // 操作按钮点击
            const actionBtns = document.querySelectorAll('.header-actions .btn');
            actionBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    const action = this.textContent.trim();
                    
                    if (action === '返回列表') {
                        goBack();
                        return;
                    }
                    
                    if (action === '编辑') {
                        openPage('采购计划管理-新建编辑页.html', '编辑采购计划-CG-2024-001');
                        return;
                    }
                    
                    e.preventDefault();
                    
                    if (action === '审批通过') {
                        if (confirm('确定要审批通过该采购计划吗？')) {
                            alert('审批通过成功！');
                        }
                    } else if (action === '驳回') {
                        const reason = prompt('请输入驳回原因：');
                        if (reason) {
                            alert('驳回成功！原因：' + reason);
                        }
                    } else if (action === '编辑') {
                        alert('跳转到编辑页面');
                    }
                });
            });
            
            // 附件操作
            const attachmentBtns = document.querySelectorAll('.attachment-btn');
            attachmentBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const action = this.textContent.trim();
                    const fileName = this.closest('.attachment-item').querySelector('.attachment-name').textContent;
                    
                    if (action === '预览') {
                        alert('预览文件：' + fileName);
                    } else if (action === '下载') {
                        alert('下载文件：' + fileName);
                    }
                });
            });
        });
    </script>
</body>
</html>