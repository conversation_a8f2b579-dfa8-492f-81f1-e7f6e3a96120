<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知公告管理 - 招采平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .page-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        .page-header {
            background: white;
            color: #333;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-bottom: 1px solid #e8eaec;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }

        .page-breadcrumb {
            margin-top: 8px;
            opacity: 0.9;
            font-size: 14px;
        }

        .page-content {
            padding: 24px;
        }

        /* 查询区域样式 */
        .search-section {
            background: #fff;
            border: 1px solid #e8eaec;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .search-form {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: flex-end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }

        .form-label {
            font-size: 13px;
            color: #666;
            margin-bottom: 6px;
            font-weight: 500;
        }

        .form-control {
            height: 36px;
            padding: 8px 12px;
            border: 1px solid #dcdee2;
            border-radius: 4px;
            font-size: 13px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .form-control:focus {
            outline: none;
            border-color: #5cadff;
            box-shadow: 0 0 0 2px rgba(92, 173, 255, 0.2);
        }

        .select-control {
            cursor: pointer;
        }

        .date-range {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .date-separator {
            color: #999;
            font-size: 12px;
        }

        .search-buttons {
            display: flex;
            gap: 12px;
            margin-left: auto;
        }

        .btn {
            padding: 8px 20px;
            border: none;
            border-radius: 4px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            height: 36px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
        }

        .btn-primary {
            background: #5cadff;
            color: white;
        }

        .btn-primary:hover {
            background: #4a9eff;
            transform: translateY(-1px);
        }

        .btn-default {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #dcdee2;
        }

        .btn-default:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .btn-link {
            background: none;
            color: #5cadff;
            padding: 8px 0;
        }

        .btn-link:hover {
            color: #4a9eff;
            text-decoration: underline;
        }

        /* 高级查询 */
        .advanced-search {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #f0f0f0;
            display: none;
        }

        .advanced-search.show {
            display: block;
        }

        /* 功能按钮区域 */
        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 16px 20px;
            background: #fff;
            border: 1px solid #e8eaec;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .action-buttons {
            display: flex;
            gap: 12px;
        }

        .btn-success {
            background: #19be6b;
            color: white;
        }

        .btn-success:hover {
            background: #17a85a;
            transform: translateY(-1px);
        }

        .btn-warning {
            background: #ff9900;
            color: white;
        }

        .btn-warning:hover {
            background: #e68a00;
            transform: translateY(-1px);
        }

        .btn-danger {
            background: #ed4014;
            color: white;
        }

        .btn-danger:hover {
            background: #d73502;
            transform: translateY(-1px);
        }

        /* 数据表格样式 */
        .table-container {
            background: #fff;
            border: 1px solid #e8eaec;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
            table-layout: fixed;
        }

        .data-table th {
            background: #f8f9fa;
            color: #333;
            font-weight: 600;
            padding: 12px 15px;
            text-align: left;
            border-bottom: 2px solid #e8eaec;
            white-space: nowrap;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: middle;
            word-wrap: break-word;
        }

        .data-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .data-table tbody tr:last-child td {
            border-bottom: none;
        }

        /* 冻结列样式 */
        .frozen-left {
            position: sticky;
            left: 0;
            background: inherit;
            z-index: 5;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }

        .frozen-right {
            position: sticky;
            right: 0;
            background: inherit;
            z-index: 5;
            box-shadow: -2px 0 5px rgba(0,0,0,0.1);
        }

        .table-scroll {
            overflow-x: auto;
            max-width: 100%;
        }

        /* 状态标签 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
        }

        .status-pending {
            background: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }

        .status-published {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }

        /* 操作按钮 */
        .action-buttons-cell {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
            height: auto;
            min-height: 28px;
        }

        .btn-info {
            background: #2db7f5;
            color: white;
        }

        .btn-info:hover {
            background: #1890ff;
        }

        /* 分页样式 */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: #fff;
            border: 1px solid #e8eaec;
            border-top: none;
            border-radius: 0 0 6px 6px;
        }

        .pagination-info {
            color: #666;
            font-size: 13px;
        }

        .pagination {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .page-btn {
            padding: 6px 12px;
            border: 1px solid #dcdee2;
            background: #fff;
            color: #666;
            cursor: pointer;
            border-radius: 4px;
            font-size: 13px;
            transition: all 0.3s ease;
        }

        .page-btn:hover {
            border-color: #5cadff;
            color: #5cadff;
        }

        .page-btn.active {
            background: #5cadff;
            color: white;
            border-color: #5cadff;
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .page-container {
                margin: 0 20px;
            }
        }

        @media (max-width: 768px) {
            .search-form {
                flex-direction: column;
            }
            
            .form-group {
                min-width: 100%;
            }
            
            .search-buttons {
                margin-left: 0;
                margin-top: 10px;
            }
            
            .action-bar {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
        }

        /* 链接样式 */
        .link-primary {
            color: #5cadff;
            text-decoration: none;
            cursor: pointer;
        }

        .link-primary:hover {
            color: #4a9eff;
            text-decoration: underline;
        }

        /* 功能说明图标 */
        .help-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            background: #5cadff;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 16px;
            font-size: 12px;
            margin-left: 8px;
            cursor: help;
            position: relative;
        }

        .help-icon:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 120%;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .help-icon:hover::before {
            content: '';
            position: absolute;
            bottom: 110%;
            left: 50%;
            transform: translateX(-50%);
            border: 5px solid transparent;
            border-top-color: #333;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">
                通知公告管理
                <span class="help-icon" data-tooltip="支持对通知公告管理进行新增、编辑、删除、查询、发布、撤销、查看详情">?</span>
            </h1>
            <div class="page-breadcrumb">代理机构 > 通知公告管理</div>
        </div>

        <div class="page-content">
            <!-- 查询区域 -->
            <div class="search-section">
                <div class="search-form">
                    <div class="form-group">
                        <label class="form-label">标题名称</label>
                        <input type="text" class="form-control" placeholder="请输入标题名称（2位以上进行模糊查询）">
                    </div>
                    <div class="form-group">
                        <label class="form-label">发布状态</label>
                        <select class="form-control select-control">
                            <option value="">全部</option>
                            <option value="待发布">待发布</option>
                            <option value="已发布">已发布</option>
                        </select>
                    </div>
                    <div class="search-buttons">
                        <button class="btn btn-primary" onclick="searchNotices()">查询</button>
                        <button class="btn btn-default" onclick="resetSearch()">重置</button>
                        <button class="btn btn-link" onclick="toggleAdvancedSearch()">高级查询</button>
                    </div>
                </div>

                <!-- 高级查询区域 -->
                <div class="advanced-search" id="advancedSearch">
                    <div class="search-form">
                        <div class="form-group">
                            <label class="form-label">更新时间</label>
                            <div class="date-range">
                                <input type="date" class="form-control">
                                <span class="date-separator">至</span>
                                <input type="date" class="form-control">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">创建时间</label>
                            <div class="date-range">
                                <input type="date" class="form-control">
                                <span class="date-separator">至</span>
                                <input type="date" class="form-control">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 功能按钮区域 -->
            <div class="action-bar">
                <div class="action-buttons">
                    <button class="btn btn-success" onclick="createNotice()">新增通知公告</button>
                    <button class="btn btn-danger" onclick="batchDelete()">批量删除</button>
                </div>
            </div>

            <!-- 数据表格 -->
            <div class="table-container">
                <div class="table-scroll">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th style="width: 50px;" class="frozen-left">
                                    <input type="checkbox" onchange="toggleSelectAll(this)">
                                </th>
                                <th style="width: 300px;" class="frozen-left">标题名称</th>
                                <th style="width: 120px;">发布状态</th>
                                <th style="width: 120px;">更新人</th>
                                <th style="width: 150px;">更新时间</th>
                                <th style="width: 120px;">创建人</th>
                                <th style="width: 150px;">创建时间</th>
                                <th style="width: 200px;" class="frozen-right">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="frozen-left">
                                    <input type="checkbox" name="selectItem" value="1">
                                </td>
                                <td class="frozen-left">
                                    <a href="javascript:void(0)" class="link-primary" onclick="viewNoticeDetail('1')">关于加强招标采购管理的通知</a>
                                </td>
                                <td>
                                    <span class="status-badge status-published">已发布</span>
                                </td>
                                <td>张三</td>
                                <td>2024-03-15 14:30:25</td>
                                <td>李四</td>
                                <td>2024-03-10 09:15:30</td>
                                <td class="frozen-right">
                                    <div class="action-buttons-cell">
                                        <button class="btn btn-warning btn-sm" onclick="revokeNotice('1')">撤销</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="frozen-left">
                                    <input type="checkbox" name="selectItem" value="2">
                                </td>
                                <td class="frozen-left">
                                    <a href="javascript:void(0)" class="link-primary" onclick="viewNoticeDetail('2')">供应商资质审核要求说明</a>
                                </td>
                                <td>
                                    <span class="status-badge status-pending">待发布</span>
                                </td>
                                <td>王五</td>
                                <td>2024-03-14 16:45:12</td>
                                <td>王五</td>
                                <td>2024-03-14 16:45:12</td>
                                <td class="frozen-right">
                                    <div class="action-buttons-cell">
                                        <button class="btn btn-primary btn-sm" onclick="editNotice('2')">编辑</button>
                                        <button class="btn btn-success btn-sm" onclick="publishNotice('2')">发布</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="frozen-left">
                                    <input type="checkbox" name="selectItem" value="3">
                                </td>
                                <td class="frozen-left">
                                    <a href="javascript:void(0)" class="link-primary" onclick="viewNoticeDetail('3')">招标文件模板更新通知</a>
                                </td>
                                <td>
                                    <span class="status-badge status-published">已发布</span>
                                </td>
                                <td>赵六</td>
                                <td>2024-03-12 11:20:45</td>
                                <td>赵六</td>
                                <td>2024-03-12 10:30:15</td>
                                <td class="frozen-right">
                                    <div class="action-buttons-cell">
                                        <button class="btn btn-warning btn-sm" onclick="revokeNotice('3')">撤销</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="frozen-left">
                                    <input type="checkbox" name="selectItem" value="4">
                                </td>
                                <td class="frozen-left">
                                    <a href="javascript:void(0)" class="link-primary" onclick="viewNoticeDetail('4')">电子招投标平台操作指南</a>
                                </td>
                                <td>
                                    <span class="status-badge status-pending">待发布</span>
                                </td>
                                <td>孙七</td>
                                <td>2024-03-11 15:10:30</td>
                                <td>孙七</td>
                                <td>2024-03-11 15:10:30</td>
                                <td class="frozen-right">
                                    <div class="action-buttons-cell">
                                        <button class="btn btn-primary btn-sm" onclick="editNotice('4')">编辑</button>
                                        <button class="btn btn-success btn-sm" onclick="publishNotice('4')">发布</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="frozen-left">
                                    <input type="checkbox" name="selectItem" value="5">
                                </td>
                                <td class="frozen-left">
                                    <a href="javascript:void(0)" class="link-primary" onclick="viewNoticeDetail('5')">关于节假日期间招标活动安排的通知</a>
                                </td>
                                <td>
                                    <span class="status-badge status-published">已发布</span>
                                </td>
                                <td>周八</td>
                                <td>2024-03-08 09:30:20</td>
                                <td>周八</td>
                                <td>2024-03-08 09:30:20</td>
                                <td class="frozen-right">
                                    <div class="action-buttons-cell">
                                        <button class="btn btn-warning btn-sm" onclick="revokeNotice('5')">撤销</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 分页 -->
            <div class="pagination-container">
                <div class="pagination-info">
                    共 25 条记录，每页显示 10 条，共 3 页
                </div>
                <div class="pagination">
                    <button class="page-btn" disabled>上一页</button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">3</button>
                    <button class="page-btn">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 高级查询展开收起
        function toggleAdvancedSearch() {
            const advancedSearch = document.getElementById('advancedSearch');
            advancedSearch.classList.toggle('show');
        }

        // 查询功能
        function searchNotices() {
            console.log('执行查询操作');
            // 这里添加查询逻辑
        }

        // 重置查询
        function resetSearch() {
            document.querySelectorAll('.form-control').forEach(input => {
                if (input.type === 'text' || input.type === 'date') {
                    input.value = '';
                } else if (input.tagName === 'SELECT') {
                    input.selectedIndex = 0;
                }
            });
        }

        // 全选/取消全选
        function toggleSelectAll(checkbox) {
            const checkboxes = document.querySelectorAll('input[name="selectItem"]');
            checkboxes.forEach(cb => cb.checked = checkbox.checked);
        }

        // 页面跳转函数
        function openPage(url, title) {
            if (window.parent && window.parent !== window) {
                // 向父窗口发送消息，请求打开新页面
                window.parent.postMessage({
                    type: 'openPage',
                    title: title,
                    url: url
                }, '*');
            } else {
                window.open(url, '_blank');
            }
        }

        // 新增通知公告
        function createNotice() {
            openPage('通知公告管理-新建编辑页.html', '新增通知公告');
        }

        // 编辑通知公告
        function editNotice(id) {
            openPage(`通知公告管理-新建编辑页.html?id=${id}&mode=edit`, '编辑通知公告');
        }

        // 查看详情
        function viewNoticeDetail(id) {
            openPage(`通知公告管理-详情页.html?id=${id}`, '通知公告详情');
        }

        // 发布通知公告
        function publishNotice(id) {
            if (confirm('确认发布该通知公告吗？')) {
                console.log('发布通知公告:', id);
                // 这里添加发布逻辑
                alert('发布成功！');
                location.reload();
            }
        }

        // 撤销通知公告
        function revokeNotice(id) {
            if (confirm('确认撤销该通知公告吗？')) {
                console.log('撤销通知公告:', id);
                // 这里添加撤销逻辑
                alert('撤销成功！');
                location.reload();
            }
        }

        // 批量删除
        function batchDelete() {
            const selectedItems = document.querySelectorAll('input[name="selectItem"]:checked');
            if (selectedItems.length === 0) {
                alert('请选择要删除的记录');
                return;
            }
            
            if (confirm(`确认删除选中的 ${selectedItems.length} 条记录吗？`)) {
                console.log('批量删除:', Array.from(selectedItems).map(item => item.value));
                // 这里添加批量删除逻辑
                alert('删除成功！');
                location.reload();
            }
        }
    </script>
</body>
</html>