<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知公告管理 - 详情 - 招采平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .page-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        .page-header {
            background: white;
            color: #333;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-bottom: 1px solid #e8eaec;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .back-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
        }

        .page-breadcrumb {
            margin-top: 8px;
            opacity: 0.9;
            font-size: 14px;
        }

        .page-content {
            padding: 24px;
        }

        /* 页签样式 */
        .tabs-container {
            background: #fff;
            border: 1px solid #e8eaec;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .tabs-header {
            background: #f8f9fa;
            border-bottom: 1px solid #e8eaec;
            display: flex;
        }

        .tab-item {
            padding: 15px 24px;
            cursor: pointer;
            border-right: 1px solid #e8eaec;
            transition: all 0.3s ease;
            font-weight: 500;
            position: relative;
        }

        .tab-item:last-child {
            border-right: none;
        }

        .tab-item:hover {
            background: #e9ecef;
        }

        .tab-item.active {
            background: #fff;
            color: #5cadff;
            border-bottom: 2px solid #5cadff;
        }

        .tab-content {
            padding: 24px;
            min-height: 500px;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        /* 详情信息样式 */
        .detail-section {
            margin-bottom: 30px;
        }

        .detail-section:last-child {
            margin-bottom: 0;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
            position: relative;
        }

        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            bottom: -2px;
            width: 40px;
            height: 2px;
            background: #5cadff;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-item.full-width {
            grid-column: 1 / -1;
        }

        .detail-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .detail-value {
            font-size: 14px;
            color: #333;
            padding: 10px 12px;
            background: #f8f9fa;
            border: 1px solid #e8eaec;
            border-radius: 4px;
            min-height: 40px;
            display: flex;
            align-items: center;
        }

        .detail-value.rich-content {
            min-height: auto;
            padding: 15px;
            line-height: 1.6;
            align-items: flex-start;
        }

        .detail-value.rich-content p {
            margin-bottom: 10px;
        }

        .detail-value.rich-content p:last-child {
            margin-bottom: 0;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-badge.published {
            background: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }

        .status-badge.draft {
            background: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }

        /* 附件列表 */
        .attachment-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .attachment-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            background: #fff;
            border: 1px solid #e8eaec;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .attachment-item:hover {
            border-color: #5cadff;
            box-shadow: 0 2px 8px rgba(92, 173, 255, 0.1);
        }

        .attachment-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .attachment-icon {
            width: 32px;
            height: 32px;
            background: #5cadff;
            color: white;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
        }

        .attachment-details {
            display: flex;
            flex-direction: column;
        }

        .attachment-name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .attachment-size {
            font-size: 12px;
            color: #999;
        }

        .attachment-actions {
            display: flex;
            gap: 8px;
        }

        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-primary {
            background: #5cadff;
            color: white;
        }

        .btn-primary:hover {
            background: #4a9eff;
        }

        .btn-default {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #dcdee2;
        }

        .btn-default:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        /* 时间轴样式 */
        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e8eaec;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 24px;
            padding-bottom: 24px;
        }

        .timeline-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -23px;
            top: 8px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #5cadff;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #5cadff;
        }

        .timeline-content {
            background: #fff;
            border: 1px solid #e8eaec;
            border-radius: 6px;
            padding: 16px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .timeline-action {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        .timeline-time {
            font-size: 12px;
            color: #999;
        }

        .timeline-user {
            font-size: 13px;
            color: #666;
        }

        .timeline-desc {
            font-size: 13px;
            color: #666;
            margin-top: 8px;
            line-height: 1.5;
        }

        /* 操作按钮区域 */
        .detail-actions {
            background: #f8f9fa;
            padding: 20px 24px;
            border-top: 1px solid #e8eaec;
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .btn-success {
            background: #19be6b;
            color: white;
        }

        .btn-success:hover {
            background: #17a85a;
        }

        .btn-warning {
            background: #fa8c16;
            color: white;
        }

        .btn-warning:hover {
            background: #d46b08;
        }

        .btn-danger {
            background: #ed4014;
            color: white;
        }

        .btn-danger:hover {
            background: #d73502;
        }

        .btn {
            padding: 8px 16px;
            font-size: 14px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .detail-grid {
                grid-template-columns: 1fr;
            }
            
            .tabs-header {
                flex-direction: column;
            }
            
            .tab-item {
                border-right: none;
                border-bottom: 1px solid #e8eaec;
            }
            
            .detail-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">
                <a href="通知公告管理-列表页.html" class="back-btn">← 返回</a>
                通知公告详情
            </h1>
            <div class="page-breadcrumb">代理机构 > 通知公告管理 > 详情</div>
        </div>

        <div class="page-content">
            <div class="tabs-container">
                <!-- 页签头部 -->
                <div class="tabs-header">
                    <div class="tab-item active" onclick="switchTab('basic')">
                        基本信息
                    </div>
                    <div class="tab-item" onclick="switchTab('process')">
                        流程记录
                    </div>
                </div>

                <!-- 基本信息页签 -->
                <div class="tab-content">
                    <div class="tab-pane active" id="basicTab">
                        <!-- 通知公告信息 -->
                        <div class="detail-section">
                            <h3 class="section-title">通知公告信息</h3>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <div class="detail-label">标题名称</div>
                                    <div class="detail-value">关于加强招标采购管理的通知</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">发布状态</div>
                                    <div class="detail-value">
                                        <span class="status-badge published">已发布</span>
                                    </div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">创建人</div>
                                    <div class="detail-value">张三</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">创建时间</div>
                                    <div class="detail-value">2024-01-15 09:30:00</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">更新人</div>
                                    <div class="detail-value">李四</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">更新时间</div>
                                    <div class="detail-value">2024-01-16 14:20:00</div>
                                </div>
                                <div class="detail-item full-width">
                                    <div class="detail-label">通知公告说明</div>
                                    <div class="detail-value rich-content">
                                        <p>为进一步规范招标采购活动，提高采购效率和质量，现就有关事项通知如下：</p>
                                        <p><strong>一、严格执行招标采购程序</strong></p>
                                        <p>各单位要严格按照《招标投标法》等法律法规要求，规范执行招标采购程序，确保采购活动公开、公平、公正。</p>
                                        <p><strong>二、加强供应商资质审核</strong></p>
                                        <p>建立健全供应商资质审核机制，严格审查供应商的资质条件、业绩能力和信用状况。</p>
                                        <p><strong>三、完善评标专家管理</strong></p>
                                        <p>加强评标专家库建设，规范专家抽取和使用，提高评标工作质量。</p>
                                        <p><strong>四、强化监督检查</strong></p>
                                        <p>建立常态化监督检查机制，及时发现和纠正采购活动中的违法违规行为。</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 附件信息 -->
                        <div class="detail-section">
                            <h3 class="section-title">附件信息</h3>
                            <div class="detail-grid">
                                <div class="detail-item full-width">
                                    <div class="detail-label">附件列表</div>
                                    <div class="detail-value">
                                        <div class="attachment-list">
                                            <div class="attachment-item">
                                                <div class="attachment-info">
                                                    <div class="attachment-icon">PDF</div>
                                                    <div class="attachment-details">
                                                        <div class="attachment-name">招标采购管理办法.pdf</div>
                                                        <div class="attachment-size">2.5MB</div>
                                                    </div>
                                                </div>
                                                <div class="attachment-actions">
                                                    <button class="btn btn-primary" onclick="downloadFile('招标采购管理办法.pdf')">下载</button>
                                                    <button class="btn btn-default" onclick="previewFile('招标采购管理办法.pdf')">预览</button>
                                                </div>
                                            </div>
                                            <div class="attachment-item">
                                                <div class="attachment-info">
                                                    <div class="attachment-icon">DOC</div>
                                                    <div class="attachment-details">
                                                        <div class="attachment-name">供应商资质要求.docx</div>
                                                        <div class="attachment-size">1.2MB</div>
                                                    </div>
                                                </div>
                                                <div class="attachment-actions">
                                                    <button class="btn btn-primary" onclick="downloadFile('供应商资质要求.docx')">下载</button>
                                                    <button class="btn btn-default" onclick="previewFile('供应商资质要求.docx')">预览</button>
                                                </div>
                                            </div>
                                            <div class="attachment-item">
                                                <div class="attachment-info">
                                                    <div class="attachment-icon">XLS</div>
                                                    <div class="attachment-details">
                                                        <div class="attachment-name">评标专家名单.xlsx</div>
                                                        <div class="attachment-size">856KB</div>
                                                    </div>
                                                </div>
                                                <div class="attachment-actions">
                                                    <button class="btn btn-primary" onclick="downloadFile('评标专家名单.xlsx')">下载</button>
                                                    <button class="btn btn-default" onclick="previewFile('评标专家名单.xlsx')">预览</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 流程记录页签 -->
                    <div class="tab-pane" id="processTab">
                        <div class="detail-section">
                            <h3 class="section-title">操作记录</h3>
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-content">
                                        <div class="timeline-header">
                                            <div class="timeline-action">发布通知公告</div>
                                            <div class="timeline-time">2024-01-16 14:20:00</div>
                                        </div>
                                        <div class="timeline-user">操作人：李四</div>
                                        <div class="timeline-desc">将通知公告状态从"待发布"更改为"已发布"，通知公告正式生效。</div>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-content">
                                        <div class="timeline-header">
                                            <div class="timeline-action">编辑通知公告</div>
                                            <div class="timeline-time">2024-01-16 10:15:00</div>
                                        </div>
                                        <div class="timeline-user">操作人：李四</div>
                                        <div class="timeline-desc">修改了通知公告内容，增加了"强化监督检查"相关条款，并上传了新的附件文件。</div>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-content">
                                        <div class="timeline-header">
                                            <div class="timeline-action">审核通过</div>
                                            <div class="timeline-time">2024-01-15 16:45:00</div>
                                        </div>
                                        <div class="timeline-user">操作人：王五</div>
                                        <div class="timeline-desc">审核通过该通知公告，内容符合相关规定，可以发布。</div>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-content">
                                        <div class="timeline-header">
                                            <div class="timeline-action">提交审核</div>
                                            <div class="timeline-time">2024-01-15 14:30:00</div>
                                        </div>
                                        <div class="timeline-user">操作人：张三</div>
                                        <div class="timeline-desc">完成通知公告内容编写，提交给上级领导审核。</div>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-content">
                                        <div class="timeline-header">
                                            <div class="timeline-action">创建通知公告</div>
                                            <div class="timeline-time">2024-01-15 09:30:00</div>
                                        </div>
                                        <div class="timeline-user">操作人：张三</div>
                                        <div class="timeline-desc">创建新的通知公告，标题为"关于加强招标采购管理的通知"，状态为"待发布"。</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="detail-actions">
                    <button class="btn btn-primary" onclick="editNotice()">编辑</button>
                    <button class="btn btn-warning" onclick="revokeNotice()" id="revokeBtn">撤销</button>
                    <button class="btn btn-success" onclick="publishNotice()" id="publishBtn" style="display: none;">发布</button>
                    <button class="btn btn-danger" onclick="deleteNotice()">删除</button>
                    <button class="btn btn-default" onclick="printNotice()">打印</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
        });

        // 初始化页面
        function initializePage() {
            const urlParams = new URLSearchParams(window.location.search);
            const id = urlParams.get('id');
            
            if (id) {
                loadNoticeDetail(id);
            }
            
            updateActionButtons();
        }

        // 加载通知公告详情
        function loadNoticeDetail(id) {
            // 模拟加载数据
            console.log('加载通知公告详情，ID:', id);
        }

        // 切换页签
        function switchTab(tabName) {
            // 移除所有活动状态
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.remove('active');
            });
            
            // 激活当前页签
            event.target.classList.add('active');
            document.getElementById(tabName + 'Tab').classList.add('active');
        }

        // 更新操作按钮状态
        function updateActionButtons() {
            const status = '已发布'; // 从数据中获取状态
            const revokeBtn = document.getElementById('revokeBtn');
            const publishBtn = document.getElementById('publishBtn');
            
            if (status === '已发布') {
                revokeBtn.style.display = 'inline-flex';
                publishBtn.style.display = 'none';
            } else {
                revokeBtn.style.display = 'none';
                publishBtn.style.display = 'inline-flex';
            }
        }

        // 编辑通知公告
        function editNotice() {
            const urlParams = new URLSearchParams(window.location.search);
            const id = urlParams.get('id') || '1';
            window.location.href = `通知公告管理-新建编辑页.html?mode=edit&id=${id}`;
        }

        // 发布通知公告
        function publishNotice() {
            if (confirm('确认发布该通知公告吗？')) {
                console.log('发布通知公告');
                alert('发布成功！');
                location.reload();
            }
        }

        // 撤销通知公告
        function revokeNotice() {
            if (confirm('确认撤销该通知公告吗？撤销后将不再对外展示。')) {
                console.log('撤销通知公告');
                alert('撤销成功！');
                location.reload();
            }
        }

        // 删除通知公告
        function deleteNotice() {
            if (confirm('确认删除该通知公告吗？删除后无法恢复。')) {
                console.log('删除通知公告');
                alert('删除成功！');
                window.location.href = '通知公告管理-列表页.html';
            }
        }

        // 打印通知公告
        function printNotice() {
            window.print();
        }

        // 下载附件
        function downloadFile(fileName) {
            console.log('下载文件:', fileName);
            alert('开始下载文件: ' + fileName);
        }

        // 预览附件
        function previewFile(fileName) {
            console.log('预览文件:', fileName);
            alert('打开文件预览: ' + fileName);
        }
    </script>
</body>
</html>