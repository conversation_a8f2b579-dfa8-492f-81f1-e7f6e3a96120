<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理机构管理 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 页面头部 */
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .help-icon {
            cursor: pointer;
            color: #3498db;
            font-size: 18px;
            padding: 4px;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .help-icon:hover {
            background-color: #e3f2fd;
        }



        /* 查询区域 */
        .search-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 13px;
            color: #555;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-control {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .btn-group {
            display: flex;
            gap: 10px;
            align-items: end;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-outline {
            background: white;
            color: #3498db;
            border: 1px solid #3498db;
        }

        .btn-outline:hover {
            background: #3498db;
            color: white;
        }

        /* 高级查询 */
        .advanced-search {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        /* 功能操作栏 */
        .action-bar {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .action-left {
            display: flex;
            gap: 10px;
        }

        .action-right {
            color: #7f8c8d;
            font-size: 13px;
        }

        .result-count {
            font-weight: 500;
        }

        .result-count strong {
            color: #2c3e50;
        }

        /* 数据表格 */
        .data-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-container {
            overflow-x: auto;
            max-width: 100%;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            min-width: 1200px;
        }

        .data-table th,
        .data-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #eee;
            font-size: 13px;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        /* 列宽设置 */
        .col-checkbox { width: 50px; }
        .col-fixed { width: 200px; }
        .col-name { width: 250px; }
        .col-code { width: 150px; }
        .col-contact { width: 120px; }
        .col-phone { width: 150px; }
        .col-time { width: 140px; }
        .col-action { width: 120px; }

        /* 冻结列 */
        .col-checkbox,
        .col-fixed {
            position: sticky;
            left: 0;
            background: white;
            z-index: 5;
        }

        .col-checkbox {
            left: 0;
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
        }

        .col-fixed {
            left: 50px;
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
        }

        .col-action {
            position: sticky;
            right: 0;
            background: white;
            z-index: 5;
            box-shadow: -2px 0 4px rgba(0,0,0,0.1);
        }

        .data-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        /* 机构名称链接 */
        .agency-name-link {
            color: #3498db;
            text-decoration: none;
            font-weight: 500;
        }

        .agency-name-link:hover {
            text-decoration: underline;
        }

        /* 操作按钮 */
        .btn-action {
            background: none;
            border: none;
            cursor: pointer;
            padding: 4px 8px;
            margin: 0 2px;
            border-radius: 3px;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .btn-action:hover {
            background-color: #e3f2fd;
        }

        /* 分页 */
        .pagination-wrapper {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #eee;
        }

        .pagination-info {
            color: #7f8c8d;
            font-size: 13px;
        }

        .pagination {
            display: flex;
            gap: 5px;
        }

        .page-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            font-size: 13px;
            transition: all 0.3s;
        }

        .page-btn:hover {
            background: #f8f9fa;
        }

        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .help-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .help-close {
            position: absolute;
            right: 15px;
            top: 15px;
            font-size: 24px;
            cursor: pointer;
            color: #aaa;
        }

        .help-close:hover {
            color: #333;
        }

        .help-title {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 20px;
        }

        .help-content h4 {
            color: #34495e;
            margin: 15px 0 10px 0;
            font-size: 16px;
        }

        .help-content ul {
            margin-left: 20px;
            margin-bottom: 15px;
        }

        .help-content li {
            margin-bottom: 8px;
            line-height: 1.6;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .search-form {
                grid-template-columns: 1fr;
            }

            .action-bar {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .action-left {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">
                    代理机构管理
                    <span class="help-icon" onclick="showHelp()" title="查看帮助">❓</span>
                </h1>

            </div>
        </div>

        <!-- 查询区域 -->
        <div class="search-section">
            <div class="search-form">
                <div class="form-group">
                    <label class="form-label">代理机构名称</label>
                    <input type="text" class="form-control" placeholder="请输入代理机构名称">
                </div>
                <div class="form-group">
                    <label class="form-label">企业代码</label>
                    <input type="text" class="form-control" placeholder="请输入企业代码">
                </div>
                <div class="form-group">
                    <label class="form-label">联系人</label>
                    <input type="text" class="form-control" placeholder="请输入联系人">
                </div>
                <div class="form-group">
                    <label class="form-label">联系方式</label>
                    <input type="text" class="form-control" placeholder="请输入联系方式">
                </div>
                <div class="btn-group">
                    <button class="btn btn-primary">查询</button>
                    <button class="btn btn-secondary">重置</button>
                    <button class="btn btn-outline" onclick="toggleAdvancedSearch()">高级查询</button>
                </div>
            </div>
            
            <!-- 高级查询区域 -->
            <div class="advanced-search" id="advanced-search" style="display: none;">
                <div class="search-form">
                    <div class="form-group">
                        <label class="form-label">创建时间</label>
                        <input type="date" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label">至</label>
                        <input type="date" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label">更新时间</label>
                        <input type="date" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label">至</label>
                        <input type="date" class="form-control">
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能操作栏 -->
        <div class="action-bar">
            <div class="action-left">
                <button class="btn btn-primary" onclick="createAgency()">新增代理机构</button>
                <button class="btn btn-default">批量导入</button>
                <button class="btn btn-success">导出数据</button>
                <button class="btn btn-danger">批量删除</button>
            </div>
            <div class="action-right">
                <span class="result-count">共找到 <strong>128</strong> 条记录</span>
            </div>
        </div>

        <!-- 数据列表 -->
        <div class="data-section">
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th class="col-checkbox">
                                <input type="checkbox" id="select-all">
                            </th>
                            <th class="col-fixed">代理机构名称</th>
                            <th class="col-code">企业代码</th>
                            <th class="col-contact">联系人</th>
                            <th class="col-phone">联系方式</th>
                            <th class="col-time">更新时间</th>
                            <th class="col-time">创建时间</th>
                            <th class="col-action">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><input type="checkbox"></td>
                            <td class="col-fixed"><a href="javascript:void(0)" onclick="viewDetail(1)" class="agency-name-link">中建工程咨询有限公司</a></td>
                            <td>91110000123456789X</td>
                            <td>张经理</td>
                            <td>13800138001</td>
                            <td>2024-01-15 14:30</td>
                            <td>2023-06-20 09:15</td>
                            <td class="col-action">
                                <div class="operation-buttons">
                                    <!-- 基础数据管理，无变更按钮 -->
                                    <button class="op-btn op-btn-warning" title="编辑">编辑</button>
                                    <button class="op-btn op-btn-danger" title="删除">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox"></td>
                            <td class="col-fixed"><a href="javascript:void(0)" onclick="viewDetail(2)" class="agency-name-link">华信工程管理集团</a></td>
                            <td>91110000987654321Y</td>
                            <td>李主任</td>
                            <td>13900139002</td>
                            <td>2024-01-14 16:45</td>
                            <td>2023-05-18 11:20</td>
                            <td class="col-action">
                                <div class="operation-buttons">
                                    <!-- 基础数据管理，无变更按钮 -->
                                    <button class="op-btn op-btn-warning" title="编辑">编辑</button>
                                    <button class="op-btn op-btn-danger" title="删除">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox"></td>
                            <td class="col-fixed"><a href="javascript:void(0)" onclick="viewDetail(3)" class="agency-name-link">北京建设工程咨询公司</a></td>
                            <td>91110000456789123Z</td>
                            <td>王总监</td>
                            <td>13700137003</td>
                            <td>2024-01-13 10:20</td>
                            <td>2023-04-25 15:30</td>
                            <td class="col-action">
                                <div class="operation-buttons">
                                    <!-- 基础数据管理，无变更按钮 -->
                                    <button class="op-btn op-btn-warning" title="编辑">编辑</button>
                                    <button class="op-btn op-btn-danger" title="删除">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox"></td>
                            <td class="col-fixed"><a href="javascript:void(0)" onclick="viewDetail(4)" class="agency-name-link">上海工程项目管理有限公司</a></td>
                            <td>91310000789123456A</td>
                            <td>陈经理</td>
                            <td>13600136004</td>
                            <td>2024-01-12 09:15</td>
                            <td>2023-03-10 14:45</td>
                            <td class="col-action">
                                <div class="operation-buttons">
                                    <!-- 基础数据管理，无变更按钮 -->
                                    <button class="op-btn op-btn-warning" title="编辑">编辑</button>
                                    <button class="op-btn op-btn-danger" title="删除">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox"></td>
                            <td class="col-fixed"><a href="javascript:void(0)" onclick="viewDetail(5)" class="agency-name-link">广东建筑工程咨询集团</a></td>
                            <td>91440000321654987B</td>
                            <td>刘部长</td>
                            <td>13500135005</td>
                            <td>2024-01-11 15:40</td>
                            <td>2023-02-15 10:25</td>
                            <td class="col-action">
                                <div class="operation-buttons">
                                    <!-- 基础数据管理，无变更按钮 -->
                                    <button class="op-btn op-btn-warning" title="编辑">编辑</button>
                                    <button class="op-btn op-btn-danger" title="删除">删除</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="pagination-wrapper">
                <div class="pagination-info">
                    显示第 1-20 条，共 128 条记录
                </div>
                <div class="pagination">
                    <button class="page-btn">上一页</button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">3</button>
                    <button class="page-btn">...</button>
                    <button class="page-btn">7</button>
                    <button class="page-btn">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="helpModal" class="help-modal">
        <div class="help-content">
            <span class="help-close" onclick="hideHelp()">&times;</span>
            <h3 class="help-title">代理机构管理说明</h3>
            <p>代理机构管理用于维护招标代理机构的基本信息，包括机构资质、联系方式等。</p>
            
            <h4>功能说明</h4>
            <ul>
                <li><strong>新增代理机构：</strong>添加新的招标代理机构信息</li>
                <li><strong>批量导入：</strong>通过Excel文件批量导入代理机构信息</li>
                <li><strong>导出数据：</strong>将代理机构列表导出为Excel文件</li>
                <li><strong>批量删除：</strong>选择多个代理机构进行批量删除操作</li>
            </ul>
            
            <h4>查询条件</h4>
            <ul>
                <li><strong>代理机构名称：</strong>支持模糊查询机构名称</li>
                <li><strong>企业代码：</strong>统一社会信用代码或组织机构代码</li>
                <li><strong>联系人：</strong>机构主要联系人姓名</li>
                <li><strong>联系方式：</strong>联系电话或邮箱</li>
                <li><strong>时间范围：</strong>支持按创建时间和更新时间筛选</li>
            </ul>
            
            <h4>操作说明</h4>
            <ul>
                <li><strong>编辑：</strong>修改代理机构的基本信息</li>
                <li><strong>删除：</strong>删除不再使用的代理机构（需确认无关联项目）</li>
                <li><strong>查看详情：</strong>查看代理机构详细信息和项目总览</li>
            </ul>
        </div>
    </div>

    <script>
        // 高级查询切换
        function toggleAdvancedSearch() {
            const advancedSearch = document.getElementById('advanced-search');
            if (advancedSearch.style.display === 'none') {
                advancedSearch.style.display = 'block';
            } else {
                advancedSearch.style.display = 'none';
            }
        }

        // 全选功能
        document.getElementById('select-all').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // 查看详情
        function viewDetail(id) {
            // 跳转到详情页
            window.location.href = `代理机构管理-详情页.html?id=${id}`;
        }

        // 新增代理机构
        function createAgency() {
            // 跳转到新建编辑页
            window.location.href = '代理机构管理-新建编辑页.html';
        }

        // 帮助弹窗
        function showHelp() {
            document.getElementById('helpModal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('helpModal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('helpModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>