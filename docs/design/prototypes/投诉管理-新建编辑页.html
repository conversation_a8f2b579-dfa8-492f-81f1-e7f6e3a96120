<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>投诉管理 - 新建/编辑</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* 页面标题区域 */
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .back-btn {
            padding: 8px 16px;
            background: #f5f5f5;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            color: #666;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .back-btn:hover {
            background: #e6f7ff;
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .page-title {
            font-size: 18px;
            font-weight: bold;
            color: #1c4e80;
        }
        
        /* 表单区域 */
        .form-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .section-header {
            background: #fafafa;
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            font-weight: 600;
            color: #262626;
            position: relative;
        }
        
        .section-header::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #1890ff;
        }
        
        .form-content {
            padding: 20px;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-group label {
            font-size: 13px;
            color: #262626;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .form-group label .required {
            color: #ff4d4f;
            margin-left: 2px;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 13px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .form-group input:disabled,
        .form-group select:disabled {
            background-color: #f5f5f5;
            color: #bfbfbf;
            cursor: not-allowed;
        }
        
        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        /* 选择按钮样式 */
        .select-btn {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 13px;
            text-align: left;
            color: #666;
            transition: all 0.3s;
            position: relative;
        }
        
        .select-btn:hover {
            border-color: #1890ff;
        }
        
        .select-btn::after {
            content: '选择';
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #1890ff;
            font-size: 12px;
        }
        
        .select-btn.selected {
            color: #333;
        }
        
        .select-btn.selected::after {
            content: '重新选择';
        }
        
        /* 文件上传 */
        .file-upload {
            border: 2px dashed #d9d9d9;
            border-radius: 4px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .file-upload:hover {
            border-color: #1890ff;
            background: #fafafa;
        }
        
        .file-upload input {
            display: none;
        }
        
        .file-upload-text {
            color: #666;
            font-size: 13px;
        }
        
        .file-list {
            margin-top: 10px;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: #f5f5f5;
            border-radius: 4px;
            margin-bottom: 8px;
        }
        
        .file-name {
            font-size: 13px;
            color: #333;
        }
        
        .file-remove {
            color: #ff4d4f;
            cursor: pointer;
            font-size: 12px;
        }
        
        /* 富文本编辑器样式 */
        .rich-editor {
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .editor-toolbar {
            background: #fafafa;
            padding: 8px 12px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            gap: 8px;
        }
        
        .editor-btn {
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            background: white;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            color: #666;
        }
        
        .editor-btn:hover {
            background: #e6f7ff;
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .editor-content {
            min-height: 150px;
            padding: 12px;
            outline: none;
        }
        
        /* 操作按钮 */
        .form-actions {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        
        .btn {
            padding: 10px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            min-width: 100px;
        }
        
        .btn-primary {
            background: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
        }
        
        .btn-default {
            background: #f5f5f5;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .btn-default:hover {
            background: #e6f7ff;
            border-color: #1890ff;
            color: #1890ff;
        }
        
        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 1000px;
            max-height: 80%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #999;
        }
        
        .modal-close:hover {
            color: #666;
        }
        
        .modal-body {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }
        
        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        /* 弹窗内表格 */
        .modal-search {
            margin-bottom: 20px;
        }
        
        .modal-search .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .modal-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }
        
        .modal-table th,
        .modal-table td {
            padding: 10px 8px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .modal-table th {
            background: #fafafa;
            font-weight: 600;
            color: #262626;
        }
        
        .modal-table tbody tr:hover {
            background-color: #f5f5f5;
        }
        
        .modal-table tbody tr.selected {
            background-color: #e6f7ff;
        }
        
        /* 响应式 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .form-actions {
                flex-direction: column;
            }
            
            .modal-content {
                width: 95%;
                max-height: 90%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <a href="javascript:history.back()" class="back-btn">← 返回</a>
            <h1 class="page-title" id="pageTitle">新建投诉</h1>
        </div>
        
        <!-- 表单区域 -->
        <div class="form-section">
            <div class="section-header">投诉信息</div>
            <div class="form-content">
                <form id="complaintForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="segmentSelect">选择标段<span class="required">*</span></label>
                            <div class="select-btn" id="segmentSelect" onclick="openSegmentModal()">
                                请选择标段
                            </div>
                            <input type="hidden" id="segmentId" name="segmentId" required>
                        </div>
                        <div class="form-group">
                            <label for="projectName">所属项目</label>
                            <input type="text" id="projectName" name="projectName" readonly>
                        </div>
                        <div class="form-group">
                            <label for="companyName">项目所属企业</label>
                            <input type="text" id="companyName" name="companyName" readonly>
                        </div>
                        <div class="form-group">
                            <label for="complaintTime">投诉时间<span class="required">*</span></label>
                            <input type="date" id="complaintTime" name="complaintTime" required>
                        </div>
                        <div class="form-group">
                            <label for="complaintSource">投诉来源<span class="required">*</span></label>
                            <select id="complaintSource" name="complaintSource" required>
                                <option value="">请选择</option>
                                <option value="书面异议" selected>书面异议</option>
                                <option value="书面投诉">书面投诉</option>
                                <option value="网络理政">网络理政</option>
                                <option value="纪委转办">纪委转办</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="isHandled">是否处理<span class="required">*</span></label>
                            <select id="isHandled" name="isHandled" required>
                                <option value="是" selected>是</option>
                                <option value="否">否</option>
                            </select>
                        </div>
                        <div class="form-group full-width">
                            <label for="complaintContent">投诉内容<span class="required">*</span></label>
                            <div class="rich-editor">
                                <div class="editor-toolbar">
                                    <button type="button" class="editor-btn" onclick="formatText('bold')">粗体</button>
                                    <button type="button" class="editor-btn" onclick="formatText('italic')">斜体</button>
                                    <button type="button" class="editor-btn" onclick="formatText('underline')">下划线</button>
                                    <button type="button" class="editor-btn" onclick="formatText('insertOrderedList')">有序列表</button>
                                    <button type="button" class="editor-btn" onclick="formatText('insertUnorderedList')">无序列表</button>
                                </div>
                                <div class="editor-content" id="complaintContent" contenteditable="true" placeholder="请输入投诉内容..."></div>
                            </div>
                        </div>
                        <div class="form-group full-width">
                            <label for="attachments">处理结果附件</label>
                            <div class="file-upload" onclick="document.getElementById('fileInput').click()">
                                <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                <div class="file-upload-text">
                                    点击上传文件或拖拽文件到此处<br>
                                    <small>支持 PDF、Word、图片格式，单个文件不超过10MB</small>
                                </div>
                            </div>
                            <div class="file-list" id="fileList"></div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="form-actions">
            <button type="button" class="btn btn-primary" onclick="saveComplaint()">保存</button>
            <button type="button" class="btn btn-default" onclick="history.back()">取消</button>
        </div>
    </div>
    
    <!-- 选择标段弹窗 -->
    <div class="modal" id="segmentModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">请选择标段</div>
                <button class="modal-close" onclick="closeSegmentModal()">×</button>
            </div>
            <div class="modal-body">
                <div class="modal-search">
                    <div class="search-form">
                        <div class="form-group">
                            <label for="searchSegmentName">标段名称</label>
                            <input type="text" id="searchSegmentName" placeholder="请输入标段名称">
                        </div>
                        <div class="form-group">
                            <label for="searchProjectName">项目名称</label>
                            <input type="text" id="searchProjectName" placeholder="请输入项目名称">
                        </div>
                        <div class="form-group">
                            <label for="searchProcurementMethod">采购方式</label>
                            <select id="searchProcurementMethod">
                                <option value="">请选择</option>
                                <option value="公开招标">公开招标</option>
                                <option value="邀请招标">邀请招标</option>
                                <option value="竞争性谈判">竞争性谈判</option>
                                <option value="单一来源">单一来源</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="searchProcurementType">采购类型</label>
                            <select id="searchProcurementType">
                                <option value="">请选择</option>
                                <option value="工程">工程</option>
                                <option value="货物">货物</option>
                                <option value="服务">服务</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="searchCreateTimeStart">创建时间（开始）</label>
                            <input type="date" id="searchCreateTimeStart">
                        </div>
                        <div class="form-group">
                            <label for="searchCreateTimeEnd">创建时间（结束）</label>
                            <input type="date" id="searchCreateTimeEnd">
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <button type="button" class="btn btn-primary" onclick="searchSegments()">查询</button>
                        <button type="button" class="btn btn-default" onclick="resetSegmentSearch()">重置</button>
                    </div>
                </div>
                
                <table class="modal-table">
                    <thead>
                        <tr>
                            <th width="30"></th>
                            <th>标段名称</th>
                            <th>所属项目</th>
                            <th>采购方式</th>
                            <th>采购类型</th>
                            <th>创建时间</th>
                        </tr>
                    </thead>
                    <tbody id="segmentTableBody">
                        <!-- 示例数据 -->
                        <tr onclick="selectSegment(this, 1, 'XX项目第一标段', 'XX基础设施建设项目', 'XX建设集团有限公司')">
                            <td><input type="radio" name="segmentRadio" value="1"></td>
                            <td>XX项目第一标段</td>
                            <td>XX基础设施建设项目</td>
                            <td>公开招标</td>
                            <td>工程</td>
                            <td>2024-01-10</td>
                        </tr>
                        <tr onclick="selectSegment(this, 2, 'YY工程第二标段', 'YY市政工程项目', 'YY工程有限责任公司')">
                            <td><input type="radio" name="segmentRadio" value="2"></td>
                            <td>YY工程第二标段</td>
                            <td>YY市政工程项目</td>
                            <td>邀请招标</td>
                            <td>工程</td>
                            <td>2024-01-08</td>
                        </tr>
                        <tr onclick="selectSegment(this, 3, 'ZZ项目第三标段', 'ZZ园区建设项目', 'ZZ建筑工程有限公司')">
                            <td><input type="radio" name="segmentRadio" value="3"></td>
                            <td>ZZ项目第三标段</td>
                            <td>ZZ园区建设项目</td>
                            <td>竞争性谈判</td>
                            <td>工程</td>
                            <td>2024-01-05</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="confirmSegmentSelection()">确认</button>
                <button type="button" class="btn btn-default" onclick="closeSegmentModal()">取消</button>
            </div>
        </div>
    </div>
    
    <script>
        let selectedSegment = null;
        let uploadedFiles = [];
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否为编辑模式
            const urlParams = new URLSearchParams(window.location.search);
            const id = urlParams.get('id');
            
            if (id) {
                document.getElementById('pageTitle').textContent = '编辑投诉';
                loadComplaintData(id);
            }
            
            // 设置默认投诉时间为今天
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('complaintTime').value = today;
        });
        
        // 加载投诉数据（编辑模式）
        function loadComplaintData(id) {
            // 这里应该调用后端API获取数据
            console.log('加载投诉数据:', id);
            
            // 模拟数据
            const mockData = {
                segmentId: 1,
                segmentName: 'XX项目第一标段',
                projectName: 'XX基础设施建设项目',
                companyName: 'XX建设集团有限公司',
                complaintTime: '2024-01-15',
                complaintSource: '书面异议',
                isHandled: '否',
                complaintContent: '关于XX项目第一标段的投诉内容...'
            };
            
            // 填充表单数据
            document.getElementById('segmentId').value = mockData.segmentId;
            document.getElementById('segmentSelect').textContent = mockData.segmentName;
            document.getElementById('segmentSelect').classList.add('selected');
            document.getElementById('projectName').value = mockData.projectName;
            document.getElementById('companyName').value = mockData.companyName;
            document.getElementById('complaintTime').value = mockData.complaintTime;
            document.getElementById('complaintSource').value = mockData.complaintSource;
            document.getElementById('isHandled').value = mockData.isHandled;
            document.getElementById('complaintContent').innerHTML = mockData.complaintContent;
        }
        
        // 打开标段选择弹窗
        function openSegmentModal() {
            document.getElementById('segmentModal').classList.add('show');
        }
        
        // 关闭标段选择弹窗
        function closeSegmentModal() {
            document.getElementById('segmentModal').classList.remove('show');
            selectedSegment = null;
            // 清除选中状态
            document.querySelectorAll('#segmentTableBody tr').forEach(tr => {
                tr.classList.remove('selected');
                tr.querySelector('input[type="radio"]').checked = false;
            });
        }
        
        // 选择标段
        function selectSegment(row, id, segmentName, projectName, companyName) {
            // 清除之前的选中状态
            document.querySelectorAll('#segmentTableBody tr').forEach(tr => {
                tr.classList.remove('selected');
                tr.querySelector('input[type="radio"]').checked = false;
            });
            
            // 设置当前选中状态
            row.classList.add('selected');
            row.querySelector('input[type="radio"]').checked = true;
            
            selectedSegment = {
                id: id,
                segmentName: segmentName,
                projectName: projectName,
                companyName: companyName
            };
        }
        
        // 确认标段选择
        function confirmSegmentSelection() {
            if (!selectedSegment) {
                alert('请选择一个标段');
                return;
            }
            
            // 填充表单数据
            document.getElementById('segmentId').value = selectedSegment.id;
            document.getElementById('segmentSelect').textContent = selectedSegment.segmentName;
            document.getElementById('segmentSelect').classList.add('selected');
            document.getElementById('projectName').value = selectedSegment.projectName;
            document.getElementById('companyName').value = selectedSegment.companyName;
            
            closeSegmentModal();
        }
        
        // 查询标段
        function searchSegments() {
            // 这里应该调用后端API进行查询
            console.log('查询标段');
            alert('查询功能待实现');
        }
        
        // 重置标段查询
        function resetSegmentSearch() {
            document.getElementById('searchSegmentName').value = '';
            document.getElementById('searchProjectName').value = '';
            document.getElementById('searchProcurementMethod').value = '';
            document.getElementById('searchProcurementType').value = '';
            document.getElementById('searchCreateTimeStart').value = '';
            document.getElementById('searchCreateTimeEnd').value = '';
        }
        
        // 富文本编辑器格式化
        function formatText(command) {
            document.execCommand(command, false, null);
            document.getElementById('complaintContent').focus();
        }
        
        // 文件上传处理
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            
            files.forEach(file => {
                // 检查文件大小（10MB限制）
                if (file.size > 10 * 1024 * 1024) {
                    alert(`文件 ${file.name} 超过10MB限制`);
                    return;
                }
                
                // 检查文件类型
                const allowedTypes = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png'];
                const fileExt = '.' + file.name.split('.').pop().toLowerCase();
                if (!allowedTypes.includes(fileExt)) {
                    alert(`文件 ${file.name} 格式不支持`);
                    return;
                }
                
                uploadedFiles.push(file);
                addFileToList(file);
            });
            
            // 清空input值，允许重复选择同一文件
            e.target.value = '';
        });
        
        // 添加文件到列表
        function addFileToList(file) {
            const fileList = document.getElementById('fileList');
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <span class="file-name">${file.name}</span>
                <span class="file-remove" onclick="removeFile('${file.name}')">删除</span>
            `;
            fileList.appendChild(fileItem);
        }
        
        // 删除文件
        function removeFile(fileName) {
            uploadedFiles = uploadedFiles.filter(file => file.name !== fileName);
            
            // 从DOM中移除
            const fileItems = document.querySelectorAll('.file-item');
            fileItems.forEach(item => {
                if (item.querySelector('.file-name').textContent === fileName) {
                    item.remove();
                }
            });
        }
        
        // 保存投诉
        function saveComplaint() {
            // 表单验证
            const form = document.getElementById('complaintForm');
            const formData = new FormData(form);
            
            // 检查必填字段
            if (!document.getElementById('segmentId').value) {
                alert('请选择标段');
                return;
            }
            
            if (!document.getElementById('complaintTime').value) {
                alert('请选择投诉时间');
                return;
            }
            
            if (!document.getElementById('complaintSource').value) {
                alert('请选择投诉来源');
                return;
            }
            
            if (!document.getElementById('isHandled').value) {
                alert('请选择是否处理');
                return;
            }
            
            const complaintContent = document.getElementById('complaintContent').innerHTML.trim();
            if (!complaintContent || complaintContent === '<br>') {
                alert('请输入投诉内容');
                return;
            }
            
            // 添加富文本内容
            formData.append('complaintContent', complaintContent);
            
            // 添加文件
            uploadedFiles.forEach(file => {
                formData.append('attachments', file);
            });
            
            // 这里应该调用后端API保存数据
            console.log('保存投诉数据:', Object.fromEntries(formData));
            console.log('上传文件:', uploadedFiles);
            
            alert('保存成功');
            history.back();
        }
        
        // 阻止弹窗内容区域点击事件冒泡
        document.querySelector('.modal-content').addEventListener('click', function(e) {
            e.stopPropagation();
        });
        
        // 点击弹窗背景关闭弹窗
        document.getElementById('segmentModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeSegmentModal();
            }
        });
    </script>
</body>
</html>