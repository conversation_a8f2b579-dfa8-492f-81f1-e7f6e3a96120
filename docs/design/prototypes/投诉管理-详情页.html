<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>投诉管理 - 详情</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* 页面标题区域 */
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .back-btn {
            padding: 8px 16px;
            background: #f5f5f5;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            color: #666;
            text-decoration: none;
            transition: all 0.3s;
        }
        
        .back-btn:hover {
            background: #e6f7ff;
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .page-title {
            font-size: 18px;
            font-weight: bold;
            color: #1c4e80;
        }
        
        .header-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
        }
        
        .btn-success {
            background: #52c41a;
            color: white;
        }
        
        .btn-success:hover {
            background: #73d13d;
        }
        
        .btn-danger {
            background: #ff4d4f;
            color: white;
        }
        
        .btn-danger:hover {
            background: #ff7875;
        }
        
        /* 页签区域 */
        .tabs-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .tabs-header {
            display: flex;
            background: #fafafa;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .tab-item {
            padding: 15px 20px;
            cursor: pointer;
            border-right: 1px solid #f0f0f0;
            transition: all 0.3s;
            font-weight: 500;
            color: #666;
        }
        
        .tab-item:last-child {
            border-right: none;
        }
        
        .tab-item.active {
            background: white;
            color: #1890ff;
            border-bottom: 2px solid #1890ff;
            margin-bottom: -1px;
        }
        
        .tab-item:hover:not(.active) {
            background: #f0f0f0;
            color: #333;
        }
        
        .tab-content {
            padding: 20px;
        }
        
        .tab-pane {
            display: none;
        }
        
        .tab-pane.active {
            display: block;
        }
        
        /* 详情信息区域 */
        .detail-section {
            margin-bottom: 30px;
        }
        
        .section-header {
            background: #fafafa;
            padding: 15px 20px;
            border-radius: 8px 8px 0 0;
            border-bottom: 1px solid #f0f0f0;
            font-weight: 600;
            color: #262626;
            position: relative;
            margin: 0 -20px 20px -20px;
        }
        
        .section-header::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #1890ff;
        }
        
        .detail-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .detail-item {
            display: flex;
            flex-direction: column;
        }
        
        .detail-item.full-width {
            grid-column: 1 / -1;
        }
        
        .detail-label {
            font-size: 13px;
            color: #666;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .detail-value {
            font-size: 14px;
            color: #333;
            word-wrap: break-word;
        }
        
        .detail-value.empty {
            color: #bfbfbf;
            font-style: italic;
        }
        
        /* 状态标签 */
        .status-tag {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-handled {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .status-unhandled {
            background: #fff2e8;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }
        
        /* 富文本内容 */
        .rich-content {
            background: #fafafa;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #f0f0f0;
            min-height: 100px;
            line-height: 1.8;
        }
        
        /* 附件列表 */
        .attachment-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .attachment-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 15px;
            background: #f5f5f5;
            border-radius: 4px;
            border: 1px solid #e8e8e8;
        }
        
        .attachment-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .attachment-icon {
            width: 24px;
            height: 24px;
            background: #1890ff;
            color: white;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .attachment-name {
            font-size: 13px;
            color: #333;
        }
        
        .attachment-size {
            font-size: 12px;
            color: #999;
        }
        
        .attachment-download {
            color: #1890ff;
            cursor: pointer;
            font-size: 13px;
            text-decoration: none;
        }
        
        .attachment-download:hover {
            color: #40a9ff;
            text-decoration: underline;
        }
        
        /* 操作记录 */
        .operation-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .operation-item {
            padding: 15px;
            background: #fafafa;
            border-radius: 8px;
            border-left: 4px solid #1890ff;
        }
        
        .operation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .operation-action {
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }
        
        .operation-time {
            font-size: 12px;
            color: #999;
        }
        
        .operation-user {
            font-size: 13px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .operation-content {
            font-size: 13px;
            color: #666;
            line-height: 1.6;
        }
        
        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #999;
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }
        
        .empty-text {
            font-size: 14px;
        }
        
        /* 响应式 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
            
            .header-actions {
                justify-content: center;
            }
            
            .detail-grid {
                grid-template-columns: 1fr;
            }
            
            .tabs-header {
                flex-direction: column;
            }
            
            .tab-item {
                border-right: none;
                border-bottom: 1px solid #f0f0f0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="header-left">
                <a href="javascript:history.back()" class="back-btn">← 返回</a>
                <h1 class="page-title">投诉详情</h1>
            </div>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="editComplaint()">编辑</button>
                <button class="btn btn-success" onclick="processComplaint()" id="processBtn">处理</button>
                <button class="btn btn-danger" onclick="deleteComplaint()">删除</button>
            </div>
        </div>
        
        <!-- 页签内容 -->
        <div class="tabs-container">
            <div class="tabs-header">
                <div class="tab-item active" onclick="switchTab('basic')">基本信息</div>
                <div class="tab-item" onclick="switchTab('operations')">操作记录</div>
            </div>
            
            <div class="tab-content">
                <!-- 基本信息页签 -->
                <div class="tab-pane active" id="basicTab">
                    <div class="detail-section">
                        <div class="section-header">投诉信息</div>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">投诉来源</div>
                                <div class="detail-value" id="complaintSource">书面异议</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">投诉标段</div>
                                <div class="detail-value" id="complaintSegment">XX项目第一标段</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">投诉企业</div>
                                <div class="detail-value" id="complaintCompany">XX建设集团有限公司</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">所属项目</div>
                                <div class="detail-value" id="projectName">XX基础设施建设项目</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">是否处理</div>
                                <div class="detail-value">
                                    <span class="status-tag status-unhandled" id="handledStatus">否</span>
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">投诉时间</div>
                                <div class="detail-value" id="complaintTime">2024-01-15</div>
                            </div>
                            <div class="detail-item full-width">
                                <div class="detail-label">投诉内容</div>
                                <div class="detail-value">
                                    <div class="rich-content" id="complaintContent">
                                        关于XX项目第一标段的投诉内容，涉及招标过程中的相关问题。具体包括：<br>
                                        1. 招标文件中的技术要求不够明确<br>
                                        2. 评标过程中存在不公平现象<br>
                                        3. 中标结果公示时间不足<br><br>
                                        希望相关部门能够重视并妥善处理此次投诉。
                                    </div>
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">创建时间</div>
                                <div class="detail-value" id="createTime">2024-01-15 10:30:25</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">创建人</div>
                                <div class="detail-value" id="creator">张三</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">最后更新时间</div>
                                <div class="detail-value" id="updateTime">2024-01-15 10:30:25</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="detail-section">
                        <div class="section-header">处理结果</div>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">处理状态</div>
                                <div class="detail-value">
                                    <span class="status-tag status-unhandled" id="processStatus">待处理</span>
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">处理人</div>
                                <div class="detail-value empty" id="processor">暂无</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">处理时间</div>
                                <div class="detail-value empty" id="processTime">暂无</div>
                            </div>
                            <div class="detail-item full-width">
                                <div class="detail-label">处理结果说明</div>
                                <div class="detail-value">
                                    <div class="rich-content" id="processResult">
                                        <span class="empty">暂无处理结果</span>
                                    </div>
                                </div>
                            </div>
                            <div class="detail-item full-width">
                                <div class="detail-label">处理结果附件</div>
                                <div class="detail-value">
                                    <div class="attachment-list" id="attachmentList">
                                        <div class="empty-state">
                                            <div class="empty-icon">📎</div>
                                            <div class="empty-text">暂无附件</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 操作记录页签 -->
                <div class="tab-pane" id="operationsTab">
                    <div class="operation-list" id="operationList">
                        <div class="operation-item">
                            <div class="operation-header">
                                <div class="operation-action">创建投诉</div>
                                <div class="operation-time">2024-01-15 10:30:25</div>
                            </div>
                            <div class="operation-user">操作人：张三</div>
                            <div class="operation-content">
                                创建了新的投诉记录，投诉来源：书面异议，投诉标段：XX项目第一标段
                            </div>
                        </div>
                        
                        <div class="operation-item">
                            <div class="operation-header">
                                <div class="operation-action">修改投诉信息</div>
                                <div class="operation-time">2024-01-15 14:20:15</div>
                            </div>
                            <div class="operation-user">操作人：李四</div>
                            <div class="operation-content">
                                修改了投诉内容，补充了相关说明信息
                            </div>
                        </div>
                        
                        <div class="operation-item">
                            <div class="operation-header">
                                <div class="operation-action">分配处理人</div>
                                <div class="operation-time">2024-01-16 09:15:30</div>
                            </div>
                            <div class="operation-user">操作人：王五</div>
                            <div class="operation-content">
                                将投诉分配给处理人：赵六，要求在3个工作日内完成处理
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let complaintData = {};
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const id = urlParams.get('id');
            
            if (id) {
                loadComplaintDetail(id);
            }
        });
        
        // 加载投诉详情
        function loadComplaintDetail(id) {
            // 这里应该调用后端API获取数据
            console.log('加载投诉详情:', id);
            
            // 模拟数据
            complaintData = {
                id: id,
                complaintSource: '书面异议',
                complaintSegment: 'XX项目第一标段',
                complaintCompany: 'XX建设集团有限公司',
                projectName: 'XX基础设施建设项目',
                isHandled: '否',
                complaintTime: '2024-01-15',
                complaintContent: '关于XX项目第一标段的投诉内容，涉及招标过程中的相关问题...',
                createTime: '2024-01-15 10:30:25',
                creator: '张三',
                updateTime: '2024-01-15 10:30:25',
                processStatus: '待处理',
                processor: null,
                processTime: null,
                processResult: null,
                attachments: []
            };
            
            // 填充页面数据
            fillPageData();
        }
        
        // 填充页面数据
        function fillPageData() {
            document.getElementById('complaintSource').textContent = complaintData.complaintSource;
            document.getElementById('complaintSegment').textContent = complaintData.complaintSegment;
            document.getElementById('complaintCompany').textContent = complaintData.complaintCompany;
            document.getElementById('projectName').textContent = complaintData.projectName;
            document.getElementById('complaintTime').textContent = complaintData.complaintTime;
            document.getElementById('complaintContent').innerHTML = complaintData.complaintContent;
            document.getElementById('createTime').textContent = complaintData.createTime;
            document.getElementById('creator').textContent = complaintData.creator;
            document.getElementById('updateTime').textContent = complaintData.updateTime;
            
            // 处理状态
            const handledStatus = document.getElementById('handledStatus');
            if (complaintData.isHandled === '是') {
                handledStatus.textContent = '是';
                handledStatus.className = 'status-tag status-handled';
                document.getElementById('processBtn').style.display = 'none';
            } else {
                handledStatus.textContent = '否';
                handledStatus.className = 'status-tag status-unhandled';
            }
            
            // 处理结果
            const processStatus = document.getElementById('processStatus');
            const processor = document.getElementById('processor');
            const processTime = document.getElementById('processTime');
            const processResult = document.getElementById('processResult');
            
            if (complaintData.processor) {
                processor.textContent = complaintData.processor;
                processor.classList.remove('empty');
            }
            
            if (complaintData.processTime) {
                processTime.textContent = complaintData.processTime;
                processTime.classList.remove('empty');
            }
            
            if (complaintData.processResult) {
                processResult.innerHTML = complaintData.processResult;
                processResult.querySelector('.empty')?.remove();
            }
            
            // 附件列表
            if (complaintData.attachments && complaintData.attachments.length > 0) {
                renderAttachments();
            }
        }
        
        // 渲染附件列表
        function renderAttachments() {
            const attachmentList = document.getElementById('attachmentList');
            attachmentList.innerHTML = '';
            
            complaintData.attachments.forEach(attachment => {
                const attachmentItem = document.createElement('div');
                attachmentItem.className = 'attachment-item';
                attachmentItem.innerHTML = `
                    <div class="attachment-info">
                        <div class="attachment-icon">${getFileIcon(attachment.name)}</div>
                        <div>
                            <div class="attachment-name">${attachment.name}</div>
                            <div class="attachment-size">${formatFileSize(attachment.size)}</div>
                        </div>
                    </div>
                    <a href="#" class="attachment-download" onclick="downloadFile('${attachment.id}')">下载</a>
                `;
                attachmentList.appendChild(attachmentItem);
            });
        }
        
        // 获取文件图标
        function getFileIcon(fileName) {
            const ext = fileName.split('.').pop().toLowerCase();
            switch (ext) {
                case 'pdf':
                    return 'PDF';
                case 'doc':
                case 'docx':
                    return 'DOC';
                case 'jpg':
                case 'jpeg':
                case 'png':
                    return 'IMG';
                default:
                    return 'FILE';
            }
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 下载文件
        function downloadFile(fileId) {
            // 这里应该调用后端API下载文件
            console.log('下载文件:', fileId);
            alert('下载功能待实现');
        }
        
        // 切换页签
        function switchTab(tabName) {
            // 移除所有活动状态
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.remove('active');
            });
            
            // 激活当前页签
            event.target.classList.add('active');
            document.getElementById(tabName + 'Tab').classList.add('active');
        }
        
        // 页签跳转函数
        function openPage(url, title) {
            try {
                // 优先使用 postMessage 方式
                window.parent.postMessage({
                    type: 'openPage',
                    url: url,
                    title: title
                }, '*');
            } catch (e) {
                // 降级方案：直接打开新窗口
                window.open(url, '_blank');
            }
        }
        
        // 编辑投诉
        function editComplaint() {
            openPage('投诉管理-新建编辑页.html?id=' + complaintData.id, '编辑投诉');
        }
        
        // 处理投诉
        function processComplaint() {
            if (confirm('确认要处理这条投诉吗？')) {
                // 这里应该调用后端API
                console.log('处理投诉:', complaintData.id);
                
                // 模拟处理成功
                complaintData.isHandled = '是';
                complaintData.processStatus = '已处理';
                complaintData.processor = '当前用户';
                complaintData.processTime = new Date().toLocaleString('zh-CN');
                complaintData.processResult = '经核实，投诉内容属实，已要求相关部门整改。';
                
                // 更新页面显示
                fillPageData();
                
                alert('处理成功');
            }
        }
        
        // 删除投诉
        function deleteComplaint() {
            if (confirm('确认要删除这条投诉吗？删除后无法恢复。')) {
                // 这里应该调用后端API
                console.log('删除投诉:', complaintData.id);
                alert('删除成功');
                history.back();
            }
        }
    </script>
</body>
</html>