<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中标结果公示管理 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #fff;
            border-bottom: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
        }

        .help-icon {
            margin-left: 8px;
            width: 16px;
            height: 16px;
            background: #6b7280;
            border-radius: 50%;
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }



        /* 页签样式 */
        .tab-container {
            background: #fff;
            border-bottom: 1px solid #e6e8eb;
        }

        .tab-nav {
            display: flex;
            padding: 0 24px;
        }

        .tab-item {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            color: #6b7280;
            font-weight: 500;
            transition: all 0.3s;
        }

        .tab-item.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
            background: #f5f7fa;
        }

        .tab-item:hover {
            color: #1890ff;
            background: #f5f7fa;
        }

        /* 内容区域 */
        .content-area {
            padding: 24px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 查询区域 */
        .search-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 13px;
            color: #555;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-control {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .btn-group {
            display: flex;
            gap: 10px;
            align-items: end;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-outline {
            background: white;
            color: #3498db;
            border: 1px solid #3498db;
        }

        .btn-outline:hover {
            background: #3498db;
            color: white;
        }

        /* 高级查询 */
        .advanced-search {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        .advanced-search .search-form {
            display: grid !important;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
            gap: 15px !important;
            align-items: end !important;
        }

        /* 确保高级查询区域的form-group在grid中正确显示 */
        .advanced-search .form-group {
            display: flex !important;
            flex-direction: column !important;
        }

        /* 功能操作栏 */
        .action-bar {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .action-left {
            display: flex;
            gap: 10px;
        }

        .action-right {
            color: #7f8c8d;
            font-size: 13px;
        }

        .result-count {
            font-weight: 500;
        }

        .result-count strong {
            color: #2c3e50;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
        }

        /* 数据表格 */
        .data-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-container {
            overflow-x: auto;
            max-width: 100%;
        }

        .table-wrapper {
            overflow-x: auto;
            max-width: 100%;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            min-width: 1200px;
        }

        .data-table th,
        .data-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e8eaec;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .data-table th {
            background-color: #fafafa;
            font-weight: 600;
            color: #262626;
        }

        .data-table tr:hover td {
            background-color: #e6f7ff;
        }

        /* 冻结列样式 */
        .frozen-column {
            position: sticky;
            background: white;
            z-index: 10;
        }

        .frozen-column.checkbox-col {
            left: 0;
            width: 50px;
            min-width: 50px;
        }

        .frozen-column.name-col {
            left: 50px;
            width: 200px;
            min-width: 200px;
        }

        .frozen-column.action-col {
            right: 0;
            width: 200px;
            min-width: 200px;
        }

        .data-table th.frozen-column {
            background-color: #fafafa;
        }

        .data-table tr:hover .frozen-column {
            background-color: #e6f7ff;
        }

        /* 冻结列阴影效果 */
        .frozen-column.name-col::after {
            content: '';
            position: absolute;
            top: 0;
            right: -1px;
            width: 1px;
            height: 100%;
            background: linear-gradient(to right, rgba(0,0,0,0.1), transparent);
        }

        .frozen-column.action-col::before {
            content: '';
            position: absolute;
            top: 0;
            left: -1px;
            width: 1px;
            height: 100%;
            background: linear-gradient(to left, rgba(0,0,0,0.1), transparent);
        }

        /* 表格链接 */
        .table-link {
            color: #1890ff;
            text-decoration: none;
            cursor: pointer;
        }

        .table-link:hover {
            text-decoration: underline;
        }

        /* 状态标签 */
        .status-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }

        .status-pending {
            background-color: #fff7e6;
            color: #fa8c16;
        }

        .status-processing {
            background-color: #e6f7ff;
            color: #1890ff;
        }

        .status-approved {
            background-color: #e8f5e8;
            color: #52c41a;
        }

        .status-rejected {
            background-color: #fff2f0;
            color: #ff4d4f;
        }

        .status-published {
            background-color: #e8f5e8;
            color: #52c41a;
        }

        .status-draft {
            background-color: #f6f6f6;
            color: #8c8c8c;
        }

        /* 操作按钮 */
        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 2px;
            font-size: 12px;
            cursor: pointer;
            margin-right: 4px;
            transition: all 0.3s;
        }

        .action-btn:last-child {
            margin-right: 0;
        }

        .btn-submit {
            background-color: #52c41a;
            color: white;
        }

        .btn-submit:hover {
            background-color: #73d13d;
        }

        .btn-audit {
            background-color: #1890ff;
            color: white;
        }

        .btn-audit:hover {
            background-color: #40a9ff;
        }

        .btn-publish {
            background-color: #722ed1;
            color: white;
        }

        .btn-publish:hover {
            background-color: #9254de;
        }

        .btn-edit {
            background-color: #faad14;
            color: white;
        }

        .btn-edit:hover {
            background-color: #ffc53d;
        }

        .btn-delete {
            background-color: #ff4d4f;
            color: white;
        }

        .btn-delete:hover {
            background-color: #ff7875;
        }

        .btn-view {
            background: #13c2c2;
            color: white;
        }

        .btn-view:hover {
            background: #36cfc9;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 0;
            border-top: 1px solid #e8eaec;
            background-color: #fafafa;
        }

        .pagination-info {
            color: #8c8c8c;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .page-size-selector {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
            color: #6b7280;
        }

        .page-size-selector select {
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 13px;
        }

        .page-nav {
            display: flex;
            gap: 4px;
        }

        .page-btn {
            padding: 6px 12px;
            border: 1px solid #d1d5db;
            background: white;
            color: #374151;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
        }

        .page-btn:hover {
            background: #f3f4f6;
        }

        .page-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 24px;
            border-radius: 8px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e6e8eb;
        }

        .help-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .help-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #6b7280;
        }

        .help-body {
            color: #374151;
            line-height: 1.6;
        }

        .help-body h4 {
            margin: 16px 0 8px 0;
            color: #1f2937;
        }

        .help-body ul {
            margin: 8px 0;
            padding-left: 20px;
        }

        .help-body li {
            margin: 4px 0;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .container {
                max-width: 100%;
                margin: 0;
            }

            .search-form {
                grid-template-columns: repeat(3, 1fr);
            }

            .search-buttons {
                grid-column: span 3;
            }
        }

        @media (max-width: 768px) {
            .search-form {
                grid-template-columns: repeat(2, 1fr);
            }

            .search-buttons {
                grid-column: span 2;
            }

            .action-bar {
                flex-direction: column;
                gap: 12px;
                align-items: stretch;
            }

            .action-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">
                    中标结果公示管理
                    <span class="help-icon" onclick="showHelp()" title="功能说明">?</span>
                </h1>

            </div>
        </div>

        <!-- 页签导航 -->
        <div class="tab-container">
            <div class="tab-nav">
                <div class="tab-item active" onclick="switchTab('pending')">
                    待办
                </div>
                <div class="tab-item" onclick="switchTab('completed')">
                    已办
                </div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 待办页签内容 -->
            <div id="pending-tab" class="tab-content active">
                <!-- 查询区域 -->
                <div class="search-section">
                    <form class="search-form">
                        <div class="form-group">
                            <label class="form-label">中标结果公示标题</label>
                            <input type="text" class="form-control" placeholder="请输入中标结果公示标题">
                        </div>
                        <div class="form-group">
                            <label class="form-label">审核状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="pending">待审核</option>
                                <option value="approved">已通过</option>
                                <option value="rejected">已驳回</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">发布状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="draft">草稿</option>
                                <option value="published">已发布</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购方式</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="public">公开招标</option>
                                <option value="invite">邀请招标</option>
                                <option value="competitive">竞争性谈判</option>
                                <option value="inquiry">询价采购</option>
                                <option value="single">单一来源</option>
                            </select>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary" onclick="searchData()">查询</button>
                            <button type="button" class="btn btn-secondary" onclick="resetSearch()">重置</button>
                            <button type="button" class="btn btn-outline" onclick="toggleAdvanced()">高级查询</button>
                        </div>
                    
                    <!-- 高级查询区域 -->
                    <div class="advanced-search" id="advanced-search" style="display: none;">
                        <div class="search-form" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; align-items: end;">
                            <div class="form-group" style="display: flex; flex-direction: column;">
                                <label class="form-label">采购类型</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="goods">货物</option>
                                    <option value="service">服务</option>
                                    <option value="engineering">工程</option>
                                </select>
                            </div>
                            <div class="form-group" style="display: flex; flex-direction: column;">
                                <label class="form-label">采购组织方式</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="self">自行采购</option>
                                    <option value="agent">委托代理</option>
                                </select>
                            </div>
                            <div class="form-group" style="display: flex; flex-direction: column;">
                                <label class="form-label">资金来源</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="fiscal">财政资金</option>
                                    <option value="self">自有资金</option>
                                    <option value="loan">银行贷款</option>
                                    <option value="other">其他</option>
                                </select>
                            </div>
                            <div class="form-group" style="display: flex; flex-direction: column;">
                                <label class="form-label">招标金额（万元）</label>
                                <input type="number" class="form-control" placeholder="请输入金额">
                            </div>
                            <div class="form-group" style="display: flex; flex-direction: column;">
                                <label class="form-label">申请人</label>
                                <input type="text" class="form-control" placeholder="请输入申请人">
                            </div>
                            <div class="form-group" style="display: flex; flex-direction: column;">
                                <label class="form-label">招标时间</label>
                                <input type="date" class="form-control">
                            </div>
                            <div class="form-group" style="display: flex; flex-direction: column;">
                                <label class="form-label">计划时间</label>
                                <input type="date" class="form-control">
                            </div>
                            <div class="form-group" style="display: flex; flex-direction: column;">
                                <label class="form-label">创建时间</label>
                                <input type="date" class="form-control">
                            </div>
                        </div>
                    </div>
                    </form>
                </div>

                <!-- 功能操作栏 -->
                <div class="action-bar">
                    <div class="action-left">
                        <button class="btn btn-success" onclick="createAnnouncement()">新建中标结果公示</button>
                    </div>
                    <div class="action-right">
                        <span class="result-count">共 <strong>25</strong> 条记录</span>
                    </div>
                </div>

                <!-- 数据列表 -->
                <div class="data-section">
                    <div class="table-container">
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th class="frozen-column checkbox-col">
                                        <input type="checkbox" onclick="toggleAll(this)">
                                    </th>
                                    <th class="frozen-column name-col">中标结果公示标题</th>
                                    <th style="width: 100px;">审核状态</th>
                                    <th style="width: 100px;">发布状态</th>
                                    <th style="width: 120px;">采购方式</th>
                                    <th style="width: 100px;">采购类型</th>
                                    <th style="width: 150px;">关联标段</th>
                                    <th style="width: 150px;">关联项目</th>
                                    <th style="width: 120px;">创建时间</th>
                                    <th class="frozen-column action-col">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('WIN-2024-001')">办公设备采购项目中标结果公示</a>
                                    </td>
                                    <td><span class="status-tag status-pending">待审核</span></td>
                                    <td><span class="status-tag status-pending">待发布</span></td>
                                    <td>公开招标</td>
                                    <td>货物采购</td>
                                    <td>办公桌椅采购标段</td>
                                    <td>办公设备采购项目</td>
                                    <td>2024-03-15</td>
                                    <td class="frozen-column action-col">
                                        <button class="action-btn btn-submit" onclick="submitAnnouncement('WIN-2024-001')">提交</button>
                                        <button class="action-btn btn-edit" onclick="editAnnouncement('WIN-2024-001')">编辑</button>
                                        <button class="action-btn btn-delete" onclick="deleteAnnouncement('WIN-2024-001')">删除</button>
                                        <button class="action-btn btn-change" onclick="changeAnnouncement('WIN-2024-001')">变更</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('WIN-2024-002')">IT服务外包项目中标结果公示</a>
                                    </td>
                                    <td><span class="status-tag status-processing">审核中</span></td>
                                    <td><span class="status-tag status-pending">待发布</span></td>
                                    <td>邀请招标</td>
                                    <td>服务采购</td>
                                    <td>系统开发标段</td>
                                    <td>IT系统建设项目</td>
                                    <td>2024-03-16</td>
                                    <td class="frozen-column action-col">
                                        <button class="action-btn btn-audit" onclick="approveAnnouncement('WIN-2024-002')">审核</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('WIN-2024-003')">基础设施建设项目中标结果公示</a>
                                    </td>
                                    <td><span class="status-tag status-approved">已通过</span></td>
                                    <td><span class="status-tag status-pending">待发布</span></td>
                                    <td>公开招标</td>
                                    <td>货物采购</td>
                                    <td>文具用品采购标段</td>
                                    <td>办公设备采购项目</td>
                                    <td>2024-03-17</td>
                                    <td class="frozen-column action-col">
                                        <button class="action-btn btn-publish" onclick="publishAnnouncement('WIN-2024-003')">发布</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('WIN-2024-004')">电脑设备采购项目中标结果公示</a>
                                    </td>
                                    <td><span class="status-tag status-rejected">未通过</span></td>
                                    <td><span class="status-tag status-pending">待发布</span></td>
                                    <td>公开招标</td>
                                    <td>货物采购</td>
                                    <td>电脑设备采购标段</td>
                                    <td>办公设备采购项目</td>
                                    <td>2024-03-18</td>
                                    <td class="frozen-column action-col">
                                        <button class="action-btn btn-submit" onclick="submitAnnouncement('WIN-2024-004')">提交</button>
                                        <button class="action-btn btn-edit" onclick="editAnnouncement('WIN-2024-004')">编辑</button>
                                        <button class="action-btn btn-delete" onclick="deleteAnnouncement('WIN-2024-004')">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="pagination">
                        <div class="pagination-info">
                            显示第 1-10 条，共 25 条记录
                        </div>
                        <div class="pagination-controls">
                            <div class="page-size-selector">
                                <span>每页显示</span>
                                <select onchange="changePageSize(this.value)">
                                    <option value="10">10</option>
                                    <option value="20">20</option>
                                    <option value="50">50</option>
                                </select>
                                <span>条</span>
                            </div>
                            <div class="page-nav">
                                <button class="page-btn" disabled onclick="goToPage(1)">首页</button>
                                <button class="page-btn" disabled onclick="goToPage(0)">上一页</button>
                                <button class="page-btn active" onclick="goToPage(1)">1</button>
                                <button class="page-btn" onclick="goToPage(2)">2</button>
                                <button class="page-btn" onclick="goToPage(3)">3</button>
                                <button class="page-btn" onclick="goToPage(2)">下一页</button>
                                <button class="page-btn" onclick="goToPage(3)">末页</button>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>
            </div>

            <!-- 已办页签内容 -->
            <div id="completed-tab" class="tab-content">
                <!-- 查询区域 -->
                <div class="search-section">
                    <form class="search-form">
                        <div class="form-group">
                            <label class="form-label">中标结果公示标题</label>
                            <input type="text" class="form-control" placeholder="请输入中标结果公示标题">
                        </div>
                        <div class="form-group">
                            <label class="form-label">审核状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="approved">已通过</option>
                                <option value="rejected">已驳回</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">发布状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="published">已发布</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购方式</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="public">公开招标</option>
                                <option value="invite">邀请招标</option>
                                <option value="competitive">竞争性谈判</option>
                                <option value="inquiry">询价采购</option>
                                <option value="single">单一来源</option>
                            </select>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary" onclick="searchData()">查询</button>
                            <button type="button" class="btn btn-secondary" onclick="resetSearch()">重置</button>
                            <button type="button" class="btn btn-outline" onclick="toggleAdvancedCompleted()">高级查询</button>
                        </div>
                    
                    <!-- 高级查询区域 -->
                    <div class="advanced-search" id="advanced-search-completed" style="display: none;">
                        <div class="search-form" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; align-items: end;">
                            <div class="form-group" style="display: flex; flex-direction: column;">
                                <label class="form-label">采购类型</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="goods">货物</option>
                                    <option value="service">服务</option>
                                    <option value="engineering">工程</option>
                                </select>
                            </div>
                            <div class="form-group" style="display: flex; flex-direction: column;">
                                <label class="form-label">采购组织方式</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="self">自行采购</option>
                                    <option value="agent">委托代理</option>
                                </select>
                            </div>
                            <div class="form-group" style="display: flex; flex-direction: column;">
                                <label class="form-label">资金来源</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="fiscal">财政资金</option>
                                    <option value="self">自有资金</option>
                                    <option value="loan">银行贷款</option>
                                    <option value="other">其他</option>
                                </select>
                            </div>
                            <div class="form-group" style="display: flex; flex-direction: column;">
                                <label class="form-label">招标金额（万元）</label>
                                <input type="number" class="form-control" placeholder="请输入金额">
                            </div>
                            <div class="form-group" style="display: flex; flex-direction: column;">
                                <label class="form-label">申请人</label>
                                <input type="text" class="form-control" placeholder="请输入申请人">
                            </div>
                            <div class="form-group" style="display: flex; flex-direction: column;">
                                <label class="form-label">招标时间</label>
                                <input type="date" class="form-control">
                            </div>
                            <div class="form-group" style="display: flex; flex-direction: column;">
                                <label class="form-label">计划时间</label>
                                <input type="date" class="form-control">
                            </div>
                            <div class="form-group" style="display: flex; flex-direction: column;">
                                <label class="form-label">创建时间</label>
                                <input type="date" class="form-control">
                            </div>
                        </div>
                    </div>
                    </form>
                </div>

                <!-- 功能操作栏 -->
                <div class="action-bar">
                    <div class="action-left">
                        <button class="btn btn-danger" onclick="batchDelete()">批量删除</button>
                    </div>
                    <div class="action-right">
                        <span class="result-count">共 <strong>18</strong> 条记录</span>
                    </div>
                </div>

                <!-- 数据列表 -->
                <div class="data-section">
                    <div class="table-container">
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th class="frozen-column checkbox-col">
                                        <input type="checkbox" id="selectAllCompleted" onchange="toggleAllCompleted()">
                                    </th>
                                    <th class="frozen-column name-col">中标结果公示标题</th>
                                    <th style="width: 120px;">项目编号</th>
                                    <th style="width: 100px;">采购方式</th>
                                    <th style="width: 100px;">采购类型</th>
                                    <th style="width: 120px;">招标金额（万元）</th>
                                    <th style="width: 120px;">中标供应商</th>
                                    <th style="width: 100px;">审核状态</th>
                                    <th style="width: 100px;">发布状态</th>
                                    <th style="width: 120px;">创建人</th>
                                    <th style="width: 120px;">创建时间</th>
                                    <th class="frozen-column action-col">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox" name="rowSelectCompleted" value="4">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail(4)">网络设备采购项目中标结果公示</a>
                                    </td>
                                    <td>ZB2024004</td>
                                    <td>公开招标</td>
                                    <td>货物</td>
                                    <td>200.00</td>
                                    <td>某某网络科技公司</td>
                                    <td><span class="status-tag status-approved">已通过</span></td>
                                    <td><span class="status-tag status-approved">已发布</span></td>
                                    <td>赵六</td>
                                    <td>2024-01-10</td>
                                    <td class="frozen-column action-col">
                                        <!-- 已发布状态，无可用操作 -->
                                    </td>
                                </tr>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox" name="rowSelectCompleted" value="5">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail(5)">物业管理服务项目中标结果公示</a>
                                    </td>
                                    <td>ZB2024005</td>
                                    <td>邀请招标</td>
                                    <td>服务</td>
                                    <td>300.00</td>
                                    <td>某某物业管理公司</td>
                                    <td><span class="status-tag status-approved">已通过</span></td>
                                    <td><span class="status-tag status-approved">已发布</span></td>
                                    <td>孙七</td>
                                    <td>2024-01-08</td>
                                    <td class="frozen-column action-col">
                                        <!-- 已发布状态，无可用操作 -->
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="pagination">
                        <div class="pagination-info">
                            显示第 1-10 条，共 15 条记录
                        </div>
                        <div class="pagination-controls">
                            <div class="page-size-selector">
                                <span>每页显示</span>
                                <select onchange="changePageSize(this.value)">
                                    <option value="10">10</option>
                                    <option value="20">20</option>
                                    <option value="50">50</option>
                                </select>
                                <span>条</span>
                            </div>
                            <div class="page-nav">
                                <button class="page-btn" disabled onclick="goToPage(1)">首页</button>
                                <button class="page-btn" disabled onclick="goToPage(0)">上一页</button>
                                <button class="page-btn active" onclick="goToPage(1)">1</button>
                                <button class="page-btn" onclick="goToPage(2)">2</button>
                                <button class="page-btn" onclick="goToPage(2)">下一页</button>
                                <button class="page-btn" onclick="goToPage(2)">末页</button>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="helpModal" class="help-modal">
        <div class="help-content">
            <div class="help-header">
                <h3 class="help-title">中标结果公示管理功能说明</h3>
                <button class="help-close" onclick="hideHelp()">&times;</button>
            </div>
            <div class="help-body">
                <h4>功能概述</h4>
                <p>中标结果公示管理模块用于管理招标项目的中标结果公示信息，包括公示内容的创建、审核、发布和查看等功能。</p>
                
                <h4>主要功能</h4>
                <ul>
                    <li><strong>待办页签：</strong>显示需要处理的中标结果公示，包括待审核、草稿状态的公示</li>
                    <li><strong>已办页签：</strong>显示已处理完成的中标结果公示，包括已通过审核并发布的公示</li>
                    <li><strong>新建公示：</strong>创建新的中标结果公示信息</li>
                    <li><strong>审批功能：</strong>对提交的中标结果公示进行审核</li>
                    <li><strong>批量操作：</strong>支持批量审批和批量删除操作</li>
                </ul>
                
                <h4>操作说明</h4>
                <ul>
                    <li><strong>查询：</strong>可按公示标题、审核状态、发布状态、采购方式等条件查询</li>
                    <li><strong>高级查询：</strong>提供更多查询条件，如采购组织方式、资金来源、招标金额等</li>
                    <li><strong>状态说明：</strong>
                        <ul>
                            <li>草稿：公示信息已保存但未提交审核</li>
                            <li>待审核：公示信息已提交，等待审核</li>
                            <li>已通过：公示信息审核通过</li>
                            <li>已驳回：公示信息审核未通过</li>
                            <li>已发布：公示信息已对外发布</li>
                        </ul>
                    </li>
                </ul>
                
                <h4>注意事项</h4>
                <ul>
                    <li>中标结果公示需要按照相关法规要求进行发布</li>
                    <li>公示期间不得修改中标结果信息</li>
                    <li>已发布的公示信息需要保留完整的审核记录</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 页签切换功能
        function switchTab(tabName) {
            // 隐藏所有页签内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有页签链接的激活状态
            const tabLinks = document.querySelectorAll('.tab-link');
            tabLinks.forEach(link => {
                link.classList.remove('active');
            });
            
            // 显示对应的页签内容
            if (tabName === 'pending') {
                document.getElementById('pending-tab').classList.add('active');
                document.querySelector('[onclick="switchTab(\'pending\')"]').classList.add('active');
            } else if (tabName === 'completed') {
                document.getElementById('completed-tab').classList.add('active');
                document.querySelector('[onclick="switchTab(\'completed\')"]').classList.add('active');
            }
        }

        // 高级查询切换
        function toggleAdvanced() {
            const advancedSearch = document.getElementById('advanced-search');
            if (advancedSearch.style.display === 'none') {
                advancedSearch.style.display = 'block';
            } else {
                advancedSearch.style.display = 'none';
            }
        }

        // 已办页签高级查询切换
        function toggleAdvancedCompleted() {
            const advancedSearch = document.getElementById('advanced-search-completed');
            if (advancedSearch.style.display === 'none') {
                advancedSearch.style.display = 'block';
            } else {
                advancedSearch.style.display = 'none';
            }
        }

        // 全选功能
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('input[name="rowSelect"]');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }

        // 已办页签全选功能
        function toggleAllCompleted() {
            const selectAll = document.getElementById('selectAllCompleted');
            const checkboxes = document.querySelectorAll('input[name="rowSelectCompleted"]');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }

        // 帮助功能
        function showHelp() {
            document.getElementById('helpModal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('helpModal').style.display = 'none';
        }

        // 查询功能
        function searchData() {
            // 这里添加查询逻辑
            console.log('执行查询操作');
        }

        function resetSearch() {
            // 重置查询表单
            const forms = document.querySelectorAll('.search-form');
            forms.forEach(form => {
                form.reset();
            });
        }

        // 业务操作函数
        function createAnnouncement() {
            // 跳转到新建页面
            window.location.href = '中标结果公示管理-新建编辑页.html';
        }

        function viewDetail(id) {
            // 跳转到详情页面
            window.location.href = `中标结果公示管理-详情页.html?id=${id}`;
        }

        function editAnnouncement(id) {
            // 跳转到编辑页面
            window.location.href = `中标结果公示管理-新建编辑页.html?id=${id}`;
        }

        function approveAnnouncement(id) {
            // 跳转到审核页面
            window.location.href = `中标结果公示管理-审核页.html?id=${id}`;
        }

        function deleteAnnouncement(id) {
            if (confirm('确定要删除这条中标结果公示吗？')) {
                // 执行删除操作
                console.log('删除中标结果公示:', id);
            }
        }

        function batchApprove() {
            const selectedIds = getSelectedIds();
            if (selectedIds.length === 0) {
                alert('请选择要审批的记录');
                return;
            }
            if (confirm(`确定要批量审批选中的 ${selectedIds.length} 条记录吗？`)) {
                console.log('批量审批:', selectedIds);
            }
        }

        function batchDelete() {
            const selectedIds = getSelectedIds();
            if (selectedIds.length === 0) {
                alert('请选择要删除的记录');
                return;
            }
            if (confirm(`确定要批量删除选中的 ${selectedIds.length} 条记录吗？`)) {
                console.log('批量删除:', selectedIds);
            }
        }

        function getSelectedIds() {
            const checkboxes = document.querySelectorAll('input[name="rowSelect"]:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        // 分页功能
        function goToPage(page) {
            console.log('跳转到第', page, '页');
        }

        function changePageSize(size) {
            console.log('每页显示', size, '条');
        }

        // 页面加载完成后默认显示待办页签
        document.addEventListener('DOMContentLoaded', function() {
            switchTab('pending');
        });
    </script>
</body>
</html>