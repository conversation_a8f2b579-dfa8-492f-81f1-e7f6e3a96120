<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目预警详情 - 招采平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 页面头部 */
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .help-icon {
            width: 16px;
            height: 16px;
            background: #3498db;
            border-radius: 50%;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
            font-weight: bold;
        }

        .page-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-outline {
            background: white;
            color: #3498db;
            border: 1px solid #3498db;
        }

        .btn-outline:hover {
            background: #3498db;
            color: white;
        }

        /* 页签导航 */
        .tab-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .tab-nav {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .tab-nav-item {
            padding: 15px 25px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
            font-weight: 500;
            color: #666;
        }

        .tab-nav-item:hover {
            background: #e9ecef;
            color: #333;
        }

        .tab-nav-item.active {
            background: white;
            color: #3498db;
            border-bottom-color: #3498db;
        }

        .tab-content {
            display: none;
            padding: 25px;
        }

        .tab-content.active {
            display: block;
        }

        /* 详情表单样式 */
        .detail-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 60px;
            height: 2px;
            background: #3498db;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-item.full-width {
            grid-column: 1 / -1;
        }

        .detail-label {
            font-weight: 500;
            color: #555;
            margin-bottom: 5px;
            font-size: 13px;
        }

        .detail-value {
            color: #333;
            font-size: 14px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .detail-value.highlight {
            background: #fff3cd;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #ffeaa7;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-published {
            background: #d4edda;
            color: #155724;
        }

        .status-draft {
            background: #f8d7da;
            color: #721c24;
        }

        .status-paused {
            background: #fff3cd;
            color: #856404;
        }

        /* 操作记录样式 */
        .operation-list {
            border: 1px solid #e9ecef;
            border-radius: 6px;
            overflow: hidden;
        }

        .operation-item {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .operation-item:last-child {
            border-bottom: none;
        }

        .operation-time {
            min-width: 140px;
            color: #666;
            font-size: 13px;
        }

        .operation-user {
            min-width: 80px;
            font-weight: 500;
            color: #333;
        }

        .operation-action {
            flex: 1;
            color: #333;
        }

        .operation-status {
            min-width: 80px;
            text-align: right;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .help-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 8px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .help-close {
            position: absolute;
            right: 15px;
            top: 15px;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            color: #999;
        }

        .help-close:hover {
            color: #333;
        }

        .help-title {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 18px;
        }

        .help-content h4 {
            color: #3498db;
            margin: 20px 0 10px 0;
            font-size: 14px;
        }

        .help-content ul {
            margin-left: 20px;
            margin-bottom: 15px;
        }

        .help-content li {
            margin-bottom: 5px;
            font-size: 13px;
            line-height: 1.5;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .detail-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .detail-grid {
                grid-template-columns: 1fr;
            }
            
            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }
            
            .tab-nav {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="page-title">
                项目预警详情
                <span class="help-icon" onclick="showHelp()" title="查看帮助">?</span>
            </div>
            <div class="page-actions">
                <button class="btn btn-primary" onclick="handleWarning()">处理预警</button>
                <button class="btn btn-outline" onclick="ignoreWarning()">忽略预警</button>
                <button class="btn btn-secondary" onclick="goBack()">返回列表</button>
            </div>
        </div>

        <!-- 页签容器 -->
        <div class="tab-container">
            <!-- 页签导航 -->
            <div class="tab-nav">
                <div class="tab-nav-item active" onclick="switchTab('basicInfo', this)">基本信息</div>
                <div class="tab-nav-item" onclick="switchTab('projectInfo', this)">项目信息</div>
                <div class="tab-nav-item" onclick="switchTab('operationRecord', this)">操作记录</div>
            </div>

            <!-- 基本信息页签 -->
            <div id="basicInfo" class="tab-content active">
                <div class="detail-section">
                    <h3 class="section-title">预警基本信息</h3>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">预警类型</div>
                            <div class="detail-value">招标公告变更预警</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">预警名称</div>
                            <div class="detail-value">招标公告发布后进行重要变更</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">所属标段</div>
                            <div class="detail-value">某市政道路改造工程-第一标段</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">标段阶段</div>
                            <div class="detail-value">招标公告</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">所属项目</div>
                            <div class="detail-value">某市政道路改造工程</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">发布状态</div>
                            <div class="detail-value"><span class="status-badge status-published">已发布</span></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">采购方式</div>
                            <div class="detail-value">公开招标</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">采购类型</div>
                            <div class="detail-value">工程类</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">生成时间</div>
                            <div class="detail-value">2024-01-15 09:30:25</div>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h3 class="section-title">预警详细信息</h3>
                    <div class="detail-grid">
                        <div class="detail-item full-width">
                            <div class="detail-label">变更内容</div>
                            <div class="detail-value highlight">
                                <strong>原内容：</strong>投标文件递交截止时间为2024年1月20日上午9:00<br>
                                <strong>变更为：</strong>投标文件递交截止时间调整为2024年1月25日上午9:00，同时开标时间相应调整为2024年1月25日上午9:30
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 项目信息页签 -->
            <div id="projectInfo" class="tab-content">
                <div class="detail-section">
                    <h3 class="section-title">项目信息</h3>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">项目名称</div>
                            <div class="detail-value">某市政道路改造工程</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">项目标题</div>
                            <div class="detail-value">某市政道路改造工程招标公告</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">项目业主</div>
                            <div class="detail-value">某市城市建设投资有限公司</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">采购方式</div>
                            <div class="detail-value">公开招标</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">采购类别</div>
                            <div class="detail-value">工程类</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">所属二级公司单位</div>
                            <div class="detail-value">市政工程分公司</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">立项决策日期</div>
                            <div class="detail-value">2023-12-15</div>
                        </div>
                        <div class="detail-item full-width">
                            <div class="detail-label">项目基本情况</div>
                            <div class="detail-value">
                                本项目为某市政道路改造工程，主要包括道路路面重新铺设、排水系统改造、路灯设施更新等工作。项目总长度约3.2公里，预计工期6个月。项目建设内容包括：1）道路路面改造：采用沥青混凝土路面，厚度20cm；2）排水系统：新建雨水管网1200米，检查井30座；3）照明设施：安装LED路灯60盏，配套电缆敷设；4）绿化工程：道路两侧绿化带种植，面积约2000平方米。
                            </div>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h3 class="section-title">关联标段信息</h3>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">标段编号</div>
                            <div class="detail-value">BD-2024-001</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">标段名称</div>
                            <div class="detail-value">某市政道路改造工程-第一标段</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">标段状态</div>
                            <div class="detail-value"><span class="status-badge status-published">招标中</span></div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">标段阶段</div>
                            <div class="detail-value">招标公告</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">采购金额</div>
                            <div class="detail-value">1,250,000.00 元</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">预计开标时间</div>
                            <div class="detail-value">2024-01-25 09:30</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作记录页签 -->
            <div id="operationRecord" class="tab-content">
                <div class="detail-section">
                    <h3 class="section-title">操作记录</h3>
                    <div class="operation-list">
                        <div class="operation-item">
                            <div class="operation-time">2024-01-15 09:30:25</div>
                            <div class="operation-user">系统</div>
                            <div class="operation-action">系统自动检测到招标公告发生变更，生成预警信息</div>
                            <div class="operation-status">
                                <span class="status-badge status-published">已生成</span>
                            </div>
                        </div>
                        <div class="operation-item">
                            <div class="operation-time">2024-01-15 10:15:30</div>
                            <div class="operation-user">张三</div>
                            <div class="operation-action">查看预警详情</div>
                            <div class="operation-status">
                                <span class="status-badge status-published">已查看</span>
                            </div>
                        </div>
                        <div class="operation-item">
                            <div class="operation-time">2024-01-15 14:20:15</div>
                            <div class="operation-user">李四</div>
                            <div class="operation-action">标记预警为待处理状态，分配给相关负责人</div>
                            <div class="operation-status">
                                <span class="status-badge status-paused">待处理</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="helpModal" class="help-modal">
        <div class="help-content">
            <span class="help-close" onclick="hideHelp()">&times;</span>
            <h3 class="help-title">项目预警详情说明</h3>
            <p>项目预警详情页面用于查看和处理具体的预警信息，包含预警的基本信息、关联项目信息和操作记录。</p>
            
            <h4>基本信息页签</h4>
            <ul>
                <li><strong>预警基本信息：</strong>显示预警的类型、名称、所属标段、阶段等基础信息</li>
                <li><strong>预警详细信息：</strong>显示具体的变更内容，包括原内容和变更后的内容对比</li>
                <li><strong>状态标识：</strong>通过不同颜色的标签显示发布状态、处理状态等</li>
            </ul>
            
            <h4>项目信息页签</h4>
            <ul>
                <li><strong>项目信息：</strong>显示预警关联项目的详细信息，包括项目名称、业主、采购方式等</li>
                <li><strong>关联标段信息：</strong>显示预警涉及标段的具体信息，包括标段编号、状态、金额等</li>
                <li><strong>项目基本情况：</strong>详细描述项目的建设内容和技术要求</li>
            </ul>
            
            <h4>操作记录页签</h4>
            <ul>
                <li><strong>时间轴记录：</strong>按时间顺序显示预警的所有操作记录</li>
                <li><strong>操作人员：</strong>记录每次操作的具体人员信息</li>
                <li><strong>操作内容：</strong>详细描述每次操作的具体内容和结果</li>
                <li><strong>状态变更：</strong>跟踪预警状态的变更历史</li>
            </ul>
            
            <h4>操作说明</h4>
            <ul>
                <li><strong>处理预警：</strong>标记预警为已处理状态，需要填写处理说明和结果</li>
                <li><strong>忽略预警：</strong>标记预警为已忽略状态，适用于误报或不需要处理的情况</li>
                <li><strong>查看历史：</strong>在操作记录页签中查看完整的处理历史</li>
                <li><strong>关联跳转：</strong>可以通过项目信息跳转到相关的项目或标段详情页面</li>
            </ul>
        </div>
    </div>

    <script>
        // 页签切换
        function switchTab(tabId, element) {
            // 移除所有活动状态
            document.querySelectorAll('.tab-nav-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 添加当前活动状态
            element.classList.add('active');
            document.getElementById(tabId).classList.add('active');
        }

        // 处理预警
        function handleWarning() {
            if (confirm('确定要处理这个预警吗？')) {
                alert('预警处理功能开发中...');
            }
        }

        // 忽略预警
        function ignoreWarning() {
            if (confirm('确定要忽略这个预警吗？')) {
                alert('预警忽略功能开发中...');
            }
        }

        // 返回列表
        function goBack() {
            window.history.back();
        }

        // 显示帮助
        function showHelp() {
            document.getElementById('helpModal').style.display = 'block';
        }

        // 隐藏帮助
        function hideHelp() {
            document.getElementById('helpModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('helpModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('项目预警详情页面加载完成');
        });
    </script>
</body>
</html>