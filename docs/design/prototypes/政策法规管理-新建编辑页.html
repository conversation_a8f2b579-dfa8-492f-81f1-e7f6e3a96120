<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>政策法规管理 - 新建/编辑 - 招采平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .page-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        .page-header {
            background: white;
            color: #333;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-bottom: 1px solid #e8eaec;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }

        .page-breadcrumb {
            margin-top: 8px;
            opacity: 0.7;
            font-size: 14px;
        }

        .page-content {
            padding: 24px;
        }

        /* 表单容器 */
        .form-container {
            background: #fff;
            border: 1px solid #e8eaec;
            border-radius: 6px;
            overflow: hidden;
        }

        /* 表单分组 */
        .form-section {
            border-bottom: 1px solid #e8eaec;
        }

        .form-section:last-child {
            border-bottom: none;
        }

        .section-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e8eaec;
            font-weight: 600;
            font-size: 16px;
            color: #333;
            position: relative;
        }

        .section-header::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #5cadff;
        }

        .section-content {
            padding: 20px;
        }

        /* 表单行 */
        .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row:last-child {
            margin-bottom: 0;
        }

        .form-group {
            flex: 1;
            min-width: 300px;
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            flex: 1 1 100%;
            min-width: 100%;
        }

        .form-label {
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-label.required::after {
            content: ' *';
            color: #ff4d4f;
        }

        .form-control {
            height: 40px;
            padding: 10px 12px;
            border: 1px solid #dcdee2;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .form-control:focus {
            outline: none;
            border-color: #5cadff;
            box-shadow: 0 0 0 2px rgba(92, 173, 255, 0.2);
        }

        .form-control:disabled {
            background: #f5f5f5;
            color: #999;
        }

        .form-control.error {
            border-color: #ff4d4f;
        }

        .error-message {
            color: #ff4d4f;
            font-size: 12px;
            margin-top: 4px;
        }

        .form-help {
            color: #999;
            font-size: 12px;
            margin-top: 4px;
        }

        /* 富文本编辑器 */
        .rich-editor {
            border: 1px solid #dcdee2;
            border-radius: 4px;
            overflow: hidden;
        }

        .editor-toolbar {
            background: #f8f9fa;
            padding: 10px;
            border-bottom: 1px solid #dcdee2;
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .editor-btn {
            padding: 5px 8px;
            border: 1px solid #dcdee2;
            background: white;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .editor-btn:hover {
            background: #e9ecef;
        }

        .editor-btn.active {
            background: #5cadff;
            color: white;
            border-color: #5cadff;
        }

        .editor-content {
            min-height: 300px;
            padding: 15px;
            outline: none;
            line-height: 1.6;
        }

        .editor-content:focus {
            box-shadow: inset 0 0 0 2px rgba(92, 173, 255, 0.2);
        }

        /* 文件上传 */
        .upload-area {
            border: 2px dashed #dcdee2;
            border-radius: 6px;
            padding: 40px 20px;
            text-align: center;
            background: #fafafa;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #5cadff;
            background: #f0f8ff;
        }

        .upload-area.dragover {
            border-color: #5cadff;
            background: #e6f7ff;
        }

        .upload-icon {
            font-size: 48px;
            color: #ccc;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 16px;
            color: #666;
            margin-bottom: 10px;
        }

        .upload-hint {
            font-size: 12px;
            color: #999;
        }

        .file-input {
            display: none;
        }

        /* 文件列表 */
        .file-list {
            margin-top: 15px;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #e8eaec;
            border-radius: 4px;
            margin-bottom: 8px;
            background: white;
        }

        .file-icon {
            width: 32px;
            height: 32px;
            background: #5cadff;
            color: white;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-size: 14px;
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-size: 14px;
            color: #333;
            margin-bottom: 2px;
        }

        .file-size {
            font-size: 12px;
            color: #999;
        }

        .file-actions {
            display: flex;
            gap: 8px;
        }

        .file-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 3px;
            font-size: 12px;
            cursor: pointer;
            text-decoration: none;
        }

        .btn-download {
            background: #e6f7ff;
            color: #1890ff;
        }

        .btn-remove {
            background: #fff2f0;
            color: #ff4d4f;
        }

        /* 操作按钮 */
        .form-actions {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e8eaec;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #5cadff;
            color: white;
        }

        .btn-primary:hover {
            background: #4a9eff;
        }

        .btn-success {
            background: #52c41a;
            color: white;
        }

        .btn-success:hover {
            background: #45b018;
        }

        .btn-default {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #dcdee2;
        }

        .btn-default:hover {
            background: #e9ecef;
        }

        /* 字符计数 */
        .char-count {
            text-align: right;
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }

        .char-count.warning {
            color: #fa8c16;
        }

        .char-count.error {
            color: #ff4d4f;
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }

            .form-group {
                min-width: auto;
            }

            .form-actions {
                flex-direction: column;
            }

            .btn {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title" id="pageTitle">新增政策法规</h1>
            <div class="page-breadcrumb">政策法规管理 > 新增</div>
        </div>

        <div class="page-content">
            <form class="form-container" id="policyForm">
                <!-- 政策法规信息 -->
                <div class="form-section">
                    <div class="section-header">政策法规信息</div>
                    <div class="section-content">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label required">标题名称</label>
                                <input type="text" class="form-control" id="titleName" placeholder="请输入标题名称" maxlength="50" required>
                                <div class="char-count" id="titleCharCount">0/50</div>
                                <div class="error-message" id="titleError"></div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group full-width">
                                <label class="form-label required">政策法规说明</label>
                                <div class="rich-editor">
                                    <div class="editor-toolbar">
                                        <button type="button" class="editor-btn" onclick="formatText('bold')"><b>B</b></button>
                                        <button type="button" class="editor-btn" onclick="formatText('italic')"><i>I</i></button>
                                        <button type="button" class="editor-btn" onclick="formatText('underline')"><u>U</u></button>
                                        <button type="button" class="editor-btn" onclick="formatText('insertOrderedList')">1.</button>
                                        <button type="button" class="editor-btn" onclick="formatText('insertUnorderedList')">•</button>
                                        <button type="button" class="editor-btn" onclick="formatText('justifyLeft')">左对齐</button>
                                        <button type="button" class="editor-btn" onclick="formatText('justifyCenter')">居中</button>
                                        <button type="button" class="editor-btn" onclick="formatText('justifyRight')">右对齐</button>
                                        <select class="editor-btn" onchange="formatText('fontSize', this.value)">
                                            <option value="">字号</option>
                                            <option value="1">小</option>
                                            <option value="3">中</option>
                                            <option value="5">大</option>
                                            <option value="7">特大</option>
                                        </select>
                                    </div>
                                    <div class="editor-content" id="policyDescription" contenteditable="true" placeholder="请输入政策法规说明..."></div>
                                </div>
                                <div class="error-message" id="descriptionError"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 附件上传 -->
                <div class="form-section">
                    <div class="section-header">附件上传</div>
                    <div class="section-content">
                        <div class="form-row">
                            <div class="form-group full-width">
                                <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
                                    <div class="upload-icon">📎</div>
                                    <div class="upload-text">点击上传文件或拖拽文件到此区域</div>
                                    <div class="upload-hint">支持多文件上传，单个文件不超过10MB</div>
                                </div>
                                <input type="file" id="fileInput" class="file-input" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif" onchange="handleFileSelect(event)">
                                <div class="file-list" id="fileList"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="form-actions">
                    <button type="button" class="btn btn-default" onclick="goBack()">返回</button>
                    <button type="button" class="btn btn-primary" onclick="saveDraft()">保存</button>
                    <button type="button" class="btn btn-success" onclick="saveAndPublish()">保存并发布</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let uploadedFiles = [];
        let isEditMode = false;
        let currentId = null;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否为编辑模式
            const urlParams = new URLSearchParams(window.location.search);
            currentId = urlParams.get('id');
            
            if (currentId) {
                isEditMode = true;
                document.getElementById('pageTitle').textContent = '编辑政策法规';
                document.querySelector('.page-breadcrumb').textContent = '政策法规管理 > 编辑';
                loadPolicyData(currentId);
            }

            // 标题字符计数
            const titleInput = document.getElementById('titleName');
            titleInput.addEventListener('input', function() {
                updateCharCount('titleName', 'titleCharCount', 50);
                validateTitle();
            });

            // 拖拽上传
            setupDragAndDrop();
        });

        // 字符计数更新
        function updateCharCount(inputId, countId, maxLength) {
            const input = document.getElementById(inputId);
            const counter = document.getElementById(countId);
            const currentLength = input.value.length;
            
            counter.textContent = `${currentLength}/${maxLength}`;
            
            if (currentLength > maxLength * 0.9) {
                counter.className = 'char-count warning';
            } else if (currentLength >= maxLength) {
                counter.className = 'char-count error';
            } else {
                counter.className = 'char-count';
            }
        }

        // 标题验证
        function validateTitle() {
            const titleInput = document.getElementById('titleName');
            const errorDiv = document.getElementById('titleError');
            const title = titleInput.value.trim();
            
            if (!title) {
                titleInput.classList.add('error');
                errorDiv.textContent = '请输入标题名称';
                return false;
            } else if (title.length > 50) {
                titleInput.classList.add('error');
                errorDiv.textContent = '标题名称不能超过50个字符';
                return false;
            } else {
                titleInput.classList.remove('error');
                errorDiv.textContent = '';
                return true;
            }
        }

        // 富文本编辑器格式化
        function formatText(command, value = null) {
            document.execCommand(command, false, value);
            document.getElementById('policyDescription').focus();
        }

        // 设置拖拽上传
        function setupDragAndDrop() {
            const uploadArea = document.getElementById('uploadArea');
            
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                handleFiles(files);
            });
        }

        // 文件选择处理
        function handleFileSelect(event) {
            const files = event.target.files;
            handleFiles(files);
        }

        // 文件处理
        function handleFiles(files) {
            for (let file of files) {
                if (validateFile(file)) {
                    uploadedFiles.push({
                        id: Date.now() + Math.random(),
                        file: file,
                        name: file.name,
                        size: file.size,
                        type: file.type
                    });
                }
            }
            updateFileList();
        }

        // 文件验证
        function validateFile(file) {
            const maxSize = 10 * 1024 * 1024; // 10MB
            const allowedTypes = [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.ms-excel',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-powerpoint',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'text/plain',
                'image/jpeg',
                'image/png',
                'image/gif'
            ];
            
            if (file.size > maxSize) {
                alert(`文件 "${file.name}" 大小超过10MB限制`);
                return false;
            }
            
            if (!allowedTypes.includes(file.type)) {
                alert(`文件 "${file.name}" 类型不支持`);
                return false;
            }
            
            return true;
        }

        // 更新文件列表
        function updateFileList() {
            const fileList = document.getElementById('fileList');
            
            if (uploadedFiles.length === 0) {
                fileList.innerHTML = '';
                return;
            }
            
            fileList.innerHTML = uploadedFiles.map(fileItem => `
                <div class="file-item">
                    <div class="file-icon">📄</div>
                    <div class="file-info">
                        <div class="file-name">${fileItem.name}</div>
                        <div class="file-size">${formatFileSize(fileItem.size)}</div>
                    </div>
                    <div class="file-actions">
                        <button type="button" class="file-btn btn-download" onclick="downloadFile('${fileItem.id}')">下载</button>
                        <button type="button" class="file-btn btn-remove" onclick="removeFile('${fileItem.id}')">删除</button>
                    </div>
                </div>
            `).join('');
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 删除文件
        function removeFile(fileId) {
            uploadedFiles = uploadedFiles.filter(file => file.id !== fileId);
            updateFileList();
        }

        // 下载文件
        function downloadFile(fileId) {
            const fileItem = uploadedFiles.find(file => file.id === fileId);
            if (fileItem) {
                const url = URL.createObjectURL(fileItem.file);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileItem.name;
                a.click();
                URL.revokeObjectURL(url);
            }
        }

        // 表单验证
        function validateForm() {
            let isValid = true;
            
            // 验证标题
            if (!validateTitle()) {
                isValid = false;
            }
            
            // 验证说明
            const description = document.getElementById('policyDescription').innerHTML.trim();
            const descriptionError = document.getElementById('descriptionError');
            
            if (!description || description === '<br>' || description === '<div><br></div>') {
                descriptionError.textContent = '请输入政策法规说明';
                isValid = false;
            } else {
                descriptionError.textContent = '';
            }
            
            return isValid;
        }

        // 加载政策法规数据（编辑模式）
        function loadPolicyData(id) {
            // 模拟加载数据
            const mockData = {
                titleName: '政府采购法实施条例',
                description: '<p>这是政府采购法实施条例的详细说明内容...</p>',
                files: [
                    { id: '1', name: '政府采购法实施条例.pdf', size: 1024000, type: 'application/pdf' }
                ]
            };
            
            document.getElementById('titleName').value = mockData.titleName;
            document.getElementById('policyDescription').innerHTML = mockData.description;
            
            // 模拟已上传的文件
            uploadedFiles = mockData.files.map(file => ({
                ...file,
                id: file.id
            }));
            updateFileList();
            updateCharCount('titleName', 'titleCharCount', 50);
        }

        // 保存草稿
        function saveDraft() {
            if (!validateForm()) {
                return;
            }
            
            const formData = {
                titleName: document.getElementById('titleName').value,
                description: document.getElementById('policyDescription').innerHTML,
                files: uploadedFiles,
                status: 'draft'
            };
            
            console.log('保存草稿:', formData);
            alert('保存成功');
            
            // 返回列表页
            goBack();
        }

        // 保存并发布
        function saveAndPublish() {
            if (!validateForm()) {
                return;
            }
            
            if (confirm('确定要发布这条政策法规吗？发布后将对外公开。')) {
                const formData = {
                    titleName: document.getElementById('titleName').value,
                    description: document.getElementById('policyDescription').innerHTML,
                    files: uploadedFiles,
                    status: 'published'
                };
                
                console.log('保存并发布:', formData);
                alert('发布成功');
                
                // 返回列表页
                goBack();
            }
        }

        // 返回
        function goBack() {
            if (window.parent && window.parent.closeTab) {
                window.parent.closeTab(null, isEditMode ? '编辑政策法规' : '新增政策法规');
            } else {
                window.history.back();
            }
        }
    </script>
</body>
</html>