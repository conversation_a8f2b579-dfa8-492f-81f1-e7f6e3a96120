<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>签约履行管理 - 新建/编辑 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #fff;
            border-bottom: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
        }

        .help-icon {
            margin-left: 8px;
            width: 20px;
            height: 20px;
            background: #3b82f6;
            border-radius: 50%;
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-weight: bold;
        }

        .breadcrumb {
            color: #6b7280;
            font-size: 14px;
        }

        .back-btn {
            padding: 8px 16px;
            background: #6b7280;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: background 0.3s;
        }

        .back-btn:hover {
            background: #4b5563;
        }

        /* 内容区域 */
        .content-area {
            padding: 24px;
        }

        /* 表单区域 */
        .form-section {
            background: white;
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .section-header {
            padding: 16px 20px;
            border-bottom: 1px solid #e6e8eb;
            background: #f8fafc;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 16px;
            background: #3b82f6;
            margin-right: 8px;
        }

        .section-content {
            padding: 20px;
        }

        /* 表单网格 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-grid.full-width {
            grid-template-columns: 1fr;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            font-size: 13px;
            color: #374151;
            margin-bottom: 6px;
            font-weight: 500;
        }

        .form-label.required::after {
            content: '*';
            color: #ef4444;
            margin-left: 4px;
        }

        .form-input {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 13px;
            transition: border-color 0.3s;
        }

        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 13px;
            background: white;
            cursor: pointer;
        }

        .form-textarea {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 13px;
            resize: vertical;
            min-height: 100px;
            font-family: inherit;
        }

        .form-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 富文本编辑器 */
        .rich-editor {
            border: 1px solid #d1d5db;
            border-radius: 4px;
            overflow: hidden;
        }

        .editor-toolbar {
            background: #f8fafc;
            border-bottom: 1px solid #e6e8eb;
            padding: 8px 12px;
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .editor-btn {
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .editor-btn:hover {
            background: #f3f4f6;
        }

        .editor-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .editor-content {
            min-height: 300px;
            padding: 12px;
            outline: none;
            line-height: 1.6;
        }

        .editor-content:focus {
            background: #fefefe;
        }

        /* 文件上传 */
        .file-upload {
            border: 2px dashed #d1d5db;
            border-radius: 6px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .file-upload:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }

        .file-upload.dragover {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        .upload-icon {
            font-size: 32px;
            color: #9ca3af;
            margin-bottom: 8px;
        }

        .upload-text {
            color: #6b7280;
            font-size: 14px;
        }

        .upload-hint {
            color: #9ca3af;
            font-size: 12px;
            margin-top: 4px;
        }

        .file-list {
            margin-top: 12px;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: #f8fafc;
            border: 1px solid #e6e8eb;
            border-radius: 4px;
            margin-bottom: 8px;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .file-name {
            font-size: 13px;
            color: #374151;
        }

        .file-size {
            font-size: 12px;
            color: #6b7280;
        }

        .file-remove {
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
        }

        .file-remove:hover {
            background: #dc2626;
        }

        /* 标段信息表格 */
        .segment-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 12px;
        }

        .segment-table th,
        .segment-table td {
            padding: 12px 8px;
            text-align: left;
            border: 1px solid #e6e8eb;
            font-size: 13px;
        }

        .segment-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
        }

        .segment-table tbody tr:hover {
            background: #f9fafb;
        }

        .segment-actions {
            display: flex;
            gap: 4px;
        }

        .btn-small {
            padding: 4px 8px;
            border: none;
            border-radius: 3px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        /* 操作按钮 */
        .action-bar {
            background: #f8fafc;
            border-top: 1px solid #e6e8eb;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            bottom: 0;
        }

        .btn-group {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-outline {
            background: white;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        /* 模态弹窗 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e6e8eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            color: #6b7280;
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            color: #374151;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid #e6e8eb;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin-top: 16px;
        }

        .page-btn {
            padding: 6px 12px;
            border: 1px solid #d1d5db;
            background: white;
            color: #374151;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
        }

        .page-btn:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .page-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e6e8eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .help-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .help-body {
            padding: 24px;
        }

        .help-section {
            margin-bottom: 24px;
        }

        .help-section h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
        }

        .help-section p {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 12px;
        }

        .help-list {
            list-style: none;
            padding-left: 0;
        }

        .help-list li {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 8px;
            padding-left: 16px;
            position: relative;
        }

        .help-list li::before {
            content: '•';
            color: #3b82f6;
            position: absolute;
            left: 0;
        }

        .help-list strong {
            color: #1f2937;
        }

        /* 选择标段样式 */
        .section-select-container {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .section-select-container .form-input {
            flex: 1;
        }
        
        .selected-segment {
            padding: 8px 12px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .segment-info {
            color: #374151;
            font-size: 13px;
        }

        .segment-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .segment-item {
            padding: 12px;
            border-bottom: 1px solid #e6e8eb;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .segment-item:hover {
            background: #f9fafb;
        }

        .segment-item:last-child {
            border-bottom: none;
        }

        .segment-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .segment-details {
            font-size: 12px;
            color: #6b7280;
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .btn-group {
                flex-direction: column;
            }
            
            .action-bar {
                flex-direction: column;
                gap: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">
                    签约履行管理 - 新建/编辑
                    <span class="help-icon" onclick="showHelp()" title="功能说明">?</span>
                </h1>
                <div class="breadcrumb">首页 > 签约履行管理 > 新建/编辑</div>
            </div>
            <button class="back-btn" onclick="goBack()">返回列表</button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 基本信息 -->
            <div class="form-section">
                <div class="section-header">
                    <h3 class="section-title">基本信息</h3>
                </div>
                <div class="section-content">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label required">选择标段</label>
                            <div class="section-select-container">
                                <input type="text" class="form-input" id="sectionName" placeholder="请选择标段" readonly>
                                <input type="hidden" id="sectionId" value="">
                                <button type="button" class="btn btn-outline" onclick="openSectionSelectModal()">选择标段</button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">签约履行标题</label>
                            <input type="text" class="form-input" placeholder="由标段自动带出" value="办公设备采购项目签约履行" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">项目编号</label>
                            <input type="text" class="form-input" placeholder="由标段自动带出" value="ZB2024001" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">项目名称</label>
                            <input type="text" class="form-input" placeholder="由标段自动带出" value="办公设备采购项目" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label">项目业主</label>
                            <input type="text" class="form-input" placeholder="由标段自动带出" value="某某单位" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">采购方式</label>
                            <input type="text" class="form-input" placeholder="由标段自动带出" value="公开招标" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购类型</label>
                            <input type="text" class="form-input" placeholder="由标段自动带出" value="货物" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购金额（万元）</label>
                            <input type="number" class="form-input" placeholder="由标段自动带出" value="50.00" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label">中标金额（万元）</label>
                            <input type="number" class="form-input" placeholder="由标段自动带出" value="45.50" readonly>
                        </div>
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label">项目基本情况（建设内容及规模）</label>
                        <textarea class="form-textarea" rows="4" placeholder="由标段自动带出" readonly>本项目为办公设备采购，包括计算机、打印机、复印机等办公设备的采购和安装，预计采购数量约100台套，用于满足日常办公需求。</textarea>
                    </div>
                </div>
            </div>

            <!-- 履约信息 -->
            <div class="form-section">
                <div class="section-header">
                    <h3 class="section-title">履约信息</h3>
                </div>
                <div class="section-content">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">履约单位</label>
                            <input type="text" class="form-input" placeholder="由标段自动带出实际为中标单位" value="XX科技有限公司" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">是否签订合同</label>
                            <select class="form-select" id="contractSigned" onchange="toggleContractInfo()">
                                <option value="">请选择</option>
                                <option value="是" selected>是</option>
                                <option value="否">否</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label required">履约详情</label>
                        <div class="rich-editor">
                            <div class="editor-toolbar">
                                <button class="editor-btn" onclick="formatText('bold')"><strong>B</strong></button>
                                <button class="editor-btn" onclick="formatText('italic')"><em>I</em></button>
                                <button class="editor-btn" onclick="formatText('underline')"><u>U</u></button>
                                <button class="editor-btn" onclick="formatText('strikeThrough')"><s>S</s></button>
                                <button class="editor-btn" onclick="formatText('insertOrderedList')">1.</button>
                                <button class="editor-btn" onclick="formatText('insertUnorderedList')">•</button>
                                <button class="editor-btn" onclick="formatText('justifyLeft')">左对齐</button>
                                <button class="editor-btn" onclick="formatText('justifyCenter')">居中</button>
                                <button class="editor-btn" onclick="formatText('justifyRight')">右对齐</button>
                                <button class="editor-btn" onclick="insertTable()">表格</button>
                                <button class="editor-btn" onclick="insertLink()">链接</button>
                            </div>
                            <div class="editor-content" contenteditable="true" id="editor-content">
                                <h2>一、合同基本信息</h2>
                                <p>本项目为办公设备采购项目签约履行，合同金额为45.5万元，中标供应商为XX科技有限公司。合同签订时间为2024年1月15日，计划履行期限为3个月。</p>
                                
                                <h2>二、履行内容及要求</h2>
                                <ol>
                                    <li>按照合同约定的技术规格和质量标准供应办公设备；</li>
                                    <li>确保所有设备符合国家相关标准和行业规范；</li>
                                    <li>提供设备安装、调试和培训服务；</li>
                                    <li>提供不少于3年的质保服务；</li>
                                    <li>按照约定的时间节点完成设备交付和验收。</li>
                                </ol>
                                
                                <h2>三、履行进度安排</h2>
                                <p><strong>第一阶段：</strong>2024年1月15日-2024年2月15日，完成设备采购和生产</p>
                                <p><strong>第二阶段：</strong>2024年2月16日-2024年3月15日，完成设备交付和安装</p>
                                <p><strong>第三阶段：</strong>2024年3月16日-2024年4月15日，完成调试、培训和验收</p>
                                
                                <h2>四、质量控制措施</h2>
                                <p><strong>质量标准：</strong>严格按照合同技术规格执行</p>
                                <p><strong>检验方式：</strong>分批次验收，确保质量合格</p>
                                <p><strong>验收标准：</strong>按照国家标准和合同约定执行</p>
                                
                                <h2>五、联系方式</h2>
                                <p><strong>采购方联系人：</strong>张先生</p>
                                <p><strong>电话：</strong>0755-12345678</p>
                                <p><strong>供应商联系人：</strong>李经理</p>
                                <p><strong>电话：</strong>0755-87654321</p>
                                <p><strong>项目负责人：</strong>王工程师</p>
                                <p><strong>电话：</strong>0755-11223344</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



            <!-- 合同信息 -->
            <div class="form-section" id="contractInfo">
                <div class="section-header">
                    <h3 class="section-title">合同信息</h3>
                </div>
                <div class="section-content">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label required">合同名称</label>
                            <input type="text" class="form-input" placeholder="请输入关联合同名称" value="办公设备采购合同">
                        </div>
                        <div class="form-group">
                            <label class="form-label required">合同状态</label>
                            <select class="form-select">
                                <option value="">请选择合同状态</option>
                                <option value="签订中">签订中</option>
                                <option value="履行中" selected>履行中</option>
                                <option value="执行完毕">执行完毕</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">合同附件</label>
                        <div class="file-upload" onclick="selectFiles('contract-files')">
                            <div class="upload-icon">📁</div>
                            <div class="upload-text">点击上传或拖拽文件到此处</div>
                            <div class="upload-hint">支持 PDF、DOC、DOCX 格式，单个文件不超过 10MB</div>
                        </div>
                        <input type="file" id="contract-files" multiple accept=".pdf,.doc,.docx" style="display: none;" onchange="handleFileSelect(this, 'contract-file-list')">
                        <div id="contract-file-list" class="file-list">
                            <div class="file-item">
                                <div class="file-info">
                                    <span class="file-name">采购合同.pdf</span>
                                    <span class="file-size">(2.5 MB)</span>
                                </div>
                                <button class="file-remove" onclick="removeFile(this)">删除</button>
                            </div>
                            <div class="file-item">
                                <div class="file-info">
                                    <span class="file-name">合同附件.docx</span>
                                    <span class="file-size">(1.2 MB)</span>
                                </div>
                                <button class="file-remove" onclick="removeFile(this)">删除</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 其他附件 -->
            <div class="form-section">
                <div class="section-header">
                    <h3 class="section-title">其他附件</h3>
                </div>
                <div class="section-content">
                    <div class="form-group">
                        <label class="form-label">相关文件</label>
                        <div class="file-upload" onclick="selectFiles('contract-files')">
                            <div class="upload-icon">📁</div>
                            <div class="upload-text">点击上传或拖拽文件到此处</div>
                            <div class="upload-hint">支持 PDF、DOC、DOCX 格式，单个文件不超过 10MB</div>
                        </div>
                        <input type="file" id="contract-files" multiple accept=".pdf,.doc,.docx" style="display: none;" onchange="handleFileSelect(this, 'contract-file-list')">
                        <div id="contract-file-list" class="file-list">
                            <div class="file-item">
                                <div class="file-info">
                                    <span class="file-name">采购合同.pdf</span>
                                    <span class="file-size">(2.5 MB)</span>
                                </div>
                                <button class="file-remove" onclick="removeFile(this)">删除</button>
                            </div>
                        </div>
                    </div>
                    

                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-bar">
            <div class="btn-group">
                <button class="btn btn-secondary" onclick="goBack()">返回</button>
            </div>
            <div class="btn-group">
                <button class="btn btn-outline" onclick="saveDraft()">保存草稿</button>
                <button class="btn btn-warning" onclick="preview()">预览</button>
                <button class="btn btn-primary" onclick="submitForReview()">提交审核</button>
            </div>
        </div>
    </div>

    <!-- 选择标段弹窗 -->
    <div id="segment-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">选择标段</h3>
                <button class="close-btn" onclick="closeSegmentModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="segment-list">
                    <div class="segment-item" onclick="selectSegmentItem('BD-001', '台式电脑采购', '30.00万元')">
                        <div class="segment-title">BD-001 - 台式电脑采购</div>
                        <div class="segment-details">预算金额：30.00万元 | 采购内容：台式电脑50台</div>
                    </div>
                    <div class="segment-item" onclick="selectSegmentItem('BD-002', '办公设备采购', '20.00万元')">
                        <div class="segment-title">BD-002 - 办公设备采购</div>
                        <div class="segment-details">预算金额：20.00万元 | 采购内容：打印机、复印机等</div>
                    </div>
                    <div class="segment-item" onclick="selectSegmentItem('BD-003', '网络设备采购', '15.50万元')">
                        <div class="segment-title">BD-003 - 网络设备采购</div>
                        <div class="segment-details">预算金额：15.50万元 | 采购内容：交换机、路由器等</div>
                    </div>
                    <div class="segment-item" onclick="selectSegmentItem('BD-004', '软件系统采购', '25.00万元')">
                        <div class="segment-title">BD-004 - 软件系统采购</div>
                        <div class="segment-details">预算金额：25.00万元 | 采购内容：办公软件、管理系统等</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeSegmentModal()">取消</button>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="help-modal" class="help-modal">
        <div class="help-content">
            <div class="help-header">
                <h3 class="help-title">签约履行管理新建/编辑功能说明</h3>
                <button class="close-btn" onclick="hideHelp()">&times;</button>
            </div>
            <div class="help-body">
                <div class="help-section">
                    <h4>功能概述</h4>
                    <p>签约履行管理新建/编辑页面用于创建和修改签约履行记录。支持富文本编辑、标段选择、附件上传等功能，确保履行信息的完整性和规范性。</p>
                </div>
                
                <div class="help-section">
                    <h4>基本信息</h4>
                    <ul class="help-list">
                        <li><strong>签约履行标题：</strong>必填项，简明扼要地描述履行项目</li>
                        <li><strong>项目编号：</strong>系统自动生成，唯一标识</li>
                        <li><strong>采购方式：</strong>选择采购方式</li>
                        <li><strong>采购类型：</strong>选择采购类型</li>
                        <li><strong>合同金额：</strong>签约合同金额</li>
                        <li><strong>中标供应商：</strong>中标的供应商名称</li>
                        <li><strong>关联标段：</strong>选择关联的标段信息</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>履行详情</h4>
                    <ul class="help-list">
                        <li><strong>富文本编辑：</strong>支持文字格式化、列表、表格等</li>
                        <li><strong>内容结构：</strong>建议包含合同基本信息、履行内容及要求、履行进度安排、质量控制措施、联系方式等</li>
                        <li><strong>格式规范：</strong>遵循合同履行管理规范要求</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>标段选择</h4>
                    <ul class="help-list">
                        <li><strong>选择标段：</strong>从已有标段中选择关联标段</li>
                        <li><strong>标段信息：</strong>显示标段编号、名称、预算金额等</li>
                        <li><strong>移除标段：</strong>可移除已选择的标段</li>
                        <li><strong>单一关联：</strong>每个履行记录只能关联一个标段</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>附件上传</h4>
                    <ul class="help-list">
                        <li><strong>合同文件：</strong>上传相关合同文档</li>
                        <li><strong>支持格式：</strong>PDF、DOC、DOCX</li>
                        <li><strong>文件大小：</strong>单个文件不超过10MB</li>
                        <li><strong>上传方式：</strong>点击上传或拖拽文件</li>
                        <li><strong>文件管理：</strong>可删除已上传的文件</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>操作说明</h4>
                    <ul class="help-list">
                        <li><strong>保存草稿：</strong>保存当前内容为草稿状态</li>
                        <li><strong>预览：</strong>预览履行记录效果</li>
                        <li><strong>提交审核：</strong>提交履行记录进入审核流程</li>
                        <li><strong>返回：</strong>返回履行管理列表页面</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>注意事项</h4>
                    <ul class="help-list">
                        <li>必填项目必须完整填写</li>
                        <li>履行内容应符合合同约定要求</li>
                        <li>提交审核前请仔细检查内容</li>
                        <li>草稿状态可随时修改，提交后需审核通过才能修改</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 富文本编辑器功能
        function formatText(command, value = null) {
            document.execCommand(command, false, value);
            document.getElementById('editor-content').focus();
        }

        function insertTable() {
            const rows = prompt('请输入行数:', '3');
            const cols = prompt('请输入列数:', '3');
            
            if (rows && cols) {
                let tableHTML = '<table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;">';
                for (let i = 0; i < rows; i++) {
                    tableHTML += '<tr>';
                    for (let j = 0; j < cols; j++) {
                        tableHTML += '<td style="padding: 8px; border: 1px solid #ccc;">单元格</td>';
                    }
                    tableHTML += '</tr>';
                }
                tableHTML += '</table>';
                
                document.execCommand('insertHTML', false, tableHTML);
            }
        }

        function insertLink() {
            const url = prompt('请输入链接地址:', 'http://');
            const text = prompt('请输入链接文字:', '链接');
            
            if (url && text) {
                const linkHTML = `<a href="${url}" target="_blank">${text}</a>`;
                document.execCommand('insertHTML', false, linkHTML);
            }
        }

        // 标段选择管理
        let selectedSegmentData = null;

        function selectSegment() {
            document.getElementById('segment-modal').style.display = 'block';
        }

        function selectSegmentItem(code, name, budget) {
            selectedSegmentData = { code, name, budget };
            
            // 显示选中的标段信息
            const selectedSegment = document.getElementById('selected-segment');
            const segmentInfo = selectedSegment.querySelector('.segment-info');
            segmentInfo.textContent = `${code} - ${name} (预算：${budget})`;
            selectedSegment.style.display = 'flex';
            
            // 关闭弹窗
            closeSegmentModal();
        }

        function removeSelectedSegment() {
            selectedSegmentData = null;
            document.getElementById('selected-segment').style.display = 'none';
        }

        function closeSegmentModal() {
            document.getElementById('segment-modal').style.display = 'none';
        }

        // 文件上传
        function selectFiles(inputId) {
            document.getElementById(inputId).click();
        }

        function handleFileSelect(input, listId) {
            const files = input.files;
            const fileList = document.getElementById(listId);

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="file-info">
                        <span class="file-name">${file.name}</span>
                        <span class="file-size">(${(file.size / 1024 / 1024).toFixed(2)} MB)</span>
                    </div>
                    <button class="file-remove" onclick="removeFile(this)">删除</button>
                `;
                fileList.appendChild(fileItem);
            }

            // 清空input，允许重复选择相同文件
            input.value = '';
        }

        function removeFile(button) {
            button.parentElement.remove();
        }

        // 拖拽上传
        document.querySelectorAll('.file-upload').forEach(uploadArea => {
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                const inputId = this.getAttribute('onclick').match(/'([^']+)'/)[1];
                const input = document.getElementById(inputId);
                
                // 模拟文件选择
                const dt = new DataTransfer();
                for (let i = 0; i < files.length; i++) {
                    dt.items.add(files[i]);
                }
                input.files = dt.files;
                
                // 触发文件处理
                const listId = inputId.replace('-files', '-file-list');
                handleFileSelect(input, listId);
            });
        });

        // 帮助弹窗
        function showHelp() {
            document.getElementById('help-modal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('help-modal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });

        document.getElementById('segment-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeSegmentModal();
            }
        });

        // 业务操作函数
        function goBack() {
            if (confirm('确定要返回吗？未保存的内容将丢失。')) {
                window.location.href = '签约履行管理-列表页.html';
            }
        }

        function saveDraft() {
            console.log('保存草稿');
            alert('草稿保存成功！');
        }

        function preview() {
            console.log('预览公告');
            alert('预览功能待实现');
        }

        function submitForReview() {
            // 验证必填项
            const title = document.querySelector('input[placeholder="请输入签约履行标题"]').value;
            const projectCode = document.querySelector('input[placeholder="系统自动生成"]').value;
            const content = document.getElementById('editor-content').innerHTML;

            if (!title || !projectCode || !content.trim()) {
                alert('请填写必填项目');
                return;
            }

            if (!selectedSegmentData) {
                alert('请选择关联标段');
                return;
            }

            if (confirm('确定要提交审核吗？提交后将无法修改。')) {
                console.log('提交审核');
                alert('提交成功！');
                window.location.href = '签约履行管理-列表页.html';
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('签约履行管理新建/编辑页面加载完成');
            
            // 初始化富文本编辑器
            const editor = document.getElementById('editor-content');
            editor.addEventListener('input', function() {
                // 可以在这里添加自动保存功能
            });
        });
    </script>
</body>
</html>