<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>招采平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #333;
            overflow: hidden;
        }
        
        /* 顶部标题栏 */
        .top-header {
            height: 64px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .header-logo {
            width: 32px;
            height: 32px;
            background: linear-gradient(45deg, #1890ff 0%, #40a9ff 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 16px;
        }
        
        .header-title {
            font-size: 22px;
            font-weight: 600;
            letter-spacing: 0.5px;
        }
        
        .sidebar-toggle {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            transition: background-color 0.3s;
        }
        
        .sidebar-toggle:hover {
            background-color: rgba(255,255,255,0.1);
        }
        
        /* 主体布局 */
        .main-container {
            display: flex;
            height: calc(100vh - 64px);
            margin-top: 64px;
        }
        
        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border: 2px solid transparent;
            border-radius: 12px;
            color: #2c3e50;
            overflow-y: auto;

            box-shadow: 0 4px 20px rgba(24, 144, 255, 0.15);
            margin: 8px;
        }
        
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }
        
        .sidebar::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(0,0,0,0.2);
            border-radius: 3px;
        }
        
        .sidebar-menu {
            padding: 24px 0;
        }
        
        .menu-item {
            margin: 0 12px 4px;
            padding: 14px 16px;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: flex-start;
            text-align: left;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }
        
        .menu-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            opacity: 0;
            transition: opacity 0.3s;
            border-radius: 12px;
        }
        
        .menu-item:hover {
            transform: translateX(4px);
            box-shadow: 0 4px 15px rgba(24, 144, 255, 0.3);
        }
        
        .menu-item:hover::before {
            opacity: 0.1;
        }
        
        .menu-item.active {
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            color: white;
            transform: translateX(4px);
            box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);
        }
        
        .menu-item.active::before {
            opacity: 0;
        }
        
        .menu-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        
        /* 二级菜单样式 */
        .menu-item.has-submenu {
            justify-content: space-between;
        }
        
        .submenu-arrow {
            font-size: 12px;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            opacity: 0.7;
        }
        
        .menu-item.has-submenu.expanded .submenu-arrow {
            transform: rotate(180deg);
        }
        
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255,255,255,0.05);
            margin: 4px 12px;
            border-radius: 8px;
        }
        
        .submenu.expanded {
            max-height: 500px;
            padding: 8px 0;
        }
        
        .submenu-item {
            margin: 2px 8px;
            padding: 10px 16px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 8px;
            font-size: 14px;
            position: relative;
            overflow: hidden;
            text-align: left;
        }
        
        .submenu-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            opacity: 0;
            transition: opacity 0.3s;
            border-radius: 8px;
        }
        
        .submenu-item:hover {
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
        }
        
        .submenu-item:hover::before {
            opacity: 0.1;
        }
        
        .submenu-item.active {
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            color: white;
            transform: translateX(2px);
            box-shadow: 0 3px 12px rgba(24, 144, 255, 0.3);
        }
        
        .submenu-item.active::before {
            opacity: 0;
        }
        
        /* 内容区域 */
        .content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border: 2px solid transparent;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(24, 144, 255, 0.15);
            padding: 20px;
            max-width: 1400px;
            margin: 8px 8px 8px 0;
        }
        
        /* 页签区域 */
        .tabs {
            display: flex;
            background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 100%);
            backdrop-filter: blur(10px);
            border-radius: 10px 10px 0 0;
            border: 1px solid transparent;
            border-bottom: none;
            margin-bottom: -1px;
            position: relative;
            z-index: 1;
            overflow-x: auto;
            min-height: 48px;
        }
        
        .tabs::-webkit-scrollbar {
            height: 4px;
        }
        
        .tabs::-webkit-scrollbar-track {
            background: transparent;
        }
        
        .tabs::-webkit-scrollbar-thumb {
            background: rgba(0,0,0,0.2);
            border-radius: 2px;
        }
        
        .tab {
            padding: 12px 20px;
            cursor: pointer;
            border-right: 1px solid #e8eaec;
            position: relative;
            display: flex;
            align-items: center;
            gap: 8px;
            background: #f8f9fa;
            white-space: nowrap;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            border-radius: 8px 8px 0 0;
            margin: 4px 2px 0;
            z-index: 1;
        }
        
        .tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            opacity: 0;
            transition: opacity 0.3s;
            border-radius: 8px 8px 0 0;
        }
        
        .tab:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
        }
        
        .tab:hover::before {
            opacity: 0.05;
        }
        
        .tab.active {
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            color: white;
            border-bottom: 2px solid transparent;
            margin-bottom: -1px;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(24, 144, 255, 0.3);
        }
        
        .tab.active::before {
            opacity: 0;
        }
        
        .tab-close {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            margin-left: 8px;
            background: none;
            border: none;
            cursor: pointer;
            color: rgba(255,255,255,0.7);
            transition: all 0.3s;
            position: relative;
            z-index: 10;
            pointer-events: auto;
            flex-shrink: 0;
        }
        
        .tab:not(.active) .tab-close {
            color: #999;
        }
        
        .tab-close:hover {
            background-color: rgba(255,255,255,0.2);
            color: white;
            transform: scale(1.1);
        }
        
        .tab:not(.active) .tab-close:hover {
            background-color: rgba(0,0,0,0.1);
            color: #666;
        }
        
        /* 页面内容 */
        .page-content {
            background: #fafbfc;
            border-radius: 0 10px 10px 10px;
            border: 1px solid transparent;
            flex: 1;
            overflow: hidden;
            position: relative;
        }
        
        .page-frame {
            display: none;
            width: 100%;
            height: 100%;
            border: none;
            position: absolute;
            top: 0;
            left: 0;
            border-radius: 0 0 8px 8px;
        }
        
        .page-frame.active {
            display: block;
        }
        
        /* 用户信息 */
        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .user-avatar {
            width: 36px;
            height: 36px;
            background: linear-gradient(45deg, #1890ff 0%, #40a9ff 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }
        
        .user-name {
            font-size: 14px;
            font-weight: 500;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
            cursor: pointer;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <!-- 顶部标题栏 -->
    <div class="top-header">
        <div class="header-left">
            <button class="sidebar-toggle">☰</button>
            <div class="header-logo">招</div>
            <div class="header-title">招标采购平台</div>
        </div>
        <div class="user-info">
            <div class="user-avatar">管</div>
            <span class="user-name">管理员</span>
            <button class="logout-btn" onclick="logout()">退出</button>
        </div>
    </div>
    
    <div class="main-container">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <div class="sidebar-menu">
                <div class="menu-item active" onclick="openTab('工作台', '工作台.html')">
                    <span>工作台</span>
                </div>
                <div class="menu-item" onclick="openTab('采购计划管理', '采购计划管理-列表页.html')">
                    <span>采购计划管理</span>
                </div>
                <div class="menu-item has-submenu" onclick="toggleSubmenu(this)">
                    <span>采购执行管理</span>
                    <div class="submenu-arrow">▼</div>
                </div>
                <div class="submenu">
                    <div class="submenu-item" onclick="openTab('项目标段管理', '项目标段管理-列表页.html')">
                        <span>项目标段管理</span>
                    </div>
                    <div class="submenu-item" onclick="openTab('公告管理', '公告管理-列表页.html')">
                        <span>公告管理</span>
                    </div>
                    <div class="submenu-item" onclick="openTab('补遗/澄清/答疑管理', '补遗澄清答疑管理-列表页.html')">
                        <span>补遗/澄清/答疑管理</span>
                    </div>
                    <div class="submenu-item" onclick="openTab('流标或中止管理', '流标或中止管理-列表页.html')">
                        <span>流标或中止管理</span>
                    </div>
                    <div class="submenu-item" onclick="openTab('评标结果公示管理', '评标结果公示管理-列表页.html')">
                        <span>评标结果公示管理</span>
                    </div>
                    <div class="submenu-item" onclick="openTab('中标结果公示管理', '中标结果公示管理-列表页.html')">
                        <span>中标结果公示管理</span>
                    </div>
                    <div class="submenu-item" onclick="openTab('签约履行管理', '签约履行管理-列表页.html')">
                        <span>签约履行管理</span>
                    </div>
                </div>
                <div class="menu-item" onclick="openTab('项目预警管理', '项目预警管理-列表页.html')">
                    <span>项目预警管理</span>
                </div>
                <div class="menu-item has-submenu" onclick="toggleSubmenu(this)">
                    <span>代理机构管理</span>
                    <div class="submenu-arrow">▼</div>
                </div>
                <div class="submenu">
                    <div class="submenu-item" onclick="openTab('代理机构管理', '代理机构管理-列表页.html')">
                        <span>代理机构管理</span>
                    </div>
                    <div class="submenu-item" onclick="openTab('机构人员管理', '机构人员管理-列表页.html')">
                        <span>机构人员管理</span>
                    </div>
                </div>
                <div class="menu-item" onclick="openTab('供应商管理', '供应商管理-列表页.html')">
                    <span>供应商管理</span>
                </div>
                <div class="menu-item" onclick="openTab('通知公告管理', '通知公告管理-列表页.html')">
                    <span>通知公告管理</span>
                </div>
                <div class="menu-item" onclick="openTab('政策法规管理', '政策法规管理-列表页.html')">
                    <span>政策法规管理</span>
                </div>
                <div class="menu-item has-submenu" onclick="toggleSubmenu(this)">
                    <span>数据统计分析</span>
                    <div class="submenu-arrow">▼</div>
                </div>
                <div class="submenu">
                    <div class="submenu-item" onclick="openTab('数据统计', '移动端H5/数据统计.html')">
                        <span>数据统计</span>
                    </div>
                    <div class="submenu-item" onclick="openTab('统计报表', '统计报表-列表页.html')">
                        <span>统计报表</span>
                    </div>
                    <div class="submenu-item" onclick="openTab('数据分析', '数据分析-列表页.html')">
                        <span>数据分析</span>
                    </div>
                </div>
                <div class="menu-item has-submenu" onclick="toggleSubmenu(this)">
                    <span>帮助中心</span>
                    <div class="submenu-arrow">▼</div>
                </div>
                <div class="submenu">
                    <div class="submenu-item" onclick="openTab('帮助中心', '帮助中心-列表页.html')">
                        <span>帮助中心</span>
                    </div>
                    <div class="submenu-item" onclick="openTab('投诉管理', '投诉管理-列表页.html')">
                        <span>投诉管理</span>
                    </div>
                </div>
                <div class="menu-item has-submenu" onclick="toggleSubmenu(this)">
                    <span>系统设置</span>
                    <div class="submenu-arrow">▼</div>
                </div>
                <div class="submenu">
                    <div class="submenu-item" onclick="openTab('预警配置', '预警配置-列表页.html')">
                        <span>预警配置</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 内容区域 -->
        <div class="content-wrapper">
            <!-- 页签区域 -->
            <div class="tabs" id="tabs">
                <div class="tab active" data-page="工作台" id="workspace-tab">
                    <span>工作台</span>
                    <div class="tab-close" id="workspace-close">×</div>
                </div>
            </div>
            
            <!-- 页面内容 -->
            <div class="page-content">
                <iframe id="frame-工作台" class="page-frame active" src="工作台.html"></iframe>
            </div>
        </div>
    </div>
    
    <script>
        let openTabs = ['工作台'];
        let activeTab = '工作台';
        
        // 页面加载完成后绑定工作台关闭按钮事件
        document.addEventListener('DOMContentLoaded', function() {
            const workspaceCloseBtn = document.getElementById('workspace-close');
            if (workspaceCloseBtn) {
                workspaceCloseBtn.onmousedown = function(event) {
                    event.preventDefault();
                    event.stopPropagation();
                    return false;
                };
                
                workspaceCloseBtn.onclick = function(event) {
                    event.preventDefault();
                    event.stopPropagation();
                    closeTab(event, '工作台');
                    return false;
                };
                
                workspaceCloseBtn.onmouseup = function(event) {
                    event.preventDefault();
                    event.stopPropagation();
                    return false;
                };
            }
            
            const workspaceTab = document.getElementById('workspace-tab');
            if (workspaceTab) {
                workspaceTab.onclick = function(event) {
                    if (!event.target.classList.contains('tab-close')) {
                        switchTab('工作台');
                    }
                };
            }
        });
        
        function openTab(tabName, url) {
            // 如果URL为#，表示功能未实现，显示提示
            if (url === '#') {
                alert('该功能正在开发中，敬请期待！');
                return;
            }
            
            // 如果标签页已存在，直接激活
            if (openTabs.includes(tabName)) {
                switchTab(tabName);
                return;
            }
            
            // 添加新标签页
            openTabs.push(tabName);
            
            // 创建标签页
            const tabsContainer = document.getElementById('tabs');
            const newTab = document.createElement('div');
            newTab.className = 'tab';
            newTab.setAttribute('data-page', tabName);
            
            const tabSpan = document.createElement('span');
            tabSpan.textContent = tabName;
            
            const closeBtn = document.createElement('div');
            closeBtn.className = 'tab-close';
            closeBtn.textContent = '×';
            closeBtn.onmousedown = function(event) {
                event.preventDefault();
                event.stopPropagation();
                return false;
            };
            
            closeBtn.onclick = function(event) {
                event.preventDefault();
                event.stopPropagation();
                closeTab(event, tabName);
                return false;
            };
            
            closeBtn.onmouseup = function(event) {
                event.preventDefault();
                event.stopPropagation();
                return false;
            };
            
            newTab.appendChild(tabSpan);
            newTab.appendChild(closeBtn);
            newTab.onclick = function(event) {
                if (!event.target.classList.contains('tab-close')) {
                    switchTab(tabName);
                }
            };
            tabsContainer.appendChild(newTab);
            
            // 创建页面框架
            const pageContent = document.querySelector('.page-content');
            const newFrame = document.createElement('iframe');
            newFrame.id = `frame-${tabName}`;
            newFrame.className = 'page-frame';
            newFrame.src = url;
            pageContent.appendChild(newFrame);
            
            // 激活新标签页
            switchTab(tabName);
        }
        
        function switchTab(tabName) {
            // 更新活动标签页
            activeTab = tabName;
            
            // 更新标签页样式
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
                if (tab.getAttribute('data-page') === tabName) {
                    tab.classList.add('active');
                }
            });
            
            // 更新页面框架显示
            document.querySelectorAll('.page-frame').forEach(frame => {
                frame.classList.remove('active');
                if (frame.id === `frame-${tabName}`) {
                    frame.classList.add('active');
                }
            });
            
            // 更新菜单项样式
            document.querySelectorAll('.menu-item').forEach(item => {
                item.classList.remove('active');
                if (item.textContent.trim() === tabName) {
                    item.classList.add('active');
                }
            });
            
            // 更新二级菜单项样式
            document.querySelectorAll('.submenu-item').forEach(item => {
                item.classList.remove('active');
                if (item.textContent.trim() === tabName) {
                    item.classList.add('active');
                    // 展开父级菜单
                    const parentMenu = item.closest('.submenu').previousElementSibling;
                    const submenu = item.closest('.submenu');
                    parentMenu.classList.add('expanded');
                    submenu.classList.add('expanded');
                }
            });
        }
        
        // 二级菜单展开收起功能
        function toggleSubmenu(menuItem) {
            const submenu = menuItem.nextElementSibling;
            const isExpanded = menuItem.classList.contains('expanded');
            
            // 收起所有其他二级菜单
            document.querySelectorAll('.menu-item.has-submenu').forEach(item => {
                if (item !== menuItem) {
                    item.classList.remove('expanded');
                    item.nextElementSibling.classList.remove('expanded');
                }
            });
            
            // 切换当前菜单状态
            if (isExpanded) {
                menuItem.classList.remove('expanded');
                submenu.classList.remove('expanded');
            } else {
                menuItem.classList.add('expanded');
                submenu.classList.add('expanded');
            }
        }
        
        function closeTab(event, tabName) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            // 不能关闭工作台
            if (tabName === '工作台') {
                return;
            }
            
            // 从数组中移除
            const index = openTabs.indexOf(tabName);
            if (index > -1) {
                openTabs.splice(index, 1);
            }
            
            // 移除标签页
            const tab = document.querySelector(`[data-page="${tabName}"]`);
            if (tab) {
                tab.remove();
            }
            
            // 移除页面框架
            const frame = document.getElementById(`frame-${tabName}`);
            if (frame) {
                frame.remove();
            }
            
            // 如果关闭的是当前活动标签页，切换到最后一个标签页
            if (activeTab === tabName && openTabs.length > 0) {
                const lastTab = openTabs[openTabs.length - 1];
                switchTab(lastTab);
            }
        }
        
        function logout() {
            if (confirm('确定要退出系统吗？')) {
                // 这里可以添加退出逻辑
                alert('已退出系统');
            }
        }
        
        // 监听iframe中的页面跳转事件
        window.addEventListener('message', function(event) {
            if (event.data.type === 'openPage' || event.data.type === 'openTab') {
                openTab(event.data.title, event.data.url);
            }
        });
    </script>
</body>
</html>