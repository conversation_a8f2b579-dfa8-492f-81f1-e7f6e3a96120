<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>招采平台 - 新建采购计划</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        /* 顶部导航栏 */
        .top-nav {
            height: 60px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .logo {
            font-size: 20px;
            font-weight: bold;
            margin-right: 40px;
        }
        
        .nav-menu {
            display: flex;
            gap: 30px;
            flex: 1;
        }
        
        .nav-item {
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .nav-item:hover, .nav-item.active {
            background-color: rgba(255,255,255,0.2);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        /* 主体布局 */
        .main-container {
            height: 100vh;
        }
        
        /* 内容区域 */
        .content-wrapper {
            overflow-y: auto;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        /* 页签区域 */
        .tabs {
            display: flex;
            background: white;
            border-radius: 4px 4px 0 0;
            border: 1px solid #e8eaec;
            border-bottom: none;
            margin-bottom: -1px;
            position: relative;
            z-index: 1;
        }
        
        .tab {
            padding: 12px 20px;
            cursor: pointer;
            border-right: 1px solid #e8eaec;
            position: relative;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tab.active {
            color: #1890ff;
            background-color: #f5f7fa;
            border-bottom: 2px solid #1890ff;
            margin-bottom: -1px;
        }
        
        .tab-close {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .tab-close:hover {
            background-color: #e6e6e6;
        }
        
        /* 页面内容 */
        .page-content {
            background: white;
            border-radius: 0 4px 4px 4px;
            border: 1px solid #e8eaec;
            padding: 20px;
        }
        
        /* 页面标题 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e8eaec;
        }
        
        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #262626;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .help-icon {
            width: 16px;
            height: 16px;
            background: #1890ff;
            border-radius: 50%;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
        }
        
        .back-btn {
            padding: 8px 16px;
            background: #f0f0f0;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
            color: #262626;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .back-btn:hover {
            background: #e6e6e6;
        }
        
        /* 表单样式 */
        .form-section {
            margin-bottom: 32px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #1890ff;
            position: relative;
        }
        
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            bottom: -2px;
            width: 40px;
            height: 2px;
            background: #1890ff;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-grid.full-width {
            grid-template-columns: 1fr;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-group.required .form-label::after {
            content: '*';
            color: #ff4d4f;
            margin-left: 4px;
        }
        
        .form-label {
            font-size: 14px;
            color: #262626;
            font-weight: 500;
        }
        
        .form-control {
            height: 36px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 0 12px;
            font-size: 14px;
            width: 100%;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
        }
        
        .form-control.error {
            border-color: #ff4d4f;
        }
        
        .form-control.error:focus {
            box-shadow: 0 0 0 2px rgba(255,77,79,0.2);
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
            padding: 12px;
            line-height: 1.5;
        }
        
        .form-textarea.large {
            min-height: 120px;
        }
        
        .error-message {
            color: #ff4d4f;
            font-size: 12px;
            margin-top: 4px;
        }
        
        .help-text {
            color: #8c8c8c;
            font-size: 12px;
            margin-top: 4px;
        }
        
        /* 附件上传 */
        .upload-area {
            border: 2px dashed #d9d9d9;
            border-radius: 4px;
            padding: 40px 20px;
            text-align: center;
            background: #fafafa;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .upload-area:hover {
            border-color: #1890ff;
            background: #e6f7ff;
        }
        
        .upload-area.dragover {
            border-color: #1890ff;
            background: #e6f7ff;
        }
        
        .upload-icon {
            font-size: 48px;
            color: #d9d9d9;
            margin-bottom: 16px;
        }
        
        .upload-text {
            font-size: 16px;
            color: #262626;
            margin-bottom: 8px;
        }
        
        .upload-hint {
            font-size: 14px;
            color: #8c8c8c;
        }
        
        .file-list {
            margin-top: 16px;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            border: 1px solid #e8eaec;
            border-radius: 4px;
            margin-bottom: 8px;
            background: white;
        }
        
        .file-info {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }
        
        .file-icon {
            width: 32px;
            height: 32px;
            background: #1890ff;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }
        
        .file-details {
            flex: 1;
        }
        
        .file-name {
            font-size: 14px;
            color: #262626;
            margin-bottom: 4px;
        }
        
        .file-meta {
            font-size: 12px;
            color: #8c8c8c;
        }
        
        .file-actions {
            display: flex;
            gap: 8px;
        }
        
        /* 表单控件组合样式 */
        .form-control-with-btn {
            display: flex;
            gap: 8px;
        }
        
        .form-control-with-btn .form-control {
            flex: 1;
        }
        
        .btn-sm {
            height: 32px;
            padding: 0 12px;
            font-size: 12px;
        }
        
        .file-action {
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }
        
        .file-action:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .file-action.delete:hover {
            border-color: #ff4d4f;
            color: #ff4d4f;
        }
        
        /* 操作按钮 */
        .form-actions {
            background: #f8fafc;
            border-top: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            gap: 16px;
            z-index: 1000;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn-group {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            padding: 0 24px;
            height: 40px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            border: 1px solid transparent;
            transition: all 0.3s;
            min-width: 100px;
        }
        
        .btn-primary {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        
        .btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }
        
        .btn-success {
            background-color: #52c41a;
            color: white;
            border-color: #52c41a;
        }
        
        .btn-success:hover {
            background-color: #73d13d;
            border-color: #73d13d;
        }
        
        .btn-default {
            background-color: white;
            border-color: #d9d9d9;
            color: #262626;
        }
        
        .btn-default:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        /* 金额计算显示 */
        .amount-summary {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 4px;
            padding: 16px;
            margin-top: 16px;
        }
        
        .amount-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .amount-row:last-child {
            margin-bottom: 0;
            font-weight: 600;
            font-size: 16px;
            color: #1890ff;
            border-top: 1px solid #d9f7be;
            padding-top: 8px;
        }
        
        .amount-label {
            color: #262626;
        }
        
        .amount-value {
            color: #52c41a;
            font-weight: 500;
        }
        
        /* 为固定底部按钮留出空间 */
        .page-content {
            padding-bottom: 100px;
        }
        
        /* 响应式 */
        @media (max-width: 1200px) {
            .form-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .content-wrapper {
                padding: 10px;
            }
        }
    </style>
</head>
<body>

    
    <!-- 主体容器 -->
    <div class="main-container">
        <!-- 内容区域 -->
        <div class="content-wrapper">

            
            <!-- 页面内容 -->
            <div class="page-content">

                
                <!-- 表单内容 -->
                <form id="procurementForm">
                    <!-- 招标信息 -->
                    <div class="form-section">
                        <div class="section-title">招标信息</div>
                        <div class="form-grid">
                            <div class="form-group required">
                                <label class="form-label">计划项目编号</label>
                                <input type="text" class="form-control" name="planCode" value="CG-2024-0001" readonly>
                                <div class="help-text">根据采购类型、招标类型拼音大写-后9位编号，系统自动生成</div>
                            </div>
                            <div class="form-group required">
                                <label class="form-label">计划项目名称</label>
                                <input type="text" class="form-control" name="planName" placeholder="请输入计划项目名称" maxlength="50">
                                <div class="help-text">不超过50位</div>
                            </div>
                            <div class="form-group required">
                                <label class="form-label">采购类型</label>
                                <select class="form-control" name="procurementType">
                                    <option value="">请选择采购类型</option>
                                    <option value="goods">货物</option>
                                    <option value="construction" selected>施工</option>
                                    <option value="service">服务</option>
                                    <option value="other">其他</option>
                                </select>
                            </div>
                            <div class="form-group required">
                                <label class="form-label">招标类别</label>
                                <select class="form-control" name="bidCategory" id="bidCategory">
                                    <option value="">请选择招标类别</option>
                                    <optgroup label="工程类">
                                        <option value="construction">施工招标</option>
                                        <option value="design">勘察设计招标</option>
                                        <option value="epc">工程总承包招标（EPC）</option>
                                        <option value="supervision" selected>监理招标</option>
                                    </optgroup>
                                    <optgroup label="货物类">
                                        <option value="materials">材料采购招标</option>
                                        <option value="equipment">设备采购招标</option>
                                        <option value="goods_installation">货物供应及安装招标</option>
                                    </optgroup>
                                    <optgroup label="服务类">
                                        <option value="supervision_service">监理服务招标</option>
                                        <option value="consulting">咨询服务招标</option>
                                        <option value="property">物业服务招标</option>
                                    </optgroup>
                                    <option value="other_custom">其他</option>
                                </select>
                                <input type="text" class="form-control" name="bidCategoryCustom" id="bidCategoryCustom" placeholder="请输入其他招标类别" style="display: none; margin-top: 8px;">
                            </div>
                            <div class="form-group required">
                                <label class="form-label">采购方式</label>
                                <select class="form-control" name="procurementMethod">
                                    <option value="">请选择采购方式</option>
                                    <option value="public_comparison" selected>公告比选</option>
                                    <option value="invite_comparison">邀请比选</option>
                                    <option value="competitive_negotiation">竞争性磋商</option>
                                    <option value="competitive_dialogue">竞争性谈判</option>
                                    <option value="inquiry">询价择优</option>
                                    <option value="single_source">单一来源</option>
                                </select>
                            </div>
                            <div class="form-group required">
                                <label class="form-label">采购预算金额（万元）</label>
                                <input type="number" class="form-control" name="bidAmount" step="0.01">
                            </div>
                            <div class="form-group required">
                                <label class="form-label">资金来源</label>
                                <select class="form-control" name="fundingSource">
                                    <option value="">请选择资金来源</option>
                                    <option value="self" selected>自有资金</option>
                                    <option value="government">政府资本</option>
                                    <option value="social">其他社会资本</option>
                                </select>
                            </div>
                            <div class="form-group required">
                                <label class="form-label">招标时间</label>
                                <input type="text" class="form-control" name="bidTime" placeholder="如：2023年7月/2023年1季度" value="2024年第3季度">
                                <div class="help-text">示例：2023年7月/2023年1季度</div>
                            </div>
                            <div class="form-group required">
                                <label class="form-label">采购组织方式</label>
                                <select class="form-control" name="organizationMethod" id="organizationMethod">
                                    <option value="">请选择采购组织方式</option>
                                    <option value="self">自主招标</option>
                                    <option value="entrust" selected>委托招标</option>
                                </select>
                            </div>
                            <div class="form-group" id="agencyGroup">
                                <label class="form-label">代理机构</label>
                                <select class="form-control" name="agency" multiple>
                                    <option value="agency1" selected>某招标代理公司</option>
                                    <option value="agency2">某咨询代理公司</option>
                                    <option value="agency3">某工程代理公司</option>
                                </select>
                                <div class="help-text">代理机构非必填，可以后续确定后再重新提交</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">年采购计划（万元）</label>
                                <input type="number" class="form-control" name="annualPlan" step="0.01">
                                <div class="help-text">计划在本年度对外支付的金额不能大于采购预算金额</div>
                            </div>
                            <div class="form-group required">
                                <label class="form-label">项目经办人</label>
                                <div class="form-control-with-btn">
                                    <input type="text" class="form-control" name="applicant" value="张三" readonly>
                                    <button type="button" class="btn btn-default btn-sm" id="selectApplicantBtn">选择</button>
                                </div>
                                <div class="help-text">点击后，弹窗展示组织机构及人员信息</div>
                            </div>
                            <div class="form-group required">
                                <label class="form-label">立项决策日期</label>
                                <input type="date" class="form-control" name="decisionDate" id="decisionDate">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 项目信息 -->
                    <div class="form-section">
                        <div class="section-title">项目信息</div>
                        <div class="form-grid">


                            <div class="form-group required">
                                <label class="form-label">项目类型</label>
                                <select class="form-control" name="projectType">
                                    <option value="">请选择项目类型</option>
                                    <option value="legal" selected>依法必须招标项目</option>
                                    <option value="non_legal">非法定招标采购项目</option>
                                </select>
                            </div>
                            <div class="form-group required">
                                <label class="form-label">项目业主</label>
                                <input type="text" class="form-control" name="projectOwner" value="张三" readonly>
                                <div class="help-text">由项目经办人信息自动带入，可再次配置</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">所属二级公司单位</label>
                                <input type="text" class="form-control" name="secondaryCompany" placeholder="请输入所属二级公司单位" maxlength="255">
                                <div class="help-text">不超过255位</div>
                            </div>
                        </div>
                        
                        <div class="form-grid full-width">
                            <div class="form-group">
                                <label class="form-label">项目基本情况（建设内容及规模）</label>
                                <textarea class="form-control form-textarea" name="projectDescription" placeholder="请详细描述项目基本情况、建设内容及规模" maxlength="255"></textarea>
                                <div class="help-text">不超过255位</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">备注</label>
                                <textarea class="form-control form-textarea" name="remarks" placeholder="需要说明的其他情况，可不填" maxlength="255"></textarea>
                                <div class="help-text">需要说明的其他情况，可不填</div>
                            </div>
                        </div>
                    </div>
                    

                    
                    <!-- 立项决策文件 -->
                    <div class="form-section">
                        <div class="section-title">立项决策文件</div>
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-icon">📁</div>
                            <div class="upload-text">点击或拖拽文件到此处上传</div>
                            <div class="upload-hint">上传盖章立项依据，支持 PDF、DOC、DOCX、XLS、XLSX 格式，单个文件不超过 10MB</div>
                            <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.xls,.xlsx" style="display: none;">
                        </div>
                        <div class="file-list" id="fileList"></div>
                        <div class="form-help-text">请上传盖章的立项决策文件，包括但不限于项目立项批复、可行性研究报告批复、投资决策文件等相关材料</div>
                    </div>
                    

                    
                    <!-- 操作按钮 -->
                    <div class="form-actions">
                        <div class="btn-group">
                            <button type="button" class="btn btn-default" id="cancelBtn">取消</button>
                            <button type="button" class="btn btn-default" id="saveBtn">保存草稿</button>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-primary" id="submitBtn">提交审批</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('procurementForm');
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const fileList = document.getElementById('fileList');
            const budgetInput = document.querySelector('input[name="budgetAmount"]');
            const budgetDisplay = document.getElementById('budgetDisplay');
            const totalDisplay = document.getElementById('totalDisplay');
            const savingDisplay = document.getElementById('savingDisplay');
            
            let uploadedFiles = [];
            
            // 设置默认日期
            const today = new Date();
            const startDateInput = document.getElementById('startDate');
            const endDateInput = document.getElementById('endDate');
            
            // 设置计划开始时间为当前日期
            startDateInput.value = today.toISOString().split('T')[0];
            
            // 设置计划结束时间为当前日期+90天
            const endDate = new Date(today);
            endDate.setDate(today.getDate() + 90);
            endDateInput.value = endDate.toISOString().split('T')[0];
            
            // 设置采购预算金额和年采购计划默认显示0.00
            const bidAmountInput = document.querySelector('input[name="bidAmount"]');
            const annualPlanInput = document.querySelector('input[name="annualPlan"]');
            
            // 确保数字输入框正确显示两位小数
            if (bidAmountInput) {
                bidAmountInput.value = '0.00';
                console.log('设置采购预算金额默认值:', bidAmountInput.value);
            }
            if (annualPlanInput) {
                annualPlanInput.value = '0.00';
                console.log('设置年采购计划默认值:', annualPlanInput.value);
            }
            
            // 延迟再次设置，确保值被正确应用
            setTimeout(() => {
                if (bidAmountInput && !bidAmountInput.value) {
                    bidAmountInput.value = '0.00';
                }
                if (annualPlanInput && !annualPlanInput.value) {
                    annualPlanInput.value = '0.00';
                }
            }, 200);
            
            // 控制代理机构显示
            const organizationMethodSelect = document.getElementById('organizationMethod');
            const agencyGroup = document.getElementById('agencyGroup');
            
            function toggleAgencyGroup() {
                if (organizationMethodSelect.value === 'entrust') {
                    agencyGroup.style.display = 'block';
                } else {
                    agencyGroup.style.display = 'none';
                }
            }
            
            // 初始化显示状态
            toggleAgencyGroup();
            
            // 监听采购组织方式变化
            organizationMethodSelect.addEventListener('change', toggleAgencyGroup);
            
            // 招标类别联动
            const bidCategorySelect = document.getElementById('bidCategory');
            const bidCategoryCustom = document.getElementById('bidCategoryCustom');
            
            bidCategorySelect.addEventListener('change', function() {
                if (this.value === 'other_custom') {
                    bidCategoryCustom.style.display = 'block';
                    bidCategoryCustom.required = true;
                } else {
                    bidCategoryCustom.style.display = 'none';
                    bidCategoryCustom.required = false;
                    bidCategoryCustom.value = '';
                }
            });
            
            // 页签点击
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
                
                const closeBtn = tab.querySelector('.tab-close');
                closeBtn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    if (tab.classList.contains('active') && tabs.length > 1) {
                        const nextTab = tab.nextElementSibling || tab.previousElementSibling;
                        nextTab.classList.add('active');
                    }
                    tab.remove();
                });
            });
            
            // 文件上传
            uploadArea.addEventListener('click', () => fileInput.click());
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('drop', handleDrop);
            fileInput.addEventListener('change', handleFileSelect);
            
            function handleDragOver(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            }
            
            function handleDrop(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = Array.from(e.dataTransfer.files);
                addFiles(files);
            }
            
            function handleFileSelect(e) {
                const files = Array.from(e.target.files);
                addFiles(files);
            }
            
            function addFiles(files) {
                files.forEach(file => {
                    if (file.size > 10 * 1024 * 1024) {
                        alert(`文件 ${file.name} 超过 10MB 限制`);
                        return;
                    }
                    
                    const fileObj = {
                        id: Date.now() + Math.random(),
                        name: file.name,
                        size: file.size,
                        type: file.type,
                        file: file
                    };
                    
                    uploadedFiles.push(fileObj);
                    renderFileList();
                });
            }
            
            function renderFileList() {
                fileList.innerHTML = uploadedFiles.map(file => `
                    <div class="file-item" data-id="${file.id}">
                        <div class="file-info">
                            <div class="file-icon">📄</div>
                            <div class="file-details">
                                <div class="file-name">${file.name}</div>
                                <div class="file-meta">${formatFileSize(file.size)} | ${new Date().toLocaleString()}</div>
                            </div>
                        </div>
                        <div class="file-actions">
                            <button type="button" class="file-action" onclick="downloadFile('${file.id}')">下载</button>
                            <button type="button" class="file-action delete" onclick="removeFile('${file.id}')">删除</button>
                        </div>
                    </div>
                `).join('');
            }
            
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
            
            // 全局函数
            window.removeFile = function(fileId) {
                uploadedFiles = uploadedFiles.filter(file => file.id != fileId);
                renderFileList();
            };
            
            window.downloadFile = function(fileId) {
                const file = uploadedFiles.find(f => f.id == fileId);
                if (file) {
                    const url = URL.createObjectURL(file.file);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = file.name;
                    a.click();
                    URL.revokeObjectURL(url);
                }
            };
            
            // 金额计算
            budgetInput.addEventListener('input', function() {
                const budget = parseFloat(this.value) || 0;
                const saving = budget * 0.05; // 假设节约5%
                const total = budget - saving;
                
                budgetDisplay.textContent = budget.toFixed(2) + ' 万元';
                savingDisplay.textContent = saving.toFixed(2) + ' 万元';
                totalDisplay.textContent = total.toFixed(2) + ' 万元';
            });
            
            // 表单验证
            function validateForm() {
                const requiredFields = form.querySelectorAll('.form-group.required .form-control');
                let isValid = true;
                
                requiredFields.forEach(field => {
                    const errorMsg = field.parentNode.querySelector('.error-message');
                    if (errorMsg) errorMsg.remove();
                    
                    field.classList.remove('error');
                    
                    if (!field.value.trim()) {
                        field.classList.add('error');
                        const error = document.createElement('div');
                        error.className = 'error-message';
                        error.textContent = '此字段为必填项';
                        field.parentNode.appendChild(error);
                        isValid = false;
                    }
                });
                
                return isValid;
            }
            
            // 页面跳转函数
            function openPage(url, title) {
                if (window.parent && window.parent !== window) {
                    // 向父窗口发送消息，请求打开新页面
                    window.parent.postMessage({
                        type: 'openPage',
                        title: title,
                        url: url
                    }, '*');
                } else {
                    window.open(url, '_blank');
                }
            }
            
            // 返回列表
            function goBack() {
                if (window.parent && window.parent !== window) {
                    // 向父窗口发送消息，请求打开新页面
                    window.parent.postMessage({
                        type: 'openPage',
                        title: '采购计划管理',
                        url: '采购计划管理-列表页.html'
                    }, '*');
                } else {
                    window.location.href = '采购计划管理-列表页.html';
                }
            }
            
            // 按钮事件
            document.getElementById('cancelBtn').addEventListener('click', function() {
                if (confirm('确定要取消吗？未保存的数据将丢失。')) {
                    goBack();
                }
            });
            
            document.getElementById('saveBtn').addEventListener('click', function() {
                if (validateForm()) {
                    alert('保存成功！');
                }
            });
            
            document.getElementById('submitBtn').addEventListener('click', function() {
                if (validateForm()) {
                    if (confirm('确定要提交审批吗？提交后将无法修改。')) {
                        alert('提交成功！已进入审批流程。');
                    }
                }
            });
            
            // 返回按钮
            document.querySelector('.back-btn').addEventListener('click', function(e) {
                e.preventDefault();
                if (confirm('确定要返回吗？未保存的数据将丢失。')) {
                    goBack();
                }
            });
            
            // 采购类型和方式联动
            const typeSelect = document.querySelector('select[name="procurementType"]');
            const methodSelect = document.querySelector('select[name="procurementMethod"]');
            
            typeSelect.addEventListener('change', function() {
                const type = this.value;
                const methods = {
                    'goods': ['open', 'invite', 'inquiry'],
                    'project': ['open', 'invite', 'negotiate'],
                    'service': ['open', 'invite', 'negotiate', 'single']
                };
                
                const methodOptions = {
                    'open': '公开招标',
                    'invite': '邀请招标',
                    'negotiate': '竞争性谈判',
                    'inquiry': '询价',
                    'single': '单一来源'
                };
                
                methodSelect.innerHTML = '<option value="">请选择采购方式</option>';
                
                if (type && methods[type]) {
                    methods[type].forEach(method => {
                        const option = document.createElement('option');
                        option.value = method;
                        option.textContent = methodOptions[method];
                        methodSelect.appendChild(option);
                    });
                }
            });
        });
    </script>
</body>
</html>