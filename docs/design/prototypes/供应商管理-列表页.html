<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>供应商管理 - 列表页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 页面头部 */
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
        }

        .page-title::before {
            content: '';
            width: 4px;
            height: 20px;
            background: #3498db;
            margin-right: 12px;
        }

        .help-icon {
            margin-left: 8px;
            color: #95a5a6;
            cursor: pointer;
            font-size: 16px;
        }

        /* 查询区域 */
        .search-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .search-form {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .form-control {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .btn-group {
            display: flex;
            gap: 12px;
            margin-left: auto;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-outline {
            background: white;
            color: #3498db;
            border: 1px solid #3498db;
        }

        .btn-outline:hover {
            background: #3498db;
            color: white;
        }

        /* 高级查询区域 */
        .advanced-search {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        /* 功能操作栏 */
        .action-bar {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .action-left {
            display: flex;
            gap: 12px;
        }

        .btn-default {
            background: #ecf0f1;
            color: #2c3e50;
            border: 1px solid #bdc3c7;
        }

        .btn-default:hover {
            background: #d5dbdb;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        /* 数据表格 */
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-wrapper {
            overflow-x: auto;
            max-width: 100%;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            min-width: 1200px;
        }

        /* 列宽设置 */
        .col-checkbox { width: 50px; }
        .col-fixed { width: 150px; }
        .col-industry { width: 120px; }
        .col-contact { width: 100px; }
        .col-phone { width: 130px; }
        .col-email { width: 180px; }
        .col-scope { width: 200px; }
        .col-license { width: 120px; }
        .col-legal { width: 100px; }
        .col-time { width: 160px; }
        .col-action { width: 120px; }

        .data-table th,
        .data-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table td {
            font-size: 13px;
            color: #495057;
        }

        .data-table tr:hover {
            background: #f8f9fa;
        }

        /* 冻结列样式 */
        .col-checkbox,
        .col-fixed {
            position: sticky;
            left: 0;
            background: inherit;
            z-index: 5;
        }

        .col-action {
            position: sticky;
            right: 0;
            background: inherit;
            z-index: 5;
        }

        .data-table th.col-checkbox,
        .data-table th.col-fixed {
            z-index: 15;
        }

        .data-table th.col-action {
            z-index: 15;
        }

        .col-fixed {
            left: 50px;
        }

        /* 供应商名称链接 */
        .supplier-name-link {
            color: #3498db;
            text-decoration: none;
            font-weight: 500;
        }

        .supplier-name-link:hover {
            text-decoration: underline;
        }

        /* 操作按钮 */
        .operation-buttons {
            display: flex;
            gap: 8px;
        }

        .op-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 3px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .op-btn-warning {
            background: #f39c12;
            color: white;
        }

        .op-btn-warning:hover {
            background: #e67e22;
        }

        .op-btn-danger {
            background: #e74c3c;
            color: white;
        }

        .op-btn-danger:hover {
            background: #c0392b;
        }

        /* 分页组件 */
        .pagination-container {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }

        .pagination-info {
            font-size: 14px;
            color: #6c757d;
        }

        .pagination {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .page-btn {
            padding: 6px 12px;
            border: 1px solid #dee2e6;
            background: white;
            color: #495057;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .page-btn:hover {
            background: #e9ecef;
        }

        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .page-btn:disabled {
            background: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 8px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .help-text {
            font-size: 14px;
            line-height: 1.8;
            color: #555;
            margin-bottom: 15px;
        }

        .close-help {
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            font-size: 24px;
            color: #95a5a6;
            cursor: pointer;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .search-form {
                flex-direction: column;
            }
            
            .form-group {
                min-width: 100%;
            }
            
            .btn-group {
                margin-left: 0;
                margin-top: 15px;
            }
            
            .action-bar {
                flex-direction: column;
                gap: 15px;
            }
            
            .action-left {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="page-title">
                供应商管理
                <span class="help-icon" onclick="showHelp()" title="帮助说明">ⓘ</span>
            </div>
        </div>

        <!-- 查询区域 -->
        <div class="search-section">
            <div class="search-form">
                <div class="form-group">
                    <label class="form-label">代理机构名称</label>
                    <select class="form-control">
                        <option value="">全部机构</option>
                        <option value="1">中建工程咨询有限公司</option>
                        <option value="2">华信工程管理集团</option>
                        <option value="3">北京建设工程咨询公司</option>
                        <option value="4">上海工程项目管理有限公司</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">企业代码</label>
                    <input type="text" class="form-control" placeholder="请输入企业代码">
                </div>
                <div class="form-group">
                    <label class="form-label">联系人</label>
                    <input type="text" class="form-control" placeholder="请输入联系人">
                </div>
                <div class="form-group">
                    <label class="form-label">联系方式</label>
                    <input type="text" class="form-control" placeholder="请输入联系方式">
                </div>
                <div class="btn-group">
                    <button class="btn btn-primary">查询</button>
                    <button class="btn btn-secondary">重置</button>
                    <button class="btn btn-outline" onclick="toggleAdvancedSearch()">高级查询</button>
                </div>
            </div>

            <!-- 高级查询区域 -->
            <div class="advanced-search" id="advanced-search" style="display: none;">
                <div class="search-form">
                    <div class="form-group">
                        <label class="form-label">创建时间</label>
                        <input type="date" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label">至</label>
                        <input type="date" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label">更新时间</label>
                        <input type="date" class="form-control">
                    </div>
                    <div class="form-group">
                        <label class="form-label">至</label>
                        <input type="date" class="form-control">
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能操作栏 -->
        <div class="action-bar">
            <div class="action-left">
                <button class="btn btn-primary" onclick="createSupplier()">新增供应商</button>
                <button class="btn btn-default">批量导入</button>
                <button class="btn btn-success">导出数据</button>
                <button class="btn btn-danger">批量删除</button>
            </div>
        </div>

        <!-- 数据列表 -->
        <div class="table-container">
            <div class="table-wrapper">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th class="col-checkbox">
                                <input type="checkbox" id="select-all">
                            </th>
                            <th class="col-fixed">供应商名称</th>
                            <th class="col-industry">行业分类</th>
                            <th class="col-contact">联系人</th>
                            <th class="col-phone">联系电话</th>
                            <th class="col-email">联系邮箱</th>
                            <th class="col-scope">经营范围</th>
                            <th class="col-license">营业执照附件</th>
                            <th class="col-legal">法定代表人</th>
                            <th class="col-time">更新时间</th>
                            <th class="col-time">创建时间</th>
                            <th class="col-action">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="col-checkbox"><input type="checkbox"></td>
                            <td class="col-fixed"><a href="javascript:void(0)" onclick="viewDetail(1)" class="supplier-name-link">北京建筑材料有限公司</a></td>
                            <td>建筑材料</td>
                            <td>张经理</td>
                            <td>13800138001</td>
                            <td><EMAIL></td>
                            <td>建筑材料销售、工程施工</td>
                            <td><a href="#" style="color: #3498db;">查看附件</a></td>
                            <td>李建国</td>
                            <td>2024-01-15 14:30</td>
                            <td>2020-03-15 09:00</td>
                            <td class="col-action">
                                <div class="operation-buttons">
                                    <button class="op-btn op-btn-warning" title="编辑">编辑</button>
                                    <button class="op-btn op-btn-danger" title="删除">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-checkbox"><input type="checkbox"></td>
                            <td class="col-fixed"><a href="javascript:void(0)" onclick="viewDetail(2)" class="supplier-name-link">上海机械设备集团</a></td>
                            <td>机械设备</td>
                            <td>王总监</td>
                            <td>13900139002</td>
                            <td><EMAIL></td>
                            <td>机械设备制造、销售、维修</td>
                            <td><a href="#" style="color: #3498db;">查看附件</a></td>
                            <td>王志强</td>
                            <td>2024-01-14 16:45</td>
                            <td>2019-07-20 10:30</td>
                            <td class="col-action">
                                <div class="operation-buttons">
                                    <button class="op-btn op-btn-warning" title="编辑">编辑</button>
                                    <button class="op-btn op-btn-danger" title="删除">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-checkbox"><input type="checkbox"></td>
                            <td class="col-fixed"><a href="javascript:void(0)" onclick="viewDetail(3)" class="supplier-name-link">广州电子科技有限公司</a></td>
                            <td>电子科技</td>
                            <td>陈工程师</td>
                            <td>13700137003</td>
                            <td><EMAIL></td>
                            <td>电子产品研发、生产、销售</td>
                            <td><a href="#" style="color: #3498db;">查看附件</a></td>
                            <td>陈明华</td>
                            <td>2024-01-13 10:20</td>
                            <td>2024-01-10 14:15</td>
                            <td class="col-action">
                                <div class="operation-buttons">
                                    <button class="op-btn op-btn-warning" title="编辑">编辑</button>
                                    <button class="op-btn op-btn-danger" title="删除">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-checkbox"><input type="checkbox"></td>
                            <td class="col-fixed"><a href="javascript:void(0)" onclick="viewDetail(4)" class="supplier-name-link">深圳软件开发公司</a></td>
                            <td>软件开发</td>
                            <td>刘项目经理</td>
                            <td>13600136004</td>
                            <td><EMAIL></td>
                            <td>软件开发、系统集成、技术服务</td>
                            <td><a href="#" style="color: #3498db;">查看附件</a></td>
                            <td>刘德华</td>
                            <td>2024-01-12 09:15</td>
                            <td>2021-09-01 11:20</td>
                            <td class="col-action">
                                <div class="operation-buttons">
                                    <button class="op-btn op-btn-warning" title="编辑">编辑</button>
                                    <button class="op-btn op-btn-danger" title="删除">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="col-checkbox"><input type="checkbox"></td>
                            <td class="col-fixed"><a href="javascript:void(0)" onclick="viewDetail(5)" class="supplier-name-link">成都物流运输有限公司</a></td>
                            <td>物流运输</td>
                            <td>赵经理</td>
                            <td>13500135005</td>
                            <td><EMAIL></td>
                            <td>货物运输、仓储服务、物流配送</td>
                            <td><a href="#" style="color: #3498db;">查看附件</a></td>
                            <td>赵国强</td>
                            <td>2024-01-11 15:40</td>
                            <td>2022-05-15 16:30</td>
                            <td class="col-action">
                                <div class="operation-buttons">
                                    <button class="op-btn op-btn-warning" title="编辑">编辑</button>
                                    <button class="op-btn op-btn-danger" title="删除">删除</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页组件 -->
            <div class="pagination-container">
                <div class="pagination-info">
                    显示第 1-5 条，共 25 条记录
                </div>
                <div class="pagination">
                    <button class="page-btn" disabled>上一页</button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">3</button>
                    <button class="page-btn">4</button>
                    <button class="page-btn">5</button>
                    <button class="page-btn">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div class="help-modal" id="helpModal">
        <div class="help-content">
            <button class="close-help" onclick="hideHelp()">&times;</button>
            <div class="help-title">供应商管理 - 操作说明</div>
            <div class="help-text">
                <strong>功能说明：</strong><br>
                本页面用于管理供应商信息，支持查询、新增、编辑、删除等操作。
            </div>
            <div class="help-text">
                <strong>查询功能：</strong><br>
                • <strong>基础查询：</strong>支持按代理机构名称、企业代码、联系人、联系方式进行查询<br>
                • <strong>高级查询：</strong>支持按创建时间、更新时间范围进行筛选
            </div>
            <div class="help-text">
                <strong>列表字段说明：</strong><br>
                • <strong>供应商名称：</strong>点击可查看详情<br>
                • <strong>行业分类：</strong>供应商所属行业类别<br>
                • <strong>联系人：</strong>主要联系人姓名<br>
                • <strong>联系电话：</strong>联系电话号码<br>
                • <strong>联系邮箱：</strong>联系邮箱地址<br>
                • <strong>经营范围：</strong>企业经营业务范围<br>
                • <strong>营业执照附件：</strong>营业执照扫描件<br>
                • <strong>法定代表人：</strong>企业法定代表人姓名
            </div>
            <div class="help-text">
                <strong>操作说明：</strong><br>
                1. 使用查询条件筛选供应商信息<br>
                2. 点击"新增供应商"创建新的供应商档案<br>
                3. 点击供应商名称查看详细信息<br>
                4. 使用"编辑"按钮修改供应商信息<br>
                5. 支持批量导入、导出和删除操作
            </div>
        </div>
    </div>

    <script>
        // 高级查询切换
        function toggleAdvancedSearch() {
            const advancedSearch = document.getElementById('advanced-search');
            if (advancedSearch.style.display === 'none') {
                advancedSearch.style.display = 'block';
            } else {
                advancedSearch.style.display = 'none';
            }
        }

        // 全选功能
        document.getElementById('select-all').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // 查看详情
        function viewDetail(id) {
            window.location.href = '供应商管理-详情页.html?id=' + id;
        }

        // 新增供应商
        function createSupplier() {
            window.location.href = '供应商管理-新建编辑页.html';
        }

        // 帮助弹窗
        function showHelp() {
            document.getElementById('helpModal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('helpModal').style.display = 'none';
        }

        // 点击弹窗外部关闭弹窗
        document.getElementById('helpModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });
    </script>
</body>
</html>