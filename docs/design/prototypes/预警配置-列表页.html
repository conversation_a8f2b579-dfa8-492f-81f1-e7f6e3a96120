<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预警配置</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .help-icon {
            width: 20px;
            height: 20px;
            background: #3498db;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .help-icon:hover {
            background: #2980b9;
        }

        .config-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .config-section {
            border-bottom: 1px solid #eee;
            padding: 25px;
        }

        .config-section:last-child {
            border-bottom: none;
        }

        .config-section-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
            display: inline-block;
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }

        .config-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }

        .config-label {
            font-weight: 500;
            color: #495057;
            flex: 1;
        }

        .config-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #3498db;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .input-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-control {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            width: 80px;
            text-align: center;
        }

        .input-unit {
            font-size: 12px;
            color: #666;
        }

        .config-actions {
            padding: 25px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .btn {
            padding: 10px 24px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        /* 帮助弹窗样式 */
        .help-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .help-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 8px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .help-close {
            position: absolute;
            right: 20px;
            top: 20px;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #aaa;
        }

        .help-close:hover {
            color: #333;
        }

        .help-title {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 20px;
        }

        .help-content h4 {
            color: #3498db;
            margin: 20px 0 10px 0;
            font-size: 16px;
        }

        .help-content ul {
            margin-left: 20px;
            margin-bottom: 15px;
        }

        .help-content li {
            margin-bottom: 8px;
            line-height: 1.6;
        }

        .help-content strong {
            color: #2c3e50;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="page-title">
                预警配置
                <div class="help-icon" onclick="showHelp()" title="查看帮助">?</div>
            </div>
        </div>

        <!-- 配置内容 -->
        <div class="config-content">
            <!-- 招标公告预警配置 -->
            <div class="config-section">
                <div class="config-section-title">招标公告预警配置</div>
                <div class="config-grid">
                    <div class="config-item">
                        <div class="config-label">招标公告变更预警</div>
                        <div class="config-control">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <div class="input-group">
                                <span>设置次数：</span>
                                <input type="number" class="form-control" value="5" min="1" max="99">
                                <span class="input-unit">次</span>
                            </div>
                        </div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">采购邀请函变更</div>
                        <div class="config-control">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <div class="input-group">
                                <span>设置次数：</span>
                                <input type="number" class="form-control" value="5" min="1" max="99">
                                <span class="input-unit">次</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中标公告预警配置 -->
            <div class="config-section">
                <div class="config-section-title">中标公告预警配置</div>
                <div class="config-grid">
                    <div class="config-item">
                        <div class="config-label">中标候选人公示(中标金额大于预算价)</div>
                        <div class="config-control">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">中标候选人公示(公示日期少于3日)</div>
                        <div class="config-control">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">中标候选人公示变更</div>
                        <div class="config-control">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <div class="input-group">
                                <span>设置次数：</span>
                                <input type="number" class="form-control" value="5" min="1" max="99">
                                <span class="input-unit">次</span>
                            </div>
                        </div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">中标结果公告变更</div>
                        <div class="config-control">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <div class="input-group">
                                <span>设置次数：</span>
                                <input type="number" class="form-control" value="5" min="1" max="99">
                                <span class="input-unit">次</span>
                            </div>
                        </div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">中标结果通知书变更</div>
                        <div class="config-control">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <div class="input-group">
                                <span>设置次数：</span>
                                <input type="number" class="form-control" value="5" min="1" max="99">
                                <span class="input-unit">次</span>
                            </div>
                        </div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">多次中标提醒</div>
                        <div class="config-control">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <div class="input-group">
                                <span>设置次数：</span>
                                <input type="number" class="form-control" value="5" min="1" max="99">
                                <span class="input-unit">次</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 投标预警配置 -->
            <div class="config-section">
                <div class="config-section-title">投标预警配置</div>
                <div class="config-grid">
                    <div class="config-item">
                        <div class="config-label">多次投标提醒</div>
                        <div class="config-control">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <div class="input-group">
                                <span>设置次数：</span>
                                <input type="number" class="form-control" value="5" min="1" max="99">
                                <span class="input-unit">次</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 评标公告预警配置 -->
            <div class="config-section">
                <div class="config-section-title">评标公告预警配置</div>
                <div class="config-grid">
                    <div class="config-item">
                        <div class="config-label">预算价异常接近</div>
                        <div class="config-control">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <div class="input-group">
                                <span>设置接近范围：</span>
                                <input type="number" class="form-control" value="5" min="1" max="999">
                                <span class="input-unit">万元以内</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 流标或中止预警配置 -->
            <div class="config-section">
                <div class="config-section-title">流标或中止预警配置</div>
                <div class="config-grid">
                    <div class="config-item">
                        <div class="config-label">标段流标</div>
                        <div class="config-control">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <span class="input-unit">流标或中止</span>
                        </div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">标段终止</div>
                        <div class="config-control">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <span class="input-unit">流标或中止</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 履约执行预警配置 -->
            <div class="config-section">
                <div class="config-section-title">履约执行预警配置</div>
                <div class="config-grid">
                    <div class="config-item">
                        <div class="config-label">成交结果公告变更</div>
                        <div class="config-control">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <div class="input-group">
                                <span>设置次数：</span>
                                <input type="number" class="form-control" value="5" min="1" max="99">
                                <span class="input-unit">次</span>
                            </div>
                        </div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">成交结果通知书变更</div>
                        <div class="config-control">
                            <label class="switch">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <div class="input-group">
                                <span>设置次数：</span>
                                <input type="number" class="form-control" value="5" min="1" max="99">
                                <span class="input-unit">次</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="config-actions">
                <button class="btn btn-primary" onclick="saveConfig()">保存配置</button>
                <button class="btn btn-secondary" onclick="resetConfig()">重置配置</button>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="helpModal" class="help-modal">
        <div class="help-content">
            <span class="help-close" onclick="hideHelp()">&times;</span>
            <h3 class="help-title">预警配置说明</h3>
            <p>预警配置用于设置系统自动监控和预警的规则，确保招标采购过程的合规性和风险控制。</p>
            
            <h4>招标公告预警配置</h4>
            <ul>
                <li><strong>招标公告变更预警：</strong>监控招标公告的变更次数，超过设定次数时触发预警</li>
                <li><strong>采购邀请函变更：</strong>监控采购邀请函的变更次数，防止频繁变更影响公平性</li>
            </ul>
            
            <h4>中标公告预警配置</h4>
            <ul>
                <li><strong>中标金额大于预算价：</strong>当中标金额超出项目预算价时自动触发预警</li>
                <li><strong>公示日期少于3日：</strong>当中标候选人公示期少于法定3天时触发预警</li>
                <li><strong>中标候选人公示变更：</strong>监控中标候选人公示的变更次数</li>
                <li><strong>中标结果公告变更：</strong>监控中标结果公告的变更次数</li>
                <li><strong>中标结果通知书变更：</strong>监控中标结果通知书的变更次数</li>
                <li><strong>多次中标提醒：</strong>监控供应商在指定时间内的中标次数</li>
            </ul>
            
            <h4>投标预警配置</h4>
            <ul>
                <li><strong>多次投标提醒：</strong>监控供应商在指定时间内的投标次数，防止恶意投标</li>
            </ul>
            
            <h4>评标公告预警配置</h4>
            <ul>
                <li><strong>预算价异常接近：</strong>当多个投标报价过于接近预算价时触发预警，防止串标</li>
            </ul>
            
            <h4>流标或中止预警配置</h4>
            <ul>
                <li><strong>标段流标：</strong>当标段出现流标情况时自动触发预警</li>
                <li><strong>标段终止：</strong>当标段被终止时自动触发预警</li>
            </ul>
            
            <h4>履约执行预警配置</h4>
            <ul>
                <li><strong>成交结果公告变更：</strong>监控成交结果公告的变更次数</li>
                <li><strong>成交结果通知书变更：</strong>监控成交结果通知书的变更次数</li>
            </ul>
            
            <h4>配置说明</h4>
            <ul>
                <li>开关控制：每个预警项都可以通过开关进行启用或禁用</li>
                <li>次数设置：对于需要设置次数的预警项，可以自定义触发预警的次数阈值</li>
                <li>范围设置：对于预算价异常接近，可以设置触发预警的金额范围</li>
                <li>配置修改后立即生效，影响后续的预警生成</li>
                <li>建议根据实际业务需求合理设置各项预警参数</li>
            </ul>
        </div>
    </div>

    <script>
        // 显示帮助
        function showHelp() {
            document.getElementById('helpModal').style.display = 'block';
        }

        // 隐藏帮助
        function hideHelp() {
            document.getElementById('helpModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('helpModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

        // 保存配置
        function saveConfig() {
            // 收集所有配置数据
            const configs = [];
            const configItems = document.querySelectorAll('.config-item');
            
            configItems.forEach(item => {
                const label = item.querySelector('.config-label').textContent;
                const switchInput = item.querySelector('input[type="checkbox"]');
                const numberInput = item.querySelector('input[type="number"]');
                
                const config = {
                    name: label,
                    enabled: switchInput.checked,
                    value: numberInput ? numberInput.value : null
                };
                
                configs.push(config);
            });
            
            console.log('保存配置:', configs);
            alert('配置保存成功！');
        }

        // 重置配置
        function resetConfig() {
            if (confirm('确定要重置所有配置为默认值吗？')) {
                // 重置所有开关为开启状态
                document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
                    checkbox.checked = true;
                });
                
                // 重置所有数值输入为默认值
                document.querySelectorAll('input[type="number"]').forEach(input => {
                    if (input.closest('.config-item').textContent.includes('预算价异常接近')) {
                        input.value = 5; // 预算价异常接近默认5万元
                    } else {
                        input.value = 5; // 其他次数类默认5次
                    }
                });
                
                alert('配置已重置为默认值！');
            }
        }

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('预警配置页面加载完成');
        });
    </script>
</body>
</html>