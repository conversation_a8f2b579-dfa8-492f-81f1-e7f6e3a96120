<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目标段管理 - 招采平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            font-size: 14px;
        }

        /* 页面整体布局 */
        .page-container {
            max-width: 1400px;
            margin: 0 auto;
            min-height: 100vh;
        }

        /* 页面标题区 */
        .page-header {
            background: white;
            padding: 20px 0 15px;
            border-bottom: 1px solid #e6e6e6;
            margin-bottom: 20px;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #262626;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .help-icon {
            width: 16px;
            height: 16px;
            background-color: #1890ff;
            border-radius: 50%;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
        }



        /* 内容区域 */
        .content-area {
            padding: 20px;
        }

        /* 页面内容 */
        .page-content {
            background: white;
            border-radius: 0;
            border: 1px solid #e8eaec;
            padding: 24px;
        }

        /* 页签容器 */
        .tab-container {
            margin: 20px 0;
        }

        .tab-header {
            display: flex;
            border-bottom: 2px solid #e6e6e6;
            margin-bottom: 20px;
        }

        .tab-btn {
            padding: 12px 24px;
            border: none;
            background: transparent;
            cursor: pointer;
            font-size: 14px;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .tab-btn.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
            font-weight: 500;
        }

        .tab-btn:hover {
            color: #1890ff;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 主页签区域 */
        .main-tabs {
            display: flex;
            border-bottom: 1px solid #e6e6e6;
            margin-bottom: 24px;
        }

        .tab-item {
            padding: 12px 24px;
            cursor: pointer;
            font-size: 14px;
            color: #666;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
            background: none;
            border: none;
        }

        .tab-item.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
            font-weight: 500;
        }

        .tab-item:hover {
            color: #1890ff;
        }

        /* 视图按钮区域 */
        .view-buttons {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
        }

        .view-btn {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            background: white;
            color: #666;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }

        .view-btn.active {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .view-btn:hover {
            border-color: #40a9ff;
            color: #40a9ff;
            background-color: #f0f9ff;
        }

        /* 视图容器样式 */
        .view-content {
            display: none;
        }

        .view-content.active {
            display: block;
        }

        /* 视图内的查询区域样式 */
        .view-content .search-area {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .view-content .search-form {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 15px;
            align-items: end;
        }

        .view-content .search-buttons {
            display: flex;
            gap: 10px;
            align-items: end;
        }

        /* 查询区域 */
        .search-area {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .search-form {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 13px;
            color: #555;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-control {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .btn-group {
            display: flex;
            gap: 10px;
            align-items: end;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background: #1890ff;
            color: white;
            border: 1px solid #1890ff;
        }

        .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }

        .btn-secondary {
            background: #1890ff;
            color: white;
            border: 1px solid #1890ff;
        }

        .btn-secondary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }

        .btn-outline {
            background: white;
            color: #1890ff;
            border: 1px solid #1890ff;
        }

        .btn-outline:hover {
            background: #1890ff;
            color: white;
        }

        .btn-link {
            background: none;
            color: #1890ff;
            text-decoration: none;
            border: none;
            padding: 8px 0;
        }

        /* 高级查询 */
        .advanced-search {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        /* 功能操作栏 */
        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .btn-success {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .btn-success:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }

        .btn-warning {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .btn-warning:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
        }

        .btn-danger {
            background-color: #ff4d4f;
            color: white;
            border-color: #ff4d4f;
        }

        .btn-danger:hover {
            background-color: #ff7875;
            border-color: #ff7875;
        }

        /* 表格容器 */
        .table-container {
            background: white;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .data-table {
            width: 100%;
            min-width: 2200px;
            border-collapse: collapse;
            table-layout: fixed;
        }

        .data-table th,
        .data-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e8eaec;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .data-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #262626;
        }

        .data-table tr:hover td {
            background-color: #e6f7ff;
        }

        /* 表格横向滚动容器 */
        .table-container {
            overflow-x: auto;
            position: relative;
        }

        /* 自定义滚动条样式 */
        .table-container::-webkit-scrollbar {
            height: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 冻结列样式 */
        .frozen-column {
            position: sticky;
            background: white;
            z-index: 10;
        }

        .frozen-column.checkbox-col {
            left: 0;
            width: 50px;
            text-align: center;
        }

        .frozen-column.code-col {
            left: 50px;
            width: 150px;
        }

        .frozen-column.action-col {
            right: 0;
            width: 180px;
            text-align: center;
        }

        .data-table th.frozen-column {
            background-color: #f8f9fa;
        }

        .data-table tr:hover .frozen-column {
            background-color: #e6f7ff;
        }

        /* 冻结列阴影 */
        .frozen-column.checkbox-col::after {
            content: '';
            position: absolute;
            top: 0;
            right: -1px;
            bottom: 0;
            width: 1px;
            background: linear-gradient(to right, rgba(0,0,0,0.1), transparent);
        }

        .frozen-column.code-col::after {
            content: '';
            position: absolute;
            top: 0;
            right: -1px;
            bottom: 0;
            width: 1px;
            background: linear-gradient(to right, rgba(0,0,0,0.1), transparent);
        }

        .frozen-column.action-col::before {
            content: '';
            position: absolute;
            left: -1px;
            top: 0;
            bottom: 0;
            width: 1px;
            background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.1), transparent);
        }
        
        .action-placeholder {
            color: #999;
            font-size: 14px;
            text-align: center;
            display: inline-block;
            width: 100%;
        }

        /* 二级表格样式 */
        .section-container {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            margin: 10px 0;
        }

        .section-header {
            font-size: 14px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e6e6e6;
        }

        .section-table {
            width: 100%;
            min-width: 1000px;
            border-collapse: collapse;
            table-layout: fixed;
            background: white;
            border-radius: 4px;
            overflow: hidden;
        }

        .section-table th,
        .section-table td {
            padding: 10px 8px;
            text-align: left;
            border-bottom: 1px solid #e6e6e6;
            font-size: 12px;
        }

        .section-table th {
            background: #f1f3f4;
            font-weight: 600;
            color: #2c3e50;
        }

        .section-table tbody tr:hover {
            background: #f8f9fa;
        }

        /* 二级表格冻结列 */
        .section-table .frozen-section-name {
            position: sticky;
            left: 0;
            background: white;
            z-index: 5;
            width: 200px;
        }

        .section-table th.frozen-section-name {
            background: #f1f3f4;
        }

        .section-table tr:hover .frozen-section-name {
            background: #f8f9fa;
        }

        .section-table .frozen-section-name::after {
            content: '';
            position: absolute;
            top: 0;
            right: -1px;
            bottom: 0;
            width: 1px;
            background: linear-gradient(to right, rgba(0,0,0,0.1), transparent);
        }

        /* 表格链接 */
        .table-link {
            color: #1890ff;
            text-decoration: none;
            cursor: pointer;
        }

        .table-link:hover {
            text-decoration: underline;
        }

        /* 操作按钮 */
        .action-btn {
            padding: 4px 8px;
            margin: 0 2px;
            border: none;
            border-radius: 2px;
            cursor: pointer;
            font-size: 12px;
        }

        .action-btn.btn-primary {
            background-color: #1890ff;
            color: white;
        }

        .action-btn.btn-warning {
            background-color: #faad14;
            color: white;
        }

        .action-btn.btn-danger {
            background-color: #ff4d4f;
            color: white;
        }

        /* 状态标签 */
        .status-tag {
            padding: 2px 8px;
            border-radius: 2px;
            font-size: 12px;
        }

        /* 标段审核状态 */
        .status-pending { background-color: #fff7e6; color: #fa8c16; }
        .status-processing { background-color: #e6f7ff; color: #1890ff; }
        .status-approved { background-color: #e8f5e8; color: #52c41a; }
        .status-rejected { background-color: #fff2f0; color: #ff4d4f; }

        /* 审核状态 */
        .audit-pending { background-color: #fff7e6; color: #fa8c16; }
        .audit-processing { background-color: #e6f7ff; color: #1890ff; }
        .audit-approved { background-color: #e8f5e8; color: #52c41a; }
        .audit-rejected { background-color: #fff2f0; color: #ff4d4f; }

        /* 展开图标 */
        .expand-icon {
            cursor: pointer;
            margin-right: 5px;
            transition: transform 0.2s;
        }

        .expand-icon.expanded {
            transform: rotate(90deg);
        }

        /* 子表格 */
        .sub-table {
            background: #f8f9fa;
        }

        .sub-table .data-table {
            margin: 10px 0;
        }

        .sub-table th {
            background: #e9ecef;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 0;
            background-color: #f8f9fa;
            border-top: 1px solid #e8eaec;
        }

        .pagination-info {
            color: #8c8c8c;
            font-size: 14px;
            font-size: 12px;
        }

        .pagination-controls {
            display: flex;
            gap: 5px;
            align-items: center;
        }

        .page-btn {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            background-color: white;
            cursor: pointer;
            border-radius: 2px;
            font-size: 14px;
            color: #262626;
        }

        .page-btn.active {
            background-color: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .page-btn:hover:not(.active) {
            background-color: #f5f5f5;
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 帮助弹窗 */
        .help-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 500px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .help-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e6e6e6;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #7f8c8d;
        }

        /* 响应式 */
        @media (max-width: 1200px) {
            .page-container {
                max-width: 100%;
            }
            
            .search-form {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 内容区域 -->
        <div class="content-area">


                <!-- 页面内容 -->
                <div class="page-content">
                    <!-- 主页签区域 -->
                    <div class="main-tabs">
                        <div class="tab-item active" data-tab="todo" onclick="switchMainTab('todo')">
                            待办
                        </div>
                        <div class="tab-item" data-tab="done" onclick="switchMainTab('done')">
                            已办
                        </div>
                        <div class="tab-item" data-tab="all" onclick="switchMainTab('all')">
                            全部
                        </div>
                    </div>
                        
                    
                    <!-- 视图按钮区域 -->
                    <div class="view-buttons">
                        <button class="view-btn active" data-view="project-view" onclick="switchView('project')">
                            项目视图
                        </button>
                        <button class="view-btn" data-view="section-view" onclick="switchView('section')">
                            标段视图
                        </button>
                    </div>
                    
                    <!-- 查询区域 -->
                    <div class="search-area">
                        <div class="search-form">
                            <div class="form-group">
                                <label class="form-label">项目名称</label>
                                <input type="text" class="form-control" placeholder="请输入项目名称">
                            </div>
                            <div class="form-group">
                                <label class="form-label">审核状态</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="pending">待审核</option>
                                    <option value="processing">审核中</option>
                                    <option value="approved">已通过</option>
                                    <option value="rejected">已拒绝</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">采购类型</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="goods">货物</option>
                                    <option value="service">服务</option>
                                    <option value="engineering">工程</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">采购方式</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="tender">公开招标</option>
                                    <option value="invite">邀请招标</option>
                                    <option value="negotiate">竞争性谈判</option>
                                    <option value="inquiry">询价</option>
                                </select>
                            </div>
                            <div class="btn-group">
                                <button class="btn btn-primary">查询</button>
                                <button class="btn btn-secondary">重置</button>
                                <button class="btn btn-outline" onclick="toggleAdvanced(this)">高级查询</button>
                            </div>
                        </div>

                        <!-- 高级查询 -->
                        <div class="advanced-search" id="advanced-search" style="display: none;">
                            <div class="search-form">
                                <div class="form-group">
                                    <label class="form-label">采购组织方式</label>
                                    <select class="form-control">
                                        <option value="">全部</option>
                                        <option value="centralized">集中采购</option>
                                        <option value="decentralized">分散采购</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">资金来源</label>
                                    <select class="form-control">
                                        <option value="">全部</option>
                                        <option value="fiscal">财政资金</option>
                                        <option value="self">自有资金</option>
                                        <option value="loan">贷款资金</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">招标金额</label>
                                    <input type="text" class="form-control" placeholder="请输入金额范围">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">申请人</label>
                                    <input type="text" class="form-control" placeholder="请输入申请人">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">招标时间</label>
                                    <input type="date" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">计划时间</label>
                                    <input type="date" class="form-control">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">创建时间</label>
                                    <input type="date" class="form-control">
                                </div>
                            </div>
                        </div>
                    </div>

                    
                    <!-- 功能操作栏 -->
                    <div class="action-bar">
                        <div class="action-buttons">
                            <button class="btn btn-success" onclick="createProject()">新建项目标段</button>
                            <button class="btn btn-danger">批量删除</button>
                        </div>
                    </div>

                    <!-- 项目列表表格 -->
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th class="frozen-column checkbox-col">
                                        <input type="checkbox" id="selectAll">
                                    </th>
                                    <th class="frozen-column code-col">项目编号</th>
                                    <th style="width: 200px;">项目名称</th>
                                    <th style="width: 100px;">采购类型</th>
                                    <th style="width: 120px;">招标类别</th>
                                    <th style="width: 120px;">采购方式</th>
                                    <th style="width: 140px;">采购组织方式</th>
                                    <th style="width: 160px;">代理机构</th>
                                    <th style="width: 100px;">申请人</th>
                                    <th style="width: 160px;">项目业主</th>
                                    <th style="width: 120px;">招标时间</th>
                                    <th style="width: 140px;">计划开始时间</th>
                                    <th style="width: 140px;">计划结束时间</th>
                                    <th style="width: 140px;">创建时间</th>
                                    <th class="frozen-column action-col">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                               <tr>
                                                   <td class="frozen-column checkbox-col">
                                                       <input type="checkbox">
                                                   </td>
                                                   <td class="frozen-column code-col">
                                                       <span class="expand-icon" onclick="toggleProject(this)">▶</span>
                                                       <a href="#" class="table-link" onclick="viewProjectDetail('PRJ-2024-001')">PRJ-2024-001</a>
                                                   </td>
                                                   <td>办公设备采购项目</td>
                                                   <td>货物采购</td>
                                                   <td>公开招标</td>
                                                   <td>公开招标</td>
                                                   <td>公开招标</td>
                                                   <td>集中采购</td>
                                                   <td>XX招标代理有限公司</td>
                                                   <td>张三</td>
                                                   <td>XX有限公司</td>
                                                   <td>2024-03-15</td>
                                                   <td>2024-03-01</td>
                                                   <td>2024-04-30</td>
                                                   <td>2024-02-20</td>
                                                   <td class="frozen-column action-col">
                                                       <button class="action-btn btn-warning" onclick="splitSection('PRJ-2024-001')">标段拆分</button>
                                                   </td>
                                               </tr>
                                               <tr class="sub-table" id="sub-PRJ-2024-001" style="display: none;">
                                                   <td colspan="15">
                                                       <div style="padding: 10px;">
                                                           <h4 style="margin-bottom: 10px; color: #2c3e50;">项目下标段信息</h4>
                                                           <table class="data-table">
                                                               <thead>
                                                                   <tr>
                                                                       <th style="width: 200px;">标段名称</th>
                                                                       <th style="width: 120px;">标段阶段</th>
                                                                       <th style="width: 120px;">审核状态</th>
                                                                       <th>操作</th>
                                                                   </tr>
                                                               </thead>
                                                               <tbody>
                                                                   <tr>
                                                                       <td><a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-001-00')">文具用品采购标段</a></td>
                                                                       <td>准备阶段</td>
                                                                       <td><span class="status-tag status-pending">待审核</span></td>
                                                                       <td>
                                                                           <button class="action-btn btn-primary" onclick="editSection('SEC-2024-001-00')">编辑</button>
                                                                           <button class="action-btn btn-warning" onclick="auditSection('SEC-2024-001-00')">审核</button>
                                                                       </td>
                                                                   </tr>
                                                                   <tr>
                                                                       <td><a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-001-01')">办公桌椅采购标段</a></td>
                                                                       <td>交易公告</td>
                                                                       <td><span class="status-tag status-processing">审核中</span></td>
                                                                       <td>
                                                                           <!-- 非准备阶段，无编辑按钮 -->
                                                                       </td>
                                                                   </tr>
                                                                   <tr>
                                                                       <td><a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-001-02')">电脑设备采购标段</a></td>
                                                                       <td>补遗、澄清、答疑</td>
                                                                       <td><span class="status-tag status-pending">待审核</span></td>
                                                                       <td>
                                                                           <!-- 非准备阶段，无编辑按钮 -->
                                                                       </td>
                                                                   </tr>
                                                               </tbody>
                                                           </table>
                                                       </div>
                                                   </td>
                                               </tr>
                                               <tr>
                                                   <td class="frozen-column checkbox-col">
                                                       <input type="checkbox">
                                                   </td>
                                                   <td class="frozen-column code-col">
                                                       <span class="expand-icon" onclick="toggleProject(this)">▶</span>
                                                       <a href="#" class="table-link" onclick="viewProjectDetail('PRJ-2024-002')">PRJ-2024-002</a>
                                                   </td>
                                                   <td>IT系统建设项目</td>
                                                   <td>服务采购</td>
                                                   <td>邀请招标</td>
                                                   <td>邀请招标</td>
                                                   <td>邀请招标</td>
                                                   <td>分散采购</td>
                                                   <td>YY咨询有限公司</td>
                                                   <td>李四</td>
                                                   <td>YY科技有限公司</td>
                                                   <td>2024-04-01</td>
                                                   <td>2024-04-15</td>
                                                   <td>2024-06-30</td>
                                                   <td>2024-03-01</td>
                                                   <td class="frozen-column action-col">
                                                       <button class="action-btn btn-warning" onclick="splitSection('PRJ-2024-002')">标段拆分</button>
                                                   </td>
                                               </tr>
                                               <tr class="sub-table" id="sub-PRJ-2024-002" style="display: none;">
                                                   <td colspan="15">
                                                       <div style="padding: 10px;">
                                                           <h4 style="margin-bottom: 10px; color: #2c3e50;">项目下标段信息</h4>
                                                           <table class="data-table">
                                                               <thead>
                                                                   <tr>
                                                                       <th style="width: 200px;">标段名称</th>
                                                                       <th style="width: 120px;">标段阶段</th>
                                                                       <th style="width: 120px;">审核状态</th>
                                                                       <th>操作</th>
                                                                   </tr>
                                                               </thead>
                                                               <tbody>
                                                                   <tr>
                                                                       <td><a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-002-01')">系统开发标段</a></td>
                                                                       <td>评标结果公示</td>
                                                                       <td><span class="status-tag status-approved">审核通过</span></td>
                                                                       <td>
                                                                           <!-- 非准备阶段，无编辑按钮 -->
                                                                       </td>
                                                                   </tr>
                                                               </tbody>
                                                           </table>
                                                       </div>
                                                   </td>
                                               </tr>
                                           </tbody>
                                       </table>
                                       
                                       <!-- 分页 -->
                                       <div class="pagination">
                                           <div class="pagination-info">共 2 条记录，当前第 1 页</div>
                                           <div class="pagination-controls">
                                               <button class="page-btn">上一页</button>
                                               <button class="page-btn active">1</button>
                                               <button class="page-btn">2</button>
                                               <button class="page-btn">3</button>
                                               <button class="page-btn">下一页</button>
                                           </div>
                                       </div>
                                   </div>
                               </div>
                              
                              <!-- 待办标段视图 -->
                              <div id="todo-section-view" class="view-content" style="display: none;">
                                  <!-- 查询区域 -->
                                  <div class="search-area">
                                      <div class="search-form">
                                          <div class="form-group">
                                              <label class="form-label">标段名称</label>
                                              <input type="text" class="form-control" placeholder="请输入标段名称">
                                          </div>
                                          <div class="form-group">
                                              <label class="form-label">审核状态</label>
                                              <select class="form-control">
                                                  <option value="">全部</option>
                                                  <option value="pending">待审核</option>
                                                  <option value="processing">审核中</option>
                                                  <option value="approved">已通过</option>
                                                  <option value="rejected">已拒绝</option>
                                              </select>
                                          </div>
                                          <div class="form-group">
                                              <label class="form-label">采购类型</label>
                                              <select class="form-control">
                                                  <option value="">全部</option>
                                                  <option value="goods">货物</option>
                                                  <option value="service">服务</option>
                                                  <option value="engineering">工程</option>
                                              </select>
                                          </div>
                                          <div class="form-group">
                                              <label class="form-label">采购方式</label>
                                              <select class="form-control">
                                                  <option value="">全部</option>
                                                  <option value="tender">公开招标</option>
                                                  <option value="invite">邀请招标</option>
                                                  <option value="negotiate">竞争性谈判</option>
                                                  <option value="inquiry">询价</option>
                                              </select>
                                          </div>
                                          <div class="btn-group">
                                              <button class="btn btn-primary">查询</button>
                                              <button class="btn btn-secondary">重置</button>
                                              <button class="btn btn-outline" onclick="toggleAdvanced(this)">高级查询</button>
                                          </div>
                                      </div>

                                      <!-- 高级查询 -->
                                      <div class="advanced-search" id="advanced-search-todo-section" style="display: none;">
                                          <div class="search-form">
                                              <div class="form-group">
                                                  <label class="form-label">采购组织方式</label>
                                                  <select class="form-control">
                                                      <option value="">全部</option>
                                                      <option value="centralized">集中采购</option>
                                                      <option value="decentralized">分散采购</option>
                                                  </select>
                                              </div>
                                              <div class="form-group">
                                                  <label class="form-label">资金来源</label>
                                                  <select class="form-control">
                                                      <option value="">全部</option>
                                                      <option value="fiscal">财政资金</option>
                                                      <option value="self">自有资金</option>
                                                      <option value="loan">贷款资金</option>
                                                  </select>
                                              </div>
                                              <div class="form-group">
                                                  <label class="form-label">招标金额</label>
                                                  <input type="text" class="form-control" placeholder="请输入金额范围">
                                              </div>
                                              <div class="form-group">
                                                  <label class="form-label">申请人</label>
                                                  <input type="text" class="form-control" placeholder="请输入申请人">
                                              </div>
                                              <div class="form-group">
                                                  <label class="form-label">招标时间</label>
                                                  <input type="date" class="form-control">
                                              </div>
                                              <div class="form-group">
                                                  <label class="form-label">计划时间</label>
                                                  <input type="date" class="form-control">
                                              </div>
                                              <div class="form-group">
                                                  <label class="form-label">创建时间</label>
                                                  <input type="date" class="form-control">
                                              </div>
                                          </div>
                                      </div>
                                  </div>

                                  <!-- 功能操作栏 -->
                                  <div class="action-bar">
                                      <div class="action-buttons">
                                          <button class="btn btn-success" onclick="createSection()">新建项目标段</button>
                                          <button class="btn btn-danger">批量删除</button>
                                      </div>
                                  </div>

                                  <!-- 标段列表表格 -->
                                  <div class="table-container">
                                      <table class="data-table">
                                          <thead>
                                              <tr>
                                                  <th class="frozen-column checkbox-col">
                                                      <input type="checkbox">
                                                  </th>
                                                  <th class="frozen-column code-col">标段名称</th>
                                                  <th style="width: 120px;">标段阶段</th>
                                                  <th style="width: 120px;">标段状态</th>
                                                  <th style="width: 200px;">所属项目</th>
                                                  <th style="width: 120px;">项目编号</th>
                                                  <th style="width: 100px;">计划状态</th>
                                                  <th style="width: 100px;">审核状态</th>
                                                  <th style="width: 120px;">招标类别</th>
                                                  <th style="width: 120px;">采购方式</th>
                                                  <th style="width: 140px;">采购组织方式</th>
                                                  <th style="width: 160px;">代理机构</th>
                                                  <th style="width: 100px;">申请人</th>
                                                  <th style="width: 160px;">项目业主</th>
                                                  <th style="width: 120px;">招标时间</th>
                                                  <th style="width: 140px;">计划开始时间</th>
                                                  <th style="width: 140px;">计划结束时间</th>
                                                  <th style="width: 140px;">创建时间</th>
                                                  <th class="frozen-column action-col">操作</th>
                                              </tr>
                                          </thead>
                                          <tbody>
                                              <tr>
                                                  <td class="frozen-column checkbox-col">
                                                      <input type="checkbox">
                                                  </td>
                                                  <td class="frozen-column code-col">
                                                      <a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-001-00')">文具用品采购标段</a>
                                                  </td>
                                                  <td>准备阶段</td>
                                                  <td><span class="status-tag status-pending">待审核</span></td>
                                                  <td>办公设备采购项目</td>
                                                  <td>PRJ-2024-001</td>
                                                  <td><span class="status-tag status-active">进行中</span></td>
                                                  <td><span class="status-tag audit-approved">审核通过</span></td>
                                                  <td>公开招标</td>
                                                  <td>公开招标</td>
                                                  <td>集中采购</td>
                                                  <td>XX招标代理有限公司</td>
                                                  <td>张三</td>
                                                  <td>XX有限公司</td>
                                                  <td>2024-03-15</td>
                                                  <td>2024-03-01</td>
                                                  <td>2024-04-30</td>
                                                  <td>2024-02-20</td>
                                                  <td class="frozen-column action-col">
                                                      <button class="action-btn btn-success" onclick="submitSection('SEC-2024-001-00')">提交</button>
                                                      <button class="action-btn btn-primary" onclick="editSection('SEC-2024-001-00')">编辑</button>
                                                      <button class="action-btn btn-danger" onclick="deleteSection('SEC-2024-001-00')">删除</button>
                                                  </td>
                                              </tr>
                                              <tr>
                                                  <td class="frozen-column checkbox-col">
                                                      <input type="checkbox">
                                                  </td>
                                                  <td class="frozen-column code-col">
                                                      <a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-001-01')">办公桌椅采购标段</a>
                                                  </td>
                                                  <td>流标</td>
                                                  <td><span class="status-tag status-processing">审核中</span></td>
                                                  <td>办公设备采购项目</td>
                                                  <td>PRJ-2024-001</td>
                                                  <td><span class="status-tag status-active">进行中</span></td>
                                                  <td><span class="status-tag audit-approved">审核通过</span></td>
                                                  <td>公开招标</td>
                                                  <td>公开招标</td>
                                                  <td>集中采购</td>
                                                  <td>XX招标代理有限公司</td>
                                                  <td>张三</td>
                                                  <td>XX有限公司</td>
                                                  <td>2024-03-15</td>
                                                  <td>2024-03-01</td>
                                                  <td>2024-04-30</td>
                                                  <td>2024-02-20</td>
                                                  <td class="frozen-column action-col">
                                                      <button class="action-btn btn-info" onclick="auditSection('SEC-2024-001-01')">审核</button>
                                                      <button class="action-btn btn-danger" onclick="deleteSection('SEC-2024-001-01')">删除</button>
                                                  </td>
                                              </tr>
                                              <tr>
                                                  <td class="frozen-column checkbox-col">
                                                      <input type="checkbox">
                                                  </td>
                                                  <td class="frozen-column code-col">
                                                      <a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-001-02')">电脑设备采购标段</a>
                                                  </td>
                                                  <td>办公设备采购项目</td>
                                                  <td>货物采购</td>
                                                  <td>公开招标</td>
                                                  <td><span class="status-tag status-approved">审核通过</span></td>
                                                  <td>集中采购</td>
                                                  <td>XX招标代理有限公司</td>
                                                  <td>张三</td>
                                                  <td>XX有限公司</td>
                                                  <td>2024-03-15</td>
                                                  <td>2024-02-20</td>
                                                  <td class="frozen-column action-col">
                                                      <button class="action-btn btn-success" onclick="submitSection('SEC-2024-001-02')">提交</button>
                                                      <button class="action-btn btn-primary" onclick="editSection('SEC-2024-001-02')">编辑</button>
                                                      <button class="action-btn btn-danger" onclick="deleteSection('SEC-2024-001-02')">删除</button>
                                                  </td>
                                              </tr>
                                              <tr>
                                                  <td class="frozen-column checkbox-col">
                                                      <input type="checkbox">
                                                  </td>
                                                  <td class="frozen-column code-col">
                                                      <a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-002-01')">系统开发标段</a>
                                                  </td>
                                                  <td>IT系统建设项目</td>
                                                  <td>服务采购</td>
                                                  <td>邀请招标</td>
                                                  <td><span class="status-tag status-rejected">审核未过</span></td>
                                                  <td>分散采购</td>
                                                  <td>YY咨询有限公司</td>
                                                  <td>李四</td>
                                                  <td>YY科技有限公司</td>
                                                  <td>2024-04-01</td>
                                                  <td>2024-03-01</td>
                                                  <td class="frozen-column action-col">
                                                      <button class="action-btn btn-success" onclick="submitSection('SEC-2024-002-01')">提交</button>
                                                      <button class="action-btn btn-primary" onclick="editSection('SEC-2024-002-01')">编辑</button>
                                                      <button class="action-btn btn-danger" onclick="deleteSection('SEC-2024-002-01')">删除</button>
                                                  </td>
                                              </tr>
                                          </tbody>
                                      </table>
                                      
                                      <!-- 分页 -->
                                      <div class="pagination">
                                          <div class="pagination-info">共 3 条记录，当前第 1 页</div>
                                          <div class="pagination-controls">
                                              <button class="page-btn">上一页</button>
                                              <button class="page-btn active">1</button>
                                              <button class="page-btn">2</button>
                                              <button class="page-btn">3</button>
                                              <button class="page-btn">下一页</button>
                                          </div>
                                      </div>
                                  </div>
                              </div>
                          </div>
                          
                          <!-- 已办页签内容 -->
                          <div id="done-tab" class="tab-content">
                              <!-- 视图切换 -->
                              <div class="view-switch">
                                  <button class="view-btn active" onclick="switchView('project', 'done')">项目视图</button>
                                  <button class="view-btn" onclick="switchView('section', 'done')">标段视图</button>
                              </div>
                              
                              <!-- 已办项目视图 -->
                              <div id="done-project-view" class="view-content">
                                  <!-- 查询区域 -->
                                  <div class="search-area">
                                      <div class="search-form">
                                          <div class="form-group">
                                              <label class="form-label">项目名称</label>
                                              <input type="text" class="form-control" placeholder="请输入项目名称">
                                          </div>
                                          <div class="form-group">
                                              <label class="form-label">审核状态</label>
                                              <select class="form-control">
                                                  <option value="">全部</option>
                                                  <option value="pending">待审核</option>
                                                  <option value="processing">审核中</option>
                                                  <option value="approved">已通过</option>
                                                  <option value="rejected">已拒绝</option>
                                              </select>
                                          </div>
                                          <div class="form-group">
                                              <label class="form-label">采购类型</label>
                                              <select class="form-control">
                                                  <option value="">全部</option>
                                                  <option value="goods">货物</option>
                                                  <option value="service">服务</option>
                                                  <option value="engineering">工程</option>
                                              </select>
                                          </div>
                                          <div class="form-group">
                                              <label class="form-label">采购方式</label>
                                              <select class="form-control">
                                                  <option value="">全部</option>
                                                  <option value="tender">公开招标</option>
                                                  <option value="invite">邀请招标</option>
                                                  <option value="negotiate">竞争性谈判</option>
                                                  <option value="inquiry">询价</option>
                                              </select>
                                          </div>
                                          <div class="btn-group">
                                              <button class="btn btn-primary">查询</button>
                                              <button class="btn btn-secondary">重置</button>
                                              <button class="btn btn-outline" onclick="toggleAdvanced(this)">高级查询</button>
                                          </div>
                                      </div>

                                      <!-- 高级查询 -->
                                      <div class="advanced-search" id="advanced-search-done-project" style="display: none;">
                                          <div class="search-form">
                                              <div class="form-group">
                                                  <label class="form-label">采购组织方式</label>
                                                  <select class="form-control">
                                                      <option value="">全部</option>
                                                      <option value="centralized">集中采购</option>
                                                      <option value="decentralized">分散采购</option>
                                                  </select>
                                              </div>
                                              <div class="form-group">
                                                  <label class="form-label">资金来源</label>
                                                  <select class="form-control">
                                                      <option value="">全部</option>
                                                      <option value="fiscal">财政资金</option>
                                                      <option value="self">自有资金</option>
                                                      <option value="loan">贷款资金</option>
                                                  </select>
                                              </div>
                                              <div class="form-group">
                                                  <label class="form-label">招标金额</label>
                                                  <input type="text" class="form-control" placeholder="请输入金额范围">
                                              </div>
                                              <div class="form-group">
                                                  <label class="form-label">申请人</label>
                                                  <input type="text" class="form-control" placeholder="请输入申请人">
                                              </div>
                                              <div class="form-group">
                                                  <label class="form-label">招标时间</label>
                                                  <input type="date" class="form-control">
                                              </div>
                                              <div class="form-group">
                                                  <label class="form-label">计划时间</label>
                                                  <input type="date" class="form-control">
                                              </div>
                                              <div class="form-group">
                                                  <label class="form-label">创建时间</label>
                                                  <input type="date" class="form-control">
                                              </div>
                                          </div>
                                      </div>
                                  </div>

                                  <!-- 功能操作栏 -->
                                  <div class="action-bar">
                                      <div class="action-buttons">
                                          <button class="btn btn-success" onclick="createProject()">新建项目</button>
                                          <button class="btn btn-danger">批量删除</button>
                                      </div>
                                  </div>

                                  <!-- 项目列表表格 -->
                                  <div class="table-container">
                                      <table class="data-table">
                                          <thead>
                                              <tr>
                                                   <th class="frozen-column checkbox-col">
                                                       <input type="checkbox">
                                                   </th>
                                                   <th class="frozen-column code-col">项目编号</th>
                                                   <th style="width: 200px;">项目名称</th>
                                                   <th style="width: 100px;">采购类型</th>
                                                   <th style="width: 120px;">招标类别</th>
                                                   <th style="width: 120px;">采购方式</th>
                                                   <th style="width: 140px;">采购组织方式</th>
                                                   <th style="width: 160px;">代理机构</th>
                                                   <th style="width: 100px;">申请人</th>
                                                   <th style="width: 160px;">项目业主</th>
                                                   <th style="width: 120px;">招标时间</th>
                                                   <th style="width: 140px;">计划开始时间</th>
                                                   <th style="width: 140px;">计划结束时间</th>
                                                   <th style="width: 140px;">创建时间</th>
                                                   <th class="frozen-column action-col">操作</th>
                                               </tr>
                                          </thead>
                                          <tbody>
                                              <tr>
                                                   <td class="frozen-column checkbox-col">
                                                       <input type="checkbox">
                                                   </td>
                                                   <td class="frozen-column code-col">
                                                       <span class="expand-icon" onclick="toggleProject(this)">▶</span>
                                                       <a href="#" class="table-link" onclick="viewProjectDetail('PRJ-2024-001')">PRJ-2024-001</a>
                                                   </td>
                                                   <td>办公设备采购项目</td>
                                                   <td>货物采购</td>
                                                   <td>公开招标</td>
                                                   <td>公开招标</td>
                                                   <td>公开招标</td>
                                                   <td>集中采购</td>
                                                   <td>XX招标代理有限公司</td>
                                                   <td>张三</td>
                                                   <td>XX有限公司</td>
                                                   <td>2024-03-15</td>
                                                   <td>2024-03-01</td>
                                                   <td>2024-04-30</td>
                                                   <td>2024-02-20</td>
                                                   <td class="frozen-column action-col">
                                                       <button class="action-btn btn-warning" onclick="splitSection('PRJ-2024-001')">标段拆分</button>
                                                   </td>
                                               </tr>
                                               <tr class="sub-table" id="sub-PRJ-2024-001" style="display: none;">
                                                   <td colspan="15">
                                                       <div style="padding: 10px;">
                                                           <h4 style="margin-bottom: 10px; color: #2c3e50;">项目下标段信息</h4>
                                                           <table class="data-table">
                                                               <thead>
                                                                   <tr>
                                                                       <th style="width: 200px;">标段名称</th>
                                                                       <th style="width: 120px;">标段阶段</th>
                                                                       <th style="width: 120px;">标段状态</th>
                                                                       <th>操作</th>
                                                                   </tr>
                                                               </thead>
                                                               <tbody>
                                                                   <tr>
                                                                       <td><a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-001-00')">文具用品采购标段</a></td>
                                                                       <td>已完成</td>
                                                                       <td><span class="status-tag status-approved">审核通过</span></td>
                                                                       <td>
                                                                           <!-- 已办项目，无编辑和审核按钮 -->
                                                                       </td>
                                                                   </tr>
                                                                   <tr>
                                                                       <td><a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-001-01')">办公桌椅采购标段</a></td>
                                                                       <td>已完成</td>
                                                                       <td><span class="status-tag status-approved">审核通过</span></td>
                                                                       <td>
                                                                           <!-- 已办项目，无编辑和审核按钮 -->
                                                                       </td>
                                                                   </tr>
                                                                   <tr>
                                                                       <td><a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-001-02')">电脑设备采购标段</a></td>
                                                                       <td>已完成</td>
                                                                       <td><span class="status-tag status-approved">审核通过</span></td>
                                                                       <td>
                                                                           <!-- 已办项目，无编辑和审核按钮 -->
                                                                       </td>
                                                                   </tr>
                                                               </tbody>
                                                           </table>
                                                       </div>
                                                   </td>
                                               </tr>
                                              <tr>
                                                   <td class="frozen-column checkbox-col">
                                                       <input type="checkbox">
                                                   </td>
                                                   <td class="frozen-column code-col">
                                                       <span class="expand-icon" onclick="toggleProject(this)">▶</span>
                                                       <a href="#" class="table-link" onclick="viewProjectDetail('PRJ-2024-002')">PRJ-2024-002</a>
                                                   </td>
                                                   <td>IT系统建设项目</td>
                                                   <td>服务采购</td>
                                                   <td>邀请招标</td>
                                                   <td>邀请招标</td>
                                                   <td>邀请招标</td>
                                                   <td>分散采购</td>
                                                   <td>YY咨询有限公司</td>
                                                   <td>李四</td>
                                                   <td>YY科技有限公司</td>
                                                   <td>2024-04-01</td>
                                                   <td>2024-04-15</td>
                                                   <td>2024-06-30</td>
                                                   <td>2024-03-01</td>
                                                   <td class="frozen-column action-col">
                                                       <button class="action-btn btn-warning" onclick="splitSection('PRJ-2024-002')">标段拆分</button>
                                                   </td>
                                               </tr>
                                               <tr class="sub-table" id="sub-PRJ-2024-002" style="display: none;">
                                                   <td colspan="15">
                                                       <div style="padding: 10px;">
                                                           <h4 style="margin-bottom: 10px; color: #2c3e50;">项目下标段信息</h4>
                                                           <table class="data-table">
                                                               <thead>
                                                                   <tr>
                                                                       <th style="width: 200px;">标段名称</th>
                                                                       <th style="width: 120px;">标段阶段</th>
                                                                       <th style="width: 120px;">标段状态</th>
                                                                       <th>操作</th>
                                                                   </tr>
                                                               </thead>
                                                               <tbody>
                                                                   <tr>
                                                                       <td><a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-002-01')">系统开发标段</a></td>
                                                                       <td>已完成</td>
                                                                       <td><span class="status-tag status-approved">审核通过</span></td>
                                                                       <td>
                                                                           <!-- 已办项目，无编辑和审核按钮 -->
                                                                       </td>
                                                                   </tr>
                                                               </tbody>
                                                           </table>
                                                       </div>
                                                   </td>
                                               </tr>
                                              <tr class="section-row" id="sections-PRJ-2024-002" style="display: none;">
                                                  <td colspan="15">
                                                      <div class="section-container">
                                                          <div class="section-header">项目下标段信息</div>
                                                          <table class="section-table">
                                                              <thead>
                                                                  <tr>
                                                                      <th style="width: 200px;">标段名称</th>
                                                                      <th style="width: 120px;">标段状态</th>
                                                                      <th style="width: 120px;">招标类别</th>
                                                                      <th style="width: 120px;">采购方式</th>
                                                                      <th style="width: 140px;">代理机构</th>
                                                                      <th style="width: 100px;">申请人</th>
                                                                      <th style="width: 120px;">招标时间</th>
                                                                      <th style="width: 140px;">操作</th>
                                                                  </tr>
                                                              </thead>
                                                              <tbody>
                                                                  <tr>
                                                                      <td>系统开发标段</td>
                                                                      <td><span class="status-tag status-pending">待审核</span></td>
                                                                      <td>邀请招标</td>
                                                                      <td>邀请招标</td>
                                                                      <td>YY咨询有限公司</td>
                                                                      <td>李四</td>
                                                                      <td>2024-04-01</td>
                                                                      <td>
                                                                          <button class="action-btn btn-primary" onclick="editSection('SEC-2024-002-01')">编辑</button>
                                                                      </td>
                                                                  </tr>
                                                              </tbody>
                                                          </table>
                                                      </div>
                                                  </td>
                                              </tr>
                                          </tbody>
                                      </table>
                                      
                                      <!-- 分页 -->
                                      <div class="pagination">
                                          <div class="pagination-info">共 2 条记录，当前第 1 页</div>
                                          <div class="pagination-controls">
                                              <button class="page-btn">上一页</button>
                                              <button class="page-btn active">1</button>
                                              <button class="page-btn">2</button>
                                              <button class="page-btn">3</button>
                                              <button class="page-btn">下一页</button>
                                          </div>
                                      </div>
                                  </div>
                              </div>
                              
                              <!-- 已办标段视图 -->
                              <div id="done-section-view" class="view-content" style="display: none;">
                                  <!-- 查询区域 -->
                                  <div class="search-area">
                                      <div class="search-form">
                                          <div class="form-group">
                                              <label class="form-label">标段名称</label>
                                              <input type="text" class="form-control" placeholder="请输入标段名称">
                                          </div>
                                          <div class="form-group">
                                              <label class="form-label">审核状态</label>
                                              <select class="form-control">
                                                  <option value="">全部</option>
                                                  <option value="pending">待审核</option>
                                                  <option value="processing">审核中</option>
                                                  <option value="approved">已通过</option>
                                                  <option value="rejected">已拒绝</option>
                                              </select>
                                          </div>
                                          <div class="form-group">
                                              <label class="form-label">采购类型</label>
                                              <select class="form-control">
                                                  <option value="">全部</option>
                                                  <option value="goods">货物</option>
                                                  <option value="service">服务</option>
                                                  <option value="engineering">工程</option>
                                              </select>
                                          </div>
                                          <div class="form-group">
                                              <label class="form-label">采购方式</label>
                                              <select class="form-control">
                                                  <option value="">全部</option>
                                                  <option value="tender">公开招标</option>
                                                  <option value="invite">邀请招标</option>
                                                  <option value="negotiate">竞争性谈判</option>
                                                  <option value="inquiry">询价</option>
                                              </select>
                                          </div>
                                          <div class="btn-group">
                                              <button class="btn btn-primary">查询</button>
                                              <button class="btn btn-secondary">重置</button>
                                              <button class="btn btn-outline" onclick="toggleAdvanced(this)">高级查询</button>
                                          </div>
                                      </div>

                                      <!-- 高级查询 -->
                                      <div class="advanced-search" id="advanced-search-done-section" style="display: none;">
                                          <div class="search-form">
                                              <div class="form-group">
                                                  <label class="form-label">采购组织方式</label>
                                                  <select class="form-control">
                                                      <option value="">全部</option>
                                                      <option value="centralized">集中采购</option>
                                                      <option value="decentralized">分散采购</option>
                                                  </select>
                                              </div>
                                              <div class="form-group">
                                                  <label class="form-label">资金来源</label>
                                                  <select class="form-control">
                                                      <option value="">全部</option>
                                                      <option value="fiscal">财政资金</option>
                                                      <option value="self">自有资金</option>
                                                      <option value="loan">贷款资金</option>
                                                  </select>
                                              </div>
                                              <div class="form-group">
                                                  <label class="form-label">招标金额</label>
                                                  <input type="text" class="form-control" placeholder="请输入金额范围">
                                              </div>
                                              <div class="form-group">
                                                  <label class="form-label">申请人</label>
                                                  <input type="text" class="form-control" placeholder="请输入申请人">
                                              </div>
                                              <div class="form-group">
                                                  <label class="form-label">招标时间</label>
                                                  <input type="date" class="form-control">
                                              </div>
                                              <div class="form-group">
                                                  <label class="form-label">计划时间</label>
                                                  <input type="date" class="form-control">
                                              </div>
                                              <div class="form-group">
                                                  <label class="form-label">创建时间</label>
                                                  <input type="date" class="form-control">
                                              </div>
                                          </div>
                                      </div>
                                  </div>

                                  <!-- 功能操作栏 -->
                                  <div class="action-bar">
                                      <div class="action-buttons">
                                          <button class="btn btn-success" onclick="createSection()">新建项目标段</button>
          
                                          <button class="btn btn-danger">批量删除</button>
                                      </div>
                                  </div>

                                  <!-- 标段列表表格 -->
                                  <div class="table-container">
                                      <table class="data-table">
                                          <thead>
                                              <tr>
                                                  <th class="frozen-column checkbox-col">
                                                      <input type="checkbox">
                                                  </th>
                                                  <th class="frozen-column code-col">标段编号</th>
                                                  <th style="width: 200px;">标段名称</th>
                                                  <th style="width: 120px;">标段阶段</th>
                                                  <th style="width: 200px;">所属项目</th>
                                                  <th style="width: 100px;">标段状态</th>
                                                  <th style="width: 100px;">审核状态</th>
                                                  <th style="width: 120px;">招标类别</th>
                                                  <th style="width: 120px;">采购方式</th>
                                                  <th style="width: 140px;">采购组织方式</th>
                                                  <th style="width: 160px;">代理机构</th>
                                                  <th style="width: 100px;">申请人</th>
                                                  <th style="width: 160px;">项目业主</th>
                                                  <th style="width: 120px;">招标时间</th>
                                                  <th style="width: 140px;">计划开始时间</th>
                                                  <th style="width: 140px;">计划结束时间</th>
                                                  <th style="width: 140px;">创建时间</th>
                                                  <th class="frozen-column action-col">操作</th>
                                              </tr>
                                          </thead>
                                          <tbody>
                                              <tr>
                                                  <td class="frozen-column checkbox-col">
                                                      <input type="checkbox">
                                                  </td>
                                                  <td class="frozen-column code-col">
                                                      <a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-001-00')">SEC-2024-001-00</a>
                                                  </td>
                                                  <td>文具用品采购标段</td>
                                                  <td>准备阶段</td>
                                                  <td>办公设备采购项目</td>
                                                  <td><span class="status-tag status-approved">审核通过</span></td>
                                                  <td><span class="status-tag audit-approved">已通过</span></td>
                                                  <td>公开招标</td>
                                                  <td>公开招标</td>
                                                  <td>集中采购</td>
                                                  <td>XX招标代理有限公司</td>
                                                  <td>张三</td>
                                                  <td>XX有限公司</td>
                                                  <td>2024-03-15</td>
                                                  <td>2024-03-01</td>
                                                  <td>2024-04-30</td>
                                                  <td>2024-02-20</td>
                                                  <td class="frozen-column action-col">
                                                      <!-- 已办标段，审核通过，操作为- -->
                                                      <span class="action-placeholder">-</span>
                                                  </td>
                                              </tr>
                                              <tr>
                                                  <td class="frozen-column checkbox-col">
                                                      <input type="checkbox">
                                                  </td>
                                                  <td class="frozen-column code-col">
                                                      <a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-001-01')">SEC-2024-001-01</a>
                                                  </td>
                                                  <td>办公桌椅采购标段</td>
                                                  <td>评标结果公示</td>
                                                  <td>办公设备采购项目</td>
                                                  <td><span class="status-tag status-approved">审核通过</span></td>
                                                  <td><span class="status-tag audit-approved">已通过</span></td>
                                                  <td>公开招标</td>
                                                  <td>公开招标</td>
                                                  <td>集中采购</td>
                                                  <td>XX招标代理有限公司</td>
                                                  <td>张三</td>
                                                  <td>XX有限公司</td>
                                                  <td>2024-03-15</td>
                                                  <td>2024-03-01</td>
                                                  <td>2024-04-30</td>
                                                  <td>2024-02-20</td>
                                                  <td class="frozen-column action-col">
                                                      <!-- 已通过审核，只保留删除功能 -->
                                                      <button class="action-btn btn-danger" onclick="deleteSection('SEC-2024-001-01')">删除</button>
                                                  </td>
                                              </tr>
                                              <tr>
                                                  <td class="frozen-column checkbox-col">
                                                      <input type="checkbox">
                                                  </td>
                                                  <td class="frozen-column code-col">
                                                      <a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-001-02')">SEC-2024-001-02</a>
                                                  </td>
                                                  <td>电脑设备采购标段</td>
                                                  <td>交易公告</td>
                                                  <td>办公设备采购项目</td>
                                                  <td><span class="status-tag status-approved">审核通过</span></td>
                                                  <td><span class="status-tag audit-approved">已通过</span></td>
                                                  <td>公开招标</td>
                                                  <td>公开招标</td>
                                                  <td>集中采购</td>
                                                  <td>XX招标代理有限公司</td>
                                                  <td>张三</td>
                                                  <td>XX有限公司</td>
                                                  <td>2024-03-15</td>
                                                  <td>2024-03-01</td>
                                                  <td>2024-04-30</td>
                                                  <td>2024-02-20</td>
                                                  <td class="frozen-column action-col">
                                                      <!-- 已办标段，审核通过，操作为- -->
                                                      <span class="action-placeholder">-</span>
                                                  </td>
                                              </tr>
                                              <tr>
                                                  <td class="frozen-column checkbox-col">
                                                      <input type="checkbox">
                                                  </td>
                                                  <td class="frozen-column code-col">
                                                      <a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-002-01')">SEC-2024-002-01</a>
                                                  </td>
                                                  <td>系统开发标段</td>
                                                  <td>中标结果公示</td>
                                                  <td>IT系统建设项目</td>
                                                  <td><span class="status-tag status-approved">审核通过</span></td>
                                                  <td><span class="status-tag audit-approved">已通过</span></td>
                                                  <td>邀请招标</td>
                                                  <td>邀请招标</td>
                                                  <td>分散采购</td>
                                                  <td>YY咨询有限公司</td>
                                                  <td>李四</td>
                                                  <td>YY科技有限公司</td>
                                                  <td>2024-04-01</td>
                                                  <td>2024-04-15</td>
                                                  <td>2024-06-30</td>
                                                  <td>2024-03-01</td>
                                                  <td class="frozen-column action-col">
                                                      <!-- 已办标段，审核通过，操作为- -->
                                                      <span class="action-placeholder">-</span>
                                                  </td>
                                              </tr>
                                          </tbody>
                                      </table>
                                      
                                      <!-- 分页 -->
                                      <div class="pagination">
                                          <div class="pagination-info">共 3 条记录，当前第 1 页</div>
                                          <div class="pagination-controls">
                                              <button class="page-btn">上一页</button>
                                              <button class="page-btn active">1</button>
                                              <button class="page-btn">2</button>
                                              <button class="page-btn">3</button>
                                              <button class="page-btn">下一页</button>
                                          </div>
                                      </div>
                                  </div>
                              </div>
                          </div>
                    </div>

                    <!-- 原项目视图内容将被移动到页签中 -->
                    <div id="project-view" class="view-content" style="display: none;">
                        <!-- 查询区域 -->
                        <div class="search-area">
                            <div class="search-form">
                                <div class="form-group">
                                    <label>项目名称</label>
                                    <input type="text" placeholder="请输入项目名称">
                                </div>
                                <div class="form-group">
                                    <label>审核状态</label>
                                    <select>
                                        <option value="">全部</option>
                                        <option value="pending">待审核</option>
                                        <option value="processing">审核中</option>
                                        <option value="approved">已通过</option>
                                        <option value="rejected">已拒绝</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>采购类型</label>
                                    <select>
                                        <option value="">全部</option>
                                        <option value="goods">货物</option>
                                        <option value="service">服务</option>
                                        <option value="engineering">工程</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>采购方式</label>
                                    <select>
                                        <option value="">全部</option>
                                        <option value="tender">公开招标</option>
                                        <option value="invite">邀请招标</option>
                                        <option value="negotiate">竞争性谈判</option>
                                        <option value="inquiry">询价</option>
                                    </select>
                                </div>
                                <div class="search-buttons">
                                    <button class="btn btn-primary">查询</button>
                                    <button class="btn btn-default">重置</button>
                                    <button class="btn-link" onclick="toggleAdvanced(this)">高级查询</button>
                                </div>
                            </div>

                            <!-- 高级查询 -->
                            <div class="advanced-search" id="advanced-search-extra1" style="display: none;">
                                <div class="search-form">
                                    <div class="form-group">
                                        <label>采购组织方式</label>
                                        <select>
                                            <option value="">全部</option>
                                            <option value="centralized">集中采购</option>
                                            <option value="decentralized">分散采购</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>资金来源</label>
                                        <select>
                                            <option value="">全部</option>
                                            <option value="fiscal">财政资金</option>
                                            <option value="self">自有资金</option>
                                            <option value="loan">贷款资金</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>招标金额</label>
                                        <input type="text" placeholder="请输入金额范围">
                                    </div>
                                    <div class="form-group">
                                        <label>申请人</label>
                                        <input type="text" placeholder="请输入申请人">
                                    </div>
                                    <div class="form-group">
                                        <label>招标时间</label>
                                        <input type="date">
                                    </div>
                                    <div class="form-group">
                                        <label>计划时间</label>
                                        <input type="date">
                                    </div>
                                    <div class="form-group">
                                        <label>创建时间</label>
                                        <input type="date">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 功能操作栏 -->
                        <div class="action-bar">
                            <div class="action-buttons">
                                <button class="btn btn-success" onclick="createProject()">新建项目</button>
                                
                                <button class="btn btn-danger">批量删除</button>
                            </div>
                        </div>

                        <!-- 项目列表表格 -->
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th class="frozen-column checkbox-col">
                                            <input type="checkbox" id="selectAll">
                                        </th>
                                        <th class="frozen-column code-col">项目编号</th>
                                        <th style="width: 200px;">项目名称</th>
                                        <th style="width: 100px;">采购类型</th>
                                        <th style="width: 120px;">招标类别</th>
                                        <th style="width: 120px;">采购方式</th>
                                        <th style="width: 140px;">采购组织方式</th>
                                        <th style="width: 160px;">代理机构</th>
                                        <th style="width: 100px;">申请人</th>
                                        <th style="width: 160px;">项目业主</th>
                                        <th style="width: 120px;">招标时间</th>
                                        <th style="width: 140px;">计划开始时间</th>
                                        <th style="width: 140px;">计划结束时间</th>
                                        <th style="width: 140px;">创建时间</th>
                                        <th class="frozen-column action-col">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="frozen-column checkbox-col">
                                            <input type="checkbox">
                                        </td>
                                        <td class="frozen-column code-col">
                                            <span class="expand-icon" onclick="toggleProject(this)">▶</span>
                                            <a href="#" class="table-link" onclick="viewProjectDetail('PRJ-2024-001')">PRJ-2024-001</a>
                                        </td>
                                        <td>办公设备采购项目</td>
                                        <td>货物采购</td>
                                        <td>公开招标</td>
                                        <td>公开招标</td>
                                        <td>集中采购</td>
                                        <td>XX招标代理有限公司</td>
                                        <td>张三</td>
                                        <td>XX有限公司</td>
                                        <td>2024-03-15</td>
                                        <td>2024-03-01</td>
                                        <td>2024-04-30</td>
                                        <td>2024-02-20</td>
                                        <td class="frozen-column action-col">
                                            <button class="action-btn btn-primary" onclick="editProject('PRJ-2024-001')">编辑</button>
                                            <button class="action-btn btn-warning" onclick="splitSection('PRJ-2024-001')">标段拆分</button>
                                            <button class="action-btn btn-danger" onclick="deleteProject('PRJ-2024-001')">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="sub-table" id="sub-PRJ-2024-001" style="display: none;">
                                        <td colspan="16">
                                            <div style="padding: 10px;">
                                                <h4 style="margin-bottom: 10px; color: #2c3e50;">项目下标段信息</h4>
                                                <div style="overflow-x: auto; position: relative;">
                                                    <table class="data-table">
                                                        <thead>
                                                            <tr>
                                                                <th class="frozen-section-name" style="width: 200px;">标段名称</th>
                                                                <th style="width: 120px;">标段阶段</th>
                                                                <th style="width: 120px;">审核状态</th>
                                                                <th>操作</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td class="frozen-section-name"><a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-001-01')">办公桌椅采购标段</a></td>
                                                                <td>补遗、澄清、答疑</td>
                                                                <td><span class="status-tag status-processing">审核中</span></td>
                                                                <td>—</td>
                                                            </tr>
                                                            <tr>
                                                                <td class="frozen-section-name"><a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-001-02')">电脑设备采购标段</a></td>
                                                                <td>流标</td>
                                                                <td><span class="status-tag status-pending">待审核</span></td>
                                                                <td>—</td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="frozen-column checkbox-col">
                                            <input type="checkbox">
                                        </td>
                                        <td class="frozen-column code-col">
                                            <span class="expand-icon" onclick="toggleProject(this)">▶</span>
                                            <a href="#" class="table-link" onclick="viewProjectDetail('PRJ-2024-002')">PRJ-2024-002</a>
                                        </td>
                                        <td>IT系统建设项目</td>
                                        <td>服务采购</td>
                                        <td>邀请招标</td>
                                        <td>邀请招标</td>
                                        <td>分散采购</td>
                                        <td>YY咨询有限公司</td>
                                        <td>李四</td>
                                        <td>YY科技有限公司</td>
                                        <td>2024-04-01</td>
                                        <td>2024-04-15</td>
                                        <td>2024-06-30</td>
                                        <td>2024-03-01</td>
                                        <td class="frozen-column action-col">
                                            <button class="action-btn btn-primary" onclick="editProject('PRJ-2024-002')">编辑</button>
                                            <button class="action-btn btn-warning" onclick="splitSection('PRJ-2024-002')">标段拆分</button>
                                            <button class="action-btn btn-danger" onclick="deleteProject('PRJ-2024-002')">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="sub-table" id="sub-PRJ-2024-002" style="display: none;">
                                        <td colspan="16">
                                            <div style="padding: 10px;">
                                                <h4 style="margin-bottom: 10px; color: #2c3e50;">项目下标段信息</h4>
                                                <div style="overflow-x: auto; position: relative;">
                                                    <table class="data-table">
                                                        <thead>
                                                            <tr>
                                                                <th class="frozen-section-name" style="width: 200px;">标段名称</th>
                                                                <th style="width: 120px;">标段阶段</th>
                                                                <th style="width: 120px;">审核状态</th>
                                                                <th>操作</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td class="frozen-section-name"><a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-002-01')">系统开发标段</a></td>
                                                                <td>评标结果公示</td>
                                                                <td><span class="status-tag status-rejected">审核未过</span></td>
                                                                <td>—</td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            
                            <!-- 分页 -->
                            <div class="pagination">
                                <div class="pagination-info">共 2 条记录，当前第 1 页</div>
                                <div class="pagination-controls">
                                    <button class="page-btn">上一页</button>
                                    <button class="page-btn active">1</button>
                                    <button class="page-btn">2</button>
                                    <button class="page-btn">3</button>
                                    <button class="page-btn">下一页</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 标段视图 -->
                    <div id="section-view" class="view-content" style="display: none;">
                        <!-- 查询区域 -->
                        <div class="search-area">
                            <div class="search-form">
                                <div class="form-group">
                                    <label class="form-label">标段名称</label>
                                    <input type="text" class="form-control" placeholder="请输入标段名称">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">审核状态</label>
                                    <select class="form-control">
                                        <option value="">全部</option>
                                        <option value="pending">待审核</option>
                                        <option value="processing">审核中</option>
                                        <option value="approved">已通过</option>
                                        <option value="rejected">已拒绝</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">采购类型</label>
                                    <select class="form-control">
                                        <option value="">全部</option>
                                        <option value="goods">货物</option>
                                        <option value="service">服务</option>
                                        <option value="engineering">工程</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">采购方式</label>
                                    <select class="form-control">
                                        <option value="">全部</option>
                                        <option value="tender">公开招标</option>
                                        <option value="invite">邀请招标</option>
                                        <option value="negotiate">竞争性谈判</option>
                                        <option value="inquiry">询价</option>
                                    </select>
                                </div>
                                <div class="btn-group">
                                    <button class="btn btn-primary">查询</button>
                                    <button class="btn btn-secondary">重置</button>
                                    <button class="btn btn-outline" onclick="toggleAdvanced(this)">高级查询</button>
                                </div>
                            </div>

                            <!-- 高级查询 -->
                            <div class="advanced-search" id="advanced-search-extra2" style="display: none;">
                                <div class="search-form">
                                    <div class="form-group">
                                        <label class="form-label">采购组织方式</label>
                                        <select class="form-control">
                                            <option value="">全部</option>
                                            <option value="centralized">集中采购</option>
                                            <option value="decentralized">分散采购</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">资金来源</label>
                                        <select class="form-control">
                                            <option value="">全部</option>
                                            <option value="fiscal">财政资金</option>
                                            <option value="self">自有资金</option>
                                            <option value="loan">贷款资金</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">招标金额</label>
                                        <input type="text" class="form-control" placeholder="请输入金额范围">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">申请人</label>
                                        <input type="text" class="form-control" placeholder="请输入申请人">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">招标时间</label>
                                        <input type="date" class="form-control">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">计划时间</label>
                                        <input type="date" class="form-control">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">创建时间</label>
                                        <input type="date" class="form-control">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 功能操作栏 -->
                        <div class="action-bar">
                            <div class="action-buttons">
                                <button class="btn btn-success" onclick="createSection()">新建标段</button>
                                
                                <button class="btn btn-danger">批量删除</button>
                            </div>
                        </div>

                        <!-- 标段列表表格 -->
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th class="frozen-column checkbox-col">
                                            <input type="checkbox">
                                        </th>
                                        <th class="frozen-column code-col">标段名称</th>
                                        <th style="width: 120px;">标段阶段</th>
                                        <th style="width: 120px;">审核状态</th>
                                        <th style="width: 200px;">所属项目</th>
                                        <th style="width: 120px;">项目编号</th>
                                        <th style="width: 100px;">采购类型</th>
                                        <th style="width: 100px;">审核状态</th>
                                        <th style="width: 120px;">招标类别</th>
                                        <th style="width: 120px;">采购方式</th>
                                        <th style="width: 140px;">采购组织方式</th>
                                        <th style="width: 160px;">代理机构</th>
                                        <th style="width: 100px;">申请人</th>
                                        <th style="width: 160px;">项目业主</th>
                                        <th style="width: 120px;">招标时间</th>
                                        <th style="width: 140px;">计划开始时间</th>
                                        <th style="width: 140px;">计划结束时间</th>
                                        <th style="width: 140px;">创建时间</th>
                                        <th class="frozen-column action-col">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="frozen-column checkbox-col">
                                            <input type="checkbox">
                                        </td>
                                        <td class="frozen-column code-col">
                                            <a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-001-00')">文具用品采购标段</a>
                                        </td>
                                        <td>已完成</td>
                                        <td><span class="status-tag status-approved">审核通过</span></td>
                                        <td>办公设备采购项目</td>
                                        <td>PRJ-2024-001</td>
                                        <td>货物采购</td>
                                        <td><span class="status-tag audit-approved">已通过</span></td>
                                        <td>公开招标</td>
                                        <td>公开招标</td>
                                        <td>集中采购</td>
                                        <td>XX招标代理有限公司</td>
                                        <td>张三</td>
                                        <td>XX有限公司</td>
                                        <td>2024-03-15</td>
                                        <td>2024-03-01</td>
                                        <td>2024-04-30</td>
                                        <td>2024-02-20</td>
                                        <td class="frozen-column action-col">
                                            <button class="action-btn btn-danger" onclick="deleteSection('SEC-2024-001-00')">删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="frozen-column checkbox-col">
                                            <input type="checkbox">
                                        </td>
                                        <td class="frozen-column code-col">
                                            <a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-001-01')">办公桌椅采购标段</a>
                                        </td>
                                        <td>已完成</td>
                                        <td><span class="status-tag status-approved">审核通过</span></td>
                                        <td>办公设备采购项目</td>
                                        <td>PRJ-2024-001</td>
                                        <td>货物采购</td>
                                        <td><span class="status-tag status-completed">已完成</span></td>
                                        <td><span class="status-tag audit-approved">已通过</span></td>
                                        <td>公开招标</td>
                                        <td>公开招标</td>
                                        <td>集中采购</td>
                                        <td>XX招标代理有限公司</td>
                                        <td>张三</td>
                                        <td>XX有限公司</td>
                                        <td>2024-03-15</td>
                                        <td>2024-03-01</td>
                                        <td>2024-04-30</td>
                                        <td>2024-02-20</td>
                                        <td class="frozen-column action-col">
                                            <button class="action-btn btn-danger" onclick="deleteSection('SEC-2024-001-01')">删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="frozen-column checkbox-col">
                                            <input type="checkbox">
                                        </td>
                                        <td class="frozen-column code-col">
                                            <a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-001-02')">电脑设备采购标段</a>
                                        </td>
                                        <td>已完成</td>
                                        <td><span class="status-tag status-approved">审核通过</span></td>
                                        <td>办公设备采购项目</td>
                                        <td>PRJ-2024-001</td>
                                        <td>货物采购</td>
                                        <td><span class="status-tag status-completed">已完成</span></td>
                                        <td><span class="status-tag audit-approved">已通过</span></td>
                                        <td>公开招标</td>
                                        <td>公开招标</td>
                                        <td>集中采购</td>
                                        <td>XX招标代理有限公司</td>
                                        <td>张三</td>
                                        <td>XX有限公司</td>
                                        <td>2024-03-15</td>
                                        <td>2024-03-01</td>
                                        <td>2024-04-30</td>
                                        <td>2024-02-20</td>
                                        <td class="frozen-column action-col">
                                            <button class="action-btn btn-danger" onclick="deleteSection('SEC-2024-001-02')">删除</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="frozen-column checkbox-col">
                                            <input type="checkbox">
                                        </td>
                                        <td class="frozen-column code-col">
                                            <a href="#" class="table-link" onclick="viewSectionDetail('SEC-2024-002-01')">系统开发标段</a>
                                        </td>
                                        <td>已完成</td>
                                        <td><span class="status-tag status-approved">审核通过</span></td>
                                        <td>IT系统建设项目</td>
                                        <td>PRJ-2024-002</td>
                                        <td>服务采购</td>
                                        <td><span class="status-tag status-completed">已完成</span></td>
                                        <td><span class="status-tag audit-approved">已通过</span></td>
                                        <td>邀请招标</td>
                                        <td>邀请招标</td>
                                        <td>分散采购</td>
                                        <td>YY咨询有限公司</td>
                                        <td>李四</td>
                                        <td>YY科技有限公司</td>
                                        <td>2024-04-01</td>
                                        <td>2024-04-15</td>
                                        <td>2024-06-30</td>
                                        <td>2024-03-01</td>
                                        <td class="frozen-column action-col">
                                            <button class="action-btn btn-danger" onclick="deleteSection('SEC-2024-002-01')">删除</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            
                            <!-- 分页 -->
                            <div class="pagination">
                                <div class="pagination-info">共 3 条记录，当前第 1 页</div>
                                <div class="pagination-controls">
                                    <button class="page-btn">上一页</button>
                                    <button class="page-btn active">1</button>
                                    <button class="page-btn">2</button>
                                    <button class="page-btn">3</button>
                                    <button class="page-btn">下一页</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>

    <!-- 帮助弹窗 -->
    <div class="help-modal" id="helpModal">
        <div class="help-content">
            <div class="help-header">
                <h3>功能说明</h3>
                <button class="close-btn" onclick="hideHelp()">&times;</button>
            </div>
            <div class="help-body">
                <h4>页面功能</h4>
                <p>项目标段管理页面用于管理招采项目和标段信息，支持以下功能：</p>
                <ul>
                    <li><strong>待办/已办页签：</strong>区分待处理和已处理的项目标段</li>
                    <li><strong>项目视图：</strong>以项目为主体，支持展开查看项目下的标段信息</li>
                    <li><strong>标段视图：</strong>以标段为主体，平铺显示所有标段信息</li>
                    <li><strong>查询功能：</strong>支持基础查询和高级查询条件</li>
                    <li><strong>批量操作：</strong>支持批量审核、删除等操作</li>
                </ul>
                
                <h4>操作说明</h4>
                <ul>
                    <li><strong>新建：</strong>点击"新建项目"或"新建标段"按钮创建新记录</li>
                    <li><strong>编辑：</strong>点击操作列的"编辑"按钮修改记录信息</li>
                    <li><strong>审核：</strong>点击操作列的"审核"按钮进行审核操作</li>
                    <li><strong>详情：</strong>点击编号或名称链接查看详细信息</li>
                    <li><strong>展开项目：</strong>在项目视图中点击项目编号前的箭头展开标段列表</li>
                </ul>
                
                <h4>状态说明</h4>
                <ul>
                    <li><strong>计划状态：</strong>草稿、待开始、进行中、已完成</li>
                    <li><strong>标段状态：</strong>待审核、审核中、审核通过、审核未过</li>
                    <li><strong>标段阶段：</strong>准备阶段、交易公告、补遗澄清答疑、流标、评标结果公示、中标结果公示</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 页签切换
        function switchTab(tabType) {
            const todoTab = document.getElementById('todo-tab');
            const doneTab = document.getElementById('done-tab');
            const tabBtns = document.querySelectorAll('.tab-btn');
            
            tabBtns.forEach(btn => btn.classList.remove('active'));
            
            if (tabType === 'todo') {
                todoTab.classList.add('active');
                doneTab.classList.remove('active');
                document.querySelector('.tab-btn[onclick*="todo"]').classList.add('active');
            } else {
                todoTab.classList.remove('active');
                doneTab.classList.add('active');
                document.querySelector('.tab-btn[onclick*="done"]').classList.add('active');
            }
            
            // 如果当前处于视图模式，需要重新切换到对应的视图
            const activeViewBtn = document.querySelector('.view-btn.active');
            if (activeViewBtn) {
                const viewType = activeViewBtn.textContent.includes('项目') ? 'project' : 'section';
                switchView(viewType);
            }
        }

        // 视图切换
        function switchView(viewType) {
            // 获取当前激活的页签
            const activeTab = document.querySelector('.tab-btn.active');
            const currentTab = activeTab ? (activeTab.textContent.includes('待办') ? 'todo' : 'done') : 'todo';
            
            // 查找所有视图容器
            const todoProjectView = document.getElementById('todo-project-view');
            const doneProjectView = document.getElementById('done-project-view');
            const todoSectionView = document.getElementById('todo-section-view');
            const doneSectionView = document.getElementById('done-section-view');
            const projectView = document.getElementById('project-view');
            const sectionView = document.getElementById('section-view');
            
            // 查找红框区域的固定内容
            const fixedSearchArea = document.querySelector('.search-area');
            const fixedActionBar = document.querySelector('.action-bar');
            const fixedTableContainer = document.querySelector('.table-container');
            
            // 隐藏所有视图容器
            const allViews = [todoProjectView, doneProjectView, todoSectionView, doneSectionView, projectView, sectionView];
            allViews.forEach(view => {
                if (view) {
                    view.style.display = 'none';
                    view.classList.remove('active');
                }
            });
            
            // 更新视图按钮状态
            const viewBtns = document.querySelectorAll('.view-btn');
            viewBtns.forEach(btn => btn.classList.remove('active'));
            
            if (viewType === 'project') {
                // 根据当前页签显示对应的项目视图
                let targetView = null;
                if (currentTab === 'todo' && todoProjectView) {
                    targetView = todoProjectView;
                } else if (currentTab === 'done' && doneProjectView) {
                    targetView = doneProjectView;
                } else {
                    // 如果没有找到对应的页签视图，显示固定区域的内容
                    if (fixedSearchArea) fixedSearchArea.style.display = 'block';
                    if (fixedActionBar) fixedActionBar.style.display = 'block';
                    if (fixedTableContainer) fixedTableContainer.style.display = 'block';
                }
                
                if (targetView) {
                    targetView.style.display = 'block';
                    targetView.classList.add('active');
                    // 隐藏红框区域的固定内容
                    if (fixedSearchArea) fixedSearchArea.style.display = 'none';
                    if (fixedActionBar) fixedActionBar.style.display = 'none';
                    if (fixedTableContainer) fixedTableContainer.style.display = 'none';
                }
                
                // 激活项目视图按钮
                const projectBtn = document.querySelector('.view-btn[onclick*="project"]');
                if (projectBtn) projectBtn.classList.add('active');
                
            } else if (viewType === 'section') {
                // 根据当前页签显示对应的标段视图
                let targetView = null;
                if (currentTab === 'todo' && todoSectionView) {
                    targetView = todoSectionView;
                } else if (currentTab === 'done' && doneSectionView) {
                    targetView = doneSectionView;
                } else if (sectionView) {
                    targetView = sectionView;
                }
                
                if (targetView) {
                    targetView.style.display = 'block';
                    targetView.classList.add('active');
                }
                
                // 隐藏红框区域的固定内容
                if (fixedSearchArea) fixedSearchArea.style.display = 'none';
                if (fixedActionBar) fixedActionBar.style.display = 'none';
                if (fixedTableContainer) fixedTableContainer.style.display = 'none';
                
                // 激活标段视图按钮
                const sectionBtn = document.querySelector('.view-btn[onclick*="section"]');
                if (sectionBtn) sectionBtn.classList.add('active');
            }
        }

        // 高级查询切换
        function toggleAdvanced(button) {
            // 如果没有传入button参数，尝试从event获取
            if (!button && typeof event !== 'undefined') {
                button = event.target;
            }
            
            // 找到当前按钮所在的查询区域
            const searchArea = button.closest('.search-area');
            let advancedSearch = null;
            
            if (searchArea) {
                advancedSearch = searchArea.querySelector('.advanced-search');
            }
            
            // 如果在search-area中没找到，尝试在同级元素中查找
            if (!advancedSearch) {
                const parent = button.closest('.view-content, .tab-content');
                if (parent) {
                    advancedSearch = parent.querySelector('.advanced-search');
                }
            }
            
            // 如果还是没找到，使用ID查找
            if (!advancedSearch) {
                advancedSearch = document.getElementById('advanced-search');
            }
            
            if (advancedSearch) {
                if (advancedSearch.style.display === 'none' || advancedSearch.style.display === '') {
                    advancedSearch.style.display = 'block';
                    button.textContent = '收起';
                } else {
                    advancedSearch.style.display = 'none';
                    button.textContent = '高级查询';
                }
            }
        }

        // 展开/收起项目下的标段
        function toggleProject(icon) {
            const row = icon.closest('tr');
            const projectCode = row.querySelector('.table-link').textContent;
            const subTable = document.getElementById('sub-' + projectCode);
            
            if (subTable.style.display === 'none') {
                subTable.style.display = 'table-row';
                icon.classList.add('expanded');
                icon.textContent = '▼';
            } else {
                subTable.style.display = 'none';
                icon.classList.remove('expanded');
                icon.textContent = '▶';
            }
        }

        // 显示帮助
        function showHelp() {
            document.getElementById('helpModal').style.display = 'block';
        }

        // 隐藏帮助
        function hideHelp() {
            document.getElementById('helpModal').style.display = 'none';
        }

        // 页面跳转函数
        function goToHome() {
            window.location.href = '招采平台主页.html';
        }

        function createProject() {
            openPageInTab('新建项目', '项目标段管理-新建编辑页.html?type=project&action=create');
        }

        function createSection() {
            openPageInTab('新建标段', '项目标段管理-新建编辑页.html?type=section&action=create');
        }

        function editProject(projectId) {
            openPageInTab('编辑项目-' + projectId, '项目标段管理-新建编辑页.html?type=project&action=edit&id=' + projectId);
        }

        function editSection(sectionId) {
            openPageInTab('编辑标段-' + sectionId, '项目标段管理-新建编辑页.html?type=section&action=edit&id=' + sectionId);
        }

        function auditProject(projectId) {
            openPageInTab('项目审核-' + projectId, '项目标段管理-审核页.html?type=project&id=' + projectId);
        }

        function auditSection(sectionId) {
            openPageInTab('标段审核-' + sectionId, '项目标段管理-审核页.html?type=section&id=' + sectionId);
        }

        function viewProjectDetail(projectId) {
            openPageInTab('项目详情-' + projectId, '项目标段管理-项目详情页.html?id=' + projectId);
        }

        function viewSectionDetail(sectionId) {
            openPageInTab('标段详情-' + sectionId, '项目标段管理-标段详情页.html?id=' + sectionId);
        }

        // 在主页面页签中打开页面的通用函数
        function openPageInTab(title, url) {
            // 向父窗口发送消息，在页签中打开页面
            if (window.parent && window.parent !== window) {
                window.parent.postMessage({
                    type: 'openPage',
                    title: title,
                    url: url
                }, '*');
            } else {
                // 如果不在iframe中，则直接跳转（兼容性处理）
                window.location.href = url;
            }
        }

        function deleteProject(projectId) {
            if (confirm('确定要删除该项目吗？')) {
                // 这里添加删除逻辑
                alert('项目删除成功');
            }
        }

        function deleteSection(sectionId) {
            if (confirm('确定要删除该标段吗？')) {
                // 这里添加删除逻辑
                alert('标段删除成功');
            }
        }

        function splitSection(id) {
            if (confirm('确定要拆分该标段吗？拆分后将生成新的子标段。')) {
                // 这里添加标段拆分逻辑
                alert('标段拆分成功，已生成新的子标段');
                // 可以跳转到标段拆分页面或刷新列表
                // openPageInTab('标段拆分-' + id, '项目标段管理-标段拆分页.html?id=' + id);
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认的页签为待办
            switchTab('todo');
            
            // 默认显示固定区域（红框区域），不切换到项目视图
            // 如果需要默认显示项目视图，可以取消下面这行的注释
            // switchView('project');
        });

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('helpModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>