<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中标结果公示管理 - 审核 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #fff;
            border-bottom: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
        }

        .help-icon {
            margin-left: 8px;
            width: 16px;
            height: 16px;
            background: #6b7280;
            border-radius: 50%;
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .breadcrumb {
            color: #6b7280;
            font-size: 14px;
        }

        .back-btn {
            background: #6b7280;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
        }

        .back-btn:hover {
            background: #4b5563;
        }

        /* 内容区域 */
        .content-area {
            padding: 24px;
        }

        /* 页签导航 */
        .tab-nav {
            border-bottom: 1px solid #e6e8eb;
            margin-bottom: 24px;
        }

        .tab-list {
            display: flex;
            list-style: none;
        }

        .tab-item {
            margin-right: 32px;
        }

        .tab-link {
            display: block;
            padding: 12px 0;
            color: #6b7280;
            text-decoration: none;
            font-size: 14px;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab-link.active {
            color: #2563eb;
            border-bottom-color: #2563eb;
        }

        .tab-link:hover {
            color: #2563eb;
        }

        /* 页签内容 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 详情区域 */
        .detail-section {
            background: white;
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .section-header {
            background: #f8fafc;
            border-bottom: 1px solid #e6e8eb;
            padding: 15px 20px;
            font-weight: 600;
            color: #1f2937;
            font-size: 14px;
            position: relative;
        }

        .section-header::after {
            content: '';
            position: absolute;
            left: 20px;
            bottom: 0;
            width: 60px;
            height: 2px;
            background: #2563eb;
        }

        .section-content {
            padding: 20px;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .detail-grid.full-width {
            grid-template-columns: 1fr;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-size: 13px;
            color: #6b7280;
            margin-bottom: 6px;
            font-weight: 500;
        }

        .detail-value {
            font-size: 13px;
            color: #1f2937;
            min-height: 20px;
            padding: 4px 0;
        }

        .detail-value.rich-content {
            border: 1px solid #e6e8eb;
            border-radius: 4px;
            padding: 12px;
            background: #f9fafb;
            min-height: 60px;
        }

        .detail-value.rich-content p {
            margin-bottom: 8px;
        }

        .detail-value.rich-content h1 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            text-align: center;
        }

        .detail-value.rich-content h2 {
            font-size: 16px;
            font-weight: 600;
            margin: 16px 0 8px 0;
        }

        .detail-value.rich-content h3 {
            font-size: 14px;
            font-weight: 600;
            margin: 12px 0 6px 0;
        }

        .detail-value.rich-content ul {
            margin-left: 20px;
            margin-bottom: 8px;
        }

        .detail-value.rich-content ol {
            margin-left: 20px;
            margin-bottom: 8px;
        }

        .detail-value.rich-content .signature {
            text-align: right;
            margin-top: 20px;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-draft {
            background: #f3f4f6;
            color: #374151;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-approved {
            background: #d1fae5;
            color: #065f46;
        }

        .status-published {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-rejected {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-withdrawn {
            background: #f3f4f6;
            color: #6b7280;
        }

        /* 操作按钮 */
        .action-bar {
            background: #fff;
            border-top: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .btn-group {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: 1px solid transparent;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-outline {
            background: #fff;
            border-color: #d1d5db;
            color: #374151;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        /* 候选人信息表格样式 */
        .candidate-info-section {
            width: 100%;
        }

        .selected-winners {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f0f9ff;
            border-radius: 6px;
            border-left: 4px solid #3b82f6;
        }

        .winner-info {
            font-weight: 600;
            color: #1e40af;
        }

        .candidate-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            overflow: hidden;
        }

        .candidate-table th {
            background-color: #f9fafb;
            padding: 12px 8px;
            text-align: center;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
        }

        .candidate-table td {
            padding: 12px 8px;
            text-align: center;
            border-bottom: 1px solid #f3f4f6;
        }

        .candidate-table tbody tr:last-child td {
            border-bottom: none;
        }

        .candidate-table tbody tr:hover {
            background-color: #f9fafb;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        /* 时间线样式 */
        .timeline {
            position: relative;
            padding-left: 24px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e5e7eb;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 24px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -20px;
            top: 6px;
            width: 8px;
            height: 8px;
            background: #3b82f6;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #3b82f6;
        }

        .timeline-content {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 16px;
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .timeline-action {
            font-weight: 600;
            color: #1f2937;
            font-size: 14px;
        }

        .timeline-time {
            color: #6b7280;
            font-size: 12px;
        }

        .timeline-user {
            color: #6b7280;
            font-size: 13px;
            margin-bottom: 4px;
        }

        .timeline-desc {
            color: #374151;
            font-size: 13px;
            line-height: 1.6;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e6e8eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .help-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            color: #6b7280;
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            color: #374151;
        }

        .help-body {
            padding: 24px;
        }

        .help-section {
            margin-bottom: 24px;
        }

        .help-section h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
        }

        .help-section p {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 12px;
        }

        .help-list {
            list-style: none;
            padding-left: 0;
        }

        .help-list li {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 8px;
            padding-left: 16px;
            position: relative;
        }

        .help-list li::before {
            content: '•';
            color: #3b82f6;
            position: absolute;
            left: 0;
        }

        .help-list strong {
            color: #1f2937;
        }

        /* 操作按钮 */
        .action-bar {
            background: #f8fafc;
            border-top: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            bottom: 0;
        }

        .btn-group {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #2563eb;
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
        }

        .btn-success {
            background: #059669;
            color: white;
        }

        .btn-success:hover {
            background: #047857;
        }

        .btn-warning {
            background: #d97706;
            color: white;
        }

        .btn-warning:hover {
            background: #b45309;
        }

        .btn-danger {
            background: #dc2626;
            color: white;
        }

        .btn-danger:hover {
            background: #b91c1c;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-outline {
            background: white;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">
                    中标结果公示管理 - 审核
                    <span class="help-icon" onclick="showHelp()" title="功能说明">?</span>
                </h1>
                <div class="breadcrumb">首页 > 中标结果公示管理 > 公示审核</div>
            </div>
            <button class="back-btn" onclick="goBack()">返回列表</button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 页签导航 -->
            <div class="tab-nav">
                <ul class="tab-list">
                    <li class="tab-item">
                        <a href="#" class="tab-link active" onclick="switchTab('announcement')">公告详情</a>
                    </li>
                    <li class="tab-item">
                        <a href="#" class="tab-link" onclick="switchTab('project')">项目信息</a>
                    </li>
                    <li class="tab-item">
                        <a href="#" class="tab-link" onclick="switchTab('record')">操作记录</a>
                    </li>
                </ul>
            </div>

            <!-- 公告详情页签 -->
            <div id="announcement-tab" class="tab-content active">
                <!-- 项目信息 -->
                <div class="detail-section">
                    <div class="section-header">项目信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">计划项目编号</div>
                                <div class="detail-value">JHXM-2024-001</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">所属计划项目名称</div>
                                <div class="detail-value">办公用品采购项目</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购方式</div>
                                <div class="detail-value">公开招标</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">项目业主</div>
                                <div class="detail-value">XX有限公司</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">所属二级公司单位</div>
                                <div class="detail-value">XX分公司</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">代理机构</div>
                                <div class="detail-value">XX招标代理有限公司</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">立项决策文件</div>
                                <div class="detail-value">项目立项申请书.pdf</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">立项决策日期</div>
                                <div class="detail-value">2024-01-10</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">项目总预算（万元）</div>
                                <div class="detail-value">25.00</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 标段信息 -->
                <div class="detail-section">
                    <div class="section-header">标段信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">标段名称</div>
                                <div class="detail-value">办公桌椅采购</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">标段编号</div>
                                <div class="detail-value">BD-2024-001</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购金额（万元）</div>
                                <div class="detail-value">23.50</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">标段说明</div>
                                <div class="detail-value">采购办公桌椅，包括办公桌、办公椅、会议桌椅等，要求产品质量可靠，符合环保要求。</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 中标信息 -->
                <div class="detail-section">
                    <div class="section-header">中标信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">公告标题</div>
                                <div class="detail-value">XX有限公司办公桌椅采购中标结果公示</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">是否公示</div>
                                <div class="detail-value">
                                    <span class="status-badge status-pending">是</span>
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">成交金额（万元）</div>
                                <div class="detail-value">23.50</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">中选结果公示开始时间</div>
                                <div class="detail-value">2024-01-26 09:00:00</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">中选结果公示结束时间</div>
                                <div class="detail-value">2024-01-31 17:00:00</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">业主代表姓名</div>
                                <div class="detail-value">张三</div>
                            </div>
                        </div>
                        
                        <!-- 候选人信息表格 -->
                        <div class="detail-item" style="margin-top: 20px;">
                            <div class="detail-label">候选人信息</div>
                            <div class="detail-value">
                                <div class="candidate-info-section">
                                    <div class="selected-winners">
                                        <div class="winner-info">中标人：XX家具有限公司</div>
                                    </div>
                                    <table class="candidate-table">
                                        <thead>
                                            <tr>
                                                <th width="60">排序</th>
                                                <th>投标单位全称</th>
                                                <th width="100">投标形式</th>
                                                <th width="120">投标报价</th>
                                                <th width="100">综合得分</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr style="background: #f0f9ff;">
                                                <td>1</td>
                                                <td>XX家具有限公司</td>
                                                <td>金额</td>
                                                <td>23.50万元</td>
                                                <td>95.8</td>
                                            </tr>
                                            <tr>
                                                <td>2</td>
                                                <td>YY办公用品有限公司</td>
                                                <td>金额</td>
                                                <td>24.20万元</td>
                                                <td>92.5</td>
                                            </tr>
                                            <tr>
                                                <td>3</td>
                                                <td>ZZ家具制造有限公司</td>
                                                <td>金额</td>
                                                <td>24.80万元</td>
                                                <td>89.2</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width" style="margin-top: 20px;">
                            <div class="detail-item">
                                <div class="detail-label">中标情况说明</div>
                                <div class="detail-value">经评标委员会评审，XX家具有限公司综合得分最高，符合中标条件，确定为中标人。中标产品质量可靠，价格合理，具有完善的售后服务体系。</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">异常情况说明</div>
                                <div class="detail-value empty">无</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 附件信息 -->
                <div class="detail-section">
                    <div class="section-header">附件信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">中标结果公示文件</div>
                                <div class="detail-value">
                                    <div class="file-list">
                                        <div class="file-item">
                                            <div class="file-name">中标结果公示文件.pdf</div>
                                            <a href="#" class="file-download">下载</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">评标报告</div>
                                <div class="detail-value">
                                    <div class="file-list">
                                        <div class="file-item">
                                            <div class="file-name">评标报告.pdf</div>
                                            <a href="#" class="file-download">下载</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 项目信息页签 -->
            <div id="project-tab" class="tab-content">
                <!-- 项目信息 -->
                <div class="detail-section">
                    <div class="section-header">项目信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">计划项目编号</div>
                                <div class="detail-value">JHXM-2024-001</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">所属计划项目名称</div>
                                <div class="detail-value">办公用品采购项目</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购方式</div>
                                <div class="detail-value">公开招标</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">项目业主</div>
                                <div class="detail-value">XX有限公司</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">所属二级公司单位</div>
                                <div class="detail-value">XX分公司</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">代理机构</div>
                                <div class="detail-value">XX招标代理有限公司</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">立项决策文件</div>
                                <div class="detail-value">项目立项申请书.pdf</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">立项决策日期</div>
                                <div class="detail-value">2024-01-10</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">项目总预算（万元）</div>
                                <div class="detail-value">25.00</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 标段信息 -->
                <div class="detail-section">
                    <div class="section-header">标段信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">标段名称</div>
                                <div class="detail-value">办公桌椅采购</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">标段编号</div>
                                <div class="detail-value">BD-2024-001</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购金额（万元）</div>
                                <div class="detail-value">23.50</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">标段说明</div>
                                <div class="detail-value">采购办公桌椅，包括办公桌、办公椅、会议桌椅等，要求产品质量可靠，符合环保要求。</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 中标信息 -->
                <div class="detail-section">
                    <div class="section-header">中标信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">公告标题</div>
                                <div class="detail-value">XX有限公司办公桌椅采购中标结果公示</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">是否公示</div>
                                <div class="detail-value">
                                    <span class="status-badge status-pending">是</span>
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">成交金额（万元）</div>
                                <div class="detail-value">23.50</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">中选结果公示开始时间</div>
                                <div class="detail-value">2024-01-26 09:00:00</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">中选结果公示结束时间</div>
                                <div class="detail-value">2024-01-31 17:00:00</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">业主代表姓名</div>
                                <div class="detail-value">张三</div>
                            </div>
                        </div>
                        
                        <!-- 候选人信息表格 -->
                        <div class="detail-item" style="margin-top: 20px;">
                            <div class="detail-label">候选人信息</div>
                            <div class="detail-value">
                                <div class="candidate-info-section">
                                    <div class="selected-winners">
                                        <div class="winner-info">中标人：XX家具有限公司</div>
                                    </div>
                                    <table class="candidate-table">
                                        <thead>
                                            <tr>
                                                <th width="60">排序</th>
                                                <th>投标单位全称</th>
                                                <th width="100">投标形式</th>
                                                <th width="120">投标报价</th>
                                                <th width="100">综合得分</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr style="background: #f0f9ff;">
                                                <td>1</td>
                                                <td>XX家具有限公司</td>
                                                <td>金额</td>
                                                <td>23.50万元</td>
                                                <td>95.8</td>
                                            </tr>
                                            <tr>
                                                <td>2</td>
                                                <td>YY办公用品有限公司</td>
                                                <td>金额</td>
                                                <td>24.20万元</td>
                                                <td>92.5</td>
                                            </tr>
                                            <tr>
                                                <td>3</td>
                                                <td>ZZ家具制造有限公司</td>
                                                <td>金额</td>
                                                <td>24.80万元</td>
                                                <td>89.2</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width" style="margin-top: 20px;">
                            <div class="detail-item">
                                <div class="detail-label">中标情况说明</div>
                                <div class="detail-value">经评标委员会评审，XX家具有限公司综合得分最高，符合中标条件，确定为中标人。中标产品质量可靠，价格合理，具有完善的售后服务体系。</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">异常情况说明</div>
                                <div class="detail-value empty">无</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作记录页签 -->
            <div id="record-tab" class="tab-content">
                <div class="detail-section">
                    <div class="section-header">操作记录</div>
                    <div class="section-content">
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">提交审核</div>
                                        <div class="timeline-time">2024-01-12 16:45:30</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三（采购专员）</div>
                                    <div class="timeline-desc">
                                        提交公告审核，等待审核人员审核。状态变更：草稿 → 待审核
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">编辑</div>
                                        <div class="timeline-time">2024-01-11 09:15:42</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三</div>
                                    <div class="timeline-desc">
                                        完善申请人资格要求和采购文件获取信息。状态变更：草稿 → 草稿
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">创建</div>
                                        <div class="timeline-time">2024-01-10 14:30:25</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三</div>
                                    <div class="timeline-desc">
                                        创建公告，关联标段：办公桌椅采购标段。状态变更：无 → 草稿
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-bar">
            <div class="btn-group">
                <button class="btn btn-outline" onclick="goBack()">返回列表</button>
                <button class="btn btn-secondary" onclick="printAnnouncement()">打印公告</button>
            </div>
            <div class="btn-group">
                <button class="btn btn-success" onclick="approveAnnouncement()">审核通过</button>
                <button class="btn btn-danger" onclick="rejectAnnouncement()">审核驳回</button>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="help-modal" class="help-modal">
        <div class="help-content">
            <div class="help-header">
                <h3 class="help-title">公告审核功能说明</h3>
                <button class="close-btn" onclick="hideHelp()">&times;</button>
            </div>
            <div class="help-body">
                <div class="help-section">
                    <h4>功能概述</h4>
                    <p>公告审核页面用于审核人员对提交的招标采购公告进行审核，确保公告内容的合规性和完整性。</p>
                </div>
                
                <div class="help-section">
                    <h4>页签说明</h4>
                    <ul class="help-list">
                        <li><strong>公告详情：</strong>显示待审核公告的完整内容</li>
                        <li><strong>项目信息：</strong>显示关联项目的详细信息，便于核对一致性</li>
                        <li><strong>操作记录：</strong>查看公告的操作历史</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>审核说明</h4>
                    <ul class="help-list">
                        <li><strong>审核通过：</strong>公告内容符合要求，可以发布</li>
                        <li><strong>审核驳回：</strong>公告内容存在问题，需要修改后重新提交</li>
                        <li><strong>审核意见：</strong>必须填写，说明审核的具体意见</li>
                        <li><strong>备注：</strong>可选填写，补充说明其他事项</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>注意事项</h4>
                    <ul class="help-list">
                        <li>审核前请仔细核对公告内容的准确性</li>
                        <li>确认与项目信息的一致性</li>
                        <li>审核意见将作为历史记录保存</li>
                        <li>审核通过后公告将自动发布</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页签切换
        function switchTab(tabName) {
            // 隐藏所有页签内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有页签链接的激活状态
            const tabLinks = document.querySelectorAll('.tab-link');
            tabLinks.forEach(link => {
                link.classList.remove('active');
            });
            
            // 显示选中的页签内容
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 激活选中的页签链接
            event.target.classList.add('active');
        }

        // 帮助弹窗
        function showHelp() {
            document.getElementById('help-modal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('help-modal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });

        // 业务操作函数
        function goBack() {
            window.location.href = '中标结果公示管理-列表页.html';
        }

        function approveAnnouncement() {
            const reason = prompt('请输入审核通过的意见：');
            if (reason && reason.trim()) {
                console.log('审核通过', reason);
                alert('审核通过成功！公告已发布。');
                // 跳转回列表页
                window.location.href = '中标结果公示管理-列表页.html';
            } else if (reason !== null) {
                alert('请填写审核意见');
            }
        }

        function rejectAnnouncement() {
             const reason = prompt('请输入驳回的具体原因：');
             if (reason && reason.trim()) {
                 console.log('审核驳回', reason);
                 alert('审核驳回成功！已通知创建人修改。');
                 // 跳转回列表页
                 window.location.href = '中标结果公示管理-列表页.html';
             } else if (reason !== null) {
                 alert('请填写驳回原因');
             }
         }

         function printAnnouncement() {
             console.log('打印公告');
             window.print();
         }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('公告审核页面加载完成');
        });
    </script>
</body>
</html>