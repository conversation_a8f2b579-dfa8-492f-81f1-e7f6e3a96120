<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知详情 - 招采平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            padding: 0;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* 页面标题 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 2px solid #e8eaec;
        }
        
        .page-title {
            font-size: 20px;
            font-weight: bold;
            color: #17233d;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .back-btn {
            color: #1890ff;
            text-decoration: none;
            font-size: 14px;
            margin-right: 12px;
        }
        
        .back-btn:hover {
            color: #40a9ff;
        }
        
        .page-breadcrumb {
            font-size: 13px;
            color: #808695;
        }
        
        /* 详情卡片 */
        .detail-card {
            background: white;
            border-radius: 4px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.05);
            margin-bottom: 20px;
        }
        
        .detail-header {
            padding: 20px;
            border-bottom: 1px solid #e8eaec;
        }
        
        .detail-title {
            font-size: 18px;
            font-weight: bold;
            color: #17233d;
            margin-bottom: 12px;
            line-height: 1.4;
        }
        
        .detail-meta {
            display: flex;
            gap: 24px;
            font-size: 13px;
            color: #808695;
        }
        
        .detail-body {
            padding: 20px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #17233d;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e8eaec;
        }
        
        .content-text {
            font-size: 14px;
            line-height: 1.8;
            color: #515a6e;
            margin-bottom: 20px;
            text-align: justify;
        }
        
        .content-text p {
            margin-bottom: 12px;
        }
        
        .content-text ul {
            margin-left: 20px;
            margin-bottom: 12px;
        }
        
        .content-text li {
            margin-bottom: 6px;
        }
        
        /* 附件区域 */
        .attachment-section {
            margin-top: 20px;
        }
        
        .attachment-list {
            list-style: none;
        }
        
        .attachment-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px;
            background-color: #f8f9fa;
            border-radius: 4px;
            margin-bottom: 8px;
            transition: background-color 0.3s;
        }
        
        .attachment-item:hover {
            background-color: #e6f7ff;
        }
        
        .attachment-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        
        .attachment-name {
            flex: 1;
            font-size: 14px;
            color: #515a6e;
        }
        
        .attachment-size {
            font-size: 12px;
            color: #808695;
        }
        
        .attachment-download {
            color: #1890ff;
            text-decoration: none;
            font-size: 13px;
            padding: 4px 8px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        .attachment-download:hover {
            background-color: #e6f7ff;
            text-decoration: none;
        }
        
        /* 操作按钮 */
        .action-buttons {
            padding: 20px;
            border-top: 1px solid #e8eaec;
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }
        
        .btn {
            height: 36px;
            padding: 0 16px;
            border: none;
            border-radius: 4px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }
        
        .btn-primary {
            background-color: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #40a9ff;
        }
        
        .btn-default {
            background-color: #f5f5f5;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .btn-default:hover {
            background-color: #e6e6e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div>
                <div class="page-title">
                    <a href="javascript:history.back()" class="back-btn">← 返回</a>
                    通知详情
                </div>
                <div class="page-breadcrumb">工作台 > 通知详情</div>
            </div>
        </div>
        
        <!-- 详情内容 -->
        <div class="detail-card">
            <div class="detail-header">
                <div class="detail-title" id="noticeTitle">关于2024年度采购计划编制工作的通知</div>
                <div class="detail-meta">
                    <span>发布人：系统管理员</span>
                    <span>发布时间：2023-12-10 09:30:00</span>
                    <span>浏览次数：156</span>
                </div>
            </div>
            
            <div class="detail-body">
                <div class="section-title">通知内容</div>
                <div class="content-text" id="noticeContent">
                    <p>各相关部门：</p>
                    <p>为进一步规范和完善我单位2024年度采购计划编制工作，确保采购活动的科学性、合理性和有效性，现就相关事项通知如下：</p>
                    
                    <p><strong>一、编制原则</strong></p>
                    <ul>
                        <li>坚持统筹规划、合理安排的原则，确保采购计划与单位发展规划和年度预算相衔接；</li>
                        <li>坚持公开透明、公平竞争的原则，严格按照政府采购法律法规执行；</li>
                        <li>坚持节约高效、绿色环保的原则，优先采购节能环保产品。</li>
                    </ul>
                    
                    <p><strong>二、时间安排</strong></p>
                    <ul>
                        <li>2023年12月15日前：各部门提交初步采购需求；</li>
                        <li>2023年12月25日前：完成采购计划汇总和初审；</li>
                        <li>2024年1月5日前：完成采购计划审批和发布。</li>
                    </ul>
                    
                    <p><strong>三、工作要求</strong></p>
                    <p>1. 各部门要高度重视采购计划编制工作，指定专人负责，确保按时完成；</p>
                    <p>2. 采购需求要真实准确，避免重复采购和浪费；</p>
                    <p>3. 严格执行采购预算，不得超预算采购。</p>
                    
                    <p>特此通知。</p>
                    
                    <p style="text-align: right; margin-top: 30px;">招采管理部<br>2023年12月10日</p>
                </div>
                
                <!-- 附件区域 -->
                <div class="attachment-section">
                    <div class="section-title">附件</div>
                    <ul class="attachment-list">
                        <li class="attachment-item">
                            <div class="attachment-icon">📄</div>
                            <div class="attachment-name">2024年度采购计划编制指南.pdf</div>
                            <div class="attachment-size">2.5MB</div>
                            <a href="#" class="attachment-download" onclick="downloadAttachment('guide.pdf')">下载</a>
                        </li>
                        <li class="attachment-item">
                            <div class="attachment-icon">📊</div>
                            <div class="attachment-name">采购计划申报表模板.xlsx</div>
                            <div class="attachment-size">156KB</div>
                            <a href="#" class="attachment-download" onclick="downloadAttachment('template.xlsx')">下载</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="detail-card">
            <div class="action-buttons">
                <button class="btn btn-default" onclick="printNotice()">打印</button>
                <button class="btn btn-primary" onclick="shareNotice()">分享</button>
            </div>
        </div>
    </div>
    
    <script>
        // 下载附件
        function downloadAttachment(filename) {
            console.log('下载附件:', filename);
            alert('下载功能已触发：' + filename);
        }
        
        // 打印通知
        function printNotice() {
            window.print();
        }
        
        // 分享通知
        function shareNotice() {
            const url = window.location.href;
            if (navigator.share) {
                navigator.share({
                    title: document.getElementById('noticeTitle').textContent,
                    url: url
                });
            } else {
                // 复制链接到剪贴板
                navigator.clipboard.writeText(url).then(() => {
                    alert('链接已复制到剪贴板');
                });
            }
        }
        
        // 根据URL参数加载对应的通知详情
        function loadNoticeDetail() {
            const urlParams = new URLSearchParams(window.location.search);
            const noticeId = urlParams.get('id');
            
            if (noticeId) {
                console.log('加载通知详情，ID:', noticeId);
                
                // 根据不同的ID加载不同的内容
                const noticeData = {
                    'notice-001': {
                        title: '关于2024年度采购计划编制工作的通知',
                        content: document.getElementById('noticeContent').innerHTML
                    },
                    'notice-002': {
                        title: '招采平台系统升级维护通知',
                        content: '<p>各位用户：</p><p>为提升系统性能和用户体验，招采平台将于2023年12月15日22:00-24:00进行系统升级维护。</p><p>维护期间系统将暂停服务，请各位用户提前做好相关准备工作。</p><p>给您带来的不便，敬请谅解。</p><p style="text-align: right; margin-top: 30px;">技术部<br>2023-12-08日</p>'
                    },
                    'notice-003': {
                        title: '关于规范采购流程管理的通知',
                        content: '<p>各相关部门：</p><p>为进一步规范采购流程管理，提高采购效率和质量，现就相关事项通知如下：</p><p><strong>一、严格执行采购程序</strong></p><p>所有采购活动必须严格按照既定程序执行，不得随意简化或跳过必要环节。</p><p><strong>二、加强过程监督</strong></p><p>建立健全采购过程监督机制，确保采购活动公开、公平、公正。</p><p>特此通知。</p><p style="text-align: right; margin-top: 30px;">采购管理部<br>2023-12-05日</p>'
                    },
                    'notice-004': {
                        title: '关于开展供应商资格审查工作的通知',
                        content: '<p>各相关部门：</p><p>为确保供应商质量，维护采购秩序，现决定开展供应商资格审查工作。</p><p><strong>审查时间：</strong>2023年12月20日-2024年1月10日</p><p><strong>审查内容：</strong>资质证书、业绩证明、财务状况等</p><p>请各部门积极配合，确保审查工作顺利进行。</p><p style="text-align: right; margin-top: 30px;">供应商管理部<br>2023-12-12日</p>'
                    },
                    'notice-005': {
                        title: '关于加强合同履约管理的通知',
                        content: '<p>各相关部门：</p><p>为加强合同履约管理，防范履约风险，现就相关事项通知如下：</p><p>1. 建立合同履约跟踪机制；</p><p>2. 定期开展履约检查；</p><p>3. 及时处理履约争议。</p><p>特此通知。</p><p style="text-align: right; margin-top: 30px;">合同管理部<br>2023-12-06日</p>'
                    }
                };
                
                if (noticeData[noticeId]) {
                    document.getElementById('noticeTitle').textContent = noticeData[noticeId].title;
                    document.getElementById('noticeContent').innerHTML = noticeData[noticeId].content;
                }
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadNoticeDetail();
            console.log('工作台通知详情页加载完成');
        });
    </script>
</body>
</html>