<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公告管理 - 新建/编辑 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }



        /* 内容区域 */
        .content-area {
            padding: 24px;
        }

        /* 表单区域 */
        .form-section {
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #1890ff;
            position: relative;
        }

        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            bottom: -2px;
            width: 40px;
            height: 2px;
            background: #1890ff;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-grid.full-width {
            grid-template-columns: 1fr;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 13px;
            color: #374151;
            margin-bottom: 6px;
            font-weight: 500;
        }

        .form-label.required::after {
            content: '*';
            color: #dc2626;
            margin-left: 4px;
        }

        .form-input, .form-select, .form-textarea {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 13px;
            transition: border-color 0.3s;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-input:disabled {
            background-color: #f9fafb;
            color: #6b7280;
            cursor: not-allowed;
        }

        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }

        .help-text {
            font-size: 12px;
            color: #6b7280;
            margin-top: 4px;
        }

        /* 选择标段按钮 */
        .section-select-container {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .section-select-container .form-input {
            flex: 1;
        }

        .btn {
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
            text-align: center;
            text-decoration: none;
            display: inline-block;
        }

        .btn-outline {
            background: white;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #2563eb;
            color: #2563eb;
        }

        .btn-primary {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }

        .btn-primary:hover {
            background: #1d4ed8;
        }

        .btn-success {
            background: #059669;
            color: white;
            border-color: #059669;
        }

        .btn-success:hover {
            background: #047857;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
            border-color: #6b7280;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        /* 富文本编辑器 */
        .rich-editor {
            border: 1px solid #d1d5db;
            border-radius: 4px;
            overflow: hidden;
        }

        .editor-toolbar {
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
            padding: 8px 12px;
            display: flex;
            gap: 8px;
        }

        .editor-btn {
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }

        .editor-btn:hover {
            background: #e5e7eb;
        }

        .editor-content {
            min-height: 120px;
            padding: 12px;
            outline: none;
            line-height: 1.6;
        }

        .editor-content:focus {
            background: #fefefe;
        }

        /* 操作按钮区域 */
        .action-bar {
            position: sticky;
            bottom: 0;
            background: white;
            border-top: 1px solid #e6e8eb;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        }

        .btn-group {
            display: flex;
            gap: 12px;
        }

        /* 标段选择弹窗样式 */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
        }
        
        .modal-content {
            background-color: #fff;
            margin: 2% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 1200px;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }
        
        .modal-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e8e8e8;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f8f9fa;
        }
        
        .modal-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .close {
            font-size: 24px;
            font-weight: bold;
            color: #999;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            color: #333;
        }
        
        .search-section {
            padding: 20px 24px;
            border-bottom: 1px solid #e8e8e8;
            background-color: #fafafa;
        }
        
        .search-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-bottom: 16px;
        }
        
        .search-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-group label {
            min-width: 80px;
            font-size: 13px;
            color: #666;
            white-space: nowrap;
        }
        
        .search-group input,
        .search-group select {
            flex: 1;
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 13px;
        }
        
        .date-range {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
        }
        
        .date-range input {
            flex: 1;
        }
        
        .date-range span {
            font-size: 13px;
            color: #666;
        }
        
        .search-buttons {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }
        
        .table-section {
            padding: 20px 24px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
        }
        
        .data-table th {
            background-color: #f5f5f5;
            font-weight: 600;
            color: #333;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .data-table tbody tr:hover {
            background-color: #f0f8ff;
        }
        
        .data-table input[type="radio"] {
            margin: 0;
        }
        
        .modal-footer {
            padding: 16px 24px;
            border-top: 1px solid #e8e8e8;
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            background-color: #f8f9fa;
        }

        /* 帮助弹窗 */
        .help-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            display: none;
            align-items: center;
            justify-content: center;
        }

        .help-content {
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .help-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e6e8eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8fafc;
        }

        .help-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            color: #6b7280;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            color: #374151;
        }

        .help-body {
            padding: 24px;
        }

        .help-section {
            margin-bottom: 24px;
        }

        .help-section h4 {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .help-section p {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 8px;
        }

        .help-list {
            color: #6b7280;
            line-height: 1.6;
            padding-left: 20px;
        }

        .help-list li {
            margin-bottom: 4px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .btn-group {
                width: 100%;
                justify-content: center;
            }

            .search-grid {
                grid-template-columns: 1fr;
            }

            .modal-content {
                width: 95%;
                margin: 20px auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 项目信息 -->
            <div class="form-section">
                <div class="section-title">项目信息</div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label required">选择标段</label>
                            <div class="section-select-container">
                                <input type="text" class="form-input" id="sectionName" placeholder="请选择标段" readonly>
                                <input type="hidden" id="sectionId" value="">
                                <button type="button" class="btn btn-outline" onclick="openSectionSelectModal()">选择标段</button>
                            </div>
                            <div class="help-text">选择标段为审核通过、阶段为准备阶段</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">计划项目编号</label>
                            <input type="text" class="form-input" id="projectCode" disabled>
                            <div class="help-text">由标段自动带出</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">所属计划项目名称</label>
                            <input type="text" class="form-input" id="projectName" disabled>
                            <div class="help-text">由标段自动带出</div>
                        </div>
                    </div>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">采购方式</label>
                            <input type="text" class="form-input" id="procurementMethod" disabled>
                            <div class="help-text">由标段自动带出</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购金额</label>
                            <input type="text" class="form-input" id="procurementAmount" disabled>
                            <div class="help-text">由标段自动带出</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">项目业主</label>
                            <input type="text" class="form-input" id="projectOwner" disabled>
                            <div class="help-text">由标段自动带出</div>
                        </div>
                    </div>
                    
                    <div class="form-grid full-width">
                        <div class="form-group">
                            <label class="form-label">项目基本情况（建设内容及规模）</label>
                            <textarea class="form-textarea" id="projectDescription" disabled placeholder="由标段自动带出"></textarea>
                            <div class="help-text">由标段自动带出</div>
                        </div>
                    </div>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">所属二级公司单位</label>
                            <input type="text" class="form-input" id="companyUnit" disabled>
                            <div class="help-text">由标段自动带出</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">代理机构</label>
                            <input type="text" class="form-input" id="agencyOrganization" disabled>
                            <div class="help-text">由标段自动带出</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">备注</label>
                            <input type="text" class="form-input" id="projectRemarks" disabled>
                            <div class="help-text">由标段自动带出</div>
                        </div>
                    </div>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">立项决策文件</label>
                            <input type="text" class="form-input" id="decisionDocument" disabled>
                            <div class="help-text">由标段自动带出</div>
                        </div>
                    </div>
        </div>

        <!-- 标段信息 -->
        <div class="form-section">
            <div class="section-title">标段信息</div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">标段名称</label>
                            <input type="text" class="form-input" id="sectionNameDisplay" disabled>
                            <div class="help-text">由标段自动带出</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">标段编号</label>
                            <input type="text" class="form-input" id="sectionCode" disabled>
                            <div class="help-text">由标段自动带出</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">标段说明</label>
                            <input type="text" class="form-input" id="sectionDescription" disabled>
                            <div class="help-text">由标段自动带出</div>
                        </div>
                    </div>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">采购金额（万元）</label>
                            <input type="text" class="form-input" id="sectionAmount" disabled>
                            <div class="help-text">由标段自动带出</div>
                        </div>
                    </div>
        </div>

        <!-- 公告信息 -->
        <div class="form-section">
            <div class="section-title">公告信息</div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label required">公告标题</label>
                            <input type="text" class="form-input" id="announcementTitle" placeholder="请输入公告标题" maxlength="255">
                            <div class="help-text">底部提示"项目业主全称+标段名称+公告类型"，不超过255字符</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">评审办法</label>
                            <select class="form-select" id="evaluationMethod">
                                <option value="">请选择评审办法</option>
                                <option value="综合评估法">综合评估法</option>
                                <option value="经评审最低价法">经评审最低价法</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">是否公示</label>
                            <select class="form-select" id="isPublic">
                                <option value="是">是</option>
                                <option value="否">否</option>
                            </select>
                            <div class="help-text">根据采购金额判断，若大于配置数则为是</div>
                        </div>
                    </div>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label required">公告开始时间</label>
                            <input type="datetime-local" class="form-input" id="announcementStartTime">
                            <div class="help-text">默认当前时间</div>
                        </div>
                    </div>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label required">澄清截止时间</label>
                            <input type="datetime-local" class="form-input" id="clarificationDeadline">
                            <div class="help-text">默认当前时间+5天</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">开标时间</label>
                            <input type="datetime-local" class="form-input" id="openingTime">
                            <div class="help-text">默认当前时间+5天</div>
                        </div>
                    </div>
                    
                    <div class="form-grid full-width">
                        <div class="form-group">
                            <label class="form-label required">公告内容</label>
                            <div class="rich-editor">
                                <div class="editor-toolbar">
                                    <button class="editor-btn" onclick="formatText('bold')"><b>B</b></button>
                                    <button class="editor-btn" onclick="formatText('italic')"><i>I</i></button>
                                    <button class="editor-btn" onclick="formatText('underline')"><u>U</u></button>
                                    <button class="editor-btn" onclick="insertList()">列表</button>
                                    <button class="editor-btn" onclick="insertTable()">表格</button>
                                </div>
                                <div class="editor-content" contenteditable="true" id="announcementContent">
                                    <p>请输入公告内容...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-grid full-width">
                        <div class="form-group">
                            <label class="form-label">备注</label>
                            <input type="text" class="form-input" id="announcementRemarks" placeholder="请输入备注" maxlength="255">
                            <div class="help-text">对该项目基本情况的补充说明，不超过255字符</div>
                        </div>
                    </div>
        </div>

        <!-- 其他信息 -->
        <div class="form-section">
            <div class="section-title">其他信息</div>
                    <div class="form-grid full-width">
                        <div class="form-group">
                            <label class="form-label">审核依据</label>
                            <input type="file" class="form-input" id="auditBasis" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                            <div class="help-text">支持上传PDF、Word文档、图片等格式文件</div>
                        </div>
                    </div>
        </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-bar">
            <div class="btn-group">
                <button class="btn btn-outline" onclick="goBack()">取消</button>
                <button class="btn btn-secondary" onclick="saveDraft()">保存草稿</button>
            </div>
            <div class="btn-group">
                <button class="btn btn-primary" onclick="preview()">预览</button>
                <button class="btn btn-success" onclick="submit()">提交审核</button>
            </div>
        </div>
    </div>

    <!-- 标段选择弹窗 -->
    <div id="section-select-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>请选择标段</h3>
                <span class="close" onclick="closeSectionSelectModal()">&times;</span>
            </div>
            
            <!-- 查询区域 -->
            <div class="search-section">
                <div class="search-grid">
                    <div class="search-group">
                        <label>标段名称：</label>
                        <input type="text" id="searchSectionName" placeholder="请输入标段名称">
                    </div>
                    <div class="search-group">
                        <label>项目名称：</label>
                        <input type="text" id="searchProjectName" placeholder="请输入项目名称">
                    </div>
                    <div class="search-group">
                        <label>采购方式：</label>
                        <select id="searchProcurementMethod">
                            <option value="">全部</option>
                            <option value="公开招标">公开招标</option>
                            <option value="邀请招标">邀请招标</option>
                            <option value="竞争性谈判">竞争性谈判</option>
                            <option value="单一来源">单一来源</option>
                        </select>
                    </div>
                    <div class="search-group">
                        <label>采购类型：</label>
                        <select id="searchProcurementType">
                            <option value="">全部</option>
                            <option value="货物">货物</option>
                            <option value="工程">工程</option>
                            <option value="服务">服务</option>
                        </select>
                    </div>
                    <div class="search-group">
                        <label>创建时间：</label>
                        <div class="date-range">
                            <input type="date" id="searchStartDate">
                            <span>至</span>
                            <input type="date" id="searchEndDate">
                        </div>
                    </div>
                </div>
                <div class="search-buttons">
                    <button class="btn btn-outline" onclick="resetSearch()">重置</button>
                    <button class="btn btn-primary" onclick="searchSections()">查询</button>
                </div>
            </div>
            
            <!-- 数据列表 -->
            <div class="table-section">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th width="50">选择</th>
                            <th>标段名称</th>
                            <th>所属项目</th>
                            <th>采购方式</th>
                            <th>采购类型</th>
                            <th>创建时间</th>
                        </tr>
                    </thead>
                    <tbody id="sectionTableBody">
                        <tr>
                            <td><input type="radio" name="sectionSelect" value="1" data-section-name="标段A" data-project-code="PRJ001" data-project-name="测试项目A" data-procurement-method="公开招标" data-procurement-amount="500万元" data-project-owner="测试业主A" data-project-description="测试项目基本情况A" data-company-unit="二级公司A" data-project-remarks="项目备注A" data-decision-document="决策文件A" data-section-code="SEC001" data-section-description="标段说明A" data-section-amount="500"></td>
                            <td>标段A</td>
                            <td>测试项目A</td>
                            <td>公开招标</td>
                            <td>工程</td>
                            <td>2024-01-15 10:30:00</td>
                        </tr>
                        <tr>
                            <td><input type="radio" name="sectionSelect" value="2" data-section-name="标段B" data-project-code="PRJ002" data-project-name="测试项目B" data-procurement-method="邀请招标" data-procurement-amount="300万元" data-project-owner="测试业主B" data-project-description="测试项目基本情况B" data-company-unit="二级公司B" data-project-remarks="项目备注B" data-decision-document="决策文件B" data-section-code="SEC002" data-section-description="标段说明B" data-section-amount="300"></td>
                            <td>标段B</td>
                            <td>测试项目B</td>
                            <td>邀请招标</td>
                            <td>货物</td>
                            <td>2024-01-16 14:20:00</td>
                        </tr>
                        <tr>
                            <td><input type="radio" name="sectionSelect" value="3" data-section-name="标段C" data-project-code="PRJ003" data-project-name="测试项目C" data-procurement-method="竞争性谈判" data-procurement-amount="200万元" data-project-owner="测试业主C" data-project-description="测试项目基本情况C" data-company-unit="二级公司C" data-project-remarks="项目备注C" data-decision-document="决策文件C" data-section-code="SEC003" data-section-description="标段说明C" data-section-amount="200"></td>
                            <td>标段C</td>
                            <td>测试项目C</td>
                            <td>竞争性谈判</td>
                            <td>服务</td>
                            <td>2024-01-17 09:15:00</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 弹窗底部 -->
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeSectionSelectModal()">取消</button>
                <button class="btn btn-primary" onclick="confirmSectionSelect()">确认</button>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="help-modal" class="help-modal">
        <div class="help-content">
            <div class="help-header">
                <h3 class="help-title">公告新建/编辑功能说明</h3>
                <button class="close-btn" onclick="hideHelp()">&times;</button>
            </div>
            <div class="help-body">
                <div class="help-section">
                    <h4>功能概述</h4>
                    <p>公告新建/编辑页面用于创建和修改招标采购公告，支持富文本编辑和自动数据关联。</p>
                </div>
                
                <div class="help-section">
                    <h4>操作流程</h4>
                    <ul class="help-list">
                        <li>选择关联标段，系统自动带出相关信息</li>
                        <li>填写公告标题和必填项</li>
                        <li>编辑公告内容等富文本内容</li>
                        <li>设置公告时间信息</li>
                        <li>上传审核依据文件</li>
                        <li>保存草稿或提交审核</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>字段说明</h4>
                    <ul class="help-list">
                        <li><strong>选择标段：</strong>必选，选择后自动带出项目相关信息</li>
                        <li><strong>公告标题：</strong>必填，建议格式为"项目业主全称+标段名称+公告类型"</li>
                        <li><strong>是否公示：</strong>根据采购金额自动判断</li>
                        <li><strong>公告开始时间：</strong>公告发布的开始时间</li>
                        <li><strong>澄清截止时间：</strong>澄清问题的截止时间</li>
                        <li><strong>开标时间：</strong>公开开标的具体时间</li>
                        <li><strong>公告内容：</strong>支持富文本编辑</li>
                        <li><strong>审核依据：</strong>上传相关审核文件</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>注意事项</h4>
                    <ul class="help-list">
                        <li>标有红色星号(*)的字段为必填项</li>
                        <li>灰色字段由关联标段自动带出，不可编辑</li>
                        <li>公告标题不超过255个字符</li>
                        <li>备注不超过255个字符</li>
                        <li>审核依据支持多文件上传</li>
                        <li>保存草稿后可继续编辑，提交审核后需等待审核</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认时间
            const now = new Date();
            const currentDateTime = now.toISOString().slice(0, 16);
            
            // 设置公告开始时间为当前时间
            document.getElementById('announcementStartTime').value = currentDateTime;
            
            // 设置澄清截止时间为当前时间+5天
            const clarificationDate = new Date(now.getTime() + 5 * 24 * 60 * 60 * 1000);
            document.getElementById('clarificationDeadline').value = clarificationDate.toISOString().slice(0, 16);
            
            // 设置开标时间为当前时间+5天
            document.getElementById('openingTime').value = clarificationDate.toISOString().slice(0, 16);
        });

        // 打开标段选择弹窗
        function openSectionSelectModal() {
            document.getElementById('section-select-modal').style.display = 'block';
        }

        // 关闭标段选择弹窗
        function closeSectionSelectModal() {
            document.getElementById('section-select-modal').style.display = 'none';
        }

        // 确认选择标段
        function confirmSectionSelect() {
            const selectedRadio = document.querySelector('input[name="sectionSelect"]:checked');
            if (!selectedRadio) {
                alert('请选择一个标段');
                return;
            }

            // 获取选中标段的数据
            const sectionData = selectedRadio.dataset;
            
            // 填充标段信息
            document.getElementById('sectionName').value = sectionData.sectionName;
            document.getElementById('sectionId').value = selectedRadio.value;
            
            // 填充项目信息
            document.getElementById('projectCode').value = sectionData.projectCode;
            document.getElementById('projectName').value = sectionData.projectName;
            document.getElementById('procurementMethod').value = sectionData.procurementMethod;
            document.getElementById('procurementAmount').value = sectionData.procurementAmount;
            document.getElementById('projectOwner').value = sectionData.projectOwner;
            document.getElementById('projectDescription').value = sectionData.projectDescription;
            document.getElementById('companyUnit').value = sectionData.companyUnit;
            document.getElementById('agencyOrganization').value = sectionData.agencyOrganization || '中国招标有限公司';
            document.getElementById('projectRemarks').value = sectionData.projectRemarks;
            document.getElementById('decisionDocument').value = sectionData.decisionDocument;
            
            // 填充标段信息
            document.getElementById('sectionNameDisplay').value = sectionData.sectionName;
            document.getElementById('sectionCode').value = sectionData.sectionCode;
            document.getElementById('sectionDescription').value = sectionData.sectionDescription;
            document.getElementById('sectionAmount').value = sectionData.sectionAmount;
            
            // 根据采购金额判断是否公示
            const amount = parseFloat(sectionData.sectionAmount);
            const isPublicSelect = document.getElementById('isPublic');
            if (amount > 100) { // 假设配置值为100万
                isPublicSelect.value = '是';
            } else {
                isPublicSelect.value = '否';
            }
            
            // 关闭弹窗
            closeSectionSelectModal();
        }

        // 查询标段
        function searchSections() {
            // 这里应该调用后端API进行查询
            alert('查询功能需要后端支持');
        }

        // 重置查询
        function resetSearch() {
            document.getElementById('searchSectionName').value = '';
            document.getElementById('searchProjectName').value = '';
            document.getElementById('searchProcurementMethod').value = '';
            document.getElementById('searchProcurementType').value = '';
            document.getElementById('searchStartDate').value = '';
            document.getElementById('searchEndDate').value = '';
        }

        // 富文本编辑器功能
        function formatText(command) {
            document.execCommand(command, false, null);
        }

        function insertList() {
            document.execCommand('insertUnorderedList', false, null);
        }

        function insertTable() {
            const table = '<table border="1" style="border-collapse: collapse; width: 100%;"><tr><td>单元格1</td><td>单元格2</td></tr><tr><td>单元格3</td><td>单元格4</td></tr></table>';
            document.execCommand('insertHTML', false, table);
        }

        // 显示帮助
        function showHelp() {
            document.getElementById('help-modal').style.display = 'flex';
        }

        // 隐藏帮助
        function hideHelp() {
            document.getElementById('help-modal').style.display = 'none';
        }

        // 返回列表
        function goBack() {
            if (confirm('确定要返回吗？未保存的数据将丢失。')) {
                window.location.href = '公告管理-列表页.html';
            }
        }

        // 保存草稿
        function saveDraft() {
            // 验证必填字段
            if (!validateForm(false)) {
                return;
            }
            
            // 收集表单数据
            const formData = collectFormData();
            formData.status = '草稿';
            
            // 这里应该调用后端API保存草稿
            console.log('保存草稿:', formData);
            alert('草稿保存成功！');
        }

        // 预览
        function preview() {
            // 验证必填字段
            if (!validateForm(true)) {
                return;
            }
            
            // 这里应该打开预览页面
            alert('预览功能需要后端支持');
        }

        // 提交审核
        function submit() {
            // 验证必填字段
            if (!validateForm(true)) {
                return;
            }
            
            if (confirm('确定要提交审核吗？提交后将无法修改。')) {
                // 收集表单数据
                const formData = collectFormData();
                formData.status = '待审核';
                
                // 这里应该调用后端API提交审核
                console.log('提交审核:', formData);
                alert('提交成功！');
                window.location.href = '公告管理-列表页.html';
            }
        }

        // 验证表单
        function validateForm(isSubmit) {
            const requiredFields = [
                { id: 'sectionId', name: '选择标段' },
                { id: 'announcementTitle', name: '公告标题' },
                { id: 'isPublic', name: '是否公示' },
                { id: 'announcementStartTime', name: '公告开始时间' },
                { id: 'clarificationDeadline', name: '澄清截止时间' },
                { id: 'openingTime', name: '开标时间' }
            ];
            
            for (let field of requiredFields) {
                const element = document.getElementById(field.id);
                if (!element.value.trim()) {
                    alert(`请填写${field.name}`);
                    element.focus();
                    return false;
                }
            }
            
            // 验证公告内容
            const content = document.getElementById('announcementContent').innerText.trim();
            if (isSubmit && (!content || content === '请输入公告内容...')) {
                alert('请填写公告内容');
                document.getElementById('announcementContent').focus();
                return false;
            }
            
            return true;
        }

        // 收集表单数据
        function collectFormData() {
            return {
                // 项目信息
                sectionId: document.getElementById('sectionId').value,
                sectionName: document.getElementById('sectionName').value,
                projectCode: document.getElementById('projectCode').value,
                projectName: document.getElementById('projectName').value,
                procurementMethod: document.getElementById('procurementMethod').value,
                procurementAmount: document.getElementById('procurementAmount').value,
                projectOwner: document.getElementById('projectOwner').value,
                projectDescription: document.getElementById('projectDescription').value,
                companyUnit: document.getElementById('companyUnit').value,
                agencyOrganization: document.getElementById('agencyOrganization').value,
                projectRemarks: document.getElementById('projectRemarks').value,
                decisionDocument: document.getElementById('decisionDocument').value,
                
                // 标段信息
                sectionNameDisplay: document.getElementById('sectionNameDisplay').value,
                sectionCode: document.getElementById('sectionCode').value,
                sectionDescription: document.getElementById('sectionDescription').value,
                sectionAmount: document.getElementById('sectionAmount').value,
                
                // 公告信息
                announcementTitle: document.getElementById('announcementTitle').value,
                isPublic: document.getElementById('isPublic').value,
                announcementStartTime: document.getElementById('announcementStartTime').value,
                clarificationDeadline: document.getElementById('clarificationDeadline').value,
                openingTime: document.getElementById('openingTime').value,
                announcementContent: document.getElementById('announcementContent').innerHTML,
                announcementRemarks: document.getElementById('announcementRemarks').value,
                
                // 其他信息
                auditBasis: document.getElementById('auditBasis').files
            };
        }

        // 点击弹窗外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('section-select-modal');
            const helpModal = document.getElementById('help-modal');
            if (event.target === modal) {
                closeSectionSelectModal();
            }
            if (event.target === helpModal) {
                hideHelp();
            }
        }
    </script>
</body>
</html>