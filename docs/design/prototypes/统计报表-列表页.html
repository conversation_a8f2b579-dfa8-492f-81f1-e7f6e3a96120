<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计报表</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            padding: 20px;
        }
        
        .page-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        /* 页面标题 */
        .page-header {
            padding: 20px;
            border-bottom: 1px solid #e8eaec;
            background: #fafbfc;
        }
        
        .page-title {
            font-size: 18px;
            font-weight: bold;
            color: #1c4e80;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .help-icon {
            width: 16px;
            height: 16px;
            background: #1890ff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
            position: relative;
        }
        
        .help-tooltip {
            position: absolute;
            top: 25px;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
            display: none;
        }
        
        .help-tooltip::before {
            content: '';
            position: absolute;
            top: -4px;
            left: 50%;
            transform: translateX(-50%);
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-bottom: 4px solid #333;
        }
        
        .help-icon:hover .help-tooltip {
            display: block;
        }
        
        /* 页签样式 */
        .report-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e8eaec;
        }
        
        .report-tab {
            padding: 15px 25px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .report-tab:hover {
            background: #e6f7ff;
        }
        
        .report-tab.active {
            color: #1890ff;
            background: white;
            border-bottom-color: #1890ff;
        }
        
        /* 查询区域 */
        .query-section {
            padding: 20px;
            background: #fafbfc;
            border-bottom: 1px solid #e8eaec;
        }
        
        .query-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .form-label {
            font-size: 13px;
            color: #666;
            font-weight: 500;
        }
        
        .form-control {
            height: 36px;
            padding: 0 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 13px;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .form-control.select-control {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12"><path d="M6 8L2 4h8z" fill="%23666"/></svg>');
            background-repeat: no-repeat;
            background-position: right 8px center;
            background-size: 12px;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
        }
        
        .form-control.select-control:focus {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12"><path d="M6 8L2 4h8z" fill="%231890ff"/></svg>');
        }
        
        .query-buttons {
            display: flex;
            gap: 10px;
            align-items: end;
        }
        
        .btn {
            height: 36px;
            padding: 0 16px;
            border: none;
            border-radius: 4px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .btn-primary {
            background: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
        }
        
        .btn-default {
            background: white;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .btn-default:hover {
            color: #1890ff;
            border-color: #1890ff;
        }
        
        .btn-link {
            background: none;
            color: #1890ff;
            border: none;
            text-decoration: underline;
        }
        
        .btn-link:hover {
            color: #40a9ff;
        }
        
        /* 高级查询 */
        .advanced-query {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e8eaec;
            display: none;
        }
        
        .advanced-query.show {
            display: block;
        }
        
        /* 统计指标区 */
        .stats-section {
            padding: 20px;
            background: white;
            border-bottom: 1px solid #e8eaec;
        }
        
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .stats-card {
            background: #fafbfc;
            border: 1px solid #e8eaec;
            border-radius: 6px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s;
        }
        
        .stats-card:hover {
            border-color: #1890ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
        }
        
        .stats-value {
            font-size: 24px;
            font-weight: 600;
            color: #1890ff;
            margin-bottom: 8px;
        }
        
        .stats-label {
            font-size: 13px;
            color: #666;
            font-weight: 500;
        }
        
        /* 功能按钮区 */
        .action-bar {
            padding: 15px 20px;
            background: white;
            border-bottom: 1px solid #e8eaec;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
        }
        
        .btn-success {
            background: #52c41a;
            color: white;
        }
        
        .btn-success:hover {
            background: #73d13d;
        }
        
        .btn-warning {
            background: #faad14;
            color: white;
        }
        
        .btn-warning:hover {
            background: #ffc53d;
        }
        
        /* 表格区域 */
        .table-container {
            padding: 20px;
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            table-layout: fixed;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e8eaec;
            font-size: 13px;
        }
        
        .data-table th {
            background: #fafafa;
            font-weight: 600;
            color: #333;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .data-table tbody tr:hover {
            background: #f5f5f5;
        }
        
        /* 冻结列样式 */
        .data-table th:first-child,
        .data-table td:first-child {
            position: sticky;
            left: 0;
            background: inherit;
            z-index: 5;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }
        
        .data-table th:first-child {
            z-index: 15;
        }
        
        /* 分页 */
        .pagination {
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #fafbfc;
            border-top: 1px solid #e8eaec;
        }
        
        .pagination-info {
            font-size: 13px;
            color: #666;
        }
        
        .pagination-controls {
            display: flex;
            gap: 5px;
            align-items: center;
        }
        
        .page-btn {
            width: 32px;
            height: 32px;
            border: 1px solid #d9d9d9;
            background: white;
            color: #666;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 13px;
        }
        
        .page-btn:hover {
            color: #1890ff;
            border-color: #1890ff;
        }
        
        .page-btn.active {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        
        .page-btn:disabled {
            color: #ccc;
            border-color: #f0f0f0;
            cursor: not-allowed;
        }
        
        /* 响应式 */
        @media (max-width: 768px) {
            .query-form {
                grid-template-columns: 1fr;
            }
            
            .action-bar {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
            
            .action-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="page-title">
                统计报表
                <div class="help-icon">
                    ?
                    <div class="help-tooltip">支持对采购项目和开标项目进行统计分析，提供多维度查询和报表导出功能</div>
                </div>
            </div>
        </div>
        
        <!-- 报表页签 -->
        <div class="report-tabs">
            <div class="report-tab active" onclick="switchReportTab('procurement')">
                采购项目情况统计表
            </div>
            <div class="report-tab" onclick="switchReportTab('bidding')">
                开标项目情况统计表
            </div>
        </div>
        
        <!-- 采购项目统计表 -->
        <div id="procurement-report" class="report-content">
            <!-- 查询区域 -->
            <div class="query-section">
                <div class="query-form">
                    <div class="form-group">
                        <label class="form-label">项目名称</label>
                        <input type="text" class="form-control" placeholder="请输入项目名称">
                    </div>
                    <div class="form-group">
                        <label class="form-label">项目类型</label>
                        <select class="form-control select-control">
                            <option value="">全部</option>
                            <option value="依法必须招标项目">依法必须招标项目</option>
                            <option value="非法定招标采购项目">非法定招标采购项目</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">采购类型</label>
                        <select class="form-control select-control">
                            <option value="">全部</option>
                            <option value="货物">货物</option>
                            <option value="施工">施工</option>
                            <option value="服务">服务</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">采购方式</label>
                        <select class="form-control select-control">
                            <option value="">全部</option>
                            <option value="公开招标">公开招标</option>
                            <option value="邀请招标">邀请招标</option>
                            <option value="竞争性谈判">竞争性谈判</option>
                            <option value="竞争性磋商">竞争性磋商</option>
                            <option value="单一来源">单一来源</option>
                            <option value="询价采购">询价采购</option>
                        </select>
                    </div>
                    <div class="query-buttons">
                        <button class="btn btn-primary" onclick="queryReport()">查询</button>
                        <button class="btn btn-default" onclick="resetQuery()">重置</button>
                        <button class="btn btn-link" onclick="toggleAdvanced()">高级查询</button>
                    </div>
                </div>
                
                <!-- 高级查询 -->
                <div class="advanced-query" id="advanced-query">
                    <div class="query-form">
                        <div class="form-group">
                            <label class="form-label">采购组织方式</label>
                            <select class="form-control select-control">
                                <option value="">全部</option>
                                <option value="集中采购">集中采购</option>
                                <option value="分散采购">分散采购</option>
                                <option value="委托代理">委托代理</option>
                                <option value="自主采购">自主采购</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">预算金额</label>
                            <select class="form-control select-control">
                                <option value="">全部</option>
                                <option value="0-50万">50万以下</option>
                                <option value="50-100万">50-100万</option>
                                <option value="100-500万">100-500万</option>
                                <option value="500-1000万">500-1000万</option>
                                <option value="1000万以上">1000万以上</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">标段数</label>
                            <select class="form-control select-control">
                                <option value="">全部</option>
                                <option value="1">1个标段</option>
                                <option value="2-3">2-3个标段</option>
                                <option value="4-5">4-5个标段</option>
                                <option value="5以上">5个以上标段</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">所属计划</label>
                            <select class="form-control select-control">
                                <option value="">全部</option>
                                <option value="2024年度基建计划">2024年度基建计划</option>
                                <option value="2024年度设备采购计划">2024年度设备采购计划</option>
                                <option value="2024年度信息化建设计划">2024年度信息化建设计划</option>
                                <option value="2024年度后勤服务计划">2024年度后勤服务计划</option>
                                <option value="2024年度车辆更新计划">2024年度车辆更新计划</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">计划开始时间</label>
                            <input type="date" class="form-control" value="2024-01-01">
                        </div>
                        <div class="form-group">
                            <label class="form-label">计划结束时间</label>
                            <input type="date" class="form-control" value="2024-12-31">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 统计指标 -->
            <div class="stats-section">
                <div class="stats-cards">
                    <div class="stats-card">
                        <div class="stats-value">156</div>
                        <div class="stats-label">项目总数</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-value">68</div>
                        <div class="stats-label">货物类型数</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-value">45</div>
                        <div class="stats-label">服务类型数</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-value">43</div>
                        <div class="stats-label">施工类型数</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-value">12,580.5</div>
                        <div class="stats-label">预算金额(万元)</div>
                    </div>
                </div>
            </div>
            

            
            <!-- 功能按钮 -->
            <div class="action-bar">
                <div class="action-buttons">
                    <button class="btn btn-success" onclick="exportReport('excel')">导出Excel</button>
                    <button class="btn btn-warning" onclick="exportReport('pdf')">导出PDF</button>
                    <button class="btn btn-default" onclick="printReport()">打印</button>
                </div>
                <div class="pagination-info">
                    共 <strong>156</strong> 条记录
                </div>
            </div>
            
            <!-- 数据表格 -->
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th style="width: 150px;">项目编号</th>
                            <th style="width: 200px;">项目名称</th>
                            <th style="width: 100px;">项目类型</th>
                            <th style="width: 100px;">采购类型</th>
                            <th style="width: 120px;">采购方式</th>
                            <th style="width: 140px;">采购组织方式</th>
                            <th style="width: 120px;">预算金额(万元)</th>
                            <th style="width: 100px;">标段数</th>
                            <th style="width: 150px;">所属计划</th>
                            <th style="width: 140px;">计划开始时间</th>
                            <th style="width: 140px;">计划结束时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>CG2024001</td>
                            <td>办公楼装修改造项目</td>
                            <td>依法必须招标项目</td>
                            <td>施工</td>
                            <td>公开招标</td>
                            <td>集中采购</td>
                            <td>500.00</td>
                            <td>3</td>
                            <td>2024年度基建计划</td>
                            <td>2024-01-15</td>
                            <td>2024-03-01</td>
                        </tr>
                        <tr>
                            <td>CG2024002</td>
                            <td>办公设备采购项目</td>
                            <td>非法定招标采购项目</td>
                            <td>货物</td>
                            <td>公开招标</td>
                            <td>分散采购</td>
                            <td>120.00</td>
                            <td>2</td>
                            <td>2024年度设备采购计划</td>
                            <td>2024-02-01</td>
                            <td>2024-03-15</td>
                        </tr>
                        <tr>
                            <td>CG2024003</td>
                            <td>信息系统开发服务</td>
                            <td>非法定招标采购项目</td>
                            <td>服务</td>
                            <td>竞争性谈判</td>
                            <td>委托代理</td>
                            <td>200.00</td>
                            <td>1</td>
                            <td>2024年度信息化建设计划</td>
                            <td>2024-02-15</td>
                            <td>2024-05-30</td>
                        </tr>
                        <tr>
                            <td>CG2024004</td>
                            <td>车辆采购项目</td>
                            <td>非法定招标采购项目</td>
                            <td>货物</td>
                            <td>邀请招标</td>
                            <td>分散采购</td>
                            <td>80.00</td>
                            <td>1</td>
                            <td>2024年度车辆更新计划</td>
                            <td>2024-03-01</td>
                            <td>2024-04-15</td>
                        </tr>
                        <tr>
                            <td>CG2024005</td>
                            <td>物业管理服务</td>
                            <td>非法定招标采购项目</td>
                            <td>服务</td>
                            <td>单一来源</td>
                            <td>委托代理</td>
                            <td>150.00</td>
                            <td>1</td>
                            <td>2024年度后勤服务计划</td>
                            <td>2024-01-10</td>
                            <td>2024-01-25</td>
                        </tr>

                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="pagination">
                <div class="pagination-info">
                    显示第 1-10 条，共 156 条记录
                </div>
                <div class="pagination-controls">
                    <button class="page-btn" disabled>‹</button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">3</button>
                    <button class="page-btn">...</button>
                    <button class="page-btn">16</button>
                    <button class="page-btn">›</button>
                </div>
            </div>
        </div>
        
        <!-- 开标项目统计表 -->
        <div id="bidding-report" class="report-content" style="display: none;">
            <!-- 查询区域 -->
            <div class="query-section">
                <div class="query-form">
                    <div class="form-group">
                        <label class="form-label">标段名称</label>
                        <input type="text" class="form-control" placeholder="请输入标段名称">
                    </div>
                    <div class="form-group">
                        <label class="form-label">标段阶段</label>
                        <select class="form-control select-control">
                            <option value="">全部</option>
                            <option value="交易公告">交易公告</option>
                            <option value="补遗澄清答疑">补遗澄清答疑</option>
                            <option value="中标结果公示">中标结果公示</option>
                            <option value="评标结果公示">评标结果公示</option>
                            <option value="签约履行">签约履行</option>
                            <option value="流标或中止">流标或中止</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">采购类型</label>
                        <select class="form-control select-control">
                            <option value="">全部</option>
                            <option value="货物">货物</option>
                            <option value="施工">施工</option>
                            <option value="服务">服务</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">采购方式</label>
                        <select class="form-control select-control">
                            <option value="">全部</option>
                            <option value="公开招标">公开招标</option>
                            <option value="邀请招标">邀请招标</option>
                            <option value="竞争性谈判">竞争性谈判</option>
                            <option value="竞争性磋商">竞争性磋商</option>
                            <option value="单一来源">单一来源</option>
                            <option value="询价采购">询价采购</option>
                        </select>
                    </div>
                    <div class="query-buttons">
                        <button class="btn btn-primary" onclick="queryReport()">查询</button>
                        <button class="btn btn-default" onclick="resetQuery()">重置</button>
                        <button class="btn btn-link" onclick="toggleAdvanced()">高级查询</button>
                    </div>
                </div>
                
                <!-- 高级查询 -->
                <div class="advanced-query" id="advanced-query">
                    <div class="query-form">
                        <div class="form-group">
                            <label class="form-label">项目名称</label>
                            <input type="text" class="form-control" placeholder="请输入项目名称">
                        </div>
                        <div class="form-group">
                            <label class="form-label">项目类型</label>
                            <select class="form-control select-control">
                                <option value="">全部</option>
                                <option value="依法必须招标项目">依法必须招标项目</option>
                                <option value="非法定招标采购项目">非法定招标采购项目</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购类型</label>
                            <select class="form-control select-control">
                                <option value="">全部</option>
                                <option value="货物">货物</option>
                                <option value="施工">施工</option>
                                <option value="服务">服务</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购方式</label>
                            <select class="form-control select-control">
                                <option value="">全部</option>
                                <option value="公开招标">公开招标</option>
                                <option value="邀请招标">邀请招标</option>
                                <option value="竞争性谈判">竞争性谈判</option>
                                <option value="竞争性磋商">竞争性磋商</option>
                                <option value="单一来源">单一来源</option>
                                <option value="询价">询价</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购组织方式</label>
                            <select class="form-control select-control">
                                <option value="">全部</option>
                                <option value="集中采购">集中采购</option>
                                <option value="分散采购">分散采购</option>
                                <option value="委托代理">委托代理</option>
                                <option value="自主采购">自主采购</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">预算金额</label>
                            <select class="form-control select-control">
                                <option value="">全部</option>
                                <option value="0-50万">50万以下</option>
                                <option value="50-100万">50-100万</option>
                                <option value="100-500万">100-500万</option>
                                <option value="500-1000万">500-1000万</option>
                                <option value="1000万以上">1000万以上</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">标段数</label>
                            <select class="form-control select-control">
                                <option value="">全部</option>
                                <option value="1">1个标段</option>
                                <option value="2-3">2-3个标段</option>
                                <option value="4-5">4-5个标段</option>
                                <option value="5以上">5个以上标段</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">所属计划</label>
                            <select class="form-control select-control">
                                <option value="">全部</option>
                                <option value="2024年度基建计划">2024年度基建计划</option>
                                <option value="2024年度设备采购计划">2024年度设备采购计划</option>
                                <option value="2024年度信息化建设计划">2024年度信息化建设计划</option>
                                <option value="2024年度后勤服务计划">2024年度后勤服务计划</option>
                                <option value="2024年度车辆更新计划">2024年度车辆更新计划</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">计划开始时间</label>
                            <input type="date" class="form-control" value="2024-01-01">
                        </div>
                        <div class="form-group">
                            <label class="form-label">计划结束时间</label>
                            <input type="date" class="form-control" value="2024-12-31">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 统计指标 -->
            <div class="stats-section">
                <div class="stats-cards">
                    <div class="stats-card">
                        <div class="stats-value">89</div>
                        <div class="stats-label">标段总数</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-value">52</div>
                        <div class="stats-label">已完成标段数</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-value">8,650.5</div>
                        <div class="stats-label">预算金额(万元)</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-value">7,890.2</div>
                        <div class="stats-label">招标金额(万元)</div>
                    </div>
                    <div class="stats-card">
                        <div class="stats-value">7,456.8</div>
                        <div class="stats-label">采购金额(万元)</div>
                    </div>
                </div>
            </div>
            
            <!-- 功能按钮 -->
            <div class="action-bar">
                <div class="action-buttons">
                    <button class="btn btn-success" onclick="exportReport('excel')">导出Excel</button>
                    <button class="btn btn-warning" onclick="exportReport('pdf')">导出PDF</button>
                    <button class="btn btn-default" onclick="printReport()">打印</button>
                </div>
                <div class="pagination-info">
                    共 <strong>89</strong> 条记录
                </div>
            </div>
            
            <!-- 数据表格 -->
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th style="width: 150px;">标段编号</th>
                            <th style="width: 200px;">标段名称</th>
                            <th style="width: 120px;">标段阶段</th>
                            <th style="width: 150px;">项目编号</th>
                            <th style="width: 200px;">项目名称</th>
                            <th style="width: 120px;">项目类型</th>
                            <th style="width: 100px;">采购类型</th>
                            <th style="width: 120px;">采购方式</th>
                            <th style="width: 120px;">预算金额(万元)</th>
                            <th style="width: 120px;">招标金额(万元)</th>
                            <th style="width: 100px;">标段数</th>
                            <th style="width: 150px;">所属计划</th>
                            <th style="width: 140px;">计划开始时间</th>
                            <th style="width: 140px;">计划结束时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>BD2024001-01</td>
                            <td>办公楼装修改造标段一</td>
                            <td><span style="color: #52c41a;">签约履行</span></td>
                            <td>CG2024001</td>
                            <td>办公楼装修改造项目</td>
                            <td>依法必须招标项目</td>
                            <td>施工</td>
                            <td>公开招标</td>
                            <td>300.00</td>
                            <td>285.50</td>
                            <td>3</td>
                            <td>2024年度基建计划</td>
                            <td>2024-01-15</td>
                            <td>2024-03-01</td>
                        </tr>
                        <tr>
                            <td>BD2024001-02</td>
                            <td>办公楼装修改造标段二</td>
                            <td><span style="color: #1890ff;">中标结果公示</span></td>
                            <td>CG2024001</td>
                            <td>办公楼装修改造项目</td>
                            <td>依法必须招标项目</td>
                            <td>施工</td>
                            <td>公开招标</td>
                            <td>200.00</td>
                            <td>195.80</td>
                            <td>3</td>
                            <td>2024年度基建计划</td>
                            <td>2024-01-15</td>
                            <td>2024-03-01</td>
                        </tr>
                        <tr>
                            <td>BD2024002-01</td>
                            <td>办公设备采购标段</td>
                            <td><span style="color: #52c41a;">签约履行</span></td>
                            <td>CG2024002</td>
                            <td>办公设备采购项目</td>
                            <td>非法定招标采购项目</td>
                            <td>货物</td>
                            <td>公开招标</td>
                            <td>120.00</td>
                            <td>115.80</td>
                            <td>2</td>
                            <td>2024年度设备采购计划</td>
                            <td>2024-02-01</td>
                            <td>2024-03-15</td>
                        </tr>
                        <tr>
                            <td>BD2024003-01</td>
                            <td>信息系统开发标段</td>
                            <td><span style="color: #faad14;">评标结果公示</span></td>
                            <td>CG2024003</td>
                            <td>信息系统开发服务</td>
                            <td>非法定招标采购项目</td>
                            <td>服务</td>
                            <td>竞争性谈判</td>
                            <td>200.00</td>
                            <td>195.00</td>
                            <td>1</td>
                            <td>2024年度信息化建设计划</td>
                            <td>2024-02-15</td>
                            <td>2024-05-30</td>
                        </tr>
                        <tr>
                            <td>BD2024004-01</td>
                            <td>车辆采购标段</td>
                            <td><span style="color: #722ed1;">交易公告</span></td>
                            <td>CG2024004</td>
                            <td>车辆采购项目</td>
                            <td>非法定招标采购项目</td>
                            <td>货物</td>
                            <td>邀请招标</td>
                            <td>80.00</td>
                            <td>-</td>
                            <td>1</td>
                            <td>2024年度车辆更新计划</td>
                            <td>2024-03-01</td>
                            <td>2024-04-15</td>
                        </tr>
                        <tr>
                            <td>BD2024005-01</td>
                            <td>清洁服务采购标段</td>
                            <td><span style="color: #fa8c16;">补遗澄清答疑</span></td>
                            <td>CG2024005</td>
                            <td>清洁服务采购</td>
                            <td>非法定招标采购项目</td>
                            <td>服务</td>
                            <td>公开招标</td>
                            <td>35.00</td>
                            <td>-</td>
                            <td>1</td>
                            <td>2024年度后勤服务计划</td>
                            <td>2024-02-05</td>
                            <td>2024-03-20</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="pagination">
                <div class="pagination-info">
                    显示第 1-10 条，共 89 条记录
                </div>
                <div class="pagination-controls">
                    <button class="page-btn" disabled>‹</button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">3</button>
                    <button class="page-btn">...</button>
                    <button class="page-btn">9</button>
                    <button class="page-btn">›</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 页签切换
        function switchReportTab(type) {
            // 更新页签样式
            document.querySelectorAll('.report-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 切换内容
            document.getElementById('procurement-report').style.display = type === 'procurement' ? 'block' : 'none';
            document.getElementById('bidding-report').style.display = type === 'bidding' ? 'block' : 'none';
        }
        
        // 高级查询展开收起
        function toggleAdvanced() {
            const advancedQuery = document.getElementById('advanced-query');
            const isShow = advancedQuery.classList.contains('show');
            
            if (isShow) {
                advancedQuery.classList.remove('show');
                event.target.textContent = '高级查询';
            } else {
                advancedQuery.classList.add('show');
                event.target.textContent = '收起查询';
            }
        }
        
        // 查询报表
        function queryReport() {
            alert('正在查询报表数据...');
            // 这里可以添加实际的查询逻辑
        }
        
        // 重置查询
        function resetQuery() {
            // 重置所有表单字段
            document.querySelectorAll('.form-control').forEach(input => {
                if (input.type === 'text' || input.type === 'date') {
                    input.value = '';
                } else if (input.tagName === 'SELECT') {
                    input.selectedIndex = 0;
                }
            });
        }
        
        // 导出报表
        function exportReport(format) {
            alert(`正在导出${format.toUpperCase()}格式报表...`);
            // 这里可以添加实际的导出逻辑
        }
        
        // 打印报表
        function printReport() {
            window.print();
        }
    </script>
</body>
</html>