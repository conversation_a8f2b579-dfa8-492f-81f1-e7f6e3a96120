<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中标结果公示管理 - 新建/编辑 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #fff;
            border-bottom: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
        }

        .help-icon {
            margin-left: 8px;
            width: 16px;
            height: 16px;
            background: #6b7280;
            border-radius: 50%;
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .breadcrumb {
            color: #6b7280;
            font-size: 14px;
        }

        .back-btn {
            background: #6b7280;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
        }

        .back-btn:hover {
            background: #4b5563;
        }

        /* 内容区域 */
        .content-area {
            padding: 24px;
        }

        /* 表单区域 */
        .form-section {
            background: white;
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .section-header {
            background: #f8fafc;
            border-bottom: 1px solid #e6e8eb;
            padding: 15px 20px;
            font-weight: 600;
            color: #1f2937;
            font-size: 14px;
            position: relative;
        }

        .section-header::after {
            content: '';
            position: absolute;
            left: 20px;
            bottom: 0;
            width: 60px;
            height: 2px;
            background: #2563eb;
        }

        .section-content {
            padding: 20px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-grid.full-width {
            grid-template-columns: 1fr;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 13px;
            color: #374151;
            margin-bottom: 6px;
            font-weight: 500;
        }

        .form-label.required::after {
            content: '*';
            color: #dc2626;
            margin-left: 4px;
        }

        .form-input {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 13px;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 13px;
            background: white;
            cursor: pointer;
        }

        .form-textarea {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 13px;
            resize: vertical;
            min-height: 80px;
            font-family: inherit;
        }

        .form-textarea:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        /* 富文本编辑器 */
        .rich-editor {
            border: 1px solid #d1d5db;
            border-radius: 4px;
            overflow: hidden;
        }

        .editor-toolbar {
            background: #f8fafc;
            border-bottom: 1px solid #e6e8eb;
            padding: 8px 12px;
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .editor-btn {
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }

        .editor-btn:hover {
            background: #f3f4f6;
        }

        .editor-btn.active {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }

        .editor-content {
            min-height: 200px;
            padding: 12px;
            outline: none;
            font-size: 13px;
            line-height: 1.6;
        }

        /* 按钮样式 */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #2563eb;
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
        }

        .btn-success {
            background: #059669;
            color: white;
        }

        .btn-success:hover {
            background: #047857;
        }

        .btn-warning {
            background: #d97706;
            color: white;
        }

        .btn-warning:hover {
            background: #b45309;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-outline {
            background: white;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        /* 操作按钮栏 */
        .action-bar {
            background: #f8fafc;
            border-top: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            bottom: 0;
        }

        .btn-group {
            display: flex;
            gap: 12px;
        }

        /* 标段选择按钮 */
        .select-segment-btn {
            background: #f3f4f6;
            border: 1px dashed #9ca3af;
            color: #6b7280;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .select-segment-btn:hover {
            background: #e5e7eb;
            border-color: #6b7280;
        }

        .selected-segment {
            background: #dbeafe;
            border-color: #2563eb;
            color: #1e40af;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            padding: 24px;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e6e8eb;
        }

        .help-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #6b7280;
        }

        .help-body {
            color: #374151;
            line-height: 1.6;
        }

        .help-section {
            margin-bottom: 16px;
        }

        .help-section h4 {
            color: #1f2937;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .help-section p {
            margin-bottom: 8px;
            font-size: 13px;
        }

        .help-list {
            list-style: none;
            padding-left: 16px;
        }

        .help-list li {
            margin-bottom: 4px;
            font-size: 13px;
            position: relative;
        }

        .help-list li::before {
            content: '•';
            color: #2563eb;
            position: absolute;
            left: -12px;
        }

        /* 标段选择弹窗 */
        .segment-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .segment-modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 1000px;
            max-height: 80vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .segment-modal-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e6e8eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .segment-modal-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .segment-modal-body {
            flex: 1;
            overflow-y: auto;
            padding: 20px 24px;
        }

        .segment-search {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
            margin-bottom: 20px;
            padding: 16px;
            background: #f8fafc;
            border-radius: 6px;
        }

        .segment-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .segment-table th {
            background: #f8fafc;
            border: 1px solid #e6e8eb;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            color: #374151;
        }

        .segment-table td {
            border: 1px solid #e6e8eb;
            padding: 12px 8px;
            color: #1f2937;
        }

        .segment-table tr:nth-child(even) {
            background: #f9fafb;
        }

        .segment-table tr:hover {
            background: #f3f4f6;
        }

        .segment-table tr.selected {
            background: #dbeafe;
        }

        .segment-modal-footer {
            padding: 16px 24px;
            border-top: 1px solid #e6e8eb;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 0;
            margin-top: 16px;
        }

        .pagination-info {
            color: #6b7280;
            font-size: 13px;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .page-btn {
            padding: 6px 12px;
            border: 1px solid #d1d5db;
            background: white;
            color: #374151;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.2s;
        }

        .page-btn:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }

        .page-btn.active {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 文件上传样式 */
        .file-upload-area {
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: #fafafa;
        }

        .upload-box {
            position: relative;
            padding: 40px 20px;
            text-align: center;
            border: 2px dashed #d9d9d9;
            border-radius: 6px;
            background: #fff;
            transition: all 0.3s;
            cursor: pointer;
        }

        .upload-box:hover {
            border-color: #1890ff;
            background: #f0f8ff;
        }

        .upload-icon {
            font-size: 48px;
            color: #d9d9d9;
            margin-bottom: 16px;
        }

        .upload-text p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }

        .upload-hint {
            font-size: 12px !important;
            color: #999 !important;
            margin-top: 8px !important;
        }

        .file-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .file-list {
            padding: 16px;
            border-top: 1px solid #f0f0f0;
            background: #fff;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            margin-bottom: 8px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
        }

        .file-info {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .file-icon {
            margin-right: 8px;
            font-size: 16px;
        }

        .file-name {
            font-size: 13px;
            color: #333;
            margin-right: 12px;
        }

        .file-size {
            font-size: 12px;
            color: #999;
        }

        .file-actions {
            display: flex;
            gap: 8px;
        }

        .file-action-btn {
            padding: 4px 8px;
            font-size: 12px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .file-download {
            background: #e6f7ff;
            color: #1890ff;
        }

        .file-download:hover {
            background: #bae7ff;
        }

        .file-delete {
            background: #fff2f0;
            color: #ff4d4f;
        }

        .file-delete:hover {
            background: #ffccc7;
        }

        /* 候选人信息表格样式 */
        .candidate-info-section {
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
        }
        
        .selected-winners {
            background: #f0f9ff;
            border-bottom: 1px solid #e0e0e0;
            padding: 12px 16px;
        }
        
        .winner-info {
            color: #1976d2;
            font-size: 14px;
        }
        
        .candidate-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        
        .candidate-table th,
        .candidate-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .candidate-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .candidate-table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .candidate-table input[type="checkbox"] {
            width: 16px;
            height: 16px;
            cursor: pointer;
        }
        
        /* 表单提示文字样式 */
        .form-hint {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
            line-height: 1.4;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .form-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .action-bar {
                flex-direction: column;
                gap: 12px;
            }
            
            .btn-group {
                width: 100%;
                justify-content: center;
            }
            
            .segment-search {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .segment-search {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="page-title">
                中标结果公示管理 - 新建/编辑
                <span class="help-icon" onclick="showHelp()" title="功能说明">?</span>
            </div>
            <div style="display: flex; align-items: center; gap: 16px;">
                <div class="breadcrumb">
                    首页 > 招标管理 > 中标结果公示管理 > 新建/编辑
                </div>
                <button class="back-btn" onclick="goBack()">返回列表</button>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 项目信息 -->
            <div class="form-section">
                <div class="section-header">项目信息</div>
                <div class="section-content">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label required">选择标段</label>
                            <div class="select-segment-btn" onclick="openSegmentModal()" id="segment-selector">
                                <span>+ 选择标段</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">计划项目编号</label>
                            <input type="text" class="form-input" id="project-number" readonly placeholder="由标段自动带出">
                        </div>
                        <div class="form-group">
                            <label class="form-label">所属计划项目名称</label>
                            <input type="text" class="form-input" id="project-name" readonly placeholder="由标段自动带出">
                        </div>
                    </div>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">采购方式</label>
                            <input type="text" class="form-input" id="procurement-method" readonly placeholder="由标段自动带出">
                        </div>
                        <div class="form-group">
                            <label class="form-label">项目业主</label>
                            <input type="text" class="form-input" id="project-owner" readonly placeholder="由标段自动带出">
                        </div>
                        <div class="form-group">
                            <label class="form-label">所属二级公司单位</label>
                            <input type="text" class="form-input" id="secondary-company" readonly placeholder="由标段自动带出">
                        </div>
                    </div>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">代理机构</label>
                            <input type="text" class="form-input" id="agency" readonly placeholder="由标段自动带出">
                        </div>
                        <div class="form-group">
                            <label class="form-label">立项决策文件</label>
                            <input type="text" class="form-input" id="decision-document" readonly placeholder="由标段自动带出">
                        </div>
                        <div class="form-group">
                            <!-- 空位保持布局对齐 -->
                        </div>
                    </div>
                    
                    <div class="form-grid full-width">
                        <div class="form-group">
                            <label class="form-label">项目基本情况（建设内容及规模）</label>
                            <textarea class="form-textarea" id="project-description" readonly placeholder="由标段自动带出"></textarea>
                        </div>
                    </div>
                    
                    <div class="form-grid full-width">
                        <div class="form-group">
                            <label class="form-label">备注</label>
                            <textarea class="form-textarea" id="remarks" readonly placeholder="由标段自动带出"></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 标段信息 -->
            <div class="form-section">
                <div class="section-header">标段信息</div>
                <div class="section-content">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">标段名称</label>
                            <input type="text" class="form-input" id="segment-name" readonly placeholder="由标段自动带出">
                        </div>
                        <div class="form-group">
                            <label class="form-label">标段编号</label>
                            <input type="text" class="form-input" id="segment-number" readonly placeholder="由标段自动带出">
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购金额（万元）</label>
                            <input type="number" class="form-input" id="procurement-amount" readonly placeholder="由标段自动带出">
                        </div>
                    </div>
                    
                    <div class="form-grid full-width">
                        <div class="form-group">
                            <label class="form-label">标段说明</label>
                            <textarea class="form-textarea" id="segment-description" readonly placeholder="由标段自动带出"></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中标信息 -->
            <div class="form-section">
                <div class="section-header">中标信息</div>
                <div class="section-content">
                    <div class="form-grid full-width">
                        <div class="form-group">
                            <label class="form-label required">公告标题</label>
                            <input type="text" class="form-input" id="announcement-title" placeholder="请输入公告标题" maxlength="255">
                            <div class="form-hint">项目业主全称+标段名称+公告类型</div>
                        </div>
                    </div>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label required">中选结果公示开始时间</label>
                            <input type="datetime-local" class="form-input" id="publicity-start-time">
                        </div>
                        <div class="form-group">
                            <label class="form-label required">中选结果公示结束时间</label>
                            <input type="datetime-local" class="form-input" id="publicity-end-time">
                        </div>
                        <div class="form-group">
                            <label class="form-label required">是否公示</label>
                            <select class="form-select" id="is-public">
                                <option value="">请选择</option>
                                <option value="yes">是</option>
                                <option value="no">否</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- 候选人信息表格 -->
                    <div class="form-grid full-width">
                        <div class="form-group">
                            <label class="form-label required">候选人信息</label>
                            <div class="candidate-info-section">
                                <div class="selected-winners" id="selected-winners" style="display: none;">
                                    <div class="winner-info">
                                        <strong>中标人信息：</strong>
                                        <span id="winner-names"></span>
                                    </div>
                                </div>
                                <div class="table-wrapper">
                                    <table class="candidate-table">
                                        <thead>
                                            <tr>
                                                <th width="50">选择</th>
                                                <th width="60">排序</th>
                                                <th width="200">投标单位全称</th>
                                                <th width="120">投标形式</th>
                                                <th width="120">投标报价（万元）</th>
                                                <th width="100">综合得分</th>
                                            </tr>
                                        </thead>
                                        <tbody id="candidate-tbody">
                                            <tr>
                                                <td><input type="checkbox" name="candidate" value="1" onchange="updateSelectedWinners()"></td>
                                                <td>1</td>
                                                <td>北京建设工程有限公司</td>
                                                <td>金额</td>
                                                <td>98.50</td>
                                                <td>95.2</td>
                                            </tr>
                                            <tr>
                                                <td><input type="checkbox" name="candidate" value="2" onchange="updateSelectedWinners()"></td>
                                                <td>2</td>
                                                <td>上海工程建设集团</td>
                                                <td>费率</td>
                                                <td>2.5%</td>
                                                <td>92.8</td>
                                            </tr>
                                            <tr>
                                                <td><input type="checkbox" name="candidate" value="3" onchange="updateSelectedWinners()"></td>
                                                <td>3</td>
                                                <td>广州建筑工程公司</td>
                                                <td>金额</td>
                                                <td>102.30</td>
                                                <td>89.5</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label required">成交金额</label>
                            <input type="number" class="form-input" id="transaction-amount" placeholder="请输入成交金额" step="0.01">
                        </div>
                        <div class="form-group">
                            <label class="form-label">业主代表姓名</label>
                            <input type="text" class="form-input" id="owner-representative" placeholder="请输入业主代表姓名">
                        </div>
                        <div class="form-group">
                            <!-- 空位保持布局对齐 -->
                        </div>
                    </div>
                    
                    <div class="form-grid full-width">
                        <div class="form-group">
                            <label class="form-label">中标情况说明</label>
                            <div class="rich-editor" id="winning-description-editor">
                                <div class="editor-toolbar">
                                    <button type="button" class="editor-btn" onclick="formatText('bold')"><b>B</b></button>
                                    <button type="button" class="editor-btn" onclick="formatText('italic')"><i>I</i></button>
                                    <button type="button" class="editor-btn" onclick="formatText('underline')"><u>U</u></button>
                                    <button type="button" class="editor-btn" onclick="formatText('insertOrderedList')">1.</button>
                                    <button type="button" class="editor-btn" onclick="formatText('insertUnorderedList')">•</button>
                                </div>
                                <div class="editor-content" contenteditable="true" id="winning-description-content" placeholder="请输入中标情况说明"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-grid full-width">
                        <div class="form-group">
                            <label class="form-label">异常情况说明</label>
                            <textarea class="form-textarea" id="exception-description" placeholder="如有异常填写异常说明，无异常不填" maxlength="255"></textarea>
                            <div class="form-hint">如有异常填写异常说明，无异常不填</div>
                        </div>
                    </div>
                    
                    <div class="form-grid full-width">
                        <div class="form-group">
                            <label class="form-label">审核依据</label>
                            <div class="file-upload-area">
                                <div class="upload-box">
                                    <div class="upload-icon">📎</div>
                                    <div class="upload-text">
                                        <p>点击上传或拖拽文件到此区域</p>
                                        <p class="upload-hint">支持 PDF、DOC、DOCX、XLS、XLSX、JPG、PNG 格式，单个文件不超过 50MB</p>
                                    </div>
                                    <input type="file" class="file-input" id="audit-basis-upload" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png" onchange="handleFileUpload(this)">
                                </div>
                                <div class="file-list" id="audit-basis-file-list">
                                    <!-- 已上传文件列表 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-bar">
            <div class="btn-group">
                <button class="btn btn-outline" onclick="goBack()">返回</button>
            </div>
            <div class="btn-group">
                <button class="btn btn-secondary" onclick="saveDraft()">保存草稿</button>
                <button class="btn btn-warning" onclick="preview()">预览</button>
                <button class="btn btn-primary" onclick="submitForApproval()">提交审核</button>
            </div>
        </div>
    </div>

    <!-- 标段选择弹窗 -->
    <div id="segment-modal" class="segment-modal">
        <div class="segment-modal-content">
            <div class="segment-modal-header">
                <h3 class="segment-modal-title">选择标段</h3>
                <button class="close-btn" onclick="closeSegmentModal()">&times;</button>
            </div>
            <div class="segment-modal-body">
                <!-- 查询区域 -->
                <div class="segment-search">
                    <div class="form-group">
                        <label class="form-label">标段名称</label>
                        <input type="text" class="form-input" placeholder="请输入标段名称">
                    </div>
                    <div class="form-group">
                        <label class="form-label">项目名称</label>
                        <input type="text" class="form-input" placeholder="请输入项目名称">
                    </div>
                    <div class="form-group">
                        <label class="form-label">采购方式</label>
                        <select class="form-select">
                            <option value="">全部方式</option>
                            <option value="public">公开招标</option>
                            <option value="invite">邀请招标</option>
                            <option value="inquiry">询价采购</option>
                        </select>
                    </div>
                    <div class="form-group" style="display: flex; align-items: end; gap: 8px;">
                        <button class="btn btn-primary" onclick="searchSegments()">查询</button>
                        <button class="btn btn-outline" onclick="resetSegmentSearch()">重置</button>
                    </div>
                </div>
                
                <!-- 列表区域 -->
                <div class="table-wrapper">
                    <table class="segment-table">
                        <thead>
                            <tr>
                                <th width="50">选择</th>
                                <th width="120">标段编号</th>
                                <th width="200">标段名称</th>
                                <th width="150">关联项目</th>
                                <th width="100">预算金额(万元)</th>
                                <th width="100">采购方式</th>
                                <th width="120">预计开标时间</th>
                                <th>标段说明</th>
                            </tr>
                        </thead>
                        <tbody id="segment-tbody">
                            <tr onclick="selectSegment(this, 'BD-2024-001')">
                                <td><input type="radio" name="segment" value="BD-2024-001"></td>
                                <td>BD-2024-001</td>
                                <td>办公桌椅采购</td>
                                <td>办公用品采购项目</td>
                                <td>25.00</td>
                                <td>公开招标</td>
                                <td>2024-01-25 14:30</td>
                                <td>采购办公桌椅，包括办公桌、办公椅、会议桌椅等</td>
                            </tr>
                            <tr onclick="selectSegment(this, 'BD-2024-002')">
                                <td><input type="radio" name="segment" value="BD-2024-002"></td>
                                <td>BD-2024-002</td>
                                <td>服务器采购</td>
                                <td>IT设备采购项目</td>
                                <td>120.00</td>
                                <td>公开招标</td>
                                <td>2024-01-28 09:00</td>
                                <td>采购服务器设备，包括应用服务器、数据库服务器等</td>
                            </tr>
                            <tr onclick="selectSegment(this, 'BD-2024-003')">
                                <td><input type="radio" name="segment" value="BD-2024-003"></td>
                                <td>BD-2024-003</td>
                                <td>商务车采购</td>
                                <td>车辆采购项目</td>
                                <td>80.00</td>
                                <td>邀请招标</td>
                                <td>2024-02-01 10:00</td>
                                <td>采购商务用车，满足日常公务出行需求</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页区域 -->
                <div class="pagination">
                    <div class="pagination-info">
                        显示第 1-3 条，共 3 条记录
                    </div>
                    <div class="pagination-controls">
                        <button class="page-btn" disabled>首页</button>
                        <button class="page-btn" disabled>上一页</button>
                        <button class="page-btn active">1</button>
                        <button class="page-btn" disabled>下一页</button>
                        <button class="page-btn" disabled>末页</button>
                    </div>
                </div>
            </div>
            <div class="segment-modal-footer">
                <button class="btn btn-outline" onclick="closeSegmentModal()">取消</button>
                <button class="btn btn-primary" onclick="confirmSegmentSelection()">确定</button>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="help-modal" class="help-modal">
        <div class="help-content">
            <div class="help-header">
                <h3 class="help-title">中标结果公示管理新建/编辑功能说明</h3>
                <button class="close-btn" onclick="hideHelp()">&times;</button>
            </div>
            <div class="help-body">
                <div class="help-section">
                    <h4>功能概述</h4>
                    <p>中标结果公示管理新建/编辑页面用于创建或修改招标采购公告，支持富文本编辑和标段关联。</p>
                </div>
                
                <div class="help-section">
                    <h4>操作流程</h4>
                    <ul class="help-list">
                        <li><strong>选择标段：</strong>首先需要选择关联的标段，系统会自动填充相关信息</li>
                        <li><strong>完善信息：</strong>根据实际情况完善各个模块的详细信息</li>
                        <li><strong>保存草稿：</strong>可以随时保存为草稿，便于后续继续编辑</li>
                        <li><strong>预览确认：</strong>提交前可以预览公告效果</li>
                        <li><strong>提交审核：</strong>信息确认无误后提交审核</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>填写说明</h4>
                    <ul class="help-list">
                        <li><strong>基本信息：</strong>关联标段为必填项，其他信息会根据标段自动填充</li>
                        <li><strong>资格要求：</strong>支持富文本编辑，可以添加列表、表格等格式</li>
                        <li><strong>时间设置：</strong>注意各个时间节点的逻辑关系</li>
                        <li><strong>联系信息：</strong>确保联系方式准确有效</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>注意事项</h4>
                    <ul class="help-list">
                        <li>标有红色星号(*)的字段为必填项</li>
                        <li>富文本编辑器支持常用格式，建议保持格式简洁</li>
                        <li>时间设置要符合采购流程的逻辑顺序</li>
                        <li>保存草稿后可以继续编辑，提交审核后需要审核通过才能发布</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedSegmentData = null;

        // 富文本编辑器功能
        function formatText(command) {
            document.execCommand(command, false, null);
        }

        function insertTable() {
            const table = '<table border="1" style="border-collapse: collapse; width: 100%; margin: 10px 0;"><tr><th>列1</th><th>列2</th><th>列3</th></tr><tr><td>内容1</td><td>内容2</td><td>内容3</td></tr></table>';
            document.execCommand('insertHTML', false, table);
        }

        // 标段选择弹窗
        function openSegmentModal() {
            document.getElementById('segment-modal').style.display = 'block';
        }

        function closeSegmentModal() {
            document.getElementById('segment-modal').style.display = 'none';
        }

        function selectSegment(row, segmentId) {
            // 清除之前的选择
            const rows = document.querySelectorAll('#segment-tbody tr');
            rows.forEach(r => r.classList.remove('selected'));
            
            // 选中当前行
            row.classList.add('selected');
            row.querySelector('input[type="radio"]').checked = true;
            
            // 保存选中的标段数据
            selectedSegmentData = {
                id: segmentId,
                name: row.cells[2].textContent,
                project: row.cells[3].textContent,
                budget: row.cells[4].textContent,
                method: row.cells[5].textContent,
                openTime: row.cells[6].textContent,
                description: row.cells[7].textContent
            };
        }

        function confirmSegmentSelection() {
            if (!selectedSegmentData) {
                alert('请选择一个标段');
                return;
            }
            
            // 填充表单数据
            const selector = document.getElementById('segment-selector');
            selector.innerHTML = `<span>${selectedSegmentData.name}</span>`;
            selector.classList.add('selected-segment');
            
            // 自动填充相关字段
            document.getElementById('project-name').value = selectedSegmentData.project;
            document.getElementById('project-owner').value = 'XX有限公司'; // 模拟数据
            document.getElementById('budget-amount').value = selectedSegmentData.budget;
            document.getElementById('project-description').value = selectedSegmentData.description;
            
            // 自动生成公告名称
            const announcementName = `${selectedSegmentData.project}${selectedSegmentData.name}招标公告`;
            document.getElementById('announcement-name').value = announcementName;
            
            closeSegmentModal();
        }

        function searchSegments() {
            console.log('搜索标段');
            // 实现搜索逻辑
        }

        function resetSegmentSearch() {
            const form = document.querySelector('.segment-search');
            const inputs = form.querySelectorAll('input, select');
            inputs.forEach(input => {
                if (input.type === 'text' || input.tagName === 'SELECT') {
                    input.value = '';
                }
            });
        }

        // 点击弹窗外部关闭
        document.getElementById('segment-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeSegmentModal();
            }
        });

        // 帮助弹窗
        function showHelp() {
            document.getElementById('help-modal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('help-modal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });

        // 文件上传处理
        function handleFileUpload(input) {
            const files = Array.from(input.files);
            const fileList = document.getElementById('file-list');
            
            files.forEach(file => {
                // 检查文件大小
                if (file.size > 50 * 1024 * 1024) {
                    alert(`文件 ${file.name} 超过50MB限制`);
                    return;
                }
                
                // 检查文件类型
                const allowedTypes = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.jpg', '.jpeg', '.png'];
                const fileExt = '.' + file.name.split('.').pop().toLowerCase();
                if (!allowedTypes.includes(fileExt)) {
                    alert(`文件 ${file.name} 格式不支持`);
                    return;
                }
                
                // 创建文件项
                const fileItem = createFileItem(file);
                fileList.appendChild(fileItem);
            });
            
            // 清空input
            input.value = '';
        }
        
        function createFileItem(file) {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            
            const fileSize = formatFileSize(file.size);
            const fileIcon = getFileIcon(file.name);
            
            fileItem.innerHTML = `
                <div class="file-info">
                    <span class="file-icon">${fileIcon}</span>
                    <span class="file-name">${file.name}</span>
                    <span class="file-size">${fileSize}</span>
                </div>
                <div class="file-actions">
                    <button class="file-action-btn file-download" onclick="downloadFile(this)" title="下载">下载</button>
                    <button class="file-action-btn file-delete" onclick="deleteFile(this)" title="删除">删除</button>
                </div>
            `;
            
            return fileItem;
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function getFileIcon(fileName) {
            const ext = fileName.split('.').pop().toLowerCase();
            const iconMap = {
                'pdf': '📄',
                'doc': '📝',
                'docx': '📝',
                'xls': '📊',
                'xlsx': '📊',
                'jpg': '🖼️',
                'jpeg': '🖼️',
                'png': '🖼️'
            };
            return iconMap[ext] || '📎';
        }
        
        function downloadFile(btn) {
            const fileName = btn.closest('.file-item').querySelector('.file-name').textContent;
            console.log('下载文件:', fileName);
            // 实现文件下载逻辑
        }
        
        function deleteFile(btn) {
            if (confirm('确定要删除这个文件吗？')) {
                btn.closest('.file-item').remove();
            }
        }

        // 候选人信息表格交互
        function updateSelectedWinners() {
            const checkboxes = document.querySelectorAll('.candidate-table input[type="checkbox"]:checked');
            const selectedWinners = [];
            let totalAmount = 0;
            let maxRate = 0;
            
            checkboxes.forEach(checkbox => {
                const row = checkbox.closest('tr');
                const company = row.cells[2].textContent;
                const bidType = row.cells[3].textContent;
                const bidPrice = parseFloat(row.cells[4].textContent.replace(/[^\d.]/g, ''));
                
                selectedWinners.push(company);
                
                // 计算成交金额
                if (bidType.includes('金额')) {
                    totalAmount += bidPrice;
                } else if (bidType.includes('费率')) {
                    maxRate = Math.max(maxRate, bidPrice);
                }
            });
            
            // 更新中标人信息显示
            const winnerInfo = document.querySelector('.winner-info');
            if (selectedWinners.length > 0) {
                winnerInfo.textContent = `中标人：${selectedWinners.join('、')}`;
                
                // 更新成交金额
                const amountInput = document.getElementById('deal-amount');
                if (totalAmount > 0) {
                    amountInput.value = totalAmount.toFixed(2);
                } else if (maxRate > 0) {
                    amountInput.value = maxRate.toFixed(2) + '%';
                }
            } else {
                winnerInfo.textContent = '请选择中标候选人';
                document.getElementById('deal-amount').value = '';
            }
        }
        
        // 初始化候选人表格事件
        function initCandidateTable() {
            const checkboxes = document.querySelectorAll('.candidate-table input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateSelectedWinners);
            });
        }

        // 业务操作函数
        function goBack() {
            if (confirm('确定要返回吗？未保存的数据将丢失。')) {
                window.history.back();
            }
        }

        function saveDraft() {
            console.log('保存草稿');
            alert('草稿保存成功！');
        }

        function preview() {
            console.log('预览公告');
            // 打开预览窗口或页面
        }

        function submitForApproval() {
            // 验证必填字段
            const requiredFields = [
                { id: 'announcement-title', name: '公告标题' },
                { id: 'publicity-start-time', name: '中选结果公示开始时间' },
                { id: 'publicity-end-time', name: '中选结果公示结束时间' },
                { id: 'is-publicity', name: '是否公示' },
                { id: 'deal-amount', name: '成交金额' },
                { id: 'owner-representative', name: '业主代表姓名' }
            ];
            
            for (let field of requiredFields) {
                const element = document.getElementById(field.id);
                if (!element || !element.value.trim()) {
                    alert(`请填写${field.name}`);
                    if (element) element.focus();
                    return;
                }
            }
            
            // 验证是否选择了标段
            const segmentSelector = document.getElementById('segment-selector');
            if (!segmentSelector.classList.contains('selected-segment')) {
                alert('请选择标段');
                return;
            }
            
            // 验证是否选择了候选人
            const selectedCandidates = document.querySelectorAll('.candidate-table input[type="checkbox"]:checked');
            if (selectedCandidates.length === 0) {
                alert('请选择中标候选人');
                return;
            }
            
            if (confirm('确定要提交审核吗？提交后将无法修改。')) {
                console.log('提交审核');
                alert('提交成功！');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('中标结果公示管理新建/编辑页面加载完成');
            
            // 设置默认时间
            const now = new Date();
            const fiveDaysLater = new Date(now.getTime() + 5 * 24 * 60 * 60 * 1000);
            
            // 格式化时间为 datetime-local 格式
            const formatDateTime = (date) => {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                return `${year}-${month}-${day}T${hours}:${minutes}`;
            };
            
            // 设置中标信息的默认时间
            document.getElementById('publicity-start-time').value = formatDateTime(now);
            document.getElementById('publicity-end-time').value = formatDateTime(fiveDaysLater);
            
            // 初始化候选人表格事件
            initCandidateTable();
            
            // 根据采购金额判断是否公示（示例逻辑）
            const isPublicitySelect = document.getElementById('is-publicity');
            // 这里可以根据实际的配置数值来判断
            isPublicitySelect.value = '是'; // 默认设置为是
        });
    </script>
</body>
</html>