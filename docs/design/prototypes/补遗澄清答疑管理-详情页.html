<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>补遗/澄清/答疑管理 - 详情 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #fff;
            border-bottom: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
        }

        .help-icon {
            margin-left: 8px;
            width: 16px;
            height: 16px;
            background: #6b7280;
            border-radius: 50%;
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .breadcrumb {
            color: #6b7280;
            font-size: 14px;
        }

        .back-btn {
            background: #6b7280;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
        }

        .back-btn:hover {
            background: #4b5563;
        }

        /* 内容区域 */
        .content-area {
            padding: 24px;
        }

        /* 详情页签样式 */
        .detail-tabs {
            display: flex;
            border-bottom: 1px solid #e6e8eb;
            margin-bottom: 24px;
        }

        .detail-tab {
            padding: 12px 24px;
            cursor: pointer;
            color: #6b7280;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .detail-tab.active {
            color: #2563eb;
            border-bottom-color: #2563eb;
        }

        .detail-tab:hover {
            color: #2563eb;
        }

        /* 详情内容 */
        .detail-content {
            display: none;
        }

        .detail-content.active {
            display: block;
        }

        /* 详情区域 */
        .detail-section {
            background: white;
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .section-header {
            background: #f8fafc;
            border-bottom: 1px solid #e6e8eb;
            padding: 15px 20px;
            font-weight: 600;
            color: #1f2937;
            font-size: 14px;
            position: relative;
        }

        .section-header::after {
            content: '';
            position: absolute;
            left: 20px;
            bottom: 0;
            width: 60px;
            height: 2px;
            background: #2563eb;
        }

        .section-content {
            padding: 20px;
        }

        /* 信息展示 */
        .info-section {
            margin-bottom: 32px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #1890ff;
            position: relative;
        }
        
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            bottom: -2px;
            width: 40px;
            height: 2px;
            background: #1890ff;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .info-grid.full-width {
            grid-template-columns: 1fr;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .info-label {
            font-size: 14px;
            color: #8c8c8c;
            font-weight: 500;
        }
        
        .info-value {
            font-size: 14px;
            color: #262626;
            min-height: 20px;
        }
        
        .info-value.empty {
            color: #d9d9d9;
        }
        
        .info-value.description {
            line-height: 1.6;
            background: #fafafa;
            padding: 12px;
            border-radius: 4px;
            border: 1px solid #e8eaec;
        }

        .detail-value.rich-content p {
            margin-bottom: 8px;
        }

        .detail-value.rich-content ul, .detail-value.rich-content ol {
            margin-left: 20px;
            margin-bottom: 8px;
        }

        .detail-value.rich-content table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 8px;
        }

        .detail-value.rich-content table td, .detail-value.rich-content table th {
            border: 1px solid #d1d5db;
            padding: 8px;
            text-align: left;
        }

        .detail-value.rich-content table th {
            background: #f3f4f6;
            font-weight: 600;
        }

        /* 状态标签 */
        .status-tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
        }

        .status-published {
            background: #dcfce7;
            color: #166534;
        }

        .status-approved {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-draft {
            background: #f3f4f6;
            color: #374151;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        /* 状态标签 */
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-draft {
            background-color: #f5f5f5;
            color: #8c8c8c;
        }
        
        .status-pending {
            background-color: #e6f7ff;
            color: #1890ff;
        }
        
        .status-approved {
            background-color: #f6ffed;
            color: #52c41a;
        }
        
        .status-rejected {
            background-color: #fff1f0;
            color: #ff4d4f;
        }

        /* 分组列表 */
        .section-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .section-item {
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            overflow: hidden;
        }

        .section-item-header {
            background: #f8fafc;
            padding: 12px 16px;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e6e8eb;
        }

        .section-item-content {
            padding: 16px;
        }

        /* 附件列表 */
        .attachment-list {
            display: grid;
            gap: 12px;
        }
        
        .attachment-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #e8eaec;
            border-radius: 4px;
            background: #fafafa;
        }
        
        .attachment-icon {
            width: 32px;
            height: 32px;
            background: #1890ff;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            margin-right: 12px;
        }
        
        .attachment-info {
            flex: 1;
        }
        
        .attachment-name {
            font-size: 14px;
            color: #262626;
            margin-bottom: 4px;
        }
        
        .attachment-meta {
            font-size: 12px;
            color: #8c8c8c;
        }
        
        .attachment-actions {
            display: flex;
            gap: 8px;
        }
        
        .attachment-btn {
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            color: #1890ff;
            text-decoration: none;
        }
        
        .attachment-btn:hover {
            border-color: #1890ff;
            background: #e6f7ff;
        }

        .btn-link {
            padding: 4px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
            text-decoration: none;
            color: #262626;
        }

        .btn-link:hover {
            border-color: #1890ff;
            color: #1890ff;
            text-decoration: none;
        }

        /* 操作记录时间线样式 */
        .operation-timeline {
            position: relative;
            padding-left: 20px;
        }

        .operation-timeline::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
            padding-left: 25px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -4px;
            top: 8px;
            width: 8px;
            height: 8px;
            background: #007bff;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 0 0 2px #e9ecef;
        }

        .timeline-content {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px 15px;
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .timeline-action {
            font-weight: 600;
            color: #495057;
            font-size: 14px;
        }

        .timeline-time {
            color: #6c757d;
            font-size: 12px;
        }

        .timeline-user {
            color: #6c757d;
            font-size: 12px;
            margin-bottom: 6px;
        }

        .timeline-desc {
            color: #495057;
            font-size: 13px;
            line-height: 1.4;
        }

        /* 操作记录表格 */
        .record-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .record-table th {
            background: #f8fafc;
            border: 1px solid #e6e8eb;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            color: #374151;
        }

        .record-table td {
            border: 1px solid #e6e8eb;
            padding: 12px 8px;
            color: #1f2937;
        }

        .record-table tr:nth-child(even) {
            background: #f9fafb;
        }

        .record-table tr:hover {
            background: #f3f4f6;
        }

        /* 操作按钮 */
        .action-bar {
            background: #f8fafc;
            border-top: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            bottom: 0;
        }

        .btn-group {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #2563eb;
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
        }

        .btn-success {
            background: #059669;
            color: white;
        }

        .btn-success:hover {
            background: #047857;
        }

        .btn-warning {
            background: #d97706;
            color: white;
        }

        .btn-warning:hover {
            background: #b45309;
        }

        .btn-danger {
            background: #dc2626;
            color: white;
        }

        .btn-danger:hover {
            background: #b91c1c;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-outline {
            background: white;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            padding: 24px;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #e6e8eb;
        }

        .help-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #6b7280;
        }

        .help-body {
            color: #374151;
            line-height: 1.6;
        }

        .help-section {
            margin-bottom: 16px;
        }

        .help-section h4 {
            color: #1f2937;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .help-section p {
            margin-bottom: 8px;
            font-size: 13px;
        }

        .help-list {
            list-style: none;
            padding-left: 16px;
        }

        .help-list li {
            margin-bottom: 4px;
            font-size: 13px;
            position: relative;
        }

        .help-list li::before {
            content: '•';
            color: #2563eb;
            position: absolute;
            left: -12px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .detail-grid {
                grid-template-columns: 1fr;
            }
            
            .action-bar {
                flex-direction: column;
                gap: 12px;
            }
            
            .btn-group {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">


        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 详情页签 -->
            <div class="detail-tabs">
                <div class="detail-tab active" data-tab="announcement">公告信息</div>
                <div class="detail-tab" data-tab="project">项目信息</div>
                <div class="detail-tab" data-tab="operation">流程记录</div>
            </div>
            
            <!-- 公告信息 -->
            <div class="detail-content active" id="announcement">
                <!-- 公告信息 -->
                <div class="info-section">
                    <div class="section-title">公告信息</div>
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">公告标题</div>
                            <div class="info-value">办公楼装修工程补遗公告</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">公告阶段</div>
                            <div class="info-value">招标阶段</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">公告状态</div>
                            <div class="info-value"><span class="status-badge status-approved">已发布</span></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">是否公示</div>
                            <div class="info-value">是</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">类型</div>
                            <div class="info-value">补遗</div>
                        </div>
                    </div>
                    
                    <div class="info-grid full-width">
                        <div class="info-item">
                            <div class="info-label">补遗/澄清/答疑说明</div>
                            <div class="info-value description">
                                根据投标人咨询，现对招标文件进行如下补遗：<br>
                                1. 施工工期调整为90个工作日<br>
                                2. 增加环保要求：施工过程中需使用环保材料<br>
                                3. 安全要求：施工期间需配备专职安全员<br>
                                4. 质量标准：装修质量需达到国家相关标准
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">审核依据</div>
                            <div class="info-value">根据《招标投标法》及相关规定，结合项目实际情况进行补遗</div>
                        </div>
                    </div>
                </div>
                
                <!-- 文件信息 -->
                <div class="info-section">
                    <div class="section-title">文件信息</div>
                    <div class="attachment-list">
                        <div class="attachment-item">
                            <div class="attachment-icon">PDF</div>
                            <div class="attachment-info">
                                <div class="attachment-name">补遗公告文件.pdf</div>
                                <div class="attachment-meta">1.5MB · 上传于 2024-03-15 · 张三</div>
                            </div>
                            <div class="attachment-actions">
                                <a href="javascript:void(0)" class="attachment-btn">预览</a>
                                <a href="javascript:void(0)" class="attachment-btn">下载</a>
                            </div>
                        </div>
                        <div class="attachment-item">
                            <div class="attachment-icon">DOC</div>
                            <div class="attachment-info">
                                <div class="attachment-name">审核依据文件.docx</div>
                                <div class="attachment-meta">800KB · 上传于 2024-03-15 · 张三</div>
                            </div>
                            <div class="attachment-actions">
                                <a href="javascript:void(0)" class="attachment-btn">预览</a>
                                <a href="javascript:void(0)" class="attachment-btn">下载</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 项目信息 -->
            <div class="detail-content" id="project">
                <!-- 项目信息 -->
                 <div class="info-section">
                     <div class="section-title">项目信息</div>
                     <div class="info-grid">
                         <div class="info-item">
                             <div class="info-label">计划项目编号</div>
                             <div class="info-value">CG-2024-001</div>
                         </div>
                         <div class="info-item">
                             <div class="info-label">计划项目名称</div>
                             <div class="info-value">办公楼装修工程采购计划</div>
                         </div>
                         <div class="info-item">
                             <div class="info-label">项目类型</div>
                             <div class="info-value">依法必须招标项目</div>
                         </div>
                         <div class="info-item">
                             <div class="info-label">项目业主</div>
                             <div class="info-value">某建设集团有限公司</div>
                         </div>
                         <div class="info-item">
                             <div class="info-label">采购类型</div>
                             <div class="info-value">施工</div>
                         </div>
                         <div class="info-item">
                             <div class="info-label">采购方式</div>
                             <div class="info-value">公告比选</div>
                         </div>
                         <div class="info-item">
                             <div class="info-label">招标类别</div>
                             <div class="info-value">监理招标</div>
                         </div>
                         <div class="info-item">
                             <div class="info-label">采购组织方式</div>
                             <div class="info-value">委托招标</div>
                         </div>
                         <div class="info-item">
                             <div class="info-label">所属二级公司单位</div>
                             <div class="info-value">建设工程分公司</div>
                         </div>
                         <div class="info-item">
                             <div class="info-label">代理机构</div>
                             <div class="info-value">某招标代理公司</div>
                         </div>
                     </div>
                     
                     <div class="info-grid full-width">
                         <div class="info-item">
                             <div class="info-label">备注</div>
                             <div class="info-value">项目需在2024年4月底前完成，确保不影响正常办公。施工期间需做好安全防护措施。</div>
                         </div>
                     </div>
                 </div>
                 
                 <!-- 附件 -->
                 <div class="info-section">
                     <div class="section-title">附件</div>
                     <div class="attachment-list">
                         <div class="attachment-item">
                             <div class="attachment-icon">PDF</div>
                             <div class="attachment-info">
                                 <div class="attachment-name">办公楼装修工程立项批复.pdf</div>
                                 <div class="attachment-meta">1.2MB · 上传于 2024-01-10 · 张三</div>
                             </div>
                             <div class="attachment-actions">
                                 <a href="javascript:void(0)" class="attachment-btn">预览</a>
                                 <a href="javascript:void(0)" class="attachment-btn">下载</a>
                             </div>
                         </div>
                     </div>
                 </div>
                 
                 <!-- 标段信息 -->
                 <div class="info-section">
                     <div class="section-title">标段信息</div>
                     <div class="section-list">
                         <div class="section-item">
                             <div class="section-item-header">
                                 <div style="display: flex; justify-content: space-between; align-items: center;">
                                     <span>标段一：主体装修工程</span>
                                     <a href="项目标段管理-标段详情页.html" class="btn-link">查看详情</a>
                                 </div>
                             </div>
                             <div class="section-item-content">
                                 <div class="info-grid">
                                     <div class="info-item">
                                         <div class="info-label">标段编号</div>
                                         <div class="info-value">BD-2024-001-01</div>
                                     </div>
                                     <div class="info-item">
                                         <div class="info-label">标段名称</div>
                                         <div class="info-value">主体装修工程</div>
                                     </div>
                                     <div class="info-item">
                                         <div class="info-label">标段阶段</div>
                                         <div class="info-value">招标阶段</div>
                                     </div>
                                     <div class="info-item">
                                         <div class="info-label">审核状态</div>
                                         <div class="info-value"><span class="status-badge status-approved">审核通过</span></div>
                                     </div>
                                     <div class="info-item">
                                         <div class="info-label">采购金额（万元）</div>
                                         <div class="info-value">800.00</div>
                                     </div>
                                 </div>
                                 <div class="info-grid full-width">
                                     <div class="info-item">
                                         <div class="info-label">标段说明</div>
                                         <div class="info-value">包括办公区域、会议室、公共区域的装修改造工程</div>
                                     </div>
                                 </div>
                             </div>
                         </div>
                         
                         <div class="section-item">
                             <div class="section-item-header">
                                 <div style="display: flex; justify-content: space-between; align-items: center;">
                                     <span>标段二：配套设施工程</span>
                                     <a href="项目标段管理-标段详情页.html" class="btn-link">查看详情</a>
                                 </div>
                             </div>
                             <div class="section-item-content">
                                 <div class="info-grid">
                                     <div class="info-item">
                                         <div class="info-label">标段编号</div>
                                         <div class="info-value">BD-2024-001-02</div>
                                     </div>
                                     <div class="info-item">
                                         <div class="info-label">标段名称</div>
                                         <div class="info-value">配套设施工程</div>
                                     </div>
                                     <div class="info-item">
                                         <div class="info-label">标段阶段</div>
                                         <div class="info-value">招标阶段</div>
                                     </div>
                                     <div class="info-item">
                                         <div class="info-label">审核状态</div>
                                         <div class="info-value"><span class="status-badge status-pending">待审核</span></div>
                                     </div>
                                     <div class="info-item">
                                         <div class="info-label">采购金额（万元）</div>
                                         <div class="info-value">400.00</div>
                                     </div>
                                 </div>
                                 <div class="info-grid full-width">
                                     <div class="info-item">
                                         <div class="info-label">标段说明</div>
                                         <div class="info-value">包括消防系统、安防系统、网络布线等配套设施工程</div>
                                     </div>
                                 </div>
                             </div>
                         </div>
                     </div>
                 </div>
             </div>
             
             <!-- 流程记录 -->
             <div class="detail-content" id="operation">
                 <div class="info-section">
                     <div class="section-title">操作记录</div>
                     <div class="operation-timeline">
                         <div class="timeline-item">
                             <div class="timeline-dot"></div>
                             <div class="timeline-content">
                                 <div class="timeline-header">
                                     <span class="timeline-title">创建补遗公告</span>
                                     <span class="timeline-time">2024-01-15 10:30:00</span>
                                 </div>
                                 <div class="timeline-body">
                                     <p>操作人：张三</p>
                                     <p>操作内容：创建办公设备采购技术规格补遗公告</p>
                                 </div>
                             </div>
                         </div>
                         
                         <div class="timeline-item">
                             <div class="timeline-dot"></div>
                             <div class="timeline-content">
                                 <div class="timeline-header">
                                     <span class="timeline-title">提交审核</span>
                                     <span class="timeline-time">2024-01-15 11:00:00</span>
                                 </div>
                                 <div class="timeline-body">
                                     <p>操作人：张三</p>
                                     <p>操作内容：提交补遗公告至审核流程</p>
                                 </div>
                             </div>
                         </div>
                         
                         <div class="timeline-item">
                             <div class="timeline-dot"></div>
                             <div class="timeline-content">
                                 <div class="timeline-header">
                                     <span class="timeline-title">审核通过</span>
                                     <span class="timeline-time">2024-01-15 14:30:00</span>
                                 </div>
                                 <div class="timeline-body">
                                     <p>操作人：李四</p>
                                     <p>操作内容：审核通过，补遗公告符合要求</p>
                                     <p>审核意见：技术规格补遗内容完整，可以发布</p>
                                 </div>
                             </div>
                         </div>
                         
                         <div class="timeline-item">
                             <div class="timeline-dot active"></div>
                             <div class="timeline-content">
                                 <div class="timeline-header">
                                     <span class="timeline-title">发布公告</span>
                                     <span class="timeline-time">2024-01-15 15:00:00</span>
                                 </div>
                                 <div class="timeline-body">
                                     <p>操作人：系统自动</p>
                                     <p>操作内容：补遗公告已发布至招标平台</p>
                                     <p>状态：已公示</p>
                                 </div>
                             </div>
                         </div>
                     </div>
                </div>

            </div>
            
            <!-- 项目信息页签 -->
            <div class="detail-content" id="project">
                <!-- 项目信息 -->
                <div class="detail-section">
                    <div class="section-header">项目信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">项目名称</div>
                                <div class="detail-value">办公设备采购项目</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">项目标题</div>
                                <div class="detail-value">办公设备采购项目招标</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">项目业主</div>
                                <div class="detail-value">XX集团有限公司</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">采购方式</div>
                                <div class="detail-value">公开招标</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购类别</div>
                                <div class="detail-value">货物采购</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">所属二级公司单位</div>
                                <div class="detail-value">XX分公司</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">立项决策日期</div>
                                <div class="detail-value">2024-02-15</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">创建时间</div>
                                <div class="detail-value">2024-01-15 10:30:00</div>
                            </div>
                            <div class="detail-item">
                                <!-- 空位保持布局对齐 -->
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">项目基本情况</div>
                                <div class="detail-value">采购办公桌椅、文具用品、电脑设备等日常办公用品，满足公司日常办公需求。</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 关联标段信息 -->
                <div class="detail-section">
                    <div class="section-header">关联标段信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">标段编号</div>
                                <div class="detail-value">BD-2024-003-01</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">标段名称</div>
                                <div class="detail-value">办公设备采购第一标段</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">是否公示</div>
                                <div class="detail-value"><span class="status-badge status-approved">是</span></div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">标段阶段</div>
                                <div class="detail-value">评标阶段</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">标段状态</div>
                                <div class="detail-value"><span class="status-badge status-in-progress">进行中</span></div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购金额</div>
                                <div class="detail-value">50万元</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">标段说明</div>
                                <div class="detail-value">包含办公桌椅、文具用品等基础办公设备的采购。</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文件信息 -->
                <div class="detail-section">
                    <div class="section-header">文件信息</div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">招标文件</div>
                                <div class="detail-value">
                                    <div class="file-list">
                                        <div class="file-item">
                                            <span class="file-name">招标文件.pdf</span>
                                            <a href="#" class="file-download">下载</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">技术文件</div>
                                <div class="detail-value">
                                    <div class="file-list">
                                        <div class="file-item">
                                            <span class="file-name">技术规格书.docx</span>
                                            <a href="#" class="file-download">下载</a>
                                        </div>
                                        <div class="file-item">
                                            <span class="file-name">技术要求说明.pdf</span>
                                            <a href="#" class="file-download">下载</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 流程记录 -->
            <div class="detail-content" id="operation">
                <div class="section-content">
                    <div class="info-section">
                        <div class="section-title">操作记录</div>
                        <div class="operation-timeline">
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">发布</div>
                                        <div class="timeline-time">2024-03-16 14:00:15</div>
                                    </div>
                                    <div class="timeline-user">操作人：王五</div>
                                    <div class="timeline-desc">
                                        补遗/澄清/答疑正式发布，通知所有相关投标人。状态变更：审核通过 → 已发布
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">审核通过</div>
                                        <div class="timeline-time">2024-03-16 10:45:33</div>
                                    </div>
                                    <div class="timeline-user">操作人：李四（部门经理）</div>
                                    <div class="timeline-desc">
                                        审核通过，补遗内容符合要求。状态变更：待审核 → 审核通过
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">部门审批</div>
                                        <div class="timeline-time">2024-03-16 09:30:25</div>
                                    </div>
                                    <div class="timeline-user">操作人：王经理（采购部）</div>
                                    <div class="timeline-desc">
                                        部门审批通过，同意发布补遗。状态变更：待审批 → 待审核
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">提交审批</div>
                                        <div class="timeline-time">2024-03-15 16:20:18</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三（采购专员）</div>
                                    <div class="timeline-desc">
                                        提交部门审批，等待部门领导审批。状态变更：草稿 → 待审批
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">编辑</div>
                                        <div class="timeline-time">2024-03-15 14:15:42</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三</div>
                                    <div class="timeline-desc">
                                        完善补遗内容和相关文件。状态变更：草稿 → 草稿
                                    </div>
                                </div>
                            </div>
                            
                            <div class="timeline-item">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-action">创建</div>
                                        <div class="timeline-time">2024-03-15 10:30:25</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三</div>
                                    <div class="timeline-desc">
                                        创建补遗/澄清/答疑，关联标段：办公桌椅采购标段。状态变更：无 → 草稿
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-bar">
            <div class="btn-group">
                <button class="btn btn-outline" onclick="goBack()">返回列表</button>
                <button class="btn btn-secondary" onclick="printClarification()">打印</button>
                <button class="btn btn-success" onclick="exportClarification()">导出</button>
            </div>
            <div class="btn-group">
                <button class="btn btn-primary" onclick="editClarification()">编辑</button>
                <button class="btn btn-warning" onclick="withdrawClarification()">撤回</button>
                <button class="btn btn-danger" onclick="deleteClarification()">删除</button>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="help-modal" class="help-modal">
        <div class="help-content">
            <div class="help-header">
                <h3 class="help-title">补遗/澄清/答疑详情功能说明</h3>
                <button class="close-btn" onclick="hideHelp()">&times;</button>
            </div>
            <div class="help-body">
                <div class="help-section">
                    <h4>功能概述</h4>
                    <p>补遗/澄清/答疑详情页面用于查看补遗、澄清或答疑的完整信息，包括基本信息、项目信息和操作记录。</p>
                </div>
                
                <div class="help-section">
                    <h4>页签说明</h4>
                    <ul class="help-list">
                        <li><strong>基本信息：</strong>显示补遗/澄清/答疑的详细内容，包括标题、类型、内容、影响范围等</li>
                        <li><strong>项目信息：</strong>显示关联项目和标段的详细信息</li>
                        <li><strong>操作记录：</strong>显示从创建到发布的全部操作历史</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>操作说明</h4>
                    <ul class="help-list">
                        <li><strong>编辑：</strong>修改补遗/澄清/答疑内容（仅草稿状态可编辑）</li>
                        <li><strong>撤回：</strong>撤回已发布的内容（需要相应权限）</li>
                        <li><strong>删除：</strong>删除内容（仅草稿状态可删除）</li>
                        <li><strong>打印：</strong>打印补遗/澄清/答疑内容</li>
                        <li><strong>导出：</strong>导出为PDF或Word文档</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>类型说明</h4>
                    <ul class="help-list">
                        <li><strong>补遗：</strong>对招标文件的补充说明或修正</li>
                        <li><strong>澄清：</strong>对招标文件中不明确内容的澄清说明</li>
                        <li><strong>答疑：</strong>对投标人疑问的回复和解答</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>状态说明</h4>
                    <ul class="help-list">
                        <li><strong>草稿：</strong>内容尚未提交，可以编辑</li>
                        <li><strong>待审核：</strong>内容已提交，等待审核</li>
                        <li><strong>审核中：</strong>内容正在审核过程中</li>
                        <li><strong>已通过：</strong>内容审核通过，可以发布</li>
                        <li><strong>未通过：</strong>内容审核未通过，需要修改</li>
                        <li><strong>已发布：</strong>内容已发布，投标人可以查看</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>注意事项</h4>
                    <ul class="help-list">
                        <li>已发布的内容撤回需要相应权限</li>
                        <li>操作记录不可修改，系统自动记录</li>
                        <li>内容的修改会影响投标人的投标准备</li>
                        <li>重要操作建议先预览确认后再执行</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页签切换
        document.querySelectorAll('.detail-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                const targetTab = this.getAttribute('data-tab');
                
                // 移除所有页签的激活状态
                document.querySelectorAll('.detail-tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.detail-content').forEach(c => c.classList.remove('active'));
                
                // 激活当前页签和对应内容
                this.classList.add('active');
                document.getElementById(targetTab).classList.add('active');
            });
        });

        // 帮助弹窗
        function showHelp() {
            document.getElementById('help-modal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('help-modal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });

        // 业务操作函数
        function goBack() {
            window.history.back();
        }

        function editClarification() {
            // 检查状态是否允许编辑
            const status = '已发布'; // 这里应该从页面获取实际状态
            if (status === '已发布') {
                alert('已发布的补遗/澄清/答疑不能直接编辑，请先撤回后再编辑。');
                return;
            }
            
            console.log('编辑补遗/澄清/答疑');
            // 跳转到编辑页面
        }

        function withdrawClarification() {
            if (confirm('确定要撤回此补遗/澄清/答疑吗？撤回后将不再对外展示。')) {
                console.log('撤回补遗/澄清/答疑');
                alert('补遗/澄清/答疑撤回成功！');
            }
        }

        function deleteClarification() {
            // 检查状态是否允许删除
            const status = '已发布'; // 这里应该从页面获取实际状态
            if (status !== '草稿') {
                alert('只有草稿状态的补遗/澄清/答疑才能删除。');
                return;
            }
            
            if (confirm('确定要删除此补遗/澄清/答疑吗？删除后无法恢复。')) {
                console.log('删除补遗/澄清/答疑');
                alert('补遗/澄清/答疑删除成功！');
            }
        }

        function printClarification() {
            // 打印补遗/澄清/答疑内容
            window.print();
        }

        function exportClarification() {
            console.log('导出补遗/澄清/答疑');
            alert('导出功能开发中...');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('补遗/澄清/答疑详情页面加载完成');
            
            // 初始化页签切换功能
            document.querySelectorAll('.detail-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');
                    
                    // 移除所有页签的激活状态
                    document.querySelectorAll('.detail-tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.detail-content').forEach(c => c.classList.remove('active'));
                    
                    // 激活当前页签和对应内容
                    this.classList.add('active');
                    document.getElementById(targetTab).classList.add('active');
                });
            });
        });
    </script>
</body>
</html>