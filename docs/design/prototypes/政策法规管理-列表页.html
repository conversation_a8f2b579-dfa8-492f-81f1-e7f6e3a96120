<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>政策法规管理 - 招采平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .page-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        .page-header {
            background: white;
            color: #333;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-bottom: 1px solid #e8eaec;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .help-icon {
            width: 18px;
            height: 18px;
            background: #5cadff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
            position: relative;
        }

        .help-tooltip {
            position: absolute;
            top: 25px;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .help-icon:hover .help-tooltip {
            opacity: 1;
            visibility: visible;
        }

        .page-breadcrumb {
            margin-top: 8px;
            opacity: 0.7;
            font-size: 14px;
        }

        .page-content {
            padding: 24px;
        }

        /* 查询区域样式 */
        .search-section {
            background: #fff;
            border: 1px solid #e8eaec;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .search-form {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: flex-end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }

        .form-label {
            font-size: 13px;
            color: #666;
            margin-bottom: 6px;
            font-weight: 500;
        }

        .form-control {
            height: 36px;
            padding: 8px 12px;
            border: 1px solid #dcdee2;
            border-radius: 4px;
            font-size: 13px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .form-control:focus {
            outline: none;
            border-color: #5cadff;
            box-shadow: 0 0 0 2px rgba(92, 173, 255, 0.2);
        }

        .select-control {
            cursor: pointer;
        }

        .date-range {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .date-separator {
            color: #999;
            font-size: 14px;
        }

        .search-buttons {
            display: flex;
            gap: 12px;
            margin-left: auto;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            text-decoration: none;
        }

        .btn-primary {
            background: #5cadff;
            color: white;
        }

        .btn-primary:hover {
            background: #4a9eff;
        }

        .btn-default {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #dcdee2;
        }

        .btn-default:hover {
            background: #e9ecef;
        }

        .btn-link {
            background: none;
            color: #5cadff;
            padding: 8px 0;
        }

        .btn-link:hover {
            color: #4a9eff;
        }

        /* 高级查询区域 */
        .advanced-search {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e8eaec;
            display: none;
        }

        .advanced-search.show {
            display: block;
        }

        /* 功能按钮区域 */
        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 16px 20px;
            background: #fff;
            border: 1px solid #e8eaec;
            border-radius: 6px;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
        }

        .btn-success {
            background: #52c41a;
            color: white;
        }

        .btn-success:hover {
            background: #45b018;
        }

        .btn-danger {
            background: #ff4d4f;
            color: white;
        }

        .btn-danger:hover {
            background: #e63946;
        }

        .btn-danger:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        /* 表格样式 */
        .table-container {
            background: #fff;
            border: 1px solid #e8eaec;
            border-radius: 6px;
            overflow: hidden;
        }

        .table-wrapper {
            overflow-x: auto;
            max-width: 100%;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            min-width: 1000px;
        }

        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e8eaec;
            font-size: 13px;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table tbody tr:hover {
            background: #f8f9fa;
        }

        /* 冻结列样式 */
        .data-table .col-checkbox {
            width: 60px;
            position: sticky;
            left: 0;
            background: inherit;
            z-index: 5;
        }

        .data-table .col-title {
            width: 200px;
            position: sticky;
            left: 60px;
            background: inherit;
            z-index: 5;
        }

        .data-table .col-status {
            width: 120px;
        }

        .data-table .col-updater {
            width: 120px;
        }

        .data-table .col-update-time {
            width: 150px;
        }

        .data-table .col-creator {
            width: 120px;
        }

        .data-table .col-create-time {
            width: 150px;
        }

        .data-table .col-actions {
            width: 180px;
            position: sticky;
            right: 0;
            background: inherit;
            z-index: 5;
        }

        .data-table th.col-checkbox,
        .data-table th.col-title,
        .data-table th.col-actions {
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }

        .data-table td.col-checkbox,
        .data-table td.col-title,
        .data-table td.col-actions {
            background: white;
        }

        .data-table tbody tr:hover td.col-checkbox,
        .data-table tbody tr:hover td.col-title,
        .data-table tbody tr:hover td.col-actions {
            background: #f8f9fa;
        }

        /* 状态标签 */
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-draft {
            background: #fff7e6;
            color: #fa8c16;
        }

        .status-published {
            background: #f6ffed;
            color: #52c41a;
        }

        /* 操作按钮 */
        .action-btn {
            padding: 4px 8px;
            margin: 0 2px;
            border: none;
            border-radius: 3px;
            font-size: 12px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }

        .btn-edit {
            background: #e6f7ff;
            color: #1890ff;
        }

        .btn-edit:hover {
            background: #bae7ff;
        }

        .btn-publish {
            background: #f6ffed;
            color: #52c41a;
        }

        .btn-publish:hover {
            background: #d9f7be;
        }

        .btn-revoke {
            background: #fff2e8;
            color: #fa8c16;
        }

        .btn-revoke:hover {
            background: #ffd8bf;
        }

        .btn-view {
            background: #f0f0f0;
            color: #666;
        }

        .btn-view:hover {
            background: #d9d9d9;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: #fff;
            border-top: 1px solid #e8eaec;
        }

        .pagination-info {
            font-size: 13px;
            color: #666;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .pagination-controls button {
            padding: 6px 12px;
            border: 1px solid #dcdee2;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            font-size: 13px;
        }

        .pagination-controls button:hover {
            background: #f8f9fa;
        }

        .pagination-controls button:disabled {
            background: #f5f5f5;
            color: #ccc;
            cursor: not-allowed;
        }

        .pagination-controls .current-page {
            background: #5cadff;
            color: white;
            border-color: #5cadff;
        }

        /* 链接样式 */
        .link-title {
            color: #5cadff;
            text-decoration: none;
            cursor: pointer;
        }

        .link-title:hover {
            color: #4a9eff;
            text-decoration: underline;
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .search-form {
                flex-direction: column;
                align-items: stretch;
            }

            .form-group {
                min-width: auto;
            }

            .search-buttons {
                margin-left: 0;
                justify-content: flex-start;
            }

            .action-bar {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">
                政策法规管理
                <div class="help-icon">
                    ?
                    <div class="help-tooltip">支持对政策法规管理进行新增、编辑、删除、查询、发布、撤销、查看详情</div>
                </div>
            </h1>
            <div class="page-breadcrumb">政策法规管理 > 列表</div>
        </div>

        <div class="page-content">
            <!-- 查询区域 -->
            <div class="search-section">
                <div class="search-form">
                    <div class="form-group">
                        <label class="form-label">标题名称</label>
                        <input type="text" class="form-control" placeholder="请输入标题名称（2位以上进行模糊查询）" id="titleName">
                    </div>
                    <div class="form-group">
                        <label class="form-label">发布状态</label>
                        <select class="form-control select-control" id="publishStatus">
                            <option value="">全部</option>
                            <option value="draft">待发布</option>
                            <option value="published">已发布</option>
                        </select>
                    </div>
                    <div class="search-buttons">
                        <button class="btn btn-primary" onclick="searchData()">查询</button>
                        <button class="btn btn-default" onclick="resetSearch()">重置</button>
                        <button class="btn btn-link" onclick="toggleAdvancedSearch()">高级查询 ▼</button>
                    </div>
                </div>

                <!-- 高级查询区域 -->
                <div class="advanced-search" id="advancedSearch">
                    <div class="search-form">
                        <div class="form-group">
                            <label class="form-label">更新时间</label>
                            <div class="date-range">
                                <input type="date" class="form-control" id="updateTimeStart">
                                <span class="date-separator">至</span>
                                <input type="date" class="form-control" id="updateTimeEnd">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">创建时间</label>
                            <div class="date-range">
                                <input type="date" class="form-control" id="createTimeStart">
                                <span class="date-separator">至</span>
                                <input type="date" class="form-control" id="createTimeEnd">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 功能按钮区域 -->
            <div class="action-bar">
                <div class="action-buttons">
                    <button class="btn btn-success" onclick="addNew()">+ 新增</button>
                    <button class="btn btn-danger" onclick="batchDelete()" id="batchDeleteBtn" disabled>批量删除</button>
                </div>
            </div>

            <!-- 数据表格 -->
            <div class="table-container">
                <div class="table-wrapper">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th class="col-checkbox">
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th class="col-title">标题名称</th>
                                <th class="col-status">发布状态</th>
                                <th class="col-updater">更新人</th>
                                <th class="col-update-time">更新时间</th>
                                <th class="col-creator">创建人</th>
                                <th class="col-create-time">创建时间</th>
                                <th class="col-actions">操作</th>
                            </tr>
                        </thead>
                        <tbody id="dataTableBody">
                            <tr>
                                <td class="col-checkbox">
                                    <input type="checkbox" class="row-checkbox" value="1" onchange="updateBatchButtons()">
                                </td>
                                <td class="col-title">
                                    <a href="#" class="link-title" onclick="viewDetail(1)">政府采购法实施条例</a>
                                </td>
                                <td class="col-status">
                                    <span class="status-badge status-published">已发布</span>
                                </td>
                                <td class="col-updater">张三</td>
                                <td class="col-update-time">2024-01-15 14:30</td>
                                <td class="col-creator">李四</td>
                                <td class="col-create-time">2024-01-10 09:00</td>
                                <td class="col-actions">
                                    <a href="#" class="action-btn btn-revoke" onclick="revokePolicy(1)">撤销</a>
                                </td>
                            </tr>
                            <tr>
                                <td class="col-checkbox">
                                    <input type="checkbox" class="row-checkbox" value="2" onchange="updateBatchButtons()">
                                </td>
                                <td class="col-title">
                                    <a href="#" class="link-title" onclick="viewDetail(2)">招标投标法修订草案</a>
                                </td>
                                <td class="col-status">
                                    <span class="status-badge status-draft">待发布</span>
                                </td>
                                <td class="col-updater">王五</td>
                                <td class="col-update-time">2024-01-12 16:45</td>
                                <td class="col-creator">赵六</td>
                                <td class="col-create-time">2024-01-08 11:20</td>
                                <td class="col-actions">
                                    <a href="#" class="action-btn btn-edit" onclick="editPolicy(2)">编辑</a>
                                    <a href="#" class="action-btn btn-publish" onclick="publishPolicy(2)">发布</a>
                                </td>
                            </tr>
                            <tr>
                                <td class="col-checkbox">
                                    <input type="checkbox" class="row-checkbox" value="3" onchange="updateBatchButtons()">
                                </td>
                                <td class="col-title">
                                    <a href="#" class="link-title" onclick="viewDetail(3)">政府采购货物和服务招标投标管理办法</a>
                                </td>
                                <td class="col-status">
                                    <span class="status-badge status-published">已发布</span>
                                </td>
                                <td class="col-updater">陈七</td>
                                <td class="col-update-time">2024-01-08 10:15</td>
                                <td class="col-creator">刘八</td>
                                <td class="col-create-time">2024-01-05 14:30</td>
                                <td class="col-actions">
                                    <a href="#" class="action-btn btn-revoke" onclick="revokePolicy(3)">撤销</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <div class="pagination-info">
                        共 3 条记录，每页 20 条，共 1 页
                    </div>
                    <div class="pagination-controls">
                        <button disabled>上一页</button>
                        <button class="current-page">1</button>
                        <button disabled>下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 高级查询展开/收起
        function toggleAdvancedSearch() {
            const advancedSearch = document.getElementById('advancedSearch');
            const toggleBtn = event.target;
            
            if (advancedSearch.classList.contains('show')) {
                advancedSearch.classList.remove('show');
                toggleBtn.textContent = '高级查询 ▼';
            } else {
                advancedSearch.classList.add('show');
                toggleBtn.textContent = '高级查询 ▲';
            }
        }

        // 查询功能
        function searchData() {
            const titleName = document.getElementById('titleName').value;
            const publishStatus = document.getElementById('publishStatus').value;
            const updateTimeStart = document.getElementById('updateTimeStart').value;
            const updateTimeEnd = document.getElementById('updateTimeEnd').value;
            const createTimeStart = document.getElementById('createTimeStart').value;
            const createTimeEnd = document.getElementById('createTimeEnd').value;
            
            console.log('查询条件:', {
                titleName,
                publishStatus,
                updateTimeStart,
                updateTimeEnd,
                createTimeStart,
                createTimeEnd
            });
            
            // 这里实现实际的查询逻辑
            alert('执行查询操作');
        }

        // 重置查询
        function resetSearch() {
            document.getElementById('titleName').value = '';
            document.getElementById('publishStatus').value = '';
            document.getElementById('updateTimeStart').value = '';
            document.getElementById('updateTimeEnd').value = '';
            document.getElementById('createTimeStart').value = '';
            document.getElementById('createTimeEnd').value = '';
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.row-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
            
            updateBatchButtons();
        }

        // 更新批量操作按钮状态
        function updateBatchButtons() {
            const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
            const batchDeleteBtn = document.getElementById('batchDeleteBtn');
            
            batchDeleteBtn.disabled = checkedBoxes.length === 0;
        }

        // 页面跳转函数
        function openPage(url, title) {
            if (window.parent && window.parent !== window) {
                // 向父窗口发送消息，请求打开新页面
                window.parent.postMessage({
                    type: 'openPage',
                    title: title,
                    url: url
                }, '*');
            } else {
                window.open(url, '_blank');
            }
        }

        // 新增
        function addNew() {
            openPage('政策法规管理-新建编辑页.html', '新增政策法规');
        }

        // 编辑
        function editPolicy(id) {
            openPage('政策法规管理-新建编辑页.html?id=' + id, '编辑政策法规');
        }

        // 查看详情
        function viewDetail(id) {
            openPage('政策法规管理-详情页.html?id=' + id, '政策法规详情');
        }

        // 发布
        function publishPolicy(id) {
            if (confirm('确定要发布这条政策法规吗？')) {
                console.log('发布政策法规:', id);
                alert('发布成功');
                // 这里实现实际的发布逻辑
                location.reload();
            }
        }

        // 撤销
        function revokePolicy(id) {
            if (confirm('确定要撤销这条政策法规吗？')) {
                console.log('撤销政策法规:', id);
                alert('撤销成功');
                // 这里实现实际的撤销逻辑
                location.reload();
            }
        }

        // 批量删除
        function batchDelete() {
            const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
            if (checkedBoxes.length === 0) {
                alert('请选择要删除的记录');
                return;
            }
            
            if (confirm(`确定要删除选中的 ${checkedBoxes.length} 条记录吗？`)) {
                const ids = Array.from(checkedBoxes).map(cb => cb.value);
                console.log('批量删除:', ids);
                alert('删除成功');
                // 这里实现实际的批量删除逻辑
                location.reload();
            }
        }
    </script>
</body>
</html>