<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理机构管理 - 新增/编辑 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 页面头部 */
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .help-icon {
            cursor: pointer;
            color: #3498db;
            font-size: 18px;
            padding: 4px;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .help-icon:hover {
            background-color: #e3f2fd;
        }

        .breadcrumb {
            color: #7f8c8d;
            font-size: 14px;
        }

        .breadcrumb a {
            color: #3498db;
            text-decoration: none;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .back-btn {
            background: #95a5a6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .back-btn:hover {
            background: #7f8c8d;
        }

        /* 表单区域 */
        .form-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .section-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
            position: relative;
            padding-left: 15px;
        }

        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 16px;
            background: #3498db;
            border-radius: 2px;
        }

        .section-content {
            padding: 20px;
        }

        /* 表单网格 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            font-size: 13px;
            color: #555;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-label.required::after {
            content: ' *';
            color: #e74c3c;
        }

        .form-control {
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            transition: border-color 0.3s;
            background: white;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .form-control:disabled {
            background-color: #f8f9fa;
            color: #6c757d;
        }

        .form-control.error {
            border-color: #e74c3c;
        }

        .error-message {
            color: #e74c3c;
            font-size: 12px;
            margin-top: 5px;
        }

        textarea.form-control {
            resize: vertical;
            min-height: 80px;
        }

        /* 按钮组 */
        .button-group {
            background: #f8f9fa;
            padding: 20px;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        .btn {
            padding: 10px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
            min-width: 100px;
            justify-content: center;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-outline {
            background: white;
            color: #3498db;
            border: 1px solid #3498db;
        }

        .btn-outline:hover {
            background: #3498db;
            color: white;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .help-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }

        .help-close {
            position: absolute;
            right: 15px;
            top: 15px;
            font-size: 24px;
            cursor: pointer;
            color: #aaa;
        }

        .help-close:hover {
            color: #333;
        }

        .help-title {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 20px;
        }

        .help-content h4 {
            color: #34495e;
            margin: 15px 0 10px 0;
            font-size: 16px;
        }

        .help-content ul {
            margin-left: 20px;
            margin-bottom: 15px;
        }

        .help-content li {
            margin-bottom: 8px;
            line-height: 1.6;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .form-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .page-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .button-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">
                    新增代理机构
                    <span class="help-icon" onclick="showHelp()" title="查看帮助">❓</span>
                </h1>
                <div class="breadcrumb">
                    <a href="#">首页</a> > <a href="#">系统管理</a> > <a href="代理机构管理-列表页.html">代理机构管理</a> > 新增代理机构
                </div>
            </div>
            <button class="back-btn" onclick="goBack()">
                ← 返回列表
            </button>
        </div>

        <!-- 表单区域 -->
        <div class="form-section">
            <div class="section-header">
                <h2 class="section-title">基本信息</h2>
            </div>
            <div class="section-content">
                <form id="agencyForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label required">代理机构名称</label>
                            <input type="text" class="form-control" id="agencyName" name="agencyName" placeholder="请输入代理机构名称" required>
                            <div class="error-message" id="agencyNameError"></div>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">企业代码</label>
                            <input type="text" class="form-control" id="enterpriseCode" name="enterpriseCode" placeholder="请输入统一社会信用代码" required>
                            <div class="error-message" id="enterpriseCodeError"></div>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">联系人</label>
                            <input type="text" class="form-control" id="contactPerson" name="contactPerson" placeholder="请输入联系人姓名" required>
                            <div class="error-message" id="contactPersonError"></div>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">联系方式</label>
                            <input type="text" class="form-control" id="contactInfo" name="contactInfo" placeholder="请输入联系电话或邮箱" required>
                            <div class="error-message" id="contactInfoError"></div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">更新时间</label>
                            <input type="datetime-local" class="form-control" id="updateTime" name="updateTime" disabled>
                        </div>
                        <div class="form-group">
                            <label class="form-label">创建时间</label>
                            <input type="datetime-local" class="form-control" id="createTime" name="createTime" disabled>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- 按钮组 -->
            <div class="button-group">
                <button type="button" class="btn btn-primary" onclick="saveAgency()">
                    💾 保存
                </button>
                <button type="button" class="btn btn-outline" onclick="resetForm()">
                    🔄 重置
                </button>
                <button type="button" class="btn btn-secondary" onclick="goBack()">
                    ← 取消
                </button>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="helpModal" class="help-modal">
        <div class="help-content">
            <span class="help-close" onclick="hideHelp()">&times;</span>
            <h3 class="help-title">代理机构新增/编辑说明</h3>
            <p>在此页面可以新增或编辑代理机构的基本信息。</p>
            
            <h4>字段说明</h4>
            <ul>
                <li><strong>代理机构名称：</strong>招标代理机构的完整名称，必填项</li>
                <li><strong>企业代码：</strong>统一社会信用代码或组织机构代码，必填项</li>
                <li><strong>联系人：</strong>机构主要联系人姓名，必填项</li>
                <li><strong>联系方式：</strong>联系电话或邮箱地址，必填项</li>
                <li><strong>更新时间：</strong>记录最后修改时间，系统自动生成</li>
                <li><strong>创建时间：</strong>记录创建时间，系统自动生成</li>
            </ul>
            
            <h4>填写要求</h4>
            <ul>
                <li><strong>代理机构名称：</strong>不能与已有机构重名</li>
                <li><strong>企业代码：</strong>必须符合统一社会信用代码格式</li>
                <li><strong>联系方式：</strong>支持手机号码、固定电话或邮箱格式</li>
            </ul>
            
            <h4>操作说明</h4>
            <ul>
                <li><strong>保存：</strong>保存当前填写的信息并返回列表页</li>
                <li><strong>重置：</strong>清空所有已填写的内容</li>
                <li><strong>取消：</strong>放弃当前操作并返回列表页</li>
            </ul>
        </div>
    </div>

    <script>
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否为编辑模式
            const urlParams = new URLSearchParams(window.location.search);
            const agencyId = urlParams.get('id');
            
            if (agencyId) {
                // 编辑模式
                document.querySelector('.page-title').textContent = '编辑代理机构';
                document.querySelector('.breadcrumb').innerHTML = 
                    '<a href="#">首页</a> > <a href="#">系统管理</a> > <a href="代理机构管理-列表页.html">代理机构管理</a> > 编辑代理机构';
                loadAgencyData(agencyId);
            } else {
                // 新增模式，设置创建时间
                const now = new Date();
                document.getElementById('createTime').value = formatDateTime(now);
                document.getElementById('updateTime').value = formatDateTime(now);
            }
        });

        // 加载代理机构数据（编辑模式）
        function loadAgencyData(id) {
            // 模拟从后端加载数据
            const mockData = {
                agencyName: '中建工程咨询有限公司',
                enterpriseCode: '91110000123456789X',
                contactPerson: '张经理',
                contactInfo: '13800138001',
                updateTime: '2024-01-15T14:30',
                createTime: '2023-06-20T09:15'
            };
            
            // 填充表单数据
            Object.keys(mockData).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.value = mockData[key];
                }
            });
        }

        // 保存代理机构
        function saveAgency() {
            if (validateForm()) {
                // 更新时间
                document.getElementById('updateTime').value = formatDateTime(new Date());
                
                // 模拟保存操作
                alert('保存成功！');
                goBack();
            }
        }

        // 表单验证
        function validateForm() {
            let isValid = true;
            
            // 清除之前的错误信息
            document.querySelectorAll('.error-message').forEach(el => el.textContent = '');
            document.querySelectorAll('.form-control').forEach(el => el.classList.remove('error'));
            
            // 验证代理机构名称
            const agencyName = document.getElementById('agencyName');
            if (!agencyName.value.trim()) {
                showError('agencyName', '请输入代理机构名称');
                isValid = false;
            }
            
            // 验证企业代码
            const enterpriseCode = document.getElementById('enterpriseCode');
            if (!enterpriseCode.value.trim()) {
                showError('enterpriseCode', '请输入企业代码');
                isValid = false;
            } else if (!validateEnterpriseCode(enterpriseCode.value)) {
                showError('enterpriseCode', '企业代码格式不正确');
                isValid = false;
            }
            
            // 验证联系人
            const contactPerson = document.getElementById('contactPerson');
            if (!contactPerson.value.trim()) {
                showError('contactPerson', '请输入联系人');
                isValid = false;
            }
            
            // 验证联系方式
            const contactInfo = document.getElementById('contactInfo');
            if (!contactInfo.value.trim()) {
                showError('contactInfo', '请输入联系方式');
                isValid = false;
            } else if (!validateContactInfo(contactInfo.value)) {
                showError('contactInfo', '联系方式格式不正确');
                isValid = false;
            }
            
            return isValid;
        }

        // 显示错误信息
        function showError(fieldId, message) {
            const field = document.getElementById(fieldId);
            const errorElement = document.getElementById(fieldId + 'Error');
            
            field.classList.add('error');
            errorElement.textContent = message;
        }

        // 验证企业代码格式
        function validateEnterpriseCode(code) {
            // 统一社会信用代码：18位数字和字母
            const pattern = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/;
            return pattern.test(code);
        }

        // 验证联系方式格式
        function validateContactInfo(contact) {
            // 手机号码、固定电话或邮箱
            const phonePattern = /^1[3-9]\d{9}$/;
            const telPattern = /^\d{3,4}-?\d{7,8}$/;
            const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            
            return phonePattern.test(contact) || telPattern.test(contact) || emailPattern.test(contact);
        }

        // 重置表单
        function resetForm() {
            if (confirm('确定要重置表单吗？所有已填写的内容将被清空。')) {
                document.getElementById('agencyForm').reset();
                
                // 清除错误信息
                document.querySelectorAll('.error-message').forEach(el => el.textContent = '');
                document.querySelectorAll('.form-control').forEach(el => el.classList.remove('error'));
                
                // 重新设置时间
                const urlParams = new URLSearchParams(window.location.search);
                const agencyId = urlParams.get('id');
                
                if (!agencyId) {
                    const now = new Date();
                    document.getElementById('createTime').value = formatDateTime(now);
                    document.getElementById('updateTime').value = formatDateTime(now);
                }
            }
        }

        // 返回列表页
        function goBack() {
            if (confirm('确定要离开此页面吗？未保存的更改将丢失。')) {
                window.location.href = '代理机构管理-列表页.html';
            }
        }

        // 格式化日期时间
        function formatDateTime(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            
            return `${year}-${month}-${day}T${hours}:${minutes}`;
        }

        // 帮助弹窗
        function showHelp() {
            document.getElementById('helpModal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('helpModal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('helpModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>