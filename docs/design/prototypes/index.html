<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>原型页面导航</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
          'Helvetica Neue', Helvetica, Arial, sans-serif;
        background: #f5f7fa;
        min-height: 100vh;
        margin: 0;
        padding: 0;
      }

      .container {
        display: flex;
        height: 100vh;
        background: white;
      }

      .sidebar {
        width: 350px;
        background: #2c3e50;
        color: white;
        overflow-y: auto;
        flex-shrink: 0;
      }

      .sidebar-header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        padding: 20px;
        text-align: center;
      }

      .sidebar-header h1 {
        font-size: 1.5rem;
        margin-bottom: 5px;
        font-weight: 300;
      }

      .sidebar-header p {
        font-size: 0.9rem;
        opacity: 0.9;
      }

      .main-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      .header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        padding: 20px 30px;
        text-align: center;
      }

      .header h1 {
        font-size: 2rem;
        margin-bottom: 10px;
        font-weight: 300;
      }

      .header p {
        font-size: 1rem;
        opacity: 0.9;
      }

      .content {
        flex: 1;
        padding: 30px;
        overflow-y: auto;
      }

      .search-box {
        padding: 20px;
        border-bottom: 1px solid #34495e;
      }

      .search-input {
        width: 100%;
        padding: 10px 15px;
        border: 1px solid #34495e;
        border-radius: 6px;
        font-size: 14px;
        background: #34495e;
        color: white;
        transition: border-color 0.3s ease;
      }

      .search-input::placeholder {
        color: #bdc3c7;
      }

      .search-input:focus {
        outline: none;
        border-color: #4facfe;
        background: #2c3e50;
      }

      .tree-nav {
        padding: 0;
      }

      .tree-module {
        border-bottom: 1px solid #34495e;
      }

      .module-header {
        padding: 15px 20px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: background-color 0.3s ease;
        font-weight: 500;
      }

      .module-header:hover {
        background: #34495e;
      }

      .module-header.active {
        background: #3498db;
      }

      .module-icon {
        font-size: 12px;
        transition: transform 0.3s ease;
      }

      .module-icon.expanded {
        transform: rotate(90deg);
      }

      .module-pages {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
        background: #34495e;
      }

      .module-pages.expanded {
        max-height: 500px;
      }

      .page-item {
        padding: 10px 20px 10px 40px;
        cursor: pointer;
        transition: background-color 0.3s ease;
        border-left: 3px solid transparent;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .page-item:hover {
        background: #2c3e50;
        border-left-color: #4facfe;
      }

      .page-item.active {
        background: #4facfe;
        border-left-color: #fff;
      }

      .page-name {
        font-size: 14px;
        flex: 1;
      }

      .page-type-badge {
        font-size: 11px;
        background: #7f8c8d;
        color: white;
        padding: 2px 6px;
        border-radius: 10px;
        margin-left: 10px;
      }

      .iframe-container {
        width: 100%;
        height: 100%;
        border: none;
        background: white;
      }

      .welcome-screen {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        text-align: center;
        color: #7f8c8d;
      }

      .welcome-icon {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #bdc3c7;
      }

      .welcome-title {
        font-size: 1.5rem;
        margin-bottom: 10px;
        color: #2c3e50;
      }

      .welcome-text {
        font-size: 1rem;
        line-height: 1.6;
      }

      .stats {
        padding: 20px;
        background: #34495e;
        border-bottom: 1px solid #2c3e50;
        display: flex;
        justify-content: space-around;
      }

      .stat-item {
        text-align: center;
      }

      .stat-number {
        font-size: 1.5rem;
        font-weight: 700;
        color: #4facfe;
      }

      .stat-label {
        font-size: 0.8rem;
        color: #bdc3c7;
        margin-top: 5px;
      }

      .hidden {
        display: none;
      }

      @media (max-width: 768px) {
        .container {
          flex-direction: column;
        }

        .sidebar {
          width: 100%;
          height: 300px;
        }

        .main-content {
          flex: 1;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- 左侧导航栏 -->
      <div class="sidebar">
        <div class="sidebar-header">
          <h1>原型导航</h1>
          <p>页面原型浏览器</p>
        </div>

        <div class="stats">
          <div class="stat-item">
            <div class="stat-number" id="totalPages">0</div>
            <div class="stat-label">总页面</div>
          </div>
          <div class="stat-item">
            <div class="stat-number" id="totalCategories">0</div>
            <div class="stat-label">模块</div>
          </div>
          <div class="stat-item">
            <div class="stat-number" id="visiblePages">0</div>
            <div class="stat-label">显示</div>
          </div>
        </div>

        <div class="search-box">
          <input type="text" class="search-input" id="searchInput" placeholder="搜索页面..." />
        </div>

        <div class="tree-nav" id="treeNav"></div>
      </div>

      <!-- 右侧主内容区 -->
      <div class="main-content">
        <div class="header">
          <h1 id="currentPageTitle">原型页面导航</h1>
          <p id="currentPageDesc">选择左侧导航中的页面进行预览</p>
        </div>

        <div class="content">
          <div class="welcome-screen" id="welcomeScreen">
            <div class="welcome-icon">📄</div>
            <div class="welcome-title">欢迎使用原型导航</div>
            <div class="welcome-text">
              请从左侧导航栏选择要查看的页面<br />
              点击模块名称可以展开/收起页面列表
            </div>
          </div>
          <iframe id="pageFrame" class="iframe-container hidden" src=""></iframe>
        </div>
      </div>
    </div>

    <script>
      // 页面数据
      const pages = [
        '中标结果公示管理-列表页.html',
        '中标结果公示管理-审核页.html',
        '中标结果公示管理-新建编辑页.html',
        '中标结果公示管理-详情页.html',
        '代理机构管理-列表页.html',
        '代理机构管理-新建编辑页.html',
        '代理机构管理-详情页.html',
        '供应商管理-列表页.html',
        '供应商管理-新建编辑页.html',
        '供应商管理-详情页.html',
        '公告管理-列表页.html',
        '公告管理-审核页.html',
        '公告管理-新建编辑页.html',
        '公告管理-详情页.html',
        '工作台-通知详情页.html',
        '工作台.html',
        '帮助中心-列表页.html',
        '我的待办-列表页.html',
        '投诉管理-列表页.html',
        '投诉管理-新建编辑页.html',
        '投诉管理-详情页.html',
        '招采平台主页.html',
        '政策法规管理-列表页.html',
        '政策法规管理-新建编辑页.html',
        '政策法规管理-详情页.html',
        '数据分析-列表页.html',
        '机构人员管理-列表页.html',
        '机构人员管理-新建编辑页.html',
        '机构人员管理-详情页.html',
        '流标或中止管理-列表页.html',
        '流标或中止管理-审核页.html',
        '流标或中止管理-新建编辑页.html',
        '流标或中止管理-详情页.html',
        '签约履行管理-列表页.html',
        '签约履行管理-审核页.html',
        '签约履行管理-新建编辑页.html',
        '签约履行管理-详情页.html',
        '统计报表-列表页.html',
        '补遗澄清答疑管理-列表页.html',
        '补遗澄清答疑管理-审核页.html',
        '补遗澄清答疑管理-新建编辑页.html',
        '补遗澄清答疑管理-详情页.html',
        '评标结果公示管理-列表页.html',
        '评标结果公示管理-审核页.html',
        '评标结果公示管理-新建编辑页.html',
        '评标结果公示管理-详情页.html',
        '通知公告-列表页.html',
        '通知公告-详情页.html',
        '通知公告管理-列表页.html',
        '通知公告管理-新建编辑页.html',
        '通知公告管理-详情页.html',
        '采购计划管理-列表页.html',
        '采购计划管理-审核页.html',
        '采购计划管理-新建编辑页.html',
        '采购计划管理-详情页.html',
        '项目标段管理-列表页.html',
        '项目标段管理-审核页.html',
        '项目标段管理-新建编辑页.html',
        '项目标段管理-标段详情页.html',
        '项目标段管理-项目详情页.html',
        '项目预警管理-列表页.html',
        '项目预警管理-详情页.html',
        '预警配置-列表页.html'
      ];

      let currentActivePage = null;

      // 解析页面类型
      function getPageType(filename) {
        if (filename.includes('-列表页')) return '列表页';
        if (filename.includes('-详情页')) return '详情页';
        if (filename.includes('-新建编辑页')) return '新建编辑页';
        if (filename.includes('-审核页')) return '审核页';
        if (filename.includes('-标段详情页')) return '标段详情页';
        if (filename.includes('-项目详情页')) return '项目详情页';
        if (filename.includes('-通知详情页')) return '通知详情页';
        return '主页';
      }

      // 按模块分组页面
      function groupPagesByModule() {
        const groups = {};

        pages.forEach(page => {
          let moduleName;
          if (page.includes('-')) {
            moduleName = page.split('-')[0];
          } else {
            moduleName = page.replace('.html', '');
          }

          if (!groups[moduleName]) {
            groups[moduleName] = [];
          }

          groups[moduleName].push({
            filename: page,
            title: page.replace('.html', ''),
            type: getPageType(page),
            displayName: page.replace('.html', '').split('-').slice(1).join('-') || '主页'
          });
        });

        return groups;
      }

      // 加载页面到iframe
      function loadPage(filename, title) {
        const iframe = document.getElementById('pageFrame');
        const welcomeScreen = document.getElementById('welcomeScreen');
        const pageTitle = document.getElementById('currentPageTitle');
        const pageDesc = document.getElementById('currentPageDesc');

        // 更新活动状态
        if (currentActivePage) {
          currentActivePage.classList.remove('active');
        }

        // 显示iframe，隐藏欢迎屏幕
        welcomeScreen.classList.add('hidden');
        iframe.classList.remove('hidden');

        // 加载页面
        iframe.src = filename;

        // 更新标题
        pageTitle.textContent = title;
        pageDesc.textContent = `当前查看: ${filename}`;
      }

      // 切换模块展开/收起
      function toggleModule(moduleElement, moduleName) {
        const icon = moduleElement.querySelector('.module-icon');
        const pages = moduleElement.nextElementSibling;
        const isExpanded = pages.classList.contains('expanded');

        if (isExpanded) {
          pages.classList.remove('expanded');
          icon.classList.remove('expanded');
          moduleElement.classList.remove('active');
        } else {
          pages.classList.add('expanded');
          icon.classList.add('expanded');
          moduleElement.classList.add('active');
        }
      }

      // 渲染树状导航
      function renderTreeNav(searchTerm = '') {
        const container = document.getElementById('treeNav');
        const groups = groupPagesByModule();

        container.innerHTML = '';
        let visibleCount = 0;
        let visibleCategories = 0;

        Object.keys(groups)
          .sort()
          .forEach(moduleName => {
            const modulePages = groups[moduleName].filter(page =>
              page.title.toLowerCase().includes(searchTerm.toLowerCase())
            );

            if (modulePages.length === 0) return;

            visibleCategories++;

            // 创建模块容器
            const moduleDiv = document.createElement('div');
            moduleDiv.className = 'tree-module';

            // 创建模块头部
            const moduleHeader = document.createElement('div');
            moduleHeader.className = 'module-header';
            moduleHeader.innerHTML = `
              <span>${moduleName}</span>
              <span class="module-icon">▶</span>
            `;

            // 创建页面列表容器
            const pagesContainer = document.createElement('div');
            pagesContainer.className = 'module-pages';

            // 添加页面项
            modulePages.forEach(page => {
              visibleCount++;
              const pageItem = document.createElement('div');
              pageItem.className = 'page-item';
              pageItem.innerHTML = `
                <span class="page-name">${page.displayName}</span>
                <span class="page-type-badge">${page.type}</span>
              `;

              // 添加点击事件
              pageItem.addEventListener('click', () => {
                if (currentActivePage) {
                  currentActivePage.classList.remove('active');
                }
                pageItem.classList.add('active');
                currentActivePage = pageItem;
                loadPage(page.filename, page.title);
              });

              pagesContainer.appendChild(pageItem);
            });

            // 添加模块头部点击事件
            moduleHeader.addEventListener('click', () => {
              toggleModule(moduleHeader, moduleName);
            });

            moduleDiv.appendChild(moduleHeader);
            moduleDiv.appendChild(pagesContainer);
            container.appendChild(moduleDiv);
          });

        // 更新统计信息
        document.getElementById('totalPages').textContent = pages.length;
        document.getElementById('totalCategories').textContent = Object.keys(groups).length;
        document.getElementById('visiblePages').textContent = visibleCount;
      }

      // 搜索功能
      document.getElementById('searchInput').addEventListener('input', e => {
        renderTreeNav(e.target.value);
      });

      // 初始化页面
      renderTreeNav();
    </script>
  </body>
</html>
