<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知公告管理 - 新建/编辑 - 招采平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .page-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        .page-header {
            background: white;
            color: #333;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-bottom: 1px solid #e8eaec;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .back-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
        }

        .page-breadcrumb {
            margin-top: 8px;
            opacity: 0.9;
            font-size: 14px;
        }

        .page-content {
            padding: 24px;
        }

        /* 表单容器 */
        .form-container {
            background: #fff;
            border: 1px solid #e8eaec;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .form-section {
            border-bottom: 1px solid #f0f0f0;
        }

        .form-section:last-child {
            border-bottom: none;
        }

        .section-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e8eaec;
            font-weight: 600;
            color: #333;
            position: relative;
        }

        .section-header::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #5cadff;
        }

        .section-content {
            padding: 20px;
        }

        /* 表单样式 */
        .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row:last-child {
            margin-bottom: 0;
        }

        .form-group {
            flex: 1;
            min-width: 300px;
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            flex: 1 1 100%;
            min-width: 100%;
        }

        .form-label {
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-label .required {
            color: #ed4014;
            margin-left: 2px;
        }

        .form-control {
            height: 40px;
            padding: 10px 12px;
            border: 1px solid #dcdee2;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .form-control:focus {
            outline: none;
            border-color: #5cadff;
            box-shadow: 0 0 0 2px rgba(92, 173, 255, 0.2);
        }

        .form-control:disabled {
            background: #f5f7fa;
            color: #999;
            cursor: not-allowed;
        }

        .form-control.error {
            border-color: #ed4014;
        }

        .form-control.error:focus {
            box-shadow: 0 0 0 2px rgba(237, 64, 20, 0.2);
        }

        .error-message {
            color: #ed4014;
            font-size: 12px;
            margin-top: 4px;
        }

        .form-help {
            color: #999;
            font-size: 12px;
            margin-top: 4px;
        }

        /* 富文本编辑器样式 */
        .rich-editor {
            border: 1px solid #dcdee2;
            border-radius: 4px;
            overflow: hidden;
        }

        .editor-toolbar {
            background: #f8f9fa;
            border-bottom: 1px solid #e8eaec;
            padding: 10px;
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .editor-btn {
            padding: 6px 10px;
            border: 1px solid #dcdee2;
            background: #fff;
            color: #666;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .editor-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .editor-btn.active {
            background: #5cadff;
            color: white;
            border-color: #5cadff;
        }

        .editor-content {
            min-height: 300px;
            padding: 15px;
            background: #fff;
            outline: none;
            line-height: 1.6;
        }

        .editor-content:focus {
            box-shadow: inset 0 0 0 2px rgba(92, 173, 255, 0.2);
        }

        /* 文件上传样式 */
        .upload-area {
            border: 2px dashed #dcdee2;
            border-radius: 6px;
            padding: 40px 20px;
            text-align: center;
            background: #fafbfc;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #5cadff;
            background: #f0f8ff;
        }

        .upload-area.dragover {
            border-color: #5cadff;
            background: #e6f4ff;
        }

        .upload-icon {
            font-size: 48px;
            color: #ccc;
            margin-bottom: 15px;
        }

        .upload-text {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .upload-hint {
            color: #999;
            font-size: 12px;
        }

        .file-input {
            display: none;
        }

        .file-list {
            margin-top: 15px;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            border: 1px solid #e8eaec;
            border-radius: 4px;
            margin-bottom: 8px;
            background: #fff;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .file-icon {
            width: 24px;
            height: 24px;
            background: #5cadff;
            color: white;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .file-name {
            font-size: 14px;
            color: #333;
        }

        .file-size {
            font-size: 12px;
            color: #999;
        }

        .file-actions {
            display: flex;
            gap: 8px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .btn-primary {
            background: #5cadff;
            color: white;
        }

        .btn-primary:hover {
            background: #4a9eff;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #19be6b;
            color: white;
        }

        .btn-success:hover {
            background: #17a85a;
            transform: translateY(-1px);
        }

        .btn-default {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #dcdee2;
        }

        .btn-default:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .btn-danger {
            background: #ed4014;
            color: white;
        }

        .btn-danger:hover {
            background: #d73502;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }

        /* 操作按钮区域 */
        .form-actions {
            background: #f8f9fa;
            padding: 20px;
            border-top: 1px solid #e8eaec;
            display: flex;
            justify-content: center;
            gap: 15px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }
            
            .form-group {
                min-width: 100%;
            }
            
            .form-actions {
                flex-direction: column;
            }
        }

        /* 字符计数 */
        .char-count {
            text-align: right;
            font-size: 12px;
            color: #999;
            margin-top: 4px;
        }

        .char-count.warning {
            color: #fa8c16;
        }

        .char-count.error {
            color: #ed4014;
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">
                <a href="通知公告管理-列表页.html" class="back-btn">← 返回</a>
                <span id="pageTitle">新建通知公告</span>
            </h1>
            <div class="page-breadcrumb">代理机构 > 通知公告管理 > <span id="breadcrumbTitle">新建</span></div>
        </div>

        <div class="page-content">
            <form class="form-container" id="noticeForm">
                <!-- 通知公告信息 -->
                <div class="form-section">
                    <div class="section-header">通知公告信息</div>
                    <div class="section-content">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">
                                    标题名称
                                    <span class="required">*</span>
                                </label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       placeholder="请输入标题名称" maxlength="50" required>
                                <div class="char-count">
                                    <span id="titleCount">0</span>/50
                                </div>
                                <div class="error-message" id="titleError"></div>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group full-width">
                                <label class="form-label">
                                    通知公告说明
                                    <span class="required">*</span>
                                </label>
                                <div class="rich-editor">
                                    <div class="editor-toolbar">
                                        <button type="button" class="editor-btn" onclick="formatText('bold')">粗体</button>
                                        <button type="button" class="editor-btn" onclick="formatText('italic')">斜体</button>
                                        <button type="button" class="editor-btn" onclick="formatText('underline')">下划线</button>
                                        <button type="button" class="editor-btn" onclick="formatText('justifyLeft')">左对齐</button>
                                        <button type="button" class="editor-btn" onclick="formatText('justifyCenter')">居中</button>
                                        <button type="button" class="editor-btn" onclick="formatText('justifyRight')">右对齐</button>
                                        <button type="button" class="editor-btn" onclick="formatText('insertUnorderedList')">无序列表</button>
                                        <button type="button" class="editor-btn" onclick="formatText('insertOrderedList')">有序列表</button>
                                        <button type="button" class="editor-btn" onclick="insertLink()">插入链接</button>
                                        <button type="button" class="editor-btn" onclick="formatText('removeFormat')">清除格式</button>
                                    </div>
                                    <div class="editor-content" id="content" contenteditable="true" 
                                         placeholder="请输入通知公告详细说明..."></div>
                                </div>
                                <div class="error-message" id="contentError"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 附件上传 -->
                <div class="form-section">
                    <div class="section-header">附件上传</div>
                    <div class="section-content">
                        <div class="form-row">
                            <div class="form-group full-width">
                                <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
                                    <div class="upload-icon">📁</div>
                                    <div class="upload-text">点击上传文件或拖拽文件到此区域</div>
                                    <div class="upload-hint">支持多文件上传，单个文件不超过10MB</div>
                                </div>
                                <input type="file" id="fileInput" class="file-input" multiple 
                                       accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif">
                                <div class="file-list" id="fileList"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="form-actions">
                    <button type="button" class="btn btn-success" onclick="saveNotice()">保存</button>
                    <button type="button" class="btn btn-primary" onclick="saveAndPublish()">保存并发布</button>
                    <button type="button" class="btn btn-default" onclick="cancelEdit()">取消</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
            initializeFileUpload();
            initializeEditor();
            initializeValidation();
        });

        // 初始化页面
        function initializePage() {
            const urlParams = new URLSearchParams(window.location.search);
            const mode = urlParams.get('mode');
            const id = urlParams.get('id');
            
            if (mode === 'edit' && id) {
                document.getElementById('pageTitle').textContent = '编辑通知公告';
                document.getElementById('breadcrumbTitle').textContent = '编辑';
                loadNoticeData(id);
            }
        }

        // 加载通知公告数据（编辑模式）
        function loadNoticeData(id) {
            // 模拟加载数据
            const mockData = {
                title: '关于加强招标采购管理的通知',
                content: '<p>为进一步规范招标采购活动，提高采购效率和质量，现就有关事项通知如下：</p><p>一、严格执行招标采购程序</p><p>二、加强供应商资质审核</p><p>三、完善评标专家管理</p>',
                files: [
                    { name: '招标采购管理办法.pdf', size: '2.5MB', type: 'pdf' },
                    { name: '供应商资质要求.docx', size: '1.2MB', type: 'doc' }
                ]
            };
            
            document.getElementById('title').value = mockData.title;
            document.getElementById('content').innerHTML = mockData.content;
            
            // 加载附件
            mockData.files.forEach(file => {
                addFileToList(file);
            });
            
            updateCharCount();
        }

        // 初始化文件上传
        function initializeFileUpload() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            
            // 拖拽上传
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                handleFiles(files);
            });
            
            // 文件选择
            fileInput.addEventListener('change', function(e) {
                handleFiles(e.target.files);
            });
        }

        // 处理文件
        function handleFiles(files) {
            Array.from(files).forEach(file => {
                if (validateFile(file)) {
                    addFileToList({
                        name: file.name,
                        size: formatFileSize(file.size),
                        type: getFileType(file.name),
                        file: file
                    });
                }
            });
        }

        // 验证文件
        function validateFile(file) {
            const maxSize = 10 * 1024 * 1024; // 10MB
            const allowedTypes = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.jpg', '.jpeg', '.png', '.gif'];
            
            if (file.size > maxSize) {
                alert('文件大小不能超过10MB');
                return false;
            }
            
            const fileExt = '.' + file.name.split('.').pop().toLowerCase();
            if (!allowedTypes.includes(fileExt)) {
                alert('不支持的文件类型');
                return false;
            }
            
            return true;
        }

        // 添加文件到列表
        function addFileToList(fileData) {
            const fileList = document.getElementById('fileList');
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-info">
                    <div class="file-icon">${getFileIcon(fileData.type)}</div>
                    <div>
                        <div class="file-name">${fileData.name}</div>
                        <div class="file-size">${fileData.size}</div>
                    </div>
                </div>
                <div class="file-actions">
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeFile(this)">删除</button>
                </div>
            `;
            fileList.appendChild(fileItem);
        }

        // 获取文件类型
        function getFileType(fileName) {
            const ext = fileName.split('.').pop().toLowerCase();
            if (['pdf'].includes(ext)) return 'pdf';
            if (['doc', 'docx'].includes(ext)) return 'doc';
            if (['xls', 'xlsx'].includes(ext)) return 'xls';
            if (['ppt', 'pptx'].includes(ext)) return 'ppt';
            if (['jpg', 'jpeg', 'png', 'gif'].includes(ext)) return 'img';
            return 'file';
        }

        // 获取文件图标
        function getFileIcon(type) {
            const icons = {
                pdf: 'PDF',
                doc: 'DOC',
                xls: 'XLS',
                ppt: 'PPT',
                img: 'IMG',
                file: 'FILE'
            };
            return icons[type] || 'FILE';
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 删除文件
        function removeFile(button) {
            button.closest('.file-item').remove();
        }

        // 初始化编辑器
        function initializeEditor() {
            const editor = document.getElementById('content');
            
            // 设置占位符行为
            editor.addEventListener('focus', function() {
                if (this.textContent.trim() === '') {
                    this.innerHTML = '';
                }
            });
            
            editor.addEventListener('blur', function() {
                if (this.textContent.trim() === '') {
                    this.innerHTML = '';
                }
            });
        }

        // 格式化文本
        function formatText(command, value = null) {
            document.execCommand(command, false, value);
            document.getElementById('content').focus();
        }

        // 插入链接
        function insertLink() {
            const url = prompt('请输入链接地址:');
            if (url) {
                formatText('createLink', url);
            }
        }

        // 初始化验证
        function initializeValidation() {
            const titleInput = document.getElementById('title');
            titleInput.addEventListener('input', updateCharCount);
            titleInput.addEventListener('blur', validateTitle);
        }

        // 更新字符计数
        function updateCharCount() {
            const titleInput = document.getElementById('title');
            const count = titleInput.value.length;
            const countElement = document.getElementById('titleCount');
            countElement.textContent = count;
            
            if (count > 45) {
                countElement.parentElement.className = 'char-count warning';
            } else if (count > 50) {
                countElement.parentElement.className = 'char-count error';
            } else {
                countElement.parentElement.className = 'char-count';
            }
        }

        // 验证标题
        function validateTitle() {
            const titleInput = document.getElementById('title');
            const errorElement = document.getElementById('titleError');
            
            if (!titleInput.value.trim()) {
                titleInput.classList.add('error');
                errorElement.textContent = '请输入标题名称';
                return false;
            } else if (titleInput.value.length > 50) {
                titleInput.classList.add('error');
                errorElement.textContent = '标题名称不能超过50个字符';
                return false;
            } else {
                titleInput.classList.remove('error');
                errorElement.textContent = '';
                return true;
            }
        }

        // 验证内容
        function validateContent() {
            const contentElement = document.getElementById('content');
            const errorElement = document.getElementById('contentError');
            
            if (!contentElement.textContent.trim()) {
                contentElement.style.border = '1px solid #ed4014';
                errorElement.textContent = '请输入通知公告说明';
                return false;
            } else {
                contentElement.style.border = '';
                errorElement.textContent = '';
                return true;
            }
        }

        // 验证表单
        function validateForm() {
            const titleValid = validateTitle();
            const contentValid = validateContent();
            
            return titleValid && contentValid;
        }

        // 保存通知公告
        function saveNotice() {
            if (!validateForm()) {
                alert('请检查表单填写是否正确');
                return;
            }
            
            const formData = {
                title: document.getElementById('title').value,
                content: document.getElementById('content').innerHTML,
                files: getUploadedFiles(),
                status: '待发布'
            };
            
            console.log('保存通知公告:', formData);
            alert('保存成功！');
            window.location.href = '通知公告管理-列表页.html';
        }

        // 保存并发布
        function saveAndPublish() {
            if (!validateForm()) {
                alert('请检查表单填写是否正确');
                return;
            }
            
            if (confirm('确认保存并发布该通知公告吗？')) {
                const formData = {
                    title: document.getElementById('title').value,
                    content: document.getElementById('content').innerHTML,
                    files: getUploadedFiles(),
                    status: '已发布'
                };
                
                console.log('保存并发布通知公告:', formData);
                alert('保存并发布成功！');
                window.location.href = '通知公告管理-列表页.html';
            }
        }

        // 获取上传的文件
        function getUploadedFiles() {
            const fileItems = document.querySelectorAll('.file-item');
            return Array.from(fileItems).map(item => {
                return {
                    name: item.querySelector('.file-name').textContent,
                    size: item.querySelector('.file-size').textContent
                };
            });
        }

        // 取消编辑
        function cancelEdit() {
            if (confirm('确认取消编辑吗？未保存的内容将丢失。')) {
                window.location.href = '通知公告管理-列表页.html';
            }
        }
    </script>
</body>
</html>