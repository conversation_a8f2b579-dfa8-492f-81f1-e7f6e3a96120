<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>签约履行管理 - 详情 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #fff;
            border-bottom: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
        }

        .help-icon {
            margin-left: 8px;
            width: 16px;
            height: 16px;
            background: #6b7280;
            border-radius: 50%;
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .breadcrumb {
            color: #6b7280;
            font-size: 14px;
        }

        .back-btn {
            padding: 8px 16px;
            background: #6b7280;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: background 0.3s;
        }

        .back-btn:hover {
            background: #4b5563;
        }

        /* 内容区域 */
        .content-area {
            padding: 24px;
        }

        /* 页签导航 */
        .tab-nav {
            border-bottom: 1px solid #e6e8eb;
            margin-bottom: 24px;
        }

        .tab-list {
            display: flex;
            list-style: none;
        }

        .tab-item {
            margin-right: 32px;
        }

        .tab-link {
            display: block;
            padding: 12px 0;
            color: #6b7280;
            text-decoration: none;
            font-size: 14px;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab-link.active {
            color: #2563eb;
            border-bottom-color: #2563eb;
        }

        .tab-link:hover {
            color: #2563eb;
        }

        /* 页签内容 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 详情区域 */
        .detail-section {
            background: white;
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .section-header {
            padding: 16px 20px;
            border-bottom: 1px solid #e6e8eb;
            background: #f8fafc;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            display: flex;
            align-items: center;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 16px;
            background: #3b82f6;
            margin-right: 8px;
        }

        .section-content {
            padding: 20px;
        }

        /* 详情网格 */
        .detail-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-item.full-width {
            grid-column: 1 / -1;
        }

        .detail-label {
            font-size: 13px;
            color: #6b7280;
            margin-bottom: 6px;
            font-weight: 500;
        }

        .detail-value {
            font-size: 14px;
            color: #1f2937;
            word-break: break-all;
        }

        .detail-value.rich-content {
            line-height: 1.8;
        }

        .detail-value.rich-content h1,
        .detail-value.rich-content h2,
        .detail-value.rich-content h3 {
            margin: 16px 0 8px 0;
            color: #1f2937;
        }

        .detail-value.rich-content h1 {
            font-size: 18px;
        }

        .detail-value.rich-content h2 {
            font-size: 16px;
        }

        .detail-value.rich-content h3 {
            font-size: 14px;
        }

        .detail-value.rich-content p {
            margin: 8px 0;
        }

        .detail-value.rich-content ul,
        .detail-value.rich-content ol {
            margin: 8px 0;
            padding-left: 24px;
        }

        .detail-value.rich-content li {
            margin: 4px 0;
        }

        .detail-value.rich-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 12px 0;
        }

        .detail-value.rich-content table th,
        .detail-value.rich-content table td {
            border: 1px solid #e6e8eb;
            padding: 8px 12px;
            text-align: left;
        }

        .detail-value.rich-content table th {
            background: #f8fafc;
            font-weight: 600;
        }

        .detail-value.rich-content .signature {
            text-align: right;
            margin-top: 20px;
            font-style: italic;
        }

        /* 状态标签 */
        .status-tag {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-draft {
            background: #f3f4f6;
            color: #374151;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-approved {
            background: #d1fae5;
            color: #065f46;
        }

        .status-published {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-rejected {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-withdrawn {
            background: #f3f4f6;
            color: #6b7280;
        }

        /* 文件列表 */
        .file-list {
            margin-top: 12px;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: #f8fafc;
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            margin-bottom: 8px;
            transition: all 0.3s;
        }

        .file-item:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .file-icon {
            width: 32px;
            height: 32px;
            background: #3b82f6;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        .file-details {
            display: flex;
            flex-direction: column;
        }

        .file-name {
            font-size: 14px;
            color: #1f2937;
            font-weight: 500;
        }

        .file-meta {
            font-size: 12px;
            color: #6b7280;
            margin-top: 2px;
        }

        .file-download {
            padding: 6px 12px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }

        .file-download:hover {
            background: #2563eb;
        }

        /* 操作记录时间线 */
        .operation-timeline {
            position: relative;
            padding-left: 24px;
        }

        .operation-timeline::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e6e8eb;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 24px;
            padding-left: 24px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -8px;
            top: 6px;
            width: 12px;
            height: 12px;
            background: #3b82f6;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 0 0 2px #e6e8eb;
        }

        .timeline-item.completed::before {
            background: #10b981;
        }

        .timeline-item.rejected::before {
            background: #ef4444;
        }

        .timeline-content {
            background: #f8fafc;
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            padding: 16px;
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .timeline-title {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
        }

        .timeline-time {
            font-size: 12px;
            color: #6b7280;
        }

        .timeline-user {
            font-size: 13px;
            color: #374151;
            margin-bottom: 4px;
        }

        .timeline-desc {
            font-size: 13px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* 操作记录表格 */
        .record-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }

        .record-table th,
        .record-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e6e8eb;
            font-size: 13px;
        }

        .record-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
        }

        .record-table tbody tr:hover {
            background: #f9fafb;
        }

        /* 操作按钮 */
        .action-bar {
            background: #f8fafc;
            border-top: 1px solid #e6e8eb;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            bottom: 0;
        }

        .btn-group {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-outline {
            background: white;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e6e8eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .help-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            color: #6b7280;
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            color: #374151;
        }

        .help-body {
            padding: 24px;
        }

        .help-section {
            margin-bottom: 24px;
        }

        .help-section h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
        }

        .help-section p {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 12px;
        }

        .help-list {
            list-style: none;
            padding-left: 0;
        }

        .help-list li {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 8px;
            padding-left: 16px;
            position: relative;
        }

        .help-list li::before {
            content: '•';
            color: #3b82f6;
            position: absolute;
            left: 0;
        }

        .help-list strong {
            color: #1f2937;
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .detail-grid {
                grid-template-columns: 1fr;
            }
            
            .btn-group {
                flex-direction: column;
            }
            
            .action-bar {
                flex-direction: column;
                gap: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">
                    签约履行管理 - 详情
                    <span class="help-icon" onclick="showHelp()" title="功能说明">?</span>
                </h1>
                <div class="breadcrumb">首页 > 签约履行管理 > 详情</div>
            </div>
            <button class="back-btn" onclick="goBack()">返回列表</button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 页签导航 -->
            <div class="tab-nav">
                <ul class="tab-list">
                    <li class="tab-item">
                        <a href="#" class="tab-link active" onclick="switchTab('contract')">履行详情</a>
                    </li>
                    <li class="tab-item">
                        <a href="#" class="tab-link" onclick="switchTab('project')">项目信息</a>
                    </li>
                    <li class="tab-item">
                        <a href="#" class="tab-link" onclick="switchTab('operation')">操作记录</a>
                    </li>
                </ul>
            </div>

            <!-- 履行详情页签 -->
            <div id="contract-tab" class="tab-content active">
                <!-- 基本信息 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">基本信息</h3>
                    </div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">签约履行标题</div>
                                <div class="detail-value">办公设备采购项目签约履行</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">项目编号</div>
                                <div class="detail-value">ZB2024001</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">项目名称</div>
                                <div class="detail-value">办公设备采购项目</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">项目业主</div>
                                <div class="detail-value">某某单位</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购方式</div>
                                <div class="detail-value">公开招标</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购类型</div>
                                <div class="detail-value">货物</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购金额</div>
                                <div class="detail-value">50.00万元</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">中标金额</div>
                                <div class="detail-value">45.50万元</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">创建时间</div>
                                <div class="detail-value">2024-01-10 14:30:25</div>
                            </div>
                        </div>
                        <div class="detail-item full-width">
                            <div class="detail-label">项目基本情况（建设内容及规模）</div>
                            <div class="detail-value">本项目为办公设备采购，包括计算机、打印机、复印机等办公设备的采购和安装，预计采购数量约100台套，用于满足日常办公需求。项目总投资50万元，建设周期3个月，建成后将大幅提升办公效率和信息化水平。</div>
                        </div>
                    </div>
                </div>

                <!-- 履约信息 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">履约信息</h3>
                    </div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">履约单位</div>
                                <div class="detail-value">XX科技有限公司</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">是否签订合同</div>
                                <div class="detail-value">
                                    <span class="status-tag status-approved">是</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 合同信息 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">合同信息</h3>
                    </div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">合同名称</div>
                                <div class="detail-value">办公设备采购合同</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">合同状态</div>
                                <div class="detail-value">
                                    <span class="status-tag status-pending">履行中</span>
                                </div>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">合同附件</div>
                            <div class="detail-value">
                                <div class="file-list">
                                    <div class="file-item">
                                        <div class="file-info">
                                            <span class="file-name">📄 采购合同.pdf</span>
                                            <span class="file-size">(2.5 MB)</span>
                                        </div>
                                        <a href="#" class="file-download">下载</a>
                                    </div>
                                    <div class="file-item">
                                        <div class="file-info">
                                            <span class="file-name">📄 合同附件.docx</span>
                                            <span class="file-size">(1.2 MB)</span>
                                        </div>
                                        <a href="#" class="file-download">下载</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 履约详情 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">履约详情</h3>
                    </div>
                    <div class="section-content">
                        <div class="detail-item full-width">
                            <div class="detail-value rich-content">
                                <h2>一、合同基本信息</h2>
                                <p>本合同为办公设备采购项目签约履行，合同编号：HT-2024-001，合同金额：45.50万元。中标供应商为XX科技有限公司，合同签订时间为2024年1月15日，计划完成时间为2024年4月15日。</p>
                                
                                <h2>二、履行内容及要求</h2>
                                <ol>
                                    <li>按照合同约定的技术规格和质量标准提供办公设备；</li>
                                    <li>确保所有设备符合国家相关标准和行业规范；</li>
                                    <li>提供设备安装、调试和培训服务；</li>
                                    <li>提供不少于3年的质保服务和技术支持；</li>
                                    <li>按照约定的时间节点完成设备交付和验收。</li>
                                </ol>
                                
                                <h2>三、履行进度安排</h2>
                                <p><strong>第一阶段：</strong>设备采购和生产（2024年1月15日-2024年2月29日）</p>
                                <p><strong>第二阶段：</strong>设备交付和安装（2024年3月1日-2024年3月31日）</p>
                                <p><strong>第三阶段：</strong>系统调试和培训（2024年4月1日-2024年4月10日）</p>
                                <p><strong>第四阶段：</strong>验收和交付（2024年4月11日-2024年4月15日）</p>
                                
                                <h2>四、质量控制措施</h2>
                                <p><strong>质量标准：</strong>严格按照合同技术规格执行，确保设备质量</p>
                                <p><strong>检验方式：</strong>分阶段验收，最终统一验收</p>
                                <p><strong>质保期限：</strong>3年质保，提供免费维修和技术支持</p>
                                
                                <h2>五、联系方式</h2>
                                <p><strong>采购方：</strong>市政府办公室</p>
                                <p><strong>联系人：</strong>张先生</p>
                                <p><strong>电话：</strong>0755-12345678</p>
                                <p><strong>供应商：</strong>XX科技有限公司</p>
                                <p><strong>联系人：</strong>李经理</p>
                                <p><strong>电话：</strong>0755-87654321</p>
                                
                                <div class="signature">
                                    <p>市政府采购中心</p>
                                    <p>2024年1月15日</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 合同文件 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">合同文件</h3>
                    </div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">合同编号</div>
                                <div class="detail-value">HT-2024-001</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">合同类型</div>
                                <div class="detail-value">货物采购合同</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">签订地点</div>
                                <div class="detail-value">市政府采购中心（市政府大楼3楼）</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">合同金额</div>
                                <div class="detail-value">45.50万元</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">履行期限</div>
                                <div class="detail-value">2024年1月15日至2024年4月15日</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">质保期</div>
                                <div class="detail-value">3年</div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>

            <!-- 项目信息页签 -->
            <div id="project-tab" class="tab-content">
                <!-- 项目信息 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">项目信息</h3>
                    </div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">项目名称</div>
                                <div class="detail-value">办公设备采购项目</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">项目标题</div>
                                <div class="detail-value">市政府办公室办公设备采购项目</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">项目业主</div>
                                <div class="detail-value">市政府办公室</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">采购方式</div>
                                <div class="detail-value">公开招标</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购类别</div>
                                <div class="detail-value">货物采购</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">所属二级公司单位</div>
                                <div class="detail-value">市政府采购中心</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">立项决策日期</div>
                                <div class="detail-value">2024-01-03</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">创建时间</div>
                                <div class="detail-value">2024-01-05 09:00:00</div>
                            </div>
                            <div class="detail-item">
                                <!-- 空位保持布局对齐 -->
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">项目基本情况</div>
                                <div class="detail-value">为满足市政府办公室日常办公需要，采购一批办公设备，包括电脑、打印机、复印机等设备，要求设备性能稳定、质量可靠，符合政府采购相关规定。项目预算金额50万元，采用公开招标方式进行采购，确保采购过程公开、公平、公正。</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 关联标段信息 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">关联标段信息</h3>
                    </div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">标段编号</div>
                                <div class="detail-value">BD-2024-001-01</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">标段名称</div>
                                <div class="detail-value">办公设备采购项目第一标段</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">是否公示</div>
                                <div class="detail-value">
                                    <span class="status-tag status-published">是</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">标段阶段</div>
                                <div class="detail-value">合同履行阶段</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">标段状态</div>
                                <div class="detail-value">
                                    <span class="status-tag status-pending">履行中</span>
                                </div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">采购金额</div>
                                <div class="detail-value">45.50万元</div>
                            </div>
                        </div>
                        
                        <div class="detail-grid full-width">
                            <div class="detail-item">
                                <div class="detail-label">标段说明</div>
                                <div class="detail-value">本标段主要采购办公电脑、打印机、复印机等设备，要求设备符合国家标准，具备良好的性能和稳定性。中标供应商为XX科技有限公司，中标金额45.50万元，合同履行期为3个月。</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 采购方信息 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">采购方信息</h3>
                    </div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">单位名称</div>
                                <div class="detail-value">市政府办公室</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">联系人</div>
                                <div class="detail-value">张先生</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">联系电话</div>
                                <div class="detail-value">0755-12345678</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">传真</div>
                                <div class="detail-value">0755-12345679</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">邮箱</div>
                                <div class="detail-value"><EMAIL></div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">地址</div>
                                <div class="detail-value">市政府大楼5楼办公室</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 供应商信息 -->
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">供应商信息</h3>
                    </div>
                    <div class="section-content">
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">公司名称</div>
                                <div class="detail-value">XX科技有限公司</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">联系人</div>
                                <div class="detail-value">李经理</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">联系电话</div>
                                <div class="detail-value">0755-87654321</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">传真</div>
                                <div class="detail-value">0755-87654322</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">邮箱</div>
                                <div class="detail-value"><EMAIL></div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">地址</div>
                                <div class="detail-value">高新技术产业园区A座15楼</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作记录页签 -->
            <div id="operation-tab" class="tab-content">
                <div class="detail-section">
                    <div class="section-header">
                        <h3 class="section-title">操作记录</h3>
                    </div>
                    <div class="section-content">
                        <div class="operation-timeline">
                            <div class="timeline-item completed">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-title">履行记录发布</div>
                                        <div class="timeline-time">2024-01-15 09:00:00</div>
                                    </div>
                                    <div class="timeline-user">操作人：系统管理员</div>
                                    <div class="timeline-desc">履行记录已成功发布，相关方可以查看合同履行进度和详情。</div>
                                </div>
                            </div>
                            
                            <div class="timeline-item completed">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-title">审核通过</div>
                                        <div class="timeline-time">2024-01-14 16:30:00</div>
                                    </div>
                                    <div class="timeline-user">操作人：审核员 - 李主任</div>
                                    <div class="timeline-desc">履行记录审核通过，符合合同履行相关规定，准予发布。审核意见：履行内容完整，进度安排合理，可以发布。</div>
                                </div>
                            </div>
                            
                            <div class="timeline-item completed">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-title">部门审批</div>
                                        <div class="timeline-time">2024-01-12 14:20:00</div>
                                    </div>
                                    <div class="timeline-user">操作人：部门负责人 - 王部长</div>
                                    <div class="timeline-desc">部门内部审批通过，同意提交上级审核。审批意见：履行方案可行，时间安排合理，同意按计划执行。</div>
                                </div>
                            </div>
                            
                            <div class="timeline-item completed">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-title">提交审批</div>
                                        <div class="timeline-time">2024-01-11 10:15:00</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三</div>
                                    <div class="timeline-desc">履行记录编辑完成，已提交部门负责人审批。</div>
                                </div>
                            </div>
                            
                            <div class="timeline-item completed">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-title">编辑履行记录</div>
                                        <div class="timeline-time">2024-01-10 16:45:00</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三</div>
                                    <div class="timeline-desc">修改了履行记录内容，完善了履行要求和验收标准。</div>
                                </div>
                            </div>
                            
                            <div class="timeline-item completed">
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-title">创建履行记录</div>
                                        <div class="timeline-time">2024-01-10 14:30:00</div>
                                    </div>
                                    <div class="timeline-user">操作人：张三</div>
                                    <div class="timeline-desc">创建了办公设备采购项目签约履行记录，填写了基本信息和履行详情。</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-bar">
            <div class="btn-group">
                <button class="btn btn-secondary" onclick="goBack()">返回列表</button>
            </div>
            <div class="btn-group">
                <button class="btn btn-outline" onclick="printAnnouncement()">打印公告</button>
                <button class="btn btn-success" onclick="editAnnouncement()">编辑</button>
                <button class="btn btn-warning" onclick="withdrawAnnouncement()">撤回</button>
                <button class="btn btn-danger" onclick="deleteAnnouncement()">删除</button>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="help-modal" class="help-modal">
        <div class="help-content">
            <div class="help-header">
                <h3 class="help-title">签约履行详情功能说明</h3>
                <button class="close-btn" onclick="hideHelp()">&times;</button>
            </div>
            <div class="help-body">
                <div class="help-section">
                    <h4>功能概述</h4>
                    <p>签约履行详情页面用于查看合同履行的完整信息，包括基本信息、履行详情、关联标段、履行进度和操作记录。支持履行记录的编辑、撤回、删除等操作。</p>
                </div>
                
                <div class="help-section">
                    <h4>页签说明</h4>
                    <ul class="help-list">
                        <li><strong>履行详情：</strong>显示合同的基本信息、履行内容、关联标段、验收安排等</li>
                        <li><strong>履行进度：</strong>显示履行进度概览、阶段详情、相关文件等</li>
                        <li><strong>操作记录：</strong>显示履行记录的创建、编辑、审核、发布等操作历史</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>操作说明</h4>
                    <ul class="help-list">
                        <li><strong>打印履行记录：</strong>打印履行详情内容</li>
                        <li><strong>编辑：</strong>修改履行信息（仅草稿和已驳回状态可编辑）</li>
                        <li><strong>撤回：</strong>撤回已发布的履行记录</li>
                        <li><strong>删除：</strong>删除履行记录（仅草稿状态可删除）</li>
                        <li><strong>返回列表：</strong>返回签约履行管理列表页面</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>状态说明</h4>
                    <ul class="help-list">
                        <li><strong>草稿：</strong>履行记录已创建但未提交审核</li>
                        <li><strong>待审核：</strong>履行记录已提交，等待审核</li>
                        <li><strong>已审核：</strong>履行记录审核通过，等待发布</li>
                        <li><strong>履行中：</strong>合同正在履行中</li>
                        <li><strong>已完成：</strong>合同履行已完成</li>
                        <li><strong>已驳回：</strong>履行记录审核未通过，需要修改</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>注意事项</h4>
                    <ul class="help-list">
                        <li>履行记录撤回后需要重新审核才能再次发布</li>
                        <li>删除操作不可恢复，请谨慎操作</li>
                        <li>操作记录会详细记录每次操作的时间、人员和内容</li>
                        <li>文件下载功能需要相应的权限</li>
                        <li>关联标段信息来源于中标结果，不可直接修改</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页签切换
        function switchTab(tabName) {
            // 移除所有页签的激活状态
            document.querySelectorAll('.tab-link').forEach(link => {
                link.classList.remove('active');
            });
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 激活当前页签
            event.target.classList.add('active');
            document.getElementById(tabName + '-tab').classList.add('active');
        }

        // 帮助弹窗
        function showHelp() {
            document.getElementById('help-modal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('help-modal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });

        // 业务操作函数
        function goBack() {
            window.location.href = '签约履行管理-列表页.html';
        }

        function editAnnouncement() {
            window.location.href = '签约履行管理-新建编辑页.html?id=1';
        }

        function withdrawAnnouncement() {
            if (confirm('确定要撤回这个履行记录吗？撤回后需要重新审核才能发布。')) {
                console.log('撤回履行记录');
                alert('履行记录撤回成功！');
                // 这里应该调用撤回接口，并刷新页面状态
            }
        }

        function deleteAnnouncement() {
            if (confirm('确定要删除这个履行记录吗？删除后无法恢复。')) {
                console.log('删除履行记录');
                alert('履行记录删除成功！');
                window.location.href = '签约履行管理-列表页.html';
            }
        }

        function printAnnouncement() {
            console.log('打印履行记录');
            window.print();
        }

        function downloadFile(filename) {
            console.log('下载文件:', filename);
            alert('文件下载功能待实现');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('签约履行详情页面加载完成');
        });
    </script>
</body>
</html>