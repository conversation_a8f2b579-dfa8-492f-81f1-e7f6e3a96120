<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .stats-overview {
            margin-bottom: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 20px;
        }
        
        .stat-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 20px;
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        
        .stat-content {
            text-align: center;
        }
        
        .stat-value {
            font-size: 28px;
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 8px;
            line-height: 1.2;
        }
        
        .stat-label {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            margin-bottom: 6px;
        }
        
        .stat-trend {
            font-size: 12px;
            color: #52c41a;
            font-weight: 500;
        }
        
        @media (max-width: 1200px) {
            .stats-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
        
        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-top: 20px;
        }
        
        .analysis-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .card-header {
            background: #fafafa;
            border-bottom: 1px solid #e8e8e8;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        
        .card-actions {
            display: flex;
            gap: 8px;
        }
        
        .card-btn {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }
        
        .card-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .card-content {
            padding: 20px;
        }
        
        .chart-filter {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .filter-group label {
            font-size: 13px;
            color: #666;
            white-space: nowrap;
        }
        
        .filter-select {
            padding: 6px 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 13px;
            min-width: 120px;
        }
        
        .filter-btn {
            padding: 6px 15px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: background 0.3s;
        }
        
        .filter-btn:hover {
            background: #40a9ff;
        }
        
        .chart-container {
            margin-bottom: 20px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
        }
        
        .data-table th {
            background: #fafafa;
            font-weight: bold;
            color: #333;
        }
        
        .data-table tr:hover {
            background: #f5f5f5;
        }
        
        .axis-label {
            font-size: 11px;
            fill: #666;
        }
        
        .chart-title {
            font-size: 14px;
            font-weight: bold;
            fill: #333;
        }
        
        .legend {
            font-size: 12px;
        }
        
        .legend-text {
            font-size: 12px;
            fill: #666;
        }
        
        .pie-slice {
            cursor: pointer;
            transition: opacity 0.3s;
        }
        
        .pie-slice:hover {
            opacity: 0.8;
        }
        
        .bar {
            cursor: pointer;
            transition: opacity 0.3s;
        }
        
        .bar:hover {
            opacity: 0.8;
        }
        
        .bar-label {
            font-size: 11px;
            fill: #666;
        }
        
        .bar-value {
            font-size: 11px;
            fill: #333;
            font-weight: bold;
        }
        
        .line-value {
            font-size: 10px;
            fill: #333;
            font-weight: bold;
        }
        
        .performance-line {
            stroke-dasharray: 5,5;
        }
        
        .radar-area {
            cursor: pointer;
            transition: opacity 0.3s;
        }
        
        .radar-area:hover {
            opacity: 0.6;
        }
        
        .radar-label {
            font-size: 12px;
            fill: #333;
            font-weight: bold;
        }
        
        .center-value {
            font-size: 18px;
            font-weight: bold;
            fill: #333;
        }
        
        .center-text {
            font-size: 12px;
            fill: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 指标统计区域 -->
        <div class="stats-overview">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-content">
                        <div class="stat-value">1,256</div>
                        <div class="stat-label">总项目数</div>
                        <div class="stat-trend">+12.5% 较上月</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-content">
                        <div class="stat-value">2,485</div>
                        <div class="stat-label">总标段数</div>
                        <div class="stat-trend">+8.3% 较上月</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-content">
                        <div class="stat-value">2,156</div>
                        <div class="stat-label">已完成标段数</div>
                        <div class="stat-trend">+15.2% 较上月</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-content">
                        <div class="stat-value">125.68亿</div>
                        <div class="stat-label">预算金额</div>
                        <div class="stat-trend">+6.8% 较上月</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-content">
                        <div class="stat-value">118.45亿</div>
                        <div class="stat-label">采购金额</div>
                        <div class="stat-trend">+5.9% 较上月</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-content">
                        <div class="stat-value">112.36亿</div>
                        <div class="stat-label">中标金额</div>
                        <div class="stat-trend">+4.2% 较上月</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="analysis-grid">
            <!-- 采购类型指标数据分析 -->
            <div class="analysis-card">
                <div class="card-header">
                    <div class="card-title">📊 采购类型指标数据分析</div>
                    <div class="card-actions">
                        <button class="card-btn" onclick="switchChartType('category', 'pie')">饼图</button>
                        <button class="card-btn" onclick="switchChartType('category', 'bar')">柱图</button>
                        <button class="card-btn" onclick="exportChart('category')">导出</button>
                    </div>
                </div>
                <!-- 独立查询条件 -->
                <div class="chart-filter">
                    <div class="filter-group">
                        <label>采购类型:</label>
                        <select class="filter-select">
                            <option value="all" selected>全部类型</option>
                            <option value="goods">货物</option>
                            <option value="construction">施工</option>
                            <option value="service">服务</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>时间范围:</label>
                        <select class="filter-select">
                            <option value="quarter" selected>本季度</option>
                            <option value="month">本月</option>
                            <option value="year">本年度</option>
                            <option value="custom">自定义</option>
                        </select>
                    </div>
                    <button class="filter-btn" onclick="filterChart('category')">应用筛选</button>
                </div>
                <div class="card-content">
                    <div class="chart-container">
                        <svg class="category-chart" width="100%" height="350" viewBox="0 0 500 350">
                            <!-- 饼图 -->
                            <g transform="translate(180,175)">
                                <!-- 货物采购 (45%) -->
                                <path d="M 0,-120 A 120,120 0 0,1 84.85,84.85 L 0,0 Z" 
                                      fill="#1890ff" class="pie-slice" data-label="货物" data-value="45%"/>
                                <!-- 施工 (30%) -->
                                <path d="M 84.85,84.85 A 120,120 0 0,1 -37.08,113.14 L 0,0 Z" 
                                      fill="#52c41a" class="pie-slice" data-label="施工" data-value="30%"/>
                                <!-- 服务 (20%) -->
                                <path d="M -37.08,113.14 A 120,120 0 0,1 -120,0 L 0,0 Z" 
                                      fill="#faad14" class="pie-slice" data-label="服务" data-value="20%"/>
                                <!-- 其他 (5%) -->
                                <path d="M -120,0 A 120,120 0 0,1 0,-120 L 0,0 Z" 
                                      fill="#722ed1" class="pie-slice" data-label="其他" data-value="5%"/>
                                
                                <!-- 百分比标签 -->
                                <text x="42" y="-42" text-anchor="middle" class="pie-label" style="font-size: 12px; fill: white; font-weight: bold;">45%</text>
                                <text x="24" y="99" text-anchor="middle" class="pie-label" style="font-size: 12px; fill: white; font-weight: bold;">30%</text>
                                <text x="-79" y="57" text-anchor="middle" class="pie-label" style="font-size: 12px; fill: white; font-weight: bold;">20%</text>
                                <text x="-60" y="-60" text-anchor="middle" class="pie-label" style="font-size: 12px; fill: white; font-weight: bold;">5%</text>
                            </g>
                            
                            <!-- 图例 -->
                            <g class="legend" transform="translate(350,120)">
                                <g transform="translate(0,0)">
                                    <rect x="0" y="0" width="12" height="12" fill="#1890ff"/>
                                    <text x="18" y="10" class="legend-text">货物 (45%)</text>
                                </g>
                                <g transform="translate(0,25)">
                                    <rect x="0" y="0" width="12" height="12" fill="#52c41a"/>
                                    <text x="18" y="10" class="legend-text">施工 (30%)</text>
                                </g>
                                <g transform="translate(0,50)">
                                    <rect x="0" y="0" width="12" height="12" fill="#faad14"/>
                                    <text x="18" y="10" class="legend-text">服务 (20%)</text>
                                </g>
                                <g transform="translate(0,75)">
                                    <rect x="0" y="0" width="12" height="12" fill="#722ed1"/>
                                    <text x="18" y="10" class="legend-text">其他 (5%)</text>
                                </g>
                            </g>
                            
                            <!-- 图表标题 -->
                            <text x="250" y="25" text-anchor="middle" class="chart-title">采购类型分布占比</text>
                        </svg>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>采购类型</th>
                                <th>类型占比</th>
                                <th>预算金额(万元)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>货物</td>
                                <td>45%</td>
                                <td>45,680</td>
                            </tr>
                            <tr>
                                <td>施工</td>
                                <td>30%</td>
                                <td>30,450</td>
                            </tr>
                            <tr>
                                <td>服务</td>
                                <td>20%</td>
                                <td>20,300</td>
                            </tr>
                            <tr>
                                <td>其他</td>
                                <td>5%</td>
                                <td>5,075</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 采购方式指标数据分析 -->
            <div class="analysis-card">
                <div class="card-header">
                    <div class="card-title">📈 采购方式指标数据分析</div>
                </div>
                <!-- 独立查询条件 -->
                <div class="chart-filter">
                    <div class="filter-group">
                        <label>起止日期:</label>
                        <input type="date" class="filter-select" value="2024-01-01" style="width: 140px;">
                        <span style="margin: 0 8px;">至</span>
                        <input type="date" class="filter-select" value="2024-12-31" style="width: 140px;">
                    </div>
                    <button class="filter-btn" onclick="filterChart('method')">应用筛选</button>
                </div>
                <div class="card-content">
                    <div class="chart-container">
                        <svg class="method-chart" width="100%" height="350" viewBox="0 0 500 350">
                            <!-- 背景网格 -->
                            <defs>
                                <pattern id="grid2" width="50" height="30" patternUnits="userSpaceOnUse">
                                    <path d="M 50 0 L 0 0 0 30" fill="none" stroke="#f0f0f0" stroke-width="1"/>
                                </pattern>
                            </defs>
                            <rect width="400" height="240" x="80" y="50" fill="url(#grid2)"/>
                            
                            <!-- Y轴 -->
                            <line x1="80" y1="50" x2="80" y2="290" stroke="#d9d9d9" stroke-width="2"/>
                            <!-- X轴 -->
                            <line x1="80" y1="290" x2="480" y2="290" stroke="#d9d9d9" stroke-width="2"/>
                            
                            <!-- Y轴标签 -->
                            <text x="70" y="55" text-anchor="end" class="axis-label">200</text>
                            <text x="70" y="95" text-anchor="end" class="axis-label">160</text>
                            <text x="70" y="135" text-anchor="end" class="axis-label">120</text>
                            <text x="70" y="175" text-anchor="end" class="axis-label">80</text>
                            <text x="70" y="215" text-anchor="end" class="axis-label">40</text>
                            <text x="70" y="295" text-anchor="end" class="axis-label">0</text>
                            
                            <!-- 柱状图 -->
                            <!-- 公告比选 -->
                            <rect x="90" y="80" width="45" height="210" fill="#1890ff" class="bar" data-label="公告比选" data-value="185个项目"/>
                            <text x="112" y="310" text-anchor="middle" class="bar-label" style="font-size: 10px;">公告比选</text>
                            <text x="112" y="70" text-anchor="middle" class="bar-value">185</text>
                            
                            <!-- 邀请比选 -->
                            <rect x="145" y="140" width="45" height="150" fill="#52c41a" class="bar" data-label="邀请比选" data-value="125个项目"/>
                            <text x="167" y="310" text-anchor="middle" class="bar-label" style="font-size: 10px;">邀请比选</text>
                            <text x="167" y="130" text-anchor="middle" class="bar-value">125</text>
                            
                            <!-- 竞争性磋商 -->
                            <rect x="200" y="170" width="45" height="120" fill="#faad14" class="bar" data-label="竞争性磋商" data-value="100个项目"/>
                            <text x="222" y="310" text-anchor="middle" class="bar-label" style="font-size: 10px;">竞争性磋商</text>
                            <text x="222" y="160" text-anchor="middle" class="bar-value">100</text>
                            
                            <!-- 竞争性谈判 -->
                            <rect x="255" y="200" width="45" height="90" fill="#722ed1" class="bar" data-label="竞争性谈判" data-value="75个项目"/>
                            <text x="277" y="310" text-anchor="middle" class="bar-label" style="font-size: 10px;">竞争性谈判</text>
                            <text x="277" y="190" text-anchor="middle" class="bar-value">75</text>
                            
                            <!-- 询价择优 -->
                            <rect x="310" y="230" width="45" height="60" fill="#13c2c2" class="bar" data-label="询价择优" data-value="50个项目"/>
                            <text x="332" y="310" text-anchor="middle" class="bar-label" style="font-size: 10px;">询价择优</text>
                            <text x="332" y="220" text-anchor="middle" class="bar-value">50</text>
                            
                            <!-- 单一来源 -->
                            <rect x="365" y="260" width="45" height="30" fill="#f5222d" class="bar" data-label="单一来源" data-value="25个项目"/>
                            <text x="387" y="310" text-anchor="middle" class="bar-label" style="font-size: 10px;">单一来源</text>
                            <text x="387" y="250" text-anchor="middle" class="bar-value">25</text>
                            
                            <!-- 图表标题 -->
                            <text x="250" y="25" text-anchor="middle" class="chart-title">采购方式项目数量分布</text>
                        </svg>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>采购方式</th>
                                <th>数量</th>
                                <th>占比</th>
                                <th>预算金额(万元)</th>
                                <th>采购金额(万元)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>公告比选</td>
                                <td>185</td>
                                <td>32.5%</td>
                                <td>68,650</td>
                                <td>65,420</td>
                            </tr>
                            <tr>
                                <td>邀请比选</td>
                                <td>125</td>
                                <td>22.0%</td>
                                <td>42,480</td>
                                <td>40,150</td>
                            </tr>
                            <tr>
                                <td>竞争性磋商</td>
                                <td>100</td>
                                <td>17.5%</td>
                                <td>35,200</td>
                                <td>33,680</td>
                            </tr>
                            <tr>
                                <td>竞争性谈判</td>
                                <td>75</td>
                                <td>13.2%</td>
                                <td>28,750</td>
                                <td>27,320</td>
                            </tr>
                            <tr>
                                <td>询价择优</td>
                                <td>50</td>
                                <td>8.8%</td>
                                <td>18,950</td>
                                <td>18,200</td>
                            </tr>
                            <tr>
                                <td>单一来源</td>
                                <td>25</td>
                                <td>4.4%</td>
                                <td>13,680</td>
                                <td>13,680</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            

            
            <!-- TOP10交易量指标数据分析 -->
            <div class="analysis-card" style="grid-column: 1 / -1;">
                <div class="card-header">
                    <div class="card-title">📊 TOP10交易量指标数据分析</div>
                </div>
                <!-- 独立查询条件 -->
                <div class="chart-filter">
                    <div class="filter-group">
                        <label>起止日期:</label>
                        <input type="date" class="filter-select" value="2024-01-01" style="width: 140px;">
                        <span style="margin: 0 8px;">至</span>
                        <input type="date" class="filter-select" value="2024-12-31" style="width: 140px;">
                    </div>
                    <div class="filter-group">
                        <label>展示维度:</label>
                        <select class="filter-select" onchange="switchTopDimension(this.value)">
                            <option value="agency" selected>代理机构</option>
                            <option value="supplier">供应商</option>
                        </select>
                    </div>
                    <button class="filter-btn" onclick="filterTopChart()">应用筛选</button>
                </div>
                <div class="card-content">
                    <div class="chart-container">
                        <svg class="top-chart" width="100%" height="400" viewBox="0 0 900 400">
                            <!-- 背景网格 -->
                            <defs>
                                <pattern id="grid-top" width="80" height="30" patternUnits="userSpaceOnUse">
                                    <path d="M 80 0 L 0 0 0 30" fill="none" stroke="#f0f0f0" stroke-width="1"/>
                                </pattern>
                            </defs>
                            <rect width="720" height="280" x="100" y="60" fill="url(#grid-top)"/>
                            
                            <!-- Y轴 -->
                            <line x1="100" y1="60" x2="100" y2="340" stroke="#d9d9d9" stroke-width="2"/>
                            <!-- X轴 -->
                            <line x1="100" y1="340" x2="820" y2="340" stroke="#d9d9d9" stroke-width="2"/>
                            
                            <!-- Y轴标签 -->
                            <text x="90" y="65" text-anchor="end" class="axis-label">100</text>
                            <text x="90" y="105" text-anchor="end" class="axis-label">80</text>
                            <text x="90" y="145" text-anchor="end" class="axis-label">60</text>
                            <text x="90" y="185" text-anchor="end" class="axis-label">40</text>
                            <text x="90" y="225" text-anchor="end" class="axis-label">20</text>
                            <text x="90" y="345" text-anchor="end" class="axis-label">0</text>
                            
                            <!-- 柱状图数据 -->
                            <!-- 第1名 -->
                            <rect x="120" y="80" width="55" height="260" fill="#1890ff" class="bar" data-label="中招国际" data-value="85项"/>
                            <text x="147" y="360" text-anchor="middle" class="bar-label" style="font-size: 10px;">中招国际</text>
                            <text x="147" y="70" text-anchor="middle" class="bar-value">85</text>
                            
                            <!-- 第2名 -->
                            <rect x="190" y="100" width="55" height="240" fill="#52c41a" class="bar" data-label="华润招标" data-value="72项"/>
                            <text x="217" y="360" text-anchor="middle" class="bar-label" style="font-size: 10px;">华润招标</text>
                            <text x="217" y="90" text-anchor="middle" class="bar-value">72</text>
                            
                            <!-- 第3名 -->
                            <rect x="260" y="120" width="55" height="220" fill="#faad14" class="bar" data-label="国信招标" data-value="68项"/>
                            <text x="287" y="360" text-anchor="middle" class="bar-label" style="font-size: 10px;">国信招标</text>
                            <text x="287" y="110" text-anchor="middle" class="bar-value">68</text>
                            
                            <!-- 第4名 -->
                            <rect x="330" y="140" width="55" height="200" fill="#722ed1" class="bar" data-label="中技国际" data-value="58项"/>
                            <text x="357" y="360" text-anchor="middle" class="bar-label" style="font-size: 10px;">中技国际</text>
                            <text x="357" y="130" text-anchor="middle" class="bar-value">58</text>
                            
                            <!-- 第5名 -->
                            <rect x="400" y="160" width="55" height="180" fill="#13c2c2" class="bar" data-label="招商局" data-value="52项"/>
                            <text x="427" y="360" text-anchor="middle" class="bar-label" style="font-size: 10px;">招商局</text>
                            <text x="427" y="150" text-anchor="middle" class="bar-value">52</text>
                            
                            <!-- 第6名 -->
                            <rect x="470" y="180" width="55" height="160" fill="#f5222d" class="bar" data-label="中咨公司" data-value="45项"/>
                            <text x="497" y="360" text-anchor="middle" class="bar-label" style="font-size: 10px;">中咨公司</text>
                            <text x="497" y="170" text-anchor="middle" class="bar-value">45</text>
                            
                            <!-- 第7名 -->
                            <rect x="540" y="200" width="55" height="140" fill="#fa8c16" class="bar" data-label="中建咨询" data-value="38项"/>
                            <text x="567" y="360" text-anchor="middle" class="bar-label" style="font-size: 10px;">中建咨询</text>
                            <text x="567" y="190" text-anchor="middle" class="bar-value">38</text>
                            
                            <!-- 第8名 -->
                            <rect x="610" y="220" width="55" height="120" fill="#a0d911" class="bar" data-label="国泰建中" data-value="32项"/>
                            <text x="637" y="360" text-anchor="middle" class="bar-label" style="font-size: 10px;">国泰建中</text>
                            <text x="637" y="210" text-anchor="middle" class="bar-value">32</text>
                            
                            <!-- 第9名 -->
                            <rect x="680" y="240" width="55" height="100" fill="#eb2f96" class="bar" data-label="中机国际" data-value="28项"/>
                            <text x="707" y="360" text-anchor="middle" class="bar-label" style="font-size: 10px;">中机国际</text>
                            <text x="707" y="230" text-anchor="middle" class="bar-value">28</text>
                            
                            <!-- 第10名 -->
                            <rect x="750" y="260" width="55" height="80" fill="#52c41a" class="bar" data-label="华信咨询" data-value="22项"/>
                            <text x="777" y="360" text-anchor="middle" class="bar-label" style="font-size: 10px;">华信咨询</text>
                            <text x="777" y="250" text-anchor="middle" class="bar-value">22</text>
                            
                            <!-- 图表标题 -->
                            <text x="460" y="30" text-anchor="middle" class="chart-title">TOP10代理机构交易量排行</text>
                        </svg>
                    </div>
                    <table class="data-table" id="top-data-table">
                        <thead>
                            <tr id="table-header">
                                <th>排名</th>
                                <th>代理机构名称</th>
                                <th>交易量(项)</th>
                                <th>采购金额(万元)</th>
                            </tr>
                        </thead>
                        <tbody id="table-body">
                            <tr>
                                <td>1</td>
                                <td>中招国际招标有限公司</td>
                                <td>85</td>
                                <td>4,850</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>华润招标有限公司</td>
                                <td>72</td>
                                <td>4,200</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>国信招标集团股份有限公司</td>
                                <td>68</td>
                                <td>3,800</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>中技国际招标有限公司</td>
                                <td>58</td>
                                <td>3,400</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>招商局物资有限公司</td>
                                <td>52</td>
                                <td>3,000</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td>中咨公司</td>
                                <td>45</td>
                                <td>2,600</td>
                            </tr>
                            <tr>
                                <td>7</td>
                                <td>中建咨询有限责任公司</td>
                                <td>38</td>
                                <td>2,200</td>
                            </tr>
                            <tr>
                                <td>8</td>
                                <td>国泰建中工程管理有限公司</td>
                                <td>32</td>
                                <td>1,800</td>
                            </tr>
                            <tr>
                                <td>9</td>
                                <td>中机国际工程设计研究院</td>
                                <td>28</td>
                                <td>1,400</td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td>华信咨询设计研究院</td>
                                <td>22</td>
                                <td>1,000</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- TOP10交易额指标数据分析 -->
            <div class="analysis-card" style="grid-column: 1 / -1;">
                <div class="card-header">
                    <div class="card-title">💰 TOP10交易额指标数据分析</div>
                </div>
                <!-- 独立查询条件 -->
                <div class="chart-filter">
                    <div class="filter-group">
                        <label>起止日期:</label>
                        <input type="date" class="filter-input" value="2024-01-01">
                        <span style="margin: 0 8px;">至</span>
                        <input type="date" class="filter-input" value="2024-12-31">
                    </div>
                    <div class="filter-group">
                        <label>展示维度:</label>
                        <select class="filter-select" onchange="switchAmountDimension(this.value)">
                            <option value="agency" selected>代理机构</option>
                            <option value="supplier">供应商</option>
                        </select>
                    </div>
                    <button class="filter-btn" onclick="filterAmountChart()">应用筛选</button>
                </div>
                <div class="card-content">
                    <div class="chart-container">
                        <svg class="amount-chart" width="100%" height="350" viewBox="0 0 1000 350">
                            <!-- 背景网格 -->
                            <defs>
                                <pattern id="grid4" width="90" height="30" patternUnits="userSpaceOnUse">
                                    <path d="M 90 0 L 0 0 0 30" fill="none" stroke="#f0f0f0" stroke-width="1"/>
                                </pattern>
                            </defs>
                            <rect width="820" height="250" x="80" y="50" fill="url(#grid4)"/>
                            
                            <!-- Y轴 -->
                            <line x1="80" y1="50" x2="80" y2="300" stroke="#d9d9d9" stroke-width="2"/>
                            <!-- X轴 -->
                            <line x1="80" y1="300" x2="900" y2="300" stroke="#d9d9d9" stroke-width="2"/>
                            
                            <!-- Y轴标签 -->
                            <text x="70" y="55" text-anchor="end" class="axis-label">15000</text>
                            <text x="70" y="105" text-anchor="end" class="axis-label">12000</text>
                            <text x="70" y="155" text-anchor="end" class="axis-label">9000</text>
                            <text x="70" y="205" text-anchor="end" class="axis-label">6000</text>
                            <text x="70" y="255" text-anchor="end" class="axis-label">3000</text>
                            <text x="70" y="305" text-anchor="end" class="axis-label">0</text>
                            
                            <!-- 柱状图数据 -->
                            <!-- 第1名 -->
                            <rect x="110" y="67" width="65" height="233" fill="#1890ff" class="bar" data-label="中招国际" data-value="14,850万元"/>
                            <text x="142" y="320" text-anchor="middle" class="bar-label">中招国际</text>
                            <text x="142" y="55" text-anchor="middle" class="bar-value">14,850</text>
                            
                            <!-- 第2名 -->
                            <rect x="190" y="83" width="65" height="217" fill="#52c41a" class="bar" data-label="华润招标" data-value="13,200万元"/>
                            <text x="222" y="320" text-anchor="middle" class="bar-label">华润招标</text>
                            <text x="222" y="71" text-anchor="middle" class="bar-value">13,200</text>
                            
                            <!-- 第3名 -->
                            <rect x="270" y="100" width="65" height="200" fill="#faad14" class="bar" data-label="国信招标" data-value="11,800万元"/>
                            <text x="302" y="320" text-anchor="middle" class="bar-label">国信招标</text>
                            <text x="302" y="88" text-anchor="middle" class="bar-value">11,800</text>
                            
                            <!-- 第4名 -->
                            <rect x="350" y="117" width="65" height="183" fill="#722ed1" class="bar" data-label="中技国际" data-value="10,400万元"/>
                            <text x="382" y="320" text-anchor="middle" class="bar-label">中技国际</text>
                            <text x="382" y="105" text-anchor="middle" class="bar-value">10,400</text>
                            
                            <!-- 第5名 -->
                            <rect x="430" y="133" width="65" height="167" fill="#f5222d" class="bar" data-label="招商局物资" data-value="9,000万元"/>
                            <text x="462" y="320" text-anchor="middle" class="bar-label">招商局物资</text>
                            <text x="462" y="121" text-anchor="middle" class="bar-value">9,000</text>
                            
                            <!-- 第6名 -->
                            <rect x="510" y="150" width="65" height="150" fill="#fa8c16" class="bar" data-label="中咨公司" data-value="7,600万元"/>
                            <text x="542" y="320" text-anchor="middle" class="bar-label">中咨公司</text>
                            <text x="542" y="138" text-anchor="middle" class="bar-value">7,600</text>
                            
                            <!-- 第7名 -->
                            <rect x="590" y="167" width="65" height="133" fill="#13c2c2" class="bar" data-label="中建咨询" data-value="6,200万元"/>
                            <text x="622" y="320" text-anchor="middle" class="bar-label">中建咨询</text>
                            <text x="622" y="155" text-anchor="middle" class="bar-value">6,200</text>
                            
                            <!-- 第8名 -->
                            <rect x="670" y="183" width="65" height="117" fill="#eb2f96" class="bar" data-label="国泰建中" data-value="4,800万元"/>
                            <text x="702" y="320" text-anchor="middle" class="bar-label">国泰建中</text>
                            <text x="702" y="171" text-anchor="middle" class="bar-value">4,800</text>
                            
                            <!-- 第9名 -->
                            <rect x="750" y="200" width="65" height="100" fill="#52c41a" class="bar" data-label="中机国际" data-value="3,400万元"/>
                            <text x="782" y="320" text-anchor="middle" class="bar-label">中机国际</text>
                            <text x="782" y="188" text-anchor="middle" class="bar-value">3,400</text>
                            
                            <!-- 第10名 -->
                            <rect x="830" y="233" width="65" height="67" fill="#1890ff" class="bar" data-label="华信咨询" data-value="2,000万元"/>
                            <text x="862" y="320" text-anchor="middle" class="bar-label">华信咨询</text>
                            <text x="862" y="221" text-anchor="middle" class="bar-value">2,000</text>
                            
                            <!-- 图表标题 -->
                            <text x="490" y="25" text-anchor="middle" class="chart-title">TOP10代理机构交易额排行</text>
                        </svg>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr id="amount-table-header">
                                <th>排名</th>
                                <th>代理机构名称</th>
                                <th>中标数</th>
                                <th>采购金额(万元)</th>
                            </tr>
                        </thead>
                        <tbody id="amount-table-body">
                            <tr>
                                <td>1</td>
                                <td>中招国际招标有限公司</td>
                                <td>125</td>
                                <td>14,850</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>华润招标有限公司</td>
                                <td>108</td>
                                <td>13,200</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>国信招标集团股份有限公司</td>
                                <td>95</td>
                                <td>11,800</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>中技国际招标有限公司</td>
                                <td>82</td>
                                <td>10,400</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>招商局物资有限公司</td>
                                <td>75</td>
                                <td>9,000</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td>中咨公司</td>
                                <td>68</td>
                                <td>7,600</td>
                            </tr>
                            <tr>
                                <td>7</td>
                                <td>中建咨询有限责任公司</td>
                                <td>58</td>
                                <td>6,200</td>
                            </tr>
                            <tr>
                                <td>8</td>
                                <td>国泰建中工程管理有限公司</td>
                                <td>48</td>
                                <td>4,800</td>
                            </tr>
                            <tr>
                                <td>9</td>
                                <td>中机国际工程设计研究院</td>
                                <td>38</td>
                                <td>3,400</td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td>华信咨询设计研究院</td>
                                <td>28</td>
                                <td>2,000</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

        </div>
    </div>
    
    <script>
        // 切换图表类型
        function switchChartType(category, type) {
            alert(`切换${category}分析为${type}图表`);
        }
        
        // 导出单个图表
        function exportChart(category) {
            alert(`导出${category}分析图表`);
        }
        
        // 图表筛选功能
        function filterChart(category) {
            alert(`正在根据筛选条件更新${category}图表...`);
        }
        
        // 更新组织绩效图表
        function updateOrganizationChart() {
            alert('正在更新组织绩效图表...');
        }
        
        // TOP10交易量维度切换功能
        function switchTopDimension(dimension) {
            const chartTitle = document.querySelector('.top-chart .chart-title');
            const tableHeader = document.getElementById('table-header');
            const tableBody = document.getElementById('table-body');
            
            if (dimension === 'supplier') {
                // 切换为供应商维度
                chartTitle.textContent = 'TOP10供应商交易量排行';
                tableHeader.innerHTML = `
                    <th>排名</th>
                    <th>供应商名称</th>
                    <th>中标数</th>
                    <th>采购金额(万元)</th>
                `;
                tableBody.innerHTML = `
                    <tr><td>1</td><td>中建三局集团有限公司</td><td>45</td><td>12,580</td></tr>
                    <tr><td>2</td><td>华为技术有限公司</td><td>38</td><td>8,560</td></tr>
                    <tr><td>3</td><td>联想（北京）有限公司</td><td>32</td><td>6,245</td></tr>
                    <tr><td>4</td><td>深信服科技股份有限公司</td><td>28</td><td>4,680</td></tr>
                    <tr><td>5</td><td>万科物业发展股份有限公司</td><td>25</td><td>3,250</td></tr>
                    <tr><td>6</td><td>腾讯科技（深圳）有限公司</td><td>22</td><td>2,850</td></tr>
                    <tr><td>7</td><td>阿里云计算有限公司</td><td>20</td><td>2,450</td></tr>
                    <tr><td>8</td><td>中国移动通信集团有限公司</td><td>18</td><td>2,100</td></tr>
                    <tr><td>9</td><td>京东科技信息技术有限公司</td><td>15</td><td>1,680</td></tr>
                    <tr><td>10</td><td>百度在线网络技术有限公司</td><td>12</td><td>1,200</td></tr>
                `;
            } else {
                // 切换为代理机构维度
                chartTitle.textContent = 'TOP10代理机构交易量排行';
                tableHeader.innerHTML = `
                    <th>排名</th>
                    <th>代理机构名称</th>
                    <th>中标数</th>
                    <th>采购金额(万元)</th>
                `;
                tableBody.innerHTML = `
                    <tr><td>1</td><td>中招国际招标有限公司</td><td>85</td><td>4,850</td></tr>
                    <tr><td>2</td><td>华润招标有限公司</td><td>72</td><td>4,200</td></tr>
                    <tr><td>3</td><td>国信招标集团股份有限公司</td><td>68</td><td>3,800</td></tr>
                    <tr><td>4</td><td>中技国际招标有限公司</td><td>58</td><td>3,400</td></tr>
                    <tr><td>5</td><td>招商局物资有限公司</td><td>52</td><td>3,000</td></tr>
                    <tr><td>6</td><td>中咨公司</td><td>45</td><td>2,600</td></tr>
                    <tr><td>7</td><td>中建咨询有限责任公司</td><td>38</td><td>2,200</td></tr>
                    <tr><td>8</td><td>国泰建中工程管理有限公司</td><td>32</td><td>1,800</td></tr>
                    <tr><td>9</td><td>中机国际工程设计研究院</td><td>28</td><td>1,400</td></tr>
                    <tr><td>10</td><td>华信咨询设计研究院</td><td>22</td><td>1,000</td></tr>
                `;
            }
        }
        
        // TOP10交易量图表筛选功能
        function filterTopChart() {
            alert('正在根据筛选条件更新TOP10交易量图表...');
        }
        
        // TOP10交易额维度切换功能
        function switchAmountDimension(dimension) {
            const chartTitle = document.querySelector('.amount-chart .chart-title');
            const tableHeader = document.getElementById('amount-table-header');
            const tableBody = document.getElementById('amount-table-body');
            
            if (dimension === 'supplier') {
                // 切换为供应商维度
                chartTitle.textContent = 'TOP10供应商交易额排行';
                tableHeader.innerHTML = `
                    <th>排名</th>
                    <th>供应商名称</th>
                    <th>中标数</th>
                    <th>采购金额(万元)</th>
                `;
                tableBody.innerHTML = `
                    <tr><td>1</td><td>中建三局集团有限公司</td><td>65</td><td>18,580</td></tr>
                    <tr><td>2</td><td>华为技术有限公司</td><td>58</td><td>15,560</td></tr>
                    <tr><td>3</td><td>联想（北京）有限公司</td><td>52</td><td>12,245</td></tr>
                    <tr><td>4</td><td>深信服科技股份有限公司</td><td>48</td><td>10,680</td></tr>
                    <tr><td>5</td><td>万科物业发展股份有限公司</td><td>42</td><td>8,250</td></tr>
                    <tr><td>6</td><td>腾讯科技（深圳）有限公司</td><td>38</td><td>6,850</td></tr>
                    <tr><td>7</td><td>阿里云计算有限公司</td><td>35</td><td>5,450</td></tr>
                    <tr><td>8</td><td>中国移动通信集团有限公司</td><td>32</td><td>4,100</td></tr>
                    <tr><td>9</td><td>京东科技信息技术有限公司</td><td>28</td><td>2,680</td></tr>
                    <tr><td>10</td><td>百度在线网络技术有限公司</td><td>25</td><td>1,800</td></tr>
                `;
            } else {
                // 切换为代理机构维度
                chartTitle.textContent = 'TOP10代理机构交易额排行';
                tableHeader.innerHTML = `
                    <th>排名</th>
                    <th>代理机构名称</th>
                    <th>中标数</th>
                    <th>采购金额(万元)</th>
                `;
                tableBody.innerHTML = `
                    <tr><td>1</td><td>中招国际招标有限公司</td><td>125</td><td>14,850</td></tr>
                    <tr><td>2</td><td>华润招标有限公司</td><td>108</td><td>13,200</td></tr>
                    <tr><td>3</td><td>国信招标集团股份有限公司</td><td>95</td><td>11,800</td></tr>
                    <tr><td>4</td><td>中技国际招标有限公司</td><td>82</td><td>10,400</td></tr>
                    <tr><td>5</td><td>招商局物资有限公司</td><td>75</td><td>9,000</td></tr>
                    <tr><td>6</td><td>中咨公司</td><td>68</td><td>7,600</td></tr>
                    <tr><td>7</td><td>中建咨询有限责任公司</td><td>58</td><td>6,200</td></tr>
                    <tr><td>8</td><td>国泰建中工程管理有限公司</td><td>48</td><td>4,800</td></tr>
                    <tr><td>9</td><td>中机国际工程设计研究院</td><td>38</td><td>3,400</td></tr>
                    <tr><td>10</td><td>华信咨询设计研究院</td><td>28</td><td>2,000</td></tr>
                `;
            }
        }
        
        // TOP10交易额图表筛选功能
        function filterAmountChart() {
            alert('正在根据筛选条件更新TOP10交易额图表...');
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('数据分析页面已加载完成');
        });
    </script>
</body>
</html>