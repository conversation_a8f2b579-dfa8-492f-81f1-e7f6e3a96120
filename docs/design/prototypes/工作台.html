<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: #ffffff;
            color: #2c3e50;
            padding: 20px;
            min-height: 100vh;
        }
        
        .dashboard {
            max-width: 100%;
            margin: 0;
            background: #ffffff;
            padding: 0;
        }
        
        .page-title {
            display: none;
        }
        
        .title-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #1890ff;
        }
        
        /* 卡片样式 */
        .card {
            background: #ffffff;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            margin-bottom: 24px;
            border: 1px solid #e8eaec;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
            border-color: #1c4e80;
        }
        
        .card-header {
            padding: 20px 24px;
            background: rgba(28, 78, 128, 0.05);
            border-bottom: 1px solid rgba(28, 78, 128, 0.1);
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }
        
        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: #1c4e80;
        }
        
        .card-title {
            font-size: 18px;
            color: #2c3e50;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .card-title::before {
            content: '●';
            color: #1c4e80;
            font-size: 12px;
        }
        
        .card-more {
            font-size: 13px;
            color: #808695;
            cursor: pointer;
        }
        
        .card-more:hover {
            color: #1890ff;
        }
        
        .card-body {
            padding: 20px;
        }
        
        /* 统计卡片 */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 16px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border: 1px solid #e8eaec;
            padding: 24px 20px;
            display: flex;
            flex-direction: column;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: #1c4e80;
        }
        
        .stat-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(28, 78, 128, 0.02);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .stat-card:hover::after {
            opacity: 1;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(28, 78, 128, 0.15);
            border-color: #1c4e80;
        }
        
        .stat-title {
            font-size: 13px;
            color: #515a6e;
            margin-bottom: 12px;
            line-height: 1.2;
            font-weight: 500;
        }
        
        .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #1c4e80;
            margin-bottom: 0;
            position: relative;
            z-index: 1;
        }
        
        /* 两列布局 */
        .two-column {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
        }
        
        /* 待办事项 */
        .todo-list {
            list-style: none;
        }
        
        .todo-item {
            padding: 18px 20px;
            margin-bottom: 12px;
            background: #fafbfc;
            border-radius: 8px;
            border: 1px solid #e8eaec;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .todo-item:hover {
            transform: translateX(4px);
            background: #ffffff;
            border-color: #1c4e80;
            box-shadow: 0 2px 8px rgba(28, 78, 128, 0.1);
        }
        
        .todo-item:last-child {
            margin-bottom: 0;
        }
        
        .todo-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .todo-badge {
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            background: #1c4e80;
            color: white;
            box-shadow: 0 2px 8px rgba(28, 78, 128, 0.2);
        }
        
        .todo-badge.urgent {
            background: linear-gradient(135deg, rgba(245, 34, 45, 0.1) 0%, rgba(255, 77, 79, 0.1) 100%);
            color: #f5222d;
            border-color: rgba(245, 34, 45, 0.2);
        }
        
        .todo-time {
            font-size: 13px;
            color: #808695;
        }
        
        /* 通知公告 */
        .notice-list {
            list-style: none;
        }
        
        .notice-item {
            padding: 16px 20px;
            margin-bottom: 12px;
            background: #fafbfc;
            border-radius: 8px;
            border: 1px solid #e8eaec;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .notice-item:hover {
            transform: translateY(-2px);
            background: #ffffff;
            border-color: #1c4e80;
            box-shadow: 0 2px 8px rgba(28, 78, 128, 0.1);
        }
        
        .notice-item:last-child {
            margin-bottom: 0;
        }
        
        .notice-title {
            font-size: 15px;
            font-weight: 500;
            margin-bottom: 8px;
            cursor: pointer;
            color: #2c3e50;
            transition: color 0.3s ease;
        }
        
        .notice-title:hover {
            color: #1c4e80;
        }
        
        .notice-meta {
            font-size: 12px;
            color: #808695;
            display: flex;
            gap: 10px;
        }
        
        /* 开标日历样式 */
        .calendar-container {
            width: 100%;
        }
        
        .calendar-header {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 2px;
            margin-bottom: 8px;
        }
        
        .calendar-day {
            padding: 12px 8px;
            text-align: center;
            font-weight: 600;
            font-size: 13px;
            color: #1c4e80;
            background: linear-gradient(135deg, rgba(28, 78, 128, 0.1) 0%, rgba(44, 90, 160, 0.1) 100%);
            border-radius: 8px;
            border: 1px solid rgba(28, 78, 128, 0.1);
        }
        
        .calendar-body {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 2px;
            background: transparent;
        }
        
        .calendar-date {
            min-height: 60px;
            padding: 8px 6px;
            background: #ffffff;
            border-radius: 6px;
            border: 1px solid #e8eaec;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            font-size: 13px;
            font-weight: 500;
            position: relative;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .calendar-date:hover {
            background: #f5f8fc;
            transform: scale(1.02);
            border-color: #1c4e80;
        }
        
        .calendar-date.has-event {
            background: #fafbfc;
            border-color: #1c4e80;
            font-weight: 600;
        }
        
        .event-list {
            display: flex;
            flex-direction: column;
            gap: 1px;
            margin-top: 2px;
            width: 100%;
        }
        
        .event-item {
            font-size: 10px;
            padding: 3px 6px;
            border-radius: 6px;
            color: white;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: 500;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .event-item:hover {
            transform: scale(1.08) translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        /* 评标阶段标段状态样式 */
        .event-item.bid-opened {
            background: #52c41a; /* 已开标 - 绿色 */
        }
        
        .event-item.bid-pending {
            background: #f5222d; /* 待开标 - 红色 */
        }
        
        /* 保留原有样式作为备用 */
        .event-item.stage-notice {
            background: #1890ff;
        }
        
        .event-item.stage-clarify {
            background: #52c41a;
        }
        
        .event-item.stage-evaluation {
            background: #1c4e80;
        }
        
        .event-item.stage-result {
            background: #c8102e;
        }
        
        .event-item.stage-contract {
            background: #eb2f96;
        }
        
        .event-item.stage-terminate {
            background: #f5222d;
        }
        
        .calendar-legend {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e8e8e8;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 11px;
            color: #666;
        }
        
        /* 我的待办样式 */
        .todo-tabs {
            display: flex;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 20px;
        }
        
        .todo-tab {
            padding: 10px 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #1c4e80;
            border-radius: 8px;
            transition: all 0.3s ease;
            flex: 1;
            text-align: center;
        }
        
        .todo-tab:hover {
            background: rgba(28, 78, 128, 0.1);
        }
        
        .todo-tab.active {
            color: white;
            background: #1c4e80;
            box-shadow: 0 2px 8px rgba(28, 78, 128, 0.2);
        }
        
        .todo-meta {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 5px;
            font-size: 12px;
            color: #999;
        }
        
        .todo-module {
            background: #f0f0f0;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
        }
        
        .todo-action {
            background: #1890ff;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 11px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .todo-action:hover {
            background: #40a9ff;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="page-title">
            <div class="title-icon">🏠</div>
            <span>工作台</span>
        </div>
        
        <!-- 数据看板 -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">📊 数据看板</div>
            </div>
            <div class="card-body">
                <div class="stats-container">
                    <div class="stat-card">
                        <div class="stat-title">项目总数量</div>
                        <div class="stat-value">156</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-title">交易公告数</div>
                        <div class="stat-value">23</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-title">补遗/澄清/答疑数</div>
                        <div class="stat-value">8</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-title">流标或终止公告数</div>
                        <div class="stat-value">3</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-title">评标结果公示阶段数</div>
                        <div class="stat-value">15</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-title">中标结果公示阶段数</div>
                        <div class="stat-value">12</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-title">签约履行阶段数</div>
                        <div class="stat-value">95</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 两列布局 - 待办和通知公告 -->
        <div class="two-column">
            <!-- 左侧 - 我的待办 -->
            <div>
                <!-- 我的待办 -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">📝 我的待办</div>
                        <div class="card-more" onclick="openTodoPage()">查看全部</div>
                    </div>
                    <div class="card-body">

                        <ul class="todo-list" id="pendingTodos">
                            <li class="todo-item">
                                <div class="todo-title">
                                    <span class="todo-badge urgent">紧急</span>
                                    <span>办公设备采购项目-标段001需要审核</span>
                                </div>
                                <div class="todo-meta">
                                    <span class="todo-module">公告管理</span>
                                    <span class="todo-time">2023-12-10 14:30</span>
                                    <button class="todo-action" onclick="handleTodo('公告管理-审核页.html')">去处理</button>
                                </div>
                            </li>
                            <li class="todo-item">
                                <div class="todo-title">
                                    <span class="todo-badge">普通</span>
                                    <span>IT基础设施建设项目-标段002需要审核</span>
                                </div>
                                <div class="todo-meta">
                                    <span class="todo-module">项目标段管理</span>
                                    <span class="todo-time">2023-12-11 09:15</span>
                                    <button class="todo-action" onclick="handleTodo('项目标段管理-审核页.html')">去处理</button>
                                </div>
                            </li>
                            <li class="todo-item">
                                <div class="todo-title">
                                    <span class="todo-badge urgent">紧急</span>
                                    <span>数据中心设备采购项目-标段003需要审核</span>
                                </div>
                                <div class="todo-meta">
                                    <span class="todo-module">签约履行管理</span>
                                    <span class="todo-time">2023-12-12 16:45</span>
                                    <button class="todo-action" onclick="handleTodo('签约履行管理-审核页.html')">去处理</button>
                                </div>
                            </li>
                            <li class="todo-item">
                                <div class="todo-title">
                                    <span class="todo-badge">普通</span>
                                    <span>网络安全设备采购项目-标段004需要审核</span>
                                </div>
                                <div class="todo-meta">
                                    <span class="todo-module">供应商管理</span>
                                    <span class="todo-time">2023-12-13 11:20</span>
                                    <button class="todo-action" onclick="handleTodo('供应商管理-详情页.html')">去处理</button>
                                </div>
                            </li>
                            <li class="todo-item">
                                <div class="todo-title">
                                    <span class="todo-badge">普通</span>
                                    <span>软件系统开发项目-标段005需要审核</span>
                                </div>
                                <div class="todo-meta">
                                    <span class="todo-module">采购计划管理</span>
                                    <span class="todo-time">2023-12-14 08:30</span>
                                    <button class="todo-action" onclick="handleTodo('采购计划管理-详情页.html')">去处理</button>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 右侧 - 通知公告 -->
            <div>
                <!-- 通知公告 -->
                <div class="card">
                    <div class="card-header">
                        <div class="card-title">📢 通知公告</div>
                        <div class="card-more" onclick="openNoticePage()">查看全部</div>
                    </div>
                    <div class="card-body">
                        <ul class="notice-list">
                            <li class="notice-item" onclick="viewNotice('notice-001')">
                                <div class="notice-title">关于2024年度采购计划编制工作的通知</div>
                                <div class="notice-meta">
                                    <span>发布人：系统管理员</span>
                                    <span>发布时间：2023-12-10</span>
                                </div>
                            </li>
                            <li class="notice-item" onclick="viewNotice('notice-002')">
                                <div class="notice-title">招采平台系统升级维护通知</div>
                                <div class="notice-meta">
                                    <span>发布人：IT部门</span>
                                    <span>发布时间：2023-12-08</span>
                                </div>
                            </li>
                            <li class="notice-item" onclick="viewNotice('notice-003')">
                                <div class="notice-title">关于规范采购流程管理的通知</div>
                                <div class="notice-meta">
                                    <span>发布人：采购管理部</span>
                                    <span>发布时间：2023-12-05</span>
                                </div>
                            </li>
                            <li class="notice-item" onclick="viewNotice('notice-004')">
                                <div class="notice-title">2023年第四季度供应商评估结果公示</div>
                                <div class="notice-meta">
                                    <span>发布人：供应商管理部</span>
                                    <span>发布时间：2023-12-01</span>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 开标日历 - 放在最下面 -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">📅 开标日历</div>
                <div class="card-more">
                    <select id="monthSelector" onchange="changeMonth()" style="border: 1px solid #d9d9d9; border-radius: 4px; padding: 4px 8px; font-size: 13px;">
                        <option value="2023-12">2023年12月</option>
                        <option value="2024-01">2024年01月</option>
                        <option value="2024-02">2024年02月</option>
                    </select>
                </div>
            </div>
            <div class="card-body">
                <div class="calendar-container">
                    <div class="calendar-header">
                        <div class="calendar-day">日</div>
                        <div class="calendar-day">一</div>
                        <div class="calendar-day">二</div>
                        <div class="calendar-day">三</div>
                        <div class="calendar-day">四</div>
                        <div class="calendar-day">五</div>
                        <div class="calendar-day">六</div>
                    </div>
                    <div class="calendar-body">
                        <div class="calendar-date"></div>
                        <div class="calendar-date"></div>
                        <div class="calendar-date"></div>
                        <div class="calendar-date"></div>
                        <div class="calendar-date"></div>
                        <div class="calendar-date">1</div>
                        <div class="calendar-date">2</div>
                        <div class="calendar-date">3</div>
                        <div class="calendar-date">4</div>
                        <div class="calendar-date">5</div>
                        <div class="calendar-date">6</div>
                        <div class="calendar-date">7</div>
                        <div class="calendar-date">8</div>
                        <div class="calendar-date">9</div>
                        <div class="calendar-date">10</div>
                        <div class="calendar-date">11</div>
                        <div class="calendar-date">12</div>
                        <div class="calendar-date">13</div>
                        <div class="calendar-date">14</div>
                        <div class="calendar-date">15</div>
                        <div class="calendar-date">16</div>
                        <div class="calendar-date">17</div>
                        <div class="calendar-date has-event" onclick="showDayDetail('2023-12-18')">
                            18
                            <div class="event-list">
                                <div class="event-item bid-opened" onclick="openBidSection('bid-003', '数据中心设备采购项目-标段003')" title="评标阶段 - 已开标">数据中心设备-003</div>
                            </div>
                        </div>
                        <div class="calendar-date has-event" onclick="showDayDetail('2023-12-19')">
                            19
                            <div class="event-list">
                                <div class="event-item bid-opened" onclick="openBidSection('bid-003', '数据中心设备采购项目-标段003')" title="评标阶段 - 已开标">数据中心设备-003</div>
                                <div class="event-item bid-pending" onclick="openBidSection('bid-007', '智能监控系统项目-标段007')" title="评标阶段 - 待开标">智能监控-007</div>
                            </div>
                        </div>
                        <div class="calendar-date has-event" onclick="showDayDetail('2023-12-20')">
                            20
                            <div class="event-list">
                                <div class="event-item bid-opened" onclick="openBidSection('bid-003', '数据中心设备采购项目-标段003')" title="评标阶段 - 已开标">数据中心设备-003</div>
                                <div class="event-item bid-pending" onclick="openBidSection('bid-008', '网络安全设备项目-标段008')" title="评标阶段 - 待开标">网络安全-008</div>
                            </div>
                        </div>
                        <div class="calendar-date has-event" onclick="showDayDetail('2023-12-21')">
                            21
                            <div class="event-list">
                                <div class="event-item bid-opened" onclick="openBidSection('bid-009', '办公软件采购项目-标段009')" title="评标阶段 - 已开标">办公软件-009</div>
                            </div>
                        </div>
                        <div class="calendar-date has-event" onclick="showDayDetail('2023-12-22')">
                            22
                            <div class="event-list">
                                <div class="event-item bid-pending" onclick="openBidSection('bid-010', '服务器设备项目-标段010')" title="评标阶段 - 待开标">服务器设备-010</div>
                                <div class="event-item bid-opened" onclick="openBidSection('bid-011', '存储设备项目-标段011')" title="评标阶段 - 已开标">存储设备-011</div>
                            </div>
                        </div>
                        <div class="calendar-date has-event" onclick="showDayDetail('2023-12-23')">
                            23
                            <div class="event-list">
                                <div class="event-item bid-pending" onclick="openBidSection('bid-012', '打印设备项目-标段012')" title="评标阶段 - 待开标">打印设备-012</div>
                            </div>
                        </div>
                        <div class="calendar-date has-event" onclick="showDayDetail('2023-12-24')">
                            24
                            <div class="event-list">
                                <div class="event-item bid-opened" onclick="openBidSection('bid-013', '会议系统项目-标段013')" title="评标阶段 - 已开标">会议系统-013</div>
                            </div>
                        </div>
                        <div class="calendar-date has-event" onclick="showDayDetail('2023-12-25')">
                            25
                            <div class="event-list">
                                <div class="event-item bid-pending" onclick="openBidSection('bid-014', '空调设备项目-标段014')" title="评标阶段 - 待开标">空调设备-014</div>
                                <div class="event-item bid-opened" onclick="openBidSection('bid-015', '照明设备项目-标段015')" title="评标阶段 - 已开标">照明设备-015</div>
                            </div>
                        </div>
                        <div class="calendar-date has-event" onclick="showDayDetail('2023-12-26')">
                            26
                            <div class="event-list">
                                <div class="event-item bid-pending" onclick="openBidSection('bid-016', '安防设备项目-标段016')" title="评标阶段 - 待开标">安防设备-016</div>
                            </div>
                        </div>
                        <div class="calendar-date">27</div>
                        <div class="calendar-date">28</div>
                        <div class="calendar-date">29</div>
                        <div class="calendar-date">30</div>
                        <div class="calendar-date">31</div>
                    </div>
                </div>
                <div class="calendar-legend">
                    <div class="legend-item">
                        <span class="event-item bid-opened" style="display: inline-block; padding: 2px 6px; margin-right: 5px;">已开标</span>
                        <span>评标阶段 - 已开标</span>
                    </div>
                    <div class="legend-item">
                        <span class="event-item bid-pending" style="display: inline-block; padding: 2px 6px; margin-right: 5px;">待开标</span>
                        <span>评标阶段 - 待开标</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function openPage(title, url) {
            // 向父窗口发送消息，请求打开新页面
            window.parent.postMessage({
                type: 'openPage',
                title: title,
                url: url
            }, '*');
        }
        
        // 开标日历相关函数
        function changeMonth() {
            const selector = document.getElementById('monthSelector');
            const selectedMonth = selector.value;
            console.log('切换到月份:', selectedMonth);
            // 这里可以添加重新加载日历数据的逻辑
        }
        
        function showDayDetail(date) {
            alert(`查看 ${date} 的评标阶段标段详情\n\n评标阶段标段：\n• 已开标标段（绿色）\n• 待开标标段（红色）`);
        }
        
        // 打开标段详情页面
        function openBidSection(bidId, sectionName) {
            event.stopPropagation(); // 阻止事件冒泡
            console.log(`打开标段详情: ${bidId}, 标段: ${sectionName}`);
            
            // 直接跳转到项目标段详情页面
            openPage(`${sectionName}详情`, `项目标段管理-标段详情页.html?id=${bidId}`);
        }
        
        // 我的待办相关函数
        function switchTodoTab(type) {
            // 移除所有活动状态
            document.querySelectorAll('.todo-tab').forEach(tab => tab.classList.remove('active'));
            // 激活当前页签
            event.target.classList.add('active');
            
            // 这里可以根据type加载不同的待办数据
            console.log('切换到待办类型:', type);
        }
        
        function openTodoPage() {
            openPage('我的待办', '我的待办-列表页.html');
        }
        
        function handleTodo(pageUrl) {
            // 直接跳转到指定的处理页面
            window.location.href = pageUrl;
        }
        
        // 通知公告相关函数
        function openNoticePage() {
            console.log('打开通知公告页面');
            window.parent.postMessage({
                type: 'openTab',
                title: '通知公告',
                url: '通知公告-列表页.html?from=workspace'
            }, '*');
        }
        
        function viewNotice(noticeId) {
            console.log('查看通知:', noticeId);
            // 跳转到工作台专用的通知详情页
            window.parent.postMessage({
                type: 'openTab',
                title: '通知详情',
                url: '工作台-通知详情页.html?id=' + noticeId
            }, '*');
        }
    </script>
</body>
</html>