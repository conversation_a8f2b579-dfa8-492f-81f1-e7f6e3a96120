<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目标段管理 - 详情</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        /* 主体布局 */
        .main-container {
            padding: 20px;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }
        
        /* 页面标题区域 */
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            font-size: 18px;
            font-weight: bold;
            color: #1c4e80;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .help-icon {
            width: 16px;
            height: 16px;
            background: #1890ff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
        }
        
        .page-actions {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 15px 20px;
            background: white;
            border-top: 1px solid #e8eaec;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 1000;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
        }
        
        /* 详情容器 */
        .detail-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        /* 页签导航 */
        .tab-nav {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e8eaec;
        }
        
        .tab-item {
            padding: 15px 25px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .tab-item:hover {
            background: #e6f7ff;
        }
        
        .tab-item.active {
            background: white;
            border-bottom-color: #1890ff;
            color: #1890ff;
        }
        
        /* 页签内容 */
        .tab-content {
            display: none;
            padding: 20px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* 详情分组 */
        .detail-section {
            margin-bottom: 30px;
        }
        
        .detail-section:last-child {
            margin-bottom: 0;
        }
        
        .section-header {
            padding: 12px 0;
            border-bottom: 2px solid #e8eaec;
            margin-bottom: 20px;
            font-weight: bold;
            color: #333;
            position: relative;
        }
        
        .section-header::before {
            content: '';
            position: absolute;
            left: 0;
            bottom: -2px;
            width: 60px;
            height: 2px;
            background: #1890ff;
        }
        
        /* 详情布局 */
        .detail-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            align-items: start;
        }
        
        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .detail-item.full-width {
            grid-column: 1 / -1;
        }
        
        .detail-label {
            font-size: 13px;
            color: #666;
            font-weight: 500;
        }
        
        .detail-value {
            font-size: 13px;
            color: #333;
            min-height: 20px;
            padding: 5px 0;
        }
        
        .detail-value.empty {
            color: #999;
            font-style: italic;
        }
        
        /* 状态标签 */
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-pending {
            background: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }
        
        .status-approved {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .status-rejected {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
        
        .status-draft {
            background: #f0f0f0;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        /* 标段列表 */
        .section-list {
            margin-top: 20px;
        }
        
        .section-item {
            border: 1px solid #e8eaec;
            border-radius: 4px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        
        .section-item-header {
            padding: 12px 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #e8eaec;
            font-weight: 500;
            color: #333;
        }
        
        .section-item-content {
            padding: 15px;
        }
        
        /* 文件列表 */
        .file-list {
            margin-top: 10px;
        }
        
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: #f5f5f5;
            border-radius: 4px;
            margin-bottom: 5px;
        }
        
        .file-name {
            font-size: 13px;
            color: #333;
        }
        
        .file-download {
            color: #1890ff;
            cursor: pointer;
            font-size: 12px;
            text-decoration: none;
        }
        
        .file-download:hover {
            text-decoration: underline;
        }
        
        /* 表格链接样式 */
        .table-link {
            color: #1890ff;
            text-decoration: none;
            cursor: pointer;
        }
        
        .table-link:hover {
            color: #40a9ff;
            text-decoration: underline;
        }
        
        /* 审批流程 */
        .approval-flow {
            margin: 20px 0;
        }
        
        .flow-steps {
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            margin: 30px 0;
        }
        
        .flow-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            flex: 1;
        }
        
        .step-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 8px;
            position: relative;
            z-index: 2;
        }
        
        .step-icon.completed {
            background: #52c41a;
            color: white;
        }
        
        .step-icon.current {
            background: #1890ff;
            color: white;
        }
        
        .step-icon.pending {
            background: #f0f0f0;
            color: #999;
            border: 2px solid #d9d9d9;
        }
        
        .step-title {
            font-size: 12px;
            color: #333;
            text-align: center;
            font-weight: 500;
        }
        
        .step-line {
            position: absolute;
            top: 16px;
            left: 50%;
            right: -50%;
            height: 2px;
            background: #e8eaec;
            z-index: 1;
        }
        
        .flow-step:last-child .step-line {
            display: none;
        }
        
        .step-line.completed {
            background: #52c41a;
        }
        
        /* 操作记录 */
        .operation-log {
            margin-top: 20px;
        }
        
        .log-item {
            display: flex;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .log-item:last-child {
            border-bottom: none;
        }
        
        .log-time {
            width: 150px;
            font-size: 12px;
            color: #999;
            flex-shrink: 0;
        }
        
        .log-content {
            flex: 1;
        }
        
        .log-action {
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }
        
        .log-user {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .log-desc {
            font-size: 12px;
            color: #999;
            line-height: 1.4;
        }
        
        /* 按钮样式 */
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
        }
        
        .btn-success {
            background: #52c41a;
            color: white;
        }
        
        .btn-success:hover {
            background: #73d13d;
        }
        
        .btn-danger {
            background: #ff4d4f;
            color: white;
        }
        
        .btn-danger:hover {
            background: #ff7875;
        }
        
        .btn-default {
            background: #f5f5f5;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .btn-default:hover {
            background: #e6f7ff;
            border-color: #1890ff;
            color: #1890ff;
        }
        
        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 2000;
        }
        
        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .help-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #1c4e80;
        }
        
        .help-text {
            line-height: 1.6;
            color: #666;
            margin-bottom: 10px;
        }
        
        .close-btn {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #999;
        }
    </style>
</head>
<body>


    <!-- 主体内容 -->
    <div class="main-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div class="page-title">
                项目标段详情
                <div class="help-icon" onclick="showHelp()">?</div>
            </div>
        </div>

        <!-- 详情内容 -->
        <div class="detail-container">
            <!-- 页签导航 -->
            <div class="tab-nav">
                <div class="tab-item active" onclick="switchTab('basic')">基本信息</div>
                <div class="tab-item" onclick="switchTab('process')">流程记录</div>
            </div>

            <!-- 基本信息页签 -->
            <div class="tab-content active" id="basic-tab">
                <!-- 招标信息 -->
                <div class="detail-section">
                    <div class="section-header">招标信息</div>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">计划项目编号</div>
                            <div class="detail-value">CG-2024-001</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">计划项目名称</div>
                            <div class="detail-value">办公用品采购项目</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">采购类型</div>
                            <div class="detail-value">货物</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">招标类别</div>
                            <div class="detail-value">公开招标</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">采购方式</div>
                            <div class="detail-value">公开招标</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">采购预算金额（万元）</div>
                            <div class="detail-value">50.00</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">资金来源</div>
                            <div class="detail-value">自有资金</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">招标时间</div>
                            <div class="detail-value">2024年第1季度</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">采购组织方式</div>
                            <div class="detail-value">自行组织</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">代理机构</div>
                            <div class="detail-value">-</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">年采购计划（万元）</div>
                            <div class="detail-value">200.00</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">项目经办人</div>
                            <div class="detail-value">张三</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">立项决策日期</div>
                            <div class="detail-value">2024-02-15</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">驳回原因</div>
                            <div class="detail-value">-</div>
                        </div>
                    </div>
                </div>

                <!-- 项目信息 -->
                <div class="detail-section">
                    <div class="section-header">项目信息</div>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">项目类型</div>
                            <div class="detail-value">依法必须招标项目</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">项目业主</div>
                            <div class="detail-value">XX集团有限公司</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">所属二级公司单位</div>
                            <div class="detail-value">XX分公司</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">备注</div>
                            <div class="detail-value">按季度分批采购，确保质量符合标准。</div>
                        </div>
                        <div class="detail-item full-width">
                            <div class="detail-label">项目基本情况（建设内容及规模）</div>
                            <div class="detail-value">采购办公桌椅、文具用品、电脑设备等日常办公用品，满足公司日常办公需求。</div>
                        </div>
                        <div class="detail-item full-width">
                            <div class="detail-label">备注</div>
                            <div class="detail-value">按季度分批采购，确保质量符合标准。</div>
                        </div>
                    </div>
                </div>

                <!-- 标段信息 -->
                <div class="detail-section">
                    <div class="section-header">标段信息</div>
                    <div class="section-list">
                        <div class="section-item">
                            <div class="section-item-header">标段 1 - 办公桌椅采购</div>
                            <div class="section-item-content">
                                <div class="detail-grid">
                                    <div class="detail-item">
                                        <div class="detail-label">标段编号</div>
                                        <div class="detail-value"><a href="项目标段管理-标段详情页.html?id=CG-2024-001-001" class="table-link">CG-2024-001-001</a></div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">标段状态</div>
                                        <div class="detail-value"><span class="status-badge status-approved">已审核</span></div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">采购金额（万元）</div>
                                        <div class="detail-value">25.00</div>
                                    </div>
                                    <div class="detail-item full-width">
                                        <div class="detail-label">标段说明</div>
                                        <div class="detail-value">采购办公桌椅，包括办公桌、办公椅、会议桌椅等，满足办公需求。</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="section-item">
                            <div class="section-item-header">标段 2 - 电脑设备采购</div>
                            <div class="section-item-content">
                                <div class="detail-grid">
                                    <div class="detail-item">
                                        <div class="detail-label">标段编号</div>
                                        <div class="detail-value"><a href="项目标段管理-标段详情页.html?id=CG-2024-001-002" class="table-link">CG-2024-001-002</a></div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">标段状态</div>
                                        <div class="detail-value"><span class="status-badge status-pending">待审核</span></div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="detail-label">采购金额（万元）</div>
                                        <div class="detail-value">25.00</div>
                                    </div>
                                    <div class="detail-item full-width">
                                        <div class="detail-label">标段说明</div>
                                        <div class="detail-value">采购台式电脑、笔记本电脑、打印机等办公设备。</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文件信息 -->
                <div class="detail-section">
                    <div class="section-header">文件信息</div>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">立项决策文件</div>
                            <div class="detail-value">
                                <div class="file-list">
                                    <div class="file-item">
                                        <span class="file-name">立项决策文件.pdf</span>
                                        <a href="#" class="file-download">下载</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">采购文件</div>
                            <div class="detail-value">
                                <div class="file-list">
                                    <div class="file-item">
                                        <span class="file-name">采购需求说明书.docx</span>
                                        <a href="#" class="file-download">下载</a>
                                    </div>
                                    <div class="file-item">
                                        <span class="file-name">技术规格书.pdf</span>
                                        <a href="#" class="file-download">下载</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 流程记录页签 -->
            <div class="tab-content" id="process-tab">
                <!-- 审批流程部分 -->
                <div class="approval-flow">
                    <div class="section-header">审批流程</div>
                    <div class="flow-steps">
                        <div class="flow-step">
                            <div class="step-icon completed">1</div>
                            <div class="step-title">申请人</div>
                            <div class="step-line completed"></div>
                        </div>
                        <div class="flow-step">
                            <div class="step-icon completed">2</div>
                            <div class="step-title">申请人公司审核</div>
                            <div class="step-line completed"></div>
                        </div>
                        <div class="flow-step">
                            <div class="step-icon current">3</div>
                            <div class="step-title">上级公司审核</div>
                            <div class="step-line"></div>
                        </div>
                        <div class="flow-step">
                            <div class="step-icon pending">4</div>
                            <div class="step-title">集团审核</div>
                        </div>
                    </div>
                    
                    <div class="detail-section">
                        <div class="section-header">审批详情</div>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <div class="detail-label">当前状态</div>
                                <div class="detail-value"><span class="status-badge status-pending">上级公司审核中</span></div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">提交时间</div>
                                <div class="detail-value">2024-03-01 09:30:00</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">当前审批人</div>
                                <div class="detail-value">李经理</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 操作记录部分 -->
                <div class="detail-section" style="margin-top: 30px;">
                    <div class="section-header">操作记录</div>
                <div class="operation-log">
                    <div class="log-item">
                        <div class="log-time">2024-03-05 14:30</div>
                        <div class="log-content">
                            <div class="log-action">申请人公司审核通过</div>
                            <div class="log-user">操作人：王主管</div>
                            <div class="log-desc">审核通过，项目符合公司采购要求，同意进入下一审批环节。</div>
                        </div>
                    </div>
                    <div class="log-item">
                        <div class="log-time">2024-03-01 16:45</div>
                        <div class="log-content">
                            <div class="log-action">提交审批</div>
                            <div class="log-user">操作人：张三</div>
                            <div class="log-desc">项目标段信息填写完成，提交审批流程。</div>
                        </div>
                    </div>
                    <div class="log-item">
                        <div class="log-time">2024-03-01 15:20</div>
                        <div class="log-content">
                            <div class="log-action">添加标段信息</div>
                            <div class="log-user">操作人：张三</div>
                            <div class="log-desc">添加标段2：电脑设备采购，金额25万元。</div>
                        </div>
                    </div>
                    <div class="log-item">
                        <div class="log-time">2024-03-01 15:10</div>
                        <div class="log-content">
                            <div class="log-action">添加标段信息</div>
                            <div class="log-user">操作人：张三</div>
                            <div class="log-desc">添加标段1：办公桌椅采购，金额25万元。</div>
                        </div>
                    </div>
                    <div class="log-item">
                        <div class="log-time">2024-03-01 09:30</div>
                        <div class="log-content">
                            <div class="log-action">创建项目标段</div>
                            <div class="log-user">操作人：张三</div>
                            <div class="log-desc">基于采购计划CG-2024-001创建项目标段，项目编号：XM-2024-003。</div>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>
        
        <!-- 底部操作按钮 -->
        <div class="page-actions">
            <div class="btn-group">
                <a href="项目标段管理-列表页.html" class="btn btn-default">返回列表</a>
            </div>
            <div class="btn-group">
                <a href="项目标段管理-新建编辑页.html?type=edit&id=XM-2024-003" class="btn btn-primary">编辑</a>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div class="help-modal" id="help-modal">
        <div class="help-content">
            <button class="close-btn" onclick="hideHelp()">×</button>
            <div class="help-title">项目标段管理 - 详情说明</div>
            <div class="help-text">
                <strong>页面功能：</strong><br>
                • <strong>基本信息：</strong>查看项目的招标信息、项目信息、标段信息和文件信息<br>
                • <strong>流程记录：</strong>查看当前审批状态、审批进度和所有操作历史记录
            </div>
            <div class="help-text">
                <strong>状态说明：</strong><br>
                • <strong>待审核：</strong>项目已提交，等待审批<br>
                • <strong>已审核：</strong>项目审批通过<br>
                • <strong>已驳回：</strong>项目审批被驳回，需要修改后重新提交
            </div>
            <div class="help-text">
                <strong>操作说明：</strong><br>
                • 点击"编辑"按钮可以修改项目信息（仅在特定状态下可用）<br>
                • 可以下载相关的文件附件<br>
                • 流程记录页签显示审批进度、当前审批人和完整操作历史
            </div>
        </div>
    </div>

    <script>
        // 页签切换
        function switchTab(tabName) {
            // 隐藏所有页签内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有页签的激活状态
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示选中的页签内容
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 激活选中的页签
            event.target.classList.add('active');
        }
        
        // 帮助功能
        function showHelp() {
            document.getElementById('help-modal').style.display = 'block';
        }
        
        function hideHelp() {
            document.getElementById('help-modal').style.display = 'none';
        }
        
        // 点击模态框外部关闭
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });
        
        // 文件下载
        function downloadFile(fileName) {
            // 这里可以添加实际的文件下载逻辑
            alert('下载文件：' + fileName);
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 根据URL参数加载对应的数据
            const urlParams = new URLSearchParams(window.location.search);
            const id = urlParams.get('id');
            
            if (id) {
                // 这里可以根据ID加载具体的项目数据
                console.log('加载项目ID：', id);
            }
        });
    </script>
</body>
</html>