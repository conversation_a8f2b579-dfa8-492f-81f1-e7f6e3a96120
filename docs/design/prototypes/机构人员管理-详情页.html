<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机构人员管理 - 详情页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 页面头部 */
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
        }

        .page-title::before {
            content: '';
            width: 4px;
            height: 20px;
            background: #3498db;
            margin-right: 12px;
        }

        .help-icon {
            margin-left: 8px;
            color: #95a5a6;
            cursor: pointer;
            font-size: 16px;
        }

        .back-btn {
            background: #95a5a6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .back-btn:hover {
            background: #7f8c8d;
        }

        /* 面包屑导航 */
        .breadcrumb {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            font-size: 14px;
        }

        .breadcrumb a {
            color: #3498db;
            text-decoration: none;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .breadcrumb span {
            color: #7f8c8d;
            margin: 0 8px;
        }

        /* 页签导航 */
        .tab-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .tab-nav {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .tab-item {
            padding: 15px 25px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
            font-weight: 500;
            color: #6c757d;
        }

        .tab-item.active {
            background: white;
            color: #3498db;
            border-bottom-color: #3498db;
        }

        .tab-item:hover {
            background: #e9ecef;
        }

        .tab-item.active:hover {
            background: white;
        }

        /* 页签内容 */
        .tab-content {
            display: none;
            padding: 30px;
        }

        .tab-content.active {
            display: block;
        }

        /* 详情区域 */
        .detail-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ecf0f1;
            position: relative;
        }

        .section-title::before {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 60px;
            height: 2px;
            background: #3498db;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-item.full-width {
            grid-column: 1 / -1;
        }

        .detail-label {
            font-size: 14px;
            font-weight: 500;
            color: #7f8c8d;
            margin-bottom: 8px;
        }

        .detail-value {
            font-size: 14px;
            color: #2c3e50;
            padding: 10px 12px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }

        /* 操作记录表格 */
        .operation-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .operation-table th,
        .operation-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        .operation-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        .operation-table td {
            font-size: 13px;
            color: #495057;
        }

        .operation-table tr:hover {
            background: #f8f9fa;
        }

        .operation-table tr:last-child td {
            border-bottom: none;
        }

        /* 操作类型标签 */
        .operation-type {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .operation-type.create {
            background: #d4edda;
            color: #155724;
        }

        .operation-type.update {
            background: #fff3cd;
            color: #856404;
        }

        .operation-type.delete {
            background: #f8d7da;
            color: #721c24;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 8px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .help-text {
            font-size: 14px;
            line-height: 1.8;
            color: #555;
            margin-bottom: 15px;
        }

        .close-help {
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            font-size: 24px;
            color: #95a5a6;
            cursor: pointer;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .detail-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 10px;
            }
            
            .tab-content {
                padding: 20px;
            }
            
            .tab-nav {
                flex-wrap: wrap;
            }
            
            .tab-item {
                flex: 1;
                text-align: center;
                min-width: 120px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="page-title">
                机构人员管理 - 详情页
                <span class="help-icon" onclick="showHelp()" title="帮助说明">ⓘ</span>
            </div>
            <button class="back-btn" onclick="goBack()">返回列表</button>
        </div>

        <!-- 面包屑导航 -->
        <div class="breadcrumb">
            <a href="机构人员管理-列表页.html">机构人员管理</a>
            <span>/</span>
            <span>张工程师</span>
        </div>

        <!-- 页签容器 -->
        <div class="tab-container">
            <!-- 页签导航 -->
            <div class="tab-nav">
                <div class="tab-item active" onclick="switchTab('basic')">基本信息</div>
                <div class="tab-item" onclick="switchTab('operations')">操作记录</div>
            </div>

            <!-- 基本信息页签 -->
            <div class="tab-content active" id="basic-tab">
                <div class="detail-section">
                    <div class="section-title">人员信息</div>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <div class="detail-label">人员名称</div>
                            <div class="detail-value">张工程师</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">联系方式</div>
                            <div class="detail-value">13800138001</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">所属机构</div>
                            <div class="detail-value">中建工程咨询有限公司</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">企业代码</div>
                            <div class="detail-value">91110000123456789X</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">更新时间</div>
                            <div class="detail-value">2024-01-15 14:30:25</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">创建时间</div>
                            <div class="detail-value">2020-03-15 09:00:00</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作记录页签 -->
            <div class="tab-content" id="operations-tab">
                <div class="detail-section">
                    <div class="section-title">操作记录</div>
                    <table class="operation-table">
                        <thead>
                            <tr>
                                <th style="width: 120px;">操作时间</th>
                                <th style="width: 100px;">操作类型</th>
                                <th style="width: 120px;">操作人员</th>
                                <th>操作内容</th>
                                <th style="width: 120px;">IP地址</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2024-01-15 14:30</td>
                                <td><span class="operation-type update">修改</span></td>
                                <td>系统管理员</td>
                                <td>修改了人员联系方式，从"13800138000"更新为"13800138001"</td>
                                <td>*************</td>
                            </tr>
                            <tr>
                                <td>2023-12-20 10:15</td>
                                <td><span class="operation-type update">修改</span></td>
                                <td>HR管理员</td>
                                <td>修改了人员所属角色，从"高级工程师"更新为"项目经理"</td>
                                <td>*************</td>
                            </tr>
                            <tr>
                                <td>2023-08-10 16:45</td>
                                <td><span class="operation-type update">修改</span></td>
                                <td>系统管理员</td>
                                <td>更新了企业代码信息</td>
                                <td>*************</td>
                            </tr>
                            <tr>
                                <td>2020-03-15 09:00</td>
                                <td><span class="operation-type create">创建</span></td>
                                <td>系统管理员</td>
                                <td>创建了人员档案，初始化基本信息</td>
                                <td>*************</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div class="help-modal" id="helpModal">
        <div class="help-content">
            <button class="close-help" onclick="hideHelp()">&times;</button>
            <div class="help-title">机构人员管理 - 详情页说明</div>
            <div class="help-text">
                <strong>功能说明：</strong><br>
                本页面展示机构人员的详细信息，包括基本信息和操作记录。
            </div>
            <div class="help-text">
                <strong>页签说明：</strong><br>
                • <strong>基本信息：</strong>显示人员的基本档案信息，包括姓名、联系方式、所属机构等<br>
                • <strong>操作记录：</strong>记录对该人员信息的所有操作历史，包括创建、修改等操作
            </div>
            <div class="help-text">
                <strong>字段说明：</strong><br>
                • <strong>人员名称：</strong>人员的真实姓名<br>
                • <strong>联系方式：</strong>人员的联系电话或其他联系方式<br>

                • <strong>所属机构：</strong>人员所属的代理机构名称<br>
                • <strong>企业代码：</strong>机构的统一社会信用代码<br>
                • <strong>更新时间：</strong>最后一次修改信息的时间<br>
                • <strong>创建时间：</strong>首次创建人员档案的时间
            </div>
            <div class="help-text">
                <strong>操作说明：</strong><br>
                1. 点击页签可切换查看不同类型的信息<br>
                2. 操作记录按时间倒序排列，最新操作在最上方<br>
                3. 点击"返回列表"可回到人员管理列表页面
            </div>
        </div>
    </div>

    <script>
        // 页签切换
        function switchTab(tabName) {
            // 隐藏所有页签内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有页签的激活状态
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(item => {
                item.classList.remove('active');
            });

            // 显示选中的页签内容
            document.getElementById(tabName + '-tab').classList.add('active');

            // 激活选中的页签
            event.target.classList.add('active');
        }

        // 显示帮助弹窗
        function showHelp() {
            document.getElementById('helpModal').style.display = 'block';
        }

        // 隐藏帮助弹窗
        function hideHelp() {
            document.getElementById('helpModal').style.display = 'none';
        }

        // 返回列表页
        function goBack() {
            window.location.href = '机构人员管理-列表页.html';
        }

        // 点击弹窗外部关闭弹窗
        document.getElementById('helpModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });
    </script>
</body>
</html>