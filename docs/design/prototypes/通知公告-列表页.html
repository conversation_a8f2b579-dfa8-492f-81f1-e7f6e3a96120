<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知公告 - 招采平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            padding: 0;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* 页面标题 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 2px solid #e8eaec;
        }
        
        .page-title {
            font-size: 20px;
            font-weight: bold;
            color: #17233d;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .help-icon {
            width: 16px;
            height: 16px;
            background-color: #1890ff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: help;
            position: relative;
        }
        
        .help-icon:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 25px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .page-breadcrumb {
            font-size: 13px;
            color: #808695;
        }
        
        /* 查询区域 */
        .search-section {
            background: white;
            border-radius: 4px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.05);
        }
        
        .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            align-items: end;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-label {
            font-size: 13px;
            color: #515a6e;
            margin-bottom: 6px;
            font-weight: 500;
        }
        
        .form-input {
            height: 36px;
            padding: 0 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 13px;
            transition: border-color 0.3s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .search-buttons {
            display: flex;
            gap: 8px;
            align-items: end;
        }
        
        .btn {
            height: 36px;
            padding: 0 16px;
            border: none;
            border-radius: 4px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }
        
        .btn-primary {
            background-color: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #40a9ff;
        }
        
        .btn-default {
            background-color: #f5f5f5;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .btn-default:hover {
            background-color: #e6e6e6;
        }
        
        /* 数据列表区域 */
        .table-section {
            background: white;
            border-radius: 4px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.05);
        }
        
        .table-container {
            overflow-x: auto;
            border-radius: 4px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e8eaec;
            font-size: 13px;
        }
        
        .data-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #17233d;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .data-table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .data-table th:first-child,
        .data-table td:first-child {
            position: sticky;
            left: 0;
            background-color: inherit;
            z-index: 5;
            box-shadow: 2px 0 4px rgba(0,0,0,0.1);
            width: 400px;
        }
        
        .data-table th:first-child {
            z-index: 15;
        }
        
        .data-table th:nth-child(2),
        .data-table td:nth-child(2) {
            width: 200px;
        }
        
        .link-primary {
            color: #1890ff;
            text-decoration: none;
            cursor: pointer;
        }
        
        .link-primary:hover {
            color: #40a9ff;
            text-decoration: underline;
        }
        
        /* 分页 */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            border-top: 1px solid #e8eaec;
        }
        
        .pagination-info {
            font-size: 13px;
            color: #808695;
        }
        
        .pagination {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        .page-btn {
            width: 32px;
            height: 32px;
            border: 1px solid #d9d9d9;
            background: white;
            color: #666;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 13px;
            transition: all 0.3s;
        }
        
        .page-btn:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .page-btn.active {
            background-color: #1890ff;
            border-color: #1890ff;
            color: white;
        }
        
        .page-btn:disabled {
            background-color: #f5f5f5;
            border-color: #d9d9d9;
            color: #ccc;
            cursor: not-allowed;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .search-form {
                grid-template-columns: 1fr;
            }
            
            .search-buttons {
                justify-content: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="page-header">
            <div>
                <div class="page-title">
                    📢 通知公告
                    <span class="help-icon" data-tooltip="查看系统发布的通知公告信息，点击标题可查看详细内容">?</span>
                </div>
                <div class="page-breadcrumb">首页 > 通知公告</div>
            </div>
        </div>
        
        <!-- 查询区域 -->
        <div class="search-section">
            <div class="search-form">
                <div class="form-group">
                    <label class="form-label">标题名称</label>
                    <input type="text" class="form-input" id="titleName" placeholder="请输入标题名称">
                </div>
                <div class="form-group">
                    <label class="form-label">创建时间</label>
                    <input type="date" class="form-input" id="createTime">
                </div>
                <div class="search-buttons">
                    <button class="btn btn-primary" onclick="searchNotices()">查询</button>
                    <button class="btn btn-default" onclick="resetSearch()">重置</button>
                </div>
            </div>
        </div>
        
        <!-- 数据列表区域 -->
        <div class="table-section">
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>标题名称</th>
                            <th>创建时间</th>
                        </tr>
                    </thead>
                    <tbody id="noticeTableBody">
                        <tr onclick="viewNoticeDetail('notice-001')">
                            <td>
                                <a href="javascript:void(0)" class="link-primary" onclick="viewNoticeDetail('notice-001')">关于2024年度采购计划编制工作的通知</a>
                            </td>
                            <td>2023-12-10 09:30:00</td>
                        </tr>
                        <tr onclick="viewNoticeDetail('notice-002')">
                            <td>
                                <a href="javascript:void(0)" class="link-primary" onclick="viewNoticeDetail('notice-002')">招采平台系统升级维护通知</a>
                            </td>
                            <td>2023-12-08 14:20:00</td>
                        </tr>
                        <tr onclick="viewNoticeDetail('notice-003')">
                            <td>
                                <a href="javascript:void(0)" class="link-primary" onclick="viewNoticeDetail('notice-003')">关于规范采购流程管理的通知</a>
                            </td>
                            <td>2023-12-05 16:45:00</td>
                        </tr>
                        <tr onclick="viewNoticeDetail('notice-004')">
                            <td>
                                <a href="javascript:void(0)" class="link-primary" onclick="viewNoticeDetail('notice-004')">2023年第四季度供应商评估结果公示</a>
                            </td>
                            <td>2023-12-01 10:15:00</td>
                        </tr>
                        <tr onclick="viewNoticeDetail('notice-005')">
                            <td>
                                <a href="javascript:void(0)" class="link-primary" onclick="viewNoticeDetail('notice-005')">关于加强招标采购管理的通知</a>
                            </td>
                            <td>2023-11-28 11:30:00</td>
                        </tr>
                        <tr onclick="viewNoticeDetail('notice-006')">
                            <td>
                                <a href="javascript:void(0)" class="link-primary" onclick="viewNoticeDetail('notice-006')">供应商资质审核要求说明</a>
                            </td>
                            <td>2023-11-25 15:20:00</td>
                        </tr>
                        <tr onclick="viewNoticeDetail('notice-007')">
                            <td>
                                <a href="javascript:void(0)" class="link-primary" onclick="viewNoticeDetail('notice-007')">招标文件模板更新通知</a>
                            </td>
                            <td>2023-11-22 09:45:00</td>
                        </tr>
                        <tr onclick="viewNoticeDetail('notice-008')">
                            <td>
                                <a href="javascript:void(0)" class="link-primary" onclick="viewNoticeDetail('notice-008')">电子招投标平台操作指南</a>
                            </td>
                            <td>2023-11-20 13:10:00</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="pagination-container">
                <div class="pagination-info">
                    共 8 条记录，第 1/1 页
                </div>
                <div class="pagination">
                    <button class="page-btn" disabled>‹</button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn" disabled>›</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 查询功能
        function searchNotices() {
            const titleName = document.getElementById('titleName').value;
            const createTime = document.getElementById('createTime').value;
            
            console.log('查询条件:', {
                titleName: titleName,
                createTime: createTime
            });
            
            // 这里可以添加实际的查询逻辑
            alert('查询功能已触发，请查看控制台输出');
        }
        
        // 重置查询
        function resetSearch() {
            document.getElementById('titleName').value = '';
            document.getElementById('createTime').value = '';
        }
        
        // 查看通知详情
        function viewNoticeDetail(noticeId) {
            console.log('查看通知详情，ID:', noticeId);
            
            // 检查是否从工作台进入，如果是则跳转到工作台通知详情页
            const referrer = document.referrer;
            const isFromWorkspace = referrer.includes('工作台.html') || window.location.search.includes('from=workspace');
            
            if (isFromWorkspace) {
                // 从工作台进入，跳转到工作台通知详情页
                window.parent.postMessage({
                    type: 'openTab',
                    title: '通知详情',
                    url: '工作台-通知详情页.html?id=' + noticeId
                }, '*');
            } else {
                // 从通知公告管理进入，跳转到完整的通知公告详情页
                window.parent.postMessage({
                    type: 'openTab',
                    title: '通知公告详情',
                    url: '通知公告-详情页.html?id=' + noticeId
                }, '*');
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('通知公告列表页加载完成');
        });
    </script>
</body>
</html>