<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流标或中止管理 - 招标采购管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }

        /* 页面头部 */
        .page-header {
            background: #fff;
            border-bottom: 1px solid #e6e8eb;
            padding: 20px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
            color: #262626;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .help-icon {
            width: 16px;
            height: 16px;
            background: #1890ff;
            border-radius: 50%;
            color: white;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }



        /* 页签样式 */
        .tab-container {
            background: #fff;
            border-bottom: 1px solid #e6e8eb;
        }

        .tab-nav {
            display: flex;
            padding: 0 24px;
        }

        .tab-item {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            color: #6b7280;
            font-weight: 500;
            transition: all 0.3s;
        }

        .tab-item.active {
            color: #1890ff;
            border-bottom-color: #1890ff;
            background: #f5f7fa;
        }

        .tab-item:hover {
            color: #1890ff;
            background: #f5f7fa;
        }

        /* 内容区域 */
        .content-area {
            padding: 24px;
        }

        .tab-content {
            color: #2563eb;
        }

        /* 页签内容 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 查询区域 */
        .search-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-size: 13px;
            color: #555;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-input, .form-select, .form-control {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 13px;
            transition: border-color 0.3s;
            width: 100%;
        }

        .form-input:focus, .form-select:focus, .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .search-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 16px;
        }

        .btn-group {
            display: flex;
            gap: 10px;
            align-items: end;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .btn-outline {
            background: white;
            color: #3498db;
            border: 1px solid #3498db;
        }

        .btn-outline:hover {
            background: #3498db;
            color: white;
        }

        /* 高级查询 */
        .advanced-search {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #e6e8eb;
            display: none;
        }

        .advanced-search.show {
            display: block;
        }

        .advanced-toggle {
            color: #1890ff;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        /* 功能操作栏 */
        .action-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
        }

        .btn-success {
            background-color: #52c41a;
            color: white;
            border-color: #52c41a;
        }

        .btn-success:hover {
            background-color: #73d13d;
            border-color: #73d13d;
        }

        .btn-danger {
            background-color: #ff4d4f;
            color: white;
            border-color: #ff4d4f;
        }

        .btn-danger:hover {
            background-color: #ff7875;
            border-color: #ff7875;
        }

        .btn-warning {
            background-color: #faad14;
            color: white;
            border-color: #faad14;
        }

        .btn-warning:hover {
            background-color: #ffc53d;
            border-color: #ffc53d;
        }

        /* 数据表格 */
        .table-container {
            background: white;
            border: 1px solid #e6e8eb;
            border-radius: 6px;
            overflow: hidden;
        }

        .table-wrapper {
            overflow-x: auto;
            max-width: 100%;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            min-width: 1200px;
        }

        .data-table th,
        .data-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e6e8eb;
            font-size: 13px;
            word-wrap: break-word;
        }

        .data-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        /* 冻结列样式 */
        .data-table .col-checkbox {
            width: 50px;
            position: sticky;
            left: 0;
            background: inherit;
            z-index: 11;
        }

        .data-table .col-title {
            width: 200px;
            position: sticky;
            left: 50px;
            background: inherit;
            z-index: 11;
        }

        .data-table .col-number {
            width: 120px;
        }

        .data-table .col-type {
            width: 100px;
        }

        .data-table .col-status {
            width: 80px;
        }

        .data-table .col-creator {
            width: 100px;
        }

        .data-table .col-time {
            width: 140px;
        }

        .data-table .col-budget {
            width: 100px;
        }

        .data-table .col-actions {
            width: 180px;
            position: sticky;
            right: 0;
            background: inherit;
            z-index: 11;
        }

        /* 冻结列阴影效果 */
        .data-table .col-title::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 1px;
            background: linear-gradient(to right, transparent, rgba(0,0,0,0.1));
            pointer-events: none;
        }

        .data-table .col-actions::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            width: 1px;
            background: linear-gradient(to left, transparent, rgba(0,0,0,0.1));
            pointer-events: none;
        }

        .data-table tbody tr:hover {
            background: #f9fafb;
        }

        .data-table tbody tr:hover .col-checkbox,
        .data-table tbody tr:hover .col-title,
        .data-table tbody tr:hover .col-actions {
            background: #f9fafb;
        }

        /* 状态标签 */
        .status-tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .status-draft {
            background: #f3f4f6;
            color: #374151;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-approved {
            background: #d1fae5;
            color: #065f46;
        }

        .status-published {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-rejected {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-withdrawn {
            background: #f3f4f6;
            color: #6b7280;
        }

        /* 操作按钮 */
        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 3px;
            font-size: 11px;
            cursor: pointer;
            margin-right: 4px;
            transition: all 0.3s;
        }

        .action-btn:last-child {
            margin-right: 0;
        }

        .action-btn.view {
            background: #3b82f6;
            color: white;
        }

        .action-btn.view:hover {
            background: #2563eb;
        }

        .action-btn.audit {
            background: #f59e0b;
            color: white;
        }

        .action-btn.audit:hover {
            background: #d97706;
        }

        .action-btn.edit {
            background: #10b981;
            color: white;
        }

        .action-btn.edit:hover {
            background: #059669;
        }

        .action-btn.delete {
            background: #ef4444;
            color: white;
        }

        .action-btn.delete:hover {
            background: #dc2626;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            background: #f8fafc;
            border-top: 1px solid #e6e8eb;
        }

        .pagination-info {
            color: #6b7280;
            font-size: 13px;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .page-size {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #6b7280;
            font-size: 13px;
        }

        .page-size select {
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 13px;
        }

        .page-nav {
            display: flex;
            gap: 4px;
        }

        .page-btn {
            padding: 6px 12px;
            border: 1px solid #d1d5db;
            background: white;
            color: #374151;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: all 0.3s;
        }

        .page-btn:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .page-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 帮助弹窗 */
        .help-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .help-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .help-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e6e8eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .help-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            color: #6b7280;
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .close-btn:hover {
            color: #374151;
        }

        .help-body {
            padding: 24px;
        }

        .help-section {
            margin-bottom: 24px;
        }

        .help-section h4 {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 12px;
        }

        .help-section p {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 12px;
        }

        .help-list {
            list-style: none;
            padding-left: 0;
        }

        .help-list li {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 8px;
            padding-left: 16px;
            position: relative;
        }

        .help-list li::before {
            content: '•';
            color: #3b82f6;
            position: absolute;
            left: 0;
        }

        .help-list strong {
            color: #1f2937;
        }

        /* 响应式 */
        @media (max-width: 1200px) {
            .search-form {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .advanced-form {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .search-form {
                grid-template-columns: 1fr;
            }
            
            .advanced-form {
                grid-template-columns: 1fr;
            }
            
            .action-bar {
                flex-direction: column;
                gap: 12px;
                align-items: stretch;
            }
            
            .action-left {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div>
                <h1 class="page-title">
                    流标或中止管理
                    <span class="help-icon" onclick="showHelp()" title="功能说明">?</span>
                </h1>

            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 页签导航 -->
            <div class="tab-container">
                <div class="tab-nav">
                    <div class="tab-item active" onclick="switchTab('todo')">待办</div>
                    <div class="tab-item" onclick="switchTab('done')">已办</div>
                </div>
            </div>

            <!-- 待办页签 -->
            <div id="todo-tab" class="tab-content active">
                <!-- 查询区域 -->
                <div class="search-section">
                    <div class="search-form">
                        <div class="form-group">
                            <label class="form-label">流标/中止标题</label>
                            <input type="text" class="form-control" placeholder="请输入流标/中止标题">
                        </div>
                        <div class="form-group">
                            <label class="form-label">审核状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="pending">待审核</option>
                                <option value="processing">审核中</option>
                                <option value="approved">已通过</option>
                                <option value="rejected">未通过</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">发布状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="pending">待发布</option>
                                <option value="published">已发布</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购方式</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="public">公开招标</option>
                                <option value="invite">邀请招标</option>
                                <option value="inquiry">询价采购</option>
                                <option value="single">单一来源</option>
                            </select>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-primary">查询</button>
                            <button class="btn btn-secondary">重置</button>
                            <button class="btn btn-outline" onclick="toggleAdvanced()">高级查询</button>
                        </div>
                    </div>
                    
                    <!-- 高级查询 -->
                    <div class="advanced-search" id="advanced-search">
                        <div class="search-form">
                            <div class="form-group">
                                <label class="form-label">流标/中止类型</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="failed">流标</option>
                                    <option value="terminated">中止</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">采购类型</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="goods">货物采购</option>
                                    <option value="service">服务采购</option>
                                    <option value="engineering">工程采购</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">采购组织方式</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="centralized">集中采购</option>
                                    <option value="decentralized">分散采购</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">资金来源</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="fiscal">财政资金</option>
                                    <option value="self">自有资金</option>
                                    <option value="loan">贷款资金</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">招标金额</label>
                                <input type="text" class="form-control" placeholder="请输入金额范围">
                            </div>
                            <div class="form-group">
                                <label class="form-label">申请人</label>
                                <input type="text" class="form-control" placeholder="请输入申请人">
                            </div>
                            <div class="form-group">
                                <label class="form-label">招标时间</label>
                                <input type="date" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="form-label">计划时间</label>
                                <input type="date" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="form-label">创建时间</label>
                                <input type="date" class="form-control">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能操作栏 -->
                <div class="action-bar">
                    <div class="action-buttons">
                        <button class="btn btn-success" onclick="createNew()">新建流标/中止</button>
                        <button class="btn btn-warning" onclick="batchAudit()">批量审核</button>
                        <button class="btn btn-danger" onclick="batchDelete()">批量删除</button>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="table-container">
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th class="col-checkbox">
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th class="col-title">流标/中止标题</th>
                                    <th class="col-number">项目编号</th>
                                    <th class="col-type">采购方式</th>
                                    <th class="col-type">流标/中止类型</th>
                                    <th class="col-status">审核状态</th>
                                    <th class="col-status">发布状态</th>
                                    <th class="col-creator">创建人</th>
                                    <th class="col-time">创建时间</th>
                                    <th class="col-actions">操作</th>
                                </tr>
                            </thead>
                            <tbody>

                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('FB-2024-002')">IT设备维护服务采购中止公告</a>
                                    </td>
                                    <td>FB-2024-002</td>
                                    <td>邀请招标</td>
                                    <td>中止</td>
                                    <td><span class="status-tag status-pending">待审核</span></td>
                                    <td><span class="status-tag status-draft">待发布</span></td>
                                    <td>李四</td>
                                    <td>2024-01-12</td>
                                    <td class="frozen-column action-col">
                                        <button class="action-btn btn-submit" onclick="submitAnnouncement('FB-2024-002')">提交</button>
                                        <button class="action-btn btn-edit" onclick="editAnnouncement('FB-2024-002')">编辑</button>
                                        <button class="action-btn btn-delete" onclick="deleteAnnouncement('FB-2024-002')">删除</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-checkbox">
                                        <input type="checkbox" class="row-checkbox">
                                    </td>
                                    <td class="col-title">
                                        <a href="#" onclick="viewDetail(3)" style="color: #3b82f6; text-decoration: none;">会议室装修改造项目流标公告</a>
                                    </td>
                                    <td class="col-number">FB-2024-003</td>
                                    <td class="col-type">公开招标</td>
                                    <td class="col-type">流标</td>
                                    <td class="col-status">
                                        <span class="status-tag status-draft">草稿</span>
                                    </td>
                                    <td class="col-status">
                                        <span class="status-tag status-draft">待发布</span>
                                    </td>
                                    <td class="col-creator">王五</td>
                                    <td class="col-time">2024-01-14 09:30</td>
                                    <td class="col-actions">
                                        <button class="action-btn change" onclick="changeAnnouncement(3)">变更</button>
                                        <button class="action-btn edit" onclick="editItem(3)">编辑</button>
                                        <button class="action-btn delete" onclick="deleteItem(3)">删除</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-checkbox">
                                        <input type="checkbox" class="row-checkbox">
                                    </td>
                                    <td class="col-title">
                                        <a href="#" onclick="viewDetail(4)" style="color: #3b82f6; text-decoration: none;">食堂食材采购项目中止公告</a>
                                    </td>
                                    <td class="col-number">FB-2024-004</td>
                                    <td class="col-type">邀请招标</td>
                                    <td class="col-type">中止</td>
                                    <td class="col-status">
                                        <span class="status-tag status-approved">已审核</span>
                                    </td>
                                    <td class="col-status">
                                        <span class="status-tag status-published">已发布</span>
                                    </td>
                                    <td class="col-creator">赵六</td>
                                    <td class="col-time">2024-01-16 11:20</td>
                                    <td class="col-actions">
                                        <!-- 已发布状态，无变更按钮 -->
                                        <button class="action-btn edit" onclick="editItem(4)">编辑</button>
                                        <button class="action-btn delete" onclick="deleteItem(4)">删除</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="col-checkbox">
                                        <input type="checkbox" class="row-checkbox">
                                    </td>
                                    <td class="col-title">
                                        <a href="#" onclick="viewDetail(5)" style="color: #3b82f6; text-decoration: none;">安保服务外包项目流标公告</a>
                                    </td>
                                    <td class="col-number">FB-2024-005</td>
                                    <td class="col-type">竞争性谈判</td>
                                    <td class="col-type">流标</td>
                                    <td class="col-status">
                                        <span class="status-tag status-rejected">已驳回</span>
                                    </td>
                                    <td class="col-status">
                                        <span class="status-tag status-draft">待发布</span>
                                    </td>
                                    <td class="col-creator">孙七</td>
                                    <td class="col-time">2024-01-18 08:45</td>
                                    <td class="col-actions">
                                        <button class="action-btn change" onclick="changeAnnouncement(5)">变更</button>
                                        <button class="action-btn edit" onclick="editItem(5)">编辑</button>
                                        <button class="action-btn delete" onclick="deleteItem(5)">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="pagination">
                        <div class="pagination-info">
                            显示第 1-5 条，共 25 条记录
                        </div>
                        <div class="pagination-controls">
                            <div class="page-size">
                                <span>每页显示</span>
                                <select onchange="changePageSize(this.value)">
                                    <option value="10">10</option>
                                    <option value="20">20</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                                <span>条</span>
                            </div>
                            <div class="page-nav">
                                <button class="page-btn" disabled>上一页</button>
                                <button class="page-btn active">1</button>
                                <button class="page-btn">2</button>
                                <button class="page-btn">3</button>
                                <button class="page-btn">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 已办页签 -->
            <div id="done-tab" class="tab-content">
                <!-- 查询区域 -->
                <div class="search-section">
                    <div class="search-form">
                        <div class="form-group">
                            <label class="form-label">流标/中止标题</label>
                            <input type="text" class="form-control" placeholder="请输入流标/中止标题">
                        </div>
                        <div class="form-group">
                            <label class="form-label">审核状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="pending">待审核</option>
                                <option value="processing">审核中</option>
                                <option value="approved">已通过</option>
                                <option value="rejected">未通过</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">发布状态</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="pending">待发布</option>
                                <option value="published">已发布</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">采购方式</label>
                            <select class="form-control">
                                <option value="">全部</option>
                                <option value="public">公开招标</option>
                                <option value="invite">邀请招标</option>
                                <option value="inquiry">询价采购</option>
                                <option value="single">单一来源</option>
                            </select>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-primary">查询</button>
                            <button class="btn btn-secondary">重置</button>
                            <button class="btn btn-outline" onclick="toggleAdvanced()">高级查询</button>
                        </div>
                    </div>
                    
                    <!-- 高级查询 -->
                    <div class="advanced-search" id="advanced-search-done">
                        <div class="search-form">
                            <div class="form-group">
                                <label class="form-label">流标/中止类型</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="failed">流标</option>
                                    <option value="terminated">中止</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">采购类型</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="goods">货物采购</option>
                                    <option value="service">服务采购</option>
                                    <option value="engineering">工程采购</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">采购组织方式</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="centralized">集中采购</option>
                                    <option value="decentralized">分散采购</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">资金来源</label>
                                <select class="form-control">
                                    <option value="">全部</option>
                                    <option value="fiscal">财政资金</option>
                                    <option value="self">自有资金</option>
                                    <option value="loan">贷款资金</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">招标金额</label>
                                <input type="text" class="form-control" placeholder="请输入金额范围">
                            </div>
                            <div class="form-group">
                                <label class="form-label">申请人</label>
                                <input type="text" class="form-control" placeholder="请输入申请人">
                            </div>
                            <div class="form-group">
                                <label class="form-label">招标时间</label>
                                <input type="date" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="form-label">计划时间</label>
                                <input type="date" class="form-control">
                            </div>
                            <div class="form-group">
                                <label class="form-label">创建时间</label>
                                <input type="date" class="form-control">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能操作栏 -->
                <div class="action-bar">
                    <div class="action-buttons">
                        <button class="btn btn-success" onclick="createNew()">新建流标/中止</button>
                        <button class="btn btn-secondary" onclick="exportData()">导出数据</button>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="table-container">
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th class="col-checkbox">
                                        <input type="checkbox" id="selectAllDone" onchange="toggleSelectAll()">
                                    </th>
                                    <th class="col-title">流标/中止标题</th>
                                    <th class="col-number">项目编号</th>
                                    <th class="col-type">采购方式</th>
                                    <th class="col-type">流标/中止类型</th>
                                    <th class="col-status">审核状态</th>
                                    <th class="col-status">发布状态</th>
                                    <th class="col-creator">创建人</th>
                                    <th class="col-time">创建时间</th>
                                    <th class="col-actions">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('FB-2024-006')">清洁服务外包项目流标公告</a>
                                    </td>
                                    <td>FB-2024-006</td>
                                    <td>公开招标</td>
                                    <td>流标</td>
                                    <td><span class="status-tag status-approved">已审核</span></td>
                                    <td><span class="status-tag status-published">已发布</span></td>
                                    <td>陈八</td>
                                    <td>2024-01-08</td>
                                    <td class="frozen-column action-col">
                                        <!-- 已发布状态，无可用操作 -->
                                    </td>
                                </tr>
                                <tr>
                                    <td class="frozen-column checkbox-col">
                                        <input type="checkbox">
                                    </td>
                                    <td class="frozen-column name-col">
                                        <a href="#" class="table-link" onclick="viewDetail('FB-2024-007')">绿化养护服务采购中止公告</a>
                                    </td>
                                    <td>FB-2024-007</td>
                                    <td>邀请招标</td>
                                    <td>中止</td>
                                    <td><span class="status-tag status-approved">已审核</span></td>
                                    <td><span class="status-tag status-published">已发布</span></td>
                                    <td>周九</td>
                                    <td>2024-01-05</td>
                                    <td class="frozen-column action-col">
                                        <!-- 已发布状态，无可用操作 -->
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="pagination">
                        <div class="pagination-info">
                            显示第 1-2 条，共 2 条记录
                        </div>
                        <div class="pagination-controls">
                            <div class="page-size">
                                <span>每页显示</span>
                                <select onchange="changePageSize(this.value)">
                                    <option value="10">10</option>
                                    <option value="20" selected>20</option>
                                    <option value="50">50</option>
                                </select>
                                <span>条</span>
                            </div>
                            <div class="page-nav">
                                <button class="page-btn" disabled>上一页</button>
                                <button class="page-btn active">1</button>
                                <button class="page-btn" disabled>下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="help-modal" class="help-modal">
        <div class="help-content">
            <div class="help-header">
                <h3 class="help-title">公告管理功能说明</h3>
                <button class="close-btn" onclick="hideHelp()">&times;</button>
            </div>
            <div class="help-body">
                <div class="help-section">
                    <h4>功能概述</h4>
                    <p>公告管理模块用于管理招标采购公告的全生命周期，包括公告的创建、编辑、审核、发布、撤回等操作。支持多种公告类型和状态管理。</p>
                </div>
                
                <div class="help-section">
                    <h4>页签说明</h4>
                    <ul class="help-list">
                        <li><strong>全部公告：</strong>显示所有状态的公告列表</li>
                        <li><strong>草稿：</strong>显示处于草稿状态的公告</li>
                        <li><strong>待审核：</strong>显示已提交等待审核的公告</li>
                        <li><strong>已发布：</strong>显示已审核通过并发布的公告</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>操作说明</h4>
                    <ul class="help-list">
                        <li><strong>新建：</strong>创建新的招标采购公告</li>
                        <li><strong>查看：</strong>查看公告的详细信息</li>
                        <li><strong>编辑：</strong>修改公告内容（仅草稿和已驳回状态可编辑）</li>
                        <li><strong>审核：</strong>对提交的公告进行审核</li>
                        <li><strong>删除：</strong>删除公告（仅草稿状态可删除）</li>
                        <li><strong>批量操作：</strong>支持批量审核、批量删除等操作</li>
                        <li><strong>导出数据：</strong>将公告数据导出为Excel文件</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>状态说明</h4>
                    <ul class="help-list">
                        <li><strong>草稿：</strong>公告已创建但未提交审核</li>
                        <li><strong>待审核：</strong>公告已提交，等待审核</li>
                        <li><strong>已审核：</strong>公告审核通过，等待发布</li>
                        <li><strong>已发布：</strong>公告已正式发布</li>
                        <li><strong>已驳回：</strong>公告审核未通过，需要修改</li>
                        <li><strong>已撤回：</strong>公告已撤回，不再有效</li>
                    </ul>
                </div>
                
                <div class="help-section">
                    <h4>查询功能</h4>
                    <ul class="help-list">
                        <li><strong>基础查询：</strong>支持按流标/中止标题、项目编号、状态进行查询</li>
                        <li><strong>高级查询：</strong>支持按创建人、创建时间、流标/中止类型等条件查询</li>
                        <li><strong>组合查询：</strong>支持多个条件组合查询</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页签切换
        function switchTab(tabName) {
            // 隐藏所有页签内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有页签的激活状态
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示选中的页签内容
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 激活选中的页签
            event.target.classList.add('active');
        }

        // 高级查询切换
        function toggleAdvanced() {
            const advancedSearch = document.getElementById('advanced-search');
            const advancedSearchDone = document.getElementById('advanced-search-done');
            
            // 切换待办页签的高级查询
            if (advancedSearch) {
                advancedSearch.classList.toggle('show');
            }
            
            // 切换已办页签的高级查询
            if (advancedSearchDone) {
                advancedSearchDone.classList.toggle('show');
            }
        }

        // 全选/取消全选
        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.row-checkbox');
            
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }

        // 帮助弹窗
        function showHelp() {
            document.getElementById('help-modal').style.display = 'block';
        }

        function hideHelp() {
            document.getElementById('help-modal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideHelp();
            }
        });

        // 业务操作函数
        function createNew() {
            window.location.href = '流标或中止管理-新建编辑页.html';
        }

        function viewDetail(id) {
            window.location.href = `流标或中止管理-详情页.html?id=${id}`;
        }

        function auditItem(id) {
            window.location.href = `流标或中止管理-审核页.html?id=${id}`;
        }

        function editItem(id) {
            window.location.href = `流标或中止管理-新建编辑页.html?id=${id}`;
        }

        function deleteItem(id) {
            if (confirm('确定要删除这个流标/中止记录吗？删除后无法恢复。')) {
                console.log('删除流标/中止记录:', id);
                alert('删除成功！');
                // 这里应该调用删除接口，并刷新列表
            }
        }

        function batchAudit() {
            const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
            if (checkedBoxes.length === 0) {
                alert('请选择要审核的流标/中止记录');
                return;
            }
            console.log('批量审核:', checkedBoxes.length, '个流标/中止记录');
            alert('批量审核功能待实现');
        }

        function batchDelete() {
            const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
            if (checkedBoxes.length === 0) {
                alert('请选择要删除的流标/中止记录');
                return;
            }
            if (confirm(`确定要删除选中的 ${checkedBoxes.length} 个流标/中止记录吗？删除后无法恢复。`)) {
                console.log('批量删除:', checkedBoxes.length, '个流标/中止记录');
                alert('批量删除成功！');
                // 这里应该调用批量删除接口，并刷新列表
            }
        }

        function exportData() {
            console.log('导出数据');
            alert('数据导出功能待实现');
        }

        function searchData() {
            console.log('执行查询');
            alert('查询功能待实现');
        }

        function resetSearch() {
            // 重置所有查询表单
            document.querySelectorAll('.search-form input, .search-form select').forEach(input => {
                input.value = '';
            });
            document.querySelectorAll('.advanced-form input, .advanced-form select').forEach(input => {
                input.value = '';
            });
            console.log('重置查询条件');
        }

        function changePageSize(size) {
            console.log('改变每页显示数量:', size);
            // 这里应该重新加载数据
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('公告管理页面加载完成');
        });
    </script>
</body>
</html>