# 供应商管理模块开发任务

## 任务概述

### 模块目标

开发供应商管理模块的完整功能，实现供应商的全生命周期管理，包括新建、编辑、查看、删除等核心业务流程。

### 开发范围

- **API层**: 供应商数据的HTTP请求封装
- **Adapters层**: 供应商数据的格式转换和适配
- **Store层**: 供应商状态管理和业务逻辑
- **路由配置**: 供应商管理模块的路由组织
- **页面开发**: 列表页、详情页、新建/编辑页

### 输出文件清单

```
src/apps/bidding-procurement/modules/supplier-management/
├── api/index.js              # API层
├── adapters/index.js         # 数据转换层
├── views/                    # 视图层
│   ├── list/index.vue        # 列表页
│   ├── detail/index.vue      # 详情/审核页
│   └── create/index.vue      # 新建/编辑页
├── __mocks__/index.js        # Mock数据
├── store.js                  # 状态管理
└── router.js                 # 路由配置
```

## 技术规范

### 分层架构约束

- **开发顺序**: API → Adapters → Store → Views → 路由集成
- **调用关系**: API层 → Adapters层 → Store层 → Views层
- **禁止**: 跨层调用、反向调用

### 组件使用规范

- **表格组件**: 使用FuniCurdV2组件实现列表功能
- **表单组件**: 使用FuniForm表单组件
- **富文本编辑**: 使用内置富文本编辑器
- **文件上传**: 使用FuniUpload组件

## 全局参数配置

```javascript
const moduleParams = {
  MODULE_NAME: '供应商管理',
  ENTITY_NAME: '供应商',
  API_ENDPOINT: '/api/suppliers',
  STORE_NAME: 'useSupplierStore',
  PRIMARY_KEY: 'id',
  DISPLAY_FIELD: 'supplierName'
};
```

## 开发流程

### 第一阶段：基础设施准备

#### 1.1 API层开发

**文件**: `src/apps/bidding-procurement/modules/supplier-management/api/index.js`

**模板**: `docs/prompts/frontend/templates/code-templates/api-layer.js`

**接口清单**:

- `getSupplierList(params)` - 获取供应商列表
- `getSupplierDetail(id)` - 获取供应商详情
- `createSupplier(data)` - 创建供应商
- `updateSupplier(id, data)` - 更新供应商
- `deleteSupplier(id)` - 删除供应商

#### 1.2 Adapters层开发

**文件**: `src/apps/bidding-procurement/modules/supplier-management/adapters/index.js`

**模板**: `docs/prompts/frontend/templates/code-templates/adapters-layer.js`

**转换函数**:

- `adaptSupplierList(data)` - 供应商列表数据适配
- `adaptSupplierDetail(data)` - 供应商详情数据适配
- `adaptSupplierForm(data)` - 供应商表单数据适配

#### 1.3 Store层开发

**文件**: `src/apps/bidding-procurement/modules/supplier-management/store.js`

**模板**: `docs/prompts/frontend/templates/code-templates/store-layer.js`

**状态管理**:

- 供应商列表状态
- 当前供应商详情
- 表单数据状态
- 加载状态管理

### 第二阶段：路由配置

#### 2.1 模块路由配置

**文件**: `src/apps/bidding-procurement/modules/supplier-management/router.js`

**规范**: `docs/prompts/frontend/core/router-standards.md`

**路由结构**:

```javascript
{
  path: 'supplier-management',
  name: 'SupplierManagement',
  meta: { title: '供应商管理', isMenu: true },
  children: [
    {
      path: 'list',
      name: 'SupplierManagementList',
      component: () => import('./views/list/index.vue'),
      meta: { title: '供应商列表' }
    },
    {
      path: 'detail/:id',
      name: 'SupplierManagementDetail',
      component: () => import('./views/detail/index.vue'),
      meta: { title: '供应商详情' }
    },
    {
      path: 'create',
      name: 'SupplierManagementCreate',
      component: () => import('./views/create/index.vue'),
      meta: { title: '新建供应商' }
    },
    {
      path: 'edit/:id',
      name: 'SupplierManagementEdit',
      component: () => import('./views/create/index.vue'),
      meta: { title: '编辑供应商' }
    }
  ]
}
```

### 第三阶段：页面开发

#### 3.1 列表页开发

**文件**: `src/apps/bidding-procurement/modules/supplier-management/views/list/index.vue`

**模板**: `docs/prompts/frontend/templates/page-templates/list-page.vue`

**功能特性**:

- 使用FuniListPageV2组件
- 配置多页签（全部）
- 集成搜索功能（供应商名称、企业代码、联系人、联系方式、更新时间、创建时间）
- 实现数据加载和展示
- 添加操作按钮（查看、编辑、删除）和事件处理

#### 3.2 详情/审核页开发

**文件**: `src/apps/bidding-procurement/modules/supplier-management/views/detail/index.vue`

**模板**: `docs/prompts/frontend/templates/page-templates/detail-page.vue`

**页面结构**:

基本信息页签、账户信息页签、扩展信息页签、税务信息页签、项目总揽页签、流程记录页签

#### 3.3 新建/编辑页开发

**文件**: `src/apps/bidding-procurement/modules/supplier-management/views/create/index.vue`

**模板**: `docs/prompts/frontend/templates/page-templates/add-page.vue`

**功能特性**:

分页表单、文件上传、表单验证、多页签信息管理

### 第四阶段：模块集成验证

#### 4.1 路由集成

将供应商管理模块路由集成到招标采购系统模块中：

**更新文件**: `src/apps/bidding-procurement/routers/index.js`

**集成步骤**:

1. 导入供应商管理路由：`import supplierManagementRouter from '../modules/supplier-management/router.js';`
2. 将路由添加到children数组中：添加到系统级路由的children数组中
3. 验证路由层级结构正确

#### 4.2 功能验证

- [ ] 供应商信息CRUD功能正常
- [ ] 搜索筛选功能正常
- [ ] 详情页多页签展示正常
- [ ] 项目总揽统计数据正确
- [ ] 文件上传功能正常

## 验收标准

### 功能完整性

- [ ] 供应商列表查询和展示
- [ ] 供应商详情查看
- [ ] 供应商新建和编辑
- [ ] 供应商删除功能
- [ ] 高级搜索功能
- [ ] 项目总揽统计

### 技术规范

- [ ] 符合分层架构原则
- [ ] 路由配置符合规范
- [ ] 组件使用符合约束
- [ ] 代码质量符合标准

### 用户体验

- [ ] 界面布局合理美观
- [ ] 交互流程顺畅
- [ ] 错误提示友好
- [ ] 响应速度满足要求

## 模板资源

### 代码模板

- API层模板: `docs/prompts/frontend/templates/code-templates/api-layer.js`
- Adapters层模板: `docs/prompts/frontend/templates/code-templates/adapters-layer.js`
- Store层模板: `docs/prompts/frontend/templates/code-templates/store-layer.js`

### 页面模板

- 列表页模板: `docs/prompts/frontend/templates/page-templates/list-page.vue`
- 详情/审核页模板: `docs/prompts/frontend/templates/page-templates/detail-page.vue`
- 新建/编辑页模板: `docs/prompts/frontend/templates/page-templates/add-page.vue`

### 配置规范

- 路由配置规范: `docs/prompts/frontend/core/router-standards.md`
- 架构标准: `docs/prompts/frontend/core/architecture-standards.md`
- 开发流程: `docs/prompts/frontend/core/development-workflow.md`

## 业务规则

### 状态流转规则

无特殊状态流转，标准CRUD操作

### 字段验证规则

- 供应商名称：必填，长度2-100字符
- 联系人：必填，长度2-50字符
- 联系电话：必填，符合电话号码格式
- 联系邮箱：必填，符合邮箱格式
- 税务登记号：必填，符合税务登记号格式

## 数据结构

### 供应商实体结构

```javascript
{
  id: 'string',                    // 主键ID
  supplierName: 'string',          // 供应商名称
  domesticForeign: 'string',       // 境内外企业
  isBranch: 'boolean',             // 是否分公司
  industryCategory: 'string',      // 行业分类
  contactPerson: 'string',         // 联系人
  contactPhone: 'string',          // 联系电话
  contactEmail: 'string',          // 联系邮箱
  businessScope: 'string',         // 经营范围
  legalRepresentative: 'string',   // 法定代表人
  createdAt: 'datetime',           // 创建时间
  updatedAt: 'datetime'            // 更新时间
}
```

## 注意事项

1. 供应商管理为基础主数据模块，无复杂工作流
2. 详情页需要展示供应商参与的项目统计信息
3. 税务信息页签需要支持文件上传功能
4. 项目总揽页签需要调用相关项目数据接口
