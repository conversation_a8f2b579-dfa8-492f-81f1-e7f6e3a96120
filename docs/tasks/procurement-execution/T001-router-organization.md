# T001 - 采购执行管理路由组织配置

## 任务概述

**目标**: 配置采购执行管理中间层模块的路由组织，整合所有子模块的路由配置
**模块名称**: 采购执行管理
**模块类型**: 中间层模块
**规范文档**: `docs/prompts/frontend/core/router-standards.md`
**输出文件**: `src/apps/bidding-procurement/modules/procurement-execution/router.js`

## 技术规范

### 中间层模块路由特征
- **路由组织**: 作为容器路由，整合所有子模块路由
- **路径格式**: 使用kebab-case格式
- **组件路径**: 不配置component，仅作为路由容器
- **元数据**: 必须包含title和isMenu配置

### 子模块路由集成
- **导入子模块路由**: 从各子模块的router.js文件导入路由配置
- **路由层级**: 建立完整的路由层级结构
- **路径继承**: 子模块路由使用相对路径

## 模块参数

### 基础参数
- **MODULE_NAME**: procurement-execution
- **MODULE_TITLE**: 采购执行管理
- **SYSTEM_PATH**: bidding-procurement
- **PARENT_PATH**: /procurement-execution

### 子模块列表
1. **项目标段管理** (project-section)
2. **公告管理** (announcement)
3. **补遗澄清答疑管理** (clarification)
4. **评标结果公示管理** (bid-result)
5. **中标结果公示管理** (award-result)
6. **签约履行管理** (contract-performance)
7. **流标或中止管理** (bid-failure)

## 实现步骤

### 步骤1: 创建路由组织文件
创建 `src/apps/bidding-procurement/modules/procurement-execution/router.js` 文件

### 步骤2: 导入子模块路由
```javascript
// 导入所有子模块路由配置
import projectSectionRouter from './project-section/router.js';
import announcementRouter from './announcement/router.js';
import clarificationRouter from './clarification/router.js';
import bidResultRouter from './bid-result/router.js';
import awardResultRouter from './award-result/router.js';
import contractPerformanceRouter from './contract-performance/router.js';
import bidFailureRouter from './bid-failure/router.js';
```

### 步骤3: 配置路由结构
```javascript
export default {
  path: '/procurement-execution',
  name: 'ProcurementExecution',
  meta: {
    title: '采购执行管理',
    isMenu: true
  },
  children: [
    projectSectionRouter,
    announcementRouter,
    clarificationRouter,
    bidResultRouter,
    awardResultRouter,
    contractPerformanceRouter,
    bidFailureRouter
  ]
};
```

### 步骤4: 验证路由配置
- 检查所有子模块路由文件存在性
- 验证路由路径格式正确性
- 确认路由层级结构完整性

## 路由配置模板

```javascript
/**
 * 采购执行管理 - 路由组织配置
 * 
 * 基于 router-standards.md 规范配置
 * - 中间层模块路由组织
 * - 整合所有子模块路由配置
 * - 建立完整的路由层级结构
 */

// 导入子模块路由配置
import projectSectionRouter from './project-section/router.js';
import announcementRouter from './announcement/router.js';
import clarificationRouter from './clarification/router.js';
import bidResultRouter from './bid-result/router.js';
import awardResultRouter from './award-result/router.js';
import contractPerformanceRouter from './contract-performance/router.js';
import bidFailureRouter from './bid-failure/router.js';

export default {
  path: '/procurement-execution',
  name: 'ProcurementExecution',
  meta: {
    title: '采购执行管理',
    isMenu: true
  },
  children: [
    projectSectionRouter,
    announcementRouter,
    clarificationRouter,
    bidResultRouter,
    awardResultRouter,
    contractPerformanceRouter,
    bidFailureRouter
  ]
};
```

## 验收标准

### 文件创建检查
- [ ] 路由组织文件创建成功
- [ ] 文件路径正确：`src/apps/bidding-procurement/modules/procurement-execution/router.js`
- [ ] 文件语法正确，无语法错误

### 路由配置检查
- [ ] 路由路径格式正确（kebab-case）
- [ ] 元数据配置完整（title、isMenu）
- [ ] 子模块路由导入语句正确
- [ ] 路由层级结构完整

### 集成验证检查
- [ ] 所有子模块路由文件引用路径正确
- [ ] 路由配置符合router-standards.md规范
- [ ] 可以被系统级路由正确导入

### 功能验证检查
- [ ] 路由配置可以正常加载
- [ ] 菜单显示正确（采购执行管理）
- [ ] 子模块路由可以正常访问

## 依赖关系

### 前置依赖
- 所有子模块的路由配置文件必须存在
- 子模块路由文件路径必须正确
- router-standards.md规范已加载

### 后续依赖
- 系统级路由需要导入此路由配置
- 各子模块页面开发完成后进行完整测试

## 注意事项

1. **严格遵循router-standards.md规范**，确保路由配置标准化
2. **验证子模块路由文件存在性**，避免导入错误
3. **使用相对路径导入子模块路由**，保持路径简洁
4. **确保路由层级结构正确**，支持菜单嵌套显示
5. **不配置component属性**，中间层模块仅作为路由容器
6. **遵循Airbnb JavaScript规范**进行代码编写

## 子模块路由依赖说明

此任务仅创建路由组织文件，各子模块的具体路由配置需要在各自的模块开发任务中完成：

- 项目标段管理：`./project-section/router.js`
- 公告管理：`./announcement/router.js`
- 补遗澄清答疑管理：`./clarification/router.js`
- 评标结果公示管理：`./bid-result/router.js`
- 中标结果公示管理：`./award-result/router.js`
- 签约履行管理：`./contract-performance/router.js`
- 流标或中止管理：`./bid-failure/router.js`

## 系统集成说明

完成此任务后，需要在系统级路由文件中导入此路由配置：

```javascript
// src/apps/bidding-procurement/routers/index.js
import procurementExecutionRouter from '../modules/procurement-execution/router.js';

export default {
  // ... 其他配置
  children: [
    // ... 其他模块路由
    procurementExecutionRouter,
    // ... 其他模块路由
  ]
};
```
