# T001 - 项目标段管理基础设施准备

**任务编号**: T001  
**任务名称**: 基础设施准备（API + Adapters + Store）  
**模块名称**: 项目标段管理  
**任务类型**: 基础设施搭建  
**预计工时**: 2小时  

## 任务概述

### 目标
为项目标段管理模块创建完整的基础设施层，包括API层、Adapters层和Store层，确保数据流和业务逻辑的正确实现。

### 模板文件
- API层模板: `docs/prompts/frontend/templates/code-templates/api-layer.js`
- Store层模板: `docs/prompts/frontend/templates/code-templates/store-layer.js`
- Adapters层模板: `docs/prompts/frontend/templates/code-templates/adapters-layer.js`

### 输出文件
- `src/apps/bidding-procurement/modules/procurement-execution/project-section/api/index.js`
- `src/apps/bidding-procurement/modules/procurement-execution/project-section/adapters/index.js`
- `src/apps/bidding-procurement/modules/procurement-execution/project-section/store.js`

## 技术规范

### 分层架构约束
严格遵循 `docs/prompts/frontend/core/architecture-standards.md` 规范：
- **API层**: 仅调用 `window.$http`，封装HTTP请求
- **Adapters层**: 纯函数数据转换，不调用其他层
- **Store层**: 业务逻辑和状态管理，仅调用API层和Adapters层

### 开发顺序
必须按照以下顺序创建文件：
1. API层 → 2. Adapters层 → 3. Store层

## 模板参数映射

### 占位符参数表
```javascript
{
  "{{MODULE_NAME}}": "项目标段管理",
  "{{ENTITY_NAME}}": "项目标段", 
  "{{ENTITY_NAME_EN}}": "projectSection",
  "{{API_ENDPOINT}}": "/api/project-sections",
  "{{STORE_NAME}}": "useProjectSectionStore",
  "{{PRIMARY_KEY}}": "id"
}
```

### 业务特定参数
```javascript
{
  "{{WORKFLOW_ENABLED}}": true,
  "{{AUDIT_FIELDS}}": ["auditStatus", "auditTime", "auditUser"],
  "{{SECTION_FIELDS}}": ["sectionName", "sectionAmount", "sectionDescription"],
  "{{PROJECT_RELATION}}": "projectPlanId"
}
```

## 实现步骤

### 步骤1: 创建API层
1. 使用模板 `docs/prompts/frontend/templates/code-templates/api-layer.js`
2. 替换占位符参数
3. 添加项目标段特有的API接口：
   - 根据项目计划查询标段: `getProjectSectionsByPlan(planId)`
   - 标段审核接口: `auditProjectSection(id, auditData)`
   - 标段状态更新: `updateSectionStatus(id, status)`

### 步骤2: 创建Adapters层
1. 使用模板 `docs/prompts/frontend/templates/code-templates/adapters-layer.js`
2. 实现数据转换函数：
   - 列表数据适配器: `adaptProjectSectionList`
   - 详情数据适配器: `adaptProjectSectionDetail`
   - 表单数据适配器: `adaptProjectSectionForm`
   - 工作流数据适配器: `adaptWorkflowData`

### 步骤3: 创建Store层
1. 使用模板 `docs/prompts/frontend/templates/code-templates/store-layer.js`
2. 实现业务逻辑方法：
   - 获取项目标段列表: `fetchProjectSectionList`
   - 获取项目标段详情: `fetchProjectSectionDetail`
   - 创建项目标段: `createProjectSection`
   - 更新项目标段: `updateProjectSection`
   - 删除项目标段: `deleteProjectSection`
   - 审核项目标段: `auditProjectSection`

## 验收标准

### 功能验收
- [ ] API层所有接口方法正确实现
- [ ] Adapters层数据转换函数正确实现
- [ ] Store层业务逻辑方法正确实现
- [ ] 所有文件语法正确，无错误

### 架构验收
- [ ] 严格遵循分层架构原则
- [ ] API层仅使用 `window.$http`
- [ ] Adapters层为纯函数，无副作用
- [ ] Store层正确调用API层和Adapters层
- [ ] 导入导出关系正确

### 代码质量验收
- [ ] 代码符合Airbnb JavaScript规范
- [ ] 注释完整，包含JSDoc文档
- [ ] 错误处理机制完善
- [ ] 变量命名规范，语义清晰

## 依赖关系

### 前置依赖
- 无（基础设施层为起始任务）

### 后续依赖
- T002 路由配置（依赖Store层完成）
- T003 页面开发（依赖所有基础设施层完成）

### 外部依赖
- `window.$http` 全局HTTP客户端
- Pinia状态管理库
- Vue 3 Composition API

## 注意事项

### 关键约束
1. **严格按顺序开发**: API → Adapters → Store，不可跳跃
2. **依赖验证**: 创建每个文件前验证被导入的文件已存在
3. **工作流集成**: 项目标段管理包含审批流程，需要特殊处理
4. **数据级联**: 项目标段继承项目计划的核心数据

### 常见问题预防
1. **导入路径错误**: 使用相对路径导入同模块文件
2. **循环依赖**: Store层不可被API层或Adapters层导入
3. **状态管理**: 确保响应式数据正确定义
4. **错误处理**: API调用失败时的降级处理

## 质量检查清单

### 创建后验证
- [ ] 使用 `view` 工具验证每个文件存在且内容正确
- [ ] 检查所有导入导出语句语法正确
- [ ] 验证占位符全部正确替换
- [ ] 确认业务逻辑符合项目标段管理需求

### 集成验证
- [ ] Store可以正确导入API和Adapters
- [ ] 所有方法调用链路正确
- [ ] 数据流向符合分层架构要求
- [ ] 工作流相关方法正确实现
