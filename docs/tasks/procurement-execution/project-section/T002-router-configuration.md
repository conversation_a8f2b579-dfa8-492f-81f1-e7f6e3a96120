# T002 - 项目标段管理路由配置

**任务编号**: T002  
**任务名称**: 路由配置  
**模块名称**: 项目标段管理  
**任务类型**: 路由配置  
**预计工时**: 1小时  

## 任务概述

### 目标
为项目标段管理模块配置完整的路由结构，包括列表页、详情页、新建页和编辑页的路由配置，确保符合router-standards.md规范。

### 技术规范
严格遵循 `docs/prompts/frontend/core/router-standards.md` 规范，不使用模板文件，直接按规范实现。

### 输出文件
- `src/apps/bidding-procurement/modules/procurement-execution/project-section/router.js`

## 技术规范

### 路由配置规范
基于 `docs/prompts/frontend/core/router-standards.md` 规范：

#### 路径规范
- 使用 kebab-case 格式
- 子路由使用相对路径
- 组件路径使用 @/apps/ 前缀的绝对路径

#### 组件加载规范
- 所有组件使用动态导入（懒加载）
- 组件路径格式: `@/apps/bidding-procurement/modules/procurement-execution/project-section/views/`

#### 元数据配置
- 必需字段: `title`
- 菜单控制: `isMenu` (true/false)

## 路由结构设计

### 模块路由结构
```
/project-section
├── /list (列表页)
├── /detail/:id (详情页)
├── /create (新建页)
├── /edit/:id (编辑页)
└── /audit/:id (审核页)
```

### 页面类型说明
- **列表页**: 项目标段列表展示和查询
- **详情页**: 项目标段详细信息查看
- **新建页**: 创建新的项目标段
- **编辑页**: 编辑现有项目标段
- **审核页**: 项目标段审核工作流

## 实现步骤

### 步骤1: 创建基础路由结构
创建 `src/apps/bidding-procurement/modules/procurement-execution/project-section/router.js` 文件：

```javascript
export default {
  path: 'project-section',
  name: 'ProjectSection',
  redirect: 'project-section/list',
  meta: {
    title: '项目标段管理',
    isMenu: true
  },
  children: [
    // 子路由配置
  ]
};
```

### 步骤2: 配置列表页路由
```javascript
{
  path: 'list',
  name: 'ProjectSectionList',
  component: () => import('./views/list/index.vue'),
  meta: {
    title: '项目标段列表',
    isMenu: false
  }
}
```

### 步骤3: 配置详情页路由
```javascript
{
  path: 'detail/:id',
  name: 'ProjectSectionDetail',
  component: () => import('./views/detail/index.vue'),
  meta: {
    title: '项目标段详情',
    isMenu: false
  }
}
```

### 步骤4: 配置新建页路由
```javascript
{
  path: 'create',
  name: 'ProjectSectionCreate',
  component: () => import('./views/create/index.vue'),
  meta: {
    title: '新建项目标段',
    isMenu: false
  }
}
```

### 步骤5: 配置编辑页路由
```javascript
{
  path: 'edit/:id',
  name: 'ProjectSectionEdit',
  component: () => import('./views/edit/index.vue'),
  meta: {
    title: '编辑项目标段',
    isMenu: false
  }
}
```

### 步骤6: 配置审核页路由
```javascript
{
  path: 'audit/:id',
  name: 'ProjectSectionAudit',
  component: () => import('./views/audit/index.vue'),
  meta: {
    title: '审核项目标段',
    isMenu: false,
  }
}
```

## 路由参数配置

### 动态路由参数
- `:id` - 项目标段ID，用于详情页、编辑页、审核页

### 查询参数支持
- `planId` - 项目计划ID，用于从计划页面跳转
- `status` - 状态筛选，用于列表页筛选
- `tab` - 标签页切换，用于详情页标签

## 验收标准

### 功能验收
- [ ] 所有路由路径配置正确
- [ ] 组件路径使用绝对路径格式
- [ ] 动态导入语法正确
- [ ] 路由参数配置完整

### 规范验收
- [ ] 路径使用 kebab-case 格式
- [ ] 元数据包含必需的 title 字段
- [ ] isMenu 配置正确（主路由true，子路由false）

### 导航验收
- [ ] 默认重定向到列表页
- [ ] 页面标题正确显示
- [ ] 菜单显示状态正确

## 依赖关系

### 前置依赖
- T001 基础设施准备（Store层完成）

### 后续依赖
- T003 页面开发（依赖路由配置完成）

### 系统集成依赖
- 父级路由: `src/apps/bidding-procurement/modules/procurement-execution/router.js`
- 系统级路由: `src/apps/bidding-procurement/routers/index.js`

## 集成说明

### 父级路由集成
完成路由配置后，需要在父级路由中导入：

```javascript
// src/apps/bidding-procurement/modules/procurement-execution/router.js
import projectSectionRouter from './project-section/router.js';

export default {
  // ... 其他配置
  children: [
    projectSectionRouter,
    // ... 其他子模块路由
  ]
};
```

### 系统级路由集成
确保采购执行管理路由已集成到系统级路由中：

```javascript
// src/apps/bidding-procurement/routers/index.js
import procurementExecutionRouter from '../modules/procurement-execution/router.js';
```

## 注意事项

### 关键约束
1. **路径一致性**: 确保路由路径与目录结构一致
2. **组件引用**: 所有component引用的Vue文件必须在T003任务中创建
3. **参数验证**: 动态路由参数需要在组件中进行有效性验证
4. **权限控制**: 不在路由层配置权限，在组件内部处理

### 常见问题预防
1. **路径错误**: 避免使用绝对路径，子路由使用相对路径
2. **组件路径**: 确保组件导入路径正确，使用@/apps/前缀
3. **重定向循环**: 避免重定向路径配置错误导致循环
4. **元数据缺失**: 确保所有路由都有title元数据

## 质量检查清单

### 配置验证
- [ ] 路由文件语法正确
- [ ] 所有路径格式符合规范
- [ ] 组件导入路径正确
- [ ] 元数据配置完整

### 功能验证
- [ ] 默认路由重定向正确
- [ ] 动态路由参数配置正确
- [ ] 路由名称唯一且语义清晰
- [ ] 面包屑和标题显示正确

### 集成验证
- [ ] 父级路由正确导入
- [ ] 系统级路由集成正确
- [ ] 菜单显示状态正确
- [ ] 路由层级结构清晰
