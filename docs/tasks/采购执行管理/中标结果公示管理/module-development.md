# 中标结果公示管理模块开发任务

## 任务概述

### 模块目标

开发中标结果公示管理模块的完整功能，实现中标结果公示的全生命周期管理，包括新建、编辑、审核、发布、撤销等核心业务流程。

### 开发范围

- **API层**: 中标结果公示数据的HTTP请求封装
- **Adapters层**: 中标结果公示数据的格式转换和适配
- **Store层**: 中标结果公示状态管理和业务逻辑
- **路由配置**: 中标结果公示管理模块的路由组织
- **页面开发**: 列表页、详情页、新建/编辑页、审核页

### 输出文件清单

```
src/apps/bidding-procurement/modules/procurement-execution/award-result-announcement/
├── api/index.js              # API层
├── adapters/index.js         # 数据转换层
├── views/                    # 视图层
│   ├── list/index.vue        # 列表页
│   ├── detail/index.vue      # 详情页
│   ├── create/index.vue      # 新建/编辑页
│   └── audit/index.vue       # 审核页
├── __mocks__/index.js        # Mock数据
├── store.js                  # 状态管理
└── router.js                 # 路由配置
```

## 技术规范

### 分层架构约束

- **开发顺序**: API → Adapters → Store → Views → 路由集成
- **调用关系**: API层 → Adapters层 → Store层 → Views层
- **禁止**: 跨层调用、反向调用

### 组件使用规范

- **表格组件**: 使用FuniCurdV2组件实现列表功能
- **表单组件**: 使用FuniForm表单组件
- **富文本编辑**: 使用内置富文本编辑器
- **文件上传**: 使用FuniUpload组件
- **工作流集成**: 使用FuniWorkflow组件

## 全局参数配置

```javascript
const moduleParams = {
  MODULE_NAME: '中标结果公示管理',
  ENTITY_NAME: '中标结果公示',
  API_ENDPOINT: '/api/award-result-announcements',
  STORE_NAME: 'useAwardResultAnnouncementStore',
  PRIMARY_KEY: 'id',
  DISPLAY_FIELD: 'title',
  MODULE_PATH: 'src/apps/bidding-procurement/modules/procurement-execution/award-result-announcement',
  ROUTE_PATH: 'award-result-announcement',
  ROUTE_NAME: 'AwardResultAnnouncement'
};
```

## 开发流程

### 第一阶段：基础设施准备

#### 1.1 API层开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/award-result-announcement/api/index.js`

**模板**: `docs/prompts/frontend/templates/code-templates/api-layer.js`

**接口清单**:

- `getAwardResultAnnouncementList(params)` - 获取中标结果公示列表
- `getAwardResultAnnouncementDetail(id)` - 获取中标结果公示详情
- `createAwardResultAnnouncement(data)` - 创建中标结果公示
- `updateAwardResultAnnouncement(id, data)` - 更新中标结果公示
- `deleteAwardResultAnnouncement(id)` - 删除中标结果公示
- `submitAwardResultAnnouncement(id)` - 提交审核
- `auditAwardResultAnnouncement(id, data)` - 审核中标结果公示
- `publishAwardResultAnnouncement(id)` - 发布中标结果公示
- `revokeAwardResultAnnouncement(id)` - 撤销中标结果公示

#### 1.2 Adapters层开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/award-result-announcement/adapters/index.js`

**模板**: `docs/prompts/frontend/templates/code-templates/adapters-layer.js`

**转换函数**:

- `adaptAwardResultAnnouncementList` - 列表数据适配
- `adaptAwardResultAnnouncementDetail` - 详情数据适配
- `adaptAwardResultAnnouncementForm` - 表单数据适配
- `adaptWinnerInfo` - 中标人信息适配
- `adaptAuditData` - 审核数据适配

#### 1.3 Store层开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/award-result-announcement/store.js`

**模板**: `docs/prompts/frontend/templates/code-templates/store-layer.js`

**状态管理**:

- 中标结果公示列表状态
- 当前中标结果公示详情
- 表单数据状态
- 中标人信息状态
- 加载状态管理
- 审核流程状态

#### 1.4 Mock数据开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/award-result-announcement/__mocks__/index.js`

**Mock数据内容**:

- 中标结果公示列表数据
- 中标结果公示详情数据
- 中标人信息数据
- 审核历史数据
- 项目关联数据

### 第二阶段：路由配置

#### 2.1 模块路由配置

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/award-result-announcement/router.js`

**规范**: `docs/prompts/frontend/core/router-standards.md`

**路由结构**:

```javascript
{
  path: 'award-result-announcement',
  name: 'AwardResultAnnouncement',
  meta: { title: '中标结果公示管理', isMenu: true },
  children: [
    {
      path: 'list',
      name: 'AwardResultAnnouncementList',
      component: () => import('./views/list/index.vue'),
      meta: { title: '中标结果公示列表' }
    },
    {
      path: 'detail/:id',
      name: 'AwardResultAnnouncementDetail',
      component: () => import('./views/detail/index.vue'),
      meta: { title: '中标结果公示详情' }
    },
    {
      path: 'create',
      name: 'AwardResultAnnouncementCreate',
      component: () => import('./views/create/index.vue'),
      meta: { title: '新建中标结果公示' }
    },
    {
      path: 'edit/:id',
      name: 'AwardResultAnnouncementEdit',
      component: () => import('./views/create/index.vue'),
      meta: { title: '编辑中标结果公示' }
    },
    {
      path: 'audit/:id',
      name: 'AwardResultAnnouncementAudit',
      component: () => import('./views/audit/index.vue'),
      meta: { title: '审核中标结果公示' }
    }
  ]
}
```

### 第三阶段：页面开发

#### 3.1 列表页开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/award-result-announcement/views/list/index.vue`

**模板**: `docs/prompts/frontend/templates/page-templates/list-page.vue`

**页面功能**:

- 状态页签：全部、待审核、审核中、已发布
- 搜索筛选：公告标题、审核状态、发布状态、项目名称
- 列表展示：使用FuniCurdV2组件
- 批量操作：批量删除、批量审核
- 操作按钮：新建、编辑、删除、提交、审核、发布、撤销

#### 3.2 详情页开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/award-result-announcement/views/detail/index.vue`

**模板**: `docs/prompts/frontend/templates/page-templates/detail-page.vue`

**页面功能**:

- 公示详情页签：展示完整的中标结果公示信息
- 项目信息页签：展示关联项目的详细信息
- 操作记录页签：展示公示的操作历史记录

#### 3.3 新建/编辑页开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/award-result-announcement/views/create/index.vue`

**新建/编辑功能**:

- 标段选择：关联项目标段信息
- 基本信息：公告标题、公示时间等
- 中标人信息：从候选人中选择确定中标人
- 成交金额：中标金额或费率
- 业主代表：业主代表姓名
- 富文本编辑：中标情况说明、异常情况说明
- 文件上传：相关附件上传
- 表单验证：完整的数据验证规则

#### 3.4 审核页开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/award-result-announcement/views/audit/index.vue`

**审核功能**:

- 中标结果公示信息展示（只读）
- 审核意见输入
- 审核操作（通过、驳回）
- 审核历史记录

### 第四阶段：模块集成验证

#### 4.1 路由集成

将中标结果公示管理模块路由集成到采购执行管理模块中：

**更新文件**: `src/apps/bidding-procurement/modules/procurement-execution/router.js`

**集成步骤**:

1. 导入中标结果公示管理路由：`import awardResultRouter from './award-result-announcement/router.js';`
2. 取消第15行注释，确保awardResultRouter被正确引用
3. 验证路由层级结构正确

#### 4.2 功能验证

**验证清单**:

- [ ] API接口调用正常
- [ ] 数据适配转换正确
- [ ] 状态管理功能完整
- [ ] 页面渲染和交互正常
- [ ] 路由跳转和参数传递正确
- [ ] 工作流集成功能正常
- [ ] 文件上传和富文本编辑功能正常

## 验收标准

### 功能验收

- [ ] 中标结果公示的增删改查功能完整
- [ ] 审核流程和状态管理正确
- [ ] 中标人选择和成交金额计算准确
- [ ] 富文本编辑和文件上传功能正常
- [ ] 列表筛选和搜索功能正确

### 技术验收

- [ ] 代码符合分层架构规范
- [ ] 组件使用符合FuniUI规范
- [ ] 路由配置符合router-standards.md规范
- [ ] 无跨层调用和反向调用
- [ ] Mock数据完整且格式正确

### 集成验收

- [ ] 模块路由正确集成到父级路由
- [ ] 页面间跳转和参数传递正常
- [ ] 与其他模块的数据关联正确
- [ ] 工作流集成功能完整

## 模板资源引用

- **API层模板**: `docs/prompts/frontend/templates/code-templates/api-layer.js`
- **Adapters层模板**: `docs/prompts/frontend/templates/code-templates/adapters-layer.js`
- **Store层模板**: `docs/prompts/frontend/templates/code-templates/store-layer.js`
- **Mock数据模板**: `docs/prompts/frontend/templates/code-templates/__mock__.js`
- **列表页模板**: `docs/prompts/frontend/templates/page-templates/list-page.vue`
- **详情页模板**: `docs/prompts/frontend/templates/page-templates/detail-page.vue`
- **路由配置规范**: `docs/prompts/frontend/core/router-standards.md`
