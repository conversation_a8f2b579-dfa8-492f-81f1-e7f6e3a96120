# 补遗澄清答疑管理模块开发任务

## 任务概述

### 模块目标

开发补遗澄清答疑管理模块的完整功能，实现补遗、澄清、答疑信息的全生命周期管理，包括新建、编辑、审核、发布、撤销等核心业务流程。

### 开发范围

- **API层**: 补遗澄清答疑数据的HTTP请求封装
- **Adapters层**: 补遗澄清答疑数据的格式转换和适配
- **Store层**: 补遗澄清答疑状态管理和业务逻辑
- **路由配置**: 补遗澄清答疑管理模块的路由组织
- **页面开发**: 列表页、详情页、新建/编辑页、审核页

### 输出文件清单

```
src/apps/bidding-procurement/modules/procurement-execution/clarification-management/
├── api/index.js              # API层
├── adapters/index.js         # 数据转换层
├── views/                    # 视图层
│   ├── list/index.vue        # 列表页
│   ├── detail/index.vue      # 详情页
│   ├── create/index.vue      # 新建/编辑页
│   └── audit/index.vue       # 审核页
├── __mocks__/index.js        # Mock数据
├── store.js                  # 状态管理
└── router.js                 # 路由配置
```

## 技术规范

### 分层架构约束

- **开发顺序**: API → Adapters → Store → Views → 路由集成
- **调用关系**: API层 → Adapters层 → Store层 → Views层
- **禁止**: 跨层调用、反向调用

### 组件使用规范

- **表格组件**: 使用FuniCurdV2组件实现列表功能
- **表单组件**: 使用FuniForm表单组件
- **富文本编辑**: 使用内置富文本编辑器
- **文件上传**: 使用FuniUpload组件

## 全局参数配置

```javascript
const moduleParams = {
  MODULE_NAME: '补遗澄清答疑管理',
  ENTITY_NAME: '补遗澄清答疑',
  API_ENDPOINT: '/api/clarifications',
  STORE_NAME: 'useClarificationStore',
  PRIMARY_KEY: 'id',
  DISPLAY_FIELD: 'title'
};
```

## 开发流程

### 第一阶段：基础设施准备

#### 1.1 API层开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/clarification-management/api/index.js`

**模板**: `docs/prompts/frontend/templates/code-templates/api-layer.js`

**接口清单**:

- getList: 获取补遗澄清答疑列表
- getDetail: 获取补遗澄清答疑详情
- create: 创建补遗澄清答疑
- update: 更新补遗澄清答疑
- delete: 删除补遗澄清答疑
- submit: 提交审核
- audit: 审核操作
- publish: 发布补遗澄清答疑
- revoke: 撤销补遗澄清答疑

#### 1.2 Adapters层开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/clarification-management/adapters/index.js`

**模板**: `docs/prompts/frontend/templates/code-templates/adapters-layer.js`

**转换函数**:

- formatClarificationList: 格式化列表数据
- formatClarificationDetail: 格式化详情数据
- formatCreateData: 格式化创建数据
- formatUpdateData: 格式化更新数据

#### 1.3 Store层开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/clarification-management/store.js`

**模板**: `docs/prompts/frontend/templates/code-templates/store-layer.js`

**状态管理**:

- 补遗澄清答疑列表状态
- 当前补遗澄清答疑详情
- 表单数据状态
- 加载状态管理

### 第二阶段：路由配置

#### 2.1 模块路由配置

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/clarification-management/router.js`

**路由结构**:

```javascript
{
  path: 'clarification-management',
  name: 'ClarificationManagement',
  meta: { title: '补遗澄清答疑管理', isMenu: true },
  children: [
    { path: '', component: () => import('./views/list/index.vue') },
    { path: 'detail/:id', component: () => import('./views/detail/index.vue') },
    { path: 'create', component: () => import('./views/create/index.vue') },
    { path: 'edit/:id', component: () => import('./views/create/index.vue') },
    { path: 'audit/:id', component: () => import('./views/audit/index.vue') }
  ]
}
```

### 第三阶段：页面开发

#### 3.1 列表页开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/clarification-management/views/list/index.vue`

**功能特性**:

- 多条件查询筛选（项目名称、标段名称、类型、状态等）
- 分页列表展示
- 状态标签显示（草稿、待审核、审核中、已发布等）
- 操作按钮（查看、编辑、删除、提交、审核、发布、撤销）
- 批量操作支持

#### 3.2 详情页开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/clarification-management/views/detail/index.vue`

**页面结构**:

- 项目标段信息展示（15个字段自动带出）
- 公告信息展示
- 补遗澄清答疑信息展示
- 审核依据文件展示
- 流程记录展示
- 操作按钮（编辑、删除、提交、审核、发布、撤销、打印、导出）

#### 3.3 新建/编辑页开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/clarification-management/views/create/index.vue`

**表单字段**:

- 项目标段选择（自动带出15个字段）
- 公告标题（自动生成提示）
- 类型选择（补遗、澄清、答疑）
- 是否公示（根据采购金额自动判断）
- 补遗澄清答疑说明（富文本编辑器）
- 审核依据文件上传

#### 3.4 审核页开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/clarification-management/views/audit/index.vue`

**审核功能**:

- 补遗澄清答疑信息展示（只读）
- 审核意见输入
- 审核操作（通过、驳回）
- 审核历史记录

### 第四阶段：模块集成验证

#### 4.1 路由集成

将补遗澄清答疑管理模块路由集成到采购执行管理模块中：

**更新文件**: `src/apps/bidding-procurement/modules/procurement-execution/router.js`

**集成步骤**:

1. 导入补遗澄清答疑管理路由：`import clarificationRouter from './clarification-management/router.js';`
2. 取消第13行注释，确保clarificationRouter被正确引用
3. 验证路由层级结构正确

#### 4.2 功能验证

- 列表查询和操作功能
- 新建和编辑功能
- 审核流程功能
- 发布和撤销功能
- 路由导航功能

## 验收标准

### 功能完整性

- [ ] 补遗澄清答疑列表查询和筛选功能正常
- [ ] 补遗澄清答疑新建和编辑功能正常
- [ ] 补遗澄清答疑审核流程功能正常
- [ ] 补遗澄清答疑发布和撤销功能正常
- [ ] 补遗澄清答疑详情查看功能正常

### 技术规范

- [ ] 符合分层架构原则
- [ ] 路由配置符合规范
- [ ] 组件使用符合约束
- [ ] 代码质量符合标准

### 用户体验

- [ ] 界面布局合理美观
- [ ] 交互流程顺畅
- [ ] 错误提示友好
- [ ] 响应速度满足要求

## 业务规则

### 状态流转

```
草稿 → 待审核 → 审核中 → 审核通过 → 已发布
     ↓         ↓
   删除      审核驳回
```

### 权限控制

- 创建人：可新建、编辑、删除、提交
- 审核人：可审核、通过、驳回
- 发布人：可发布、撤销
- 查看人：可查看详情

### 数据验证

- 公告标题不超过50字符
- 补遗澄清答疑说明为必填项
- 审核依据文件支持PDF、DOC、DOCX、XLS、XLSX格式
- 单个文件不超过10MB

### 业务字段配置

**项目标段信息（自动带出15个字段）**:

- 项目名称、项目编号、项目业主、采购方式
- 标段名称、标段编号、预算金额、采购类型
- 招标代理机构、联系人、联系电话、招标文件获取方式
- 投标截止时间、开标时间、开标地点

**补遗澄清答疑特有字段**:

- 公告标题（自动生成：项目业主全称+标段名称+公告类型）
- 类型（补遗/澄清/答疑）
- 是否公示（根据采购金额自动判断）
- 补遗澄清答疑说明（富文本）
- 审核依据文件（文件上传）

### 自动化规则

- **公告标题自动生成**: 项目业主全称 + 标段名称 + 公告类型
- **是否公示自动判断**: 根据采购金额阈值自动设置
- **项目标段信息自动带出**: 选择标段后自动填充相关信息

## 模板资源

### 代码模板

- API层模板: `docs/prompts/frontend/templates/code-templates/api-layer.js`
- Adapters层模板: `docs/prompts/frontend/templates/code-templates/adapters-layer.js`
- Store层模板: `docs/prompts/frontend/templates/code-templates/store-layer.js`

### 页面模板

- 列表页模板: `docs/prompts/frontend/templates/page-templates/list-page.vue`
- 详情页模板: `docs/prompts/frontend/templates/page-templates/detail-page.vue`

### 配置规范

- 路由配置规范: `docs/prompts/frontend/core/router-standards.md`

## 开发注意事项

### 技术要点

1. **富文本编辑器集成**: 补遗澄清答疑说明需要支持富文本编辑，包括文本格式化、表格插入等功能
2. **文件上传处理**: 审核依据文件上传需要支持多文件上传、文件预览、文件删除等功能
3. **状态管理**: 需要正确处理各种状态的流转和权限控制
4. **数据联动**: 项目标段选择后需要自动带出相关信息
5. **路由字段准确性**: 路由结构、路由必填字段完整性、路由字段准确性

### 开发顺序建议

1. 先完成基础的CRUD功能（API、Adapters、Store层）
2. 再开发列表页和详情页的基础展示功能
3. 然后开发新建/编辑页的表单功能
4. 最后开发审核页和状态流转功能

### 测试要点

- 各种状态下的操作权限验证
- 文件上传功能的边界测试
- 富文本编辑器的兼容性测试
- 数据联动的准确性测试
