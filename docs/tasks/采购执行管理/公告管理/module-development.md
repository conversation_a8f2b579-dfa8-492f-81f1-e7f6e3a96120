# 公告管理模块开发任务

## 任务概述

### 模块目标

开发公告管理模块的完整功能，实现招标公告的全生命周期管理，包括新建、编辑、审核、发布、撤销等核心业务流程。

### 开发范围

- **API层**: 公告数据的HTTP请求封装
- **Adapters层**: 公告数据的格式转换和适配
- **Store层**: 公告状态管理和业务逻辑
- **路由配置**: 公告模块的路由组织
- **页面开发**: 列表页、详情页、新建/编辑页

### 输出文件清单

```
src/apps/bidding-procurement/modules/procurement-execution/announcement-management/
├── api/index.js              # API层
├── adapters/index.js         # 数据转换层
├── views/                    # 视图层
│   ├── list/index.vue        # 列表页
│   ├── detail/index.vue      # 详情页
│   └── create/index.vue      # 新建/编辑页
├── __mocks__/index.js        # Mock数据
├── store.js                  # 状态管理
└── router.js                 # 路由配置
```

## 技术规范

### 分层架构约束

- **开发顺序**: API → Adapters → Store → Views → 路由集成
- **调用关系**: API层 → Adapters层 → Store层 → Views层
- **禁止**: 跨层调用、反向调用

### 组件使用规范

- **表格组件**: 使用FuniCurdV2组件实现列表功能
- **表单组件**: 使用Element Plus表单组件
- **富文本编辑**: 使用内置富文本编辑器
- **文件上传**: 使用FuniUpload组件

## 全局参数配置

```javascript
const moduleParams = {
  MODULE_NAME: '公告管理',
  ENTITY_NAME: '公告',
  API_ENDPOINT: '/api/announcements',
  STORE_NAME: 'useAnnouncementStore',
  PRIMARY_KEY: 'id',
  DISPLAY_FIELD: 'title'
};
```

## 开发流程

### 第一阶段：基础设施准备

#### 1.1 API层开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/announcement-management/api/index.js`

**模板**: `docs/prompts/frontend/templates/code-templates/api-layer.js`

**接口清单**:

- `getAnnouncementList(params)` - 获取公告列表
- `getAnnouncementDetail(id)` - 获取公告详情
- `createAnnouncement(data)` - 创建公告
- `updateAnnouncement(id, data)` - 更新公告
- `deleteAnnouncement(id)` - 删除公告
- `submitAnnouncement(id)` - 提交审核
- `auditAnnouncement(id, data)` - 审核公告
- `publishAnnouncement(id)` - 发布公告
- `revokeAnnouncement(id)` - 撤销公告

#### 1.2 Adapters层开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/announcement-management/adapters/index.js`

**模板**: `docs/prompts/frontend/templates/code-templates/adapters-layer.js`

**转换函数**:

- `formatAnnouncementList(data)` - 格式化列表数据
- `formatAnnouncementDetail(data)` - 格式化详情数据
- `formatAnnouncementForm(data)` - 格式化表单数据

#### 1.3 Store层开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/announcement-management/store.js`

**模板**: `docs/prompts/frontend/templates/code-templates/store-layer.js`

**状态管理**:

- 公告列表状态
- 当前公告详情
- 表单数据状态
- 加载状态管理

### 第二阶段：路由配置

#### 2.1 模块路由配置

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/announcement-management/router.js`

**规范**: `docs/prompts/frontend/core/router-standards.md`

**路由结构**:

```javascript
{
  path: 'announcement-management',
  name: 'AnnouncementManagement',
  meta: { title: '公告管理', isMenu: true },
  children: [
    {
      path: 'list',
      name: 'AnnouncementList',
      component: () => import('./views/list/index.vue'),
      meta: { title: '公告列表' }
    },
    {
      path: 'detail/:id',
      name: 'AnnouncementDetail',
      component: () => import('./views/detail/index.vue'),
      meta: { title: '公告详情' }
    },
    {
      path: 'create',
      name: 'AnnouncementCreate',
      component: () => import('./views/create/index.vue'),
      meta: { title: '新建公告' }
    },
    {
      path: 'edit/:id',
      name: 'AnnouncementEdit',
      component: () => import('./views/create/index.vue'),
      meta: { title: '编辑公告' }
    }
  ]
}
```

### 第三阶段：页面开发

#### 3.1 列表页开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/announcement-management/views/list/index.vue`

**模板**: `docs/prompts/frontend/templates/page-templates/list-page.vue`

**功能特性**:

- 使用FuniCurdV2组件
- 支持高级搜索（公告名称、审核状态、发布状态、采购类型等）
- 动态操作按钮（编辑、删除、提交、审核、发布等）
- 分页和排序功能

#### 3.2 详情页开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/announcement-management/views/detail/index.vue`

**模板**: `docs/prompts/frontend/templates/page-templates/detail-page.vue`

**页面结构**:

- 公告信息页签
- 项目信息页签
- 流程记录页签

#### 3.3 新建/编辑页开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/announcement-management/views/create/index.vue`

**功能特性**:

- 标段选择和信息自动带出
- 富文本编辑器
- 文件上传功能
- 表单验证
- 保存草稿和提交审核

### 第四阶段：模块集成验证

#### 4.1 路由集成

将公告管理模块路由集成到采购执行管理模块中：

**更新文件**: `src/apps/bidding-procurement/modules/procurement-execution/router.js`

**集成步骤**:

1. 导入公告管理路由：`import announcementRouter from './announcement-management/router.js';`
2. 将路由添加到children数组中：取消第28行注释，确保announcementRouter被正确引用
3. 验证路由层级结构正确

#### 4.2 功能验证

- 列表查询和操作功能
- 新建和编辑功能
- 审核流程功能
- 发布和撤销功能
- 路由导航功能

## 验收标准

### 功能完整性

- [ ] 公告列表查询和筛选功能正常
- [ ] 公告新建和编辑功能正常
- [ ] 公告审核流程功能正常
- [ ] 公告发布和撤销功能正常
- [ ] 公告详情查看功能正常

### 技术规范

- [ ] 符合分层架构原则
- [ ] 路由配置符合规范
- [ ] 组件使用符合约束
- [ ] 代码质量符合标准

### 用户体验

- [ ] 界面布局合理美观
- [ ] 交互流程顺畅
- [ ] 错误提示友好
- [ ] 响应速度满足要求

## 模板资源

### 代码模板

- API层模板: `docs/prompts/frontend/templates/code-templates/api-layer.js`
- Adapters层模板: `docs/prompts/frontend/templates/code-templates/adapters-layer.js`
- Store层模板: `docs/prompts/frontend/templates/code-templates/store-layer.js`

### 页面模板

- 列表页模板: `docs/prompts/frontend/templates/page-templates/list-page.vue`
- 详情页模板: `docs/prompts/frontend/templates/page-templates/detail-page.vue`

### 配置规范

- 路由配置规范: `docs/prompts/frontend/core/router-standards.md`
- 架构标准: `docs/prompts/frontend/core/architecture-standards.md`
- 开发流程: `docs/prompts/frontend/core/development-workflow.md`

## 业务规则

### 状态流转规则

```
待审核 → 审核中 → 审核通过 → 已发布
   ↓        ↓        ↓
 删除    撤销     撤销
```

### 操作权限矩阵

| 状态     | 编辑 | 删除 | 提交 | 审核 | 撤销 | 发布 |
| -------- | ---- | ---- | ---- | ---- | ---- | ---- |
| 待审核   | ✓    | ✓    | ✓    | -    | -    | -    |
| 审核中   | -    | -    | -    | ✓    | ✓    | -    |
| 审核通过 | -    | -    | -    | -    | ✓    | ✓    |
| 已发布   | -    | -    | -    | -    | -    | -    |

### 字段验证规则

- **公告标题**: 必填，不超过255字符
- **选择标段**: 必选，自动带出项目信息
- **是否公示**: 根据采购金额自动判断
- **公告内容**: 必填，支持富文本
- **开标时间**: 必填，不能早于当前时间
- **澄清截止时间**: 必填，不能晚于开标时间

## 数据结构

### 公告实体结构

```javascript
{
  id: String,                    // 公告ID
  title: String,                 // 公告标题
  sectionId: String,             // 关联标段ID
  sectionName: String,           // 标段名称
  projectName: String,           // 项目名称
  procurementType: String,       // 采购类型
  procurementMethod: String,     // 采购方式
  isPublic: Boolean,             // 是否公示
  content: String,               // 公告内容
  startTime: Date,               // 公告开始时间
  clarificationDeadline: Date,   // 澄清截止时间
  openingTime: Date,             // 开标时间
  auditStatus: String,           // 审核状态
  publishStatus: String,         // 发布状态
  attachments: Array,            // 附件列表
  auditDocuments: Array,         // 审核依据
  remark: String,                // 备注
  createdBy: String,             // 创建人
  createdAt: Date,               // 创建时间
  updatedBy: String,             // 更新人
  updatedAt: Date                // 更新时间
}
```

## 注意事项

1. **严格按照分层架构顺序开发**，确保依赖关系正确
2. **使用模板驱动开发**，通过占位符替换确保代码规范
3. **每个阶段完成后进行验证**，确保功能正常
4. **遵循组件使用约束**，使用指定的UI组件库
5. **注意业务流程的状态管理**，正确处理审核和发布流程
6. **文件上传功能**，支持多文件上传和文件类型验证
7. **富文本编辑器**，确保内容格式正确保存和显示
8. **时间字段验证**，确保时间逻辑的合理性
9. **权限控制**，根据用户角色显示不同操作按钮
10. **数据联动**，标段选择后自动带出相关项目信息
