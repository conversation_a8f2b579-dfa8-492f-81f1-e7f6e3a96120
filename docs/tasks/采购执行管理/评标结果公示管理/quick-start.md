# 评标结果公示管理模块 - 快速开始指南

## 开发前准备

### 1. 环境检查

确保开发环境已正确配置：

```bash
# 检查项目根目录
cd /Users/<USER>/Work/Funi/paas/funi-paas-cs-web-cli

# 检查依赖安装
npm list vue
npm list pinia
```

### 2. 目录结构创建

创建模块目录结构：

```bash
mkdir -p src/apps/bidding-procurement/modules/procurement-execution/bid-result-announcement/{api,adapters,views/{list,detail,create,audit},__mocks__}
```

### 3. 核心规范文档阅读

**必读文档**（开发前必须阅读）：

- `docs/prompts/frontend/core/architecture-standards.md` - 分层架构规范
- `docs/prompts/frontend/core/development-workflow.md` - 开发流程规范
- `docs/prompts/frontend/core/router-standards.md` - 路由配置标准

## 开发执行步骤

### 第一步：基础设施开发（预计5.5小时）

#### 1.1 API层开发（1.5小时）

**创建文件**: `src/apps/bidding-procurement/modules/procurement-execution/bid-result-announcement/api/index.js`

**参考模板**: `docs/prompts/frontend/templates/code-templates/api-layer.js`

**核心接口**:
```javascript
// 必须实现的9个接口
- getBidResultAnnouncementList(params)
- getBidResultAnnouncementDetail(id)
- createBidResultAnnouncement(data)
- updateBidResultAnnouncement(id, data)
- deleteBidResultAnnouncement(id)
- submitBidResultAnnouncement(id)
- auditBidResultAnnouncement(id, data)
- publishBidResultAnnouncement(id)
- revokeBidResultAnnouncement(id)
```

#### 1.2 Adapters层开发（1小时）

**创建文件**: `src/apps/bidding-procurement/modules/procurement-execution/bid-result-announcement/adapters/index.js`

**参考模板**: `docs/prompts/frontend/templates/code-templates/adapters-layer.js`

**核心转换函数**:
```javascript
// 必须实现的5个转换函数
- adaptBidResultAnnouncementList
- adaptBidResultAnnouncementDetail
- adaptBidResultAnnouncementForm
- adaptCandidateInfo
- adaptAuditData
```

#### 1.3 Store层开发（2小时）

**创建文件**: `src/apps/bidding-procurement/modules/procurement-execution/bid-result-announcement/store.js`

**参考模板**: `docs/prompts/frontend/templates/code-templates/store-layer.js`

**状态管理要点**:
- 使用Pinia进行状态管理
- 实现列表、详情、表单、候选人信息等状态
- 集成工作流状态管理

#### 1.4 Mock数据开发（1小时）

**创建文件**: `src/apps/bidding-procurement/modules/procurement-execution/bid-result-announcement/__mocks__/index.js`

**参考规范**: `docs/funi-ui/schemas/data-structures.md`

### 第二步：路由配置（预计0.5小时）

#### 2.1 模块路由配置

**创建文件**: `src/apps/bidding-procurement/modules/procurement-execution/bid-result-announcement/router.js`

**参考规范**: `docs/prompts/frontend/core/router-standards.md`

**路由结构**:
```javascript
// 5个核心路由
- list (列表页)
- detail/:id (详情页)
- create (新建页)
- edit/:id (编辑页)
- audit/:id (审核页)
```

### 第三步：页面开发（预计12小时）

#### 3.1 列表页开发（3小时）

**创建文件**: `src/apps/bidding-procurement/modules/procurement-execution/bid-result-announcement/views/list/index.vue`

**参考原型**: `docs/design/prototypes/评标结果公示管理-列表页.html`

**核心功能**:
- 状态页签（全部、待审核、审核中、已发布）
- 搜索筛选功能
- FuniCurdV2表格组件集成
- 批量操作功能

#### 3.2 详情页开发（2.5小时）

**创建文件**: `src/apps/bidding-procurement/modules/procurement-execution/bid-result-announcement/views/detail/index.vue`

**参考原型**: `docs/design/prototypes/评标结果公示管理-详情页.html`

**核心功能**:
- 三个页签（公示详情、项目信息、操作记录）
- 只读信息展示
- 操作按钮集成

#### 3.3 新建/编辑页开发（4小时）

**创建文件**: `src/apps/bidding-procurement/modules/procurement-execution/bid-result-announcement/views/create/index.vue`

**参考原型**: `docs/design/prototypes/评标结果公示管理-新建编辑页.html`

**核心功能**:
- 标段选择功能
- 候选人信息管理
- 富文本编辑器集成
- 文件上传功能
- 表单验证

#### 3.4 审核页开发（2.5小时）

**创建文件**: `src/apps/bidding-procurement/modules/procurement-execution/bid-result-announcement/views/audit/index.vue`

**参考原型**: `docs/design/prototypes/评标结果公示管理-审核页.html`

**核心功能**:
- 审核信息展示（只读）
- 审核意见输入
- 审核操作按钮
- 审核历史记录

### 第四步：模块集成验证（预计3.5小时）

#### 4.1 路由集成（0.5小时）

**更新文件**: `src/apps/bidding-procurement/modules/procurement-execution/router.js`

**操作步骤**:
1. 导入评标结果公示管理路由
2. 取消注释第14行的bidResultRouter引用
3. 验证路由层级结构

#### 4.2 功能验证（2小时）

**验证清单**:
- [ ] 所有页面可正常访问
- [ ] 列表查询功能正常
- [ ] 新建编辑功能正常
- [ ] 审核流程功能正常
- [ ] 发布撤销功能正常

#### 4.3 质量检查（1小时）

**检查项目**:
- [ ] 代码符合分层架构规范
- [ ] 组件使用符合约束
- [ ] 路由配置符合标准
- [ ] 用户体验满足要求

## 开发注意事项

### 技术约束

1. **严格按顺序开发**: 必须按照API→Adapters→Store→Views→集成的顺序
2. **禁止跨层调用**: Views层只能调用Store层，不能直接调用API层
3. **组件使用规范**: 必须使用指定的FuniUI组件

### 业务要点

1. **候选人信息管理**: 注意候选人排序和成交金额的处理
2. **审核流程**: 确保审核状态流转正确
3. **权限控制**: 不同角色的操作权限要明确
4. **数据关联**: 与项目标段的数据关联要准确

### 常见问题

**Q: 如何处理候选人信息的动态添加？**
A: 使用动态表单组件，参考新建编辑页原型中的候选人信息表格。

**Q: 审核流程如何集成？**
A: 使用FuniWorkflow组件，参考Store层模板中的工作流状态管理。

**Q: 富文本编辑器如何配置？**
A: 使用内置富文本编辑器，参考组件使用规范。

## 验收自检清单

### 功能完整性
- [ ] 列表页：查询、筛选、分页、批量操作
- [ ] 详情页：三个页签完整展示
- [ ] 新建页：表单验证、文件上传、候选人管理
- [ ] 编辑页：数据回填、修改保存
- [ ] 审核页：审核操作、历史记录

### 技术规范
- [ ] API层：9个接口完整实现
- [ ] Adapters层：5个转换函数正确
- [ ] Store层：状态管理完整
- [ ] 路由配置：5个路由正确配置
- [ ] 组件使用：符合FuniUI规范

### 用户体验
- [ ] 界面美观，布局合理
- [ ] 交互流畅，响应及时
- [ ] 错误提示友好明确
- [ ] 移动端适配良好

## 完成后续步骤

1. **提交代码审查**: 确保代码质量符合团队标准
2. **集成测试**: 与其他模块进行集成测试
3. **用户验收**: 邀请业务人员进行功能验收
4. **文档更新**: 更新相关技术文档和用户手册

## 技术支持

如遇到技术问题，可参考：
- **架构问题**: `docs/prompts/frontend/core/architecture-standards.md`
- **组件问题**: `docs/funi-ui/`相关文档
- **业务问题**: `docs/PRD.md`和原型文件
- **模板参考**: 已完成的公告管理模块
