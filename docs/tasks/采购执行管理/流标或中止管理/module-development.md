# 流标或中止管理模块开发任务

## 任务概述

### 模块目标

开发流标或中止管理模块的完整功能，实现流标或中止公告的全生命周期管理，包括新建、编辑、审核、发布、撤销等核心业务流程。

### 开发范围

- **API层**: 流标或中止公告数据的HTTP请求封装
- **Adapters层**: 流标或中止公告数据的格式转换和适配
- **Store层**: 流标或中止公告状态管理和业务逻辑
- **路由配置**: 流标或中止管理模块的路由组织
- **页面开发**: 列表页、详情页、新建/编辑页、审核页

### 输出文件清单

```
src/apps/bidding-procurement/modules/procurement-execution/bid-failure-management/
├── api/index.js              # API层
├── adapters/index.js         # 数据转换层
├── views/                    # 视图层
│   ├── list/index.vue        # 列表页
│   ├── detail/index.vue      # 详情页
│   ├── create/index.vue      # 新建/编辑页
│   └── audit/index.vue       # 审核页
├── __mocks__/index.js        # Mock数据
├── store.js                  # 状态管理
└── router.js                 # 路由配置
```

## 技术规范

### 分层架构约束

- **开发顺序**: API → Adapters → Store → Views → 路由集成
- **调用关系**: API层 → Adapters层 → Store层 → Views层
- **禁止**: 跨层调用、反向调用

### 组件使用规范

- **表格组件**: 使用FuniCurdV2组件实现列表功能
- **表单组件**: 使用FuniForm表单组件
- **富文本编辑**: 使用内置富文本编辑器
- **文件上传**: 使用FuniUpload组件

## 全局参数配置

```javascript
const moduleParams = {
  MODULE_NAME: '流标或中止管理',
  ENTITY_NAME: '流标或中止公告',
  MODULE_PATH: 'src/apps/bidding-procurement/modules/procurement-execution/bid-failure-management',
  API_ENDPOINT: '/api/bid-failure-announcements',
  STORE_NAME: 'useBidFailureAnnouncementStore',
  PRIMARY_KEY: 'id',
  DISPLAY_FIELD: 'title',
  API_METHODS: 'getList, getDetail, create, update, delete, submit, audit, publish, revoke',
  TAB_CONFIG: '全部, 待审核, 审核中, 已发布',
  SEARCH_FIELDS: '公告标题、审核状态、发布状态、项目名称、流标/中止类型',
  OPERATION_BUTTONS: '编辑、删除、提交、审核、发布、撤销',
  CREATE_FEATURES: '标段选择、富文本编辑、文件上传、表单验证、流标/中止类型选择',
  DETAIL_TABS: '公告信息页签、项目信息页签、操作记录页签',
  BUSINESS_RULES: '草稿→待审核→审核中→审核通过→已发布',
  ENTITY_STRUCTURE: '{ id, title, content, type, isPublic, status, attachments, auditBasis, publishTime, ... }'
};
```

## 开发流程

### 第一阶段：基础设施准备

#### 1.1 API层开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/bid-failure-management/api/index.js`

**模板**: `docs/prompts/frontend/templates/code-templates/api-layer.js`

**接口清单**:

- `getBidFailureAnnouncementList(params)` - 获取流标或中止公告列表
- `getBidFailureAnnouncementDetail(id)` - 获取流标或中止公告详情
- `createBidFailureAnnouncement(data)` - 创建流标或中止公告
- `updateBidFailureAnnouncement(id, data)` - 更新流标或中止公告
- `deleteBidFailureAnnouncement(id)` - 删除流标或中止公告
- `submitBidFailureAnnouncement(id)` - 提交审核
- `auditBidFailureAnnouncement(id, data)` - 审核流标或中止公告
- `publishBidFailureAnnouncement(id)` - 发布流标或中止公告
- `revokeBidFailureAnnouncement(id)` - 撤销流标或中止公告

#### 1.2 Adapters层开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/bid-failure-management/adapters/index.js`

**模板**: `docs/prompts/frontend/templates/code-templates/adapters-layer.js`

**转换函数**:

- `adaptBidFailureAnnouncementList(data)` - 列表数据适配
- `adaptBidFailureAnnouncementDetail(data)` - 详情数据适配
- `adaptBidFailureAnnouncementForm(data)` - 表单数据适配
- `adaptProjectSectionOptions(data)` - 项目标段选项适配

#### 1.3 Store层开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/bid-failure-management/store.js`

**模板**: `docs/prompts/frontend/templates/code-templates/store-layer.js`

**状态管理**:

- 流标或中止公告列表状态
- 当前流标或中止公告详情
- 表单数据状态
- 加载状态管理

#### 1.4 Mock数据开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/bid-failure-management/__mocks__/index.js`

**模板**: `docs/prompts/frontend/templates/code-templates/__mock__.js`

**Mock数据**:

- 流标或中止公告列表数据
- 流标或中止公告详情数据
- 项目标段选项数据
- 流标/中止类型选项数据

### 第二阶段：路由配置

#### 2.1 模块路由配置

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/bid-failure-management/router.js`

**规范**: `docs/prompts/frontend/core/router-standards.md`

**路由结构**:

```javascript
{
  path: 'bid-failure-management',
  name: 'BidFailureManagement',
  meta: { title: '流标或中止管理', isMenu: true },
  children: [
    {
      path: 'list',
      name: 'BidFailureManagementList',
      component: () => import('./views/list/index.vue'),
      meta: { title: '流标或中止公告列表' }
    },
    {
      path: 'detail/:id',
      name: 'BidFailureManagementDetail',
      component: () => import('./views/detail/index.vue'),
      meta: { title: '流标或中止公告详情' }
    },
    {
      path: 'audit/:id',
      name: 'BidFailureManagementAudit',
      component: () => import('./views/audit/index.vue'),
      meta: { title: '审核流标或中止公告' }
    },
    {
      path: 'create',
      name: 'BidFailureManagementCreate',
      component: () => import('./views/create/index.vue'),
      meta: { title: '新建流标或中止公告' }
    },
    {
      path: 'edit/:id',
      name: 'BidFailureManagementEdit',
      component: () => import('./views/create/index.vue'),
      meta: { title: '编辑流标或中止公告' }
    }
  ]
}
```

### 第三阶段：页面开发

#### 3.1 列表页开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/bid-failure-management/views/list/index.vue`

**模板**: `docs/prompts/frontend/templates/page-templates/list-page.vue`

**列表功能**:

- 标签页切换：全部、待审核、审核中、已发布
- 搜索功能：公告标题、审核状态、发布状态、项目名称、流标/中止类型
- 表格展示：流标/中止标题、项目编号、采购方式、流标/中止类型、审核状态、发布状态、创建人、创建时间
- 操作按钮：编辑、删除、提交、审核、发布、撤销（根据状态动态显示）
- 批量操作：批量审核、批量删除

#### 3.2 详情页开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/bid-failure-management/views/detail/index.vue`

**模板**: `docs/prompts/frontend/templates/page-templates/detail-page.vue`

**详情功能**:

- **公告信息页签**: 公告标题、公告阶段、公告状态、是否公示、流标或中止详情、其他附件、审核依据
- **项目信息页签**: 项目名称、项目编号、标段名称、预算金额、项目业主等（从标段自动带出）
- **操作记录页签**: 创建、提交、审核、发布等操作的历史记录

#### 3.3 新建/编辑页开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/bid-failure-management/views/create/index.vue`

**模板**: `docs/prompts/frontend/templates/page-templates/add-page.vue`

**新建/编辑功能**:

- 标段选择：关联项目标段信息（自动带出15个字段）
- 基本信息：公告标题
- 流标或中止信息：是否公示（根据采购金额自动判断）、流标或中止详情（富文本编辑）
- 文件上传：其他附件、审核依据
- 表单验证：完整的数据验证规则

#### 3.4 审核页开发

**文件**: `src/apps/bidding-procurement/modules/procurement-execution/bid-failure-management/views/audit/index.vue`

**模板**: `docs/prompts/frontend/templates/page-templates/detail-page.vue`

**审核功能**:

- 流标或中止公告信息展示（只读）
- 审核意见输入
- 审核操作（通过、驳回）
- 审核历史记录

### 第四阶段：模块集成验证

#### 4.1 路由集成

将流标或中止管理模块路由集成到采购执行管理模块中：

**更新文件**: `src/apps/bidding-procurement/modules/procurement-execution/router.js`

**集成步骤**:

1. 导入流标或中止管理路由：`import bidFailureRouter from './bid-failure-management/router.js';`
2. 取消第18行注释，确保bidFailureRouter被正确引用
3. 验证路由层级结构正确

#### 4.2 功能验证

**验证清单**:

- [ ] API接口调用正常
- [ ] 数据适配转换正确
- [ ] Store状态管理有效
- [ ] 列表页功能完整
- [ ] 详情页信息展示正确
- [ ] 新建/编辑页表单验证有效
- [ ] 审核页操作流程正常
- [ ] 路由跳转正确
- [ ] 权限控制有效

## 验收标准

### 功能验收

1. **列表管理**: 支持分页查询、条件筛选、状态切换
2. **新建编辑**: 支持标段选择、富文本编辑、文件上传
3. **审核流程**: 支持提交审核、审核通过/驳回、状态流转
4. **发布管理**: 支持公告发布、撤销操作
5. **数据级联**: 正确继承项目标段的15个字段

### 技术验收

1. **架构规范**: 严格遵循分层架构，无跨层调用
2. **代码质量**: 使用标准模板，占位符替换完整
3. **组件使用**: 正确使用FuniUI组件库
4. **路由配置**: 符合router-standards.md规范
5. **错误处理**: 完善的异常处理和用户提示

### 业务验收

1. **工作流程**: 草稿→待审核→审核中→审核通过→已发布
2. **权限控制**: 不同角色的操作权限正确
3. **数据完整性**: 流标或中止信息完整准确
4. **用户体验**: 界面友好，操作流畅

## 模板资源引用

- **API层模板**: `docs/prompts/frontend/templates/code-templates/api-layer.js`
- **Adapters层模板**: `docs/prompts/frontend/templates/code-templates/adapters-layer.js`
- **Store层模板**: `docs/prompts/frontend/templates/code-templates/store-layer.js`
- **Mock数据模板**: `docs/prompts/frontend/templates/code-templates/__mock__.js`
- **列表页模板**: `docs/prompts/frontend/templates/page-templates/list-page.vue`
- **详情页模板**: `docs/prompts/frontend/templates/page-templates/detail-page.vue`
- **新建/编辑页模板**: `docs/prompts/frontend/templates/page-templates/add-page.vue`
- **路由配置规范**: `docs/prompts/frontend/core/router-standards.md`
