# T002 - 路由配置

## 任务概述

**目标**: 基于router-standards.md规范配置采购计划管理模块的路由 **模块名称**: 采购计划管理 **规范文档**: `docs/prompts/frontend/core/router-standards.md` **输出文件**: `src/apps/bidding-procurement/modules/procurement-plan/router.js`

## 技术规范

### 路由配置规范

- **路径格式**: 使用kebab-case格式
- **组件路径**: 必须使用`@/apps/`前缀的绝对路径
- **懒加载**: 所有组件使用动态导入
- **元数据**: 必须包含title和isMenu配置

### 路由结构要求

- **父级路由**: 作为容器，不配置component
- **子路由**: 配置具体的页面组件
- **权限控制**: 不在路由配置中添加权限字段

## 路由配置规范

### 基础路由结构

```javascript
{
  path: '/procurement-plan',
  name: 'ProcurementPlan',
  meta: {
    title: '采购计划管理',
    isMenu: true
  },
  children: [
    // 子路由配置
  ]
}
```

### 字段说明

| 字段          | 类型     | 必需 | 说明                     |
| ------------- | -------- | ---- | ------------------------ |
| `path`        | String   | ✅   | 路由路径，必须以`/`开头  |
| `name`        | String   | ✅   | 路由名称，使用PascalCase |
| `component`   | Function | ❌   | 路由组件（懒加载函数）   |
| `meta`        | Object   | ✅   | 路由元数据               |
| `meta.title`  | String   | ✅   | 显示标题                 |
| `meta.isMenu` | Boolean  | ❌   | 是否显示在菜单中         |
| `children`    | Array    | ❌   | 子路由配置               |

## 实现步骤

### 步骤1: 创建路由配置文件

**输出文件**: `src/apps/bidding-procurement/modules/procurement-plan/router.js`

**路由配置内容**:

```javascript
export default {
  path: '/procurement-plan',
  name: 'ProcurementPlan',
  meta: {
    title: '采购计划管理',
    isMenu: true
  },
  children: [
    {
      path: '',
      name: 'ProcurementPlanList',
      component: () => import('./views/list/index.vue'),
      meta: {
        title: '采购计划列表',
        isMenu: false
      }
    },
    {
      path: 'detail/:id',
      name: 'ProcurementPlanDetail',
      component: () => import('./views/detail/index.vue'),
      meta: {
        title: '采购计划详情',
        isMenu: false
      }
    },
    {
      path: 'create',
      name: 'ProcurementPlanCreate',
      component: () => import('./views/create/index.vue'),
      meta: {
        title: '新建采购计划',
        isMenu: false
      }
    }
  ]
};
```

### 步骤2: 验证路由配置

**验证项目**:

1. 路由路径格式正确（kebab-case）
2. 路由名称格式正确（PascalCase）
3. 组件路径使用绝对路径
4. 元数据配置完整
5. 子路由使用相对路径

### 步骤3: 集成到系统路由

**注意**: 此步骤需要在系统级路由中引入模块路由，但不在本任务范围内

## 验收标准

### 路由配置检查

- [ ] 路由文件创建成功
- [ ] 路由路径格式符合kebab-case规范
- [ ] 路由名称格式符合PascalCase规范
- [ ] 组件路径使用@/apps/前缀的绝对路径
- [ ] 所有路由都配置了meta.title
- [ ] 父级路由配置了meta.isMenu: true

### 路由结构检查

- [ ] 父级路由不配置component
- [ ] 子路由都配置了component
- [ ] 子路由路径使用相对路径
- [ ] 动态路由参数配置正确（如:id）

### 规范符合性检查

- [ ] 符合router-standards.md规范要求
- [ ] 没有添加权限相关字段
- [ ] 路由导出格式正确（export default）

## 依赖关系

### 前置依赖

- T001 - 基础设施准备（需要Store层完成）

### 后续依赖

- T003 - 页面开发（需要路由配置完成）

## 注意事项

1. **严格遵循router-standards.md规范**，不可随意修改
2. **组件路径必须使用绝对路径**，避免相对路径导致的问题
3. **不要在路由配置中添加权限字段**，权限控制通过其他方式实现
4. **确保所有子路由的component引用的文件在后续任务中会被创建**
5. **路由名称必须唯一**，避免与其他模块冲突

## 路由命名规范

### 路由路径命名

- 使用kebab-case格式
- 体现业务含义
- 保持简洁明了

### 路由名称命名

- 使用PascalCase格式
- 以模块名开头
- 体现页面功能

### 示例

```javascript
// 正确示例
path: '/procurement-plan';
name: 'ProcurementPlan';

// 错误示例
path: '/procurementPlan'; // 应该使用kebab-case
name: 'procurement-plan'; // 应该使用PascalCase
```
