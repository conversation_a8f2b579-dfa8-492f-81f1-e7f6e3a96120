# T003 - 页面开发

## 任务概述

**目标**: 基于页面模板开发采购计划管理的列表页和详情页
**模块名称**: 采购计划管理
**页面类型**: 列表页 + 工作流页面
**输出文件**: 
- `src/apps/bidding-procurement/modules/procurement-plan/views/list/index.vue`
- `src/apps/bidding-procurement/modules/procurement-plan/views/detail/index.vue`
- `src/apps/bidding-procurement/modules/procurement-plan/views/create/index.vue`

## 技术规范

### 页面开发约束
- **模板驱动**: 严格使用预设页面模板
- **组件使用**: 基于FuniUI组件库
- **状态管理**: 调用Store层进行数据管理
- **路由集成**: 与T002配置的路由保持一致

### 业务特征
- **工作流支持**: 支持待办/已办页签切换
- **审批流程**: 集成审批操作按钮
- **文件上传**: 支持附件上传功能
- **数据导入导出**: 支持批量操作

## 模板参数

### 占位符映射表
```javascript
{
  "{{MODULE_NAME}}": "采购计划管理",
  "{{ENTITY_NAME}}": "采购计划",
  "{{ENTITY_NAME_EN}}": "procurementPlan",
  "{{STORE_NAME}}": "useProcurementPlanStore",
  "{{API_ENDPOINT}}": "/api/procurement-plans",
  "{{PRIMARY_KEY}}": "id",
  "{{DISPLAY_FIELD}}": "planName",
  "{{CREATE_ROUTE}}": "/procurement-plan/create",
  "{{DETAIL_ROUTE}}": "/procurement-plan/detail",
  "{{LIST_ROUTE}}": "/procurement-plan"
}
```

## 实现步骤

### 步骤1: 创建列表页
**模板文件**: `docs/prompts/frontend/templates/page-templates/list-page.vue`
**输出文件**: `src/apps/bidding-procurement/modules/procurement-plan/views/list/index.vue`

**特殊配置**:
1. **多页签配置**: 待办/已办页签
2. **表格列配置**: 采购计划特有字段
3. **操作按钮**: 审批相关操作
4. **批量操作**: 导入导出功能

**页签配置示例**:
```javascript
const cardTabConfig = [
  {
    key: 'pending',
    label: '待办',
    api: '/api/procurement-plans/pending',
    // 待办特有配置
  },
  {
    key: 'completed',
    label: '已办',
    api: '/api/procurement-plans/completed',
    // 已办特有配置
  }
];
```

### 步骤2: 创建详情页
**模板文件**: `docs/prompts/frontend/templates/page-templates/detail-page.vue`
**输出文件**: `src/apps/bidding-procurement/modules/procurement-plan/views/detail/index.vue`

**特殊配置**:
1. **表单字段**: 采购计划相关字段
2. **文件上传**: 支持附件上传
3. **审批流程**: 集成审批组件
4. **数据级联**: 支持关联数据显示

### 步骤3: 创建新建页
**模板文件**: `docs/prompts/frontend/templates/page-templates/detail-page.vue`
**输出文件**: `src/apps/bidding-procurement/modules/procurement-plan/views/create/index.vue`

**特殊配置**:
1. **新建模式**: 配置为create模式
2. **表单验证**: 必填字段验证
3. **数据提交**: 集成提交流程

### 步骤4: 验证页面功能
**验证项目**:
1. 页面正常渲染
2. 路由跳转正常
3. Store调用正常
4. 组件交互正常

## 页面字段配置

### 列表页表格列
```javascript
columns: [
  {
    prop: 'planCode',
    label: '计划编号',
    fixed: 'left',
    width: 150
  },
  {
    prop: 'planName',
    label: '计划名称',
    width: 200
  },
  {
    prop: 'purchaseType',
    label: '采购类型',
    width: 120
  },
  {
    prop: 'bidAmount',
    label: '预算金额(万元)',
    width: 150
  },
  {
    prop: 'status',
    label: '状态',
    width: 100
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: 180
  }
]
```

### 详情页表单字段
```javascript
formSchema: [
  {
    prop: 'planName',
    label: '计划名称',
    type: 'input',
    required: true
  },
  {
    prop: 'purchaseType',
    label: '采购类型',
    type: 'select',
    options: [
      { label: '货物', value: '货物' },
      { label: '工程', value: '工程' },
      { label: '服务', value: '服务' }
    ]
  },
  {
    prop: 'bidAmount',
    label: '预算金额',
    type: 'number',
    required: true
  }
  // 更多字段...
]
```

## 验收标准

### 页面创建检查
- [ ] 列表页文件创建成功
- [ ] 详情页文件创建成功
- [ ] 新建页文件创建成功
- [ ] 所有页面语法正确

### 功能完整性检查
- [ ] 列表页支持待办/已办页签切换
- [ ] 列表页表格显示正常
- [ ] 列表页操作按钮功能正常
- [ ] 详情页表单显示正常
- [ ] 详情页支持查看/编辑模式切换
- [ ] 新建页表单验证正常

### 路由集成检查
- [ ] 页面路由跳转正常
- [ ] 路由参数传递正确
- [ ] 面包屑导航正常

### Store集成检查
- [ ] 页面正确调用Store方法
- [ ] 数据加载正常
- [ ] 状态更新正常

## 依赖关系

### 前置依赖
- T001 - 基础设施准备（需要API、Adapters、Store层）
- T002 - 路由配置（需要路由配置完成）

### 后续依赖
- T004 - 系统集成验证（需要页面开发完成）

## 注意事项

1. **严格使用页面模板**，不可随意修改模板结构
2. **确保Store层方法调用正确**，避免方法名错误
3. **验证路由配置一致性**，确保路由路径匹配
4. **注意工作流页面特殊需求**，支持审批流程
5. **使用FuniUI组件**，保持界面一致性
6. **遵循Airbnb JavaScript规范**进行代码编写

## 工作流特殊配置

### 待办/已办页签
- 待办页签显示需要处理的采购计划
- 已办页签显示已处理的采购计划
- 不同页签有不同的操作按钮

### 审批操作按钮
- 提交审批
- 审批通过
- 审批驳回
- 撤回申请

### 文件上传功能
- 支持多文件上传
- 文件类型限制
- 文件大小限制
- 上传进度显示
