# 模块开发任务示例 - 采购计划管理

## 任务概述

**目标**: 完成采购计划管理模块的全栈开发
**模块类型**: 叶子模块
**开发范围**: API层、Store层、路由配置、页面开发
**预计时间**: 2-3小时

### 输出文件清单

```
src/apps/bidding-procurement/modules/procurement-plan/
├── api/index.js                    # API层
├── adapters/index.js               # 数据适配层
├── store.js                        # 状态管理
├── router.js                       # 路由配置
└── views/
    ├── list/index.vue              # 列表页
    └── detail/index.vue            # 详情页
```

## 技术规范

### 核心架构约束
- **分层调用**: Store → API/Adapters，禁止跨层调用
- **组件规范**: 使用FuniUI组件库，HTTP请求使用window.$http
- **状态管理**: 基于Pinia的Store模式

### 全局参数配置

```javascript
{
  "{{MODULE_NAME}}": "采购计划管理",
  "{{ENTITY_NAME}}": "采购计划",
  "{{ENTITY_NAME_EN}}": "procurementPlan",
  "{{API_ENDPOINT}}": "/api/procurement-plans",
  "{{STORE_NAME}}": "useProcurementPlanStore",
  "{{PRIMARY_KEY}}": "id",
  "{{DISPLAY_FIELD}}": "planName"
}
```

## 集成开发流程

### 第一步：基础设施准备

**1.1 创建API层**
- 模板：`docs/prompts/frontend/templates/code-templates/api-layer.js`
- 输出：`src/apps/bidding-procurement/modules/procurement-plan/api/index.js`

**1.2 创建Adapters层**
- 模板：`docs/prompts/frontend/templates/code-templates/adapters-layer.js`
- 输出：`src/apps/bidding-procurement/modules/procurement-plan/adapters/index.js`

**1.3 创建Store层**
- 模板：`docs/prompts/frontend/templates/code-templates/store-layer.js`
- 输出：`src/apps/bidding-procurement/modules/procurement-plan/store.js`

### 第二步：路由配置

**2.1 配置模块路由**
- 规范：`docs/prompts/frontend/core/router-standards.md`
- 输出：`src/apps/bidding-procurement/modules/procurement-plan/router.js`

### 第三步：页面开发

**3.1 创建列表页**
- 模板：`docs/prompts/frontend/templates/page-templates/list-page.vue`
- 输出：`src/apps/bidding-procurement/modules/procurement-plan/views/list/index.vue`

**3.2 创建详情页**
- 模板：`docs/prompts/frontend/templates/page-templates/detail-page.vue`
- 输出：`src/apps/bidding-procurement/modules/procurement-plan/views/detail/index.vue`

### 第四步：模块集成验证

**4.1 功能验证**
- 验证所有文件创建成功
- 检查分层调用关系正确
- 确认路由配置符合规范

**4.2 集成测试**
- 验证页面正常渲染
- 检查API调用正常
- 确认状态管理工作正常

## 验收标准

### 完成检查点

- [ ] 所有文件按照模板成功创建
- [ ] 占位符参数正确替换
- [ ] 分层架构调用关系正确
- [ ] 路由配置符合规范
- [ ] 页面功能正常运行
- [ ] 无语法错误和导入错误

### 质量要求

- 代码符合Airbnb JavaScript规范
- 组件使用符合FuniUI规范
- 路由配置遵循router-standards.md
- Store状态管理符合Pinia模式

## 模板资源清单

### 代码模板
- `docs/prompts/frontend/templates/code-templates/api-layer.js`
- `docs/prompts/frontend/templates/code-templates/adapters-layer.js`
- `docs/prompts/frontend/templates/code-templates/store-layer.js`

### 页面模板
- `docs/prompts/frontend/templates/page-templates/list-page.vue`
- `docs/prompts/frontend/templates/page-templates/detail-page.vue`

### 配置规范
- `docs/prompts/frontend/core/router-standards.md`
