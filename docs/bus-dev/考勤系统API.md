# 考勤系统API接口规范文档

## 文档信息
- **项目名称**：智能考勤管理系统
- **文档类型**：API接口规范文档
- **版本号**：v1.0
- **创建日期**：2024-01-31
- **文档状态**：初稿
- **基础文档**：考勤系统业务规格设计文档 v1.0

## 1. API接口清单

### 1.1 模块接口总览

#### 考勤打卡模块接口
| 接口名称 | 请求方法 | 接口路径 | 功能描述 | 权限要求 |
|---------|---------|---------|---------|----------|
| 员工打卡 | POST | /api/attendance/record/checkin | 员工上下班打卡记录 | 普通员工 |
| 考勤记录列表 | GET | /api/attendance/record/list | 查询考勤记录列表 | 普通员工 |
| 考勤记录详情 | GET | /api/attendance/record/detail | 查询单条考勤记录详情 | 普通员工 |
| 考勤统计查询 | GET | /api/attendance/record/statistics | 查询考勤统计数据 | 普通员工 |

#### 请假管理模块接口
| 接口名称 | 请求方法 | 接口路径 | 功能描述 | 权限要求 |
|---------|---------|---------|---------|----------|
| 提交请假申请 | POST | /api/attendance/leave/add | 员工提交请假申请 | 普通员工 |
| 请假申请列表 | GET | /api/attendance/leave/list | 查询请假申请列表 | 普通员工 |
| 请假申请详情 | GET | /api/attendance/leave/detail | 查询请假申请详情 | 普通员工 |
| 审批请假申请 | POST | /api/attendance/leave/approve | 审批请假申请 | 部门主管/HR |
| 撤销请假申请 | POST | /api/attendance/leave/cancel | 撤销请假申请 | 普通员工 |
| 假期余额查询 | GET | /api/attendance/leave/balance | 查询员工假期余额 | 普通员工 |

#### 加班管理模块接口
| 接口名称 | 请求方法 | 接口路径 | 功能描述 | 权限要求 |
|---------|---------|---------|---------|----------|
| 提交加班申请 | POST | /api/attendance/overtime/add | 员工提交加班申请 | 普通员工 |
| 加班申请列表 | GET | /api/attendance/overtime/list | 查询加班申请列表 | 普通员工 |
| 加班申请详情 | GET | /api/attendance/overtime/detail | 查询加班申请详情 | 普通员工 |
| 审批加班申请 | POST | /api/attendance/overtime/approve | 审批加班申请 | 部门主管/HR |
| 撤销加班申请 | POST | /api/attendance/overtime/cancel | 撤销加班申请 | 普通员工 |

#### 用户管理模块接口
| 接口名称 | 请求方法 | 接口路径 | 功能描述 | 权限要求 |
|---------|---------|---------|---------|----------|
| 用户信息列表 | GET | /api/attendance/user/list | 查询用户信息列表 | HR管理员 |
| 用户信息详情 | GET | /api/attendance/user/detail | 查询用户详细信息 | HR管理员 |
| 新增用户 | POST | /api/attendance/user/add | 新增用户信息 | HR管理员 |
| 更新用户信息 | POST | /api/attendance/user/update | 更新用户信息 | HR管理员 |
| 删除用户 | POST | /api/attendance/user/delete | 删除用户信息 | HR管理员 |
| 获取当前用户信息 | GET | /api/attendance/user/current | 获取当前登录用户信息 | 普通员工 |

#### 统计分析模块接口
| 接口名称 | 请求方法 | 接口路径 | 功能描述 | 权限要求 |
|---------|---------|---------|---------|----------|
| 部门考勤统计 | GET | /api/attendance/statistics/department | 查询部门考勤统计 | 部门主管/HR |
| 出勤率分析 | GET | /api/attendance/statistics/attendance-rate | 查询出勤率分析数据 | 部门主管/HR |
| 异常数据统计 | GET | /api/attendance/statistics/exception | 查询异常考勤数据统计 | 部门主管/HR |
| 考勤报表导出 | GET | /api/attendance/statistics/export | 导出考勤报表 | HR管理员 |

#### 系统设置模块接口
| 接口名称 | 请求方法 | 接口路径 | 功能描述 | 权限要求 |
|---------|---------|---------|---------|----------|
| 考勤规则查询 | GET | /api/attendance/setting/rules | 查询考勤规则配置 | 系统管理员 |
| 考勤规则更新 | POST | /api/attendance/setting/rules/update | 更新考勤规则配置 | 系统管理员 |
| 排班计划列表 | GET | /api/attendance/setting/schedule/list | 查询排班计划列表 | HR管理员 |
| 排班计划详情 | GET | /api/attendance/setting/schedule/detail | 查询排班计划详情 | HR管理员 |
| 新增排班计划 | POST | /api/attendance/setting/schedule/add | 新增排班计划 | HR管理员 |
| 更新排班计划 | POST | /api/attendance/setting/schedule/update | 更新排班计划 | HR管理员 |

### 1.2 接口分类统计
- 查询类接口：16 个
- 变更类接口：12 个
- 业务操作接口：4 个
- 总计：32 个

### 1.3 接口调用依赖关系图

#### 日常打卡业务流程接口调用时序
```mermaid
sequenceDiagram
    participant U as 员工
    participant F as 前端应用
    participant A as 系统后端
    participant L as 位置服务
    
    U->>F: 1. 点击打卡按钮
    F->>L: 2. 获取GPS位置信息
    L-->>F: 返回位置坐标
    
    F->>A: 3. 调用打卡接口
    Note over A: POST /api/attendance/record/checkin
    A->>A: 验证位置范围
    A->>A: 判断打卡类型
    A->>A: 保存打卡记录
    A-->>F: 返回打卡结果
    
    F->>A: 4. 查询今日考勤统计
    Note over A: GET /api/attendance/record/statistics
    A-->>F: 返回统计数据
    F-->>U: 显示打卡成功和统计信息
```

**调用说明**：
1. 获取位置信息：前端调用设备GPS服务获取当前位置坐标
2. 员工打卡：调用打卡接口记录考勤数据，包含位置验证和类型判断
3. 统计查询：获取当日考勤统计数据用于页面展示

#### 请假审批业务流程接口调用时序
```mermaid
sequenceDiagram
    participant E as 申请员工
    participant F1 as 员工端
    participant A as 系统后端
    participant F2 as 管理端
    participant M as 审批主管
    participant N as 通知服务
    
    E->>F1: 1. 填写请假申请
    F1->>A: 2. 查询假期余额
    Note over A: GET /api/attendance/leave/balance
    A-->>F1: 返回余额信息
    
    F1->>A: 3. 提交请假申请
    Note over A: POST /api/attendance/leave/add
    A->>A: 验证假期余额
    A->>A: 创建申请记录
    A->>N: 发送审批通知
    A-->>F1: 返回申请结果
    
    N->>M: 4. 通知审批人
    M->>F2: 5. 查看待审批列表
    F2->>A: 6. 获取申请详情
    Note over A: GET /api/attendance/leave/detail
    A-->>F2: 返回申请详情
    
    M->>F2: 7. 审批决定
    F2->>A: 8. 提交审批结果
    Note over A: POST /api/attendance/leave/approve
    A->>A: 更新申请状态
    A->>A: 扣减假期余额
    A->>N: 发送结果通知
    A-->>F2: 返回审批结果
    N->>E: 9. 通知申请人
```

**调用说明**：
1. 假期余额查询：验证员工剩余假期是否充足
2. 提交请假申请：创建申请记录并触发审批流程
3. 申请详情查询：审批人查看申请的详细信息
4. 审批处理：更新申请状态并处理假期余额扣减

#### 考勤统计分析业务流程接口调用时序
```mermaid
sequenceDiagram
    participant M as 管理员
    participant F as 管理端
    participant A as 系统后端
    participant D as 数据库
    participant E as 导出服务
    
    M->>F: 1. 查看考勤统计
    F->>A: 2. 查询部门统计
    Note over A: GET /api/attendance/statistics/department
    A->>D: 聚合考勤数据
    D-->>A: 返回统计结果
    A-->>F: 返回部门统计
    
    F->>A: 3. 查询出勤率分析
    Note over A: GET /api/attendance/statistics/attendance-rate
    A->>D: 计算出勤率
    D-->>A: 返回分析数据
    A-->>F: 返回出勤率数据
    
    F->>A: 4. 查询异常数据
    Note over A: GET /api/attendance/statistics/exception
    A->>D: 筛选异常记录
    D-->>A: 返回异常数据
    A-->>F: 返回异常统计
    
    M->>F: 5. 导出报表
    F->>A: 6. 请求报表导出
    Note over A: GET /api/attendance/statistics/export
    A->>E: 生成Excel报表
    E-->>A: 返回文件地址
    A-->>F: 返回下载链接
    F-->>M: 提供文件下载
```

**调用说明**：
1. 部门统计查询：获取各部门的考勤汇总数据
2. 出勤率分析：计算和展示出勤率趋势数据
3. 异常数据查询：识别和统计考勤异常情况
4. 报表导出：生成Excel格式的考勤报表文件

## 2. 详细接口规范

### 2.1 考勤打卡模块接口

#### 员工打卡 - 数据变更

##### 基本信息
- **接口路径**：POST /api/attendance/record/checkin
- **功能描述**：员工进行上下班打卡，记录考勤数据
- **业务场景**：员工到达/离开工作地点时进行考勤记录
- **权限要求**：普通员工权限

##### 请求参数
```json
{
  "latitude": 39.908823,
  "longitude": 116.397470,
  "location_name": "北京市朝阳区建国门外大街",
  "device_info": "iPhone 13 Pro",
  "remark": "正常打卡"
}
```

| 参数名 | 类型 | 必填 | 说明 | 示例值 | 验证规则 |
|-------|------|------|------|--------|----------|
| latitude | Decimal | 是 | 纬度坐标 | 39.908823 | 有效GPS坐标 |
| longitude | Decimal | 是 | 经度坐标 | 116.397470 | 有效GPS坐标 |
| location_name | String | 是 | 打卡地点名称 | "北京市朝阳区建国门外大街" | 长度不超过100 |
| device_info | String | 否 | 设备信息 | "iPhone 13 Pro" | 长度不超过50 |
| remark | String | 否 | 备注说明 | "正常打卡" | 长度不超过200 |

##### 响应数据
```json
{
  "code": 200,
  "message": "打卡成功",
  "success": true,
  "data": {
    "id": "ATT_20240131_001",
    "check_time": "2024-01-31 09:00:00",
    "check_type": "上班打卡",
    "status": "正常",
    "work_hours": "8.5"
  }
}
```

##### 业务逻辑
1. **位置验证**：验证GPS坐标是否在允许打卡范围内（办公室100米范围）
2. **时间窗口检查**：验证打卡时间是否在有效时间窗口内（7:00-23:59）
3. **打卡类型判断**：根据当前时间和员工班次自动判断上班/下班打卡
4. **重复打卡处理**：同一时间段内只保留最后一次打卡记录

##### 事务处理
- **事务范围**：打卡记录创建、考勤统计更新
- **回滚条件**：位置验证失败、数据保存失败
- **补偿机制**：支持离线打卡，网络恢复后自动同步

##### 异常情况
| 错误码 | 错误信息 | 触发条件 | 处理建议 |
|-------|---------|---------|----------|
| 400 | 位置信息无效 | GPS坐标格式错误 | 检查位置权限 |
| 403 | 打卡位置超出范围 | 不在允许打卡区域 | 填写异常说明 |
| 409 | 重复打卡 | 短时间内多次打卡 | 忽略重复操作 |
| 422 | 打卡时间异常 | 超出有效时间窗口 | 填写异常原因 |

#### 考勤记录列表 - 数据查询

##### 基本信息
- **接口路径**：GET /api/attendance/record/list
- **功能描述**：查询员工考勤记录列表，支持分页和筛选
- **业务场景**：员工查看个人考勤历史，管理员查看员工考勤情况
- **权限要求**：普通员工（查看个人）、部门主管（查看下属）、HR管理员（查看全部）

##### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例值 | 验证规则 |
|-------|------|------|------|--------|----------|
| pageNum | Integer | 否 | 页码，默认1 | 1 | 大于0 |
| pageSize | Integer | 否 | 页大小，默认20 | 20 | 1-100 |
| userId | String | 否 | 用户ID筛选 | "USR_20240101_001" | 有效用户ID |
| startDate | String | 否 | 开始日期 | "2024-01-01" | 日期格式 |
| endDate | String | 否 | 结束日期 | "2024-01-31" | 日期格式 |
| checkType | String | 否 | 打卡类型筛选 | "上班打卡" | 枚举值 |
| status | String | 否 | 状态筛选 | "正常" | 枚举值 |

##### 响应数据
```json
{
  "code": 200,
  "message": "查询成功",
  "success": true,
  "data": {
    "list": [
      {
        "id": "ATT_20240131_001",
        "user_id": "USR_20240101_001",
        "user_name": "张三",
        "check_time": "2024-01-31 09:00:00",
        "check_type": "上班打卡",
        "location_name": "北京市朝阳区建国门外大街",
        "status": "正常",
        "remark": "正常打卡",
        "create_time": "2024-01-31 09:00:00"
      }
    ],
    "total": 100
  }
}
```

##### 业务逻辑
1. **权限过滤**：根据用户角色过滤可查看的数据范围
2. **时间范围处理**：默认查询最近30天的记录
3. **数据排序**：按打卡时间倒序排列
4. **状态统计**：返回正常、异常、迟到等状态统计

##### 异常情况
| 错误码 | 错误信息 | 触发条件 | 处理建议 |
|-------|---------|---------|----------|
| 400 | 参数验证失败 | 日期格式错误 | 检查参数格式 |
| 403 | 权限不足 | 无查看权限 | 联系管理员 |
| 500 | 系统错误 | 数据库查询失败 | 稍后重试 |

#### 考勤记录详情 - 数据查询

##### 基本信息
- **接口路径**：GET /api/attendance/record/detail
- **功能描述**：查询单条考勤记录的详细信息
- **业务场景**：查看考勤记录的完整详情，包括位置、设备等信息
- **权限要求**：普通员工（查看个人）、部门主管（查看下属）、HR管理员（查看全部）

##### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例值 | 验证规则 |
|-------|------|------|------|--------|----------|
| id | String | 是 | 考勤记录ID | "ATT_20240131_001" | 有效记录ID |

##### 响应数据
```json
{
  "code": 200,
  "message": "查询成功",
  "success": true,
  "data": {
    "id": "ATT_20240131_001",
    "user_id": "USR_20240101_001",
    "user_name": "张三",
    "department_name": "技术部",
    "check_time": "2024-01-31 09:00:00",
    "check_type": "上班打卡",
    "location_name": "北京市朝阳区建国门外大街",
    "latitude": 39.908823,
    "longitude": 116.397470,
    "status": "正常",
    "device_info": "iPhone 13 Pro",
    "remark": "正常打卡",
    "create_time": "2024-01-31 09:00:00"
  }
}
```

##### 业务逻辑
1. **权限验证**：验证用户是否有权限查看该记录
2. **关联数据查询**：查询用户和部门信息
3. **位置信息展示**：提供GPS坐标和地址信息
4. **状态说明**：提供详细的状态说明和异常原因

##### 异常情况
| 错误码 | 错误信息 | 触发条件 | 处理建议 |
|-------|---------|---------|----------|
| 400 | 记录ID无效 | ID格式错误 | 检查ID格式 |
| 403 | 权限不足 | 无查看权限 | 联系管理员 |
| 404 | 记录不存在 | 记录已删除或不存在 | 确认记录ID |

#### 考勤统计查询 - 数据查询

##### 基本信息
- **接口路径**：GET /api/attendance/record/statistics
- **功能描述**：查询员工考勤统计数据，包括出勤天数、迟到次数等
- **业务场景**：员工查看个人考勤统计，管理员查看团队统计
- **权限要求**：普通员工（查看个人）、部门主管（查看下属）、HR管理员（查看全部）

##### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例值 | 验证规则 |
|-------|------|------|------|--------|----------|
| userId | String | 否 | 用户ID | "USR_20240101_001" | 有效用户ID |
| startDate | String | 否 | 开始日期 | "2024-01-01" | 日期格式 |
| endDate | String | 否 | 结束日期 | "2024-01-31" | 日期格式 |
| statisticsType | String | 否 | 统计类型 | "monthly" | daily/weekly/monthly |

##### 响应数据
```json
{
  "code": 200,
  "message": "查询成功",
  "success": true,
  "data": {
    "user_id": "USR_20240101_001",
    "user_name": "张三",
    "period": "2024-01",
    "work_days": 22,
    "attendance_days": 20,
    "late_times": 2,
    "early_leave_times": 1,
    "absence_days": 2,
    "overtime_hours": 15.5,
    "attendance_rate": 90.9,
    "work_hours_total": 176.0
  }
}
```

##### 业务逻辑
1. **统计周期处理**：支持按日、周、月进行统计
2. **出勤率计算**：出勤天数/应出勤天数*100%
3. **异常统计**：统计迟到、早退、缺勤等异常情况
4. **工时计算**：计算总工作时长和加班时长

##### 异常情况
| 错误码 | 错误信息 | 触发条件 | 处理建议 |
|-------|---------|---------|----------|
| 400 | 参数验证失败 | 日期范围无效 | 检查日期参数 |
| 403 | 权限不足 | 无查看权限 | 联系管理员 |
| 500 | 系统错误 | 统计计算失败 | 稍后重试 |

#### 出勤率分析 - 数据查询

##### 基本信息
- **接口路径**：GET /api/attendance/statistics/attendance-rate
- **功能描述**：查询出勤率分析数据，包括趋势图和对比分析
- **业务场景**：管理层查看出勤率趋势，进行考勤管理决策
- **权限要求**：部门主管、HR管理员权限

##### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例值 | 验证规则 |
|-------|------|------|------|--------|----------|
| departmentCode | String | 否 | 部门编码 | "TECH_DEPT" | 字典编码 |
| startDate | String | 否 | 开始日期 | "2024-01-01" | 日期格式 |
| endDate | String | 否 | 结束日期 | "2024-01-31" | 日期格式 |
| granularity | String | 否 | 统计粒度 | "daily" | daily/weekly/monthly |

##### 响应数据
```json
{
  "code": 200,
  "message": "查询成功",
  "success": true,
  "data": {
    "period": "2024-01",
    "overall_rate": 92.5,
    "trend_data": [
      {
        "date": "2024-01-01",
        "attendance_rate": 95.2,
        "total_employees": 100,
        "present_employees": 95
      }
    ],
    "department_comparison": [
      {
        "department_name": "技术部",
        "attendance_rate": 94.5,
        "rank": 1
      }
    ]
  }
}
```

##### 业务逻辑
1. **趋势分析**：按时间维度展示出勤率变化趋势
2. **部门对比**：不同部门出勤率的横向对比
3. **异常识别**：识别出勤率异常波动的时间点
4. **预测分析**：基于历史数据预测未来趋势

##### 异常情况
| 错误码 | 错误信息 | 触发条件 | 处理建议 |
|-------|---------|---------|----------|
| 400 | 参数验证失败 | 参数格式错误 | 检查参数格式 |
| 403 | 权限不足 | 无查看权限 | 联系管理员 |
| 500 | 系统错误 | 分析计算失败 | 稍后重试 |

#### 考勤报表导出 - 数据查询

##### 基本信息
- **接口路径**：GET /api/attendance/statistics/export
- **功能描述**：导出考勤报表，生成Excel文件
- **业务场景**：HR管理员导出月度、年度考勤报表
- **权限要求**：HR管理员权限

##### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例值 | 验证规则 |
|-------|------|------|------|--------|----------|
| reportType | String | 是 | 报表类型 | "monthly" | monthly/annual/custom |
| startDate | String | 是 | 开始日期 | "2024-01-01" | 日期格式 |
| endDate | String | 是 | 结束日期 | "2024-01-31" | 日期格式 |
| departmentCode | String | 否 | 部门筛选 | "TECH_DEPT" | 字典编码 |
| includeDetails | Boolean | 否 | 是否包含明细 | true | true/false |

##### 响应数据
```json
{
  "code": 200,
  "message": "报表生成成功",
  "success": true,
  "data": {
    "file_url": "https://example.com/files/attendance_report_202401.xlsx",
    "file_name": "考勤报表_2024年1月.xlsx",
    "file_size": "2.5MB",
    "expire_time": "2024-02-01 00:00:00"
  }
}
```

##### 业务逻辑
1. **数据汇总**：汇总指定时间范围内的考勤数据
2. **报表生成**：生成标准格式的Excel报表文件
3. **文件存储**：将生成的文件存储到文件服务器
4. **下载链接**：提供临时下载链接，设置过期时间

##### 异常情况
| 错误码 | 错误信息 | 触发条件 | 处理建议 |
|-------|---------|---------|----------|
| 400 | 参数验证失败 | 日期范围无效 | 检查参数格式 |
| 403 | 权限不足 | 无导出权限 | 联系管理员 |
| 500 | 系统错误 | 文件生成失败 | 稍后重试 |

### 2.6 系统设置模块接口

#### 考勤规则查询 - 数据查询

##### 基本信息
- **接口路径**：GET /api/attendance/setting/rules
- **功能描述**：查询考勤规则配置信息
- **业务场景**：系统管理员查看和管理考勤规则
- **权限要求**：系统管理员权限

##### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例值 | 验证规则 |
|-------|------|------|------|--------|----------|
| ruleType | String | 否 | 规则类型 | "work_time" | 规则类型枚举 |

##### 响应数据
```json
{
  "code": 200,
  "message": "查询成功",
  "success": true,
  "data": {
    "work_time_rules": {
      "work_start_time": "09:00:00",
      "work_end_time": "18:00:00",
      "lunch_start_time": "12:00:00",
      "lunch_end_time": "13:00:00",
      "late_threshold": 30,
      "early_leave_threshold": 30
    },
    "location_rules": {
      "office_latitude": 39.908823,
      "office_longitude": 116.397470,
      "allowed_radius": 100,
      "enable_location_check": true
    },
    "overtime_rules": {
      "weekday_rate": 1.5,
      "weekend_rate": 2.0,
      "holiday_rate": 3.0,
      "max_daily_overtime": 4
    }
  }
}
```

##### 业务逻辑
1. **规则分类**：按工作时间、位置、加班等分类展示规则
2. **配置验证**：验证规则配置的合理性和一致性
3. **默认值处理**：为未配置的规则提供默认值
4. **版本管理**：记录规则配置的修改历史

##### 异常情况
| 错误码 | 错误信息 | 触发条件 | 处理建议 |
|-------|---------|---------|----------|
| 403 | 权限不足 | 无查看权限 | 联系管理员 |
| 500 | 系统错误 | 配置读取失败 | 稍后重试 |

## 3. 接口调用时序和依赖关系

### 3.1 核心业务流程接口时序图

#### 员工日常考勤完整流程
```mermaid
sequenceDiagram
    participant E as 员工
    participant M as 移动端
    participant A as 系统后端
    participant D as 数据库
    participant N as 通知服务
    
    E->>M: 1. 打开考勤应用
    M->>A: 2. 获取当前用户信息
    Note over A: GET /api/attendance/user/current
    A->>D: 查询用户信息
    D-->>A: 返回用户数据
    A-->>M: 返回用户信息
    
    M->>A: 3. 查询今日考勤统计
    Note over A: GET /api/attendance/record/statistics
    A->>D: 统计今日考勤
    D-->>A: 返回统计数据
    A-->>M: 返回统计信息
    
    E->>M: 4. 点击打卡
    M->>M: 获取GPS位置
    M->>A: 5. 提交打卡请求
    Note over A: POST /api/attendance/record/checkin
    A->>A: 验证位置和时间
    A->>D: 保存打卡记录
    D-->>A: 确认保存成功
    A-->>M: 返回打卡结果
    
    M->>A: 6. 刷新考勤统计
    Note over A: GET /api/attendance/record/statistics
    A-->>M: 返回更新后统计
    M-->>E: 显示打卡成功
```

#### 请假申请完整审批流程
```mermaid
sequenceDiagram
    participant E as 申请员工
    participant M1 as 员工移动端
    participant A as 系统后端
    participant W as 工作流引擎
    participant M2 as 管理端
    participant S as 审批主管
    participant N as 通知服务
    
    E->>M1: 1. 申请请假
    M1->>A: 2. 查询假期余额
    Note over A: GET /api/attendance/leave/balance
    A-->>M1: 返回余额信息
    
    E->>M1: 3. 填写申请信息
    M1->>A: 4. 提交请假申请
    Note over A: POST /api/attendance/leave/add
    A->>A: 验证余额和时间
    A->>W: 启动审批工作流
    W-->>A: 返回工作流ID
    A->>N: 发送审批通知
    A-->>M1: 返回申请结果
    
    N->>S: 5. 通知审批人
    S->>M2: 6. 查看待审批
    M2->>A: 7. 获取申请列表
    Note over A: GET /api/attendance/leave/list
    A-->>M2: 返回待审批列表
    
    S->>M2: 8. 查看申请详情
    M2->>A: 9. 获取申请详情
    Note over A: GET /api/attendance/leave/detail
    A-->>M2: 返回详细信息
    
    S->>M2: 10. 提交审批决定
    M2->>A: 11. 审批请假申请
    Note over A: POST /api/attendance/leave/approve
    A->>W: 更新工作流状态
    A->>A: 扣减假期余额
    A->>N: 发送结果通知
    A-->>M2: 返回审批结果
    
    N->>E: 12. 通知申请人
```

### 3.2 接口依赖关系

#### 核心依赖关系图
```
用户管理接口 ← 考勤打卡接口 ← 统计分析接口
     ↑              ↑              ↑
系统设置接口    请假管理接口    报表导出接口
     ↑              ↑              ↑
排班管理接口    加班管理接口    通知服务接口
```

#### 依赖说明
- **用户管理接口 → 所有业务接口**：提供用户身份验证和权限验证基础
- **系统设置接口 → 考勤打卡接口**：提供考勤规则配置支撑
- **考勤打卡接口 → 统计分析接口**：提供基础考勤数据
- **请假/加班管理接口 → 统计分析接口**：提供请假加班数据

#### 调用顺序要求
1. **必须先调用**：用户认证接口、系统配置接口
2. **可以并行调用**：考勤打卡、请假申请、加班申请接口
3. **必须后调用**：统计分析接口、报表导出接口

## 4. 接口安全和性能要求

### 4.1 安全要求

#### 身份认证
- **Token验证**：所有接口都需要有效的JWT Token
- **权限控制**：基于角色的访问控制（RBAC）
- **数据权限**：用户只能访问授权范围内的数据

#### 数据安全
- **敏感数据加密**：位置信息、个人信息等敏感数据传输加密
- **SQL注入防护**：所有数据库查询使用参数化查询
- **XSS防护**：输入数据进行HTML转义处理

### 4.2 性能要求

#### 响应时间
- **查询接口**：平均响应时间 < 500ms
- **变更接口**：平均响应时间 < 1000ms
- **统计接口**：平均响应时间 < 2000ms
- **导出接口**：平均响应时间 < 5000ms

#### 并发处理
- **打卡接口**：支持1000并发用户同时打卡
- **查询接口**：支持5000并发查询请求
- **审批接口**：支持100并发审批操作

### 4.3 可用性要求

#### 系统可用性
- **服务可用性**：99.9%
- **数据一致性**：强一致性要求
- **故障恢复**：故障恢复时间 < 5分钟

#### 容错处理
- **重试机制**：网络异常时自动重试3次
- **降级策略**：核心功能优先保障
- **熔断保护**：异常率超过阈值时自动熔断

## 5. 接口版本管理

### 5.1 版本策略

#### 版本号规则
- **格式**：v{major}.{minor}.{patch}
- **主版本**：不兼容的API修改
- **次版本**：向下兼容的功能性新增
- **修订版本**：向下兼容的问题修正

#### 兼容性保证
- **向下兼容**：新版本保持对旧版本的兼容
- **废弃通知**：废弃接口提前3个月通知
- **迁移指南**：提供详细的版本迁移文档

### 5.2 变更管理

#### 变更流程
1. **需求评估**：评估变更的必要性和影响范围
2. **设计审查**：技术团队审查接口设计
3. **兼容性测试**：验证新版本的兼容性
4. **发布通知**：提前通知使用方版本变更

#### 文档维护
- **变更日志**：记录每个版本的详细变更内容
- **迁移指南**：提供版本升级的操作指南
- **示例代码**：提供新版本接口的调用示例

---

**文档状态**：已完成
**版本控制**：v1.0
**下一步行动**：
1. 基于本API接口规范进行前端开发
2. 基于本API接口规范进行后端实现
3. 进行API接口规范评审和确认
4. 建立API接口测试用例 检查日期参数 |
| 403 | 权限不足 | 无查看权限 | 联系管理员 |
| 500 | 系统错误 | 统计计算失败 | 稍后重试 |

### 2.2 请假管理模块接口

#### 提交请假申请 - 数据变更

##### 基本信息
- **接口路径**：POST /api/attendance/leave/add
- **功能描述**：员工提交请假申请，进入审批流程
- **业务场景**：员工需要请假时提交申请
- **权限要求**：普通员工权限

##### 请求参数
```json
{
  "leave_type_code": "ANNUAL_LEAVE",
  "start_date": "2024-02-01",
  "end_date": "2024-02-03",
  "leave_days": 3,
  "reason": "春节回家探亲",
  "attachment_url": "https://example.com/files/leave_proof.jpg"
}
```

| 参数名 | 类型 | 必填 | 说明 | 示例值 | 验证规则 |
|-------|------|------|------|--------|----------|
| leave_type_code | String | 是 | 请假类型编码 | "ANNUAL_LEAVE" | 有效字典值 |
| start_date | String | 是 | 请假开始日期 | "2024-02-01" | 日期格式 |
| end_date | String | 是 | 请假结束日期 | "2024-02-03" | 日期格式 |
| leave_days | Decimal | 是 | 请假天数 | 3 | 大于0 |
| reason | String | 是 | 请假原因 | "春节回家探亲" | 长度不超过500 |
| attachment_url | String | 否 | 附件地址 | "https://example.com/files/leave_proof.jpg" | 有效URL |

##### 响应数据
```json
{
  "code": 200,
  "message": "申请提交成功",
  "success": true,
  "data": {
    "id": "LEAVE_20240131_001",
    "business_id": "WF_20240131_001",
    "status": "待审批",
    "create_time": "2024-01-31 10:00:00",
    "remaining_balance": 7
  }
}
```

##### 业务逻辑
1. **假期余额验证**：检查员工对应假期类型的剩余天数是否充足
2. **时间冲突检查**：验证请假时间是否与已有请假或加班冲突
3. **工作流启动**：创建审批工作流实例，分配给直接主管
4. **通知发送**：向审批人发送待审批通知

##### 事务处理
- **事务范围**：请假申请创建、工作流实例创建、通知发送
- **回滚条件**：假期余额不足、时间冲突、工作流创建失败
- **补偿机制**：申请失败时清理已创建的数据

##### 异常情况
| 错误码 | 错误信息 | 触发条件 | 处理建议 |
|-------|---------|---------|----------|
| 400 | 参数验证失败 | 日期格式错误 | 检查参数格式 |
| 409 | 假期余额不足 | 剩余假期天数不够 | 调整请假天数 |
| 422 | 时间冲突 | 与已有申请冲突 | 调整请假时间 |
| 500 | 系统错误 | 工作流创建失败 | 稍后重试 |

#### 请假申请列表 - 数据查询

##### 基本信息
- **接口路径**：GET /api/attendance/leave/list
- **功能描述**：查询请假申请列表，支持分页和状态筛选
- **业务场景**：员工查看个人请假记录，管理员查看待审批申请
- **权限要求**：普通员工（查看个人）、部门主管（查看下属）、HR管理员（查看全部）

##### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例值 | 验证规则 |
|-------|------|------|------|--------|----------|
| pageNum | Integer | 否 | 页码，默认1 | 1 | 大于0 |
| pageSize | Integer | 否 | 页大小，默认20 | 20 | 1-100 |
| userId | String | 否 | 用户ID筛选 | "USR_20240101_001" | 有效用户ID |
| status | String | 否 | 审批状态筛选 | "待审批" | 枚举值 |
| leaveType | String | 否 | 请假类型筛选 | "ANNUAL_LEAVE" | 字典编码 |
| startDate | String | 否 | 申请开始日期 | "2024-01-01" | 日期格式 |
| endDate | String | 否 | 申请结束日期 | "2024-01-31" | 日期格式 |

##### 响应数据
```json
{
  "code": 200,
  "message": "查询成功",
  "success": true,
  "data": {
    "list": [
      {
        "id": "LEAVE_20240131_001",
        "user_id": "USR_20240101_001",
        "user_name": "张三",
        "leave_type_name": "年假",
        "start_date": "2024-02-01",
        "end_date": "2024-02-03",
        "leave_days": 3,
        "reason": "春节回家探亲",
        "status": "待审批",
        "create_time": "2024-01-31 10:00:00"
      }
    ],
    "total": 50
  }
}
```

##### 业务逻辑
1. **权限过滤**：根据用户角色过滤可查看的申请记录
2. **状态筛选**：支持按审批状态筛选（待审批、已通过、已拒绝等）
3. **时间排序**：按申请时间倒序排列
4. **关联查询**：查询用户信息和请假类型名称

##### 异常情况
| 错误码 | 错误信息 | 触发条件 | 处理建议 |
|-------|---------|---------|----------|
| 400 | 参数验证失败 | 参数格式错误 | 检查参数格式 |
| 403 | 权限不足 | 无查看权限 | 联系管理员 |
| 500 | 系统错误 | 数据库查询失败 | 稍后重试 |

#### 审批请假申请 - 业务操作

##### 基本信息
- **接口路径**：POST /api/attendance/leave/approve
- **功能描述**：审批人对请假申请进行审批决定
- **业务场景**：部门主管或HR对下属的请假申请进行审批
- **权限要求**：部门主管、HR管理员权限

##### 请求参数
```json
{
  "leave_id": "LEAVE_20240131_001",
  "approve_result": "APPROVED",
  "approve_comment": "同意请假，注意工作交接",
  "approve_time": "2024-01-31 14:00:00"
}
```

| 参数名 | 类型 | 必填 | 说明 | 示例值 | 验证规则 |
|-------|------|------|------|--------|----------|
| leave_id | String | 是 | 请假申请ID | "LEAVE_20240131_001" | 有效申请ID |
| approve_result | String | 是 | 审批结果 | "APPROVED" | APPROVED/REJECTED |
| approve_comment | String | 否 | 审批意见 | "同意请假，注意工作交接" | 长度不超过500 |
| approve_time | String | 是 | 审批时间 | "2024-01-31 14:00:00" | 时间格式 |

##### 业务流程
1. **权限验证**：验证审批人是否有权限审批该申请
2. **状态检查**：验证申请是否处于可审批状态
3. **审批处理**：更新申请状态和审批信息
4. **假期扣减**：审批通过时扣减员工假期余额
5. **通知发送**：向申请人发送审批结果通知

##### 响应数据
```json
{
  "code": 200,
  "message": "审批成功",
  "success": true,
  "data": {
    "leave_id": "LEAVE_20240131_001",
    "final_status": "已通过",
    "approve_time": "2024-01-31 14:00:00",
    "remaining_balance": 4
  }
}
```

##### 异常情况
| 错误码 | 错误信息 | 触发条件 | 处理建议 |
|-------|---------|---------|----------|
| 400 | 申请ID无效 | ID格式错误 | 检查申请ID |
| 403 | 无审批权限 | 非指定审批人 | 联系正确审批人 |
| 409 | 申请状态异常 | 已审批或已撤销 | 刷新申请状态 |
| 422 | 业务验证失败 | 假期余额异常 | 检查业务数据 |

#### 假期余额查询 - 数据查询

##### 基本信息
- **接口路径**：GET /api/attendance/leave/balance
- **功能描述**：查询员工各类假期的剩余余额
- **业务场景**：员工查看可用假期天数，申请前确认余额
- **权限要求**：普通员工（查看个人）、HR管理员（查看全部）

##### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例值 | 验证规则 |
|-------|------|------|------|--------|----------|
| userId | String | 否 | 用户ID | "USR_20240101_001" | 有效用户ID |
| year | Integer | 否 | 年份 | 2024 | 有效年份 |

##### 响应数据
```json
{
  "code": 200,
  "message": "查询成功",
  "success": true,
  "data": {
    "user_id": "USR_20240101_001",
    "user_name": "张三",
    "year": 2024,
    "balances": [
      {
        "leave_type_code": "ANNUAL_LEAVE",
        "leave_type_name": "年假",
        "total_days": 10,
        "used_days": 3,
        "remaining_days": 7
      },
      {
        "leave_type_code": "SICK_LEAVE",
        "leave_type_name": "病假",
        "total_days": 30,
        "used_days": 2,
        "remaining_days": 28
      }
    ]
  }
}
```

##### 业务逻辑
1. **权限验证**：员工只能查看个人余额，HR可查看所有员工
2. **年度计算**：按自然年度计算假期余额
3. **实时更新**：余额数据实时反映最新的请假使用情况
4. **类型分类**：按假期类型分别显示余额信息

##### 异常情况
| 错误码 | 错误信息 | 触发条件 | 处理建议 |
|-------|---------|---------|----------|
| 400 | 参数验证失败 | 年份格式错误 | 检查参数格式 |
| 403 | 权限不足 | 无查看权限 | 联系管理员 |
| 404 | 用户不存在 | 用户ID无效 | 确认用户ID |

### 2.3 加班管理模块接口

#### 提交加班申请 - 数据变更

##### 基本信息
- **接口路径**：POST /api/attendance/overtime/add
- **功能描述**：员工提交加班申请，进入审批流程
- **业务场景**：员工需要加班时提交申请
- **权限要求**：普通员工权限

##### 请求参数
```json
{
  "overtime_date": "2024-02-01",
  "start_time": "18:00:00",
  "end_time": "21:00:00",
  "overtime_hours": 3,
  "reason": "项目紧急需求开发",
  "overtime_type_code": "WEEKDAY_OVERTIME"
}
```

| 参数名 | 类型 | 必填 | 说明 | 示例值 | 验证规则 |
|-------|------|------|------|--------|----------|
| overtime_date | String | 是 | 加班日期 | "2024-02-01" | 日期格式 |
| start_time | String | 是 | 加班开始时间 | "18:00:00" | 时间格式 |
| end_time | String | 是 | 加班结束时间 | "21:00:00" | 时间格式 |
| overtime_hours | Decimal | 是 | 加班时长 | 3 | 大于0 |
| reason | String | 是 | 加班原因 | "项目紧急需求开发" | 长度不超过500 |
| overtime_type_code | String | 是 | 加班类型编码 | "WEEKDAY_OVERTIME" | 有效字典值 |

##### 响应数据
```json
{
  "code": 200,
  "message": "申请提交成功",
  "success": true,
  "data": {
    "id": "OVERTIME_20240131_001",
    "business_id": "WF_20240131_002",
    "status": "待审批",
    "create_time": "2024-01-31 16:00:00",
    "estimated_compensation": 150.0
  }
}
```

##### 业务逻辑
1. **时间合理性验证**：验证加班时间是否合理，不与正常工作时间重叠
2. **时长计算**：自动计算加班时长，验证与手动输入的一致性
3. **补偿预估**：根据加班类型和时长预估补偿金额
4. **工作流启动**：创建审批工作流，分配给直接主管

##### 事务处理
- **事务范围**：加班申请创建、工作流实例创建、通知发送
- **回滚条件**：时间冲突、工作流创建失败
- **补偿机制**：申请失败时清理已创建的数据

##### 异常情况
| 错误码 | 错误信息 | 触发条件 | 处理建议 |
|-------|---------|---------|----------|
| 400 | 参数验证失败 | 时间格式错误 | 检查参数格式 |
| 422 | 时间设置异常 | 结束时间早于开始时间 | 调整时间设置 |
| 409 | 时间冲突 | 与已有申请冲突 | 调整加班时间 |
| 500 | 系统错误 | 工作流创建失败 | 稍后重试 |

#### 加班申请列表 - 数据查询

##### 基本信息
- **接口路径**：GET /api/attendance/overtime/list
- **功能描述**：查询加班申请列表，支持分页和状态筛选
- **业务场景**：员工查看个人加班记录，管理员查看待审批申请
- **权限要求**：普通员工（查看个人）、部门主管（查看下属）、HR管理员（查看全部）

##### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例值 | 验证规则 |
|-------|------|------|------|--------|----------|
| pageNum | Integer | 否 | 页码，默认1 | 1 | 大于0 |
| pageSize | Integer | 否 | 页大小，默认20 | 20 | 1-100 |
| userId | String | 否 | 用户ID筛选 | "USR_20240101_001" | 有效用户ID |
| status | String | 否 | 审批状态筛选 | "待审批" | 枚举值 |
| overtimeType | String | 否 | 加班类型筛选 | "WEEKDAY_OVERTIME" | 字典编码 |
| startDate | String | 否 | 加班开始日期 | "2024-01-01" | 日期格式 |
| endDate | String | 否 | 加班结束日期 | "2024-01-31" | 日期格式 |

##### 响应数据
```json
{
  "code": 200,
  "message": "查询成功",
  "success": true,
  "data": {
    "list": [
      {
        "id": "OVERTIME_20240131_001",
        "user_id": "USR_20240101_001",
        "user_name": "张三",
        "overtime_date": "2024-02-01",
        "start_time": "18:00:00",
        "end_time": "21:00:00",
        "overtime_hours": 3,
        "overtime_type_name": "工作日加班",
        "reason": "项目紧急需求开发",
        "status": "待审批",
        "create_time": "2024-01-31 16:00:00"
      }
    ],
    "total": 30
  }
}
```

##### 业务逻辑
1. **权限过滤**：根据用户角色过滤可查看的申请记录
2. **状态筛选**：支持按审批状态筛选
3. **时间排序**：按加班日期倒序排列
4. **统计信息**：提供加班时长统计

##### 异常情况
| 错误码 | 错误信息 | 触发条件 | 处理建议 |
|-------|---------|---------|----------|
| 400 | 参数验证失败 | 参数格式错误 | 检查参数格式 |
| 403 | 权限不足 | 无查看权限 | 联系管理员 |
| 500 | 系统错误 | 数据库查询失败 | 稍后重试 |

### 2.4 用户管理模块接口

#### 用户信息列表 - 数据查询

##### 基本信息
- **接口路径**：GET /api/attendance/user/list
- **功能描述**：查询用户信息列表，支持分页和筛选
- **业务场景**：HR管理员查看和管理员工信息
- **权限要求**：HR管理员、系统管理员权限

##### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例值 | 验证规则 |
|-------|------|------|------|--------|----------|
| pageNum | Integer | 否 | 页码，默认1 | 1 | 大于0 |
| pageSize | Integer | 否 | 页大小，默认20 | 20 | 1-100 |
| keyword | String | 否 | 搜索关键词 | "张三" | 长度不超过50 |
| departmentCode | String | 否 | 部门筛选 | "TECH_DEPT" | 字典编码 |
| roleCode | String | 否 | 角色筛选 | "EMPLOYEE" | 字典编码 |
| status | String | 否 | 状态筛选 | "ACTIVE" | 枚举值 |

##### 响应数据
```json
{
  "code": 200,
  "message": "查询成功",
  "success": true,
  "data": {
    "list": [
      {
        "id": "USR_20240101_001",
        "username": "zhangsan",
        "name": "张三",
        "phone": "13800138000",
        "email": "<EMAIL>",
        "department_name": "技术部",
        "role_name": "普通员工",
        "status_name": "在职",
        "create_time": "2024-01-01 09:00:00"
      }
    ],
    "total": 150
  }
}
```

##### 业务逻辑
1. **关键词搜索**：支持按姓名、用户名、手机号搜索
2. **部门筛选**：支持按部门筛选员工
3. **状态过滤**：只显示指定状态的员工
4. **排序规则**：按创建时间倒序排列

##### 异常情况
| 错误码 | 错误信息 | 触发条件 | 处理建议 |
|-------|---------|---------|----------|
| 400 | 参数验证失败 | 参数格式错误 | 检查参数格式 |
| 403 | 权限不足 | 无管理权限 | 联系管理员 |
| 500 | 系统错误 | 数据库查询失败 | 稍后重试 |

#### 新增用户 - 数据变更

##### 基本信息
- **接口路径**：POST /api/attendance/user/add
- **功能描述**：新增用户信息，创建员工账号
- **业务场景**：HR管理员为新入职员工创建账号
- **权限要求**：HR管理员、系统管理员权限

##### 请求参数
```json
{
  "username": "lisi",
  "name": "李四",
  "phone": "13800138001",
  "email": "<EMAIL>",
  "department_code": "TECH_DEPT",
  "role_code": "EMPLOYEE",
  "status_code": "ACTIVE"
}
```

| 参数名 | 类型 | 必填 | 说明 | 示例值 | 验证规则 |
|-------|------|------|------|--------|----------|
| username | String | 是 | 用户名 | "lisi" | 长度4-20，字母数字 |
| name | String | 是 | 真实姓名 | "李四" | 长度2-20 |
| phone | String | 是 | 手机号码 | "13800138001" | 11位数字 |
| email | String | 是 | 邮箱地址 | "<EMAIL>" | 邮箱格式 |
| department_code | String | 是 | 部门编码 | "TECH_DEPT" | 有效字典值 |
| role_code | String | 是 | 角色编码 | "EMPLOYEE" | 有效字典值 |
| status_code | String | 是 | 状态编码 | "ACTIVE" | 有效字典值 |

##### 响应数据
```json
{
  "code": 200,
  "message": "用户创建成功",
  "success": true,
  "data": {
    "id": "USR_20240131_002",
    "username": "lisi",
    "name": "李四",
    "create_time": "2024-01-31 10:00:00"
  }
}
```

##### 业务逻辑
1. **唯一性验证**：验证用户名、手机号、邮箱的唯一性
2. **字典验证**：验证部门、角色、状态编码的有效性
3. **默认设置**：设置默认密码和初始假期余额
4. **关联创建**：创建用户的假期余额记录

##### 事务处理
- **事务范围**：用户信息创建、假期余额初始化
- **回滚条件**：唯一性冲突、字典验证失败
- **补偿机制**：创建失败时清理已创建的数据

##### 异常情况
| 错误码 | 错误信息 | 触发条件 | 处理建议 |
|-------|---------|---------|----------|
| 400 | 参数验证失败 | 参数格式错误 | 检查参数格式 |
| 409 | 数据冲突 | 用户名或手机号已存在 | 修改重复信息 |
| 422 | 业务验证失败 | 字典编码无效 | 检查字典配置 |
| 500 | 系统错误 | 数据库操作失败 | 稍后重试 |

### 2.5 统计分析模块接口

#### 部门考勤统计 - 数据查询

##### 基本信息
- **接口路径**：GET /api/attendance/statistics/department
- **功能描述**：查询部门考勤统计数据，包括出勤率、异常统计等
- **业务场景**：部门主管和HR查看部门考勤情况
- **权限要求**：部门主管（查看本部门）、HR管理员（查看全部）

##### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例值 | 验证规则 |
|-------|------|------|------|--------|----------|
| departmentCode | String | 否 | 部门编码 | "TECH_DEPT" | 字典编码 |
| startDate | String | 否 | 开始日期 | "2024-01-01" | 日期格式 |
| endDate | String | 否 | 结束日期 | "2024-01-31" | 日期格式 |
| statisticsType | String | 否 | 统计类型 | "monthly" | daily/weekly/monthly |

##### 响应数据
```json
{
  "code": 200,
  "message": "查询成功",
  "success": true,
  "data": {
    "department_code": "TECH_DEPT",
    "department_name": "技术部",
    "period": "2024-01",
    "total_employees": 25,
    "work_days": 22,
    "total_attendance": 520,
    "normal_attendance": 480,
    "late_times": 30,
    "early_leave_times": 10,
    "absence_times": 20,
    "attendance_rate": 92.3,
    "overtime_hours": 180.5,
    "leave_days": 45
  }
}
```

##### 业务逻辑
1. **权限过滤**：部门主管只能查看本部门数据
2. **统计计算**：计算出勤率、异常次数等关键指标
3. **时间范围**：支持按不同时间维度统计
4. **数据聚合**：汇总部门内所有员工的考勤数据

##### 异常情况
| 错误码 | 错误信息 | 触发条件 | 处理建议 |
|-------|---------|---------|----------|
| 400 | 参数验证失败 | 日期范围无效