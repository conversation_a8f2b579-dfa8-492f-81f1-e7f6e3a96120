/* General detail page styling */
.funi-detail-group {
    background-color: var(--funi-background-color-light);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.funi-detail-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 10px;
    font-size: 14px;
    color: var(--funi-text-color-regular);
}

.funi-detail-label {
    flex-shrink: 0;
    width: 120px;
    /* Adjust label width as needed */
    text-align: right;
    margin-right: 10px;
    color: var(--funi-text-color-secondary);
}

.funi-detail-value {
    flex-grow: 1;
    word-break: break-word;
    /* Allow long text to wrap */
}

.funi-file-list {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.funi-file-link {
    color: #409eff;
    text-decoration: none;
}

.funi-file-link:hover {
    text-decoration: underline;
}

/* Timeline styling for process records */
.funi-timeline {
    position: relative;
    padding: 20px 0 20px 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.funi-timeline::before {
    content: '';
    position: absolute;
    left: 10px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--funi-border-color-light);
}

.funi-timeline-item {
    position: relative;
    margin-bottom: 20px;
    padding-left: 20px;
}

.funi-timeline-dot {
    position: absolute;
    left: 5px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--funi-primary-color);
    border: 2px solid var(--funi-background-color-light);
    box-shadow: 0 0 0 2px var(--funi-primary-color);
    z-index: 1;
}

.funi-timeline-content {
    background-color: var(--funi-background-color-base);
    padding: 15px;
    border-radius: 4px;
    border: 1px solid var(--funi-border-color-lighter);
}

.funi-timeline-title {
    font-weight: bold;
    color: var(--funi-text-color-primary);
    margin-bottom: 5px;
}

.funi-timeline-description,
.funi-timeline-time {
    color: #606266;
    font-size: 13px;
    margin-bottom: 5px;
}

.funi-timeline-time {
    color: var(--funi-text-color-secondary);
}