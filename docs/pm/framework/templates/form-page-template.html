<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购计划管理 - 新增/编辑</title>
    <link rel="stylesheet" href="../assets/css/funi-framework.css" />
    <link rel="stylesheet" href="../assets/css/funi-components.css" />
    <link rel="stylesheet" href="../assets/css/funi-themes.css" />
    <link rel="stylesheet" href="../assets/css/funi-form.css" />
</head>

<body>

    <div id="app" class="form-container">
        <div class="form-step-container">
            <div class="form-step active">
                <div class="step-number">1</div>
                <div class="step-title">基本信息</div>
            </div>
            <iconify-icon icon="ic:outline-navigate-next" class="funi-step-icon"></iconify-icon>
            <div class="form-step">
                <div class="step-number">2</div>
                <div class="step-title">项目信息</div>
            </div>
            <iconify-icon icon="ic:outline-navigate-next" class="funi-step-icon"></iconify-icon>
            <div class="form-step">
                <div class="step-number">3</div>
                <div class="step-title">附件上传</div>
            </div>
        </div>
        <div class="container-content">
            <div class="form-step-content form-step-1">
                <div class="form-section-title">招标信息</div>
                <div class="funi-form">
                    <form class="form-grid">
                        <div class="form-item-row">
                            <label for="planNumber" class="form-item-label">计划编号:</label>
                            <div class="form-item-value">
                                <input type="text" id="planNumber" name="planNumber" value="自动生成" readonly>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementType" class="form-item-label required">采购类型:</label>
                            <div class="form-item-value">
                                <select id="procurementType" name="procurementType">
                                    <option value="施工">施工</option>
                                    <option value="货物">货物</option>
                                    <option value="服务">服务</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="biddingCategory" class="form-item-label required">招标类别:</label>
                            <div class="form-item-value">
                                <select id="biddingCategory" name="biddingCategory">
                                    <!-- Options will be dynamically loaded based on procurementType -->
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementMethod" class="form-item-label required">采购方式:</label>
                            <div class="form-item-value">
                                <select id="procurementMethod" name="procurementMethod">
                                    <option value="公告比选">公告比选</option>
                                    <option value="邀请比选">邀请比选</option>
                                    <option value="竞争性磋商">竞争性磋商</option>
                                    <option value="竞争性谈判">竞争性谈判</option>
                                    <option value="询价择优">询价择优</option>
                                    <option value="单一来源">单一来源</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="budgetAmount" class="form-item-label required">采购预算金额（万元）:</label>
                            <div class="form-item-value">
                                <input type="text" id="budgetAmount" name="budgetAmount" placeholder="采购预算金额">
                            </div>

                        </div>
                        <div class="form-item-row">
                            <label for="fundSource" class="form-item-label required">资金来源:</label>
                            <div class="form-item-value">
                                <select id="fundSource" name="fundSource">
                                    <option value="自有资金">自有资金</option>
                                    <option value="政府投资">政府投资</option>
                                    <option value="其它社会资本">其它社会资本</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="biddingTime" class="form-item-label">招标时间:</label>
                            <div class="form-item-value">
                                <input type="text" id="biddingTime" name="biddingTime" placeholder="请输入" maxlength="50">

                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementOrganizationMethod" class="form-item-label required">采购组织方式:</label>
                            <div class="form-item-value">
                                <select id="procurementOrganizationMethod" name="procurementOrganizationMethod">
                                    <option value="委托招标">委托招标</option>
                                    <option value="自主招标">自主招标</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row" id="agencyRow">
                            <label for="agency" class="form-item-label required">代理机构:</label>
                            <div class="form-item-value">
                                <select id="agency" name="agency">
                                    <option value="">请选择(多选)</option>
                                    <option value="代理机构A">代理机构A</option>
                                    <option value="代理机构B">代理机构B</option>
                                    <option value="代理机构C">代理机构C</option>
                                </select>

                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="annualProcurementPlan" class="form-item-label">年采购计划（万元）:</label>
                            <div class="form-item-value">
                                <input type="number" id="annualProcurementPlan" name="annualProcurementPlan"
                                    value="0.00" step="0.01">

                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectHandler" class="form-item-label required">项目经办人:</label>
                            <div class="form-item-value">
                                <input type="text" id="projectHandler" name="projectHandler" value="当前创建人" readonly>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="decisionDate" class="form-item-label required">立项决策日期:</label>
                            <div class="form-item-value">
                                <input type="date" id="decisionDate" name="decisionDate">
                            </div>
                        </div>
                    </form>
                </div>

                <div class="form-section-title">项目信息</div>
                <div class="funi-form">
                    <form class="form-grid">
                        <div class="form-item-row">
                            <label for="projectType" class="form-item-label required">项目类型:</label>
                            <div class="form-item-value">
                                <select id="projectType" name="projectType">
                                    <option value="依法必须招标项目">依法必须招标项目</option>
                                    <option value="非法定招标采购项目">非法定招标采购项目</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectOwner" class="form-item-label required">项目业主:</label>
                            <div class="form-item-value">
                                <input type="text" id="projectOwner" name="projectOwner"
                                    placeholder="由项目经办人信息自动带入，可再次配置" maxlength="50" readonly>

                            </div>
                        </div>
                        <div class="form-item-row form-item-full-width">
                            <label for="projectBasicInfo" class="form-item-label">项目基本情况:</label>
                            <div class="form-item-value">
                                <textarea id="projectBasicInfo" name="projectBasicInfo" maxlength="255"></textarea>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="secondaryCompany" class="form-item-label">所属二级公司单位:</label>
                            <div class="form-item-value">
                                <input type="text" id="secondaryCompany" name="secondaryCompany" maxlength="255"
                                    placeholder="请选择二级公司单位" readonly>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="remarks" class="form-item-label">备注:</label>
                            <div class="form-item-value">
                                <input type="text" id="remarks" name="remarks" maxlength="255">
                            </div>
                        </div>
                        <div class="form-item-row form-item-full-width">
                            <label for="decisionFiles" class="form-item-label">立项决策文件:</label>
                            <div class="form-item-value">
                                <input type="file" id="decisionFiles" name="decisionFiles" multiple>
                                <div class="form-item-tip">支持多附件上传</div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="form-step-content form-step-2"></div>
            <div class="form-step-content form-step-3"></div>
        </div>

        <div class="form-actions">
            <!-- Buttons will be dynamically loaded here -->
        </div>
    </div>

    <script src="https://code.iconify.design/iconify-icon/3.0.0/iconify-icon.min.js"></script>
    <script src="../../assets/js/funi-theme-switcher.js"></script>
    <script src="../../assets/js/funi-interactions.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const procurementTypeSelect = document.getElementById('procurementType');
            const biddingCategorySelect = document.getElementById('biddingCategory');
            const procurementOrganizationMethodSelect = document.getElementById('procurementOrganizationMethod');
            const agencyRow = document.getElementById('agencyRow');
            const decisionDateInput = document.getElementById('decisionDate');

            // Set default decision date to current date
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            decisionDateInput.value = `${year}-${month}-${day}`;

            const biddingCategories = {
                '施工': ['工程类(施工/勘察/EPC/监理)'],
                '货物': ['货物类(材料/设备/供应及安装)'],
                '服务': ['服务类(监理/咨询/物业)'],
                '其他': ['其他(文本输入)']
            };

            const updateBiddingCategory = () => {
                const selectedType = procurementTypeSelect.value;
                const categories = biddingCategories[selectedType] || [];
                biddingCategorySelect.innerHTML = '';
                categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category;
                    option.textContent = category;
                    biddingCategorySelect.appendChild(option);
                });
            };

            const toggleAgencyVisibility = () => {
                if (procurementOrganizationMethodSelect.value === '委托招标') {
                    agencyRow.style.display = 'flex';
                } else {
                    agencyRow.style.display = 'none';
                }
            };

            // Initial load
            updateBiddingCategory();
            toggleAgencyVisibility();

            procurementTypeSelect.addEventListener('change', updateBiddingCategory);
            procurementOrganizationMethodSelect.addEventListener('change', toggleAgencyVisibility);

            // New logic for form content visibility
            const formStepContents = document.querySelectorAll('.form-step-content');

            const updateFormContentVisibility = () => {
                const activeStep = document.querySelector('.form-step.active');
                if (activeStep) {
                    const activeStepNumber = activeStep.querySelector('.step-number').textContent;
                    formStepContents.forEach(content => {
                        if (content.classList.contains(`form-step-${activeStepNumber}`)) {
                            content.classList.add('active');
                        } else {
                            content.classList.remove('active');
                        }
                    });
                }
            };

            // New logic for form actions
            const formSteps = document.querySelectorAll('.form-step');
            const formActions = document.querySelector('.form-actions');

            const updateFormActions = () => {
                const activeStep = document.querySelector('.form-step.active');
                const currentIndex = Array.from(formSteps).indexOf(activeStep);
                const isLastStep = currentIndex === formSteps.length - 1;
                const isFirstStep = currentIndex === 0;

                formActions.innerHTML = ''; // Clear existing buttons

                // Always add "上一步" button if not the first step
                if (!isFirstStep) {
                    const prevStepButton = document.createElement('button');
                    prevStepButton.className = 'button primary';
                    prevStepButton.id = 'prevStepButton';
                    prevStepButton.textContent = '上一步';
                    prevStepButton.addEventListener('click', () => {
                        if (currentIndex > 0) {
                            activeStep.classList.remove('active');
                            formSteps[currentIndex - 1].classList.add('active');
                            updateFormActions(); // Re-evaluate buttons and content after changing active step
                        }
                    });
                    formActions.appendChild(prevStepButton);
                }

                if (isLastStep) {
                    const submitButton = document.createElement('button');
                    submitButton.className = 'button primary';
                    submitButton.id = 'submitButton';
                    submitButton.textContent = '提交';
                    submitButton.addEventListener('click', () => {
                        alert('提交成功！');
                        parent.window.location.hash = '#/procurement-plan-management'; // Go back to list page
                    });
                    formActions.appendChild(submitButton);
                } else {
                    // Add "暂存" button
                    const saveDraftButton = document.createElement('button');
                    saveDraftButton.className = 'button primary';
                    saveDraftButton.id = 'saveDraftButton';
                    saveDraftButton.textContent = '暂存';
                    saveDraftButton.addEventListener('click', () => {
                        alert('暂存成功！');
                        // Add actual save draft logic here
                    });
                    formActions.appendChild(saveDraftButton);

                    // Add "下一步" button
                    const nextStepButton = document.createElement('button');
                    nextStepButton.className = 'button primary';
                    nextStepButton.id = 'nextStepButton';
                    nextStepButton.textContent = '下一步';
                    nextStepButton.addEventListener('click', () => {
                        // Simulate moving to the next step by changing the active class
                        if (currentIndex < formSteps.length - 1) {
                            activeStep.classList.remove('active');
                            formSteps[currentIndex + 1].classList.add('active');
                            updateFormActions(); // Re-evaluate buttons and content after changing active step
                        }
                    });
                    formActions.appendChild(nextStepButton);
                }
                updateFormContentVisibility(); // Call this after updating active step
            };

            // Initial call to set buttons and content visibility
            updateFormActions();

            // Add event listeners to form steps to update buttons when active step changes
            // This assumes clicking a step makes it active.
            // In a real app, this would be handled by a more robust step navigation logic.
            formSteps.forEach(step => {
                step.addEventListener('click', () => {
                    formSteps.forEach(s => s.classList.remove('active'));
                    step.classList.add('active');
                    updateFormActions(); // This will now also call updateFormContentVisibility
                });
            });

            // Populate form for editing if ID is present in URL hash
            const hash = window.location.hash;
            const params = new URLSearchParams(hash.split('?')[1]);
            const id = params.get('id');

            if (id) {
                // document.querySelector('.page-title').textContent = '采购计划管理 - 编辑'; // This element does not exist in the template
                // In a real application, fetch data by ID and populate the form
                // For demo, we'll use dummy data
                const dummyData = {
                    planNumber: id,
                    procurementType: '货物',
                    biddingCategory: '货物类(材料/设备/供应及安装)',
                    procurementMethod: '公告比选',
                    budgetAmount: 150.00,
                    fundSource: '自有资金',
                    biddingTime: '2023年4季度',
                    procurementOrganizationMethod: '自主招标',
                    agency: ['代理机构A'], // Example for multi-select
                    annualProcurementPlan: 140.00,
                    projectHandler: '张三',
                    decisionDate: '2023-03-10',
                    projectType: '依法必须招标项目',
                    projectOwner: '集团本部',
                    projectBasicInfo: '这是一个测试项目基本情况描述。',
                    secondaryCompany: '某二级公司',
                    remarks: '这是备注信息。'
                };

                document.getElementById('planNumber').value = dummyData.planNumber;
                document.getElementById('procurementType').value = dummyData.procurementType;
                updateBiddingCategory(); // Update categories after setting procurementType
                document.getElementById('biddingCategory').value = dummyData.biddingCategory;
                document.getElementById('procurementMethod').value = dummyData.procurementMethod;
                document.getElementById('budgetAmount').value = dummyData.budgetAmount;
                document.getElementById('fundSource').value = dummyData.fundSource;
                document.getElementById('biddingTime').value = dummyData.biddingTime;
                document.getElementById('procurementOrganizationMethod').value = dummyData.procurementOrganizationMethod;
                toggleAgencyVisibility(); // Update agency visibility
                // For multi-select, iterate and set selected
                Array.from(document.getElementById('agency').options).forEach(option => {
                    option.selected = dummyData.agency.includes(option.value);
                });
                document.getElementById('annualProcurementPlan').value = dummyData.annualProcurementPlan;
                document.getElementById('projectHandler').value = dummyData.projectHandler;
                document.getElementById('decisionDate').value = dummyData.decisionDate;
                document.getElementById('projectType').value = dummyData.projectType;
                document.getElementById('projectOwner').value = dummyData.projectOwner;
                document.getElementById('projectBasicInfo').value = dummyData.projectBasicInfo;
                document.getElementById('secondaryCompany').value = dummyData.secondaryCompany;
                document.getElementById('remarks').value = dummyData.remarks;
            }
        });
    </script>
</body>

</html>