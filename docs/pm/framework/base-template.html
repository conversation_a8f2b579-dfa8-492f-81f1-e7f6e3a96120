<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FUNI Product Prototype</title>
    <link rel="stylesheet" href="assets/css/funi-framework.css">
    <link rel="stylesheet" href="assets/css/funi-components.css">
    <link rel="stylesheet" href="assets/css/funi-themes.css">
</head>

<body>
    <!-- Funi布局容器 -->
    <div class="funi-layout" id="app">
        <!-- 头部区域 -->
        <header class="funi-header">
            <div class="funi-header-left">
                <div class="funi-logo" onclick="window.location.hash = '';">
                    <img src="assets/FUNI.svg" alt="FUNI Logo">
                    <span class="funi-logo-text">FUNI Product Prototype</span>
                </div>
            </div>
            <div class="funi-header-right">
                <div class="funi-theme-switcher">
                    <button class="funi-theme-btn" onclick="toggleTheme()" title="切换主题">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path
                                d="M8 12a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM8 0a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 0zm0 13a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 13zm8-5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2a.5.5 0 0 1 .5.5zM3 8a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2A.5.5 0 0 1 3 8zm10.657-5.657a.5.5 0 0 1 0 .707l-1.414 1.415a.5.5 0 1 1-.707-.708l1.414-1.414a.5.5 0 0 1 .707 0zm-9.193 9.193a.5.5 0 0 1 0 .707L3.05 13.657a.5.5 0 0 1-.707-.707l1.414-1.414a.5.5 0 0 1 .707 0zm9.193 2.121a.5.5 0 0 1-.707 0l-1.414-1.414a.5.5 0 0 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .707zM4.464 4.465a.5.5 0 0 1-.707 0L2.343 3.05a.5.5 0 1 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .708z" />
                        </svg>
                    </button>
                </div>
                <div class="funi-user-menu">
                    <div class="funi-user-avatar">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTYiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxMiIgcj0iNCIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMTYgMThDMTIuNjg2MyAxOCAxMCAyMC42ODYzIDEwIDI0VjI2SDE2SDIyVjI0QzIyIDIwLjY4NjMgMTkuMzEzNyAxOCAxNiAxOFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+"
                            alt="用户头像">
                        <span class="funi-user-name">管理员</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主体区域 -->
        <div class="funi-main">
            <!-- 侧边栏 -->
            <aside class="funi-sidebar" id="sidebar">
                <button class="funi-sidebar-toggle" onclick="toggleSidebar()" title="折叠/展开侧边栏">
                    <svg width="12" height="12" viewBox="0 0 16 16" fill="currentColor">
                        <path
                            d="M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z" />
                    </svg>
                </button>
                <nav class="funi-menu">
                    <div class="funi-menu-group" data-group-id="system-management">
                        <div class="funi-menu-group-title">
                            <span>系统管理</span>
                            <svg class="funi-menu-group-toggle" width="16" height="16" viewBox="0 0 16 16"
                                fill="currentColor">
                                <path
                                    d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z" />
                            </svg>
                        </div>
                        <ul class="funi-menu-list">
                            <li class="funi-menu-item funi-menu-item-active">
                                <a href="#" class="funi-menu-link">
                                    <svg class="funi-menu-icon" width="16" height="16" viewBox="0 0 16 16"
                                        fill="currentColor">
                                        <path
                                            d="M7 14s-1 0-1-1 1-4 5-4 5 3 5 4-1 1-1 1H7zm4-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z" />
                                    </svg>
                                    <span class="funi-menu-text">用户管理</span>
                                </a>
                            </li>
                            <li class="funi-menu-item">
                                <a href="#" class="funi-menu-link">
                                    <svg class="funi-menu-icon" width="16" height="16" viewBox="0 0 16 16"
                                        fill="currentColor">
                                        <path
                                            d="M1 2.828c.885-.37 2.154-.769 3.388-.893 1.33-.134 2.458.063 3.112.752v9.746c-.935-.53-2.12-.603-3.213-.493-1.18.12-2.37.461-3.287.811V2.828zm7.5-.141c.654-.689 1.782-.886 3.112-.752 1.234.124 2.503.523 3.388.893v9.923c-.918-.35-2.107-.692-3.287-.81-1.094-.111-2.278-.039-3.213.492V2.687zM8 1.783C7.015.936 5.587.81 4.287.94c-1.514.153-3.042.672-3.994 1.105A.5.5 0 0 0 0 2.5v11a.5.5 0 0 0 .707.455c.882-.4 2.303-.881 3.68-1.02 1.409-.142 2.59.087 3.223.877a.5.5 0 0 0 .78 0c.633-.79 1.814-1.019 3.222-.877 1.378.139 2.8.62 3.681 1.02A.5.5 0 0 0 16 13.5v-11a.5.5 0 0 0-.293-.455c-.952-.433-2.48-.952-3.994-1.105C10.413.809 8.985.936 8 1.783z" />
                                    </svg>
                                    <span class="funi-menu-text">角色管理</span>
                                </a>
                            </li>
                            <li class="funi-menu-item">
                                <a href="#" class="funi-menu-link">
                                    <svg class="funi-menu-icon" width="16" height="16" viewBox="0 0 16 16"
                                        fill="currentColor">
                                        <path
                                            d="M9.405 1.05c-.413-1.4-2.397-1.4-2.81 0l-.1.34a1.464 1.464 0 0 1-2.105.872l-.31-.17c-1.283-.698-2.686.705-1.987 1.987l.169.311c.446.82.023 1.841-.872 2.105l-.34.1c-1.4.413-1.4 2.397 0 2.81l.34.1a1.464 1.464 0 0 1 .872 2.105l-.17.31c-.698 1.283.705 2.686 1.987 1.987l.311-.169a1.464 1.464 0 0 1 2.105.872l.1.34c.413 1.4 2.397 1.4 2.81 0l.1-.34a1.464 1.464 0 0 1 2.105-.872l.31.17c1.283.698 2.686-.705 1.987-1.987l-.169-.311a1.464 1.464 0 0 1 .872-2.105l.34-.1c1.4-.413 1.4-2.397 0-2.81l-.34-.1a1.464 1.464 0 0 1-.872-2.105l.17-.31c.698-1.283-.705-2.686-1.987-1.987l-.311.169a1.464 1.464 0 0 1-2.105-.872l-.1-.34zM8 10.93a2.929 2.929 0 1 1 0-5.86 2.929 2.929 0 0 1 0 5.858z" />
                                    </svg>
                                    <span class="funi-menu-text">系统设置</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </nav>
            </aside>

            <!-- 内容区域 -->
            <main class="funi-content">
                <div class="funi-content-wrapper">
                    <!-- 页面具体内容将在这里插入 -->
                    <iframe id="funi-content-iframe" frameborder="0" style="width: 100%; height: 100%;"></iframe>
                </div>
            </main>
        </div>
    </div>

    <script src="https://code.iconify.design/iconify-icon/3.0.0/iconify-icon.min.js"></script>
    <script src="assets/js/funi-theme-switcher.js"></script>
    <script src="assets/js/funi-interactions.js"></script>
    <script src="assets/js/funi-router.js"></script>
</body>

</html>