<!DOCTYPE html>
<html lang="zh-CN" style="height: 100%;">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FUNI Product Prototype</title>
    <link rel="stylesheet" href="assets/css/funi-framework.css">
    <link rel="stylesheet" href="assets/css/funi-components.css">
    <link rel="stylesheet" href="assets/css/funi-themes.css">
</head>

<body style="height: 100%; margin: 0; padding: 0;">
    <!-- Funi布局容器 -->
    <div class="funi-layout" id="app">
        <!-- 头部区域 -->
        <header class="funi-header">
            <div class="funi-header-left">
                <div class="funi-logo" onclick="window.location.hash = '';">
                    <img src="assets/FUNI.svg" alt="FUNI Logo">
                    <span class="funi-logo-text">FUNI Product Prototype</span>
                </div>
            </div>
            <div class="funi-header-right">
                <div class="funi-theme-switcher">
                    <button class="funi-theme-btn" onclick="toggleTheme()" title="切换主题">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                            <path
                                d="M8 12a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM8 0a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 0zm0 13a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-1 0v-2A.5.5 0 0 1 8 13zm8-5a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2a.5.5 0 0 1 .5.5zM3 8a.5.5 0 0 1-.5.5h-2a.5.5 0 0 1 0-1h2A.5.5 0 0 1 3 8zm10.657-5.657a.5.5 0 0 1 0 .707l-1.414 1.415a.5.5 0 1 1-.707-.708l1.414-1.414a.5.5 0 0 1 .707 0zm-9.193 9.193a.5.5 0 0 1 0 .707L3.05 13.657a.5.5 0 0 1-.707-.707l1.414-1.414a.5.5 0 0 1 .707 0zm9.193 2.121a.5.5 0 0 1-.707 0l-1.414-1.414a.5.5 0 0 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .707zM4.464 4.465a.5.5 0 0 1-.707 0L2.343 3.05a.5.5 0 1 1 .707-.707l1.414 1.414a.5.5 0 0 1 0 .708z" />
                        </svg>
                    </button>
                </div>
                <div class="funi-user-menu">
                    <div class="funi-user-avatar">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTYiIGZpbGw9IiNGNUY1RjUiLz4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxMiIgcj0iNCIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMTYgMThDMTIuNjg2MyAxOCAxMCAyMC42ODYzIDEwIDI0VjI2SDE2SDIyVjI0QzIyIDIwLjY4NjMgMTkuMzEzNyAxOCAxNiAxOFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+"
                            alt="用户头像">
                        <span class="funi-user-name">管理员</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主体区域 -->
        <div class="funi-main">
            <!-- 侧边栏 -->
            <aside class="funi-sidebar" id="sidebar">
                <button class="funi-sidebar-toggle" onclick="toggleSidebar()" title="折叠/展开侧边栏">
                    <svg width="12" height="12" viewBox="0 0 16 16" fill="currentColor">
                        <path
                            d="M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z" />
                    </svg>
                </button>
                <nav class="funi-menu">
                    <ul class="funi-menu-list">
                        <li class="funi-menu-item funi-menu-item-active">
                            <a href="#/dashboard" class="funi-menu-link">
                                <iconify-icon icon="mdi:view-dashboard" class="funi-menu-icon"></iconify-icon>
                                <span class="funi-menu-text">工作台</span>
                            </a>
                        </li>
                        <li class="funi-menu-item">
                            <a href="#/procurement-plan-management" class="funi-menu-link">
                                <iconify-icon icon="mdi:file-document-outline" class="funi-menu-icon"></iconify-icon>
                                <span class="funi-menu-text">采购计划管理</span>
                            </a>
                        </li>
                    </ul>
                    <div class="funi-menu-group" data-group-id="procurement-execution-management">
                        <div class="funi-menu-group-title">
                            <div style="display: flex; align-items: center;">
                                <iconify-icon icon="mdi:folder-open" class="funi-menu-icon"></iconify-icon>
                                <span>采购执行管理</span>
                            </div>
                            <svg class="funi-menu-group-toggle" width="16" height="16" viewBox="0 0 16 16"
                                fill="currentColor">
                                <path
                                    d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z" />
                            </svg>
                        </div>
                        <ul class="funi-menu-list">
                            <li class="funi-menu-item">
                                <a href="#/procurement-execution/project-bid-management" class="funi-menu-link">
                                    <span class="funi-menu-text">项目标段管理</span>
                                </a>
                            </li>
                            <li class="funi-menu-item">
                                <a href="#/procurement-execution/announcement-management" class="funi-menu-link">
                                    <span class="funi-menu-text">公告管理</span>
                                </a>
                            </li>
                            <li class="funi-menu-item">
                                <a href="#/procurement-execution/amendment-clarification-response-management"
                                    class="funi-menu-link">
                                    <span class="funi-menu-text">补遗/澄清/答疑管理</span>
                                </a>
                            </li>
                            <li class="funi-menu-item">
                                <a href="#/procurement-execution/evaluation-result-publicity-management"
                                    class="funi-menu-link">
                                    <span class="funi-menu-text">评标结果公示管理</span>
                                </a>
                            </li>
                            <li class="funi-menu-item">
                                <a href="#/procurement-execution/winning-bid-result-publicity-management"
                                    class="funi-menu-link">
                                    <span class="funi-menu-text">中标结果公示管理</span>
                                </a>
                            </li>
                            <li class="funi-menu-item">
                                <a href="#/procurement-execution/contract-fulfillment-management"
                                    class="funi-menu-link">
                                    <span class="funi-menu-text">签约履行管理</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                    <ul class="funi-menu-list">
                        <li class="funi-menu-item">
                            <a href="#/project-warning-management" class="funi-menu-link">
                                <iconify-icon icon="mdi:alert-outline" class="funi-menu-icon"></iconify-icon>
                                <span class="funi-menu-text">项目预警管理</span>
                            </a>
                        </li>
                    </ul>
                    <div class="funi-menu-group" data-group-id="agency-management">
                        <div class="funi-menu-group-title">
                            <div style="display: flex; align-items: center;">
                                <iconify-icon icon="mdi:account-group-outline" class="funi-menu-icon"></iconify-icon>
                                <span>代理机构管理</span>
                            </div>
                            <svg class="funi-menu-group-toggle" width="16" height="16" viewBox="0 0 16 16"
                                fill="currentColor">
                                <path
                                    d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z" />
                            </svg>
                        </div>
                        <ul class="funi-menu-list">
                            <li class="funi-menu-item">
                                <a href="#/agency-management/agency-list" class="funi-menu-link">
                                    <span class="funi-menu-text">代理机构管理</span>
                                </a>
                            </li>
                            <li class="funi-menu-item">
                                <a href="#/agency-management/personnel-management" class="funi-menu-link">
                                    <span class="funi-menu-text">机构人员管理</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                    <ul class="funi-menu-list">
                        <li class="funi-menu-item">
                            <a href="#/supplier-management" class="funi-menu-link">
                                <iconify-icon icon="mdi:truck-outline" class="funi-menu-icon"></iconify-icon>
                                <span class="funi-menu-text">供应商管理</span>
                            </a>
                        </li>
                        <li class="funi-menu-item">
                            <a href="#/notification-announcement-management" class="funi-menu-link">
                                <iconify-icon icon="mdi:bullhorn-outline" class="funi-menu-icon"></iconify-icon>
                                <span class="funi-menu-text">通知公告管理</span>
                            </a>
                        </li>
                        <li class="funi-menu-item">
                            <a href="#/policy-regulation-management" class="funi-menu-link">
                                <iconify-icon icon="mdi:book-open-outline" class="funi-menu-icon"></iconify-icon>
                                <span class="funi-menu-text">政策法规管理</span>
                            </a>
                        </li>
                    </ul>
                    <div class="funi-menu-group" data-group-id="data-statistics-analysis">
                        <div class="funi-menu-group-title">
                            <div style="display: flex; align-items: center;">
                                <iconify-icon icon="mdi:chart-bar" class="funi-menu-icon"></iconify-icon>
                                <span>数据统计分析</span>
                            </div>
                            <svg class="funi-menu-group-toggle" width="16" height="16" viewBox="0 0 16 16"
                                fill="currentColor">
                                <path
                                    d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z" />
                            </svg>
                        </div>
                        <ul class="funi-menu-list">
                            <li class="funi-menu-item">
                                <a href="#/data-statistics-analysis/statistical-reports" class="funi-menu-link">
                                    <span class="funi-menu-text">统计报表</span>
                                </a>
                            </li>
                            <li class="funi-menu-item">
                                <a href="#/data-statistics-analysis/data-analysis" class="funi-menu-link">
                                    <span class="funi-menu-text">数据分析</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="funi-menu-group" data-group-id="help-center">
                        <div class="funi-menu-group-title">
                            <div style="display: flex; align-items: center;">
                                <iconify-icon icon="mdi:help-circle-outline" class="funi-menu-icon"></iconify-icon>
                                <span>帮助中心</span>
                            </div>
                            <svg class="funi-menu-group-toggle" width="16" height="16" viewBox="0 0 16 16"
                                fill="currentColor">
                                <path
                                    d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z" />
                            </svg>
                        </div>
                        <ul class="funi-menu-list">
                            <li class="funi-menu-item">
                                <a href="#/help-center/main" class="funi-menu-link">
                                    <span class="funi-menu-text">帮助中心</span>
                                </a>
                            </li>
                            <li class="funi-menu-item">
                                <a href="#/help-center/complaint-management" class="funi-menu-link">
                                    <span class="funi-menu-text">投诉管理</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="funi-menu-group" data-group-id="system-settings">
                        <div class="funi-menu-group-title">
                            <div style="display: flex; align-items: center;">
                                <iconify-icon icon="mdi:cog-outline" class="funi-menu-icon"></iconify-icon>
                                <span>系统设置</span>
                            </div>
                            <svg class="funi-menu-group-toggle" width="16" height="16" viewBox="0 0 16 16"
                                fill="currentColor">
                                <path
                                    d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z" />
                            </svg>
                        </div>
                        <ul class="funi-menu-list">
                            <li class="funi-menu-item">
                                <a href="#/system-settings/warning-configuration" class="funi-menu-link">
                                    <span class="funi-menu-text">预警配置</span>
                                </a>
                            </li>
                            <li class="funi-menu-item">
                                <a href="#/system-settings/user-management" class="funi-menu-link">
                                    <span class="funi-menu-text">用户管理</span>
                                </a>
                            </li>
                            <li class="funi-menu-item">
                                <a href="#/system-settings/role-management" class="funi-menu-link">
                                    <span class="funi-menu-text">角色管理</span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </nav>
            </aside>

            <!-- 内容区域 -->
            <main class="funi-content">
                <div class="funi-content-wrapper" style="position: relative;">
                    <!-- 页面具体内容将在这里插入 -->
                    <iframe id="funi-content-iframe" frameborder="0" style="width: 100%; height: 100%;"></iframe>
                </div>
            </main>
        </div>
    </div>

    <script src="https://code.iconify.design/iconify-icon/3.0.0/iconify-icon.min.js"></script>
    <script src="assets/js/funi-theme-switcher.js"></script>
    <script src="assets/js/funi-interactions.js"></script>
    <script src="assets/js/funi-router.js"></script>
</body>

</html>