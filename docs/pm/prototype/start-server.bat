@echo off
chcp 65001 >nul
title Funi原型系统服务器

echo ============================================================
echo 🚀 启动 Funi原型系统服务器
echo ============================================================
echo.

cd /d "%~dp0"

echo 📁 当前目录: %CD%
echo 🔍 检查Python环境...

python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Python，尝试使用python3...
    python3 --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ 未找到Python或python3
        echo.
        echo 💡 请安装Python后重试:
        echo    https://www.python.org/downloads/
        echo.
        pause
        exit /b 1
    ) else (
        set PYTHON_CMD=python3
    )
) else (
    set PYTHON_CMD=python
)

echo ✅ Python环境检查通过
echo.
echo 🚀 启动HTTP服务器...
echo ============================================================
echo 📍 服务器地址: http://localhost:8080
echo 💡 浏览器将自动打开，如未打开请手动访问上述地址
echo 🛑 按 Ctrl+C 停止服务器
echo ============================================================
echo.

%PYTHON_CMD% start-server.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ 服务器启动失败
    echo 💡 尝试使用内置HTTP服务器...
    echo.
    %PYTHON_CMD% -m http.server 8080
)

pause
