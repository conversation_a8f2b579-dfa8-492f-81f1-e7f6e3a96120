<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购计划管理 - 详情/审核</title>
    <link rel="stylesheet" href="../../assets/css/funi-framework.css" />
    <link rel="stylesheet" href="../../assets/css/funi-components.css" />
    <link rel="stylesheet" href="../../assets/css/funi-themes.css" />
    <link rel="stylesheet" href="../../assets/css/funi-form.css" />
    <link rel="stylesheet" href="../../assets/css/detail.css" />
</head>

<body>

    <div id="app" class="form-container">
        <div class="detail-header">
            <div class="header-left">
                <div class="header-title">采购计划管理 - 详情</div>
            </div>
            <div class="header-right">
                <div class="approval-info">
                    <span>计划编号: <span id="displayPlanNumber"></span></span>
                    <span>审核状态: <span id="displayAuditStatus"></span></span>
                </div>
            </div>
        </div>

        <div class="funi-tabs">
            <div class="tab-item active" data-tab="basic-info">基本信息</div>
            <div class="tab-item" data-tab="operation-log">流程记录</div>
        </div>

        <div class="container-content">
            <div class="form-step-content form-step-1 active" data-tab-content="basic-info">
                <div class="form-section-title">招标信息</div>
                <div class="funi-form">
                    <form class="form-grid">
                        <div class="form-item-row">
                            <label for="planNumber" class="form-item-label">计划编号:</label>
                            <div class="form-item-value">
                                <div id="planNumber" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="planProjectName" class="form-item-label">计划项目名称:</label>
                            <div class="form-item-value">
                                <div id="planProjectName" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="auditStatus" class="form-item-label">审核状态:</label>
                            <div class="form-item-value">
                                <div id="auditStatus" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementType" class="form-item-label">采购类型:</label>
                            <div class="form-item-value">
                                <div id="procurementType" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementMethod" class="form-item-label">采购方式:</label>
                            <div class="form-item-value">
                                <div id="procurementMethod" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="biddingCategory" class="form-item-label">招标类别:</label>
                            <div class="form-item-value">
                                <div id="biddingCategory" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="budgetAmount" class="form-item-label">采购预算金额（万元）:</label>
                            <div class="form-item-value">
                                <div id="budgetAmount" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="fundSource" class="form-item-label">资金来源:</label>
                            <div class="form-item-value">
                                <div id="fundSource" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="biddingTime" class="form-item-label">招标时间:</label>
                            <div class="form-item-value">
                                <div id="biddingTime" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="procurementOrganizationMethod" class="form-item-label">采购组织方式:</label>
                            <div class="form-item-value">
                                <div id="procurementOrganizationMethod" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="agency" class="form-item-label">代理机构:</label>
                            <div class="form-item-value">
                                <div id="agency" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="annualProcurementPlan" class="form-item-label">年采购计划（万元）:</label>
                            <div class="form-item-value">
                                <div id="annualProcurementPlan" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectHandler" class="form-item-label">项目经办人:</label>
                            <div class="form-item-value">
                                <div id="projectHandler" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="decisionDate" class="form-item-label">立项决策日期:</label>
                            <div class="form-item-value">
                                <div id="decisionDate" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="rejectionReason" class="form-item-label">驳回原因:</label>
                            <div class="form-item-value">
                                <div id="rejectionReason" class="display-value"></div>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="form-section-title">项目信息</div>
                <div class="funi-form">
                    <form class="form-grid">
                        <div class="form-item-row">
                            <label for="projectType" class="form-item-label">项目类型:</label>
                            <div class="form-item-value">
                                <div id="projectType" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="projectOwner" class="form-item-label">项目业主:</label>
                            <div class="form-item-value">
                                <div id="projectOwner" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row form-item-full-width">
                            <label for="projectBasicInfo" class="form-item-label">项目基本情况:</label>
                            <div class="form-item-value">
                                <div id="projectBasicInfo" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="secondaryCompany" class="form-item-label">所属二级公司单位:</label>
                            <div class="form-item-value">
                                <div id="secondaryCompany" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row">
                            <label for="remarks" class="form-item-label">备注:</label>
                            <div class="form-item-value">
                                <div id="remarks" class="display-value"></div>
                            </div>
                        </div>
                        <div class="form-item-row form-item-full-width">
                            <label for="decisionFiles" class="form-item-label">立项决策文件:</label>
                            <div class="form-item-value">
                                <div id="decisionFiles" class="display-value"></div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="form-step-content" data-tab-content="operation-log">
                <div class="form-section-title">流程记录</div>
                <div class="timeline-container">
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">提交审核</div>
                            <div class="timeline-meta">
                                <span>操作人: 张三</span>
                                <span>操作时间: 2024-08-01 10:00:00</span>
                            </div>
                            <div class="timeline-description">计划已提交至审批流程。</div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">部门初审</div>
                            <div class="timeline-meta">
                                <span>操作人: 李四</span>
                                <span>操作时间: 2024-08-02 11:30:00</span>
                            </div>
                            <div class="timeline-description">初审通过，转交至财务部。</div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">财务部审核</div>
                            <div class="timeline-meta">
                                <span>操作人: 王五</span>
                                <span>操作时间: 2024-08-03 14:00:00</span>
                            </div>
                            <div class="timeline-description">财务审核通过，预算充足。</div>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">最终审批</div>
                            <div class="timeline-meta">
                                <span>操作人: 赵六</span>
                                <span>操作时间: 2024-08-04 09:00:00</span>
                            </div>
                            <div class="timeline-description">审批通过，计划正式生效。</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.iconify.design/iconify-icon/3.0.0/iconify-icon.min.js"></script>
    <script src="../../assets/js/funi-theme-switcher.js"></script>
    <script src="../../assets/js/funi-interactions.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Tab switching logic
            const tabItems = document.querySelectorAll('.funi-tabs .tab-item');
            const tabContents = document.querySelectorAll('.container-content .form-step-content');

            tabItems.forEach(item => {
                item.addEventListener('click', () => {
                    // Remove active class from all tab items and contents
                    tabItems.forEach(tab => tab.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // Add active class to the clicked tab item
                    item.classList.add('active');

                    // Show the corresponding tab content
                    const targetTab = item.dataset.tab;
                    const targetContent = document.querySelector(`.container-content [data-tab-content="${targetTab}"]`);
                    if (targetContent) {
                        targetContent.classList.add('active');
                    }
                });
            });

            // Populate form for editing if ID is present in URL hash
            const urlParams = new URLSearchParams(window.location.search);

            const id = urlParams.get('id');

            if (id) {
                // In a real application, fetch data by ID and populate the form
                // For demo, we'll use dummy data
                const dummyData = {
                    planNumber: id,
                    planProjectName: '某公司2024年度办公用品采购计划',
                    auditStatus: '待审核',
                    procurementType: '货物',
                    procurementMethod: '公告比选',
                    biddingCategory: '货物类(材料/设备/供应及安装)',
                    budgetAmount: 150.00,
                    fundSource: '自有资金',
                    biddingTime: '2024年第三季度',
                    procurementOrganizationMethod: '自主招标',
                    agency: ['无'],
                    annualProcurementPlan: 140.00,
                    projectHandler: '张三',
                    decisionDate: '2024-08-01',
                    rejectionReason: '无',
                    projectType: '依法必须招标项目',
                    projectOwner: '行政部',
                    projectBasicInfo: '本计划旨在采购公司日常运营所需的各类办公用品，以确保各部门工作的顺利进行。采购范围包括但不限于文具、纸张、打印耗材、小型办公设备等。',
                    secondaryCompany: '集团本部',
                    remarks: '请优先考虑环保型产品。',
                    decisionFiles: '立项决策文件.pdf, 附件说明.docx'
                };

                // Update header info
                document.getElementById('displayPlanNumber').textContent = dummyData.planNumber;
                document.getElementById('displayAuditStatus').textContent = dummyData.auditStatus;
                document.querySelector('.header-title').textContent = `采购计划管理 - ${dummyData.planProjectName}`;


                // Display values as plain text
                document.getElementById('planNumber').textContent = dummyData.planNumber;
                document.getElementById('planProjectName').textContent = dummyData.planProjectName;
                document.getElementById('auditStatus').textContent = dummyData.auditStatus;
                document.getElementById('procurementType').textContent = dummyData.procurementType;
                document.getElementById('procurementMethod').textContent = dummyData.procurementMethod;
                document.getElementById('biddingCategory').textContent = dummyData.biddingCategory;
                document.getElementById('budgetAmount').textContent = dummyData.budgetAmount;
                document.getElementById('fundSource').textContent = dummyData.fundSource;
                document.getElementById('biddingTime').textContent = dummyData.biddingTime;
                document.getElementById('procurementOrganizationMethod').textContent = dummyData.procurementOrganizationMethod;
                document.getElementById('agency').textContent = dummyData.agency.join(', '); // For multi-select
                document.getElementById('annualProcurementPlan').textContent = dummyData.annualProcurementPlan;
                document.getElementById('projectHandler').textContent = dummyData.projectHandler;
                document.getElementById('decisionDate').textContent = dummyData.decisionDate;
                document.getElementById('rejectionReason').textContent = dummyData.rejectionReason;
                document.getElementById('projectType').textContent = dummyData.projectType;
                document.getElementById('projectOwner').textContent = dummyData.projectOwner;
                document.getElementById('projectBasicInfo').textContent = dummyData.projectBasicInfo;
                document.getElementById('secondaryCompany').textContent = dummyData.secondaryCompany;
                document.getElementById('remarks').textContent = dummyData.remarks;
                document.getElementById('decisionFiles').textContent = dummyData.decisionFiles;
            }


            const reviewParam = urlParams.get('review');

            if (reviewParam === '1') {
                const appDiv = document.getElementById('app');
                const formActionsDiv = document.createElement('div');
                formActionsDiv.className = 'form-actions';
                formActionsDiv.innerHTML = `
                <button class="button primary" id="approveButton">通过</button>
                <button class="button" id="rejectButton">驳回</button>
            `;
                appDiv.appendChild(formActionsDiv);

                document.getElementById('approveButton').addEventListener('click', () => {
                    alert('审批通过！');
                    parent.window.location.hash = '#/procurement-plan-management'; // Go back to list page
                });

                document.getElementById('rejectButton').addEventListener('click', () => {
                    const reason = prompt('请输入驳回原因:');
                    if (reason) {
                        alert(`审批驳回，原因: ${reason}`);
                        parent.window.location.hash = '#/procurement-plan-management'; // Go back to list page
                    } else {
                        alert('驳回操作已取消。');
                    }
                });
            }
        });


    </script>
</body>

</html>