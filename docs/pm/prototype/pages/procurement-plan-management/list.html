<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购计划管理 - 列表</title>
    <link rel="stylesheet" href="../../assets/css/funi-framework.css" />
    <link rel="stylesheet" href="../../assets/css/funi-components.css" />
    <link rel="stylesheet" href="../../assets/css/funi-themes.css" />
    <link rel="stylesheet" href="../../assets/css/funi-list.css" />
</head>

<body>
    <div id="app" class="container">
        <div class="container-header">
            <!-- 1. 头部Tab切换 -->
            <div class="tabs">
                <div class="tab-item active" data-tab="all">全部</div>
                <div class="tab-item" data-tab="pending">待办</div>
                <div class="tab-item" data-tab="done">已办</div>
            </div>

            <!-- 2. 搜索区域 -->
            <div class="search-area collapsed">
                <form class="search-form">
                    <div class="search-form-item">
                        <label for="planProjectName">计划项目名称:</label>
                        <input type="text" id="planProjectName" name="planProjectName" placeholder="请输入">
                    </div>
                    <div class="search-form-item">
                        <label for="auditStatus">审核状态:</label>
                        <select id="auditStatus" name="auditStatus">
                            <option value="">请选择</option>
                            <option value="待审核">待审核</option>
                            <option value="审核中">审核中</option>
                            <option value="审核通过">审核通过</option>
                            <option value="审核未过">审核未过</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="procurementType">采购类型:</label>
                        <select id="procurementType" name="procurementType">
                            <option value="">请选择</option>
                            <option value="货物">货物</option>
                            <option value="施工">施工</option>
                            <option value="服务">服务</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="procurementMethod">采购方式:</label>
                        <select id="procurementMethod" name="procurementMethod">
                            <option value="">请选择</option>
                            <option value="公告比选">公告比选</option>
                            <option value="邀请比选">邀请比选</option>
                            <option value="竞争性磋商">竞争性磋商</option>
                            <option value="竞争性谈判">竞争性谈判</option>
                            <option value="询价择优">询价择优</option>
                            <option value="单一来源">单一来源</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="procurementOrganizationMethod">采购组织方式:</label>
                        <select id="procurementOrganizationMethod" name="procurementOrganizationMethod">
                            <option value="">请选择</option>
                            <option value="自主招标">自主招标</option>
                            <option value="委托招标">委托招标</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="fundSource">资金来源:</label>
                        <select id="fundSource" name="fundSource">
                            <option value="">请选择</option>
                            <option value="自有资金">自有资金</option>
                            <option value="政府资本">政府资本</option>
                            <option value="其他社会资本">其他社会资本</option>
                        </select>
                    </div>
                    <div class="search-form-item">
                        <label for="procurementBudgetAmountMin">采购预算金额(万元):</label>
                        <div class="range-input">
                            <input type="number" id="procurementBudgetAmountMin" name="procurementBudgetAmountMin"
                                placeholder="最小">
                            <span>~</span>
                            <input type="number" id="procurementBudgetAmountMax" name="procurementBudgetAmountMax"
                                placeholder="最大">
                        </div>
                    </div>
                    <div class="search-form-item">
                        <label for="projectHandler">项目经办人:</label>
                        <input type="text" id="projectHandler" name="projectHandler" placeholder="请输入">
                    </div>
                    <div class="search-form-item full-width">
                        <label for="decisionDateStart">立项决策日期:</label>
                        <div class="date-range-picker">
                            <input type="date" id="decisionDateStart" name="decisionDateStart">
                            <span>~</span>
                            <input type="date" id="decisionDateEnd" name="decisionDateEnd">
                        </div>
                    </div>
                    <div class="search-form-item full-width">
                        <label for="createTimeStart">创建时间:</label>
                        <div class="date-range-picker">
                            <input type="date" id="createTimeStart" name="createTimeStart">
                            <span>~</span>
                            <input type="date" id="createTimeEnd" name="createTimeEnd">
                        </div>
                    </div>
                    <div class="search-form-item search-buttons-item">
                        <button type="button" class="button primary" id="queryButton">查询</button>
                        <button type="button" class="button" id="resetButton">重置</button>
                        <button type="button" class="button text" id="toggleCollapseButton">高级查询</button>
                    </div>
                </form>
            </div>
        </div>
        <div class="container-table">
            <!-- 操作按钮区域 -->
            <div class="action-buttons">
                <button class="button primary"
                    onclick="parent.window.location.hash = '#/procurement-plan-management/add-edit'">新建</button>
            </div>
            <!-- 3. 列表区域 -->
            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>计划编号</th>
                            <th>计划项目名称</th>
                            <th>采购类型</th>
                            <th>采购方式</th>
                            <th>审核状态</th>
                            <th>采购组织方式</th>
                            <th>代理机构</th>
                            <th>项目经办人</th>
                            <th>项目业主</th>
                            <th>招标时间</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <!-- Table rows will be inserted here by JavaScript -->
                    </tbody>
                </table>
            </div>

            <!-- 分页组件 -->
            <div class="pagination-container">
                <span>总共 <span id="totalItems">0</span> 条</span>
                <select id="pageSizeSelect">
                    <option value="10">10 条/页</option>
                    <option value="20">20 条/页</option>
                    <option value="50">50 条/页</option>
                    <option value="100">100 条/页</option>
                </select>
                <div class="page-buttons">
                    <button id="prevPageButton" disabled>上一页</button>
                    <span id="currentPageSpan">1</span>
                    <button id="nextPageButton">下一页</button>
                </div>
            </div>
        </div>
    </div>
    <script src="https://code.iconify.design/iconify-icon/3.0.0/iconify-icon.min.js"></script>
    <script src="../../assets/js/funi-theme-switcher.js"></script>
    <script src="../../assets/js/funi-interactions.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const tabs = document.querySelectorAll('.tab-item');
            const searchForm = document.querySelector('.search-form');
            const queryButton = document.getElementById('queryButton');
            const resetButton = document.getElementById('resetButton');
            const toggleCollapseButton = document.getElementById('toggleCollapseButton');
            const tableBody = document.getElementById('tableBody');
            const pageSizeSelect = document.getElementById('pageSizeSelect');
            const prevPageButton = document.getElementById('prevPageButton');
            const nextPageButton = document.getElementById('nextPageButton');
            const currentPageSpan = document.getElementById('currentPageSpan');
            const totalItemsSpan = document.getElementById('totalItems');

            let activeTab = 'all';
            let isCollapsed = true;
            let currentPage = 1;
            let pageSize = parseInt(pageSizeSelect.value);

            const searchArea = document.querySelector('.search-area');
            if (isCollapsed) {
                searchArea.classList.add('collapsed');
                toggleCollapseButton.textContent = '高级查询';
            } else {
                searchArea.classList.remove('collapsed');
                toggleCollapseButton.textContent = '收起';
            }

            const allTableData = [
                {
                    planNumber: '***********-0001',
                    planProjectName: '某项目采购计划一',
                    procurementType: '货物',
                    procurementMethod: '公告比选',
                    auditStatus: '审核通过',
                    procurementOrganizationMethod: '自主招标',
                    agency: '无',
                    projectHandler: '张三',
                    projectOwner: '集团本部',
                    biddingTime: '2024年3季度',
                    createTime: '2024-08-01',
                },
                {
                    planNumber: '***********-0002',
                    planProjectName: '某项目采购计划二',
                    procurementType: '施工',
                    procurementMethod: '邀请比选',
                    auditStatus: '待审核',
                    procurementOrganizationMethod: '委托招标',
                    agency: '代理机构A',
                    projectHandler: '李四',
                    projectOwner: '分公司A',
                    biddingTime: '2024年4季度',
                    createTime: '2024-08-01',
                },
                {
                    planNumber: '***********-0003',
                    planProjectName: '某项目采购计划三',
                    procurementType: '服务',
                    procurementMethod: '竞争性磋商',
                    auditStatus: '审核中',
                    procurementOrganizationMethod: '自主招标',
                    agency: '无',
                    projectHandler: '王五',
                    projectOwner: '集团本部',
                    biddingTime: '2025年1季度',
                    createTime: '2024-08-02',
                },
                {
                    planNumber: '***********-0004',
                    planProjectName: '某项目采购计划四',
                    procurementType: '其他',
                    procurementMethod: '单一来源',
                    auditStatus: '审核未过',
                    procurementOrganizationMethod: '委托招标',
                    agency: '代理机构B',
                    projectHandler: '赵六',
                    projectOwner: '分公司B',
                    biddingTime: '2025年2季度',
                    createTime: '2024-08-02',
                },
                {
                    planNumber: '***********-0005',
                    planProjectName: '某项目采购计划五',
                    procurementType: '货物',
                    procurementMethod: '询价择优',
                    auditStatus: '审核通过',
                    procurementOrganizationMethod: '自主招标',
                    agency: '无',
                    projectHandler: '钱七',
                    projectOwner: '集团本部',
                    biddingTime: '2024年3季度',
                    createTime: '2024-08-03',
                },
                {
                    planNumber: '***********-0006',
                    planProjectName: '某项目采购计划六',
                    procurementType: '施工',
                    procurementMethod: '公告比选',
                    auditStatus: '待审核',
                    procurementOrganizationMethod: '委托招标',
                    agency: '代理机构C',
                    projectHandler: '孙八',
                    projectOwner: '分公司C',
                    biddingTime: '2024年4季度',
                    createTime: '2024-08-03',
                },
                {
                    planNumber: '***********-0007',
                    planProjectName: '某项目采购计划七',
                    procurementType: '服务',
                    procurementMethod: '竞争性谈判',
                    auditStatus: '审核中',
                    procurementOrganizationMethod: '自主招标',
                    agency: '无',
                    projectHandler: '周九',
                    projectOwner: '集团本部',
                    biddingTime: '2025年1季度',
                    createTime: '2024-08-04',
                },
                {
                    planNumber: '***********-0008',
                    planProjectName: '某项目采购计划八',
                    procurementType: '其他',
                    procurementMethod: '竞争性磋商',
                    auditStatus: '审核未过',
                    procurementOrganizationMethod: '委托招标',
                    agency: '代理机构D',
                    projectHandler: '吴十',
                    projectOwner: '分公司D',
                    biddingTime: '2025年2季度',
                    createTime: '2024-08-04',
                },
                {
                    planNumber: '***********-0009',
                    planProjectName: '某项目采购计划九',
                    procurementType: '货物',
                    procurementMethod: '邀请比选',
                    auditStatus: '审核通过',
                    procurementOrganizationMethod: '自主招标',
                    agency: '无',
                    projectHandler: '郑十一',
                    projectOwner: '集团本部',
                    biddingTime: '2024年3季度',
                    createTime: '2024-08-05',
                },
                {
                    planNumber: '***********-0010',
                    planProjectName: '某项目采购计划十',
                    procurementType: '施工',
                    procurementMethod: '公告比选',
                    auditStatus: '待审核',
                    procurementOrganizationMethod: '委托招标',
                    agency: '代理机构E',
                    projectHandler: '冯十二',
                    projectOwner: '分公司E',
                    biddingTime: '2024年4季度',
                    createTime: '2024-08-05',
                }
            ];

            let filteredTableData = [...allTableData];

            const renderTable = () => {
                tableBody.innerHTML = '';
                const start = (currentPage - 1) * pageSize;
                const end = start + pageSize;
                const paginatedData = filteredTableData.slice(start, end);

                paginatedData.forEach(rowData => {
                    const row = document.createElement('tr');
                    let operationsHtml = '';
                    if (rowData.auditStatus === '待审核') {
                        operationsHtml = `
                            <button type="button" class="button text" onclick="parent.window.location.hash = '#/procurement-plan-management/add-edit?id=${rowData.planNumber}'">编辑</button>
                            <button type="button" class="button text" data-action="delete" data-id="${rowData.planNumber}">删除</button>
                            <button type="button" class="button text" data-action="submit" data-id="${rowData.planNumber}">提交</button>
                        `;
                    } else if (rowData.auditStatus === '审核中') {
                        operationsHtml = `
                            <button type="button" class="button text" onclick="parent.window.location.hash = '#/procurement-plan-management/detail-review?id=${rowData.planNumber}&review=1'">审核</button>
                        `;
                    } else if (rowData.auditStatus === '审核通过') {
                        operationsHtml = `
                            <button type="button" class="button text" onclick="parent.window.location.hash = '#/procurement-plan-management/detail-review?id=${rowData.planNumber}'">详情</button>
                        `;
                    }

                    row.innerHTML = `
                        <td>${rowData.planNumber}</td>
                        <td>${rowData.planProjectName}</td>
                        <td>${rowData.procurementType}</td>
                        <td>${rowData.procurementMethod}</td>
                        <td>${rowData.auditStatus}</td>
                        <td>${rowData.procurementOrganizationMethod}</td>
                        <td>${rowData.agency}</td>
                        <td>${rowData.projectHandler}</td>
                        <td>${rowData.projectOwner}</td>
                        <td>${rowData.biddingTime}</td>
                        <td>${rowData.createTime}</td>
                        <td>${operationsHtml}</td>
                    `;
                    tableBody.appendChild(row);
                });

                totalItemsSpan.textContent = filteredTableData.length;
                currentPageSpan.textContent = currentPage;
                prevPageButton.disabled = currentPage === 1;
                nextPageButton.disabled = currentPage * pageSize >= filteredTableData.length;
            };

            const handleAction = (event) => {
                const button = event.target;
                if (button.tagName === 'BUTTON' && button.dataset.action) {
                    const action = button.dataset.action;
                    const id = button.dataset.id;
                    const rowData = filteredTableData.find(item => item.planNumber === id);
                    if (rowData) {
                        switch (action) {
                            case 'delete':
                                if (confirm(`确定要删除计划项目 "${rowData.planProjectName}" (编号: ${id}) 吗？`)) {
                                    alert(`删除: ${rowData.planProjectName} (ID: ${id})`);
                                    // In a real app, you'd remove from data and re-render
                                }
                                break;
                            case 'submit':
                                if (confirm(`确定要提交计划项目 "${rowData.planProjectName}" (编号: ${id}) 吗？`)) {
                                    alert(`提交: ${rowData.planProjectName} (ID: ${id})`);
                                    // In a real app, you'd update status and re-render
                                }
                                break;
                        }
                    }
                }
            };

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    tabs.forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    activeTab = tab.dataset.tab;
                    console.log('Active Tab:', activeTab);
                    // Filter data based on tab
                    if (activeTab === 'pending') {
                        filteredTableData = allTableData.filter(item => ['待审核', '审核中', '审核未过'].includes(item.auditStatus));
                    } else if (activeTab === 'done') {
                        filteredTableData = allTableData.filter(item => item.auditStatus === '审核通过');
                    } else {
                        filteredTableData = [...allTableData];
                    }
                    currentPage = 1;
                    renderTable();
                });
            });

            queryButton.addEventListener('click', () => {
                const formData = new FormData(searchForm);
                const searchParams = {};
                for (let [key, value] of formData.entries()) {
                    searchParams[key] = value;
                }
                console.log('查询条件:', searchParams);

                filteredTableData = allTableData.filter(item => {
                    let match = true;
                    if (searchParams.planProjectName && !item.planProjectName.includes(searchParams.planProjectName)) {
                        match = false;
                    }
                    if (searchParams.auditStatus && item.auditStatus !== searchParams.auditStatus) {
                        match = false;
                    }
                    if (searchParams.procurementType && item.procurementType !== searchParams.procurementType) {
                        match = false;
                    }
                    if (searchParams.procurementMethod && item.procurementMethod !== searchParams.procurementMethod) {
                        match = false;
                    }
                    if (searchParams.procurementOrganizationMethod && item.procurementOrganizationMethod !== searchParams.procurementOrganizationMethod) {
                        match = false;
                    }
                    if (searchParams.fundSource && item.fundSource !== searchParams.fundSource) {
                        match = false;
                    }
                    if (searchParams.procurementBudgetAmountMin && parseFloat(item.procurementBudgetAmount) < parseFloat(searchParams.procurementBudgetAmountMin)) {
                        match = false;
                    }
                    if (searchParams.procurementBudgetAmountMax && parseFloat(item.procurementBudgetAmount) > parseFloat(searchParams.procurementBudgetAmountMax)) {
                        match = false;
                    }
                    if (searchParams.projectHandler && !item.projectHandler.includes(searchParams.projectHandler)) {
                        match = false;
                    }
                    if (searchParams.decisionDateStart && item.createTime < searchParams.decisionDateStart) { // Using createTime as proxy for decisionDate for dummy data
                        match = false;
                    }
                    if (searchParams.decisionDateEnd && item.createTime > searchParams.decisionDateEnd) {
                        match = false;
                    }
                    if (searchParams.createTimeStart && item.createTime < searchParams.createTimeStart) {
                        match = false;
                    }
                    if (searchParams.createTimeEnd && item.createTime > searchParams.createTimeEnd) {
                        match = false;
                    }
                    return match;
                });
                currentPage = 1;
                renderTable();
            });

            resetButton.addEventListener('click', () => {
                searchForm.reset();
                console.log('重置搜索条件');
                filteredTableData = [...allTableData];
                currentPage = 1;
                renderTable();
            });

            toggleCollapseButton.addEventListener('click', () => {
                const searchArea = document.querySelector('.search-area');
                isCollapsed = !isCollapsed;
                if (isCollapsed) {
                    searchArea.classList.add('collapsed');
                    toggleCollapseButton.textContent = '高级查询';
                } else {
                    searchArea.classList.remove('collapsed');
                    toggleCollapseButton.textContent = '收起';
                }
                console.log('Toggle collapse:', isCollapsed);
            });

            tableBody.addEventListener('click', handleAction);

            pageSizeSelect.addEventListener('change', (event) => {
                pageSize = parseInt(event.target.value);
                currentPage = 1;
                renderTable();
            });

            prevPageButton.addEventListener('click', () => {
                if (currentPage > 1) {
                    currentPage--;
                    renderTable();
                }
            });

            nextPageButton.addEventListener('click', () => {
                const totalPages = Math.ceil(filteredTableData.length / pageSize);
                if (currentPage < totalPages) {
                    currentPage++;
                    renderTable();
                }
            });

            renderTable();
        });
    </script>
</body>

</html>