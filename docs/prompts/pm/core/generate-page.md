## 生成Funi管理系统页面提示词

这个文件的任务执行完成之后不需要在浏览器里面运行html

### 任务描述

根据Funi管理系统的菜单结构，为指定的模块或菜单项生成对应的HTML页面。

**指令识别**: 生成页面的指令格式为 `生成页面-[模块名称或菜单名称]`。

- 如果提供的是**模块名称**（例如：“采购执行管理”），则生成该模块下所有子菜单对应的页面。
- 如果提供的是**菜单名称**（例如：“工作台”或“项目标段管理”），则只生成当前菜单对应的页面。

每个菜单项默认应包含以下三种页面类型：

1.  **列表页面 (List Page)**: 用于展示数据列表和提供查询、新增等操作。
    - **模板地址**: `docs/pm/framework/templates/list-page-template.html` (**必须严格以此模板为基础进行内容替换和生成**)`
2.  **新增/编辑页面 (Add/Edit Page)**: 用于创建新数据或编辑现有数据。
3.  **详情/审核页面 (Detail/Review Page)**: 用于展示单条数据的详细信息和提供审核操作。

所有生成的页面文件都应放置在`docs/pm/prototype/pages/`目录下，并根据其菜单路径和页面类型命名。

### 页面生成规则

1.  **需求文档解析**:
    - 必须首先解析`docs/PRD.md`文件，特别是其中的“系统功能菜单结构”部分，以获取完整的模块和菜单层级关系。
    - 同时，从`docs/PRD.md`的“第三部分：PC端管理系统功能规格”中提取每个菜单的详细功能描述（查询功能、列表功能、新建/编辑页面、详情页面等）。

2.  **菜单结构获取 (从 index.html)**:
    - 页面生成还需结合`docs/pm/prototype/index.html`文件中的菜单结构。
    - 从`index.html`中解析出所有的菜单项及其对应的`href`属性（hash路由）和`span`标签中的文本（菜单名称）。
    - 对于模块（`funi-menu-group`），其名称通过`funi-menu-group-title`下的`span`标签获取。

3.  **模块/菜单匹配与类型判断**:
    - 根据用户提供的`[模块名称或菜单名称]`，首先在`docs/PRD.md`解析出的菜单结构中进行匹配，判断其是模块（一级菜单，如“采购执行管理”）还是具体菜单（二级或三级菜单，如“工作台”或“项目标段管理”）。
    - 如果是模块名称，则循环生成该模块下所有子菜单对应的页面。
    - 如果是菜单名称，则只生成当前菜单对应的页面。
    - **如果未找到匹配的模块名称或菜单名称，请直接结束任务并提示：“没有找到相关菜单或模块。”**

4.  **页面路径**:
    - 页面路径应基于菜单项的`path`字段（已在`docs/prompts/pm/core/generate-menu.md`中定义为hash路由）。
    - 例如，如果菜单项的path是`#/dashboard`，则其列表页面路径为`docs/pm/prototype/pages/dashboard/list.html`。
    - 如果菜单项的path是`#/procurement-execution/project-bid-management`，则其列表页面路径为`docs/pm/prototype/pages/procurement-execution/project-bid-management/list.html`。
    - **注意菜单名称和页面名称的对应以及hash名称的设计，确保它们逻辑一致且易于理解。**
    - **预期URL结构示例**:
      - 列表页面: `#{menu-path}` (e.g., `#/procurement-plan-management`)
      - 新增/编辑页面: `#{menu-path}/add-edit` (e.g., `#/procurement-plan-management/add-edit`)
        - 详情/审核页面: `#{menu-path}/detail-review?review=1` (e.g., `#/procurement-plan-management/detail-review?review=1`)

5.  **页面命名**:
    - 列表页面: `{menu-path-segment}/list.html`
    - 新增/编辑页面: `{menu-path-segment}/add-edit.html`
    - 详情/审核页面: `{menu-path-segment}/detail-review.html`
    - 其中`{menu-path-segment}`是菜单项的路径（不包含`#`前缀），并转换为文件系统路径（例如，`/procurement-execution/project-bid-management` 转换为 `procurement-execution/project-bid-management`）。

6.  **页面内容生成**: 所有页面内容生成（包括列表、新增/编辑、详情/审核页面）都必须严格遵循对应的模板结构，并根据PRD（产品需求文档）中的具体数据和功能描述进行内容替换和动态生成。
    - **原型性质说明**: 生成的页面仅为原型，旨在展示结构和基本功能，数据和逻辑无需过于精确或复杂。
    - **禁止在`select`标签上使用`multiple`属性。如果逻辑上需要多选，请在`placeholder`中添加“(多选)”提示，例如“请选择(多选)”。**
    - **必须完全使用原生的HTML、CSS和JavaScript，禁止使用任何前端框架（如Vue、React）或自定义组件（如`funi-`开头的组件）。**
    - 每个页面应包含基本的HTML结构，并引用`docs/pm/framework/base-template.html`中的CSS和JS文件。
    - 页面内容应包含一个简单的标题，指示当前页面类型和所属菜单。
    - 页面应为独立的HTML文件，不包含整体布局（如头部、侧边栏），因为它们将在主`index.html`的iframe中加载。
    - **页面内容的主体应直接包含在`<div id="app" class="container">`中，而不是额外的`funi-page-content`包裹。**
    - **根据`docs/PRD.md`中该菜单的详细描述，生成对应的代码结构，并使用原生HTML元素实现：**
      - **查询功能 (搜索区域)**: 对应页面上的搜索区域，必须严格替换模板中的 `.search-form` 容器内的 `search-form-item` 元素，根据PRD中的查询条件定义进行生成，并使用原生HTML元素（`div`、`input`、`select`、`button`等）构建表单，使用CSS进行布局和样式。
      - **列表功能**:
        - **视图 (头部Tab切换)**: 必须严格替换模板中的 `.tabs` 容器内的 `tab-item` 元素，根据PRD中的视图或状态定义进行生成，并通过JavaScript控制显示隐藏。
        - **列表字段**: 必须严格替换模板中 `table` 的 `<thead>` 部分，根据PRD中定义的列表字段生成对应的 `<th>` 元素。
        - **列表数据**: `<tbody>` 部分必须根据PRD数据生成 <=10 条自定义假数据（如果PRD未提供具体数据，则生成通用假数据）。
        - **列表操作**: 使用`button`或`a`标签作为操作按钮，通过JavaScript实现交互逻辑。**特别注意：所有页面间的跳转（如“新建”、“编辑”、“详情”、“审核”按钮点击）必须通过修改顶层窗口的hash值来实现，即使用`window.top.location.hash = '...'`。**
        - **样式调整**: 必须严格遵循模板中已有的样式结构和类名。**列表页面需额外引用 `funi-list.css`。**
      - **新增/编辑页面**: 用于创建新数据或编辑现有数据。
        - **模板地址**: `docs/pm/framework/templates/form-page-template.html` (**必须严格以此模板为基础进行内容替换和生成，禁止修改样式和类名**)。
        - **步骤导航 (`form-step`)**: 默认必须包含 `附件上传` 和 `基本信息` 两个步骤。根据需求描述，可以在这两个步骤之间添加其他中间步骤。
        - **步骤内容 (`form-step-content`)**: 必须与 `form-step` 严格绑定，确保内容与当前激活的步骤对应。
        - **表单项 (`form-item-row`)**: 仅替换 `form-item-row` 下的 `label` 标签内容和 `form-item-value` 内部的元素。`form-item-value` 容器必须保留。
        - **请参考 `docs/prompts/pm/core/form/generate-form.md` 中的表单生成要求，确保表单默认三列展示，且标签背景色为 `#f8f8f8` 并支持深色主题切换。`form-section-title` 元素应放置在 `form` 标签外部。**
        - **新增/编辑页面需额外引用 `funi-form.css`。**
      - **详情/审核页面**:
        - **模板地址**: `docs/pm/framework/templates/detail-page-template.html` (**必须严格以此模板为基础进行内容替换和生成，禁止修改添加样式**)。
        - **内容替换规则**:
          - `header-title`: 必须根据业务描述替换其内容。
          - `funi-tabs`: 根据业务描述生成对应的 `tab-item`。其中 `基本信息 (basic-info)` 和 `操作日志 (operation-log)` 两个 `tab-item` 必须保留，其余 `tab-item` 根据业务描述添加。
          - `form` 内容: 只能替换 `form-item-row` 下的 `label` 标签内容和 `form-item-value` 内部 `display-value` 的值。`form-item-value` 容器必须保留。
          - **假数据**: 必须为 `display-value` 制造假数据进行填充。
        - **详情/审核页面需额外引用 `detail.css`。** **【重要提示：此规则与模板中“禁止修改添加样式”冲突时，请严格遵循模板的CSS引用，即不额外添加`detail.css`，除非模板本身已包含。】**
      - **表单操作**: `form-actions` 元素应放置在 `form` 标签外部，并固定在页面底部。
      - **URL参数获取**: 在页面中获取URL参数时，必须从顶层窗口的hash中获取，使用以下代码模式：
        ```javascript
        const hash = window.top.location.hash;
        const queryString = hash.split('?')[1] || '';
        const urlParams = new URLSearchParams(queryString);
        const id = urlParams.get('id');
        ```
    - 示例页面内容（以“工作台”的列表页面为例，将更详细）：

- **CSS和JS文件引用路径**: 所有生成的页面（列表、新增/编辑、详情/审核）中引用的CSS (`.css`) 和 JavaScript (`.js`) 文件的相对路径必须是 `../../assets/`。这是因为生成的页面位于 `docs/pm/prototype/pages/{menu-path-segment}/` 目录下，需要向上两级目录才能访问到 `docs/pm/prototype/assets/` 目录。
  - 注意CSS和JS文件的相对路径调整（相对于`docs/pm/prototype/pages/{menu-path-segment}/`）。
  - **所有CSS和JS资源文件都应在`docs/pm/prototype/assets/`目录下找到对应同名的包。**

### 生成后检查规则

在页面生成完成后，必须严格执行以下检查：

1.  **`select` 标签 `multiple` 属性检查**:
    - 确保所有生成的 HTML 页面中，任何 `<select>` 标签都**禁止**使用 `multiple` 属性。
    - 如果该 `select` 逻辑上需要支持多选，则必须在 `<option value="">` 中添加 “(多选)” 提示，例如：“请选择(多选)”。
2.  **JS/CSS 资源引用路径检查**:
    - 仔细检查所有生成的 HTML 页面中引用的 JavaScript (`.js`) 和 CSS (`.css`) 文件的相对路径是否正确。
    - 确保这些路径能够准确指向 `docs/pm/prototype/assets/` 目录下的对应资源。
3.  **综合检查**:
    - 对照上述第 1 条和第 2 条规则，对所有生成的页面进行全面检查，确保所有要求均已满足。
