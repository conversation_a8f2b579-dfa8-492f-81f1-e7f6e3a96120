# 前端开发快速启动模板 v3.0

## 一键启动新项目

本模板提供了基于v3.0模块级任务生成的快速启动方案，通过标准化的指令序列，可以在最短时间内完成项目的初始化和开发环境搭建。

### **v3.0核心优势**

- 🚀 **模块级任务**：以功能模块为最小粒度，避免过度细分
- 📝 **精简文档**：单个任务文档控制在100行以内
- ⚡ **集成开发**：单个任务包含完整开发流程

### 适用场景

- 全新项目开发
- 现有项目重构
- 团队标准化开发
- 快速原型验证

## 快速启动检查清单

### 环境准备检查

- [ ] CLI框架环境正常（确认`src/apps/`目录存在）
- [ ] FuniUI组件库可用
- [ ] PRD文档已准备（`docs/PRD.md`）
- [ ] 系统名称已确定
- [ ] 开发权限已获取

### 文档准备检查

- [ ] PRD文档包含完整的功能模块描述
- [ ] 功能模块层级关系清晰
- [ ] 页面类型和业务特征明确
- [ ] 工作流需求已识别

## 标准启动流程

### 方案A：完整项目启动（推荐）

#### 第1步：项目模板生成

```
指令：生成项目模板-[你的系统名称]
示例：生成项目模板-采购管理系统
```

#### 第2步：按计划执行开发（v3.0）

```
# 叶子模块执行
执行任务文档-docs/tasks/[模块名]/module-development.md

# 中间层模块执行
执行任务文档-docs/tasks/[父模块名]/router-organization.md
```

### 方案B：分步启动（适合大型项目）

#### 第1步：技术方案设计

```
指令：设计技术方案-[你的系统名称]
示例：设计技术方案-财务管理系统
```

#### 第2步：模块级任务生成（v3.0）

```
指令：制定模块开发任务-[模块名称]
示例：制定模块开发任务-用户管理模块
```

#### 第3步：模块级执行开发（v3.0）

```
# 执行叶子模块任务
执行任务文档-docs/tasks/[模块名]/module-development.md

# 执行中间层模块任务
执行任务文档-docs/tasks/[父模块名]/router-organization.md
```

### 方案C：单模块快速开发

#### 适用场景

- 独立模块开发
- 功能迭代开发
- 紧急需求开发

#### 执行流程（v3.0）

```
制定模块开发任务-[模块名称]
执行任务文档-docs/tasks/[模块名]/module-development.md

# 一个任务文档包含完整开发流程
```

## 常用指令速查

### 项目初始化指令

- `生成项目模板-[系统名称]`
- `设计技术方案-[系统名称]`
- `批量创建任务-[系统名称]`

### 模块开发指令（v3.0）

- `制定模块开发任务-[模块名称]`
- `执行任务文档-[任务文档路径]`

### 进度管理指令

- `查看模块进度-[模块名称]`
- `获取下一步任务-[模块名称]`
- `构建依赖关系-[系统名称]`

## 成功标准

### 项目启动成功标准

- [ ] 所有模块的任务文档生成完成
- [ ] 依赖关系图构建正确
- [ ] 执行计划清晰可行
- [ ] 团队成员理解工作流程

### 模块开发成功标准（v3.0）

- [ ] 模块任务文档执行完成
- [ ] 代码质量验证通过
- [ ] 功能测试正常
- [ ] 集成验证通过

### 项目完成成功标准

- [ ] 所有模块集成成功
- [ ] 系统功能验证通过
- [ ] 性能指标达标
- [ ] 部署配置正确
