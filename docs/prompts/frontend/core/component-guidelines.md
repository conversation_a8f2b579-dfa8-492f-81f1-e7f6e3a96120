# 前端组件开发规范

本文档详细说明了前端组件的开发规范，特别是针对 `views/components/*.vue` 文件以及 `FuniForm` 和 `FuniCurd` 组件的使用。

## `views/components/*.vue` 文件规范

- 对于 `views/components/*.vue` 文件，如果任务文档的“验证重点”或“技术规范”中明确了其内部应使用的组件（如 `FuniForm`、`FuniCurd`），则应根据这些规范和任务文档中的实体结构来生成其内容，而非仅生成空壳。
- `views/detail/index.vue` 和 `views/create/index.vue` 中引用的组件应尽量复用，并统一放置在 `views/components/` 目录下。例如，详情页和新增/编辑页中的基本信息表单，可以复用同一个组件，并通过 `props` 传递 `isEdit` 属性来控制表单项的只读/可编辑状态。

## `FuniForm` 和 `FuniCurd` 组件使用规范

- `views/components/*.vue` 目录下的组件，表单组件应使用 `FuniForm`，列表组件应使用 `FuniCurd`。
- `FuniForm` 的基本信息在 `docs/funi-ui/components/FuniForm/` 目录下。
- `FuniCurd` 的基本信息在 `docs/funi-ui/components/FuniCurd/` 目录下。
- **重要提示**: `FuniForm` 的 `schema` 中，`component` 属性**必须**根据 `isEdit` 等 `props` 动态判断，例如 `component: props.isEdit ? 'el-input' : void 0`。
- 🈲 `FuniForm`、`FuniCurd` 不需要`import`导入,已经挂载全局了。

## `FuniDetail` 的 `steps` 下的组件规范

添加一个模版示例：

```vue
<template>
  <funi-form @get-form="e => (form = e)" :schema="schema">
    <template #customLabel="{ item }">
      <div>{{ item.prop }}</div>
    </template>
    <template #selectSlot="{ item, formModel }">
      <el-select :key="item.prop" v-model="formModel[item.prop]" placeholder="please select your zone">
        <el-option label="Zone one" value="shanghai" />
        <el-option label="Zone two" value="beijing" />
      </el-select>
    </template>
  </funi-form>
</template>

<script setup lang="jsx">
import { ref, computed, inject } from 'vue';
const loadingStatus = inject('loadingStatus');
const form = ref(null);
const schema = computed(() => {
  return [
    {
      prop: 'name',
      label: 'Activity name',
      component: () => <el-input></el-input>,
      rules: [{ required: true, message: '必填', trigger: 'change' }]
    },
    {
      prop: 'delivery',
      label: 'Instant delivery',
      component: 'el-switch',
      rules: [{ required: true, message: '必填', trigger: 'change' }]
    },

    {
      prop: 'zone_1',
      label: 'Activity zone1',
      hidden: ({ formModel }) => formModel.delivery,
      slots: { default: 'selectSlot', label: 'customLabel' },
      rules: [{ required: true, message: '必填', trigger: 'change' }]
    },
    {
      prop: 'zone_2',
      label: 'Activity zone2',
      slots: { default: 'selectSlot' },
      rules: [{ required: true, message: '必填', trigger: 'change' }]
    }
  ];
});

function ts() {
  const values = form.value.getValues();
  console.log(values);
  loadingStatus.value.status = true;
  setTimeout(() => {
    loadingStatus.value.status = false;
  }, 1000);
}
async function nextStep() {
  if (!form.value) return;
  loadingStatus.value.status = true;
  const { error, values, isValid } = await form.value.validate();
  loadingStatus.value.status = false;
  if (error) return Promise.reject();
  console.log(values);

  return Promise.resolve({});
}

async function submit() {
  return Promise.resolve({});
}
defineExpose({
  nextStep,
  ts,
  submit
});
</script>
```

- **重要提示**: 对于作为 `FuniDetail` 的 `steps` 下的一级组件（例如 `views/detail/index.vue` 和 `views/create/index.vue` 中引用的 `views/components/*.vue`），必须通过 `defineExpose` 暴露方法。具体要求如下：
  - 必须暴露 `authFun` 方法，用于表单验证，并返回 `Promise`。
  - 如果该组件是 `FuniDetail` 的 `steps` 下的非最后一个 `step`，则还必须暴露 `nextStep` 和 `ts` 方法，且这些方法必须返回 `Promise`。
  - 如果组件是 `FuniDetail` 的 `steps` 下的最后一个 `step`，或者只有一个 `step`，且该组件不是 `FuniFileTable`，则必须暴露 `ts` 和 `submit` 方法，且这些方法必须返回 `Promise`。
- **重要提示**: 对于 `FuniDetail` 的 `steps` 下的组件，应通过 `inject('loadingStatus')` 获取 `loadingStatus` 响应式引用，并在进行异步操作（如 API 请求、表单验证）时，根据 `loadingStatus.value.status = true/false` 来控制加载状态。
- **重要提示**: `FuniDetail` 的 `steps` 下的一级组件必须通过 `defineExpose` 暴露方法。如果组件是 `FuniDetail` 的 `steps` 下的非最后一个 `step`，则必须暴露 `nextStep` 和 `ts` 方法。如果组件是 `FuniDetail` 的 `steps` 下的最后一个 `step`，或者只有一个 `step`，且该组件不是 `FuniFileTable`，则必须暴露 `ts` 和 `submit` 方法。`FuniFileTable` 组件内部已定义相关操作，无需额外处理。
  - `nextStep`: 下一步调用方法，通常用于提交数据。
  - `ts`: 暂存方法。
  - `submit`: 最终提交方法。
- 示例 `defineExpose` 代码：
  ```vue
  <script setup>
  function nextStep() {}
  function ts() {}
  function submit() {}
  defineExpose({
    nextStep,
    ts,
    submit
  });
  </script>
  ```
