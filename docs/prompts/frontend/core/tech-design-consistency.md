# 技术方案设计一致性规范

**版本**: v1.0  
**适用范围**: 所有技术方案设计任务  
**加载优先级**: 最高

## 规范目的

确保相同PRD输入在不同会话中产生完全一致的技术设计输出，消除技术方案设计的随意性和主观性。

## 强制一致性要求

### 1. 文档格式标准化

**必须包含的元数据**:
```markdown
# {系统名称} - {文档类型}

**版本**: v1.0
**系统名称**: {系统名称}
**设计时间**: {YYYY-MM-DD}
```

**标准化章节结构**:
- 系统概述
- 模块结构设计
- 分层架构设计
- 核心业务架构
- 模块依赖关系
- 技术架构约束

### 2. 模块识别一致性

**强制执行规则**:
- 严格按照PRD标题层级识别模块
- 二级标题（##）= 一级模块
- 三级标题（###）= 二级模块
- 四级标题（####）= 三级模块

**模块分类标准**:
- **叶子模块**: 没有子模块的模块
- **中间层模块**: 有子模块的模块

**模块信息必须包含**:
- 模块类型（叶子模块/中间层模块）
- 页面类型（列表页/详情页/工作流页）
- 功能描述
- 包含功能列表

### 3. 占位符参数标准化

**标准格式**:
```javascript
{
  "{{MODULE_NAME}}": "模块名称",
  "{{ENTITY_NAME}}": "实体名称", 
  "{{API_ENDPOINT}}": "/api/endpoint",
  "{{STORE_NAME}}": "useStoreStore",
  "{{PRIMARY_KEY}}": "id",
  "{{DISPLAY_FIELD}}": "displayField"
}
```

**生成算法**:
- MODULE_NAME: 直接使用模块名称
- ENTITY_NAME: 移除"管理"、"模块"等后缀
- API_ENDPOINT: 基于实体名称生成RESTful路径
- STORE_NAME: 使用"use{Entity}Store"格式
- PRIMARY_KEY: 默认使用"id"
- DISPLAY_FIELD: 根据业务特点确定主显示字段

### 4. 任务分解一致性

**叶子模块任务序列**:
- T001 - 基础设施准备（API + Adapters + Store）
- T002 - 路由配置（基于router-standards.md规范）
- T003 - 页面开发（基于模板）
- T004 - 系统集成验证

**中间层模块任务序列**:
- T001 - 路由组织配置

**任务命名规范**:
- 使用模块名称作为任务名称
- 包含预估工时和依赖关系
- 明确特殊需求和功能点

## 验证检查清单

### 设计前检查

**PRD文档验证**:
- [ ] PRD包含明确的标题层级结构
- [ ] 每个模块都有功能描述
- [ ] 工作流需求明确标识

**规范文档加载**:
- [ ] 已加载architecture-standards.md
- [ ] 已加载component-constraints.md
- [ ] 已加载development-workflow.md
- [ ] 已加载router-standards.md
- [ ] 已加载prd-parsing-standards.md

### 设计过程检查

**模块识别验证**:
- [ ] 模块识别严格按照标题层级
- [ ] 模块分类正确（叶子/中间层）
- [ ] 页面类型判断使用标准算法
- [ ] 工作流需求识别准确

**参数生成验证**:
- [ ] 占位符参数格式标准化
- [ ] 参数生成算法一致
- [ ] 所有必需参数都已生成

### 设计后检查

**文档完整性**:
- [ ] 包含所有必需的元数据
- [ ] 章节结构符合标准
- [ ] 内容完整无遗漏

**一致性验证**:
- [ ] 模块结构与PRD完全对应
- [ ] 依赖关系逻辑正确
- [ ] 技术约束符合规范

## 质量保证机制

### 1. 算法确定性

**模块识别算法**:
```javascript
function identifyModules(prdContent) {
  const modules = [];
  const lines = prdContent.split('\n');
  
  for (const line of lines) {
    if (line.startsWith('## ')) {
      // 一级模块
      modules.push({
        name: extractModuleName(line),
        level: 2,
        type: 'primary',
        children: []
      });
    } else if (line.startsWith('### ')) {
      // 二级模块
      const parentModule = modules[modules.length - 1];
      parentModule.children.push({
        name: extractModuleName(line),
        level: 3,
        type: 'secondary',
        parent: parentModule.name
      });
    }
  }
  
  return modules;
}
```

**页面类型判断算法**:
```javascript
function determinePageType(moduleName, content) {
  const listKeywords = ['列表', '管理', '查询', '检索'];
  const detailKeywords = ['详情', '详细', '信息', '查看'];
  const workflowKeywords = ['审批', '流程', '审核', '工作流'];
  
  if (workflowKeywords.some(keyword => content.includes(keyword))) {
    return 'workflow';
  } else if (listKeywords.some(keyword => moduleName.includes(keyword))) {
    return 'list';
  } else if (detailKeywords.some(keyword => moduleName.includes(keyword))) {
    return 'detail';
  }
  
  return 'list'; // 默认类型
}
```

### 2. 输出标准化

**模块结构输出格式**:
```json
{
  "modules": [
    {
      "name": "模块名称",
      "level": 2,
      "type": "leaf|intermediate",
      "pageType": "list|detail|workflow",
      "placeholders": {
        "{{MODULE_NAME}}": "值",
        "{{ENTITY_NAME}}": "值"
      },
      "children": []
    }
  ]
}
```

**任务分解输出格式**:
```json
{
  "tasks": [
    {
      "id": "T001",
      "name": "任务名称",
      "module": "模块名称",
      "type": "leaf|intermediate",
      "sequence": ["T001", "T002", "T003", "T004"],
      "dependencies": [],
      "estimatedHours": 24
    }
  ]
}
```

## 错误预防机制

### 常见错误及预防

**模块识别错误**:
- 错误：忽略标题层级，按功能重组模块
- 预防：强制按照标题层级识别，禁止主观重组

**页面类型判断错误**:
- 错误：基于主观判断确定页面类型
- 预防：使用标准化算法，基于关键词匹配

**参数生成错误**:
- 错误：参数格式不统一，生成算法随意
- 预防：使用固定的参数生成算法和格式

**任务分解错误**:
- 错误：任务分解粒度不一致，依赖关系混乱
- 预防：使用标准化的任务序列和依赖规则

### 自动化验证

**格式验证**:
- 检查文档是否包含必需的元数据
- 验证章节结构是否符合标准
- 确认占位符参数格式正确

**内容验证**:
- 验证模块结构与PRD对应
- 检查页面类型判断结果
- 确认任务分解逻辑正确

**一致性验证**:
- 对比多次生成结果的一致性
- 验证关键字段的稳定性
- 检查算法执行的确定性

## 实施指南

### 1. 集成到现有流程

**更新技术设计助手**:
- 在设计流程中增加一致性检查点
- 强制执行PRD解析标准化规则
- 添加输出格式验证机制

**更新核心规范**:
- 将本规范加入核心规范加载列表
- 更新相关助手的执行流程
- 建立质量检查机制

### 2. 监控和改进

**建立监控机制**:
- 记录每次技术方案设计的关键指标
- 定期进行一致性回归测试
- 收集和分析差异性问题

**持续改进**:
- 根据监控结果优化算法
- 完善错误预防机制
- 更新验证检查清单

---

**重要**: 本规范的目标是将技术方案设计的一致性提升至99%以上。所有技术设计任务都必须严格遵循本规范执行。
