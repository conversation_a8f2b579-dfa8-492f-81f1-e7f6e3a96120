# 系统级路由集成指导

## 概述

本文档指导Agent在T004系统集成验证任务中，如何检查和生成系统级路由文件，确保模块路由正确集成到系统路由中。

## 集成检查流程

### 1. 系统级路由文件检查

#### 检查步骤
1. **确定系统编码**：从模块路径提取系统编码
   ```
   src/apps/bidding-procurement/modules/procurement-plan/
   → 系统编码: bidding-procurement
   ```

2. **检查系统级路由文件**：
   ```
   src/apps/{system-code}/routers/index.js
   ```

3. **验证文件存在性**：
   - 如果文件存在：验证配置正确性
   - 如果文件不存在：自动生成系统级路由文件

### 2. 系统级路由自动生成

#### 生成条件
- 系统级路由文件不存在
- 或文件存在但配置不完整

#### 生成步骤

**步骤1：提取系统信息**
```javascript
// 从路径提取信息
const systemCode = 'bidding-procurement';  // 从目录名提取
const systemName = 'BiddingProcurement';   // kebab-case → PascalCase
const systemTitle = '招标采购系统';          // 根据业务领域推断
const systemIcon = 'bidding';              // 根据系统类型设置
```

**步骤2：使用模板生成**
- 模板文件：`docs/prompts/frontend/templates/code-templates/system-router.js`
- 替换占位符参数
- 生成系统级路由文件

**步骤3：集成模块路由**
```javascript
// 自动添加模块路由导入
import procurementPlanRouter from '../modules/procurement-plan/router.js';

// 添加到children数组
children: [
  procurementPlanRouter
]
```

### 3. 模块路由集成验证

#### 验证项目
1. **导入路径正确**：
   ```javascript
   // 正确格式
   import moduleRouter from '../modules/{module-name}/router.js';
   ```

2. **children数组包含模块路由**：
   ```javascript
   children: [
     moduleRouter,
     // 其他模块路由...
   ]
   ```

3. **路径层级正确**：
   ```
   系统路径: /bidding-procurement
   模块路径: /procurement-plan (相对路径)
   完整路径: /bidding-procurement/procurement-plan
   ```

## 占位符参数配置

### 系统级占位符
```javascript
{
  "{{SYSTEM_CODE}}": "bidding-procurement",
  "{{SYSTEM_NAME}}": "BiddingProcurement", 
  "{{SYSTEM_TITLE}}": "招标采购系统",
  "{{SYSTEM_ICON}}": "bidding",
  "{{DEFAULT_MODULE}}": "procurement-plan"
}
```

### 参数生成规则

#### 系统编码 (SYSTEM_CODE)
- 来源：目录名
- 格式：kebab-case
- 示例：`bidding-procurement`

#### 系统名称 (SYSTEM_NAME)
- 来源：系统编码转换
- 格式：PascalCase
- 转换规则：kebab-case → PascalCase
- 示例：`bidding-procurement` → `BiddingProcurement`

#### 系统标题 (SYSTEM_TITLE)
- 来源：业务领域推断或用户提供
- 格式：中文名称
- 示例：`招标采购系统`

#### 系统图标 (SYSTEM_ICON)
- 来源：系统类型推断
- 格式：图标名称
- 推断规则：
  - bidding → 'bidding'
  - user → 'user'
  - order → 'shopping'
  - finance → 'money'

#### 默认模块 (DEFAULT_MODULE)
- 来源：第一个模块名称
- 格式：kebab-case
- 示例：`procurement-plan`

## 验证检查清单

### 文件结构验证
- [ ] 系统级路由文件存在
- [ ] 文件位置正确 (`src/apps/{system-code}/routers/index.js`)
- [ ] 文件语法正确

### 配置内容验证
- [ ] 路由路径格式正确 (`/{system-code}`)
- [ ] 路由名称格式正确 (PascalCase)
- [ ] 元数据配置完整 (title, icon, isMenu)
- [ ] 重定向路径有效

### 模块集成验证
- [ ] 模块路由正确导入
- [ ] 导入路径使用相对路径
- [ ] children数组包含所有模块路由
- [ ] 模块路由路径为相对路径

### 功能验证
- [ ] 系统菜单显示正常
- [ ] 模块菜单正确嵌套
- [ ] 路由跳转路径正确
- [ ] 面包屑导航层级正确

## 常见问题处理

### 1. 系统级路由文件缺失
**问题**：模块开发完成但系统级路由文件不存在
**解决**：
1. 使用系统级路由模板自动生成
2. 替换占位符参数
3. 集成现有模块路由

### 2. 模块路由导入错误
**问题**：模块路由导入路径不正确
**解决**：
1. 检查模块路由文件是否存在
2. 使用相对路径导入：`../modules/{module-name}/router.js`
3. 验证导入语法正确

### 3. 路径层级错误
**问题**：模块路由使用绝对路径导致路径冲突
**解决**：
1. 确保模块路由使用相对路径
2. 系统级路由提供路径前缀
3. 验证完整路径正确

### 4. 重定向路径无效
**问题**：系统级路由重定向到不存在的路径
**解决**：
1. 检查默认模块是否存在
2. 更新重定向路径到有效的模块路径
3. 验证重定向功能正常

## 自动化处理建议

### Agent执行逻辑
```javascript
// 伪代码示例
function integrateSystemRouter(systemCode, moduleName) {
  const systemRouterPath = `src/apps/${systemCode}/routers/index.js`;
  
  // 1. 检查系统级路由文件
  if (!fileExists(systemRouterPath)) {
    // 2. 自动生成系统级路由文件
    generateSystemRouter(systemCode, moduleName);
  } else {
    // 3. 验证现有配置
    validateSystemRouter(systemRouterPath);
  }
  
  // 4. 集成模块路由
  integrateModuleRouter(systemRouterPath, moduleName);
  
  // 5. 验证集成结果
  validateIntegration(systemCode, moduleName);
}
```

### 生成优先级
1. **检查现有文件**：优先验证现有配置
2. **自动生成**：文件不存在时自动生成
3. **增量集成**：添加新模块路由到现有配置
4. **验证完整性**：确保集成结果正确

## 总结

系统级路由集成是确保模块正确集成到系统中的关键步骤。Agent应该在T004任务中自动检查和处理系统级路由集成，确保：

1. 系统级路由文件存在且配置正确
2. 模块路由正确集成到系统路由中
3. 路径层级结构符合规范要求
4. 菜单和导航功能正常工作

通过自动化的检查和生成机制，可以确保每个模块都能正确集成到系统中，提供完整的用户体验。
