# 组件使用强制约束

**版本**: v2.1

**适用范围**: 所有前端开发任务

**加载优先级**: 最高

## 强制组件约束

### 页面级组件

- **列表页**: 必须使用 `FuniListPageV2`
- **详情页**: 必须使用 `FuniDetail`
- **表单组件**: 必须使用 `FuniForm`，禁止使用 el-form

### 组件开发及复用

- **页面复用**: 新增、编辑、详情、审核页面应尽可能复用同一套业务组件。
- **表单与列表**: 组件内部的表单必须使用 `FuniForm`，列表必须使用 `FuniCurd`。

### 业务组件

- **角色选择**: `FuniRUOC` (type="role")
- **用户选择**: `FuniRUOC` (type="user")
- **部门选择**: `FuniRUOC` (type="org")

### 工作流组件

- **工作流附件**: `FuniFileTable` (需 businessId)
- **审核按钮**: `FuniAuditButtomBtn`
- **审核抽屉**: `FuniBusAuditDrawer`

### 文件组件

- **工作流附件**: `FuniFileTable` (有 businessId 的工作流场景)
- **普通文件上传**: `FuniFileTable/upload.vue` (非工作流场景)

## 全局组件规则

### 重要约束

- src/components 下所有组件已在 main.js 中注册为全局组件，无需导入
- 禁止在业务代码中导入 src/components 下的任何组件
- 模板使用 kebab-case: `<funi-detail>`、`<funi-list-page-v2>`

## 技术栈约束

### 核心技术栈

- **框架**: Vue 3 + Composition API + Pinia + ElementPlus + FuniUI
- **网络请求**: `window.$http.post`、`window.$http.fetch`
- **语法偏好**: 优先使用 JSX/TSX 语法

### Vue 3 规范

- **组合式 API**: 使用 Composition API
- **响应式数据**: 使用 ref、reactive、computed
- **生命周期**: 使用 onMounted、onUnmounted
- **script 配置**: vue 组件 script 配置对应的 lang (ts、tsx、jsx)

### JavaScript 规范

- **代码风格**: 遵循 Airbnb JavaScript 风格指南
- **变量命名**: camelCase，常量 UPPER_SNAKE_CASE
- **函数定义**: 优先使用箭头函数
- **导入导出**: ES6 模块语法，按字母顺序排列导入
