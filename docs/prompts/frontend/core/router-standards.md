# 路由配置规范

## 路由表结构

### 基础结构

```javascript
// src/apps/{system-code}/routers/index.js
export default {
  path: '/system-code', // 必需：系统路径
  name: 'SystemName', // 必需：路由名称
  redirect: '/system-code/home', // 可选：重定向路径
  meta: {
    title: '系统名称', // 必需：系统显示名称
    icon: 'system-icon', // 可选：系统图标
    isMenu: true // 可选：是否显示在菜单中
  },
  children: [] // 可选：子路由数组
};
```

### 字段说明

| 字段          | 类型     | 必需 | 说明                        |
| ------------- | -------- | ---- | --------------------------- |
| `path`        | String   | ✅   | 路由路径，必须以`/`开头     |
| `name`        | String   | ✅   | 路由名称                    |
| `redirect`    | String   | ❌   | 重定向路径                  |
| `component`   | Function | ❌   | 路由组件（懒加载函数）      |
| `meta`        | Object   | ✅   | 路由元数据                  |
| `meta.title`  | String   | ✅   | 显示标题                    |
| `meta.icon`   | String   | ❌   | 图标名称                    |
| `meta.isMenu` | Boolean  | ❌   | 是否显示在菜单中，默认false |
| `children`    | Array    | ❌   | 子路由配置                  |

## 关键配置规则

### 系统编码提取

- 系统编码从根路由的`path`字段提取
- 路径必须以`/`开头
- 建议使用kebab-case格式

### 菜单显示控制

- `meta.isMenu: true` - 显示在菜单中
- `meta.isMenu: false` - 不显示在菜单中
- 未设置 - 默认不显示在菜单中
- 没有`meta.title`的路由不会显示在菜单中

### 父级路由规则

- 包含`children`的父级路由通常不配置`component`
- 父级路由作为容器使用

## 组件路径规范

### 路径格式

```javascript
component: () => import('@/apps/{system-code}/views/ComponentName.vue');
```

### 路径规范

- 必须使用`@/apps/`前缀
- 组件文件必须在对应系统的`views`目录下
- 支持子目录结构
- 文件名使用PascalCase命名

## 文件位置规范

### 路由配置文件位置

```
src/apps/{system-code}/routers/index.js
```

## 命名规范

### 命名约定

- **路由名称**: PascalCase (如UserManagement)
- **路由路径**: kebab-case (如user-management)
- **组件文件**: PascalCase (如UserList.vue)

## 常见错误

### 菜单不显示

- 缺少`meta.title`
- 缺少`meta.isMenu: true`
- 路由配置格式错误（使用数组导出而非对象导出）

### 组件加载失败

- 组件路径错误（使用相对路径而非绝对路径）
- 组件文件不存在

### 系统编码提取失败

- 路径不以`/`开头

## 系统级路由集成

### 系统级路由文件结构

系统级路由文件负责将模块路由集成到系统中，文件位置：

```
src/apps/{system-code}/routers/index.js
```

### 系统级路由模板

```javascript
// src/apps/{system-code}/routers/index.js
export default {
  path: '/{system-code}',
  name: '{SystemName}',
  redirect: '/{system-code}/home',
  meta: {
    title: '{系统中文名称}',
    icon: '{system-icon}',
    isMenu: true
  },
  children: [
    // 模块路由将被自动集成到这里
  ]
};
```

### 模块路由集成规则

#### 自动集成流程

1. **检查系统级路由文件**
   - 检查 `src/apps/{system-code}/routers/index.js` 是否存在
   - 如果不存在，根据系统架构自动生成

2. **模块路由导入**
   - 从 `src/apps/{system-code}/modules/{module-name}/router.js` 导入模块路由
   - 将模块路由添加到系统级路由的 `children` 数组中

3. **路径处理**
   - 模块路由的 `path` 保持相对路径格式
   - 系统级路由提供路径前缀

#### 集成示例

**系统级路由文件**：

```javascript
// src/apps/bidding-procurement/routers/index.js
import procurementPlanRouter from '../modules/procurement-plan/router.js';

export default {
  path: '/bidding-procurement',
  name: 'BiddingProcurement',
  redirect: '/bidding-procurement/procurement-plan',
  meta: {
    title: '招标采购系统',
    icon: 'bidding',
    isMenu: true
  },
  children: [
    procurementPlanRouter
    // 其他模块路由...
  ]
};
```

**模块路由文件**：

```javascript
// src/apps/bidding-procurement/modules/procurement-plan/router.js
export default {
  path: '/procurement-plan', // 相对路径
  name: 'ProcurementPlan',
  meta: {
    title: '采购计划管理',
    isMenu: true
  },
  children: [
    // 页面路由...
  ]
};
```

### 系统级路由生成规范

#### 占位符参数

```javascript
{
  "{{SYSTEM_CODE}}": "bidding-procurement",
  "{{SYSTEM_NAME}}": "BiddingProcurement",
  "{{SYSTEM_TITLE}}": "招标采购系统",
  "{{SYSTEM_ICON}}": "bidding",
  "{{DEFAULT_MODULE}}": "procurement-plan"
}
```

#### 生成规则

1. **系统编码提取**：从目录名提取系统编码
2. **系统名称转换**：kebab-case → PascalCase
3. **默认重定向**：指向第一个模块路由
4. **图标配置**：根据系统类型设置默认图标

### 集成验证检查

#### 必需验证项目

- [ ] 系统级路由文件存在
- [ ] 模块路由正确导入
- [ ] 路径层级结构正确
- [ ] 元数据配置完整
- [ ] 重定向路径有效
- [ ] 无自定义字段存在

#### 常见集成问题

1. **系统级路由文件缺失**
   - 问题：模块路由无法被系统识别
   - 解决：自动生成系统级路由文件

2. **模块路由导入错误**
   - 问题：导入路径不正确
   - 解决：使用相对路径导入模块路由

3. **路径冲突**
   - 问题：多个模块使用相同路径
   - 解决：确保模块路径唯一性

## 最佳实践

### 结构组织

- 避免过深的嵌套层级
- 相关功能放在同一个父级路由下
- 每个路由有明确的功能定位

### 性能优化

- 所有组件使用动态导入（懒加载）
- 大型系统可以按模块分割路由配置

## 权限控制说明

**重要**: 权限控制不是通过路由配置实现的。不要在路由配置中添加`auth`、`permission`等权限相关字段。

权限控制通过以下方式实现：

- 组件内部的权限检查
- 页面级别的权限验证
- API接口的权限控制
