# 分层架构核心规范

**版本**: v2.2

**适用范围**: 所有前端开发任务

**加载优先级**: 最高

## 架构定义

```
API层 → Adapters层 → Store层 → Views层
```

## 开发顺序约束

- **强制顺序**: API → Adapters → Store → Views → 系统集成
- **禁止跨层**: 严格按照分层顺序开发
- **依赖验证**: 每层完成后验证前置依赖

## 分层职责

- **API 层**: HTTP 请求封装，仅调用 window.$http
- **Adapters 层**: 纯函数数据转换，不调用其他层
- **Store 层**: 业务逻辑、状态管理，仅调用 API 层、Adapters 层
- **Views 层**: 数据采集、渲染、交互，仅调用 Store 层

## 调用约束

- **禁止**: 跨层调用、反向调用
- **数据流**: 单向，从上层到下层
- **依赖检查**: 创建文件前验证被导入文件存在

## 标准目录结构

```
src/apps/{系统目录}/
├── routers/index.js          # 系统级路由
├── modules/[模块名]/
│   ├── api/index.js          # API层
│   ├── adapters/index.js     # 数据转换层
│   ├── views/                # 视图层
│   │   ├── list/index.vue    # 列表页
│   │   ├── detail/index.vue  # 详情/审核页
│   │   └── create/index.vue  # 新建/编辑页
│   │   └── components/       # 新建/编辑页/详情/审核页的组件文件夹
│   ├── __mocks__/index.js    # Mock数据
│   ├── store.js              # 状态管理
│   └── router.js             # 路由配置
```

## 代码边界限制

- **允许编码**: `src/apps/{系统目录}/` 目录
- **禁止修改**: CLI 框架现有文件、src/components/、根目录配置
- **系统目录**: 新建系统使用系统名称，迭代开发可由用户指定

## HTTP 请求规范

- **使用**: `window.$http`
- **错误处理**: CLI 框架已处理，业务代码只接收成功数据
- **数据包装**: 直接使用返回的业务对象
