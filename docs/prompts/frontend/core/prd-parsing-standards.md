# PRD解析标准化规则

**版本**: v1.0 **适用范围**: 所有前端技术方案设计任务 **加载优先级**: 最高

## 规则概述

本规则旨在消除PRD解析的主观性，确保相同PRD文档在多次解析时产生完全一致的结果。所有规则基于明确的算法和模式匹配，避免人工判断的随意性。

## 1. 功能模块识别规则

### 1.1 模块边界判断标准

**规则**: 以Markdown标题层级为模块边界识别依据

**算法**:

1. 扫描PRD文档中的所有标题（# ## ### ####）
2. 二级标题（##）作为一级模块
3. 三级标题（###）作为二级模块
4. 四级标题（####）作为三级模块
5. 最多支持3层模块嵌套

**示例**:

```markdown
## 用户管理 # 一级模块

### 用户列表 # 二级模块

### 用户详情 # 二级模块

## 角色管理 # 一级模块

### 角色列表 # 二级模块
```

### 1.2 模块命名提取规则

**规则**: 直接使用标题文本作为模块名称，执行标准化处理

**算法**:

1. 提取标题文本（去除#号和前后空格）
2. 移除标题中的序号（如"1."、"1.1"、"（一）"等）
3. 移除特殊符号（如【】、()、[]等）
4. 保留中文、英文、数字和连字符

**处理规则**:

```javascript
function extractModuleName(title) {
  return title
    .replace(/^#+\s*/, '') // 移除#号
    .replace(/^\d+\.?\s*/, '') // 移除数字序号
    .replace(/^[（(]\w+[）)]\s*/, '') // 移除括号序号
    .replace(/[【\[\]】]/g, '') // 移除方括号
    .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\-]/g, '') // 保留中文英文数字连字符
    .trim();
}
```

**示例**:

- "## 1. 用户管理模块" → "用户管理模块"
- "### （一）用户列表功能" → "用户列表功能"
- "#### 【核心】用户详情页面" → "核心用户详情页面"

### 1.3 模块层级关系识别

**规则**: 基于标题层级自动构建父子关系

**算法**:

1. 维护层级栈，记录当前各层级的模块
2. 遇到新标题时，根据层级确定父子关系
3. 同级标题为兄弟关系，下级标题为子模块

**数据结构**:

```javascript
{
  "name": "用户管理",
  "level": 2,
  "parent": null,
  "children": [
    {
      "name": "用户列表",
      "level": 3,
      "parent": "用户管理",
      "children": []
    }
  ]
}
```

## 2. 页面类型判断规则

### 2.1 列表页识别规则

**关键词匹配**（优先级从高到低）:

1. 精确匹配: "列表"、"清单"、"目录"
2. 模糊匹配: "管理"、"查询"、"检索"、"浏览"
3. 英文匹配: "list"、"index"、"manage"

**模式匹配**:

- 包含"批量"、"导出"、"筛选"等操作词汇
- 描述中包含"分页"、"搜索"、"排序"等功能

**判断算法**:

```javascript
function isListPage(title, content) {
  const listKeywords = ['列表', '清单', '目录', '管理', '查询', '检索', '浏览'];
  const listPatterns = ['批量', '导出', '筛选', '分页', '搜索', '排序'];

  // 标题匹配
  if (listKeywords.some(keyword => title.includes(keyword))) {
    return true;
  }

  // 内容模式匹配
  if (listPatterns.some(pattern => content.includes(pattern))) {
    return true;
  }

  return false;
}
```

### 2.2 详情页识别规则

**关键词匹配**:

1. 精确匹配: "详情"、"详细"、"信息"、"查看"
2. 模糊匹配: "展示"、"显示"、"预览"
3. 英文匹配: "detail"、"view"、"info"

**模式匹配**:

- 包含"字段"、"属性"、"基本信息"等描述
- 描述中包含"只读"、"展示"等关键词

### 2.3 新建/编辑页识别规则

**关键词匹配**:

1. 新建页: "新建"、"创建"、"添加"、"新增"
2. 编辑页: "编辑"、"修改"、"更新"、"维护"
3. 英文匹配: "create"、"add"、"edit"、"update"

**模式匹配**:

- 包含"表单"、"输入"、"填写"等操作词汇
- 描述中包含"保存"、"提交"、"验证"等功能

### 2.4 工作流页面识别规则

**关键词匹配**:

1. 精确匹配: "审批"、"流程"、"工作流"、"审核"
2. 模糊匹配: "提交"、"驳回"、"通过"、"拒绝"

**模式匹配**:

- 包含"步骤"、"节点"、"状态"等流程词汇
- 描述中包含"businessId"、"流程实例"等技术词汇

## 3. 占位符参数生成规则

### 3.1 {{MODULE_NAME}}生成规则

**规则**: 直接使用模块名称，不做任何处理

**算法**:

```javascript
function generateModuleName(moduleName) {
  return moduleName; // 直接返回，保持原始名称
}
```

**示例**:

- "用户管理" → {{MODULE_NAME}} = "用户管理"
- "角色权限管理" → {{MODULE_NAME}} = "角色权限管理"

### 3.2 {{ENTITY_NAME}}生成规则

**规则**: 从模块名称中提取核心实体名称

**算法**:

```javascript
function generateEntityName(moduleName) {
  const suffixes = ['管理', '模块', '系统', '功能', '页面', '列表', '详情'];
  let entityName = moduleName;

  // 移除常见后缀
  for (const suffix of suffixes) {
    if (entityName.endsWith(suffix)) {
      entityName = entityName.slice(0, -suffix.length);
    }
  }

  // 如果移除后为空，使用原名称
  return entityName || moduleName;
}
```

**示例**:

- "用户管理" → {{ENTITY_NAME}} = "用户"
- "角色权限管理" → {{ENTITY_NAME}} = "角色权限"
- "订单列表" → {{ENTITY_NAME}} = "订单"

### 3.3 {{API_ENDPOINT}}生成规则

**规则**: 基于实体名称生成RESTful风格的API路径

**算法**:

```javascript
function generateApiEndpoint(entityName) {
  // 中文转英文映射表（常见业务实体）
  const cnToEnMap = {
    用户: 'users',
    角色: 'roles',
    权限: 'permissions',
    部门: 'departments',
    订单: 'orders',
    商品: 'products',
    分类: 'categories'
  };

  // 查找映射，如果没有则使用拼音
  const englishName = cnToEnMap[entityName] || toPinyin(entityName);

  return `/api/${englishName}`;
}
```

**示例**:

- "用户" → {{API_ENDPOINT}} = "/api/users"
- "角色权限" → {{API_ENDPOINT}} = "/api/role-permissions"
- "订单" → {{API_ENDPOINT}} = "/api/orders"

### 3.4 {{STORE_NAME}}生成规则

**规则**: 基于实体名称生成Pinia Store名称

**算法**:

```javascript
function generateStoreName(entityName) {
  // 转换为驼峰命名
  const camelCase = toCamelCase(entityName);
  return `use${capitalize(camelCase)}Store`;
}

function toCamelCase(str) {
  return str.replace(/[\u4e00-\u9fa5]/g, match => {
    return cnToEnMap[match] || pinyin(match);
  });
}
```

**示例**:

- "用户" → {{STORE_NAME}} = "useUserStore"
- "角色权限" → {{STORE_NAME}} = "useRolePermissionStore"
- "订单" → {{STORE_NAME}} = "useOrderStore"

## 4. 模块层级判断规则

### 4.1 叶子模块判断规则

**规则**: 没有子模块的模块为叶子模块

**算法**:

```javascript
function isLeafModule(module) {
  return !module.children || module.children.length === 0;
}
```

**任务序列**: T001-T004（完整开发流程）

### 4.2 中间层模块判断规则

**规则**: 有子模块的模块为中间层模块

**算法**:

```javascript
function isIntermediateModule(module) {
  return module.children && module.children.length > 0;
}
```

**任务序列**: T001（仅路由组织配置）

### 4.3 模块依赖关系确定规则

**规则**: 基于模块层级和业务逻辑确定依赖关系

**依赖规则**:

1. 子模块依赖父模块
2. 同级模块无依赖关系（可并行开发）
3. 基础模块优先（如用户管理、权限管理）

**算法**:

```javascript
function generateModuleDependencies(modules) {
  const dependencies = {};

  modules.forEach(module => {
    dependencies[module.name] = [];

    // 添加父模块依赖
    if (module.parent) {
      dependencies[module.name].push(module.parent);
    }

    // 添加基础模块依赖
    const basicModules = ['用户管理', '权限管理', '角色管理'];
    basicModules.forEach(basicModule => {
      if (basicModule !== module.name && modules.find(m => m.name === basicModule)) {
        dependencies[module.name].push(basicModule);
      }
    });
  });

  return dependencies;
}
```

## 5. 解析执行流程

### 5.1 标准解析流程

1. **文档预处理**: 清理格式，统一编码
2. **模块识别**: 按标题层级提取模块结构
3. **页面类型判断**: 对每个叶子模块判断页面类型
4. **参数生成**: 为每个模块生成占位符参数
5. **依赖分析**: 构建模块间依赖关系
6. **结果验证**: 检查解析结果的完整性和一致性

### 5.2 强制执行检查点

**解析前必须验证**:

- PRD文档包含明确的标题层级结构
- 二级标题作为一级模块，三级标题作为二级模块
- 每个模块都有明确的功能描述

**解析过程中必须记录**:

- 模块识别的具体依据（标题层级、关键词匹配）
- 页面类型判断的算法结果
- 占位符参数生成的完整过程

**解析后必须输出**:

- 标准化的模块结构JSON
- 完整的占位符参数表
- 模块依赖关系图

### 5.2 一致性保证机制

**确定性要求**:

- 所有规则基于明确的算法，不包含随机元素
- 相同输入必须产生相同输出
- 解析过程可重现、可验证

**验证检查**:

- 模块名称唯一性检查
- 依赖关系循环检查
- 占位符参数完整性检查
- 页面类型覆盖率检查

## 6. 错误处理规则

### 6.1 异常情况处理

**缺少标题层级**:

- 如果PRD没有二级标题，将一级标题作为模块
- 如果完全没有标题，报错并要求PRD格式修正

**模块名称冲突**:

- 如果存在同名模块，自动添加序号后缀
- 记录冲突信息，建议PRD优化

**页面类型无法判断**:

- 默认为详情页类型
- 记录未识别的模块，建议人工确认

### 6.2 质量检查规则

**必要性检查**:

- 每个系统至少包含一个叶子模块
- 每个叶子模块至少包含一种页面类型
- 所有占位符参数必须生成成功

**合理性检查**:

- 模块层级不超过3层
- 单个模块的子模块数量不超过20个
- API端点命名符合RESTful规范

## 7. 完整解析示例

### 7.1 示例PRD文档

```markdown
# 用户权限管理系统

## 用户管理

用户管理模块负责系统用户的增删改查操作。

### 用户列表

提供用户列表查询、搜索、筛选功能，支持批量操作和数据导出。

### 用户详情

展示用户的详细信息，包括基本信息、角色权限等。

### 用户新建

提供用户创建表单，包含必填字段验证和数据提交功能。

## 角色管理

角色管理模块用于管理系统角色和权限分配。

### 角色列表

显示所有角色信息，支持角色的查询和管理操作。

### 角色详情

查看角色的详细信息和关联的权限列表。

## 审批流程

工作流审批模块，处理各类业务审批流程。

### 审批列表

显示待审批和已审批的流程实例，支持审批操作。
```

### 7.2 解析结果示例

**模块结构**:

```json
{
  "modules": [
    {
      "name": "用户管理",
      "level": 2,
      "type": "intermediate",
      "children": [
        {
          "name": "用户列表",
          "level": 3,
          "type": "leaf",
          "pageType": "list",
          "placeholders": {
            "MODULE_NAME": "用户列表",
            "ENTITY_NAME": "用户",
            "API_ENDPOINT": "/api/users",
            "STORE_NAME": "useUserStore"
          }
        },
        {
          "name": "用户详情",
          "level": 3,
          "type": "leaf",
          "pageType": "detail",
          "placeholders": {
            "MODULE_NAME": "用户详情",
            "ENTITY_NAME": "用户",
            "API_ENDPOINT": "/api/users",
            "STORE_NAME": "useUserStore"
          }
        },
        {
          "name": "用户新建",
          "level": 3,
          "type": "leaf",
          "pageType": "create",
          "placeholders": {
            "MODULE_NAME": "用户新建",
            "ENTITY_NAME": "用户",
            "API_ENDPOINT": "/api/users",
            "STORE_NAME": "useUserStore"
          }
        }
      ]
    },
    {
      "name": "角色管理",
      "level": 2,
      "type": "intermediate",
      "children": [
        {
          "name": "角色列表",
          "level": 3,
          "type": "leaf",
          "pageType": "list"
        },
        {
          "name": "角色详情",
          "level": 3,
          "type": "leaf",
          "pageType": "detail"
        }
      ]
    },
    {
      "name": "审批流程",
      "level": 2,
      "type": "intermediate",
      "children": [
        {
          "name": "审批列表",
          "level": 3,
          "type": "leaf",
          "pageType": "workflow"
        }
      ]
    }
  ]
}
```

## 8. 验证测试用例

### 8.1 一致性测试

**测试目标**: 验证相同PRD多次解析结果完全一致

**测试方法**:

1. 使用同一PRD文档执行10次解析
2. 对比所有解析结果的MD5值
3. 验证关键字段的一致性

**预期结果**: 所有解析结果完全相同

### 8.2 边界情况测试

**测试用例1**: 复杂嵌套模块

```markdown
## 系统管理

### 用户管理

#### 用户列表

#### 用户详情

### 角色管理

#### 角色列表
```

**测试用例2**: 特殊字符处理

```markdown
## 1.用户管理【核心模块】

### （一）用户列表功能

### 2.1 用户详情页面
```

**测试用例3**: 英文混合内容

```markdown
## User Management用户管理

### User List用户列表
```

### 8.3 错误恢复测试

**测试用例**: 格式不规范的PRD

- 缺少标题层级
- 模块名称重复
- 页面类型无法识别

**预期行为**: 按照错误处理规则进行处理，生成警告信息

## 9. 实施指南

### 9.1 集成到现有系统

**步骤1**: 将本规则文件加载到核心规范中

```markdown
**每次执行前必须加载以下核心规范:**

1. docs/prompts/frontend/core/architecture-standards.md
2. docs/prompts/frontend/core/component-constraints.md
3. docs/prompts/frontend/core/development-workflow.md
4. docs/prompts/frontend/core/router-standards.md
5. docs/prompts/frontend/core/prd-parsing-standards.md # 新增
```

**步骤2**: 更新技术方案设计助手在`tech-design-assistant.md`中添加PRD解析规范的引用

**步骤3**: 更新任务分解引擎在`task-breakdown-engine.md`中添加模块层级判断规范的引用

### 9.2 质量保证流程

**解析前检查**:

- PRD文档格式验证
- 必要章节完整性检查
- 标题层级结构检查

**解析后验证**:

- 模块结构完整性验证
- 占位符参数有效性验证
- 依赖关系合理性验证

**一致性监控**:

- 记录每次解析的关键指标
- 定期进行一致性回归测试
- 建立解析质量评估机制

---

**重要**: 本规则确保PRD解析的完全确定性，任何修改都必须保持算法的确定性特征。严格按照本规则执行，可将PRD解析的一致性提升至99%以上。
