# 开发流程核心规范

**版本**: v2.2

**适用范围**: 所有前端开发任务

**加载优先级**: 最高

## 标准开发流程

### 开发阶段（强制顺序）

1. 基础设施准备 (API + Adapters + Store)
2. 路由配置 (基于 router-standards.md 规范)
3. 页面开发 (基于模板)
4. 系统集成验证

### 任务执行原则

- **规范优先**: 路由配置遵循 router-standards.md 规范，其他代码使用预设模板
- **占位符替换**: 通过占位符机制确保输出规范
- **分层验证**: 每个阶段完成后进行质量验证
- **依赖检查**: 创建文件前验证所有依赖文件存在

## 模板驱动开发机制

### 页面模板选择规则

根据页面类型参数选择对应的模板文件：

1. **新增页面和编辑页面**: 使用 `add-page.vue` 模板

   - `type="add"`: 新增页面
   - `type="edit"`: 编辑页面

2. **详情页面和审核页面**: 使用 `detail-page.vue` 模板

   - `type="info"`: 详情页面
   - `type="audit"`: 审核页面

3. **列表页面**: 使用 `list-page.vue` 模板

### 模板使用流程

1. 识别页面类型：根据 type 参数确定页面类型
2. 选择对应模板：按照模板选择规则选择标准模板
3. 解析业务参数：模块名、实体名、字段配置等
4. 执行占位符替换：自动替换模板中的占位符
5. 生成最终代码：输出符合规范的代码文件

### 占位符系统

标准占位符：

- `{{MODULE_NAME}}`: 模块名称
- `{{ENTITY_NAME}}`: 实体名称
- `{{API_ENDPOINT}}`: API 接口路径
- `{{PRIMARY_KEY}}`: 主键字段名
- `{{STORE_NAME}}`: Store 名称

### 模板验证机制

- **语法验证**: 确保生成的代码语法正确
- **规范验证**: 确保符合分层架构和组件约束
- **依赖验证**: 确保所有导入的文件存在
- **功能验证**: 确保基本功能可正常运行

## 质量控制机制

### 验证检查点

- **文件验证**: 每创建文件后用 view 工具验证存在性和内容
- **架构检查**: 确保分层调用关系正确
- **组件规范**: 确认 FuniUI 组件使用规范
- **依赖检查**: 创建包含导入的文件前，先验证被导入文件存在
- **路由验证**: 路由配置完成后，验证所有 component 引用的文件存在

### 错误预防机制

- **全局组件使用规则**: 无需导入 src/components 下的组件
- **路由配置验证**: 检查路由配置符合 router-standards.md 规范
- **组件依赖完整性检查**: 创建组件前确保所有依赖组件存在
- **子路由路径检查**: 确保所有子路由使用相对路径

## 任务独立性原则

### 上下文控制

- **核心规范加载**: 每个任务只加载必需的核心规范(<100 行)
- **模板直接应用**: 避免查询大量外部文档
- **完整执行上下文**: 任务文档包含执行所需的全部信息

### 依赖管理

- **前置依赖验证**: 执行前检查所有前置任务的产出
- **并行任务支持**: 同层级任务可以并行执行
- **依赖关系可视化**: 提供清晰的任务依赖关系图

### 会话连续性

- **任务文档自包含**: 每个任务文档包含完整的技术规范
- **状态检查机制**: 自动识别任务进度并继续未完成的工作
- **错误恢复机制**: 提供明确的错误提示和修复建议
