# 招标采购管理系统 - 模块架构设计

**版本**: v1.0
**系统名称**: 招标采购管理
**设计时间**: 2025-01-22

## 系统概述

招标采购管理系统是一个综合性的数字化解决方案，管理从初步规划、采购执行、合同履行到数据分析的整个采购生命周期。

## 模块结构设计

### 一级模块（中间层模块）

#### 1. 工作台
- **模块类型**: 叶子模块
- **页面类型**: 详情页
- **功能**: 个性化首页，提供关键指标和待办任务概览
- **包含功能**: 数据看板、开标日历、我的待办、通知公告

#### 2. 采购计划管理
- **模块类型**: 叶子模块  
- **页面类型**: 列表页 + 工作流
- **功能**: 采购计划全生命周期管理
- **包含功能**: 新增、编辑、删除、查询、审批、委派、导入、导出

#### 3. 采购执行管理
- **模块类型**: 中间层模块
- **子模块**: 7个子模块
- **功能**: 将已批准的采购计划拆分为可执行标段并管理执行过程

##### 3.1 项目标段管理
- **模块类型**: 叶子模块
- **页面类型**: 列表页 + 工作流
- **功能**: 将项目计划拆分为可执行标段

##### 3.2 公告管理
- **模块类型**: 叶子模块
- **页面类型**: 列表页 + 工作流
- **功能**: 创建和管理招标活动的起始公告

##### 3.3 补遗澄清答疑管理
- **模块类型**: 叶子模块
- **页面类型**: 列表页 + 工作流
- **功能**: 发布对招标文件的补充或说明

##### 3.4 评标结果公示管理
- **模块类型**: 叶子模块
- **页面类型**: 列表页 + 工作流
- **功能**: 公示中标候选人名单及相关信息

##### 3.5 中标结果公示管理
- **模块类型**: 叶子模块
- **页面类型**: 列表页 + 工作流
- **功能**: 正式公布最终中标人

##### 3.6 签约履行管理
- **模块类型**: 叶子模块
- **页面类型**: 列表页 + 工作流
- **功能**: 管理合同签订及履约情况

##### 3.7 流标或中止管理
- **模块类型**: 叶子模块
- **页面类型**: 列表页 + 工作流
- **功能**: 发布采购活动终止的公告

#### 4. 供应商管理
- **模块类型**: 叶子模块
- **页面类型**: 列表页
- **功能**: 供应商信息全面管理，提供360度全景视图

#### 5. 项目预警管理
- **模块类型**: 叶子模块
- **页面类型**: 列表页 + 工作流
- **功能**: 对系统触发的风险预警进行查看、处理和跟踪

#### 6. 数据统计分析
- **模块类型**: 中间层模块
- **子模块**: 2个子模块
- **功能**: 提供数据分析和统计报表功能

##### 6.1 数据分析
- **模块类型**: 叶子模块
- **页面类型**: 详情页
- **功能**: 提供交互式仪表盘，进行多维度可视化分析

##### 6.2 统计报表
- **模块类型**: 叶子模块
- **页面类型**: 列表页
- **功能**: 生成并导出详细的、可配置的统计报表

#### 7. 投诉管理
- **模块类型**: 叶子模块
- **页面类型**: 列表页
- **功能**: 对来自外部的正式投诉进行记录、处理和归档

#### 8. 通知公告管理
- **模块类型**: 叶子模块
- **页面类型**: 列表页
- **功能**: 内部对门户网站的通知公告进行内容创建、编辑、发布和撤销

#### 9. 政策法规管理
- **模块类型**: 叶子模块
- **页面类型**: 列表页
- **功能**: 内部对门户网站的政策法规进行内容创建、编辑、发布和撤销

#### 10. 系统设置
- **模块类型**: 中间层模块
- **子模块**: 3个子模块
- **功能**: 系统配置和管理功能

##### 10.1 预警配置
- **模块类型**: 叶子模块
- **页面类型**: 列表页
- **功能**: 对十余种风险点进行规则配置

##### 10.2 用户权限管理
- **模块类型**: 叶子模块
- **页面类型**: 列表页
- **功能**: 对用户、角色及权限进行配置

##### 10.3 审批流程管理
- **模块类型**: 叶子模块
- **页面类型**: 列表页
- **功能**: 定义系统中各类业务的审批流程

## 目录结构设计

```
src/apps/bidding-procurement/
├── routers/index.js                  # 系统级路由
├── modules/
│   ├── dashboard/                    # 工作台
│   │   ├── api/index.js
│   │   ├── adapters/index.js
│   │   ├── views/index.vue
│   │   ├── store.js
│   │   ├── router.js
│   │   └── __mocks__/index.js
│   ├── procurement-plan/             # 采购计划管理
│   │   ├── api/index.js
│   │   ├── adapters/index.js
│   │   ├── views/
│   │   │   ├── list/index.vue
│   │   │   ├── detail/index.vue
│   │   │   └── create/index.vue
│   │   ├── store.js
│   │   ├── router.js
│   │   └── __mocks__/index.js
│   ├── procurement-execution/        # 采购执行管理
│   │   ├── project-section/          # 项目标段管理
│   │   ├── announcement/             # 公告管理
│   │   ├── clarification/            # 补遗澄清答疑管理
│   │   ├── bid-result/               # 评标结果公示管理
│   │   ├── award-result/             # 中标结果公示管理
│   │   ├── contract-performance/     # 签约履行管理
│   │   ├── bid-failure/              # 流标或中止管理
│   │   └── router.js                 # 采购执行管理路由组织
│   ├── supplier/                     # 供应商管理
│   ├── project-warning/              # 项目预警管理
│   ├── data-analysis/                # 数据统计分析
│   │   ├── statistics/               # 统计报表
│   │   ├── analysis/                 # 数据分析
│   │   └── router.js                 # 数据统计分析路由组织
│   ├── complaint/                    # 投诉管理
│   ├── notice/                       # 通知公告管理
│   ├── policy/                       # 政策法规管理
│   └── system-settings/              # 系统设置
│       ├── warning-config/           # 预警配置
│       ├── user-management/          # 用户权限管理
│       ├── approval-workflow/        # 审批流程管理
│       └── router.js                 # 系统设置路由组织
```

## 分层架构约束

### 强制分层架构
严格遵循 API → Adapters → Store → Views 分层架构

### 分层职责
- **API层**: HTTP请求封装，仅调用window.$http
- **Adapters层**: 纯函数数据转换，不调用其他层
- **Store层**: 业务逻辑、状态管理，仅调用API层、Adapters层
- **Views层**: 数据采集、渲染、交互，仅调用Store层

### 调用约束
- **禁止**: 跨层调用、反向调用
- **数据流**: 单向，从上层到下层
- **依赖检查**: 创建文件前验证被导入文件存在

## 模块依赖关系

### 核心依赖
- 采购执行管理的所有子模块依赖采购计划管理
- 项目预警管理依赖采购执行管理
- 数据统计分析依赖所有业务模块
- 系统设置为基础模块，被其他模块依赖

### 模块层级
- **叶子模块**: 执行T001-T004完整任务序列
- **中间层模块**: 仅执行T001路由组织配置

## 技术栈约束

### 核心技术栈
- **框架**: Vue 3 + Composition API + Pinia + ElementPlus + FuniUI
- **网络请求**: window.$http.post、window.$http.fetch
- **语法偏好**: 优先使用JSX/TSX语法

### 组件约束
- **列表页**: 必须使用FuniListPageV2
- **详情页**: 必须使用FuniDetail
- **表单组件**: 必须使用FuniForm
- **工作流组件**: 使用FuniFileTable、FuniAuditButtomBtn、FuniBusAuditDrawer

---

