# 招标采购管理系统 - 模板选择方案

**版本**: v1.0 **系统名称**: 招标采购管理 **设计时间**: 2025-01-22

## 模板选择概述

基于PRD解析标准化规则和页面类型判断规则，为招标采购管理系统的各个模块选择合适的页面模板和代码模板。

## 页面模板选择规则

### 新增/编辑页面模板

**模板路径**: `docs/prompts/frontend/templates/page-templates/add-page.vue`

**适用场景**:

- `type="add"`: 新增页面
- `type="edit"`: 编辑页面

**适用模块**: 所有需要新增或编辑功能的模块

### 详情/审核页面模板

**模板路径**: `docs/prompts/frontend/templates/page-templates/detail-page.vue`

**适用场景**:

- `type="info"`: 详情页面
- `type="audit"`: 审核页面

**适用模块**: 所有需要查看详情或审核功能的模块

### 列表页模板

**模板路径**: `docs/prompts/frontend/templates/page-templates/list-page.vue`

**适用模块**: 所有列表页面，包括：

1. **采购计划管理** - 采购计划列表查询、管理
2. **项目标段管理** - 项目和标段列表管理
3. **公告管理** - 交易公告列表管理
4. **补遗澄清答疑管理** - 补遗澄清答疑列表管理
5. **评标结果公示管理** - 评标结果公示列表管理
6. **中标结果公示管理** - 中标结果公示列表管理
7. **签约履行管理** - 签约履行列表管理
8. **流标或中止管理** - 流标中止列表管理
9. **供应商管理** - 供应商信息列表管理
10. **项目预警管理** - 项目预警列表管理
11. **统计报表** - 统计报表列表展示
12. **投诉管理** - 投诉信息列表管理
13. **通知公告管理** - 通知公告列表管理
14. **政策法规管理** - 政策法规列表管理
15. **预警配置** - 预警配置列表管理
16. **用户权限管理** - 用户权限列表管理
17. **审批流程管理** - 审批流程列表管理

### 工作流页面模板

**模板路径**: `docs/prompts/frontend/templates/page-templates/detail-page.vue`

**适用模块**:

1. **采购计划管理** - 审批流程页面
2. **项目标段管理** - 审批流程页面
3. **公告管理** - 审批流程页面
4. **补遗澄清答疑管理** - 审批流程页面
5. **评标结果公示管理** - 审批流程页面
6. **中标结果公示管理** - 审批流程页面
7. **签约履行管理** - 审批流程页面
8. **流标或中止管理** - 审批流程页面
9. **项目预警管理** - 预警处理流程页面

## 页面类型参数定义

### 标准页面类型参数

- `type="add"`: 新增页面 → 使用 `add-page.vue` 模板
- `type="edit"`: 编辑页面 → 使用 `add-page.vue` 模板
- `type="info"`: 详情页面 → 使用 `detail-page.vue` 模板
- `type="audit"`: 审核页面 → 使用 `detail-page.vue` 模板

## 代码模板选择

### API层模板

**模板路径**: `docs/prompts/frontend/templates/code-templates/api-layer.js`

**适用于所有模块的API层实现**:

- 统一使用window.$http进行HTTP请求
- 标准化的错误处理机制
- RESTful API接口封装

### Store层模板

**模板路径**: `docs/prompts/frontend/templates/code-templates/store-layer.js`

**适用于所有模块的Store层实现**:

- 基于Pinia的状态管理
- 业务逻辑封装
- 数据流控制

### Adapters层模板

**模板路径**: `docs/prompts/frontend/templates/code-templates/adapters-layer.js`

**适用于所有模块的Adapters层实现**:

- 纯函数数据转换
- 数据格式标准化
- 业务数据适配

### 路由配置规范

**规范文档**: `docs/prompts/frontend/core/router-standards.md`

**不使用模板，直接遵循规范**:

- 基于router-standards.md规范进行路由配置
- 确保路由结构的标准化和一致性

## 占位符参数配置

### 核心模块参数

#### 1. 采购计划管理

```javascript
{
  "{{MODULE_NAME}}": "采购计划管理",
  "{{ENTITY_NAME}}": "采购计划",
  "{{API_ENDPOINT}}": "/api/procurement-plans",
  "{{STORE_NAME}}": "useProcurementPlanStore",
  "{{PRIMARY_KEY}}": "id",
  "{{DISPLAY_FIELD}}": "planName"
}
```

#### 2. 项目标段管理

```javascript
{
  "{{MODULE_NAME}}": "项目标段管理",
  "{{ENTITY_NAME}}": "项目标段",
  "{{API_ENDPOINT}}": "/api/project-sections",
  "{{STORE_NAME}}": "useProjectSectionStore",
  "{{PRIMARY_KEY}}": "id",
  "{{DISPLAY_FIELD}}": "sectionName"
}
```

#### 3. 公告管理

```javascript
{
  "{{MODULE_NAME}}": "公告管理",
  "{{ENTITY_NAME}}": "公告",
  "{{API_ENDPOINT}}": "/api/announcements",
  "{{STORE_NAME}}": "useAnnouncementStore",
  "{{PRIMARY_KEY}}": "id",
  "{{DISPLAY_FIELD}}": "title"
}
```

#### 4. 供应商管理

```javascript
{
  "{{MODULE_NAME}}": "供应商管理",
  "{{ENTITY_NAME}}": "供应商",
  "{{API_ENDPOINT}}": "/api/suppliers",
  "{{STORE_NAME}}": "useSupplierStore",
  "{{PRIMARY_KEY}}": "id",
  "{{DISPLAY_FIELD}}": "supplierName"
}
```

#### 5. 项目预警管理

```javascript
{
  "{{MODULE_NAME}}": "项目预警管理",
  "{{ENTITY_NAME}}": "项目预警",
  "{{API_ENDPOINT}}": "/api/project-warnings",
  "{{STORE_NAME}}": "useProjectWarningStore",
  "{{PRIMARY_KEY}}": "id",
  "{{DISPLAY_FIELD}}": "warningName"
}
```

#### 6. 投诉管理

```javascript
{
  "{{MODULE_NAME}}": "投诉管理",
  "{{ENTITY_NAME}}": "投诉",
  "{{API_ENDPOINT}}": "/api/complaints",
  "{{STORE_NAME}}": "useComplaintStore",
  "{{PRIMARY_KEY}}": "id",
  "{{DISPLAY_FIELD}}": "complaintTitle"
}
```

#### 7. 通知公告管理

```javascript
{
  "{{MODULE_NAME}}": "通知公告管理",
  "{{ENTITY_NAME}}": "通知公告",
  "{{API_ENDPOINT}}": "/api/notices",
  "{{STORE_NAME}}": "useNoticeStore",
  "{{PRIMARY_KEY}}": "id",
  "{{DISPLAY_FIELD}}": "title"
}
```

#### 8. 政策法规管理

```javascript
{
  "{{MODULE_NAME}}": "政策法规管理",
  "{{ENTITY_NAME}}": "政策法规",
  "{{API_ENDPOINT}}": "/api/policies",
  "{{STORE_NAME}}": "usePolicyStore",
  "{{PRIMARY_KEY}}": "id",
  "{{DISPLAY_FIELD}}": "title"
}
```

## 组件使用约束

### 强制使用组件

#### 页面级组件

- **列表页**: 必须使用`FuniListPageV2`
- **详情页**: 必须使用`FuniDetail`
- **表单组件**: 必须使用`FuniForm`，禁止使用el-form

#### 业务组件

- **角色选择**: `FuniRUOC` (type="role")
- **用户选择**: `FuniRUOC` (type="user")
- **部门选择**: `FuniRUOC` (type="org")

#### 工作流组件

- **工作流附件**: `FuniFileTable` (需businessId)
- **审核按钮**: `FuniAuditButtomBtn`
- **审核抽屉**: `FuniBusAuditDrawer`

#### 文件组件

- **工作流附件**: `FuniFileTable` (有businessId的工作流场景)
- **普通文件上传**: `FuniFileTable/upload.vue` (非工作流场景)

### 全局组件规则

- src/components下所有组件已在main.js中注册为全局组件，无需导入
- 禁止在业务代码中导入src/components下的任何组件
- 模板使用kebab-case: `<funi-detail>`、`<funi-list-page-v2>`

## 模板验证机制

### 模板完整性验证

- 确保所有占位符都被正确替换
- 验证模板文件存在性
- 检查参数配置完整性

### 规范符合性验证

- 确保生成的代码符合分层架构规范
- 验证组件使用符合约束要求
- 检查路由配置符合标准

### 依赖关系验证

- 验证所有导入的文件存在
- 检查模块间依赖关系正确
- 确保API接口调用规范

---

**重要**: 本模板选择方案严格基于PRD解析标准化规则，确保模板选择的一致性和准确性。所有模板应用都必须遵循占位符替换规则和组件使用约束。
