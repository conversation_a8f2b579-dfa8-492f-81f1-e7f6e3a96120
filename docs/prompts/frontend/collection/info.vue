<!--
 * @Author: coutinho <EMAIL>
 * @Date: 2023-07-24 14:27:35
 * @LastEditors: tao.yang <EMAIL>
 * @LastEditTime: 2025-07-24 00:47:32
 * @FilePath: /docs/prompts/frontend/collection/info.vue
 * @Description:
-->

<template>
  <div>
    <funi-detail
      :bizName="bizName"
      :auditButtons="buttons"
      :steps="steps"
      :showWorkflow="true"
      :detailHeadOption="detailHeadOption || {}"
      @auditEvent="auditEvent"
      :beforeAuditFn="beforeAuditFn"
      :businessId="['info', 'audit'].includes(type) && businessId ? businessId : void 0"
    />
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, inject, onMounted, watch, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import BaseInfo from './../component/collection/baseInfo.vue';
import IncomeList from '../component/collection/incomeList.vue';
import InvoiceList from '../component/collection/invoiceList.vue';
import ReceiptList from '../component/collection/receiptList.vue';
import { ermGlobalApi } from '@/apps/erm/config/config.jsx';
import { auditEndFunc } from '@/apps/erm/config/business.js';
import { useMultiTab } from '@/utils/hooks/useMultiTab';
const multiTab = useMultiTab();

import { unref } from 'vue';
const route = useRoute();
const router = useRouter();
const buttons = ref([]);
const _route = ref(route.query);
const type = ref(route.query.type);
const id = ref(route.query.id);
const bizName = ref(route.query.bizName);
const businessId = ref('');
const infoData = ref();

const contractSn = ref('');
const baseDom = ref();
const steps = computed(() => {
  let arr = [
    {
      title: '基本信息',
      preservable: true,
      type: BaseInfo,
      props: {
        id: id.value,
        isEdit: !['audit', 'info'].includes(type.value),
        type: type.value,
        businessId: businessId.value,
        ref: refFun
      },
      on: {
        updateID: ({ id: v, businessId: bv }) => {
          id.value = v;
          businessId.value = bv;
        },
        infoUpdate: data => {
          infoData.value = data;
          let { id: v, businessId: bv } = data;
          id.value = v;
          businessId.value = bv;
          contractSn.value = data.contractSn;
        }
      }
    },
    {
      title: '收入确认表',
      preservable: true,
      type: IncomeList,
      props: {
        contractSn: contractSn.value
      }
    },
    {
      title: '收款单',
      preservable: true,
      type: ReceiptList,
      props: {
        contractSn: contractSn.value
      }
    },
    {
      title: '开票申请',
      preservable: true,
      type: InvoiceList,
      props: {
        contractSn: contractSn.value
      }
    },
    ...(businessId.value && (route.query.dicBusinessTypeCode || unref(infoData)?.dicBusinessTypeCode)
      ? [
          {
            title: '办件记录',
            type: 'FuniWorkRecord',
            props: {
              objectListUrl: ermGlobalApi.queryBusinessUserRecordInfo,
              busListUrl: ermGlobalApi.queryBusinessUserRecordInfo,
              params: {
                businessId: businessId.value,
                dicBusinessTypeCode: route.query.dicBusinessTypeCode || unref(infoData)?.dicBusinessTypeCode
              }
            },
            on: {
              onClick
            }
          }
        ]
      : [])
  ];
  return arr;
});
const addIncome = () => {
  let { dicContractStatusCode, id, contractName } = infoData.value;
  router.push({
    name: 'ermContractIncomeAdd',
    query: {
      bizName: '新建',
      type: 'add',
      tab: '收入确认表-新建',
      cId: dicContractStatusCode == '1' ? id : void 0,
      cName: dicContractStatusCode == '1' ? contractName : void 0
    }
  });
};
const addInvoice = () => {
  let { dicContractStatusCode, id, contractName } = infoData.value;
  router.push({
    name: 'ermContractInvoiceAdd',
    query: {
      bizName: '新建',
      type: 'add',
      tab: '开票申请-新建',
      cId: dicContractStatusCode == '1' ? id : void 0,
      cName: dicContractStatusCode == '1' ? contractName : void 0
    }
  });
};
const print = () => {
  router.push({
    name: 'ermContractCollectionPrint',
    query: {
      id: id.value
    }
  });
};
const copy = () => {
  router.push({
    name: 'ermContractCollectionAdd',
    query: {
      bizName: '新建',
      type: 'add',
      tab: ['收款合同', unref(infoData).contractName || '', '新建'].join('-'),
      id: id.value,
      title: unref(infoData).contractName || '收款合同'
    }
  });
};
const refFun = e => {
  baseDom.value = e;
};

const beforeAuditFn = async ({ businessExecutionType }) => {
  if (
    ['AGREE', 'SUBMIT'].includes(businessExecutionType) &&
    ['ERM_COLLECTION_CONTRACT_ADD', 'ERM_COLLECTION_CONTRACT_CHANGE'].includes(unref(infoData).dicBusinessTypeCode)
  ) {
    try {
      return await unref(baseDom).authFun();
    } catch {
      return Promise.reject();
    }
  }
  return Promise.resolve({});
};
const detailHeadOption = computed(() => {
  let obj = {
    title: route.query.title,
    no: infoData.value?.businessId,
    status: infoData.value?.businessNode,
    statusName: '节点',
    btns: [
      {
        name: '发起收入确认表',
        props: { type: 'text' },
        on: { click: addIncome }
      },
      {
        name: '发起开票申请',
        props: { type: 'text' },
        on: { click: addInvoice }
      },
      ...(type.value === 'info'
        ? [
            {
              name: '打印',
              props: { type: 'text' },
              on: { click: print }
            },
            {
              name: '复制',
              props: { type: 'text' },
              on: { click: copy },
              menuAuth: 'ERM_CONTRACT_COLLECTION_COPY'
            }
          ]
        : [])
    ]
  };
  return obj;
});

const auditEvent = () => {
  auditEndFunc(() => {
    multiTab.closeCurrentPage();
  });
};
const onClick = row => {
  router.push({
    name: 'ermContractCollectionInfo',
    query: {
      title: row.contractName || '收款合同',
      bizName: '详情',
      type: 'info',
      id: row.dicCollContractDoingBusCode ? row.lastId || row.id : row.id,
      tab: ['收款合同', row.contractName || '', '详情'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};
</script>
