/*
 * @Author: coutinho <EMAIL>
 * @Date: 2023-08-14 14:31:58
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2023-10-26 16:21:53
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/hooks/collection/api.js
 * @Description:
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */
export const apiUrl = {
  queryCollectionContractList: '/erm/collectionContractList/queryCollectionContractList',
  queryCustomerList: '/erm/customerList/queryCustomerList', //甲方
  queryProjectListByName: '/erm/projectManagementList/queryProjectListByName',
  // queryProjectListForContract: '/erm/projectManagementList/queryProjectListForContract',
  queryProjectListForContract: '/erm/projectManagementList/queryValidProjectListByUserDeptInfo',

  queryCustomerListByCurrentEmployee: '/erm/customerList/queryPartBCustomerListByCurrentEmployee', //项目乙方
  findCompanyByName: '/erm/company/findCompanyByName',
  otherContractExt: '/erm/otherContractExt/page',
  // querySealConMultipartyInfoList: '/erm/serviceBaseList/querySealConMultipartyInfoList',
  collectionContractNew: '/erm/collectionContract/new',
  collectionContractInfo: '/erm/collectionContract/info',
  collectionContractDel: '/erm/collectionContract/delete',
  creatBus: '/erm/collectionContract/creatBus',
  queryPaymentContractListExport: '/erm/collectionContractList/queryCollectionContractListExport',

  queryFapiaoListByContractSn: '/erm/issueFapiaoApplyList/queryFapiaoListByContractSn', //开票列表-用于收款合同页签展示
  queryIncomeConfirmInfoListByContract: '/erm/incomeConfirmInfoList/queryIncomeConfirmInfoListByContract', //收入确认表查询列表-用于收款合同页签展示
  queryReceiptListByContract: '/erm/receiptList/queryReceiptListByContract', //收款单列表-用于收款合同页签展示
  format_contract: '/erm/dicList/format_contract',
  updateEmployeeId: '/erm/collectionContract/updateEmployeeId', //调整负责人
  canChange: '/erm/collectionService/canChange', //获取服务是否可变更
  queryPayConMultipartyInfoList: '/erm/payConMultipartyInfoList/queryPayConMultipartyInfoList', // //
  changeContractRelatedParties: '/erm/payConMultipartyInfo/new', //调整合同相关方
  canChangeAll: '/erm/collectionContract/canChangeAll', //合同变更时 是否能全部变更
  queryServiceBaseVoList: '/erm/serviceBaseList/queryServiceBaseVoList',

  queryAdjustServiceList: '/erm/collectionAdjustList/queryAdjustServiceList', //待调整服务列表
  getSignDateConfig: '/erm/collectionContract/isRequiredSignDate', //是否必填签约时间
  getInitiatorNode: '/erm/collectionContract/isInitiatorNode' //是否到达发起人节点
};

export const queryCollectionContractListHttp = params => {
  return $http.post(apiUrl.queryCollectionContractList, params);
};
export const queryCustomerListByCurrentEmployeeHttp = params => {
  return $http.post(apiUrl.queryCustomerListByCurrentEmployee, params);
};

export const findCompanyByNameHttp = params => {
  return $http.post(apiUrl.findCompanyByName, params);
};
export const queryServiceBaseVoListHttp = params => {
  return $http.post(apiUrl.queryServiceBaseVoList, params);
};
export const collectionContractNewHttp = params => {
  return $http.post(apiUrl.collectionContractNew, params);
};
export const collectionContractInfoHttp = params => {
  return $http.fetch(apiUrl.collectionContractInfo, params);
};
export const collectionContractDelHttp = params => {
  return $http.fetch(apiUrl.collectionContractDel, params);
};
export const creatBusHttp = params => {
  return $http.post(apiUrl.creatBus, params);
};

export const queryFapiaoListByContractSnHttp = params => {
  return $http.post(apiUrl.queryFapiaoListByContractSn, params);
};
export const queryIncomeConfirmInfoListByContractHttp = params => {
  return $http.post(apiUrl.queryIncomeConfirmInfoListByContract, params);
};

export const queryReceiptListByContractHttp = params => {
  return $http.post(apiUrl.queryReceiptListByContract, params);
};
export const formatContractHttp = params => {
  return $http.fetch(apiUrl.format_contract, params);
};
export const updateEmployeeIdHttp = params => {
  return $http.post(apiUrl.updateEmployeeId, params);
};
export const canChangeHttp = params => {
  return $http.post(apiUrl.canChange, params);
};
export const queryPayConMultipartyInfoListHttp = params => {
  return $http.post(apiUrl.queryPayConMultipartyInfoList, params);
};
export const changeContractRelatedPartiesHttp = params => {
  return $http.post(apiUrl.changeContractRelatedParties, params);
};
export const canChangeAllHttp = params => {
  return $http.fetch(apiUrl.canChangeAll, params);
};
export const queryAdjustServiceListHttp = params => {
  return $http.post(apiUrl.queryAdjustServiceList, params);
};

export const getSignDateConfigHttp = params => {
  return $http.fetch(apiUrl.getSignDateConfig, params);
};
export const getInitiatorNodeHttp = params => {
  return $http.fetch(apiUrl.getInitiatorNode, params);
};
