<template>
  <div>
    <funi-detail :bizName="bizName" :steps="steps" :detailHeadOption="detailHeadOption || {}" />
    <SubmitSuccess ref="submitSuccessRef" />
  </div>
</template>
<script setup lang="jsx">
import { useRoute } from 'vue-router';
import { ref, computed, unref } from 'vue';
import { useBusiness } from '@/hooks/useBusiness';
import BaseInfo from './../component/collection/baseInfo.vue';
import SubmitSuccess from '@/apps/erm/component/submit_success/index.vue';

const route = useRoute();
const business = useBusiness();

const id = ref();
const businessId = ref();
const infoData = ref();
const _route = ref(route.query);
id.value = unref(_route).id;
const bizName = unref(_route).bizName;
const submitSuccessRef = ref();
const steps = computed(() => {
  const { bizName, type, dicBusinessTypeCode, reason, copy } = unref(_route);
  let change_end_cancel = ['change_add', 'end_add', 'cancel_add'].includes(type);
  let isEdit = !['audit', 'info', 'end_add', 'end_edit', 'cancel_add', 'cancel_edit'].includes(type);
  let arr = [
    {
      title: '基本信息',
      preservable: true,
      type: BaseInfo,
      props: {
        id: id.value,
        isEdit,
        type,
        dicBusinessTypeCode,
        bizName,
        reason,
        copy,
        lastId: ['新建', 'add'].includes(bizName) && change_end_cancel ? unref(_route).id : ''
      },
      on: {
        updateID: ({ id: v, businessId: bv }) => {
          id.value = v;
          businessId.value = bv;
        },
        infoUpdate: data => {
          infoData.value = data;
          let { id: v, businessId: bv } = data;
          id.value = v;
          businessId.value = bv;
        },
        otherUpdate: object => {
          Object.keys(object).forEach(key => {
            infoData.value[key] = object[key] || infoData.value[key];
          });
        }
      }
    },
    {
      title: '要件信息',
      preservable: false,
      type: 'FuniFileTable',
      props: {
        params: {
          businessId: businessId.value
        },
        callbackFun: submitBusinessFunc
      }
    }
  ];
  return arr;
});

const businessName = computed(() => {
  let { companyName, deptName, contractName } = unref(infoData);
  let str = `${companyName}-${deptName}-${contractName}`;
  return str;
});

const submitBusinessFunc = async () => {
  let eum = {
    add: 'ERM_COLLECTION_CONTRACT_ADD',
    change_add: 'ERM_COLLECTION_CONTRACT_CHANGE',
    end_add: 'ERM_COLLECTION_CONTRACT_TERMINATE',
    cancel_add: 'ERM_COLLECTION_CONTRACT_CANCEL'
  };
  let code = '';
  if (bizName.value === '新建' || bizName.value === 'add') {
    code = eum[_route.type];
  } else {
    code = _route.dicBusinessTypeCode;
  }

  await submitBusiness(code, businessId.value, 'SUBMIT', unref(businessName)).finally(() => {});
  submitSuccessRef.value.show();
};

const detailHeadOption = computed(() => {
  let obj = {
    title: _route.title,
    no: infoData.value?.businessId,
    status: infoData.value?.businessNode,
    statusName: '节点'
  };
  return obj;
});
</script>
