<template>
  <funi-dialog
    v-model="dialogVisible"
    title="请输入并选择合同负责人"
    size="small"
    :close-on-click-modal="true"
    :hideFooter="null"
  >
    <div style="min-height: 100px">
      <FuniForm :schema="schema" @get-form="setForm" :border="false" :rules="rules" />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="default" @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="onOk">确定</el-button>
      </span>
    </template>
  </funi-dialog>
</template>

<script setup lang="jsx">
import { ref, computed, unref, nextTick } from 'vue';
import InfiniteSelect from '@/apps/erm/component/infiniteSelect/index.vue';
import { ermGlobalApi } from '@/apps/erm/config/config.jsx';
import { updateEmployeeIdHttp } from '../../hooks/collection/api';
const emit = defineEmits(['output']);
const dialogVisible = ref(false);
const loading = ref(false);
const form = ref();
const currentRow = ref();
const setForm = e => {
  form.value = e;
};

// 表单的  schema
const schema = computed(() => {
  return [
    {
      label: '负责人',
      component: () => (
        <InfiniteSelect
          api={ermGlobalApi.queryEmployeeList}
          defaultProps={{
            keyWord: 'keyword',
            name: 'employeeName',
            id: 'id'
          }}
        ></InfiniteSelect>
      ),
      prop: 'employeeObj'
    }
  ];
});
const rules = unref({
  employeeObj: [{ required: true, message: '必填', trigger: 'blur' }]
});

const show = row => {
  currentRow.value = row;
  dialogVisible.value = true;
  nextTick(() => {
    form.value.setValues({
      employeeObj: {
        id: row.employeeId,
        name: row.employeeName
      }
    });
  });
};
async function onOk() {
  try {
    let { isValid } = await form.value.validate();
    if (!isValid) return;
    const { employeeObj } = form.value.getValues();
    loading.value = true;
    await updateEmployeeIdHttp({
      employeeId: employeeObj.id,
      id: currentRow.value?.id
    });
    dialogVisible.value = false;
    emit('output', true);
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
}

defineExpose({ show });
</script>

<style lang="scss" scoped></style>
