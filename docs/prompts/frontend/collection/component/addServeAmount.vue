<template>
  <div>
    <funi-dialog v-model="dialogVisible" size="large" title="选择服务" @close="cancelClick">
      <div>
        <FuniCurd
          ref="funiCurd"
          @get-curd="
            e => {
              funi_curd = e;
            }
          "
          max-height="calc(50vh - 40px)"
          :columns="columnsProject"
          :useSearchV2="false"
          :isShowSearch="true"
          @row-click="getData"
          @select="getDatas"
          @select-all="getDatas"
          :row-class-name="tableRowClassName"
          size="small"
          :searchConfig="searchConfig"
          :lodaData="lodaData"
          :pagination="false"
        >
        </FuniCurd>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelClick">取消</el-button>
          <el-button type="primary" :disabled="rows.length === 0" @click="confirmFunc"> 确定 </el-button>
        </span>
      </template>
    </funi-dialog>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, nextTick, unref } from 'vue';
import { dicCode } from '@/apps/erm/config/config.jsx';
import { queryAdjustServiceListHttp } from '@/apps/erm/contract/hooks/collection/api';
import ErmSelect from '@/apps/erm/component/ermSelect/index.vue';
const funi_curd = ref(null);
const dialogVisible = ref(false);
const funiCurd = ref(void 0);
const rows = ref([]);
const otherParams = ref({});
const selected = ref([]);
const columnsProject = computed(() => {
  return [
    {
      type: 'selection',
      width: '55px',
      fixed: 'left',
      selectable: (row, index) => {
        return !selected.value.find(item => item.serviceSn === row.serviceSn && item.contractSn === row.contractSn);
      }
    },
    {
      label: '合同编号',
      prop: 'contractSn',
      minWidth: '120px'
    },
    {
      label: '来源',
      prop: 'dicServiceSourceName'
    },
    {
      label: '服务内容',
      prop: 'dicServiceContentName'
    },
    {
      label: '是否开放性条款',
      prop: 'isOpenClause',
      minWidth: '120px',
      render: ({ row, index }) => {
        return <span>{row.isOpenClause ? '是' : '否'}</span>;
      }
    },
    {
      label: '签约类型',
      prop: 'dicSignTypeName'
    },
    {
      label: '确收方式',
      prop: 'dicConfirmModeName'
    },

    {
      label: '确收总金额（元）',
      prop: 'confirmReceipt',
      align: 'right',
      minWidth: '120px'
    },
    {
      label: '收款总金额（元）',
      prop: 'accumulatReceived',
      align: 'right',
      minWidth: '120px'
    },
    {
      label: '已开票金额（元）',
      prop: 'invoiceAmount',
      align: 'right',
      minWidth: '120px'
    },
    {
      label: '金额',
      prop: 'includeTaxAmount',
      align: 'right'
    },
    {
      label: '税率',
      prop: 'taxRate',
      align: 'right'
    }
  ];
});
const searchConfig = computed(() => {
  let obj = {
    schema: [
      {
        label: '服务内容',
        component: () => <ErmSelect />,
        prop: 'dicServiceContentCode',
        props: {
          placeholder: '请选择',
          code: dicCode.service_content,
          filterable: true
        }
      },
      {
        label: '确收方式',
        component: () => <ErmSelect />,
        prop: 'dicConfirmModeCode',
        props: {
          placeholder: '请选择',
          code: dicCode.confirm_mode,
          filterable: true
        }
      },
      {
        label: '合同编号',
        component: () => <el-input></el-input>,
        prop: 'serviceContract',
        props: {
          placeholder: '请输入合同编号'
        }
      }
    ]
  };
  return obj;
});
const emit = defineEmits(['exportArray']);

const getData = ({ selection }) => {
  rows.value = selection;
};

const getDatas = v => {
  rows.value = v;
};
/**
 *
 * @param {*} page
 * @param {*} params
 */
const lodaData = async (page, params) => {
  const data = await queryAdjustServiceListHttp({ ...page, ...params, ...otherParams.value });
  return data;
};
const show = (params, arr) => {
  dialogVisible.value = true;
  otherParams.value = params;
  selected.value = arr;
};
const tableRowClassName = ({ row }) => {
  if (selected.value.find(v => v.serviceSn === row.serviceSn && v.contractSn === row.contractSn)) {
    return 'disabled-row';
  }
  return '';
};

const confirmFunc = () => {
  emit('exportArray', rows.value);
  cancelClick();
};
const cancelClick = () => {
  dialogVisible.value = false;
  rows.value = [];
};
defineExpose({
  show
});
</script>
<style scoped>
@import url('@/apps/erm/config/table_disabled.css');
</style>
