<template>
  <FuniCurdV2
    ref="curdRef"
    :isShowSearch="true"
    :queryFields="queryFields"
    :sortFields="sortFields"
    :columnFilters="columnFilters"
    :columns="columns"
    :loading="loading"
    :lodaData="lodaData"
  >
  </FuniCurdV2>
</template>

<script setup lang="jsx">
import { ref, computed, watch, onMounted, reactive } from 'vue';
import { queryIncomeConfirmInfoListByContractHttp } from '../../hooks/collection/api';
import { useAppStore } from '@/stores/useAppStore';
import { useRouter } from 'vue-router';
const props = defineProps({
  contractSn: {
    type: String,
    default: ''
  }
});
const loading = ref(false);
const curdRef = ref();
const queryFields = ref([]);
const sortFields = ref([]);
const columnFilters = ref([]);
const appStore = useAppStore();
const router = useRouter();
const searchConfig = reactive({
  pageCode: 'ermContCollectIncomeList'
});

const columns = ref([
  {
    label: '收入确认表编号',
    prop: 'incomeSn',
    render: ({ row, index }) => {
      return (
        <el-button type="primary" link onClick={() => goDetail(row)}>
          {row.incomeSn}
        </el-button>
      );
    }
  },
  {
    label: '确认表状态',
    prop: 'dicIncomeStatusName'
  },
  {
    label: '生效时间',
    prop: 'takeEffectTime'
  },
  {
    label: '服务内容',
    prop: 'dicServiceContentName'
  },
  {
    label: '确收方式',
    prop: 'dicConfirmModeName'
  },

  {
    label: '确收金额-含税(元)',
    prop: 'includeTaxAmount'
  },
  {
    label: '负责人',
    prop: 'employeeName'
  },
  {
    label: '归属公司',
    prop: 'companyName'
  },
  {
    label: '归属部门',
    prop: 'deptName'
  },
  {
    label: '项目名称',
    prop: 'projectName'
  },
  {
    label: '客户名称',
    prop: 'customerName'
  }
]);

onMounted(() => {
  fetchAdvancedQueryConfig();
});
watch(
  () => props.contractSn,
  () => {
    curdRef.value.reload({ resetPage: true });
  }
);
/**
 * 跳转详情
 */
const goDetail = row => {
  router.push({
    name: 'ermContractIncomeInfo',
    query: {
      title: row.contractName,
      bizName: '详情',
      type: 'info',
      id: row.id,
      tab: ['收入确认表', row.contractName || '', '详情'].join('-'),
      businessId: row.businessId,
      dicBusinessTypeCode: row.dicBusinessTypeCode
    }
  });
};
/**
 *
 * @param {*} page
 * @param {*} params
 */
const lodaData = async (page, params) => {
  try {
    if (!props.contractSn) {
      return { list: [] };
    }
    loading.value = true;
    const resData = await queryIncomeConfirmInfoListByContractHttp({
      contractSn: props.contractSn,
      ...page,
      ...params
    });
    return resData;
  } finally {
    loading.value = false;
  }
};
/**
 * 查询高级查询、列排序配置
 */
function fetchAdvancedQueryConfig() {
  const advQueryURL = `${searchConfig.service || appStore.service}/advQueryList/getAdvancedQueryList`;
  $http.fetch(advQueryURL, { pageCode: searchConfig.pageCode }).then(res => {
    queryFields.value = (res.list || []).sort((a, b) => a.sort - b.sort);
    sortFields.value = res.sort || [];
    columnFilters.value = res.column || [];
  });
}
</script>

<style lang="scss" scoped></style>
