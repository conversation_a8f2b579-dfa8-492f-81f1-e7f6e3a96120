<!--
 * @Author: coutinh<PERSON> <EMAIL>
 * @Date: 2023-08-02 11:19:02
 * @LastEditors: coutinho <EMAIL>
 * @LastEditTime: 2024-01-31 00:04:03
 * @FilePath: /funi-cloud-erm-ui/src/apps/erm/contract/component/collection/baseInfo.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="beasInfo_banner">
    <div
      :style="{ order: type === 'info' || type === 'audit' ? '3' : '1' }"
      v-if="type.indexOf('end') > -1 || ((type === 'info' || type === 'audit') && collectionAdjust)"
    >
      <GroupTitle title="调整合同金额" />
      <funi-form
        :schema="aschema"
        @get-form="e => (from01 = e)"
        :rules="rules"
        :col="type === 'info' || type === 'audit' ? 3 : 2"
        :border="false"
      />
      <GroupTitle title="调整服务金额" />
      <funi-curd
        ref="sAmountCurd"
        :rowSelection="false"
        :columns="sAmountColumns"
        :data="sAmountData"
        :pagination="false"
      >
        <template #header>
          <div class="serve-amount-head">
            <span>提示：请填写合同及子合同服务调整后的确收金额、收款金额、已开票金额。</span>
            <div v-if="['end_add', 'end_edit'].includes(props.type)">
              <el-button type="primary" @click="serveAmountAddFn">添加</el-button>
            </div>
          </div>
        </template>
        <template #a>
          <div class="musterW"><span>*</span>调整后确收金额（元）</div>
        </template>
        <template #b>
          <div class="musterW"><span>*</span>调整后收款金额（元）</div>
        </template>
        <template #c>
          <div class="musterW"><span>*</span>调整后已开票金额（元）</div>
        </template>
      </funi-curd>
    </div>

    <div :style="{ order: '2' }">
      <GroupTitle title="基本信息" />
      <funi-form :schema="schema" @get-form="setForm" :rules="rules" :col="3" :border="false" />
    </div>
    <div :style="{ order: '4' }">
      <GroupTitle v-if="crData_change !== false && !isMultipartyContract" title="合同相关方" isSymbol></GroupTitle>
      <ContractRelated ref="contract_related" :is-edit="isEdit" :list="crData" v-if="isMultipartyContract" isSymbol />
      <ChangCompareTable v-if="crData_change !== false">
        <div>
          <ContractRelated :showTitle="false" :is-edit="false" :list="crData_change" />
        </div>
      </ChangCompareTable>
    </div>
    <div :style="{ order: '5' }">
      <GroupTitle title="服务明细" isSymbol>
        <div v-if="isEdit">
          <el-button type="primary" @click="addFunc">添加</el-button>
        </div>
      </GroupTitle>
      <funi-curd ref="curd1" :rowSelection="false" :columns="sColumns" :data="cData" :pagination="false">
        <template #a>
          <div class="musterW"><span>*</span>来源</div>
        </template>
        <template #b>
          <div class="musterW"><span>*</span>服务内容</div>
        </template>
        <template #c>
          <div class="musterW"><span>*</span>是否开放性条款</div>
        </template>
        <template #d>
          <div class="musterW"><span>*</span>签约类型</div>
        </template>
        <template #e>
          <div class="musterW"><span>*</span>确收方式</div>
        </template>
        <template #f>
          <div class="musterW"><span>*</span>含税金额(元)</div>
        </template>
        <template #g>
          <div class="musterW"><span>*</span>实际开始时间</div>
        </template>
        <template #h>
          <div class="musterW"><span>*</span>实际结束时间</div>
        </template>
        <template #l>
          <div class="musterW"><span>*</span>税率</div>
        </template>
      </funi-curd>
      <ChangCompareTable v-if="cData_change !== false">
        <div>
          <funi-curd ref="curd1" :rowSelection="false" :columns="sColumns" :data="cData_change" :pagination="false">
            <template #a>
              <div class="musterW"><span>*</span>来源</div>
            </template>
            <template #b>
              <div class="musterW"><span>*</span>服务内容</div>
            </template>
            <template #c>
              <div class="musterW"><span>*</span>是否开放性条款</div>
            </template>
            <template #d>
              <div class="musterW"><span>*</span>签约类型</div>
            </template>
            <template #e>
              <div class="musterW"><span>*</span>确收方式</div>
            </template>
            <template #f>
              <div class="musterW"><span>*</span>含税金额(元)</div>
            </template>
            <template #g>
              <div class="musterW"><span>*</span>实际开始时间</div>
            </template>
            <template #h>
              <div class="musterW"><span>*</span>实际结束时间</div>
            </template>
            <template #l>
              <div class="musterW"><span>*</span>税率</div>
            </template>
          </funi-curd>
        </div>
      </ChangCompareTable>
    </div>
    <div :style="{ order: '6' }">
      <GroupTitle title="收款明细">
        <div v-if="isEdit">
          <el-button type="primary" @click="addFuncOne">添加</el-button>
        </div>
      </GroupTitle>
      <funi-curd :rowSelection="false" :columns="scColumns" :data="scData" :pagination="false">
        <template #a>
          <div class="musterW"><span>*</span>应收编号</div>
        </template>
        <template #b>
          <div class="musterW"><span>*</span>收款条件</div>
        </template>
        <template #c>
          <div class="musterW"><span>*</span>收款金额(元)</div>
        </template>
      </funi-curd>

      <ChangCompareTable v-if="scData_change !== false">
        <div>
          <funi-curd :rowSelection="false" :columns="scColumns" :data="scData_change" :pagination="false">
            <template #a>
              <div class="musterW"><span>*</span>应收编号</div>
            </template>
            <template #b>
              <div class="musterW"><span>*</span>收款条件</div>
            </template>
            <template #c>
              <div class="musterW"><span>*</span>收款金额(元)</div>
            </template>
          </funi-curd>
        </div>
      </ChangCompareTable>
    </div>
    <div :style="{ order: '7' }">
      <GroupTitle title="商机">
        <div v-if="isEdit">
          <el-button type="primary" @click="addFuncTwo">添加</el-button>
        </div>
      </GroupTitle>
      <funi-curd :rowSelection="false" :columns="bColumns" :data="bData" :pagination="false" />

      <ChangCompareTable v-if="bData_change !== false">
        <div>
          <funi-curd :rowSelection="false" :columns="bColumns" :data="bData_change" :pagination="false"> </funi-curd>
        </div>
      </ChangCompareTable>
    </div>
    <div :style="{ order: '8' }" v-if="['info', 'audit'].includes(type) && businessId">
      <GroupTitle title="要件信息"> </GroupTitle>
      <FuniFileTable :onlyShow="type == 'info'" ref="funiFileTable" :params="{ businessId }"></FuniFileTable>
    </div>

    <AddServeAmount ref="serverAmountRef" @exportArray="_addServeAmountData"></AddServeAmount>
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, onMounted, nextTick, unref, inject, reactive } from 'vue';
import GroupTitle from '@/apps/erm/component/groupTitle/index.vue';
import ChangCompareTable from '@/apps/erm/component/changCompareTable/index.vue';
import {
  useFormSchema,
  serveColumns,
  serveAmountColumns,
  busColumns,
  collection,
  useRule,
  getCDataFields,
  gerScDataFields,
  getBDataFields,
  adjustSchema
} from './../../hooks/collection/baseInfo.jsx';
import { baseInfoProps, ermGlobalApi } from '@/apps/erm/config/config.jsx';
import { openBusiness } from '@/apps/erm/config/business.js';
import ContractRelated from './../contractRelated/index.vue';
import {
  queryCustomerListByCurrentEmployeeHttp,
  findCompanyByNameHttp,
  queryServiceBaseVoListHttp,
  collectionContractNewHttp,
  collectionContractInfoHttp,
  creatBusHttp,
  formatContractHttp,
  canChangeHttp,
  canChangeAllHttp,
  getSignDateConfigHttp,
  getInitiatorNodeHttp
} from './../../hooks/collection/api.js';
import ermHooks from '@/apps/erm/hooks/index.js';
import { ElNotification } from 'element-plus';
import { useAppStore } from '@/stores/useAppStore';
import AddServeAmount from './addServeAmount.vue';
const props = defineProps({
  ...baseInfoProps,
  reason: String
});

const form = ref();
const from01 = ref();
const setForm = e => {
  form.value = e;
};
const contactRef = {};
const contactRefOne = {};
const sealTypeList = ref([]);
const partB = ref({});
const customerListByCurrent = ref([]);
const companyList = ref([]);
const isFormat = ref(true);
const isIncludeOpenTerm = ref(true);
const deptId = ref('');
let deptName = '';
const serviceContentList = ref([]);
const isMultipartyContract = ref(true);
const contract_related = ref(null);
const crData = ref([]);
const crData_change = ref(false);
const cData = ref([]);
const cData_change = ref(false);
const scData = ref([]);
const scData_change = ref(false);
const bData = ref([]);
const bData_change = ref(false);
const collectionAdjust = ref(void 0);
const loadingStatus = inject('loadingStatus');
let dicSealTypeCodeList = [];
const dicFormatTypeList = ref([]);
const isFormatDisableed = ref(false);
const lastDataInfo = ref(void 0);
const dicBusinessTypeCode = ref('');
const isRequiredSignDate = ref(false);
const isInitiatorNode = ref(false); //是否发起人节点
const funiFileTable = ref();
const isSealUsed = ref(false);
/**
 * canChangeAll 变更业务 为true 能编辑所有 为false 不能编辑 合同签订时间、归属部门、项目名称
 */
const canChangeAll = ref(true);

const serviceObj = reactive({});
const classType = {
  add: {
    code: 'ERM_COLLECTION_CONTRACT_ADD',
    name: '收款合同新增'
  },
  change_add: {
    code: 'ERM_COLLECTION_CONTRACT_CHANGE',
    name: '收款合同变更'
  },
  end_add: {
    code: 'ERM_COLLECTION_CONTRACT_TERMINATE',
    name: '收款合同终止'
  },
  cancel_add: {
    code: 'ERM_COLLECTION_CONTRACT_CANCEL',
    name: '收款合同作废'
  }
};
const appStore = useAppStore();
onMounted(() => {
  getSealType();
  getOwnpartyB();
  if (props.id) {
    getCollectionInfo();
  }
  if (props.isEdit) {
    addFunc();
    addFunc();
    setContractAmount();
  }

  if (props.type == 'add') {
    setDefaultUser();
  }
});
/**
 * 合同开始/结束时间
 */
const dateObj = reactive({
  startDate: null,
  endDate: null
});
const setDefaultUser = async () => {
  let userInfo = await ermHooks.getUserInfo();
  if (userInfo.userId && userInfo.username)
    unref(form).setValues({
      employeeObj: {
        id: userInfo.userId,
        name: userInfo.username
      }
    });
};

const setDate = (key, value) => {
  dateObj[key] = value;
};
const disabledStartDate = time => {
  return dateObj.endDate && time.getTime() > $utils.Date(dateObj.endDate);
};
const disabledEndDate = time => {
  return dateObj.startDate && time.getTime() < $utils.Date(dateObj.startDate);
};
/**
 * 设置合同金额 直接读取非开放性条款服务的含税金额之和进行存储显示，如果无非开放性条款服务则存储显示0
 */
const setContractAmount = $utils.debounce(() => {
  let arr = cData.value.filter(item => item.isOpenClause === false && item.includeTaxAmount);
  let contractAmount = $utils.sum(arr, 'includeTaxAmount') || 0;
  nextTick(() => {
    unref(form).setValues({
      contractAmount
    });
  });
}, 800);
const emit = defineEmits(['updateID', 'infoUpdate', 'otherUpdate']);
const schema = computed(() => {
  return useFormSchema({
    isEdit: props.isEdit,
    sealTypeList: sealTypeList.value,
    setPartB,
    getCompanyId,
    customerListByCurrent: customerListByCurrent.value,
    companyList: companyList.value,
    isFormat: isFormat.value,
    setisFormat,
    setDeptId,
    setIsIncludeOpenTerm,
    setisMultipartyContract,
    type: props.type,
    form: form.value,
    setDate,
    disabledStartDate,
    disabledEndDate,
    isFormatDisableed: isFormatDisableed.value,
    dicFormatTypeList: dicFormatTypeList.value,
    lastDataInfo: unref(lastDataInfo),
    dicBusinessTypeCode: dicBusinessTypeCode.value,
    canChangeAll: canChangeAll.value,
    isSealUsed
  });
});
const aschema = computed(() => {
  return adjustSchema({
    isEdit: ['end_add', 'end_edit'].includes(props.type),
    type: props.type
  });
});

const rules = computed(() => {
  return useRule({
    isEdit: props.isEdit,
    isRequiredSignDate: isRequiredSignDate.value,
    type: props.type,
    dicBusinessTypeCode: dicBusinessTypeCode.value
  });
});

/**
 * @description 服务内容的新增事件
 * **/
const addFunc = () => {
  cData.value.push(getCDataFields());
};
const addFuncOne = () => {
  scData.value.push(gerScDataFields());
};
const addFuncTwo = () => {
  bData.value.push(getBDataFields());
};

// 设置setPartA
const setPartB = async (v, bool = true) => {
  let data = await findCompanyByNameHttp({ companyName: v });
  if (data && data.id && data.companyName) {
    companyList.value = [
      {
        name: data.companyName,
        value: data.id
      }
    ];
    await nextTick();
    form.value.setValues({
      companyId: data.id,
      sealCompanyId: data.id
    });
  } else {
    form.value.setValues({
      companyId: void 0,
      sealCompanyId: void 0
    });
  }
  if (bool) {
    form.value.setValues({
      deptId: void 0
    });
  }
};

// 获取公司id
const getCompanyId = () => {
  return companyList.value && companyList.value.length ? companyList.value[0]?.value : '';
};

/**
 * 设置部门id
 * @param {*} e
 * @param {*} bool
 * @param {*} d 详情返回值 是否格式合同
 */
const setDeptId = async (e, bool = true, d) => {
  deptId.value = e.id;
  deptName = e.name;
  let { list } = await queryServiceBaseVoListHttp({
    deptId: deptId.value,
    pageNo: 1,
    pageSize: 99
  });
  let { list: dataList } = await formatContractHttp({ deptId: deptId.value });
  dicFormatTypeList.value = dataList;
  serviceContentList.value = list;

  isFormatDisableed.value = !dataList.length;
  isFormat.value = dataList.length && d ? true : false;
  if (bool) {
    cData.value = cData.value.map(item => {
      return {
        ...item,
        dicServiceContentCode: '',
        dicServiceContentName: ''
      };
    });
    form.value.setValues({
      isFormat: dataList.length ? void 0 : false,
      dicFormatTypeCode: void 0
    });
  }
};

//
const getSealType = () => {
  $http.fetch(ermGlobalApi.dictList, { typeCode: 'seal_type', clientId: appStore.system?.clientId }).then(res => {
    sealTypeList.value = res;
  });
};

// 设置是否是格式化合同
const setisFormat = v => {
  isFormat.value = v || false;
};

// 设置是否是开放性条款
const setIsIncludeOpenTerm = (e, bool = true) => {
  isIncludeOpenTerm.value = e;
  if (bool) {
    cData.value = cData.value.map(item => {
      return {
        ...item,
        isOpenClause: void 0
      };
    });
  }
};
// 设置是否是多方合同
const setisMultipartyContract = e => {
  isMultipartyContract.value = e;
};

/**
 * @description 获取当前公司对应的客商
 * **/
const getOwnpartyB = async () => {
  let { list } = await queryCustomerListByCurrentEmployeeHttp();
  customerListByCurrent.value = list.map(item => {
    return {
      id: item.id,
      name: item.customerName
    };
  });
  // 根据当前员工归属公司 默认选中乙方 isDefaultCustomer：true
  if (!props.id && props.type == 'add') {
    let partBList = list.filter(item => item.isDefaultCustomer)[0];
    if (partBList) {
      form.value.setValues({ partyB: partBList.id || '' });
      // 默认选中归属部门
      setPartB(partBList.customerName, false);
    }
  }
};

const iptChange = (index, key, e) => {
  cData.value[index][key] = e;
  if ((key === 'taxRate' || key === 'includeTaxAmount') && e) {
    cData.value[index]['excludeTaxAmount'] =
      cData.value[index]['includeTaxAmount'] / (1 + cData.value[index]['taxRate']);
    cData.value[index]['taxAmount'] = cData.value[index]['includeTaxAmount'] - cData.value[index]['excludeTaxAmount'];
  }
};
const iptChangeOne = (index, key, e) => {
  scData.value[index][key] = e;
};
const delServeData = index => {
  cData.value.splice(index, 1);
  setContractAmount();
};
const delCollectionData = index => {
  scData.value.splice(index, 1);
};
const delBusData = index => {
  bData.value.splice(index, 1);
};

/**
 * 调整服务金额-start
 */
const serverAmountRef = ref(null);
const sAmountData = ref([]);
const contractSn = ref(null);
const contactServerAmountRef = {};
const iptChangeServeAmount = (index, key, e) => {
  sAmountData.value[index][key] = e;
};
const serveAmountAddFn = () => {
  const params = {
    contractSn: contractSn.value
  };
  serverAmountRef.value.show(params, sAmountData.value);
};
const serveAmountDelFn = index => {
  sAmountData.value.splice(index, 1);
};
const _addServeAmountData = data => {
  sAmountData.value.push(...data);
};
const sAmountColumns = computed(() => {
  return serveAmountColumns({
    isEdit: ['end_add', 'end_edit'].includes(props.type),
    iptChange: iptChangeServeAmount,
    contactRef: contactServerAmountRef,
    serveAmountDelFn
  });
});
/**
 * 调整服务金额-end
 */

const sColumns = computed(() => {
  return serveColumns({
    isEdit: props.isEdit,
    iptChange,
    contactRef,
    serviceContentList: serviceContentList.value,
    isIncludeOpenTerm: isIncludeOpenTerm.value,
    delFunc: delServeData,
    type: props.type,
    serviceObj: serviceObj,
    setContractAmount,
    isInitiatorNode: isInitiatorNode.value
  });
});
const scColumns = computed(() => {
  return collection({
    isEdit: props.isEdit,
    iptChange: iptChangeOne,
    contactRef: contactRefOne,
    delFunc: delCollectionData
  });
});
const bColumns = computed(() => {
  return busColumns ? busColumns({ isEdit: props.isEdit, delFunc: delBusData }) : [];
});
/**
 * @description 数据保存函数
 * **/
const saveDate = async type => {
  let data = await getData(type);
  if (!data) return Promise.reject();
  let resData;

  if (!props.id) {
    resData = await openBusiness(classType[props.type]['code'], data, props.reason, classType[props.type]['name']);
    let { businessData } = resData;
    let { id, businessId, contractSn } = businessData;
    emit('infoUpdate', {
      ...businessData,
      id,
      businessId: businessId || props.businessId
    });
    form.value.setValues({
      contractSn
    });
  } else if (props.id && type === 'ts') {
    await collectionContractNewHttp({
      ...data,
      id: props.id,
      isSubmit: false
    });
  }
  await nextTick();
  if (!props.id) return Promise.reject();
  if (props.id && !type) {
    await collectionContractNewHttp({
      ...data,
      id: props.id,
      isSubmit: true
    });
  }

  if (type == 'ts') {
    ElNotification({
      title: '暂存成功',
      type: 'success'
    });
  }
  emit('otherUpdate', {
    companyName: data.companyName,
    deptName: data.deptName,
    contractName: data.contractName
  });
  return Promise.resolve({});
};
const authFun = async () => {
  if (funiFileTable.value) {
    let flag = true;
    flag = unref(funiFileTable).verification();
    if (!flag) {
      ElNotification({
        title: '请上传必传要件',
        type: 'warning'
      });
      return Promise.inject();
    }
  }

  if (isRequiredSignDate.value) {
    const { isValid } = await form.value.validate();
    if (!isValid) return Promise.reject();
  }
  if (isInitiatorNode.value && !verificationTable()) {
    return Promise.reject();
  }
  let { signDate } = form.value.getValues();
  return Promise.resolve({
    id: props.id,
    isAudit: true,
    signDate,
    collectionServiceList: isInitiatorNode.value ? cData.value : void 0
  });
};
/**
 * @description 表单数据处理
 * **/
const getData = async type => {
  let isValid = true;
  let needForm = props.type.indexOf('end') < 0 && props.type.indexOf('cancel') < 0;
  let needVerificationTable = props.type.indexOf('end') > -1;
  if (type !== 'ts' && needVerificationTable) {
    if (sAmountData.value.length == 0) {
      ElNotification({
        title: '请完善调整服务金额',
        type: 'warning'
      });
      return Promise.reject();
    }
  }

  if (type !== 'ts' && needForm) {
    let { isValid: v, values } = await form.value.validate();
    isValid =
      v &&
      verificationTable() &&
      verificationSum() &&
      verificationTableForm(contactRefOne) &&
      (!contract_related.value || (contract_related.value && contract_related.value.verificationTable()));
  }

  if (!needForm && props.type.indexOf('end') > -1) {
    let { sufContractAmount /** sufConfirmAmount, sufCollectionAmount, sufInvoiceAmount */ } = from01.value.getValues();
    if (type !== 'ts' && sAmountData.value.length > 0 && !verificationTableForm(contactServerAmountRef)) {
      return Promise.reject();
    }

    let {
      isValid: endV,
      values: { dicSealTypeCode, isSealUsed }
    } = await form.value.validateField(['isSealUsed', 'dicSealTypeCode']);

    if (type !== 'ts' && !endV) return Promise.reject();
    return {
      id: ['新建', 'add'].includes(props.bizName) ? void 0 : props.id,
      lastId: props.lastId || void 0,
      collectionAdjust: { sufContractAmount /** sufConfirmAmount, sufCollectionAmount, sufInvoiceAmount */ },
      serviceAdjustList: sAmountData.value,
      dicSealTypeCode: Array.isArray(dicSealTypeCode) ? dicSealTypeCode.join(',') : void 0,
      isSealUsed
    };
  } else if (!needForm && props.type.indexOf('cancel') > -1) {
    return {
      id: ['新建', 'add'].includes(props.bizName) ? void 0 : props.id,
      lastId: props.lastId || void 0
    };
  }
  if (type == 'ts' || isValid) {
    let fromData;
    fromData = form.value.getValues();
    fromData = JSON.parse(JSON.stringify(fromData));
    if (fromData?.projectObj?.id) {
      fromData.projectId = fromData?.projectObj?.id;
      fromData.projectName = fromData?.projectObj?.name;
    }
    if (fromData?.employeeObj?.id) {
      fromData.employeeId = fromData?.employeeObj?.id;
      fromData.employeeName = fromData?.employeeObj?.name;
    }
    // otherContractSealId
    if (fromData?.otherContractSealObj?.id) {
      fromData.otherContractSealId = fromData?.otherContractSealObj?.id;
      fromData.otherContractSealName = fromData?.otherContractSealObj?.name;
    }
    fromData.companyName = companyList.value.length ? companyList.value[0].name : '';
    fromData.sealCompanyName = companyList.value.length ? companyList.value[0].name : '';
    fromData.deptName = deptName;
    fromData.deptId = fromData.deptId || deptId.value;

    Reflect.deleteProperty(fromData, 'projectObj');
    Reflect.deleteProperty(fromData, 'employeeObj');
    Reflect.deleteProperty(fromData, 'otherContractSealObj');

    // 多方信息
    let multipartyInfoList = [];
    if (fromData?.partyA?.id) {
      multipartyInfoList.push({
        dicMultipartyTypeCode: '1',
        customerId: fromData?.partyA?.id,
        customerName: fromData?.partyA?.defaultName || fromData?.partyA?.name
      });
    }
    if (fromData?.partyB) {
      let index = customerListByCurrent.value.findIndex(el => el.id === fromData?.partyB);
      multipartyInfoList.push({
        dicMultipartyTypeCode: '2',
        customerId: fromData?.partyB,
        customerName: customerListByCurrent.value[index]?.name
      });
    }

    multipartyInfoList.push(...(contract_related.value ? contract_related.value.getData() : []));
    // 服务明细
    let collectionServiceList = cData.value;
    // 收款计划
    let collectionPlanList = scData.value;
    // 商机
    let businessOpportunityList = bData.value;

    let obj = {
      id: ['新建', 'add'].includes(props.bizName) ? void 0 : props.id,
      lastId: props.lastId || void 0,
      ...fromData,
      dicSealTypeCode: fromData.dicSealTypeCode ? fromData.dicSealTypeCode.join(',') : dicSealTypeCodeList.join(','),
      multipartyInfoList,
      collectionServiceList,
      collectionPlanList,
      businessOpportunityList
    };
    if (!obj.isFormat) {
      Reflect.deleteProperty(obj, 'dicFormatTypeCode');
    }
    return obj;
  } else {
    return false;
  }
};

const verificationSum = () => {
  let includeTaxAmountSum = 0;
  let data = form.value.getValues();
  cData.value.map(item => {
    if (item.includeTaxAmount) {
      includeTaxAmountSum += Number(item.includeTaxAmount);
    }
  });
  //
  if (Number(data.contractAmount) != includeTaxAmountSum) {
    ElNotification({
      title: '提示',
      message: '合同金额需等于所有服务含税金额之和!',
      type: 'warning'
    });
    return false;
  }
  //   if (Number(data.contractAmount) < includeTaxAmountSum) {
  //     ElNotification({
  //       title: '提示',
  //       message: '所有服务含税金额之和不能大于合同金额!',
  //       type: 'warning'
  //     });
  //     return false;
  //   }
  return true;
};
const verificationTable = () => {
  if (cData.value.length == 0) {
    ElNotification({
      title: '提示',
      message: '服务明细必填',
      type: 'warning'
    });
    return false;
  } else {
    return verificationTableForm(contactRef);
  }
};
const verificationTableForm = obj => {
  let v_l = [];
  for (let key in obj) {
    let v = obj[key]?.f_v();
    v_l.push(v);
  }
  let d = v_l.filter(i => i && i.vs == 'err');
  if (d.length > 0) return false;
  return true;
};

/**
 * @description 获取详情
 * **/

const getCollectionInfo = async () => {
  let data = void 0;
  let change_end = ['add', 'change_add', 'end_add', 'cancel_add'].includes(props.type);
  loadingStatus.value.status = true;
  if (!change_end) {
    data = await collectionContractInfoHttp({ collectionContractId: props.id }).finally(() => {
      loadingStatus.value.status = false;
    });
    if (data.dicBusinessTypeCode === 'ERM_COLLECTION_CONTRACT_CHANGE') {
      const res = await canChangeAllHttp({
        contractSn: data.contractSn
      });
      canChangeAll.value = res;
    }
  } else {
    data = await creatBusHttp({ id: props.id, dicBusinessTypeCode: classType[props.type]['code'] }).finally(() => {
      loadingStatus.value.status = false;
    });
    if (['change_add'].includes(props.type)) {
      canChangeAll.value = data.canChangeAll;
    }
  }

  // TODO 写入调整后额度 执行 nextTick 渲染调整表单
  collectionAdjust.value = data.collectionAdjust;
  dateObj.startDate = data.startDate || null;
  dateObj.endDate = data.endDate || null;
  await nextTick();

  // TODO 写入基本信息表单数据
  isSealUsed.value = data.isSealUsed;
  let obj = {};
  let filed = ['projectObj', 'employeeObj', 'otherContractSealObj'];

  schema.value.forEach(item => {
    if (filed.indexOf(item.prop) > -1) {
      let str = item.prop.slice(0, -3);
      obj[item.prop] = {
        id: data[`${str}Id`],
        name: data[`${str}Name`]
      };
    } else if (item.prop === 'partyA' || item.prop === 'partyAName') {
      let o = data?.multipartyInfoList?.filter(el => {
        return el.dicMultipartyTypeCode === '1';
      });
      if (o && o.length) {
        obj['partyA'] = {
          id: o[0].customerId,
          name: o[0].customerName
        };
        obj['partyAName'] = o[0].customerName;
      }
    } else if (item.prop === 'partyB' || item.prop === 'partyB_object') {
      let o = data?.multipartyInfoList?.filter(el => {
        return el.dicMultipartyTypeCode === '2';
      });
      if (o && o.length) {
        obj['partyB'] = o[0].customerId;
        obj['partyB_object'] = {
          name: o[0].customerName,
          id: o[0].customerId
        };
      }
    } else if (item.prop === 'dicSealTypeCode') {
      /**
       * 合同终止时不继承终止之前的合同用印类型
       */
      if (props.type === 'end_add') return;
      obj[item.prop] = data[item.prop] ? data['dicSealTypeCodeList'] : data[item.prop]?.split(',') ?? [];
    } else {
      obj[item.prop] = data[item.prop];
    }
    if (props.type === 'add' && item.copy === false) {
      obj[item.prop] = void 0;
    }
  });
  obj['dicChangeTypeCode'] = data['dicChangeTypeCode'];
  obj['dicChangeTypeName'] = data['dicChangeTypeName'];
  obj['dicSealTypeCode'] =
    props.type === 'end_add' ? [] : data['dicSealTypeCodeList'] || (data['dicSealTypeCode']?.split(',') ?? []);
  obj['dicSealTypeName'] = data['dicSealTypeName'];
  obj['contractCount'] = data['contractCount'];
  obj['isSealUsed'] = data['isSealUsed'];
  dicSealTypeCodeList = data['dicSealTypeCodeList'];
  let serviceSns = data.collectionServiceList.filter(item => !!item.serviceSn).map(item => item.serviceSn);
  if (['change_add', 'change_edit'].includes(props.type) && serviceSns.length) {
    canChangeHttp(serviceSns).then(res => {
      res.list.map(item => {
        serviceObj[item.serviceSn] = item.canChangeAll;
      });
    });
  }
  sAmountData.value = data.serviceAdjustList || [];
  console.log(data.collectionServiceList, 'data.collectionServiceList');
  cData.value = data.collectionServiceList.map(item => {
    return {
      ...item,
      uuId: $utils.guid()
    };
  });
  scData.value = data.collectionPlanList.map(item => {
    return {
      ...item,
      uuId: $utils.guid()
    };
  });
  bData.value = data.businessOpportunityList.map(item => {
    return {
      ...item,
      uuId: $utils.guid()
    };
  });
  crData.value = data.multipartyInfoList.filter(
    item => item.dicMultipartyTypeCode !== '1' && item.dicMultipartyTypeCode !== '2'
  );

  deptName = data.deptName;
  contractSn.value = data.contractSn;
  setPartB(obj?.partyB_object?.name, false);
  setDeptId(
    {
      id: data.deptId,
      name: data.deptName
    },
    false,
    obj.isFormat
  );
  setisFormat(obj.isFormat);
  setIsIncludeOpenTerm(obj.isIncludeOpenTerm, false);
  setisMultipartyContract(obj.isMultipartyContract, false);

  form.value.setValues(obj);

  // 调整
  if (from01.value) {
    // let { serviceTotalAmount, totalIncomeConfirmAmount, receiptAmount, invoiceAmount } = data;
    // let object = Object.assign({}, collectionAdjust.value, {
    //   serviceTotalAmount,
    //   totalIncomeConfirmAmount,
    //   invoiceAmount,
    //   receiptAmount
    // });
    from01.value.setValues(collectionAdjust.value);
  }
  emit('infoUpdate', data);
  dicBusinessTypeCode.value = data.dicBusinessTypeCode;
  // 判断现在是否是变更审核  变更审核需要做数据对比
  // 所以需要查询上一笔件
  if (props.type == 'audit' && data.dicBusinessTypeCode == 'ERM_COLLECTION_CONTRACT_CHANGE') {
    getLastCollectionInfo(data.lastId);
  }
  /**
   * 新增数据 审核的时候 判断合同签订时间是否必填（收款合同：“合同签订时间”流程走到发起人时，改为必填项）
   * 是否到达发起人节点 可以编辑修改服务明细
   */
  if (props.type === 'audit' && data.dicBusinessTypeCode === 'ERM_COLLECTION_CONTRACT_ADD') {
    getSignDateConfigHttp({ businessId: data.businessId }).then(res => {
      isRequiredSignDate.value = res;
    });
    getInitiatorNodeHttp({ businessId: data.businessId }).then(res => {
      isInitiatorNode.value = res;
    });
  }
  await nextTick();
  setTimeout(() => {
    form.value?.clearValidate?.();
  }, 600);
};

const getLastCollectionInfo = async id => {
  let data = await collectionContractInfoHttp({ collectionContractId: id });
  // TODO 写入基本信息表单数据
  let obj = {};
  let filed = ['projectObj', 'employeeObj', 'otherContractSealObj'];
  schema.value.forEach(item => {
    if (filed.indexOf(item.prop) > -1) {
      let str = item.prop.slice(0, -3);
      obj[item.prop] = {
        id: data[`${str}Id`],
        name: data[`${str}Name`]
      };
    } else if (item.prop === 'partyA') {
      let o = data?.multipartyInfoList?.filter(el => {
        return el.dicMultipartyTypeCode === '1';
      });
      if (o && o.length) {
        obj['partyA'] = {
          id: o[0].customerId,
          name: o[0].customerName
        };
      }
    } else if (item.prop === 'partyB' || item.prop === 'partyB_object') {
      let o = data?.multipartyInfoList?.filter(el => {
        return el.dicMultipartyTypeCode === '2';
      });
      if (o && o.length) {
        obj['partyB'] = o[0].customerId;
        obj['partyB_object'] = {
          name: o[0].customerName,
          id: o[0].customerId
        };
      }
    } else if (item.prop === 'dicSealTypeCode') {
      obj[item.prop] = data[item.prop] ? data[item.prop].split(',') : data['dicSealTypeCodeList'];
    } else {
      obj[item.prop] = data[item.prop];
    }
    if (props.type === 'add' && item.copy === false) {
      obj[item.prop] = void 0;
    }
  });
  lastDataInfo.value = obj;

  crData_change.value = data.multipartyInfoList
    .filter(item => item.dicMultipartyTypeCode !== '1' && item.dicMultipartyTypeCode !== '2')
    .map(item => {
      Reflect.deleteProperty(item, 'uuId');
      Reflect.deleteProperty(item, 'id');
      Reflect.deleteProperty(item, 'contractId');
      return item;
    });
  let _crData = $utils.clone(crData.value, true).map(item => {
    Reflect.deleteProperty(item, 'uuId');
    Reflect.deleteProperty(item, 'id');
    Reflect.deleteProperty(item, 'contractId');
    return item;
  });

  if (JSON.stringify(crData_change.value) == JSON.stringify(_crData)) {
    crData_change.value = false;
  }

  cData_change.value = data.collectionServiceList.map(item => {
    Reflect.deleteProperty(item, 'id');
    Reflect.deleteProperty(item, 'contractId');
    return item;
  });
  let _cData = $utils.clone(cData.value, true).map(item => {
    Reflect.deleteProperty(item, 'uuId');
    Reflect.deleteProperty(item, 'id');
    Reflect.deleteProperty(item, 'contractId');
    return item;
  });
  if (JSON.stringify(cData_change.value) == JSON.stringify(_cData)) {
    cData_change.value = false;
  }

  scData_change.value = data.collectionPlanList.map(item => {
    Reflect.deleteProperty(item, 'id');
    Reflect.deleteProperty(item, 'contractId');
    return item;
  });
  let _scData = $utils.clone(scData.value, true).map(item => {
    Reflect.deleteProperty(item, 'uuId');
    Reflect.deleteProperty(item, 'id');
    Reflect.deleteProperty(item, 'contractId');
    return item;
  });
  if (JSON.stringify(scData_change.value) == JSON.stringify(_scData)) {
    scData_change.value = false;
  }

  bData_change.value = data.businessOpportunityList.map(item => {
    Reflect.deleteProperty(item, 'id');
    Reflect.deleteProperty(item, 'contractId');
    return item;
  });

  let _bData = $utils.clone(bData.value, true).map(item => {
    Reflect.deleteProperty(item, 'uuId');
    Reflect.deleteProperty(item, 'id');
    Reflect.deleteProperty(item, 'contractId');
    return item;
  });
  if (JSON.stringify(bData_change.value) == JSON.stringify(_bData)) {
    bData_change.value = false;
  }
};

/**
 * @description 下一步 提交数据
 * **/
const nextStep = () => {
  return saveDate();
};
const ts = () => {
  return saveDate('ts');
};

defineExpose({
  nextStep,
  ts,
  authFun
});
</script>

<style scoped lang="less">
.serve-amount-head {
  display: flex;
  justify-content: space-between;
  align-items: end;

  span {
    color: #999;
    font-size: 14px;
  }
}

.musterW {
  overflow: hidden;
  width: 100%;
  height: 26px;
  padding-left: 5px;
}

.musterW > span {
  color: red;
  display: inline-block;
  /* margin: 3px 3px 0 0; */
  position: relative;
  top: 4px;
  right: 3px;
}

.beasInfo_banner {
  display: flex;
  flex-direction: column;
}
</style>
