<template>
  <div>
    <FuniGroupTitle :title="module.label"></FuniGroupTitle>
    <component :is="module.component" v-bind="module.options" />
  </div>
</template>
<script setup lang="jsx">
import { ref, computed, unref, onMounted } from 'vue';
import useInfo from '../hooks/info.jsx';
import { updateHttp, saveHttp, getInfoHttp } from './../hooks/api';
import { useMultiTab } from '@/utils/hooks/useMultiTab';
const multiTab = useMultiTab();

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: true
  },
  id: {
    type: String,
    default: ''
  }
});

const componentRef = ref();
const label = ref('');
const component = '';
const schema = void 0;
const onGetForm = void 0;
const col = 3;
const emit = defineEmits(['updateID', 'infoUpdate']);

const module = computed(() => {
  return {
    label: label.value,
    component,
    options: {
      schema: unref(schema),
      onGetForm,
      col
    }
  };
});

onMounted(() => {
  getInfo();
});

async function getInfo() {
  if (!props.id) return;
  const data = await getInfoHttp({ id: props.id });
  componentRef.value.setValues({ ...data });
  emit('infoUpdate', {
    ...data
  });
}

async function getFormData(type) {
  let isValid = true;
  let values = componentRef?.value?.getValues?.();
  if (type !== 'ts') {
    let { isValid: v } = await componentRef.value.validate();
    isValid = v;
  }
  if (!isValid) return Promise.reject(false);
  return values;
}

async function saveDate(type) {
  let data = await getFormData(type);
  if (!data) Promise.reject(false);
  const fetchName = props.id ? updateHttp : saveHttp;
  const resData = await $http.post(fetchName, {
    ...data,
    id: props.id
  });
  emit('infoUpdate', {
    ...resData
  });
  if (type === 'ts') {
    ElNotification({
      title: '暂存成功',
      type: 'success'
    });
  } else if (type === 'submit') {
    ElNotification({
      title: '提交成功',
      type: 'success'
    });
  }
  return Promise.resolve({
    cb: () => {
      multiTab.closeCurrentPage();
    }
  });
}
const nextStep = () => {
  return saveDate();
};
const ts = () => {
  return saveDate('ts');
};
const submit = () => {
  return saveDate('submit');
};

defineExpose({
  nextStep,
  ts,
  submit
});
</script>
