<template>
  <div>
    <funi-list-page-v2 ref="listPage" :cardTab="useCardTab" @headBtnClick="headBtnClick" />
  </div>
</template>
<script setup>
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';
const router = useRouter();
import { getColumns as useColumn, useBtnsConfig } from './hooks/column.jsx';
import { getListHttp } from './hooks/api';
const subnaem = '';
defineOptions({
  subnaem
});
const addEditRouterName = ''; // 新增、编辑页的路由名称
const infoAuditRouterName = ''; // 详情、审核页的路由名称
const listPage = ref();
const useCardTab = computed(() => {
  return [
    {
      curdOption: {
        btns: useBtnsConfig({
          addFunc
        }),
        lodaData: lodaData,
        fixedButtons: true,
        reloadOnActive: true,
        checkOnRowClick: true,
        columns: useColumn({
          seeDateils,
          editFunc,
          delFunc
        })
      }
    }
  ];
});
async function lodaData(params, queryParams) {
  listPage.value.activeCurd.resetCurrentRow();
  let data = await getListHttp({ ...params, ...queryParams });
  return data;
}

function seeDateils(row) {
  if (!infoAuditRouterName) return;
  router.push({
    name: infoAuditRouterName,
    query: {
      bizName: '详情',
      type: 'info',
      id: row?.id,
      tab: [subnaem, '详情'].join('-')
    }
  });
}
function editFunc(row) {
  if (!addEditRouterName) return;
  router.push({
    name: addEditRouterName,
    query: {
      bizName: '编辑',
      type: 'edit',
      id: row?.id,
      tab: [subnaem, '编辑'].join('-')
    }
  });
}

function delFunc() {}

function addFunc() {
  if (!addEditRouterName) return;
  router.push({
    name: addEditRouterName,
    query: {
      bizName: '新建',
      type: 'add',
      tab: [subnaem, '新建'].join('-')
    }
  });
}
</script>
