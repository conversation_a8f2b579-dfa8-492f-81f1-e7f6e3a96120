<!--
  业务新增/编辑页面模板
  用于处理业务流程的新增和编辑操作，包含基本信息填写和附件上传功能
-->
<template>
  <div>
    <!-- 业务详情组件，包含步骤流程和头部信息 -->
    <funi-detail :bizName="bizName" :steps="steps" :detailHeadOption="detailHeadOption || {}" />
    <!-- 提交成功提示组件 -->
    <funi-submit-success ref="submitSuccessRef" />
  </div>
</template>

<script setup lang="jsx">
// Vue 3 Composition API 相关导入
import { ref, computed } from 'vue';
// 路由相关导入
import { useRoute } from 'vue-router';
// 业务逻辑 Hook 导入
import { useBusiness } from '@/hooks/useBusiness.js';

// 自定义tab内容组件
import BaseInfo from './component/BaseInfo.vue';

// 获取当前路由信息
const route = useRoute();
// 获取业务逻辑处理实例
const business = useBusiness();
// 路由查询参数的响应式引用
const routeQuery = ref(route.query);

// 业务记录ID
const id = ref(routeQuery.value.id);
// 业务名称（从路由参数获取）
const bizName = computed(() => routeQuery.value.bizName);
// 业务类型（从路由参数获取）
const bizType = computed(() => routeQuery.value.type);

// 业务信息数据
const infoData = ref();
// 业务ID
const businessId = ref();

// 详情页头部配置选项
const detailHeadOption = computed(() => {
  return {
    statusName: '节点', // 状态名称
    no: infoData.value?.businessId, // 业务编号
    title: routeQuery.value.title, // 页面标题
    status: infoData.value?.businessNode // 当前业务节点状态
  };
});

// 提交成功组件的引用
const submitSuccessRef = ref();

// 计算业务名称，由公司名称、部门名称和合同名称组成
const businessName = computed(() => {
  const { companyName, deptName, contractName } = infoData.value;
  return `${companyName}-${deptName}-${contractName}`;
});

// 步骤配置，定义业务流程的各个步骤
const steps = computed(() => {
  // 判断是否为编辑模式（非审核和查看模式）
  const isEdit = !['audit', 'info'].includes(bizType.value);

  return [
    // 第一步：基本信息填写
    {
      title: '基本信息', // 步骤标题
      preservable: true, // 是否可保存草稿
      type: BaseInfo, // 自定义tab内容组件
      // 传递给组件的属性
      props: {
        id: id.value, // 记录ID
        isEdit, // 是否编辑模式
        type: bizType.value, // 业务类型
        bizName: bizName.value // 业务名称
      },
      // 组件事件监听器
      on: {
        // 更新ID事件处理器
        updateID: data => {
          id.value = data?.id;
          businessId.value = data?.businessId;
        },
        // 信息更新事件处理器
        infoUpdate: data => {
          infoData.value = data;
          id.value = data?.id;
          businessId.value = data?.businessId;
        },
        // 其他字段更新事件处理器
        otherUpdate: object => {
          if (object) {
            // 遍历更新对象的所有属性
            Object.entries(object).forEach(([key, value]) => {
              // 更新对应字段，如果新值为空则保持原值
              infoData.value[key] = value || infoData.value[key];
            });
          }
        }
      }
    },
    // 第二步: xxx
    // ...
    // 第X步：附件上传
    {
      title: '要件信息', // 步骤标题
      preservable: false, // 不可保存草稿
      type: 'FuniFileTable', // 使用系统文件表格组件
      props: {
        params: { businessId: businessId.value }, // 传递业务ID参数
        callbackFun: submitBusiness // 提交回调函数
      }
    }
  ];
});

/**
 * 提交业务流程
 * 根据业务类型确定业务代码，然后调用业务提交接口
 */
const submitBusiness = async () => {
  // 根据业务名称确定业务代码
  const businessCode =
    bizName.value === 'add'
      ? 'BUSINESS_CONFIG_CODE_ADD' // 新增业务代码
      : routeQuery.value.dicBusinessTypeCode; // 其他业务类型代码

  // 调用业务提交接口
  await business.submit(businessCode, businessId.value, 'SUBMIT', businessName.value);

  // 显示提交成功提示
  submitSuccessRef.value.show();
};
</script>
