<!--
  业务详情页面模板
  用于展示和处理业务流程的详情信息，支持多步骤查看、审核和工作流操作
-->
<template>
  <div>
    <!-- 业务详情组件 - 核心组件，集成了步骤流程、审核按钮、工作流等功能 -->
    <funi-detail
      :steps="steps"
      :bizName="bizName"
      :showWorkflow="true"
      :auditButtons="buttons"
      :beforeAuditFn="beforeAuditFn"
      :detailHeadOption="detailHeadOption || {}"
      :businessId="['info', 'audit'].includes(type) && businessId ? businessId : void 0"
      @auditEvent="auditEvent"
    />
  </div>
</template>

<script setup lang="jsx">
// Vue 3 Composition API 相关导入
import { ref, computed, unref } from 'vue';
// 路由相关导入
import { useRoute, useRouter } from 'vue-router';

// 业务逻辑 Hook 导入
import { useBusiness } from '@/hooks/useBusiness.js';
// 多标签页管理 Hook 导入
import { useMultiTab } from '@/utils/hooks/useMultiTab';

// 自定义tab内容组件导入
import BaseInfo from './../component/collection/baseInfo.vue'; // 基本信息组件
import IncomeList from '../component/collection/incomeList.vue'; // 收入确认表组件
import InvoiceList from '../component/collection/invoiceList.vue'; // 开票申请组件
import ReceiptList from '../component/collection/receiptList.vue'; // 收款单组件

// ERM 全局 API 配置导入
import { ermGlobalApi } from '@/apps/erm/config/config.jsx';

// 多标签页管理实例
const multiTab = useMultiTab();

// 获取当前路由信息
const route = useRoute();
const router = useRouter();
// 获取业务逻辑处理实例
const business = useBusiness();
// 路由查询参数的响应式引用
const routeQuery = ref(route.query);

// 业务记录ID
const id = ref(routeQuery.value.id);
// 业务名称（从路由参数获取）
const bizName = computed(() => routeQuery.value.bizName);
// 业务类型（从路由参数获取）
const bizType = computed(() => routeQuery.value.type);

// 业务信息数据
const infoData = ref();
// 业务ID
const businessId = ref();
// 审核按钮配置数组
const buttons = ref([]);

// 其他业务参数
const contractSn = ref(); // 合同序列号

// tab组件引用
const baseInfoRef = ref(); // 基本信息组件引用

/**
 * 审核前置函数
 * 在执行审核操作前进行必要的验证和处理
 * @param {Object} param - 审核参数
 * @param {string} param.businessExecutionType - 业务执行类型
 * @returns {Promise} 返回验证结果的Promise
 */
const beforeAuditFn = ({ businessExecutionType }) => {
  // 判断是否启用审核功能
  const enableAudit =
    ['AGREE', 'SUBMIT'].includes(businessExecutionType) && // 同意或提交操作
    ['BUSINESS_CONFIG_CODE'].includes(infoData.value.dicBusinessTypeCode); // 特定业务类型

  try {
    // 如果启用审核且基本信息组件有验证函数，则执行验证
    return enableAudit && baseInfoRef.value.authFun ? baseInfoRef.value.authFun() : Promise.resolve({});
  } catch {
    // 验证失败时返回拒绝的Promise
    return Promise.reject();
  }
};

/**
 * 详情页头部配置选项
 * 配置页面标题、状态、按钮等信息
 */
const detailHeadOption = computed(() => {
  return {
    statusName: '节点', // 状态名称
    title: routeQuery.value.title, // 页面标题
    no: infoData.value?.businessId, // 业务编号
    status: infoData.value?.businessNode, // 当前业务节点状态
    btns: [
      // 示例按钮（可选，根据实际情况添加）
      {
        name: '发起收入确认表', // 按钮名称
        props: { type: 'text' }, // 按钮属性
        on: { click: addIncome } // 点击事件处理函数
      }
    ]
  };
});

/**
 * 步骤配置
 * 定义详情页面的各个步骤和对应的组件
 */
const steps = computed(() => {
  return [
    // 第一步：基本信息
    {
      title: '基本信息', // 步骤标题
      preservable: true, // 是否可保存草稿
      type: BaseInfo, // 使用的组件类型
      props: {
        id: id.value, // 记录ID
        type: bizType.value, // 业务类型
        businessId: businessId.value, // 业务ID
        ref: e => (baseInfoRef.value = e), // 组件引用设置
        isEdit: !['audit', 'info'].includes(bizType.value) // 是否为编辑模式
      },
      on: {
        // 更新ID事件处理器
        updateID: data => {
          id.value = data?.id;
          businessId.value = data?.businessId;
        },
        // 信息更新事件处理器
        infoUpdate: data => {
          infoData.value = data;
          id.value = data?.id;
          businessId.value = data?.businessId;
          // 其他业务参数设置
          contractSn.value = data?.contractSn; // 设置合同序列号
        }
      }
    },
    // 第二步：收入确认表
    {
      title: '收入确认表', // 步骤标题
      preservable: true, // 是否可保存草稿
      type: IncomeList, // 收入确认表组件
      props: { contractSn: contractSn.value } // 传递合同序列号
    },
    // 第三步：收款单
    {
      title: '收款单', // 步骤标题
      preservable: true, // 是否可保存草稿
      type: ReceiptList, // 收款单组件
      props: { contractSn: contractSn.value } // 传递合同序列号
    },
    // 第四步：开票申请
    {
      title: '开票申请', // 步骤标题
      preservable: true, // 是否可保存草稿
      type: InvoiceList, // 开票申请组件
      props: { contractSn: contractSn.value } // 传递合同序列号
    },
    // 动态添加办件记录步骤（仅在有业务ID和业务类型代码时显示）
    ...(businessId.value && (routeQuery.value.dicBusinessTypeCode || infoData.value?.dicBusinessTypeCode)
      ? [
          {
            title: '办件记录', // 步骤标题
            type: 'FuniWorkRecord', // 工作记录组件
            props: {
              objectListUrl: 'queryBusinessUserRecordInfoApi', // 对象列表API
              busListUrl: 'queryBusinessUserRecordInfoApi', // 业务列表API
              params: {
                businessId: businessId.value, // 业务ID
                dicBusinessTypeCode: routeQuery.value.dicBusinessTypeCode || infoData.value?.dicBusinessTypeCode // 业务类型代码
              }
            },
            on: { onClick } // 点击事件处理
          }
        ]
      : []) // 如果条件不满足，返回空数组
  ];
});

/**
 * 审核事件处理函数
 * 处理审核完成后的操作，关闭当前页面
 */
const auditEvent = () => {
  business.auditEnd(() => multiTab.closeCurrentPage());
};

/**
 * 办件记录点击事件处理函数
 * 跳转到合同收款详情页面
 * @param {Object} row - 点击的行数据
 */
const onClick = row => {
  router.push({
    name: 'ermContractCollectionInfo', // 路由名称
    query: {
      type: 'info', // 页面类型：查看
      bizName: '详情', // 业务名称
      businessId: row.businessId, // 业务ID
      title: row.contractName || '收款合同', // 页面标题
      dicBusinessTypeCode: row.dicBusinessTypeCode, // 业务类型代码
      tab: ['收款合同', row.contractName || '', '详情'].join('-'), // 标签页标题
      id: row.dicCollContractDoingBusCode ? row.lastId || row.id : row.id // 记录ID
    }
  });
};

/**
 * 发起收入确认表函数
 * 跳转到收入确认表新建页面
 */
const addIncome = () => {
  const { dicContractStatusCode, id, contractName } = infoData.value || {};
  router.push({
    name: 'ermContractIncomeAdd', // 路由名称
    query: {
      bizName: '新建', // 业务名称
      type: 'add', // 页面类型：新增
      tab: '收入确认表-新建', // 标签页标题
      cId: dicContractStatusCode == '1' ? id : void 0, // 合同ID（仅在合同状态为1时传递）
      cName: dicContractStatusCode == '1' ? contractName : void 0 // 合同名称（仅在合同状态为1时传递）
    }
  });
};
</script>
