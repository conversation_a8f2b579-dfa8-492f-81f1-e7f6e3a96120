export function getColumns({ seeDateils, editFunc, delFunc }) {
  const operation = [
    {
      label: '操作',
      prop: 'buttonList',
      fixed: 'right',
      align: 'center',
      width: 120,
      render: ({ row, index }) => {
        let operationBtn = [
          <span
            style="color: var(--el-color-primary); cursor: pointer"
            onClick={() => {
              editFunc?.(row, index);
            }}
          >
            编辑
          </span>,
          <el-popconfirm
            title="确定删除当前数据？"
            width="220"
            onConfirm={() => {
              delFunc?.(row, index);
            }}
          >
            {{
              reference: () => <span style="color: var(--el-color-primary); cursor: pointer">删除</span>
            }}
          </el-popconfirm>
        ];

        return (
          <div style="width: 100%;display: inline-flex;justify-content: space-around;align-items: center;gap:12px;padding:0 10px">
            {operationBtn}
          </div>
        );
      }
    }
  ];
  const columns = [];
  return [...columns, ...operation];
}

export function useBtnsConfig({ addFunc }) {
  return [
    {
      component: () => (
        <el-button
          onClick={() => {
            addFunc?.();
          }}
          type="primary"
        >
          新建
        </el-button>
      )
    }
  ];
}
