<!-- {{MODULE_NAME}} - 详情页面模板 -->
<!--
  AI Agent 页面创建模板 - 标准详情页

  模板说明：
  - 基于 FuniDetail 组件的标准详情页面
  - 支持查看、审核两种模式
  - 集成多步骤流程、审核按钮、工作流等功能
  - 严格使用funi-detail罗列参数

  适用页面类型：
  - type="info": 详情页面
  - type="audit": 审核页面

  占位符说明：
  - {{MODULE_NAME}}: 模块中文名称，如"用户管理"
  - {{ENTITY_NAME}}: 实体中文名称，如"用户"
  - {{BASE_INFO_COMPONENT}}: 基本信息组件名称，如"UserBaseInfo"
  - {{BASE_INFO_PATH}}: 基本信息组件路径，如"./components/UserBaseInfo.vue"
  - {{STEP_2_COMPONENT}}: 第二步组件名称，如"UserDetailList"
  - {{STEP_2_PATH}}: 第二步组件路径，如"./components/UserDetailList.vue"
  - {{STEP_3_COMPONENT}}: 第三步组件名称，如"UserHistoryList"
  - {{STEP_3_PATH}}: 第三步组件路径，如"./components/UserHistoryList.vue"
  - {{STEP_4_COMPONENT}}: 第四步组件名称，如"UserLogList"
  - {{STEP_4_PATH}}: 第四步组件路径，如"./components/UserLogList.vue"
  - {{API_CONFIG}}: API配置导入，如"userGlobalApi"
  - {{API_CONFIG_PATH}}: API配置路径，如"@/apps/user/config/config.jsx"
  - {{BUSINESS_TYPE_CODE}}: 业务类型代码，如"USER_MANAGEMENT_CODE"
  - {{STATUS_FIELD_NAME}}: 状态字段名称，如"节点"
  - {{RELATION_FIELD}}: 关联字段，如"contractSn"
  - {{STEP_1_TITLE}}: 第一步标题，如"基本信息"
  - {{STEP_2_TITLE}}: 第二步标题，如"详细信息"
  - {{STEP_3_TITLE}}: 第三步标题，如"历史记录"
  - {{STEP_4_TITLE}}: 第四步标题，如"操作日志"
  - {{WORK_RECORD_TITLE}}: 办件记录标题，如"办件记录"
  - {{WORK_RECORD_API}}: 办件记录API，如"queryUserRecordInfoApi"
  - {{DETAIL_ROUTE_NAME}}: 详情路由名称，如"userDetailInfo"
  - {{ADD_ROUTE_NAME}}: 新增路由名称，如"userAdd"
  - {{CUSTOM_BUTTON_NAME}}: 自定义按钮名称，如"发起申请"
  - {{CUSTOM_BUTTON_ACTION}}: 自定义按钮动作，如"addApplication"
-->
<template>
  <div>
    <!-- 业务详情组件 - 核心组件，集成了步骤流程、审核按钮、工作流等功能 -->
    <funi-detail
      :steps="steps"
      :bizName="bizName"
      :showWorkflow="true"
      :auditButtons="buttons"
      :beforeAuditFn="beforeAuditFn"
      :detailHeadOption="detailHeadOption || {}"
      :businessId="['info', 'audit'].includes(type) && businessId ? businessId : void 0"
      @auditEvent="auditEvent"
    />
  </div>
</template>

<script setup lang="jsx">
// Vue 3 Composition API 相关导入
import { ref, computed, unref } from 'vue';
// 路由相关导入
import { useRoute, useRouter } from 'vue-router';

// 业务逻辑 Hook 导入，用与工作流操作，不可去掉
import { useBusiness } from '@/hooks/useBusiness.js';
// 多标签页管理 Hook 导入
import { useMultiTab } from '@/utils/hooks/useMultiTab';

// 自定义tab内容组件导入
import {{BASE_INFO_COMPONENT}} from '{{BASE_INFO_PATH}}';      // {{ENTITY_NAME}}基本信息组件
import {{STEP_2_COMPONENT}} from '{{STEP_2_PATH}}';    // {{ENTITY_NAME}}{{STEP_2_TITLE}}组件
import {{STEP_3_COMPONENT}} from '{{STEP_3_PATH}}';  // {{ENTITY_NAME}}{{STEP_3_TITLE}}组件
import {{STEP_4_COMPONENT}} from '{{STEP_4_PATH}}';  // {{ENTITY_NAME}}{{STEP_4_TITLE}}组件

// {{MODULE_NAME}} 全局 API 配置导入
import { {{ API_CONFIG }} } from '{{API_CONFIG_PATH}}';

// 获取当前路由信息
const route = useRoute();
const router = useRouter();
// 多标签页管理实例
const multiTab = useMultiTab();
// 获取业务逻辑处理实例，用与工作流操作，不可去掉
const business = useBusiness();
// 路由查询参数的响应式引用
const routeQuery = ref(route.query);

// {{ENTITY_NAME}}记录ID
const id = ref(routeQuery.value.id);
// {{ENTITY_NAME}}业务名称（从路由参数获取）
const bizName = computed(() => routeQuery.value.bizName);
// {{ENTITY_NAME}}业务类型（从路由参数获取）
const bizType = computed(() => routeQuery.value.type);

// {{ENTITY_NAME}}业务信息数据
const infoData = ref();
// {{ENTITY_NAME}}业务ID
const businessId = ref();
// 审核按钮配置数组
const buttons = ref([]);

// 其他{{ENTITY_NAME}}业务参数
const {{RELATION_FIELD}} = ref();  // {{ENTITY_NAME}}关联字段

// tab组件引用
const baseInfoRef = ref();  // {{ENTITY_NAME}}基本信息组件引用

/**
 * {{ENTITY_NAME}}审核前置函数
 * 在执行审核操作前进行必要的验证和处理
 * @param {Object} param - 审核参数
 * @param {string} param.businessExecutionType - 业务执行类型
 * @returns {Promise} 返回验证结果的Promise
 */
const beforeAuditFn = ({ businessExecutionType }) => {
  // 判断是否启用{{ENTITY_NAME}}审核功能
  const enableAudit =
    ['AGREE', 'SUBMIT'].includes(businessExecutionType) &&  // 同意或提交操作
    ['{{BUSINESS_TYPE_CODE}}'].includes(infoData.value.dicBusinessTypeCode);  // 特定{{ENTITY_NAME}}业务类型

  try {
    // 如果启用审核且{{ENTITY_NAME}}基本信息组件有验证函数，则执行验证
    return enableAudit && baseInfoRef.value.authFun ? baseInfoRef.value.authFun() : Promise.resolve({});
  } catch {
    // {{ENTITY_NAME}}验证失败时返回拒绝的Promise
    return Promise.reject();
  }
};

/**
 * {{ENTITY_NAME}}详情页头部配置选项
 * 配置页面标题、状态、按钮等信息
 */
const detailHeadOption = computed(() => {
  return {
    statusName: '{{STATUS_FIELD_NAME}}',                           // 状态名称
    title: routeQuery.value.title,               // 页面标题
    no: infoData.value?.businessId,              // {{ENTITY_NAME}}业务编号
    status: infoData.value?.businessNode,        // 当前{{ENTITY_NAME}}业务节点状态
    btns: [
      // 示例按钮（可选，根据实际情况添加）
      {
        name: '{{CUSTOM_BUTTON_NAME}}',                   // 按钮名称
        props: { type: 'text' },                 // 按钮属性
        on: { click: {{CUSTOM_BUTTON_ACTION}} }                 // 点击事件处理函数
      }
    ]
  };
});

/**
 * {{ENTITY_NAME}}步骤配置
 * 定义{{ENTITY_NAME}}详情页面的各个步骤和对应的组件
 */
const steps = computed(() => {
  return [
    // 第一步：{{ENTITY_NAME}}基本信息
    {
      title: '{{STEP_1_TITLE}}',                           // 步骤标题
      preservable: true,                          // 是否可保存草稿
      type: {{BASE_INFO_COMPONENT}},                             // 使用的{{ENTITY_NAME}}组件类型
      props: {
        id: id.value,                             // {{ENTITY_NAME}}记录ID
        type: bizType.value,                      // {{ENTITY_NAME}}业务类型
        businessId: businessId.value,             // {{ENTITY_NAME}}业务ID
        ref: e => (baseInfoRef.value = e),        // {{ENTITY_NAME}}组件引用设置
        isEdit: !['audit', 'info'].includes(bizType.value)  // 是否为编辑模式
      },
      on: {
        // 更新ID事件处理器
        updateID: data => {
          id.value = data?.id;
          businessId.value = data?.businessId;
        },
        // {{ENTITY_NAME}}信息更新事件处理器
        infoUpdate: data => {
          infoData.value = data;
          id.value = data?.id;
          businessId.value = data?.businessId;
          // 其他{{ENTITY_NAME}}业务参数设置
          {{RELATION_FIELD}}.value = data?.{{RELATION_FIELD}};    // 设置{{ENTITY_NAME}}关联字段
        }
      }
    },
    // 第二步：{{ENTITY_NAME}}{{STEP_2_TITLE}}
    {
      title: '{{STEP_2_TITLE}}',                        // 步骤标题
      preservable: true,                          // 是否可保存草稿
      type: {{STEP_2_COMPONENT}},                           // {{ENTITY_NAME}}{{STEP_2_TITLE}}组件
      props: { {{RELATION_FIELD}}: {{RELATION_FIELD}}.value }     // 传递{{ENTITY_NAME}}关联字段
    },
    // 第三步：{{ENTITY_NAME}}{{STEP_3_TITLE}}
    {
      title: '{{STEP_3_TITLE}}',                            // 步骤标题
      preservable: true,                          // 是否可保存草稿
      type: {{STEP_3_COMPONENT}},                          // {{ENTITY_NAME}}{{STEP_3_TITLE}}组件
      props: { {{RELATION_FIELD}}: {{RELATION_FIELD}}.value }     // 传递{{ENTITY_NAME}}关联字段
    },
    // 第四步：{{ENTITY_NAME}}{{STEP_4_TITLE}}
    {
      title: '{{STEP_4_TITLE}}',                          // 步骤标题
      preservable: true,                          // 是否可保存草稿
      type: {{STEP_4_COMPONENT}},                          // {{ENTITY_NAME}}{{STEP_4_TITLE}}组件
      props: { {{RELATION_FIELD}}: {{RELATION_FIELD}}.value }     // 传递{{ENTITY_NAME}}关联字段
    },
    // 动态添加{{ENTITY_NAME}}办件记录步骤（仅在有业务ID和业务类型代码时显示）
    ...(businessId.value && (routeQuery.value.dicBusinessTypeCode || infoData.value?.dicBusinessTypeCode)
      ? [
          {
            title: '{{WORK_RECORD_TITLE}}',                      // 步骤标题
            type: 'FuniWorkRecord',                 // 工作记录组件
            props: {
              objectListUrl: '{{WORK_RECORD_API}}',    // {{ENTITY_NAME}}对象列表API
              busListUrl: '{{WORK_RECORD_API}}',       // {{ENTITY_NAME}}业务列表API
              params: {
                businessId: businessId.value,                     // {{ENTITY_NAME}}业务ID
                dicBusinessTypeCode: routeQuery.value.dicBusinessTypeCode || infoData.value?.dicBusinessTypeCode  // {{ENTITY_NAME}}业务类型代码
              }
            },
            on: { onClick }                         // 点击事件处理
          }
        ]
      : [])  // 如果条件不满足，返回空数组
  ];
});

/**
 * {{ENTITY_NAME}}审核事件处理函数
 * 处理审核完成后的操作，关闭当前页面
 */
const auditEvent = () => {
  business.auditEnd(() => multiTab.closeCurrentPage());
};

/**
 * {{ENTITY_NAME}}办件记录点击事件处理函数
 * 跳转到{{ENTITY_NAME}}详情页面
 * @param {Object} row - 点击的行数据
 */
const onClick = row => {
  router.push({
    name: '{{DETAIL_ROUTE_NAME}}',              // {{ENTITY_NAME}}路由名称
    query: {
      type: 'info',                                 // 页面类型：查看
      bizName: '详情',                              // 业务名称
      businessId: row.businessId,                   // {{ENTITY_NAME}}业务ID
      title: row.{{RELATION_FIELD}} || '{{ENTITY_NAME}}详情',         // 页面标题
      dicBusinessTypeCode: row.dicBusinessTypeCode, // {{ENTITY_NAME}}业务类型代码
      tab: ['{{ENTITY_NAME}}', row.{{RELATION_FIELD}} || '', '详情'].join('-'),  // 标签页标题
      id: row.dicCollContractDoingBusCode ? row.lastId || row.id : row.id  // {{ENTITY_NAME}}记录ID
    }
  });
};

/**
 * {{CUSTOM_BUTTON_NAME}}函数
 * 跳转到{{ENTITY_NAME}}新建页面
 */
const {{CUSTOM_BUTTON_ACTION}} = () => {
  const { dicContractStatusCode, id, {{RELATION_FIELD}} } = infoData.value || {};
  router.push({
    name: '{{ADD_ROUTE_NAME}}',                   // {{ENTITY_NAME}}新增路由名称
    query: {
      bizName: '新建',                              // 业务名称
      type: 'add',                                  // 页面类型：新增
      tab: '{{ENTITY_NAME}}-新建',                       // 标签页标题
      cId: dicContractStatusCode == '1' ? id : void 0,        // {{ENTITY_NAME}}ID（仅在状态为1时传递）
      cName: dicContractStatusCode == '1' ? {{RELATION_FIELD}} : void 0  // {{ENTITY_NAME}}名称（仅在状态为1时传递）
    }
  });
};
</script>
