vue
<template>
  <div>
    <sfc-form :model="formData" ref="formRef" :rules="rules">
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </el-form-item>
    </sfc-form>
  </div>
</template>
<script>
export default {
  data() {
    return {
      formData: {
        name: ''
      },
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
      }
    };
  },
  methods: {
    submitForm() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return;
        // 提交表单
      });
    }
  }
};
</script>
