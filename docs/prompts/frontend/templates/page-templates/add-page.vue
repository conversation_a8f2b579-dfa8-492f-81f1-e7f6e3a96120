<!-- {{MODULE_NAME}} - 新增/编辑页面模板 -->
<!--
  AI Agent 页面创建模板 - 标准新增/编辑页面

  模板说明：
  - 基于 FuniDetail 组件的标准新增/编辑页面
  - 支持多步骤流程和业务流程处理
  - 集成附件上传和提交成功提示功能

  适用页面类型：
  - type="add": 新增页面
  - type="edit": 编辑页面

  占位符说明：
  - {{MODULE_NAME}}: 模块中文名称，如"用户管理"
  - {{ENTITY_NAME}}: 实体中文名称，如"用户"
  - {{ENTITY_NAME_EN}}: 实体英文名称，如"user"
  - {{COMPONENT_NAME_1}}: 基本信息组件名称，如"UserBaseInfo"
  - {{COMPONENT_PATH_1}}: 组件路径，如"./components/UserBaseInfo.vue"
  - {{COMPONENT_NAME_2}}: 基本信息组件名称，如"ProfileBaseInfo" (可选)
  - {{COMPONENT_PATH_2}}: 组件路径，如"./components/ProfileBaseInfo.vue" (可选)
  - {{BUSINESS_HOOK}}: 业务逻辑Hook名称，如"useUserBusiness"
  - {{BUSINESS_HOOK_PATH}}: Hook路径，如"@/hooks/useUserBusiness.js"
  - {{BUSINESS_CODE_ADD}}: 新增业务代码，如"USER_CONFIG_CODE_ADD"
  - {{STATUS_FIELD_NAME}}: 状态字段名称，如"节点"
  - {{INFO_FIELD_1}}: businessName字段组成第一部分
  - {{INFO_FIELD_2}}: businessName字段组成第二部分
  - {{INFO_FIELD_3}}: businessName字段组成第三部分
  - {{STEP_1_TITLE}}: 第一步标题，如"基本信息"
  - {{STEP_2_TITLE}}: 第二步标题，如"用户信息" (可选)
  - {{STEP_X_TITLE}}: 第二步标题，如"要件信息"
  - {{FILE_TABLE_COMPONENT}}: 文件表格组件名称，如"FuniFileTable"
-->
<template>
  <div>
    <!-- {{ENTITY_NAME}}详情组件，包含步骤流程和头部信息 -->
    <funi-detail :bizName="bizName" :steps="steps" :detailHeadOption="detailHeadOption || {}" />
    <!-- 提交成功提示组件 -->
    <funi-submit-success ref="submitSuccessRef" />
  </div>
</template>

<script setup lang="jsx">
// Vue 3 Composition API 相关导入
import { ref, computed } from 'vue';
// 路由相关导入
import { useRoute } from 'vue-router';
// 业务逻辑 Hook 导入，用与工作流操作，不可去掉
import { useBusiness } from '@/hooks/useBusiness.js';

// 自定义tab内容组件 - {{ENTITY_NAME}}基本信息组件
import {{COMPONENT_NAME_1}} from '{{COMPONENT_PATH_1}}';
import {{COMPONENT_NAME_2}} from '{{COMPONENT_PATH_2}}';

// 获取当前路由信息
const route = useRoute();
// 获取{{ENTITY_NAME}}业务逻辑处理实例，用与工作流操作，不可去掉
const business = useBusiness();
// 路由查询参数的响应式引用
const routeQuery = ref(route.query);

// {{ENTITY_NAME}}记录ID
const id = ref(routeQuery.value.id);
// {{ENTITY_NAME}}业务名称（从路由参数获取）
const bizName = computed(() => routeQuery.value.bizName);
// {{ENTITY_NAME}}业务类型（从路由参数获取）
const bizType = computed(() => routeQuery.value.type);

// {{ENTITY_NAME}}业务信息数据
const infoData = ref();
// {{ENTITY_NAME}}业务ID
const businessId = ref();

// 详情页头部配置选项
const detailHeadOption = computed(() => {
  return {
    statusName: '{{STATUS_FIELD_NAME}}', // 状态名称
    no: infoData.value?.businessId, // 业务编号
    title: routeQuery.value.title, // 页面标题
    status: infoData.value?.businessNode // 当前业务节点状态
  };
});

// 提交成功组件的引用
const submitSuccessRef = ref();

// 计算{{ENTITY_NAME}}业务名称，由公司名称、部门名称和合同名称组成
const businessName = computed(() => {
  const { {{INFO_FIELD_1}}, {{INFO_FIELD_2}}, {{INFO_FIELD_3}} } = infoData.value || {};
  return `${{{INFO_FIELD_1}}}-${{{INFO_FIELD_2}}}-${{{INFO_FIELD_3}}}`;
});

// 步骤配置，定义{{ENTITY_NAME}}业务流程的各个步骤
const steps = computed(() => {
  // 判断是否为编辑模式（非审核和查看模式）
  const isEdit = !['audit', 'info'].includes(bizType.value);

  return [
    // 第一步：{{ENTITY_NAME}}基本信息填写
    {
      title: '{{STEP_1_TITLE}}', // 步骤标题
      preservable: true, // 是否可保存草稿
      type: {{COMPONENT_NAME_1}}, // 自定义tab内容组件
      // 传递给组件的属性
      props: {
        id: id.value, // {{ENTITY_NAME}}记录ID
        isEdit, // 是否编辑模式
        type: bizType.value, // {{ENTITY_NAME}}业务类型
        bizName: bizName.value // {{ENTITY_NAME}}业务名称
      },
      // 组件事件监听器
      on: {
        // 更新ID事件处理器
        updateID: data => {
          id.value = data?.id;
          businessId.value = data?.businessId;
        },
        // {{ENTITY_NAME}}信息更新事件处理器
        infoUpdate: data => {
          infoData.value = data;
          id.value = data?.id;
          businessId.value = data?.businessId;
        },
        // 其他字段更新事件处理器
        otherUpdate: object => {
          if (object) {
            // 遍历更新对象的所有属性
            Object.entries(object).forEach(([key, value]) => {
              // 更新对应字段，如果新值为空则保持原值
              infoData.value[key] = value || infoData.value[key];
            });
          }
        }
      }
    },
    // 第二步: 可根据业务需求添加更多步骤
    {
      title: '{{STEP_2_TITLE}}', // 步骤标题
      preservable: true, // 是否可保存草稿
      type: {{COMPONENT_NAME_2}}, // 自定义tab内容组件
      // 传递给组件的属性
      props: {
        id: id.value, // {{ENTITY_NAME}}记录ID
        isEdit, // 是否编辑模式
        type: bizType.value, // {{ENTITY_NAME}}业务类型
        bizName: bizName.value // {{ENTITY_NAME}}业务名称
      },
      // 组件事件监听器
      on: {
        // 更新ID事件处理器
        updateID: data => {
          id.value = data?.id;
          businessId.value = data?.businessId;
        },
        // {{ENTITY_NAME}}信息更新事件处理器
        infoUpdate: data => {
          infoData.value = data;
          id.value = data?.id;
          businessId.value = data?.businessId;
        },
        // 其他字段更新事件处理器
        otherUpdate: object => {
          if (object) {
            // 遍历更新对象的所有属性
            Object.entries(object).forEach(([key, value]) => {
              // 更新对应字段，如果新值为空则保持原值
              infoData.value[key] = value || infoData.value[key];
            });
          }
        }
      }
    },
    // ...
    // 最后一步：{{ENTITY_NAME}}要件信息
    {
      title: '要件信息', // 步骤标题
      preservable: false, // 不可保存草稿
      type: 'FuniFileTable', // 使用系统文件表格组件
      props: {
        params: { businessId: businessId.value }, // 传递业务ID参数
        callbackFun: submitBusiness // 提交回调函数
      }
    }
  ];
});

/**
 * 提交{{ENTITY_NAME}}业务流程
 * 根据业务类型确定业务代码，然后调用业务提交接口
 */
const submitBusiness = async () => {
  // 根据{{ENTITY_NAME}}业务名称确定业务代码
  const businessCode =
    bizName.value === '新增'
      ? '{{BUSINESS_CODE_ADD}}' // {{ENTITY_NAME}}新增业务代码
      : routeQuery.value.dicBusinessTypeCode; // 其他{{ENTITY_NAME}}业务类型代码

  // 调用{{ENTITY_NAME}}业务提交接口
  await business.submit(businessCode, businessId.value, 'SUBMIT', businessName.value);

  // 显示{{ENTITY_NAME}}提交成功提示
  submitSuccessRef.value.show();
};
</script>
