<!-- {{MODULE_NAME}} - 列表页面模板 -->
<!--
  AI Agent 页面创建模板 - 标准列表页

  模板说明：
  - 基于 FuniListPageV2 组件的标准列表页面
  - 支持多页签配置和标准CRUD操作
  - 集成权限控制、搜索配置、数据脱敏等企业级功能
  - 支持动态按钮生成和完善的错误处理
  - 路由跳转参数与 add-page.vue 和 detail-page.vue 保持一致

  路由跳转规则：
  - 新增页面：type="add", bizName="新增"
  - 编辑页面：type="edit", bizName="编辑"
  - 详情页面：type="info", bizName="详情"
  - 审核页面：type="audit", bizName="审核"

  占位符说明：
  - {{MODULE_NAME}}: 模块中文名称，如"用户管理"
  - {{ENTITY_NAME}}: 实体中文名称，如"用户"
  - {{ENTITY_NAME_EN}}: 实体英文名称，如"user"
  - {{API_FUNCTION}}: API函数名称，如"queryUserList"
  - {{DELETE_API_FUNCTION}}: 删除API函数名称，如"deleteUser"
  - {{EXPORT_API_URL}}: 导出API地址，如"ApiUrl.userExport"
  - {{PRIMARY_KEY}}: 主键字段，如"id"
  - {{DISPLAY_FIELD}}: 显示字段，如"name"
  - {{BUSINESS_NO_FIELD}}: 业务编号字段，如"businessNo"
  - {{CREATE_ROUTE}}: 新建页面路由，如"/user-management/create"
  - {{DETAIL_ROUTE}}: 详情页面路由，如"/user-management/detail"
  - {{EDIT_ROUTE}}: 编辑页面路由，如"/user-management/edit"
  - {{PAGE_CODE}}: 搜索配置页面代码，如"user_management_list"
  - {{AUTH_PREFIX}}: 权限前缀，如"user_management"
  - {{BUSINESS_TYPE_CODE}}: 业务类型代码，如"USER_MANAGEMENT"
-->
<template>
  <funi-list-page-v2 ref="listPageRef" :cardTab="cardTabConfig" @headBtnClick="handleHeadBtnClick" />
</template>

<script setup lang="jsx">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElNotification, ElMessageBox } from 'element-plus';
import { {{API_FUNCTION}}, {{DELETE_API_FUNCTION}}, {{EXPORT_API_URL}} } from '@/apps/{{APP_NAME}}/api/{{API_MODULE}}/index';
import { expotrFunction } from '@/apps/{{APP_NAME}}/common/js/export';

// ==================== 基础变量配置 ====================
const listPageRef = ref();
const router = useRouter();

// ==================== 权限配置 ====================
/**
 * 权限配置对象
 * 根据实际业务需求配置对应的权限码
 */
const auths = {
  export: '{{AUTH_PREFIX}}_export', // 导出权限
  add: '{{AUTH_PREFIX}}_add', // 新增权限
  delete: '{{AUTH_PREFIX}}_delete', // 删除权限
  audit: '{{AUTH_PREFIX}}_audit', // 审核权限
  edit: '{{AUTH_PREFIX}}_edit', // 编辑权限
  revocation: '{{AUTH_PREFIX}}_revocation', // 撤回权限
  detail: '{{AUTH_PREFIX}}_detail' // 详情权限
};

// ==================== 数据状态管理 ====================
// 列表查询参数存储
const listPageParams = ref({});

// ==================== 核心业务函数 ====================

/**
 * 跳转到详情页面
 * @param {Object} row - 当前行数据
 */
const goDetail = row => {
  router.push({
    path: '{{DETAIL_ROUTE}}',
    query: {
      id: row.{{PRIMARY_KEY}},                        // 记录ID
      {{BUSINESS_NO_FIELD}}: row.{{BUSINESS_NO_FIELD}},  // 业务编号
      type: 'info',                                   // 页面类型：详情
      bizName: '详情',                                // 业务名称
      title: row.{{DISPLAY_FIELD}} || '{{ENTITY_NAME}}详情',  // 页面标题
      tab: ['{{ENTITY_NAME}}', row.{{DISPLAY_FIELD}} || '', '详情'].join('-')  // 标签页标题
    }
  });
};

/**
 * 删除记录
 * @param {string|number} id - 记录ID
 */
const deleteItem = async id => {
  try {
    await {{DELETE_API_FUNCTION}}({ {{PRIMARY_KEY}}: id });
    ElNotification({
      title: '提示',
      message: '删除成功',
      type: 'success',
      duration: 2000
    });
    listPageRef.value.reload({ resetPage: false });
  } catch (error) {
    console.error('删除失败:', error);
    ElNotification({
      title: '错误',
      message: '删除失败，请重试',
      type: 'error',
      duration: 2000
    });
  }
};

/**
 * 跳转到编辑页面
 * @param {Object} row - 当前行数据
 */
const editItem = row => {
  router.push({
    path: '{{EDIT_ROUTE}}',
    query: {
      id: row.{{PRIMARY_KEY}},                        // 记录ID
      {{BUSINESS_NO_FIELD}}: row.{{BUSINESS_NO_FIELD}},  // 业务编号
      type: 'edit',                                   // 页面类型：编辑
      bizName: '编辑',                                // 业务名称
      title: row.{{DISPLAY_FIELD}} || '编辑{{ENTITY_NAME}}',  // 页面标题
      tab: ['{{ENTITY_NAME}}', row.{{DISPLAY_FIELD}} || '', '编辑'].join('-')  // 标签页标题
    }
  });
};

/**
 * 跳转到审核页面
 * @param {Object} row - 当前行数据
 */
const audit = row => {
  router.push({
    path: '{{DETAIL_ROUTE}}',
    query: {
      id: row.{{PRIMARY_KEY}},                        // 记录ID
      {{BUSINESS_NO_FIELD}}: row.{{BUSINESS_NO_FIELD}},  // 业务编号
      type: 'audit',                                  // 页面类型：审核
      bizName: '审核',                                // 业务名称
      title: row.{{DISPLAY_FIELD}} || '审核{{ENTITY_NAME}}',  // 页面标题
      tab: ['{{ENTITY_NAME}}', row.{{DISPLAY_FIELD}} || '', '审核'].join('-'),  // 标签页标题
      businessId: row.businessId,                     // 业务ID（审核页面需要）
      dicBusinessTypeCode: row.dicBusinessTypeCode    // 业务类型代码（审核页面需要）
    }
  });
};

// 撤销功能（可选）
const revocation = row => {
  ElMessageBox.confirm('确认撤销该记录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 这里调用撤销API
    ElNotification({
      title: '提示',
      message: '撤销成功',
      type: 'success',
      duration: 2000
    });
    listPageRef.value.reload({ resetPage: false });
  }).catch(() => {
    // 用户取消操作
  });
};

// ==================== 动态按钮生成器 ====================

/**
 * 动态按钮生成器 - 集成版本
 *
 * 功能说明：
 * 1. 基于 generateListButton.jsx 的核心逻辑重构
 * 2. 支持权限控制（v-auth 指令）
 * 3. 支持动态按钮渲染（基于 row.buttonList）
 * 4. 内置常用按钮类型（审核、编辑、删除、撤回、详情）
 * 5. 删除按钮自带确认弹窗
 *
 * @param {Object} row - 当前行数据，必须包含 buttonList 数组
 * @param {Object} btnFunctions - 按钮事件处理函数映射
 * @param {Object} authConfig - 权限配置对象
 * @returns {JSX.Element} 渲染的按钮组
 *
 * 使用示例：
 * generateActionButtons(row, {
 *   EDIT_BUTTON: editItem,
 *   DELETE_BUTTON: deleteItem
 * }, auths)
 */
const generateActionButtons = (row, btnFunctions = {}, authConfig = {}) => {
  // 按钮配置映射表 - 定义所有支持的按钮类型
  const buttonTypeConfig = {
    // 审核按钮 - 跳转到审核页面
    AUDIT_BUTTON: {
      component: (row, fn, auths) => (
        <el-link
          type="primary"
          v-auth={auths.audit}
          onClick={() => fn(row)}
          style={{ marginRight: '20px' }}
        >
          审核
        </el-link>
      )
    },

    // 撤回按钮 - 撤回当前记录
    REVOCATION: {
      component: (row, fn, auths) => (
        <el-link
          type="primary"
          v-auth={auths.revocation}
          onClick={() => fn(row)}
          style={{ marginRight: '20px' }}
        >
          撤回
        </el-link>
      )
    },

    // 编辑按钮 - 跳转到编辑页面
    EDIT_BUTTON: {
      component: (row, fn, auths) => (
        <el-link
          type="primary"
          v-auth={auths.edit}
          onClick={() => fn(row)}
          style={{ marginRight: '20px' }}
        >
          编辑
        </el-link>
      )
    },

    // 删除按钮 - 带确认弹窗的删除操作
    DELETE_BUTTON: {
      component: (row, fn, auths) => (
        <el-popconfirm
          title="是否删除?"
          onConfirm={() => fn(row.{{PRIMARY_KEY}})}
        >
          {{
            reference: () => (
              <el-link type="primary" v-auth={auths.delete}>
                删除
              </el-link>
            )
          }}
        </el-popconfirm>
      )
    },

    // 详情按钮 - 跳转到详情页面
    DETAIL_BUTTON: {
      component: (row, fn, auths) => (
        <el-link
          type="primary"
          v-auth={auths.detail}
          onClick={() => fn(row)}
          style={{ marginRight: '20px' }}
        >
          详情
        </el-link>
      )
    }
  };

  // 数据验证：检查 row.buttonList 是否存在且为数组
  if (!row.buttonList || !Array.isArray(row.buttonList)) {
    return <span>--</span>;
  }

  // 动态渲染按钮：根据 buttonList 配置生成对应按钮
  return (
    <>
      {row.buttonList.map((buttonKey, index) => {
        const buttonConfig = buttonTypeConfig[buttonKey];
        const buttonFunction = btnFunctions[buttonKey];

        // 安全检查：如果按钮配置或处理函数不存在，跳过该按钮
        if (!buttonConfig || !buttonFunction) {
          console.warn(`按钮配置缺失: ${buttonKey}`);
          return null;
        }

        return (
          <span key={`${buttonKey}-${index}`}>
            {buttonConfig.component(row, buttonFunction, authConfig)}
          </span>
        );
      })}
    </>
  );
};

/**
 * 表格列配置
 * 配置说明：
 * 1.一般列无需设置width
 * 2.操作列设置固定宽度
 * 3.特殊情况下可以设置width
 */
const columns = reactive([
  {
    label: '{{BUSINESS_NO_FIELD}}',
    prop: '{{BUSINESS_NO_FIELD}}',
    fixed: 'left',
    render: ({ row, index }) => {
      return (
         <el-link type="primary" onClick={() => goDetail(row)}>{row.{{BUSINESS_NO_FIELD}} || '--'}</el-link>
      );
    }
  },
  {
    label: '状态',
    prop: 'statusName',
    render: ({ row }) => (
      <el-tag type={row.status === 1 ? 'success' : 'danger'}>
        {row.statusName || '--'}
      </el-tag>
    )
  },
  {
    label: '{{DISPLAY_FIELD}}',
    prop: '{{DISPLAY_FIELD}}',
  },
  {
    label: '操作',
    prop: 'actions',
    width: 150,
    fixed: 'right',
    render: ({ row }) => {
      return (
        <div>
          {generateActionButtons(
            row,
            {
              AUDIT_BUTTON: audit,
              REVOCATION: revocation,
              EDIT_BUTTON: editItem,
              DELETE_BUTTON: deleteItem,
              DETAIL_BUTTON: goDetail
            },
            auths
          )}
        </div>
      );
    }
  }
]);

// 数据加载函数
const loadData = async (pageParams, searchParams) => {
  listPageParams.value = searchParams;
  return await {{API_FUNCTION}}({
    ...pageParams,
    ...searchParams,
    // 添加业务特定参数
    // dicBusinessTypeCode: '{{BUSINESS_TYPE_CODE}}'
  });
};

const loadDataWithHandle = async (pageParams, searchParams, handle) => {
  listPageParams.value = searchParams;
  return await {{API_FUNCTION}}({
    ...pageParams,
    ...searchParams,
    handle,
    // dicBusinessTypeCode: '{{BUSINESS_TYPE_CODE}}'
  });
};

// 页签配置
const cardTabConfig = computed(() => {
  return [
    {
      label: '待办件',
      key: 'pending',
      curdOption: {
        columns,
        lodaData: (pageParams, searchParams) => loadDataWithHandle(pageParams, searchParams, 0),
        btns: [
          { key: 'add', label: '新增', auth: auths.add, type: 'primary' },
          { key: 'export', label: '导出', auth: auths.export }
        ],
        fixedButtons: true,
        reloadOnActive: true,
        requestParams: { handle: 0 }
      }
    },
    {
      label: '已办件',
      key: 'completed',
      curdOption: {
        columns: columns.slice(0, -1), // 移除操作列
        lodaData: (pageParams, searchParams) => loadDataWithHandle(pageParams, searchParams, 1),
        btns: [{ key: 'exportCompleted', label: '导出', auth: auths.export }],
        fixedButtons: true,
        reloadOnActive: true,
        requestParams: { handle: 1 }
      }
    }
  ];
});

// 导出功能
const exportPending = async () => {
  try {
    await expotrFunction({
      url: {{EXPORT_API_URL}},
      FileName: '{{MODULE_NAME}}列表',
      params: {
        // dicBusinessTypeCode: '{{BUSINESS_TYPE_CODE}}',
        flag: false,
        ...listPageParams.value,
        handle: 0
      }
    });
  } catch (error) {
    console.error('导出失败:', error);
  }
};

const exportCompleted = async () => {
  try {
    await expotrFunction({
      url: {{EXPORT_API_URL}},
      FileName: '{{MODULE_NAME}}列表',
      params: {
        // dicBusinessTypeCode: '{{BUSINESS_TYPE_CODE}}',
        flag: false,
        ...listPageParams.value,
        handle: 1
      }
    });
  } catch (error) {
    console.error('导出失败:', error);
  }
};

// 新增功能
const addItem = () => {
  router.push({
    path: '{{CREATE_ROUTE}}',
    query: {
      type: 'add',                                    // 页面类型：新增
      bizName: '新增',                                // 业务名称
      title: '新增{{ENTITY_NAME}}',                   // 页面标题
      tab: '{{ENTITY_NAME}}-新增'                     // 标签页标题
    }
  });
};

// 头部按钮点击事件处理
const handleHeadBtnClick = key => {
  switch (key) {
    case 'add':
      addItem();
      break;
    case 'export':
      exportPending();
      break;
    case 'exportCompleted':
      exportCompleted();
      break;
    case '{{BUTTON_KEY}}':
      {{FUNCTION_NAME}}();
      break;

    default:
      break;
  }
};

// ==================== 生命周期钩子 ====================

/**
 * 组件挂载完成
 * 可在此处执行初始化逻辑
 */
onMounted(() => {
  console.log('{{MODULE_NAME}}列表页面已挂载');
  // 可选：执行初始化数据加载
  // listPageRef.value?.reload();
});
</script>
