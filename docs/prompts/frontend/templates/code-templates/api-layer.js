/**
 * 采购计划管理 - API层模板
 *
 * 模板说明：
 * - 标准的API接口封装模板
 * - 基于 window.$http 的统一请求方法
 * - 包含完整的CRUD操作接口
 *
 * 占位符说明：
 * - 采购计划管理: 模块中文名称，如"用户管理"
 * - 采购计划: 实体中文名称，如"用户"
 * - procurementPlan: 实体英文名称，如"user"
 * - /api/procurement-plans: API基础路径，如"/api/users"
 */
import { enableMock, mockProcurementPlanList } from '../__mocks__/index.js';
/**
 * 采购计划管理相关API接口
 */
export const procurementPlanApi = {
  /**
   * 获取采购计划列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页数量
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.status - 状态筛选
   * @returns {Promise} 返回采购计划列表数据
   */
  getList(params = {}) {
    if (enableMock) {
      return { total: mockProcurementPlanList.length, list: mockProcurementPlanList };
    }
    return window.$http.post('/api/procurement-plans/list', {
      page: params.page || 1,
      size: params.size || 10,
      keyword: params.keyword || '',
      status: params.status
    });
  },

  /**
   * 根据ID获取采购计划详情
   * @param {string|number} id - 采购计划ID
   * @returns {Promise} 返回采购计划详情数据
   */
  getById(id) {
    if (enableMock) {
      return mockProcurementPlanList.find(item => item.id === id);
    }
    return window.$http.fetch(`/api/procurement-plans/${id}`);
  },

  /**
   * 创建新采购计划
   * @param {Object} data - 采购计划数据
   * @param {string} data.name - 采购计划名称
   * @param {number} data.status - 状态
   * @param {string} data.remark - 备注
   * @returns {Promise} 返回创建结果
   */
  create(data) {
    if (enableMock) {
      return { id: Date.now(), ...data };
    }
    return window.$http.post('/api/procurement-plans/create', {
      name: data.name,
      status: data.status || 1,
      remark: data.remark || ''
    });
  },

  /**
   * 更新采购计划信息
   * @param {string|number} id - 采购计划ID
   * @param {Object} data - 更新数据
   * @param {string} data.name - 采购计划名称
   * @param {number} data.status - 状态
   * @param {string} data.remark - 备注
   * @returns {Promise} 返回更新结果
   */
  update(id, data) {
    if (enableMock) {
      const item = mockProcurementPlanList.find(item => item.id === id);
      if (item) {
        Object.assign(item, data);
        return item;
      }
      return null;
    }
    return window.$http.post(`/api/procurement-plans/update/${id}`, {
      name: data.name,
      status: data.status,
      remark: data.remark
    });
  },

  /**
   * 删除采购计划
   * @param {string|number} id - 采购计划ID
   * @returns {Promise} 返回删除结果
   */
  delete(id) {
    if (enableMock) {
      const index = mockProcurementPlanList.findIndex(item => item.id === id);
      if (index > -1) {
        mockProcurementPlanList.splice(index, 1);
      }
      return true;
    }
    return window.$http.post(`/api/procurement-plans/delete/${id}`);
  },

  /**
   * 批量删除采购计划
   * @param {Array} ids - 采购计划ID数组
   * @returns {Promise} 返回批量删除结果
   */
  batchDelete(ids) {
    return window.$http.post('/api/procurement-plans/batch-delete', {
      ids: ids
    });
  },

  /**
   * 更新采购计划状态
   * @param {string|number} id - 采购计划ID
   * @param {number} status - 新状态 (1:启用, 0:禁用)
   * @returns {Promise} 返回状态更新结果
   */
  updateStatus(id, status) {
    return window.$http.post(`/api/procurement-plans/update-status/${id}`, {
      status: status
    });
  },

  /**
   * 导出采购计划数据
   * @param {Object} params - 导出参数
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.status - 状态筛选
   * @returns {Promise} 返回导出文件信息
   */
  export(params = {}) {
    return window.$http.post('/api/procurement-plans/export', {
      keyword: params.keyword || '',
      status: params.status
    });
  }
};

// 默认导出
export default procurementPlanApi;
