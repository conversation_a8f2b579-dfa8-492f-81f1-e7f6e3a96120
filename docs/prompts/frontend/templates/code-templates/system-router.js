/**
 * {{SYSTEM_TITLE}} - 系统级路由配置
 * 
 * 模板说明：
 * - 系统级路由配置模板
 * - 负责集成模块路由到系统中
 * - 提供系统级菜单和导航结构
 * 
 * 占位符说明：
 * - {{SYSTEM_CODE}}: 系统编码，如"bidding-procurement"
 * - {{SYSTEM_NAME}}: 系统名称，如"BiddingProcurement"
 * - {{SYSTEM_TITLE}}: 系统中文名称，如"招标采购系统"
 * - {{SYSTEM_ICON}}: 系统图标，如"bidding"
 * - {{DEFAULT_MODULE}}: 默认模块，如"procurement-plan"
 */

// 导入模块路由
// 注意：模块路由导入将在系统集成时自动添加

/**
 * {{SYSTEM_TITLE}}系统路由配置
 */
export default {
  path: '/{{SYSTEM_CODE}}',
  name: '{{SYSTEM_NAME}}',
  redirect: '/{{SYSTEM_CODE}}/{{DEFAULT_MODULE}}',
  meta: {
    title: '{{SYSTEM_TITLE}}',
    icon: '{{SYSTEM_ICON}}',
    isMenu: true
  },
  children: [
    // 模块路由将在系统集成时自动添加到这里
    // 例如：
    // procurementPlanRouter,
    // bidManagementRouter,
    // contractManagementRouter
  ]
};
