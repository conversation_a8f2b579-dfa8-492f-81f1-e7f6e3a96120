/**
 * {{MODULE_NAME}} - Adapters层模板
 * 
 * 模板说明：
 * - 纯函数数据转换模板
 * - 负责API数据与前端数据结构的转换
 * - 不调用其他层，保持纯函数特性
 * 
 * 占位符说明：
 * - {{MODULE_NAME}}: 模块中文名称，如"用户管理"
 * - {{ENTITY_NAME}}: 实体中文名称，如"用户"
 * - {{ENTITY_NAME_EN}}: 实体英文名称，如"user"
 */

/**
 * {{MODULE_NAME}} 数据转换适配器
 */
export const {{ENTITY_NAME_EN}}Adapters = {
  /**
   * 适配列表数据
   * 将API返回的列表数据转换为前端需要的格式
   * @param {Object} apiResponse - API响应数据
   * @returns {Object} 转换后的列表数据
   */
  adaptListData(apiResponse) {
    if (!apiResponse) {
      return { list: [], total: 0 };
    }

    const { list, total } = apiResponse;
    
    return {
      list: list.map(item => this.adaptItemData(item)),
      total: total || 0,
    };
  },

  /**
   * 适配单个{{ENTITY_NAME}}数据
   * 将API返回的单个{{ENTITY_NAME}}数据转换为前端格式
   * @param {Object} apiItem - API返回的单个{{ENTITY_NAME}}数据
   * @returns {Object} 转换后的{{ENTITY_NAME}}数据
   */
  adaptItemData(apiItem) {
    if (!apiItem) return null;

    return {
      id: apiItem.id || '',
      name: apiItem.name || '',
      status: apiItem.status !== undefined ? apiItem.status : 1,
      statusText: this.getStatusText(apiItem.status),
      remark: apiItem.remark || '',
      createTime: this.formatDateTime(apiItem.createTime),
      updateTime: this.formatDateTime(apiItem.updateTime),
      createUser: apiItem.createUser || '',
      updateUser: apiItem.updateUser || ''
    };
  },

  /**
   * 适配详情数据
   * 将API返回的详情数据转换为前端格式
   * @param {Object} apiResponse - API响应数据
   * @returns {Object} 转换后的详情数据
   */
  adaptDetailData(apiResponse) {
    if (!apiResponse) return null;
    
    return this.adaptItemData(apiResponse);
  },

  /**
   * 适配创建数据
   * 将前端表单数据转换为API需要的格式
   * @param {Object} formData - 前端表单数据
   * @returns {Object} 转换后的API数据
   */
  adaptCreateData(formData) {
    if (!formData) return {};

    return {
      name: formData.name || '',
      status: formData.status !== undefined ? formData.status : 1,
      remark: formData.remark || ''
    };
  },

  /**
   * 适配更新数据
   * 将前端表单数据转换为API需要的格式
   * @param {Object} formData - 前端表单数据
   * @returns {Object} 转换后的API数据
   */
  adaptUpdateData(formData) {
    if (!formData) return {};

    return {
      name: formData.name || '',
      status: formData.status !== undefined ? formData.status : 1,
      remark: formData.remark || ''
    };
  },

  /**
   * 获取状态文本
   * @param {number} status - 状态值
   * @returns {string} 状态文本
   */
  getStatusText(status) {
    const statusMap = {
      1: '启用',
      0: '禁用'
    };
    return statusMap[status] || '未知';
  },

  /**
   * 格式化日期时间
   * @param {string|Date} dateTime - 日期时间
   * @returns {string} 格式化后的日期时间字符串
   */
  formatDateTime(dateTime) {
    if (!dateTime) return '';
    
    try {
      const date = new Date(dateTime);
      if (isNaN(date.getTime())) return '';
      
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (error) {
      console.warn('日期格式化失败:', error);
      return '';
    }
  },

  /**
   * 格式化日期
   * @param {string|Date} date - 日期
   * @returns {string} 格式化后的日期字符串
   */
  formatDate(date) {
    if (!date) return '';
    
    try {
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) return '';
      
      return dateObj.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      console.warn('日期格式化失败:', error);
      return '';
    }
  },

  /**
   * 适配搜索参数
   * 将前端搜索条件转换为API参数
   * @param {Object} searchParams - 前端搜索参数
   * @returns {Object} 转换后的API参数
   */
  adaptSearchParams(searchParams) {
    if (!searchParams) return {};

    return {
      keyword: searchParams.keyword || '',
      status: searchParams.status !== undefined ? searchParams.status : null,
      startDate: searchParams.startDate || '',
      endDate: searchParams.endDate || ''
    };
  }
};

// 默认导出
export default {{ENTITY_NAME_EN}}Adapters;
