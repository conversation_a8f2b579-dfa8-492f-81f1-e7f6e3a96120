/**
 * {{MODULE_NAME}} - Store层模板
 * 
 * 模板说明：
 * - 基于 Pinia 的状态管理模板
 * - 集成API层和Adapters层调用
 * - 包含完整的业务逻辑封装
 * 
 * 占位符说明：
 * - {{MODULE_NAME}}: 模块中文名称，如"用户管理"
 * - {{ENTITY_NAME}}: 实体中文名称，如"用户"
 * - {{ENTITY_NAME_EN}}: 实体英文名称，如"user"
 * - {{STORE_NAME}}: Store名称，如"useUserStore"
 */

import { defineStore } from 'pinia';
import { ref, reactive } from 'vue';
import { {{ENTITY_NAME_EN}}Api } from './api/index.js';
import { {{ENTITY_NAME_EN}}Adapters } from './adapters/index.js';

/**
 * {{MODULE_NAME}} Store
 */
export const {{STORE_NAME}} = defineStore('{{ENTITY_NAME_EN}}', () => {
  // 状态定义
  const {{ENTITY_NAME_EN}}List = ref([]);
  const current{{ENTITY_NAME}} = ref(null);
  const loading = ref(false);
  const pagination = reactive({
    page: 1,
    size: 10,
    total: 0
  });

  // 搜索条件
  const searchParams = reactive({
    keyword: '',
    status: null
  });

  /**
   * 获取{{ENTITY_NAME}}列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 返回处理后的列表数据
   */
  const get{{ENTITY_NAME}}List = async (params = {}) => {
    try {
      loading.value = true;
      
      // 合并查询参数
      const queryParams = {
        ...searchParams,
        ...params,
        page: pagination.page,
        size: pagination.size
      };

      // 调用API
      const response = await {{ENTITY_NAME_EN}}Api.getList(queryParams);
      
      // 数据转换
      const adaptedData = {{ENTITY_NAME_EN}}Adapters.adaptListData(response);
      
      // 更新状态
      {{ENTITY_NAME_EN}}List.value = adaptedData.list;
      pagination.total = adaptedData.total;
      
      return adaptedData;
    } catch (error) {
      console.error('获取{{ENTITY_NAME}}列表失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 根据ID获取{{ENTITY_NAME}}详情
   * @param {string|number} id - {{ENTITY_NAME}}ID
   * @returns {Promise} 返回处理后的详情数据
   */
  const get{{ENTITY_NAME}} = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await {{ENTITY_NAME_EN}}Api.getById(id);
      
      // 数据转换
      const adaptedData = {{ENTITY_NAME_EN}}Adapters.adaptDetailData(response);
      
      // 更新状态
      current{{ENTITY_NAME}}.value = adaptedData;
      
      return adaptedData;
    } catch (error) {
      console.error('获取{{ENTITY_NAME}}详情失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 创建新{{ENTITY_NAME}}
   * @param {Object} data - {{ENTITY_NAME}}数据
   * @returns {Promise} 返回创建结果
   */
  const create{{ENTITY_NAME}} = async (data) => {
    try {
      loading.value = true;
      
      // 数据转换
      const apiData = {{ENTITY_NAME_EN}}Adapters.adaptCreateData(data);
      
      // 调用API
      const response = await {{ENTITY_NAME_EN}}Api.create(apiData);
      
      // 数据转换
      const adaptedData = {{ENTITY_NAME_EN}}Adapters.adaptDetailData(response);
      
      return adaptedData;
    } catch (error) {
      console.error('创建{{ENTITY_NAME}}失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新{{ENTITY_NAME}}信息
   * @param {string|number} id - {{ENTITY_NAME}}ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 返回更新结果
   */
  const update{{ENTITY_NAME}} = async (id, data) => {
    try {
      loading.value = true;
      
      // 数据转换
      const apiData = {{ENTITY_NAME_EN}}Adapters.adaptUpdateData(data);
      
      // 调用API
      const response = await {{ENTITY_NAME_EN}}Api.update(id, apiData);
      
      // 数据转换
      const adaptedData = {{ENTITY_NAME_EN}}Adapters.adaptDetailData(response);
      
      // 更新当前{{ENTITY_NAME}}状态
      if (current{{ENTITY_NAME}}.value && current{{ENTITY_NAME}}.value.id === id) {
        current{{ENTITY_NAME}}.value = adaptedData;
      }
      
      return adaptedData;
    } catch (error) {
      console.error('更新{{ENTITY_NAME}}失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 删除{{ENTITY_NAME}}
   * @param {string|number} id - {{ENTITY_NAME}}ID
   * @returns {Promise} 返回删除结果
   */
  const delete{{ENTITY_NAME}} = async (id) => {
    try {
      loading.value = true;
      
      // 调用API
      const response = await {{ENTITY_NAME_EN}}Api.delete(id);
      
      // 从列表中移除
      const index = {{ENTITY_NAME_EN}}List.value.findIndex(item => item.id === id);
      if (index > -1) {
        {{ENTITY_NAME_EN}}List.value.splice(index, 1);
        pagination.total--;
      }
      
      return response;
    } catch (error) {
      console.error('删除{{ENTITY_NAME}}失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 更新搜索条件
   * @param {Object} params - 搜索参数
   */
  const updateSearchParams = (params) => {
    Object.assign(searchParams, params);
    pagination.page = 1; // 重置页码
  };

  /**
   * 更新分页信息
   * @param {Object} paginationData - 分页数据
   */
  const updatePagination = (paginationData) => {
    Object.assign(pagination, paginationData);
  };

  /**
   * 重置状态
   */
  const resetState = () => {
    {{ENTITY_NAME_EN}}List.value = [];
    current{{ENTITY_NAME}}.value = null;
    loading.value = false;
    pagination.page = 1;
    pagination.total = 0;
    searchParams.keyword = '';
    searchParams.status = null;
  };

  // 返回状态和方法
  return {
    // 状态
    {{ENTITY_NAME_EN}}List,
    current{{ENTITY_NAME}},
    loading,
    pagination,
    searchParams,
    
    // 方法
    get{{ENTITY_NAME}}List,
    get{{ENTITY_NAME}},
    create{{ENTITY_NAME}},
    update{{ENTITY_NAME}},
    delete{{ENTITY_NAME}},
    updateSearchParams,
    updatePagination,
    resetState
  };
});
