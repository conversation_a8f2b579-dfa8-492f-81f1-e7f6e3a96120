# 模块开发任务模板使用指南

## 模板概述

`module-development-template.md` 是为任务分解引擎设计的标准化模块开发任务模板，用于快速生成完整的功能模块开发文档。

## 占位符参数说明

### 基础参数

| 占位符              | 说明         | 示例                 |
| ------------------- | ------------ | -------------------- |
| `{{MODULE_NAME}}`   | 模块中文名称 | 公告管理             |
| `{{ENTITY_NAME}}`   | 实体中文名称 | 公告                 |
| `{{API_ENDPOINT}}`  | API接口路径  | /api/announcements   |
| `{{STORE_NAME}}`    | Store名称    | useAnnouncementStore |
| `{{PRIMARY_KEY}}`   | 主键字段名   | id                   |
| `{{DISPLAY_FIELD}}` | 显示字段名   | title                |

### 路径参数

| 占位符                   | 说明         | 示例                                                                               |
| ------------------------ | ------------ | ---------------------------------------------------------------------------------- |
| `{{MODULE_PATH}}`        | 模块完整路径 | src/apps/bidding-procurement/modules/procurement-execution/announcement-management |
| `{{ROUTE_PATH}}`         | 路由路径     | announcement-management                                                            |
| `{{ROUTE_NAME}}`         | 路由名称     | AnnouncementManagement                                                             |
| `{{ROUTE_IMPORT_NAME}}`  | 路由导入名称 | announcementRouter                                                                 |
| `{{PARENT_MODULE}}`      | 父模块名称   | 采购执行管理                                                                       |
| `{{PARENT_MODULE_PATH}}` | 父模块路径   | src/apps/bidding-procurement/modules/procurement-execution                         |

### 功能参数

| 占位符                     | 说明            | 示例                                                                                                     |
| -------------------------- | --------------- | -------------------------------------------------------------------------------------------------------- |
| `{{API_METHODS_LIST}}`     | API方法清单     | - `getAnnouncementList(params)` - 获取公告列表<br>- `getAnnouncementDetail(id)` - 获取公告详情           |
| `{{ADAPTER_METHODS_LIST}}` | 适配器方法清单  | - `formatAnnouncementList(data)` - 格式化列表数据<br>- `formatAnnouncementDetail(data)` - 格式化详情数据 |
| `{{API_METHODS}}`          | API方法简化列表 | getList, getDetail, create, update, delete, submit, audit                                                |
| `{{TAB_CONFIG}}`           | 页签配置        | 全部, 待审核, 审核中, 已发布                                                                             |
| `{{SEARCH_FIELDS}}`        | 搜索字段        | 公告名称、审核状态、发布状态、采购类型等                                                                 |
| `{{OPERATION_BUTTONS}}`    | 操作按钮        | 编辑、删除、提交、审核、发布等                                                                           |

### 页面结构参数

| 占位符                | 说明           | 示例                                                         |
| --------------------- | -------------- | ------------------------------------------------------------ |
| `{{DETAIL_TABS}}`     | 详情页标签页   | - 公告信息页签<br>- 项目信息页签<br>- 流程记录页签           |
| `{{CREATE_FEATURES}}` | 新建页功能特性 | - 标段选择和信息自动带出<br>- 富文本编辑器<br>- 文件上传功能 |

### 业务规则参数

| 占位符                  | 说明         | 示例                                                                                              |
| ----------------------- | ------------ | ------------------------------------------------------------------------------------------------- |
| `{{STATUS_FLOW_RULES}}` | 状态流转规则 | `<br>待审核 → 审核中 → 审核通过 → 已发布<br>   ↓        ↓        ↓<br> 删除    撤销     撤销<br>` |
| `{{PERMISSION_MATRIX}}` | 权限矩阵表格 | 状态与操作权限的对应关系表                                                                        |
| `{{VALIDATION_RULES}}`  | 字段验证规则 | - **公告标题**: 必填，不超过255字符<br>- **选择标段**: 必选，自动带出项目信息                     |

### 验证参数

| 占位符                       | 说明           | 示例                                                             |
| ---------------------------- | -------------- | ---------------------------------------------------------------- |
| `{{VERIFICATION_CHECKLIST}}` | 功能验证清单   | - 列表查询和操作功能<br>- 新建和编辑功能<br>- 审核流程功能       |
| `{{FUNCTIONAL_CHECKLIST}}`   | 功能完整性检查 | - [ ] 公告列表查询和筛选功能正常<br>- [ ] 公告新建和编辑功能正常 |

### 集成参数

| 占位符                              | 说明         | 示例                                             |
| ----------------------------------- | ------------ | ------------------------------------------------ |
| `{{ROUTE_INTEGRATION_INSTRUCTION}}` | 路由集成指令 | 取消第28行注释，确保announcementRouter被正确引用 |
| `{{ENTITY_STRUCTURE}}`              | 实体数据结构 | JavaScript对象结构定义                           |
| `{{DEVELOPMENT_NOTES}}`             | 开发注意事项 | 编号列表的开发要点和约束                         |

## 使用流程

### 1. 参数准备

根据PRD文档和技术方案，准备所有必要的占位符参数值。

### 2. 模板替换

使用任务分解引擎的占位符替换机制，将模板中的所有占位符替换为实际值。

### 3. 内容定制

根据具体模块的业务特点，调整以下可变内容：

- API方法清单
- 页面功能特性
- 业务规则定义
- 验证规则配置

### 4. 质量检查

确保生成的任务文档：

- 所有占位符都已正确替换
- 文件路径和命名规范正确
- 业务逻辑描述准确
- 技术规范符合标准

## 模板扩展

### 添加新占位符

如需添加新的占位符，请遵循以下规范：

1. **命名规范**: 使用大写字母和下划线，如 `{{NEW_PARAMETER}}`
2. **文档更新**: 在本指南中添加参数说明
3. **示例提供**: 为新参数提供具体的使用示例

### 定制化调整

对于特殊业务场景，可以：

1. **保留核心结构**: 保持任务概述、开发流程、验收标准等核心章节
2. **调整具体内容**: 根据业务特点调整功能描述和技术要求
3. **扩展验证规则**: 添加特定的业务验证和质量标准

## 质量标准

### 文档完整性

- [ ] 所有必需占位符已定义
- [ ] 开发流程步骤清晰
- [ ] 验收标准具体可执行
- [ ] 模板资源引用正确

### 技术规范性

- [ ] 符合分层架构原则
- [ ] 遵循路由配置规范
- [ ] 组件使用约束明确
- [ ] 代码模板引用准确

### 业务适配性

- [ ] 业务流程描述准确
- [ ] 状态流转规则清晰
- [ ] 权限控制逻辑合理
- [ ] 数据结构定义完整

## 注意事项

1. **参数一致性**: 确保同一概念在整个文档中使用相同的占位符
2. **路径准确性**: 文件路径必须与实际项目结构保持一致
3. **业务逻辑**: 状态流转和权限矩阵必须符合实际业务需求
4. **模板引用**: 确保所有引用的模板文件确实存在
5. **版本兼容**: 保持与核心规范文档的版本兼容性
