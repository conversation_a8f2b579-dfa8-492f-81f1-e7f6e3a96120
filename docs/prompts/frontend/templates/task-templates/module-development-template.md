# {{MODULE_NAME}}模块开发任务

## 任务概述

### 模块目标

开发{{MODULE_NAME}}模块的完整功能，实现{{ENTITY_NAME}}的全生命周期管理，包括新建、编辑、审核、发布、撤销等核心业务流程。

### 开发范围

- **API层**: {{ENTITY_NAME}}数据的HTTP请求封装
- **Adapters层**: {{ENTITY_NAME}}数据的格式转换和适配
- **Store层**: {{ENTITY_NAME}}状态管理和业务逻辑
- **路由配置**: {{MODULE_NAME}}模块的路由组织
- **页面开发**: 列表页、详情页、新建/编辑页

### 输出文件清单

```
{{MODULE_PATH}}/
├── api/index.js              # API层
├── adapters/index.js         # 数据转换层
├── views/                    # 视图层
│   ├── list/index.vue        # 列表页
│   ├── detail/index.vue      # 详情/审核页
│   └── create/index.vue      # 新建/编辑页
├── __mocks__/index.js        # Mock数据
├── store.js                  # 状态管理
└── router.js                 # 路由配置
```

## 技术规范

### 分层架构约束

- **开发顺序**: API → Adapters → Store → Views → 路由集成
- **调用关系**: API层 → Adapters层 → Store层 → Views层
- **禁止**: 跨层调用、反向调用

### 组件使用规范

- **表格组件**: 使用FuniCurdV2组件实现列表功能
- **表单组件**: 使用FuniForm表单组件
- **富文本编辑**: 使用内置富文本编辑器
- **文件上传**: 使用FuniUpload组件

## 全局参数配置

```javascript
const moduleParams = {
  MODULE_NAME: '{{MODULE_NAME}}',
  ENTITY_NAME: '{{ENTITY_NAME}}',
  API_ENDPOINT: '{{API_ENDPOINT}}',
  STORE_NAME: '{{STORE_NAME}}',
  PRIMARY_KEY: '{{PRIMARY_KEY}}',
  DISPLAY_FIELD: '{{DISPLAY_FIELD}}'
};
```

## 开发流程

### 第一阶段：基础设施准备

#### 1.1 API层开发

**文件**: `{{MODULE_PATH}}/api/index.js`

**模板**: `docs/prompts/frontend/templates/code-templates/api-layer.js`

**接口清单**:

{{API_METHODS_LIST}}

#### 1.2 Adapters层开发

**文件**: `{{MODULE_PATH}}/adapters/index.js`

**模板**: `docs/prompts/frontend/templates/code-templates/adapters-layer.js`

**转换函数**:

{{ADAPTER_METHODS_LIST}}

#### 1.3 Store层开发

**文件**: `{{MODULE_PATH}}/store.js`

**模板**: `docs/prompts/frontend/templates/code-templates/store-layer.js`

**状态管理**:

- {{ENTITY_NAME}}列表状态
- 当前{{ENTITY_NAME}}详情
- 表单数据状态
- 加载状态管理

### 第二阶段：路由配置

#### 2.1 模块路由配置

**文件**: `{{MODULE_PATH}}/router.js`

**规范**: `docs/prompts/frontend/core/router-standards.md`

**路由结构**:

```javascript
{
  path: '{{ROUTE_PATH}}',
  name: '{{ROUTE_NAME}}',
  meta: { title: '{{MODULE_NAME}}', isMenu: true },
  children: [
    {
      path: 'list',
      name: '{{ROUTE_NAME}}List',
      component: () => import('./views/list/index.vue'),
      meta: { title: '{{ENTITY_NAME}}列表' }
    },
    {
      path: 'detail/:{{PRIMARY_KEY}}',
      name: '{{ROUTE_NAME}}Detail',
      component: () => import('./views/detail/index.vue'),
      meta: { title: '{{ENTITY_NAME}}详情' }
    },
    {
      path: 'audit/:{{PRIMARY_KEY}}',
      name: '{{ROUTE_NAME}}Audit',
      component: () => import('./views/detail/index.vue'),
      meta: { title: '审核{{ENTITY_NAME}}' }
    },
    {
      path: 'create',
      name: '{{ROUTE_NAME}}Create',
      component: () => import('./views/create/index.vue'),
      meta: { title: '新建{{ENTITY_NAME}}' }
    },
    {
      path: 'edit/:{{PRIMARY_KEY}}',
      name: '{{ROUTE_NAME}}Edit',
      component: () => import('./views/create/index.vue'),
      meta: { title: '编辑{{ENTITY_NAME}}' }
    }
  ]
}
```

### 第三阶段：页面开发

#### 3.1 列表页开发

**文件**: `{{MODULE_PATH}}/views/list/index.vue`

**模板**: `docs/prompts/frontend/templates/page-templates/list-page.vue`

**功能特性**:

- 使用FuniListPageV2组件
- 配置多页签（{{TAB_CONFIG}}）
- 集成搜索功能
- 实现数据加载和展示
- 添加操作按钮和事件处理

#### 3.2 详情/审核页开发

**文件**: `{{MODULE_PATH}}/views/detail/index.vue`

**模板**: `docs/prompts/frontend/templates/page-templates/detail-page.vue`

**页面结构**:

{{DETAIL_TABS}}

#### 3.3 新建/编辑页开发

**文件**: `{{MODULE_PATH}}/views/create/index.vue`

**模板**: `docs/prompts/frontend/templates/page-templates/add-page.vue`

**功能特性**:

{{CREATE_FEATURES}}

### 第四阶段：模块集成验证

#### 4.1 路由集成

将{{MODULE_NAME}}模块路由集成到{{PARENT_MODULE}}模块中：

**更新文件**: `{{PARENT_MODULE_PATH}}/router.js`

**集成步骤**:

1. 导入{{MODULE_NAME}}路由：`import {{ROUTE_IMPORT_NAME}} from './{{ROUTE_PATH}}/router.js';`
2. 将路由添加到children数组中：{{ROUTE_INTEGRATION_INSTRUCTION}}
3. 验证路由层级结构正确

#### 4.2 功能验证

{{VERIFICATION_CHECKLIST}}

## 验收标准

### 功能完整性

{{FUNCTIONAL_CHECKLIST}}

### 技术规范

- [ ] 符合分层架构原则
- [ ] 路由配置符合规范
- [ ] 组件使用符合约束
- [ ] 代码质量符合标准

### 用户体验

- [ ] 界面布局合理美观
- [ ] 交互流程顺畅
- [ ] 错误提示友好
- [ ] 响应速度满足要求

## 模板资源

### 代码模板

- API层模板: `docs/prompts/frontend/templates/code-templates/api-layer.js`
- Adapters层模板: `docs/prompts/frontend/templates/code-templates/adapters-layer.js`
- Store层模板: `docs/prompts/frontend/templates/code-templates/store-layer.js`

### 页面模板

- 列表页模板: `docs/prompts/frontend/templates/page-templates/list-page.vue`
- 详情/审核页模板: `docs/prompts/frontend/templates/page-templates/detail-page.vue`
- 新建/编辑页模板: `docs/prompts/frontend/templates/page-templates/add-page.vue`

### 配置规范

- 路由配置规范: `docs/prompts/frontend/core/router-standards.md`
- 架构标准: `docs/prompts/frontend/core/architecture-standards.md`
- 开发流程: `docs/prompts/frontend/core/development-workflow.md`

## 业务规则

### 状态流转规则

{{STATUS_FLOW_RULES}}

### 操作权限矩阵

{{PERMISSION_MATRIX}}

### 字段验证规则

{{VALIDATION_RULES}}

## 数据结构

### {{ENTITY_NAME}}实体结构

```javascript
{
  {
    ENTITY_STRUCTURE;
  }
}
```

## 注意事项

{{DEVELOPMENT_NOTES}}
