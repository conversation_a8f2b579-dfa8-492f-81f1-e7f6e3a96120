# 前端开发标准化工作流指南 v3.0

## 🎯 **工作流概述**

本指南提供了一套完整的、可复用的前端开发标准化工作流，通过模块级任务生成和模板驱动机制确保开发的高效性和准确性。

### **核心优势**

- ✅ **模块级任务**：以功能模块为最小粒度，避免过度细分
- ✅ **集成开发**：单个任务包含完整开发流程，减少上下文切换
- ✅ **模板驱动**：预设模板避免重复工作，提高开发效率
- ✅ **精简文档**：单个任务文档控制在100行以内
- ✅ **可复用性**：整套流程可直接应用到新项目

### **v3.0核心优化**

- 📈 **效率提升**：任务数量从4个减少到1个，文档长度从400+行减少到100行以内
- 🎯 **职责聚焦**：模块级任务生成，避免跨模块依赖
- 🔧 **流程简化**：统一的开发步骤和模板应用

## 🔄 **标准化工作流程**

### **阶段1：项目准备**

#### **1.1 环境确认**

```bash
# 确认CLI框架环境
- 检查 src/apps/ 目录存在
- 确认 docs/PRD.md 文件存在
- 验证 FuniUI 组件库可用
```

#### **1.2 PRD分析**

```plaintext
# 分析产品需求文档
1. 读取 docs/PRD.md
2. 识别功能模块结构（严格按PRD层级）
3. 确定页面类型和业务特征
4. 识别工作流需求
```

### **阶段2：技术方案设计**

#### **2.1 启动技术方案设计**

```plaintext
指令：设计技术方案-[系统名称]

示例：设计技术方案-采购管理系统
```

#### **2.2 设计输出**

- **模块架构设计.md** - 完整的模块结构和分层架构,文件路径：`frontend/design/模块架构设计.md`
- **模板选择方案.md** - 为每个模块选择的页面和代码模板,文件路径：`frontend/design/模板选择方案.md`
- **开发任务概览.md** - 整体开发计划和任务依赖关系,文件路径：`frontend/design/开发任务概览.md`

#### **2.3 设计验证**

```bash
# 验证设计文档
- 确认模块结构与PRD一致
- 验证模板选择的合理性
- 检查依赖关系的正确性
```

### **阶段3：任务分解**

#### **3.1 模块级任务生成**

```plaintext
# 为每个模块生成单一集成任务文档
指令：制定模块开发任务-[模块名称]

示例：
制定模块开发任务-用户管理模块
制定模块开发任务-角色管理模块
制定模块开发任务-权限管理模块
```

#### **3.2 任务文档结构（v3.0优化）**

根据模块类型生成对应的任务文档：

**叶子模块（无子模块）**：

- **module-development.md** - 包含API层、Store层、路由配置、页面开发的完整流程

**中间层模块（有子模块）**：

- **router-organization.md** - 专注于子模块路由的整合和配置

#### **3.3 任务优化特性**

```bash
# v3.0任务优化
- 模板集成：单个任务文档包含所需的全部模板引用
- 参数复用：统一的占位符参数在整个模块开发中复用
- 流程简化：减少文档数量，提高开发效率
- 验证集中：在模块完成后进行统一的功能和集成验证
```

### **阶段4：编码执行**

#### **4.1 任务执行顺序**

```plaintext
# 按模块层级和任务依赖执行
1. 叶子模块优先（无子模块的模块）
2. 同级模块可并行执行
3. 父模块在所有子模块完成后执行
```

#### **4.2 模块任务执行（v3.0优化）**

```plaintext
指令：执行任务文档-[任务文档路径]

示例：
# 叶子模块执行
执行任务文档-docs/tasks/user-management/module-development.md

# 中间层模块执行
执行任务文档-docs/tasks/system-management/router-organization.md
```

#### **4.3 集成开发流程**

```plaintext
# v3.0单任务集成流程
1. 基础设施准备 (API + Adapters + Store)
2. 路由配置 (基于router-standards.md规范)
3. 页面开发 (基于页面模板)
4. 模块集成验证

# 所有步骤在单个任务文档中顺序执行
```

### **阶段5：质量验证**

#### **5.1 模块验证**

```bash
# 每个模块完成后验证
- 文件完整性检查
- 代码语法验证
- 组件使用规范检查
- 路由配置验证
```

#### **5.2 系统集成验证**

```bash
# 所有模块完成后验证
- 系统级路由配置
- 模块间集成测试
- 整体功能验证
```

## 📋 **项目模板结构**

### **标准项目结构**

```
src/apps/[系统名称]/
├── routers/
│   └── index.js                    # 系统级路由配置
├── modules/
│   ├── [模块1]/                    # 叶子模块
│   │   ├── api/index.js           # API层
│   │   ├── adapters/index.js      # 数据转换层
│   │   ├── store.js               # 状态管理层
│   │   ├── views/                 # 视图层
│   │   │   ├── list/index.vue     # 列表页
│   │   │   └── detail/index.vue   # 详情页
│   │   ├── router.js              # 模块路由
│   │   └── __mocks__/index.js     # Mock数据
│   ├── [模块2]/                    # 中间层模块
│   │   ├── router.js              # 路由组织
│   │   └── [子模块1]/             # 子模块
│   └── shared/                     # 共享资源
│       ├── components/            # 项目级组件
│       ├── utils/                 # 工具函数
│       └── constants/             # 常量定义
```

### **任务文档结构（v3.0优化）**

```
docs/tasks/
├── [父模块]/
│   ├── [叶子模块1]/
│   │   └── module-development.md      # 完整模块开发任务
│   ├── [叶子模块2]/
│   │   └── module-development.md      # 完整模块开发任务
│   └── router-organization.md         # 父模块路由组织任务
├── [中间层模块]/
│   ├── [子模块1]/
│   │   └── module-development.md
│   └── router-organization.md
└── [独立叶子模块]/
    └── module-development.md
```

**文档类型说明**：

- **module-development.md**: 叶子模块的完整开发流程（API+Store+路由+页面）
- **router-organization.md**: 中间层模块的路由整合任务

## 🔧 **快速启动模板**

### **新项目快速启动**

#### **步骤1：环境准备**

```bash
# 1. 确认CLI框架环境
cd [CLI框架根目录]
ls src/apps/  # 确认目录存在

# 2. 准备PRD文档
cp [PRD文件] docs/PRD.md
```

#### **步骤2：技术方案设计**

```plaintext
# 使用技术方案设计助手
设计技术方案-[你的系统名称]

# 等待输出：
# - docs/design/web/模块架构设计.md
# - docs/design/web/模板选择方案.md
# - docs/design/web/开发任务概览.md
```

#### **步骤3：模块级任务生成（v3.0）**

```bash
# 为每个模块生成单一集成任务文档
制定模块开发任务-模块1
制定模块开发任务-模块2
制定模块开发任务-模块3
# ... 继续为所有模块生成任务文档
```

#### **步骤4：模块级执行开发（v3.0）**

```bash
# 按依赖关系执行模块任务
执行任务文档-docs/tasks/模块1/module-development.md
执行任务文档-docs/tasks/模块2/module-development.md
执行任务文档-docs/tasks/父模块/router-organization.md

# 每个模块一个任务文档，包含完整开发流程
```

### **单模块快速开发（v3.0）**

#### **适用场景**

- 独立模块开发
- 功能迭代开发
- 紧急需求开发

#### **执行流程**

```bash
# v3.0单模块开发流程
制定模块开发任务-[模块名称]
执行任务文档-docs/tasks/[模块名]/module-development.md

# 一个任务文档包含完整开发流程
```

### **项目迁移模板**

#### **现有项目迁移到v3.0架构**

```bash
# 1. 分析现有项目结构
查看模块进度-[现有模块名]

# 2. 生成v3.0任务文档
制定模块开发任务-[现有模块名]

# 3. 执行模块级任务
执行任务文档-docs/tasks/[模块名]/module-development.md
```

## 📊 **质量控制检查清单**

### **每个模块任务完成后检查（v3.0）**

- [ ] 模块任务文档执行完成
- [ ] 所有文件创建成功（使用view工具验证）
- [ ] 代码语法正确
- [ ] 导入依赖存在
- [ ] 组件使用符合规范
- [ ] 占位符完全替换
- [ ] 路由配置正确
- [ ] 页面可正常访问
- [ ] API接口可正常调用
- [ ] Mock数据正常工作

### **项目完成后检查**

- [ ] 系统级路由配置正确
- [ ] 所有模块集成成功
- [ ] 权限控制正常
- [ ] 整体功能验证通过

## 🎯 **最佳实践建议**

### **开发效率优化（v3.0）**

1. **模块级开发**：以完整功能模块为最小粒度
2. **集成任务**：单个任务包含完整开发流程
3. **模板复用**：充分利用预设模板
4. **并行开发**：同级模块可以并行开发
5. **精简文档**：单个任务文档控制在100行以内

### **质量保障**

1. **模块完整性**：确保每个模块任务包含完整开发周期
2. **及时验证**：模块完成后立即进行集成验证
3. **规范遵循**：严格遵循技术规范
4. **文档驱动**：基于任务文档精确执行

### **问题处理**

1. **依赖缺失**：使用依赖验证工具检查
2. **任务失败**：查看错误提示，修复后重新执行
3. **进度跟踪**：使用进度查看工具了解整体状态

---

**v3.0标准化工作流通过模块级任务生成和集成开发流程，实现了前端开发的高效性、准确性和可复用性，是团队协作和项目管理的重要基础。**
