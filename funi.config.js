const config = {
  packages: [
    {
      name: 'vue',
      alias: 'vue'
    },
    {
      name: 'pinia',
      alias: 'pinia'
    },
    {
      name: 'vue-demi',
      alias: 'vue_demi'
    },
    {
      name: 'vue-router',
      alias: 'vue_router'
    },
    {
      name: '@vueuse/core',
      alias: 'vueuse_core'
    },
    {
      name: 'element-plus',
      alias: 'element_plus'
    }
  ],
  puppeteer: [
    {
      path: './src/apis/app/bpaas.js',
      alias: 'apis',
      replace: ['@/apis/app/bpaas', '@/apis/app/bpaas.js']
    },
    {
      path: './src/router/index.js',
      alias: 'router',
      export: 'VRouter',
      replace: ['@/router', '@/router/index', '@/router/index.js']
    },
    {
      path: './src/stores/useAppStore/index.js',
      alias: 'use_app_store',
      replace: ['@/stores/useAppStore', '@/stores/useAppStore/index', '@/stores/useAppStore/index.js']
    },
    {
      path: './src/utils/hooks/useMultiTab.js',
      alias: 'use_multi_tab',
      replace: ['@/utils/hooks/useMultiTab', '@/utils/hooks/useMultiTab.js']
    },
    {
      path: './src/stores/usePermissionStore.js',
      alias: 'use_permission_store',
      replace: ['@/stores/usePermissionStore', '@/stores/usePermissionStore.js']
    },
    {
      path: './src/utils/hooks/useClientConfig.js',
      alias: 'use_client_config',
      export: 'useClientConfig',
      replace: ['@/utils/hooks/useClientConfig', '@/utils/hooks/useClientConfig.js']
    },
    {
      path: './src/utils/hooks/useExportTable.js',
      alias: 'use_export_table',
      replace: ['@/utils/hooks/useExportTable', '@/utils/hooks/useExportTable.js']
    },
    {
      path: './src/utils/hooks/useForwardRef.js',
      alias: 'use_forward_ref',
      replace: ['@/utils/hooks/useForwardRef', '@/utils/hooks/useForwardRef.js']
    },
    {
      path: './src/utils/hooks/useMutationObserver.js',
      alias: 'use_mutation_observer',
      replace: ['@/utils/hooks/useMutationObserver', '@/utils/hooks/useMutationObserver.js']
    },
    {
      path: './src/utils/hooks/useWatermark.js',
      alias: 'use_watermark',
      replace: ['@/utils/hooks/useWatermark', '@/utils/hooks/useWatermark.js']
    },
    {
      path: './src/utils/hooks/useBusiness.js',
      alias: 'use_business',
      replace: ['@/utils/hooks/useBusiness', '@/utils/hooks/useBusiness.js']
    }
  ]
};

export default config;
